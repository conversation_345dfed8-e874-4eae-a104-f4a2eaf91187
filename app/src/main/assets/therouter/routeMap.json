[{"path": "personal/user_systemMessage", "className": "com.dz.business.personal.ui.page.message.MyNoticeActivity", "action": "", "description": "系统通知", "params": {}}, {"path": "personal/user_message", "className": "com.dz.business.personal.ui.page.message.MyMessageActivity", "action": "", "description": "我的消息", "params": {}}, {"path": "personal/publicArticle", "className": "com.dz.business.personal.ui.page.article.PublishActivity", "action": "", "description": "发布作品", "params": {}}, {"path": "personal/setting", "className": "com.dz.business.personal.ui.page.SettingActivity", "action": "", "description": "系统设置", "params": {}}, {"path": "personal/messageSetting", "className": "com.dz.business.personal.ui.page.MessageSettingActivity", "action": "", "description": "消息设置", "params": {}}, {"path": "personal/history", "className": "com.dz.business.personal.ui.page.HistoryActivity", "action": "", "description": "观看历史", "params": {}}, {"path": "personal/filings", "className": "com.dz.business.personal.ui.page.FilingsActivity", "action": "", "description": "APP备案信息", "params": {}}, {"path": "personal/favourite", "className": "com.dz.business.personal.ui.page.FavouriteActivity", "action": "", "description": "我的在追", "params": {}}, {"path": "personal/autoRenew", "className": "com.dz.business.personal.ui.page.AutoRenewActivity", "action": "", "description": "自动续费管理", "params": {}}, {"path": "personal/aboutUs", "className": "com.dz.business.personal.ui.page.AboutUsActivity", "action": "", "description": "关于我们", "params": {}}, {"path": "flutter/container", "className": "com.dz.business.flutter.FlutterContainerActivity", "action": "", "description": "flutter", "params": {}}, {"path": "flutter/fragment", "className": "com.dz.business.flutter.FlutterContainFragment", "action": "", "description": "flutter", "params": {}}]