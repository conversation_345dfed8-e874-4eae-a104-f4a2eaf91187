<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <com.dz.foundation.ui.widget.DzConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/common_FFF7F8FA"
        android:paddingBottom="@dimen/common_dp60">

        <com.dz.foundation.ui.widget.DzImageView
            android:id="@+id/header_bkg"
            android:layout_width="0dp"
            android:layout_height="@dimen/common_dp269"
            android:background="@color/common_FFF7F8FA"
            android:scaleType="centerCrop"
            android:src="@drawable/personal_bg_us_new"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.dz.foundation.ui.widget.DzLinearLayout
            android:id="@+id/layout_status_bar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="right"
            android:orientation="vertical">

            <com.dz.foundation.ui.widget.DzView
                android:layout_width="0dp"
                android:layout_height="@dimen/common_dp57"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.dz.foundation.ui.widget.DzImageView
                android:id="@+id/item_system_setting"
                android:layout_width="@dimen/common_dp22"
                android:layout_height="@dimen/common_dp22"
                android:layout_marginRight="@dimen/common_dp16"
                android:src="@drawable/personal_ic_system_setting"
                app:layout_constraintEnd_toEndOf="parent" />

            <com.dz.foundation.ui.widget.DzView
                android:layout_width="0dp"
                android:layout_height="@dimen/common_dp13"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </com.dz.foundation.ui.widget.DzLinearLayout>

        <com.dz.business.base.ui.refresh.DzSmartRefreshLayout
            android:id="@+id/refresh_layout"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toTopOf="@+id/view_divider"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/layout_status_bar"
            app:srlEnableRefresh="true"
            app:srlEnableLoadMore="false">

            <com.dz.foundation.ui.widget.DzNestedScrollView
                android:id="@+id/scroll_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <com.dz.foundation.ui.widget.DzConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <com.dz.business.personal.ui.component.PersonalHeaderComp
                        android:id="@+id/layout_header"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        app:layout_constraintBottom_toTopOf="@+id/layout_vip_kandian"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <com.dz.business.personal.ui.component.PersonalVipKdComp
                        android:id="@+id/layout_vip_kandian"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/common_dp16"
                        android:layout_marginTop="@dimen/common_dp12"
                        android:layout_marginEnd="@dimen/common_dp16"
                        android:visibility="visible"
                        app:layout_constraintBottom_toTopOf="@+id/layout_activity"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/layout_header" />

                    <com.dz.foundation.ui.widget.DzTextView
                        android:id="@+id/tv_discount"
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/common_dp25"
                        android:text="低至元1每天"
                        android:textSize="@dimen/common_dp12"
                        android:layout_marginEnd="@dimen/common_dp15"
                        android:paddingStart="@dimen/common_dp8"
                        android:paddingEnd="@dimen/common_dp8"
                        android:paddingBottom="@dimen/common_dp4"
                        android:paddingTop="@dimen/common_dp2"
                        android:background="@drawable/personal_ic_discount"
                        android:height="@dimen/common_dp15"
                        android:textColor="@color/common_white"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintBottom_toTopOf="@+id/layout_vip_kandian"
                        app:layout_constraintTop_toTopOf="@+id/layout_vip_kandian"/>

                    <com.dz.foundation.ui.widget.DzConstraintLayout
                        android:id="@+id/layout_history"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/common_dp16"
                        android:layout_marginTop="@dimen/common_dp10"
                        android:layout_marginEnd="@dimen/common_dp16"
                        android:orientation="vertical"
                        android:visibility="visible"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/layout_vip_kandian"
                        app:layout_constraintBottom_toTopOf="@+id/dzBanner"
                        app:shape_radius="@dimen/common_dp6"
                        app:shape_solid_color="@color/common_card_FFFFFFFF">

                        <com.dz.business.personal.ui.widget.DzPersonalTitleItem
                            android:id="@+id/item_history_more"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/common_dp12"
                            app:layout_constraintTop_toTopOf="parent"
                            app:text="浏览记录" />

                        <com.dz.business.personal.ui.widget.DzObliqueSlipScrollRecyclerView
                            android:id="@+id/rv"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:clipToPadding="false"
                            android:layout_marginTop="@dimen/common_dp12"
                            app:drv_layoutMode="linearHorizontal"
                            android:layout_marginStart="@dimen/common_dp12"
                            android:layout_marginEnd="@dimen/common_dp12"
                            app:drv_verticalSpacing="@dimen/common_dp7"
                            app:layout_constraintStart_toStartOf="@+id/layout_history"
                            app:layout_constraintTop_toBottomOf="@+id/item_history_more"/>

                    </com.dz.foundation.ui.widget.DzConstraintLayout>
                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/dzBanner_bg"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/common_dp16"
                        android:layout_marginEnd="@dimen/common_dp16"
                        android:layout_marginTop="@dimen/common_dp10"
                        android:layout_marginBottom="@dimen/common_dp12"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/layout_history"
                        app:shape="rectangle"
                        app:shape_left_top_radius="@dimen/common_dp10"
                        app:shape_right_top_radius="@dimen/common_dp10"
                        app:shape_right_bottom_radius="@dimen/common_dp10"
                        app:shape_left_bottom_radius="@dimen/common_dp10"
                        android:visibility="gone">

                        <com.dz.foundation.ui.view.banner.DzBanner
                            android:id="@+id/dzBanner"
                            android:layout_width="@dimen/common_dp0"
                            android:layout_height="@dimen/common_dp72"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:shape="rectangle"
                            app:shape_radius="@dimen/common_dp10"
                            app:shape_solid_color="@color/common_card_FFFFFFFF"
                            app:banner_loop_time="3000"
                            app:banner_auto_loop="true"
                            app:banner_infinite_loop="true"
                            app:banner_indicator_selected_color="@color/common_FFE1442E"
                            app:banner_indicator_normal_color="@color/common_99FFFFFF"
                            app:banner_indicator_height="8dp"
                            app:banner_indicator_radius="4dp"
                            app:banner_indicator_selected_width="10dp"
                            app:banner_indicator_normal_width="8dp"
                            app:banner_indicator_gravity="center"
                            app:banner_indicator_marginBottom="10dp"
                            android:visibility="gone" />

                        <!-- 固定在底部的指示器 -->
                        <LinearLayout
                            android:id="@+id/banner_indicatorContainer"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center"
                            android:padding="4dp"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintBottom_toBottomOf="parent"
                            android:visibility="gone">
                            <!-- 指示器内容 -->
                        </LinearLayout>

                    </androidx.constraintlayout.widget.ConstraintLayout>


                    <com.dz.foundation.ui.widget.DzLinearLayout
                        android:id="@+id/layout_card"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/common_dp71.5"
                        android:layout_marginStart="@dimen/common_dp16"
                        android:layout_marginTop="@dimen/common_dp10"
                        android:layout_marginEnd="@dimen/common_dp16"
                        android:clipToOutline="true"
                        android:orientation="horizontal"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/dzBanner_bg"
                        app:shape_radius="@dimen/common_dp8">

                        <com.dz.business.personal.ui.widget.SettingItem5
                            android:id="@+id/item_message"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:paddingBottom="@dimen/common_dp12"
                            app:icon="@drawable/personal_ic_my_message"
                            app:ivSize="@dimen/common_dp24"
                            app:shape_solid_color="@color/common_card_FFFFFFFF"
                            app:text="我的消息"
                            app:txSize="@dimen/common_dp13" />

                        <com.dz.business.personal.ui.widget.SettingItem6
                            android:id="@+id/itemMyMall"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight="1"
                            android:paddingBottom="@dimen/common_dp12"
                            android:visibility="gone"
                            app:icon="@drawable/personal_ic_mymall_new"
                            app:ivSize="@dimen/common_dp24"
                            app:shape_solid_color="@color/common_card_FFFFFFFF"
                            app:text="我的商城"
                            app:txSize="@dimen/common_dp13"
                            tools:visibility="visible" />

                        <com.dz.business.personal.ui.widget.SettingItem5
                            android:id="@+id/item_collection"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:paddingBottom="@dimen/common_dp12"
                            app:icon="@drawable/personal_ic_favourite_new"
                            app:ivSize="@dimen/common_dp24"
                            app:shape_solid_color="@color/common_card_FFFFFFFF"
                            app:text="我的在追"
                            app:txSize="@dimen/common_dp13" />

                        <com.dz.business.personal.ui.widget.SettingItem5
                            android:id="@+id/item_reservation"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:paddingBottom="@dimen/common_dp12"
                            app:icon="@drawable/personal_ic_reservation"
                            app:ivSize="@dimen/common_dp24"
                            app:shape_solid_color="@color/common_card_FFFFFFFF"
                            app:text="我的预约"
                            app:txSize="@dimen/common_dp13" />

                    </com.dz.foundation.ui.widget.DzLinearLayout>

                    <com.dz.foundation.ui.widget.DzFrameLayout
                        android:id="@+id/llBannerAd"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/common_dp80"
                        android:layout_marginStart="@dimen/common_dp16"
                        android:layout_marginTop="@dimen/common_dp10"
                        android:layout_marginEnd="@dimen/common_dp16"
                        android:clipToOutline="true"
                        android:visibility="gone"
                        tools:visibility="visible"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintBottom_toTopOf="@id/layout_welfare"
                        app:layout_constraintTop_toBottomOf="@id/layout_card"
                        app:shape_radius="@dimen/common_dp6"
                        app:shape_solid_color="@color/common_card_FFFFFFFF">

                    </com.dz.foundation.ui.widget.DzFrameLayout>

                    <com.dz.foundation.ui.widget.DzLinearLayout
                        android:id="@+id/layout_welfare"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/common_dp70"
                        android:layout_marginStart="@dimen/common_dp16"
                        android:layout_marginTop="@dimen/common_dp10"
                        android:layout_marginEnd="@dimen/common_dp16"
                        android:orientation="vertical"
                        android:paddingTop="@dimen/common_dp12"
                        android:paddingBottom="@dimen/common_dp12"
                        android:visibility="visible"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/llBannerAd"
                        app:shape_radius="@dimen/common_dp6"
                        app:shape_solid_color="@color/common_card_FFFFFFFF">


                        <com.dz.foundation.ui.widget.DzLinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <com.dz.foundation.ui.widget.DzLinearLayout
                                android:id="@+id/ll_species"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="center"
                                android:orientation="vertical">

                                <com.dz.foundation.ui.widget.DzTextView
                                    android:id="@+id/tv_species"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:gravity="center"
                                    android:maxLines="1"
                                    android:text="699"
                                    android:textStyle="bold"
                                    android:textColor="@color/common_FF161718"
                                    android:textSize="@dimen/common_dp20"
                                    app:layout_constraintBottom_toBottomOf="@id/iv_cover"
                                    app:layout_constraintStart_toStartOf="parent" />

                                <View
                                    android:layout_width="wrap_content"
                                    android:layout_height="@dimen/common_dp2" />

                                <com.dz.foundation.ui.widget.DzTextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:gravity="center"
                                    android:maxLines="1"
                                    android:text="金币余额"
                                    android:textColor="@color/common_FF5E6267"
                                    android:textSize="@dimen/common_dp12"
                                    app:layout_constraintBottom_toBottomOf="@id/iv_cover"
                                    app:layout_constraintStart_toStartOf="parent" />

                            </com.dz.foundation.ui.widget.DzLinearLayout>


                            <com.dz.foundation.ui.widget.DzLinearLayout
                                android:id="@+id/ll_cash"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="center"
                                android:orientation="vertical">

                                <com.dz.foundation.ui.widget.DzTextView
                                    android:id="@+id/tv_cash"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:gravity="center"
                                    android:maxLines="1"
                                    android:textStyle="bold"
                                    android:text="0"
                                    android:textColor="@color/common_FF161718"
                                    android:textSize="@dimen/common_dp20"
                                    app:layout_constraintBottom_toBottomOf="@id/iv_cover"
                                    app:layout_constraintStart_toStartOf="parent" />

                                <View
                                    android:layout_width="wrap_content"
                                    android:layout_height="@dimen/common_dp2" />

                                <com.dz.foundation.ui.widget.DzTextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:gravity="center"
                                    android:maxLines="1"
                                    android:text="现金余额"
                                    android:textColor="@color/common_FF5E6267"
                                    android:textSize="@dimen/common_dp12"
                                    app:layout_constraintBottom_toBottomOf="@id/iv_cover"
                                    app:layout_constraintStart_toStartOf="parent" />


                            </com.dz.foundation.ui.widget.DzLinearLayout>

                            <com.dz.foundation.ui.widget.DzImageView
                                android:id="@+id/personal_ic_deliver_line"
                                android:layout_width="@dimen/common_dp1"
                                android:layout_height="@dimen/common_dp41.5"
                                android:layout_marginBottom="@dimen/common_dp9.5"

                                android:src="@drawable/personal_line" />

                            <com.dz.foundation.ui.widget.DzConstraintLayout
                                android:layout_width="@dimen/common_dp95"
                                android:layout_height="@dimen/common_dp44">
                                <com.dz.foundation.ui.widget.DzTextView
                                    android:id="@+id/item_welfare_more"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginEnd="@dimen/common_dp27"
                                    android:textSize="@dimen/common_dp13"
                                    android:textColor="@color/common_FF5E6267"
                                    android:layout_gravity="center"
                                    android:text="我的钱包"
                                    app:layout_constraintBottom_toBottomOf="parent"
                                    app:layout_constraintTop_toTopOf="parent"
                                    app:layout_constraintEnd_toEndOf="parent"/>

                                <com.dz.foundation.ui.widget.DzImageView
                                    android:id="@+id/item_welfare_more_icon"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:src="@drawable/personal_welfare_jump"
                                    app:layout_constraintBottom_toBottomOf="parent"
                                    app:layout_constraintTop_toTopOf="parent"
                                    app:layout_constraintStart_toEndOf="@+id/item_welfare_more"/>
                            </com.dz.foundation.ui.widget.DzConstraintLayout>



                        </com.dz.foundation.ui.widget.DzLinearLayout>



                    </com.dz.foundation.ui.widget.DzLinearLayout>

                    <com.dz.foundation.ui.widget.DzLinearLayout
                        android:id="@+id/layout_settings"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/common_dp16"
                        android:layout_marginTop="@dimen/common_dp10"
                        android:layout_marginEnd="@dimen/common_dp16"
                        android:orientation="vertical"
                        android:visibility="visible"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/layout_welfare"
                        app:shape_radius="@dimen/common_dp6"
                        app:shape_solid_color="@color/common_card_FFFFFFFF">

                        <com.dz.foundation.ui.view.recycler.DzRecyclerView
                            android:id="@+id/gv"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            app:drv_gridSpanCount="4"
                            app:drv_horizontalSpacing="@dimen/common_dp0"
                            app:drv_layoutMode="grid"
                            app:drv_openRealExpose="true"
                            app:drv_verticalSpacing="@dimen/common_dp0"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/layout_welfare" />

                    </com.dz.foundation.ui.widget.DzLinearLayout>

                    <View
                        android:id="@+id/v_bottom"
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/common_dp16"
                        app:layout_constraintTop_toBottomOf="@+id/layout_settings" />

                </com.dz.foundation.ui.widget.DzConstraintLayout>
            </com.dz.foundation.ui.widget.DzNestedScrollView>
        </com.dz.business.base.ui.refresh.DzSmartRefreshLayout>

        <com.dz.foundation.ui.widget.DzView
            android:id="@+id/view_divider"
            android:layout_width="match_parent"
            android:layout_height="@dimen/common_dp0.5"
            android:background="@color/common_FFEFF2F4"
            app:layout_constraintBottom_toBottomOf="parent" />
    </com.dz.foundation.ui.widget.DzConstraintLayout>
</layout>
