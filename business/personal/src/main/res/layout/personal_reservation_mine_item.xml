<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <merge tools:parentTag="com.dz.foundation.ui.widget.DzConstraintLayout">

        <com.dz.foundation.ui.widget.DzImageView
            android:id="@+id/iv_cover"
            android:layout_width="@dimen/common_dp73"
            android:layout_height="@dimen/common_dp104.5"
            android:layout_marginStart="@dimen/common_dp16"
            android:src="@drawable/bbase_ic_cover_default"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:id="@+id/bottom_shadow"
            android:layout_width="0dp"
            android:layout_height="@dimen/common_dp22"
            android:background="@drawable/personal_reservation_cover_shadow"
            app:layout_constraintBottom_toBottomOf="@+id/iv_cover"
            app:layout_constraintEnd_toEndOf="@+id/iv_cover"
            app:layout_constraintStart_toStartOf="@+id/iv_cover" />

        <com.dz.foundation.ui.widget.DzTextView
            android:id="@+id/tv_chapters"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/common_dp4"
            android:layout_marginBottom="@dimen/common_dp3.5"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/common_FFFFFFFF"
            android:textSize="@dimen/common_dp11"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toBottomOf="@+id/iv_cover"
            app:layout_constraintEnd_toEndOf="@+id/iv_cover"
            tools:ignore="SpUsage"
            tools:text="99999999集" />

        <com.dz.foundation.ui.widget.DzTextView
            android:id="@+id/tv_book_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/common_dp12"
            android:layout_marginEnd="@dimen/common_dp20"
            android:ellipsize="end"
            android:gravity="start"
            android:includeFontPadding="false"
            android:maxLines="1"
            android:textColor="@color/common_FF161718"
            android:textSize="@dimen/common_dp15"
            android:textStyle="bold"
            app:layout_constraintBottom_toTopOf="@+id/tv_tags"
            app:layout_constraintEnd_toStartOf="@+id/iv_check"
            app:layout_constraintStart_toEndOf="@+id/iv_cover"
            app:layout_constraintTop_toTopOf="@+id/iv_cover"
            app:layout_constraintVertical_chainStyle="packed"
            tools:text="我嫁了个宝藏老公" />

        <com.dz.foundation.ui.widget.DzTextView
            android:id="@+id/tv_tags"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/common_dp5"
            android:ellipsize="end"
            android:gravity="start"
            android:maxLines="1"
            android:textColor="@color/common_FF7F7F7F"
            android:textSize="@dimen/common_dp12"
            app:layout_constraintBottom_toTopOf="@+id/tv_desc"
            app:layout_constraintEnd_toEndOf="@+id/tv_book_name"
            app:layout_constraintStart_toStartOf="@+id/tv_book_name"
            app:layout_constraintTop_toBottomOf="@+id/tv_book_name"
            tools:ignore="SpUsage"
            tools:text="逆袭 纯爱"
            tools:visibility="visible" />

        <com.dz.foundation.ui.widget.DzTextView
            android:id="@+id/tv_desc"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/common_dp3"
            android:ellipsize="end"
            android:gravity="start"
            android:lineSpacingExtra="@dimen/common_dp2"
            android:maxLines="2"
            android:textColor="@color/common_FF959595"
            android:textSize="@dimen/common_dp13"
            app:layout_constraintBottom_toBottomOf="@+id/iv_cover"
            app:layout_constraintEnd_toEndOf="@+id/tv_book_name"
            app:layout_constraintStart_toStartOf="@+id/tv_book_name"
            app:layout_constraintTop_toBottomOf="@+id/tv_tags"
            app:layout_constraintVertical_bias="0"
            tools:ignore="SpUsage"
            tools:text="简介文案简介文案简介文案简介文案简介简简介文案简介文案简介文案简介文案简介简简介文案简介文案简介文案简介文案简介简简介文案简介文案简介文案简介文案简介简"
            tools:visibility="visible" />

        <com.dz.foundation.ui.widget.DzImageView
            android:id="@+id/iv_check"
            android:layout_width="@dimen/common_dp24"
            android:layout_height="@dimen/common_dp24"
            android:layout_marginEnd="@dimen/common_dp16"
            android:src="@drawable/personal_reservation_uncheck"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@+id/iv_cover"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/iv_cover"
            tools:visibility="visible" />
    </merge>
</layout>