package com.dz.business.personal.vm

import com.dz.business.base.data.bean.BaseNovelInfo
import com.dz.business.personal.data.PersonalListEditBean
import com.dz.business.personal.repository.BaseDataRepository
import com.dz.business.personal.repository.CollectNovelRepository

/**
 *@Author: zhanggy
 *@Date: 2025-05-08
 *@Description:在追-小说
 *@Version:1.0
 */
class CollectNovelVM : PersonalListBaseVM<PersonalListEditBean<BaseNovelInfo>>() {

    private val _repository: BaseDataRepository<PersonalListEditBean<BaseNovelInfo>> by lazy {
        CollectNovelRepository(
            "我的",
            loadListener
        )
    }

    override fun getRepository(): BaseDataRepository<PersonalListEditBean<BaseNovelInfo>> {
        return _repository
    }

    override fun getEmptyDesc(): String = "无相关内容"
}