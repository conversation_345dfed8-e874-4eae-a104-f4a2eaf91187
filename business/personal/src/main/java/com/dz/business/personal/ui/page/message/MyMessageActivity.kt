package com.dz.business.personal.ui.page.message

import android.graphics.Typeface
import android.os.Build
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.lifecycle.LifecycleOwner
import androidx.viewpager2.widget.ViewPager2
import com.dz.business.base.data.bean.TabVo
import com.dz.business.base.message.MessageME
import com.dz.business.base.personal.PersonalMR
import com.dz.business.base.track.ITracker
import com.dz.business.base.track.TrackUtil
import com.dz.business.base.ui.BaseActivity
import com.dz.business.base.ui.component.status.StatusComponent
import com.dz.business.personal.R
import com.dz.business.personal.adapter.FragmentViewPagerAdapter
import com.dz.business.personal.data.NoticeVo
import com.dz.business.personal.databinding.PersonalMessageActivityBinding
import com.dz.business.personal.vm.MyMessageActivityVM
import com.dz.foundation.base.utils.LogUtil
import com.dz.foundation.base.utils.ScreenUtil
import com.dz.foundation.network.requester.HttpCallback
import com.dz.foundation.ui.utils.desensitization
import com.dz.platform.common.toast.ToastManager
import com.google.android.material.tabs.TabLayout
import com.therouter.router.Route
import org.json.JSONObject

/**
 * @Description:我的消息
 * @Version:1.0
 */
@Route(path = "personal/user_message", description = "我的消息")
class MyMessageActivity : BaseActivity<PersonalMessageActivityBinding, MyMessageActivityVM>() {
    private var TAG: String = "MyMessageActivity"
    private val fragments: MutableList<Fragment> = mutableListOf() //子页面
    // 当前显示的Fragment
    private var currentFragment: Fragment? = null

    // 进入页面首次自动选中tab
    private var isFistAutoSelect = true
    private var noticeFromId: Long = -1
    override fun initStatusComponent(): StatusComponent {
        return StatusComponent.create(this).bellow(mViewBinding.tvTitle).background(R.color.white)
    }

    override fun initData() {
        mViewModel.getData(this, fragments)
    }

    override fun initView() {
        mViewBinding.ivClean.visibility = View.GONE
        mViewModel.getLoginStatus()
        mViewBinding.appBarLayout.outlineProvider = null
    }

    override fun initListener() {
        mViewBinding.apply {
            ivBack.registerClickAction {
                finish()
            }
            ivClean.registerClickAction {
                var change = false
                mViewModel.channelTabData.value?.forEach {
                    if (it.showBadge) {
                        change = true
                        it.showBadge = false
                    }
                }
                if (change) {
//              TODO 全部已读  mViewModel.reportRead()
                    trackAppClick()
                    ToastManager.showToast("全部已读~")
                } else {
                    ToastManager.showToast("没有未读消息~")
                }
            }

            clNotifyEntryLayout.registerClickAction {
                PersonalMR.get().myNotice().apply {
                    fromId = noticeFromId
                }.start()
            }
        }
        mViewBinding.tabLayout.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab?) {
                LogUtil.d(TAG, "onTabSelected tab text = ${tab?.text}")
                mViewBinding.vp.currentItem = tab?.position ?: 0
                selectTab()

                if (isFistAutoSelect) {
                    isFistAutoSelect = false
                } else {
                    mViewBinding.appBarLayout.setExpanded(false, true)
                }
            }

            override fun onTabUnselected(tab: TabLayout.Tab?) {}

            override fun onTabReselected(tab: TabLayout.Tab?) {}
        })
        mViewBinding.vp.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                LogUtil.d(TAG, "onPageSelected position = $position")
                mViewBinding.tabLayout.getTabAt(position)?.select()
            }
        })
    }

    override fun subscribeEvent(lifecycleOwner: LifecycleOwner, lifecycleTag: String) {
        super.subscribeEvent(lifecycleOwner, lifecycleTag)
        MessageME.get().getLoginStatus().observe(lifecycleOwner, lifecycleTag) {
            LogUtil.d(TAG, "subscribeEvent getLoginStatus $it")
            if (it != null) {
                isFistAutoSelect = true
                updateLoginStatusUI(it)
                // 重新请求数据
                mViewModel.getData(this, fragments)
            }
        }

        MessageME.get().hasNewNotice().observe(lifecycleOwner, lifecycleTag) {
            if (it != null) {
                showNoticeBadge(it)
            }
        }
    }

    override fun subscribeObserver(lifecycleOwner: LifecycleOwner) {
        super.subscribeObserver(lifecycleOwner)

        mViewModel.hasLogin.observe(lifecycleOwner) {
            updateLoginStatusUI(it)
        }

        mViewModel.channelTabData.observe(lifecycleOwner) {
            if (!it.isNullOrEmpty() && fragments.isNotEmpty()) {
                changeBadgeUI()
            }
        }

        mViewModel.hasNewNotice.observe(lifecycleOwner) {
            LogUtil.d(TAG, "hasNewNotice observe it = $it")
            if (it != null) {
                showNoticeBadge(it)
            }
        }

        mViewModel.noticeData.observe(lifecycleOwner) {
            LogUtil.d(TAG, "noticeData observe ")
            if (it != null) {
                updateNoticeInfo(it)
                noticeFromId = it.id ?: -1
            } else {
                setDefaultNoticeInfo()
                noticeFromId = -1
            }
        }
    }

    private fun updateLoginStatusUI(isLogin: Boolean) {
        if (isLogin) {
            mViewBinding.tabLayout.visibility = View.VISIBLE
            mViewBinding.vp.isUserInputEnabled = true
        } else {
            mViewBinding.tabLayout.visibility = View.GONE
            mViewBinding.vp.isUserInputEnabled = false
        }
    }

    private fun updateNoticeInfo(noticeData: NoticeVo) {
        mViewBinding.noticeName.text = "系统通知"
        mViewBinding.noticeTime.text = noticeData.sendTime
        mViewBinding.noticeDetail.text = noticeData.title
    }

    private fun setDefaultNoticeInfo() {
        mViewBinding.noticeName.text = "系统通知"
        mViewBinding.noticeDetail.text = "暂无系统通知"
    }

    private fun showNoticeBadge(show: Boolean) {
        mViewBinding.vNoticeRedDot.visibility = if (show) View.VISIBLE else View.GONE
    }

    fun setChannel(tabList: MutableList<TabVo>) {
        mViewBinding.tabLayout.removeAllTabs()
        tabList.forEachIndexed { index, it ->
            val tab = mViewBinding.tabLayout.newTab().setText(it.tabName)
            tab.view.isLongClickable = false
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                tab.view.tooltipText = ""
            }
            mViewBinding.tabLayout.addTab(tab)
            val view = LayoutInflater.from(this)
                .inflate(R.layout.personal_message_tab, mViewBinding.tabLayout, false)
            val text = view.findViewById<TextView>(R.id.item_title_text)
            val badge = view.findViewById<TextView>(R.id.item_red_dot)
            text.text = tab.text
            text.typeface =
                if (mViewBinding.vp.currentItem == index) Typeface.DEFAULT_BOLD else Typeface.DEFAULT
            val color = if (mViewBinding.vp.currentItem == index) ContextCompat.getColor(
                this,
                R.color.common_E1442E
            ) else ContextCompat.getColor(
                this,
                R.color.common_FF191919
            )
            text.setTextColor(color)
            LogUtil.d(TAG, "setChannel index = $index color = $color")
            val drawable = if (mViewBinding.vp.currentItem == index) ContextCompat.getDrawable(
                this, R.drawable.personal_shape_tab_selected
            )
            else ContextCompat.getDrawable(
                this, R.drawable.personal_shape_tab_new
            )
            LogUtil.d(TAG, "setChannel index = $index color = $color")
            view.background = drawable
            if (mViewBinding.vp.currentItem == index && it.showBadge) {
                mViewModel.reportRead(
                    index + 1,
                    callback = object : HttpCallback {
                        override fun onSuccess(response: String?) {
                            if (response == "true") {
                                mViewModel.list.find { it.tabName == tab.text }?.showBadge = false
                                mViewModel.channelTabData.value = mViewModel.list
                            }
                        }

                        override fun onFail(e: Throwable) {

                        }

                    }
                )
            }
            badge.visibility = if (it.showBadge) View.VISIBLE else View.GONE
            tab.customView = view
        }

        mViewBinding.vp.apply {
            offscreenPageLimit = tabList.size
            adapter = FragmentViewPagerAdapter(
                this@MyMessageActivity,
                fragments
            )
        }
        //设置默认位置
        mViewBinding.vp.setCurrentItem(mViewModel.currentIndex, false)
        //降低灵敏度
        mViewBinding.vp.desensitization()
        adjustTabLayout()
    }

    private fun adjustTabLayout() {
        val tabCount = mViewBinding.tabLayout.tabCount
        for (i in 0 until tabCount) {
            val tabView =  (mViewBinding.tabLayout.getChildAt(0) as ViewGroup).getChildAt(i) as ViewGroup
            tabView.setPadding(0, 0, 0, 0) // 移除默认 padding
            val layoutParams = tabView.layoutParams as ViewGroup.MarginLayoutParams
            layoutParams.leftMargin = 0
            layoutParams.rightMargin = ScreenUtil.dip2px(this, 8)
            tabView.requestLayout()
        }
    }

    //tab选中
    fun selectTab() {
        mViewModel.currentIndex = mViewBinding.vp.currentItem
        LogUtil.d(TAG, "selectTab chanelTabData = ${mViewModel.channelTabData.value?.size} currentItem = ${mViewBinding.vp.currentItem}")
        mViewModel.channelTabData.value?.forEachIndexed { index, tabVo ->
            LogUtil.d(TAG, "selectTab index = $index tabVo = ${tabVo.tabName}")
        }
        mViewModel.channelTabData.value?.forEachIndexed { index, item ->
            val tab = mViewBinding.tabLayout.getTabAt(index)
            val view = tab?.customView ?: return@forEachIndexed
            val badge = view.findViewById<TextView>(R.id.item_red_dot)
            val text = view.findViewById<TextView>(R.id.item_title_text)
            badge.visibility = if (item.showBadge) View.VISIBLE else View.GONE
            text.typeface =
                if (mViewBinding.vp.currentItem == index) Typeface.DEFAULT_BOLD else Typeface.DEFAULT
            val color = if (mViewBinding.vp.currentItem == index) ContextCompat.getColor(
                this,
                R.color.common_E1442E
            ) else ContextCompat.getColor(
                this,
                R.color.common_FF191919
            )
            text.setTextColor(color)
            LogUtil.d(TAG, "selectTab index = $index color = $color")
            val drawable = if (mViewBinding.vp.currentItem == index) ContextCompat.getDrawable(
                this, R.drawable.personal_shape_tab_selected
            )
            else ContextCompat.getDrawable(
                this, R.drawable.personal_shape_tab_new
            )
            LogUtil.d(TAG, "selectTab index = $index color = $color")
            view.background = drawable
            if (mViewBinding.vp.currentItem == index && item.showBadge) {
                mViewModel.reportRead(
                    index + 1,
                    callback = object : HttpCallback {
                        override fun onSuccess(response: String?) {
                            if (response == "true") {
                                mViewModel.list.find { it.tabCode == item.tabCode }?.showBadge =
                                    false
                                mViewModel.channelTabData.value = mViewModel.list
                            }
                        }

                        override fun onFail(e: Throwable) {
                            LogUtil.e("onFail", "onFail===reportRead onFail: ${e.message}")
                        }
                    }
                )
            }
        }
        updateCurrentFragment()
    }

    //红点ui处理
    private fun changeBadgeUI() {
        mViewModel.channelTabData.value?.forEachIndexed { index, item ->
            val tab = mViewBinding.tabLayout.getTabAt(index)
            val view = tab?.customView ?: return@forEachIndexed
            val badge = view.findViewById<TextView>(R.id.item_red_dot)
            badge.visibility = if (item.showBadge) View.VISIBLE else View.GONE
        }
    }

    fun setRequestId(type: Int, readFromId: Long?) {
        mViewModel.setRequestId(type, readFromId)
    }

    /**
     * 更新当前显示的Fragment的方法。
     */
    private fun updateCurrentFragment() {
        // 根据当前页面索引获取对应的Fragment
        currentFragment = getFragmentByIndex(mViewBinding.vp.currentItem)
        // 若当前Fragment不是ITracker接口的实现，则记录日志
        if (currentFragment !is ITracker) {
            LogUtil.e(
                TrackUtil.TAG,
                "异常Fragment！currentItem:${mViewBinding.vp.currentItem} currentFragment:$currentFragment"
            )
        }
    }

    /**
     * 根据索引获取对应的Fragment实例。
     * 如果索引超出Fragment列表的范围，返回null。
     * 否则，返回列表中指定索引的Fragment。
     *
     * @param index 标签页的索引。
     * @return 索引对应的Fragment实例，如果不存在则为null。
     */
    private fun getFragmentByIndex(index: Int): Fragment? {
        if (fragments.size == 0 || fragments.size <= index) {
            return null
        }
        return fragments[index]
    }

    /**
     * 打点
     * */
    private fun trackAppClick() {
        kotlin.runCatching {
            val jsonObject = JSONObject().apply {
                put("\$element_content", "清理未读消息")
                put("\$element_type", "互动消息")
                put("\$element_position", "我的消息")
                put("\$screen_name", "我的")
            }
            TrackUtil.track("\$AppClick", jsonObject)
        }.onFailure {
            it.printStackTrace()
        }
    }

    override fun getPageName(): String {
        val title = (currentFragment as? ITracker)?.getPageName()
        if (!title.isNullOrEmpty()) {
            return "我的消息-$title"
        }
        return "我的消息-回复"
    }

    override fun getTrackProperties(): JSONObject {
        return super
            .getTrackProperties()
            .apply {
                put("PositionName", "我的消息")
            }
    }
}