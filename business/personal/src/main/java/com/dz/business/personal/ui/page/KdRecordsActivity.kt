package com.dz.business.personal.ui.page

import android.content.Context
import android.util.TypedValue
import android.view.View
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.dz.business.base.ui.BaseActivity
import com.dz.business.personal.R
import com.dz.business.personal.databinding.PersonalKdRecordsActivityBinding
import com.dz.business.personal.vm.KdObtainRecordsFragmentVM
import com.dz.business.personal.vm.KdRecordsActivityVM
import com.dz.foundation.base.utils.dp
import com.dz.foundation.ui.view.tabbar.ViewPagerHelper
import com.dz.foundation.ui.view.tabbar.commonnavigator.CommonNavigator
import com.dz.foundation.ui.view.tabbar.commonnavigator.abs.CommonNavigatorAdapter
import com.dz.foundation.ui.view.tabbar.commonnavigator.abs.IPagerIndicator
import com.dz.foundation.ui.view.tabbar.commonnavigator.abs.IPagerTitleView
import com.dz.foundation.ui.view.tabbar.commonnavigator.titles.BoldPagerTitleView

/**
 *@Author: zhanggy
 *@Date: 2022-10-22 周六
 *@Description: 看点变更记录
 *@Version:1.0
 */
class KdRecordsActivity : BaseActivity<PersonalKdRecordsActivityBinding, KdRecordsActivityVM>() {

    private val fragments = mutableListOf<Fragment>()

    override fun initData() {
        fragments.add(KdObtainRecordsFragment())
        fragments.add(KdConsumeRecordsFragment())
    }

    override fun initView() {
        //tab
        val commonNavigator = CommonNavigator(this).apply {
            adapter = object : CommonNavigatorAdapter() {
                override fun getTitleView(context: Context, index: Int): IPagerTitleView {
                    //标题
                    val titleView =
                        BoldPagerTitleView(context).apply {
                            normalColor = ContextCompat.getColor(context, R.color.common_FF5E6267)
                            selectedColor = ContextCompat.getColor(context, R.color.common_FF161718)
                            paint.isFakeBoldText = true
                            text = when (mViewModel.tabs[index]) {
                                KdObtainRecordsFragmentVM.TYPE_OBTAIN -> "获得记录"
                                KdObtainRecordsFragmentVM.TYPE_CONSUME -> "消费记录"
                                else -> "全部"
                            }
                            setTextSize(TypedValue.COMPLEX_UNIT_PX, 15f.dp)
                            setOnClickListener {
                                mViewBinding.vp.currentItem = index
                            }
                        }
                    return titleView
                }

                override fun getCount(): Int {
                    return mViewModel.tabs.size
                }

                override fun getIndicator(context: Context?): IPagerIndicator? {
                    // 不需要indicator色块
                    return null
                }
            }
        }
        commonNavigator.isAdjustMode = true
        mViewBinding.tabBar.navigator = commonNavigator
        ViewPagerHelper.bind(mViewBinding.tabBar, mViewBinding.vp)
        mViewBinding.vp.apply {
            isUserInputEnabled = true
            adapter = object : FragmentStateAdapter(this@KdRecordsActivity) {
                override fun getItemCount(): Int = mViewModel.tabs.size

                override fun createFragment(position: Int): Fragment {
                    return fragments[position]
                }

            }

            // 取消OverScroll
            val child: View = getChildAt(0)
            if (child is RecyclerView) {
                child.overScrollMode = View.OVER_SCROLL_NEVER
            }
        }
    }

    override fun initListener() {
    }

    override fun getPageName(): String {
        return "看点变更记录"
    }
}

const val TAG_KD_RECORDS = "kd_records"