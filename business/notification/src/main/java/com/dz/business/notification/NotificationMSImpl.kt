package com.dz.business.notification

import android.content.Context
import com.dz.business.base.BBaseMC
import com.dz.business.base.data.BBaseKV
import com.dz.business.base.data.bean.CommonConfigBean
import com.dz.business.base.notification.NotificationMC
import com.dz.business.base.notification.NotificationMS
import com.dz.business.notification.local.LocalPushManager
import com.dz.business.notification.redDot.RedDotPush.Companion.redDotCount
import com.dz.business.notification.utils.AppIconBadgeUtils
import com.dz.business.notification.utils.PushUtil
import com.dz.business.notification.voiceplay.Playgoldvoice
import com.dz.foundation.base.module.AppModule
import com.dz.foundation.base.utils.LogUtil

/**
 *@Author: zhanggy
 *@Date: 2024-07-08
 *@Description:
 *@Version:1.0
 */
class NotificationMSImpl: NotificationMS {

    override fun saveConfig(config: CommonConfigBean?) {
        LocalPushManager.saveConfig(config)
    }

    override fun startLocalPush(context: Context, @NotificationMC.LocalPush type: Int) {
        LocalPushManager.start(context, type)
    }

    override fun stopLocalPush(context: Context, type: Int) {
        LocalPushManager.stop(context)
    }

    override fun clearBadgeCount(context: Context, launchClassName: String?) {
        AppIconBadgeUtils.clearBadgeCount(context, launchClassName)
    }

    override fun setBadgeCount(context: Context, targetCount: Int, launchClassName: String?) {
        AppIconBadgeUtils.setBadgeCount(context, targetCount, launchClassName)
    }

    override fun playVoice(context: Context)
    {
        LogUtil.d("subscribeVoicePlayStatus", "subscribeVoicePlayStatus = ${BBaseMC.subscribeVoicePlayStatus}")
        LogUtil.d("subscribeVoicePlayStatus", "voicePlayStatus = ${BBaseKV.voicePlayStatus}")
        if(BBaseKV.voicePlayStatus == 1 && BBaseMC.subscribeVoicePlayStatus == 1) //开关为开且本地状态为开
        {
            Playgoldvoice.playVoice(context)
        }


    }

    override fun sysAutoShowSwitchDialog(): Boolean {
        return PushUtil.sysAutoShowSwitchDialog()
    }

    override fun setBadgeCount(num: Int) {
        AppIconBadgeUtils.setBadgeCount(
            AppModule.getApplication(),
            num,
            BBaseMC.LAUNCHER_CLASS_NAME
        )
    }
}