package com.dz.business.watching.vm

import com.dz.business.base.data.bean.BaseEmptyBean
import com.dz.business.base.flutter.FlutterMS
import com.dz.business.base.home.HomeME
import com.dz.business.base.livedata.CommLiveData
import com.dz.business.base.load.DBHelper
import com.dz.business.base.vm.PageVM
import com.dz.business.base.vm.event.RequestEventCallback
import com.dz.business.base.vm.event.VMEventOwner
import com.dz.business.watching.data.FavoriteVideoInfo
import com.dz.business.watching.data.ShelfVideoInfo
import com.dz.business.watching.network.WatchingNetWork
import com.dz.business.watching.ui.component.FavoriteItemComp
import com.dz.business.watching.ui.page.tabs.FollowTabFragment
import com.dz.foundation.base.manager.task.TaskManager
import com.dz.foundation.network.onEnd
import com.dz.foundation.network.onError
import com.dz.foundation.network.onResponse
import com.dz.foundation.network.onStart
import com.dz.foundation.router.RouteIntent
import com.dz.foundation.ui.view.recycler.DzRecyclerViewCell
import com.dz.platform.common.toast.ToastManager

/**
 * @Description: 追剧-我的在追
 * @Version:
 */

class FollowTabVM : PageVM<RouteIntent>(), VMEventOwner<RequestEventCallback> {

    //首次加载的数据
    val shelfData = CommLiveData<FavoriteVideoInfo>()

    //加载更多的数据
    val shelfDataMore = CommLiveData<FavoriteVideoInfo>()

    //增删接口返回的数据
    val operateBookResponse = CommLiveData<BaseEmptyBean>()

    /**
     * 首次获取书架数据
     *
     */
    fun getShelfData(refresh: Boolean) {
        WatchingNetWork.get()
            .getFavorites()
            .setParams("")
            .onStart {
                FollowTabFragment.isLoading = true
                eventCallback?.onRequestStart(false) }
            .onResponse {
                it.data?.run {
                    shelfData.value = this
                }
                eventCallback?.onResponse()
            }.onError {
                eventCallback?.onRequestError(it, shelfData.value != null && (shelfData.value?.content?.size?:0) >0)
            }.onEnd {
                FollowTabFragment.isLoading = false
            }.doRequest()
    }


    /**
     * 加载更多
     *
     */
    fun loadShelfDataMore(pageFlag: String) {
        WatchingNetWork.get()
            .getFavorites()
            .setParams(pageFlag)
            .onResponse {
                it.data?.run {
                    shelfDataMore.value = this
                }
            }.onError {

            }.doRequest()
    }

    //删除
    fun deleteBooks(
        bookIds: MutableList<String>
    ) {
        WatchingNetWork.get()
            .deleteFavorites()
            // 删除追剧来源，服务端打点用，10：在追（我的在追）
            .setParams(bookIds, "10")
            .onStart {
                FollowTabFragment.isLoading = true
                statusPoster.statusLoading().post()
            }.onResponse {
                FollowTabFragment.isLoading = false
                statusPoster.statusDismiss().post()
                operateBookResponse.value = it.data
                it.data?.run {
                    if (status == 1) {
                        HomeME.get().deleteFavoriteSuccess()
                            .post(bookIds)
                        for (bid in bookIds) {
                            FlutterMS.get()?.sendEventToFlutter("inBookShelf", mapOf("value" to false, "bookId" to bid))
                            TaskManager.ioTask {
                                DBHelper.insertOrUpdateHistory(bid, false)
                            }
                        }
                        //删除后通知页面刷新
                        HomeME.get().refreshFavorite().post(null)
                    }
                }

            }.onError {
                FollowTabFragment.isLoading = false
                statusPoster.statusDismiss().post()
                ToastManager.showToast(it.message)
            }.doRequest()
    }

    /**
     * 根据书架视频信息列表和监听器，创建书本项的集合。
     *
     * @param data 书架视频信息列表，用于创建书本项。
     * @param actionListener 操作监听器，用于处理书本项的点击等交互事件。
     * @return 返回一个包含创建的书本项的集合。
     */
    fun createBookCells(
        data: List<ShelfVideoInfo>,
        actionListener: FavoriteItemComp.ViewActionListener
    ): List<DzRecyclerViewCell<ShelfVideoInfo>> {
        val cellList = mutableListOf<DzRecyclerViewCell<ShelfVideoInfo>>()
        data.forEachIndexed { index, item ->
            item.listIndex = index
            cellList.add(createBookItem(item, actionListener))
        }
        return cellList
    }

    /**
     * 创建一个书本项，用于展示书架上的视频信息。
     *
     * @param item 展示的书架视频信息。
     * @param actionListener 操作监听器，用于处理书本项的点击等交互事件。
     * @return 返回创建的书本项。
     */
    private fun createBookItem(
        item: ShelfVideoInfo,
        actionListener: FavoriteItemComp.ViewActionListener
    ): DzRecyclerViewCell<ShelfVideoInfo> {
        return DzRecyclerViewCell<ShelfVideoInfo>().apply {
            viewClass = FavoriteItemComp::class.java
            viewData = item
            setActionListener(actionListener)
            spanSize = 1
        }
    }
}