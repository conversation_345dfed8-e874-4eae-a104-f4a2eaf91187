package com.dz.business.watching.ui.page.tabs

import android.os.Bundle
import android.view.View
import android.widget.ImageView
import androidx.core.content.ContextCompat
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.viewModelScope
import com.dz.business.DzDataRepository
import com.dz.business.base.BBaseMR
import com.dz.business.base.data.BBaseKV
import com.dz.business.base.data.PageConstant
import com.dz.business.base.detail.DetailMR
import com.dz.business.base.dialog.DialogMS
import com.dz.business.base.home.HomeME
import com.dz.business.base.main.MainMR
import com.dz.business.base.main.intent.MainIntent
import com.dz.business.base.personal.PersonalME
import com.dz.business.base.delegate.BaseDelegate
import com.dz.business.base.ui.BaseVisibilityFragment
import com.dz.business.base.ui.component.status.StatusComponent
import com.dz.business.base.utils.CommInfoUtil
import com.dz.business.base.vm.event.RequestEventCallback
import com.dz.business.base.watching.WatchingME
import com.dz.business.base.widget.WidgetMS
import com.dz.business.track.events.DzTrackEvents
import com.dz.business.track.trace.OmapNode
import com.dz.business.track.trace.QmapNode
import com.dz.business.track.trace.SourceNode
import com.dz.business.watching.R
import com.dz.business.watching.data.FavoriteVideoInfo
import com.dz.business.watching.data.ShelfVideoInfo
import com.dz.business.watching.databinding.WatchingFollowTabFragmentBinding
import com.dz.business.watching.ui.component.FavoriteItemComp
import com.dz.business.watching.utils.FollowDataUtils
import com.dz.business.watching.vm.FollowTabVM
import com.dz.foundation.base.utils.LogUtil
import com.dz.foundation.network.requester.RequestException
import com.dz.foundation.ui.view.recycler.DzExposeRvItemUtil
import com.dz.foundation.ui.view.recycler.DzRecyclerViewCell
import com.dz.platform.common.base.ui.dialog.PDialogComponent
import com.dz.platform.common.router.onDismiss
import com.dz.platform.common.router.onShow
import com.dz.platform.common.toast.ToastManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * @Description:我的在追页面 追剧的tab
 * @Version:1.0
 */
class FollowTabFragment :
    BaseVisibilityFragment<WatchingFollowTabFragmentBinding, FollowTabVM>(),
    FavoriteItemComp.ViewActionListener {
    // 是否编辑状态
    companion object {
        var isEditBook: Boolean = false//是否编辑状态
        var isLoading: Boolean = false//加载数据状态中
        var isEmpty = false //是否空页面
    }

    //弹窗实例 用于主动关闭弹窗
    private var commonAlertDialog: PDialogComponent<*>? = null

    var mBookList = mutableListOf<ShelfVideoInfo>()//书架剧集
    var selectBooks = mutableListOf<ShelfVideoInfo>()//选中剧集
    var pageFlag: String? = null//加载更多标识

    private var tipDelegate: BaseDelegate? = null

    //用于RecycleView的曝光打点 手动触发
    private var dzExposeRvItemUtil: DzExposeRvItemUtil = DzExposeRvItemUtil()

    override fun onEveryLoad() {
        dzExposeRvItemUtil.directExposeRecyclerItem(mViewBinding.drv)
    }

    // 初始化数据请求
    override fun onLazyLoad() {
        mViewModel.getShelfData(true)
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        // 清空操作响应数据，避免状态泄漏
        try {
            mViewModel.operateBookResponse.value = null
        } catch (e: Exception) {
            e.printStackTrace()
            LogUtil.d(TAG, "onSaveInstanceState, message:" + e.message)
        }
    }

    override fun initStatusComponent(): StatusComponent {
        return StatusComponent(mViewBinding.flRoot)
    }

    //初始化数据
    override fun initData() {

    }

    override fun initView() {
        // 配置刷新和加载更多的监听 不显示没有更多提示ui
        mViewBinding.refreshLayout.whenDataNotFullShowFooter = false
        mViewBinding.refreshLayout.setDzRefreshListener {
            mViewModel.getShelfData(true)
        }
        mViewBinding.refreshLayout.setDzLoadMoreListener {
            pageFlag?.let {
                mViewModel.loadShelfDataMore(it)
            }
        }
        mViewBinding.drv.itemAnimator = null

        // 展示打开系统通知提醒
        tipDelegate = DialogMS.get()
            ?.initPushTipDelegate(getPageId(), getContentView(), mViewBinding.drv)?.apply {
                bind(null, this@FollowTabFragment)
            }
    }

    override fun onResume() {
        super.onResume()
        mViewModel.getShelfData(true)
        commonAlertDialog?.dismiss()
        commonAlertDialog = null
        dzExposeRvItemUtil.directExposeRecyclerItem(mViewBinding.drv)
        // 设置编辑状态为非编辑模式
        HomeME.get().editFavorite().post(false)
    }

    override fun onDestroy() {
        super.onDestroy()
        // 清理资源
        commonAlertDialog?.dismiss()
        commonAlertDialog = null
    }

    override fun initListener() {
        //网络数据回调
        mViewModel.setEventCallback(this, object : RequestEventCallback {
            override fun onRequestStart(hasData: Boolean) {
                //加载动画
                if (mViewModel.shelfData.value == null || mViewModel.shelfData.value?.content?.size == 0) {
                    mViewModel.statusPoster.statusLoading().post()
                }
            }

            override fun onResponse() {
                // 根据数据状态更新UI
                if (mViewModel.shelfData.value == null || mViewModel.shelfData.value?.content?.size == 0) {
                    mViewModel.statusPoster
                        .statusDataEmpty()
                        .marginTop(0)
                        .actionText("去剧场")
                        .iconRes(R.drawable.bbase_ic_no_follow_drama)
                        .actionTextColor(
                            ContextCompat.getColor(
                                mViewBinding.refreshLayout.context,
                                R.color.common_E1442E
                            )
                        )
                        .actionBg(ContextCompat.getColor(requireContext(), R.color.common_FFF55041))
                        .actionBgResource(R.drawable.common_refresh_btn_bg)
                        .des("暂无内容，去剧场挑几部吧～")
                        .desColor(ContextCompat.getColor(requireContext(), R.color.common_FFB6BABE))
                        .actionListener {
                            MainMR.get().main().apply { selectedTab = MainIntent.TAB_THEATRE }
                                .start()
                        }
                        .post()
                } else {
                    mViewModel.statusPoster.statusDismiss().post()
                }
            }

            override fun onRequestError(e: RequestException, hasData: Boolean) {
                mViewModel.statusPoster.statusDismiss().post()
                if (hasData) {
                    if (mViewBinding.refreshLayout.isRefreshing || mViewBinding.refreshLayout.isLoading) {
                        ToastManager.showToast(e.message)
                    }
                } else {
                    mViewModel.statusPoster.statusNetError(e).marginTop(0)
                        .actionText("刷新")
                        .actionTextColor(
                            ContextCompat.getColor(
                                mViewBinding.refreshLayout.context,
                                R.color.common_E1442E
                            )
                        )
                        .actionBg(ContextCompat.getColor(requireContext(), R.color.common_FFF55041))
                        .actionBgResource(R.drawable.common_refresh_btn_bg)
                        .des("当前网络欠佳，点击重新尝试")
                        .desColor(ContextCompat.getColor(requireContext(), R.color.common_FFB6BABE))
                        .actionListener {
                            mViewModel.getShelfData(true)
                        }.post()
                }
                if (mViewBinding.refreshLayout.isRefreshing) {
                    mViewBinding.refreshLayout.finishDzRefresh()
                }
                if (mViewBinding.refreshLayout.isLoading) {
                    mViewBinding.refreshLayout.finishDzLoadMoreFail()
                }
            }

        })
        // 删除按钮点击监听
        mViewBinding.tvDelete.registerClickAction {
            if (selectBooks.isNotEmpty()) {
                BBaseMR.get().commonAlertDialog().apply {
                    title = "确认要删除这条记录吗？此操作无法撤销"
                }.onSure {
                    deleteShelfBooks()
                }.onShow {
                    commonAlertDialog = it
                }.onDismiss {
                    commonAlertDialog = null
                }.start()
            } else {
                ToastManager.showToast("请选择要删除的剧")
            }
        }
        // 全选按钮点击监听
        mViewBinding.tvAllSelect.registerClickAction {
            if (selectBooks.size == mBookList.size) {
                mViewBinding.tvAllSelect.text = "全选"
                editAllBooks(isEdit = true, isSelected = false)
            } else {
                mViewBinding.tvAllSelect.text = "取消全选"
                editAllBooks(isEdit = true, isSelected = true)
            }
        }
    }

    override fun subscribeEvent(lifecycleOwner: LifecycleOwner, lifecycleTag: String) {
        // 刷新书架数据
        HomeME.get().refreshFavorite().observe(lifecycleOwner, lifecycleTag) {
            if (isResumed) {
                mViewModel.getShelfData(true)
            }
        }

        // 编辑状态改变
        HomeME.get().editFavorite().observe(lifecycleOwner, lifecycleTag) {
            if (!isEditBook && it) {
                toEditBook("")
            }
            isEditBook = it
            if (isEditBook) {
                mViewBinding.bottomLayout.visibility = View.VISIBLE
                mViewBinding.tvDelete.alpha = 0.4f
                mViewBinding.tvAllSelect.text = "全选"
            } else {
                mViewBinding.bottomLayout.visibility = View.GONE
                editAllBooks(isEdit = false, isSelected = false)
            }
        }
        // 选中剧集变化
        WatchingME.get().selectVideosEvent().observe(lifecycleOwner, lifecycleTag) {
            selectBooks = mViewBinding.drv.adapter?.allCells?.filter {
                (it.viewData as? ShelfVideoInfo)?.isSelected == true
            }?.mapNotNull { it.viewData as? ShelfVideoInfo }?.toMutableList() ?: mutableListOf()
            if (selectBooks.isNotEmpty()) {
                mViewBinding.tvDelete.alpha = 1f
                if (selectBooks.size == mBookList.size) {
                    mViewBinding.tvAllSelect.text = "取消全选"
                } else {
                    mViewBinding.tvAllSelect.text = "全选"
                }
            } else {
                mViewBinding.tvAllSelect.text = "全选"
                mViewBinding.tvDelete.alpha = 0.4f
            }
        }
        // 用户变化时刷新数据
        PersonalME.get().onUserAccountChanged().observeForever(lifecycleTag) {
            mViewModel.viewModelScope.launch {
                mViewModel.shelfData.value = null
                mBookList.clear()
                mViewBinding.drv.removeAllCells()
                mViewModel.getShelfData(false)
            }
        }
    }

    override fun subscribeObserver(lifecycleOwner: LifecycleOwner) {
        // 监听书架数据更新
        mViewModel.shelfData.observeForever {
            it?.run {
                createCells(this)
                updateFollowedTheatreForWidget(this.content)
                if (this.content?.size == 0 || this.content?.size == null) {
                    isEmpty = true
                    mViewBinding.refreshLayout.finishDzRefresh()
                } else {
                    isEmpty = false
                    mViewBinding.refreshLayout.finishDzRefresh(it.hasMore, hideNoMore = true)
                }
                if (isEditBook) {//退出编辑状态
                    HomeME.get().editFavorite().post(false)
                }
                WatchingME.get().sendEventToShowEditButton().post(isEmpty)
            }
        }
        // 监听加载更多数据更新
        mViewModel.shelfDataMore.observe(lifecycleOwner) {
            it.run {
                createMoreCells(it)
            }
        }
        // 监听删除操作结果
        mViewModel.operateBookResponse.observe(lifecycleOwner) {
            it?.run {
                if (isEditBook) {//退出编辑状态
                    HomeME.get().editFavorite().post(false)
                }
            }
        }
    }

    /**
     * 创建列表项
     */
    private fun createCells(
        shelfBean: FavoriteVideoInfo,
    ) {
        pageFlag = shelfBean.pageFlag
        val cellList = mutableListOf<DzRecyclerViewCell<*>>()
        var isRefresh = false
        if (mViewBinding.drv.allCells.size > 0) {
            isRefresh = true
        }
        mBookList.clear()
        mViewBinding.drv.removeAllCells()
        if (!shelfBean.content.isNullOrEmpty()) {
            shelfBean.content?.let {
                mBookList.addAll(it)
            }
        }
        val bookCells = mViewModel.createBookCells(mBookList, this)
        cellList.addAll(bookCells)
        if (isRefresh) {
            dzExposeRvItemUtil.directExposeRecyclerItem(mViewBinding.drv)
        }
        mViewBinding.drv.addCells(cellList)
        mViewBinding.drv.scrollToPosition(0)
    }

    //删除选中的剧
    private fun deleteShelfBooks() {
        val bookIdIds = mutableListOf<String>()
        for (book in selectBooks) {
            bookIdIds.add(book.bookId ?: "")
        }
        mViewModel.deleteBooks(bookIdIds)
    }

    //打开编辑
    override fun toEditBook(bookId: String?) {
        mViewBinding.drv.adapter?.allCells?.forEach {
            val item = (it.viewData as? ShelfVideoInfo)
            item?.isEditBook = true
            item?.isSelected = false
            if (item?.bookId == bookId) {
                item?.isSelected = true
            }
        }
        mViewBinding.drv.notifyDataSetChanged()
        // 更新编辑状态
        isEditBook = true
        HomeME.get().editFavorite().post(true)
    }


    /**
     * 加载更多列表项
     */
    private fun createMoreCells(shelfBean: FavoriteVideoInfo) {
        // 更新分页标志
        pageFlag = shelfBean.pageFlag
        // 解析书籍内容
        shelfBean.content?.let {
            val cellList = mutableListOf<DzRecyclerViewCell<*>>()
            // 根据编辑状态设置书籍项的属性
            if (isEditBook) {
                if (it.isNotEmpty()) {
                    for (i in it.indices) {
                        it[i].isEditBook = true
                    }
                }
            }
            mBookList.addAll(it)
            val bookCells = mViewModel.createBookCells(it, this)
            cellList.addAll(bookCells)
            mViewBinding.drv.addCells(cellList)
        }
        // 结束加载状态，显示更多内容
        mViewBinding.refreshLayout.finishDzLoadMoreSuccess(shelfBean.hasMore, hideNoMore = true)
    }

    /**
     * 编辑剧状态
     */
    private fun editAllBooks(isEdit: Boolean, isSelected: Boolean) {
        mViewBinding.drv.adapter?.allCells?.forEach {
            val item = (it.viewData as? ShelfVideoInfo)
            item?.isEditBook = isEdit
            item?.isSelected = isSelected
        }
        mViewBinding.drv.notifyDataSetChanged()
    }

    //跳二级播放器
    override fun gotoPlayer(data: ShelfVideoInfo?, ivBook: ImageView) {
        // 设置视频来源相关参数
        data?.omap?.run {
            scene = SourceNode.origin_zj
            originName = SourceNode.origin_name_zj
            channelName = SourceNode.channel_name_follow
        }
        // 构造播放参数并启动播放
        DetailMR.get().videoList().apply {
            type = 0
            bookId = data?.bookId
//            bookName = data?.bookName
            chapterIndex = data?.chapterIndex
            chapterId = data?.chapterId
            updateNum = data?.updateNum
            videoStarsNum = data?.videoStarsNum
            origin = SourceNode.origin_zj
            originName = SourceNode.origin_name_zj
            channelId = SourceNode.channel_id_follow
            channelName = SourceNode.channel_name_follow
            columnId = "zj"
            columnName = "追剧"
            cOmap = data?.omap
            backToRecommend = false
            firstTierPlaySource = "追剧"
            secondTierPlaySource = "追剧-在追"
            thirdTierPlaySource = "追剧-在追"
        }.start()
    }

    // 获取当前页面名称
    override fun getPageName(): String {
        return "在追"
    }

    override fun getPageId() = PageConstant.PAGE_ID_FAVORITE_WATCHING

    /**
     * 缓存桌面组件需要的在追数据
     * 注意：因组件只用前6条数据，因此只缓存首页数据
     */
    private fun updateFollowedTheatreForWidget(list: MutableList<ShelfVideoInfo>?) {
        kotlin.runCatching {
            mViewModel.viewModelScope.launch(Dispatchers.IO) {
                DzDataRepository.followDao().deleteAllByUserId(BBaseKV.userId)
                val followList = FollowDataUtils.generateFollowData(list)
                DzDataRepository.followDao().insert(followList)
            }
            WidgetMS.get()?.sendAppWidgetRefreshBroadcast()
        }.onFailure {
            it.printStackTrace()
        }
    }

    override fun onVisibilityChanged(visible: Boolean) {
        super.onVisibilityChanged(visible)
        if (visible) {
            DzTrackEvents.get().hivePv()
                .pType("page_view")
                .withOmapSource(OmapNode().apply {
                    rgts = BBaseKV.regTime
                    nowChTime = BBaseKV.chTime
                    is_login = if (CommInfoUtil.hasLogin()) 1 else 0
                    pageName = "追剧-${getPageName()}"
                })
                .withQmapSource(QmapNode().apply {
                    eventType = "page_view"
                })
                .track()
        }
    }
}