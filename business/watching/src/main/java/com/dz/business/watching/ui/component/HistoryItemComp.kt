package com.dz.business.watching.ui.component

import android.content.Context
import android.util.AttributeSet
import android.view.View
import androidx.core.content.ContextCompat
import com.blankj.utilcode.util.GsonUtils
import com.dz.business.base.data.bean.StrategyInfo
import com.dz.business.base.detail.DetailMR
import com.dz.business.base.utils.ViewExposeUtil
import com.dz.business.base.watching.WatchingME
import com.dz.business.repository.entity.HistoryEntity
import com.dz.business.track.events.DzTrackEvents
import com.dz.business.track.trace.OmapNode
import com.dz.business.track.trace.SourceNode
import com.dz.business.watching.R
import com.dz.business.watching.databinding.WatchingHistoryItemCompBinding
import com.dz.foundation.base.utils.dp
import com.dz.foundation.imageloader.loadRoundImg
import com.dz.foundation.ui.view.custom.ActionListener
import com.dz.foundation.ui.view.custom.ActionListenerOwner
import com.dz.platform.common.base.ui.component.UIConstraintComponent

/**
 * @Description: 浏览记录-item
 * @Version:1.0
 */
class HistoryItemComp @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int? = 0,
) : UIConstraintComponent<WatchingHistoryItemCompBinding, HistoryEntity>(
    context,
    attrs,
    defStyleAttr!!
),
    //HistoryItemComp中的事件监听器。
    ActionListenerOwner<HistoryItemComp.ViewActionListener> {
    interface ViewActionListener : ActionListener {
        fun addFavorite(data: HistoryEntity?)
        fun deleteFavorite(data: HistoryEntity?)
        fun toEditBook(data: HistoryEntity?)
    }

    // 当前内容项的位置
    private var mContentPos = 0

    override fun initData() {
    }

    override fun initView() {
    }

    override fun initListener() {
        // 注册根布局的点击动作
        mViewBinding.root.registerClickAction {
            mData?.run {
                // 根据isEditBook标志决定执行选中状态切换还是启动视频播放
                if (isEditBook) {
                    isSelected = !isSelected
                    setSelectBook(isSelected)
                } else {
                    // 初始化策略信息对象，并根据情况从数据中解析出omap信息
                    var momap = StrategyInfo()
                    if (!mData?.omap.isNullOrEmpty() && mData?.omap != "null") {
                        momap = GsonUtils.fromJson(mData?.omap, StrategyInfo::class.java)
                    }
                    momap.run {
                        scene = SourceNode.origin_zj
                        originName = SourceNode.origin_name_zj
                        channelName = SourceNode.channel_name_gkls
                    }
                    // 配置视频列表信息，并启动播放
                    DetailMR.get().videoList().apply {
                        bookId = mData?.bid
                        chapterId = mData?.cur_cid
                        origin = SourceNode.origin_zj
                        originName = SourceNode.origin_name_zj
                        channelId = SourceNode.channel_id_gkls
                        channelName = SourceNode.channel_name_gkls
                        cOmap = momap
                        backToRecommend = false
                        firstTierPlaySource = "追剧"
                        secondTierPlaySource = "追剧-浏览记录"
                        thirdTierPlaySource = "追剧-浏览记录"
                        alias = mData?.alias
                        bookAlias = mData?.book_name
                    }.start()
                }
            }
        }
        // 注册根布局的长按监听器
        mViewBinding.root.setOnLongClickListener(object : View.OnLongClickListener {
            override fun onLongClick(v: View?): Boolean {
                // 当isEditBook为false时，触发编辑书籍操作
                if (mData?.isEditBook == false) {
                    mActionListener?.toEditBook(mData)
                    return true
                }
                return false
            }
        })
        // 注册收藏按钮的点击动作监听器
        mViewBinding.llFavorite.registerClickAction {
            if (mData?.add_to_shelf == true) {
                mActionListener?.deleteFavorite(mData)
            } else {
                mActionListener?.addFavorite(mData)
            }
        }
    }

    private fun setSelectBook(isSelected: Boolean) {
        if (isSelected) {
            mViewBinding.ivSelect.setImageResource(R.drawable.watching_ic_book_selected)
        } else {
            mViewBinding.ivSelect.setImageResource(R.drawable.watching_ic_history_unselected)
        }
        WatchingME.get().selectVideosEvent().post(null)
    }

    // 添加收藏
    fun addFavoriteSuccess() {
        showFavorite()
    }

    // 删除收藏
    fun deleteFavoriteSuccess() {
        showNotFavorite()
    }

    /**
     * 绑定RecyclerView项的数据
     * @param model 数据对象
     * @param position 项的位置
     */
    override fun onBindRecyclerViewItem(model: HistoryEntity?, position: Int) {
        super.onBindRecyclerViewItem(model, position)
        mContentPos = position
    }

    override fun bindData(data: HistoryEntity?) {
        super.bindData(data)
        //ui
        mData?.run {
            //编辑状态
            if (isEditBook) {
                mViewBinding.ivSelect.visibility = VISIBLE
                mViewBinding.llFavorite.visibility = GONE
                setSelectBook(isSelected)
            } else {//正常状态
                mViewBinding.ivSelect.visibility = GONE
                mViewBinding.llFavorite.visibility = VISIBLE
            }
            mViewBinding.ivCover.loadRoundImg(
                    coverurl,
                    8.dp,
                    placeholder = R.drawable.bbase_ic_cover_default,
                    error = R.drawable.bbase_ic_cover_default,
                    width = 74, height = 105
            )
            mViewBinding.tvBookName.text = book_name
            cur_index.let {
                mViewBinding.tvCharacterHistory.text =
                    String.format("观看至%s集", it)
            }
            if (add_to_shelf == true) {
                showFavorite()
            } else {
                showNotFavorite()
            }
        }
    }

    //显示未收藏
    private fun showNotFavorite() {
        mViewBinding.ivFavorite.setImageResource(R.drawable.watching_ic_not_favorite)
        mViewBinding.tvFavorite.text = "追剧"
        mViewBinding.tvFavorite.setTextColor(
            ContextCompat.getColor(
                context,
                R.color.common_FF5E6267
            )
        )
    }

    //显示已收藏
    private fun showFavorite() {
        mViewBinding.ivFavorite.setImageResource(R.drawable.watching_ic_favorite)
        mViewBinding.tvFavorite.text = "已追剧"
        mViewBinding.tvFavorite.setTextColor(
            ContextCompat.getColor(
                context,
                R.color.common_FF929AA1
            )
        )

    }

    override var mActionListener: ViewActionListener? = null

    //曝光打点
    override fun onExpose(isFirstExpose: Boolean) {
        mData?.let {
            val first = ViewExposeUtil.checkIsFirstExpose2(recyclerView, getItemDataId())
            if (first) {
                var momap = StrategyInfo()
                if (!it.omap.isNullOrEmpty() && it.omap != "null") {
                    momap = GsonUtils.fromJson(it.omap, StrategyInfo::class.java)
                }
                momap.run {
                    scene = SourceNode.origin_zj
                    originName = SourceNode.origin_name_zj
                    channelName = SourceNode.channel_name_gkls
                }
                DzTrackEvents.get().hiveExposure().show(OmapNode().apply {
                    origin = SourceNode.origin_zj
                    channelId = SourceNode.channel_id_gkls
                    channelName = SourceNode.channel_name_gkls
                    channelPos = "0"
                    columnId = ""
                    columnName = ""
                    columnPos = ""
                    contentId = it.bid ?: ""
                    contentPos = mContentPos
                    contentType = "2"
                    partnerId = ""
                    playletId = it.bid ?: ""
                    playletName = it.book_name ?: ""
                    tag = ""
                    tagId = ""
                    finishStatus = it.finish_status_cn ?: ""
                    firstCanFree = ""
                    positionName = ""
                    setStrategyInfo(momap)
                }).track()
                DzTrackEvents.get().bookViewShow()
                    .bookId(it.bid)
                    .bookName(it.book_name)
                    .bookIndex(mContentPos)
                    .origin("追剧-浏览记录")
                    .columnName("追剧-浏览记录")
                    .chapterId(it.cur_cid)
                    .track()
            }
        }
    }
    fun getItemDataId(): String {
        var itemDataId = ""
        mData?.let {
            itemDataId = it.javaClass.name+ System.identityHashCode(mData).toString()
        }
        return itemDataId
    }
}