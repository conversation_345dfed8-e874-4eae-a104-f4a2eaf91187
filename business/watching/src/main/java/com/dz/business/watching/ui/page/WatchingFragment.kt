package com.dz.business.watching.ui.page

import android.content.Context
import android.os.Bundle
import android.util.TypedValue
import android.view.View
import android.view.View.GONE
import android.view.View.VISIBLE
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.lifecycle.LifecycleOwner
import androidx.viewpager2.widget.ViewPager2
import com.dz.business.base.data.BBaseKV
import com.dz.business.base.data.BBaseKV.followBubbleStatus
import com.dz.business.base.data.PageConstant
import com.dz.business.base.home.HomeME
import com.dz.business.base.main.MainME
import com.dz.business.base.track.ITracker
import com.dz.business.base.track.TrackUtil
import com.dz.business.base.ui.BaseLazyFragment
import com.dz.business.base.ui.component.status.StatusComponent
import com.dz.business.base.utils.CommInfoUtil
import com.dz.business.base.watching.WatchingME
import com.dz.business.track.events.DzTrackEvents
import com.dz.business.track.trace.OmapNode
import com.dz.business.track.trace.QmapNode
import com.dz.business.watching.R
import com.dz.business.watching.adapter.FragmentViewPagerAdapter
import com.dz.business.watching.databinding.WatchingTabFragmentBinding
import com.dz.business.watching.ui.page.tabs.FollowTabFragment
import com.dz.business.watching.ui.page.tabs.HistoryTabFragment
import com.dz.business.watching.vm.WatchingTabVM
import com.dz.foundation.base.utils.LogUtil
import com.dz.foundation.base.utils.ScreenUtil
import com.dz.foundation.base.utils.dp
import com.dz.foundation.ui.view.tabbar.ViewPagerHelper
import com.dz.foundation.ui.view.tabbar.commonnavigator.CommonNavigator
import com.dz.foundation.ui.view.tabbar.commonnavigator.abs.CommonNavigatorAdapter
import com.dz.foundation.ui.view.tabbar.commonnavigator.abs.IPagerIndicator
import com.dz.foundation.ui.view.tabbar.commonnavigator.abs.IPagerTitleView
import com.dz.foundation.ui.view.tabbar.commonnavigator.indicators.ArcPagerIndicator
import com.dz.foundation.ui.view.tabbar.commonnavigator.titles.TextSizeTransitionPagerTitleView
import com.sensorsdata.analytics.android.sdk.SensorsDataIgnoreTrackAppViewScreen

/**
 * @Description: 追剧 :tab + tabView
 * 追剧模块的主界面Fragment，负责显示用户正在追的收藏和浏览历史。
 */
@SensorsDataIgnoreTrackAppViewScreen
class WatchingFragment : BaseLazyFragment<WatchingTabFragmentBinding, WatchingTabVM>() {
    // ViewPager的适配器
    private var pagerAdapter: FragmentViewPagerAdapter? = null

    // 当前显示的Fragment
    private var currentFragment: Fragment? = null

    override fun getPageLazyTag(): PageLazyTag {
        return PageLazyTag.FRAGMENT_WATCHING
    }


    /**
     * 懒加载回调方法，用于初始化数据或视图。
     */
    override fun onLazyLoad() {

    }

    /**
     * 初始化数据方法
     */
    override fun initData() {
        mViewModel.getDataInfo()
    }

    /**
     * Fragment视图被创建后的回调方法。
     * @param savedInstanceState 如果在Fragment重建时有保存的状态，则为该状态的Bundle对象
     */
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        savedInstanceState?.let {
            mViewModel.currentIndex = it.getInt("currentIndex")
            mViewBinding.vp.currentItem = mViewModel.currentIndex
            updateCurrentFragment()
        }
    }

    /**
     * 在Fragment保存状态时的回调方法。
     * @param outState 用于保存Fragment状态的Bundle对象
     */
    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        try {
            outState.putInt("currentIndex", mViewModel.currentIndex)
        } catch (e: Exception) {
            e.printStackTrace()
            LogUtil.d(TAG, "onSaveInstanceState error:" + e.message)
        }
    }

    /**
     * 初始化视图方法，用于设置界面布局和控件初始状态。
     */
    override fun initView() {
        initTabBar()
        setChannel()
    }

    /**
     * 初始化底部导航栏的方法。
     */
    private fun initTabBar() {
        // 设置导航栏顶部padding，与状态栏高度一致
        val topPadding = ScreenUtil.getStatusHeight(requireContext())
        mViewBinding.vp.isUserInputEnabled = true;
        mViewBinding.clRoot.setPadding(0, topPadding, 0, 0)
        mViewBinding.tvTitle.setTextSize(TypedValue.COMPLEX_UNIT_PX, 20f.dp)
        mViewBinding.tvTitle.paint.isFakeBoldText = true
        // 设置标题栏文字大小和颜色
        val commonNavigator = CommonNavigator(requireContext()).apply {
            adapter = object : CommonNavigatorAdapter() {
                override fun getTitleView(context: Context, index: Int): IPagerTitleView {
                    //标题
                    val titleView =
                        TextSizeTransitionPagerTitleView(context).apply {
                            normalColor = ContextCompat.getColor(context, R.color.common_FF7F7F7F)
                            selectedColor = ContextCompat.getColor(context, R.color.common_FF161718)
                            text = (mViewModel.list[index].tabName ?: "我的在追")
                            if (mViewModel.list[index].tabName == "我的在追") {
                                setPadding(10.dp, 0, 12.dp, 0)
                                setTextSize(TypedValue.COMPLEX_UNIT_PX, 20f.dp)
                            } else {
                                setPadding(12.dp, 0, 10.dp, 0)
                                setTextSize(TypedValue.COMPLEX_UNIT_PX, 16f.dp)
                            }
                            selectTextSize = 20f.dp
                            deselectTextSize = 16f.dp
                            setOnClickListener {
                                mViewBinding.vp.currentItem = index
                                updateCurrentFragment()
                            }
                            setSelectStatusChanged {
                                paint.isFakeBoldText = it
                            }
                        }
                    return titleView
                }

                override fun getCount(): Int {
                    return mViewModel.list.size
                }

                override fun getIndicator(context: Context?): IPagerIndicator {
                    val indicator = ArcPagerIndicator(context).apply {
                        setBitmapResource(R.drawable.watching_arc_tab) // 设置图片资源
                        yOffset = (-3.5f).dp
                    }
                    return indicator
                }
            }
        }
        // 设置导航栏
        mViewBinding.tabbar.navigator = commonNavigator
    }

    /**
     * 初始化状态组件的方法，用于设置界面的状态栏颜色等。
     * @return 状态组件实例
     */
    override fun initStatusComponent(): StatusComponent {
        return super.initStatusComponent().bellow(mViewBinding.tabbar)
            .background(R.color.common_transparent)//设置状态组件背景
    }

    /**
     * 初始化监听器的方法，用于设置界面的各种点击或状态改变监听。
     */
    override fun initListener() {
        // ViewPager页面切换监听
        mViewBinding.vp.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                super.onPageSelected(position)
                // 更新当前页面
                mViewModel.currentIndex = position
                updateCurrentFragment()
                WatchingME.get().sendEventToShowEditButton().post(null)
            }
        })
        //编辑点击
        mViewBinding.llEdit.registerClickAction(300) {
            // 根据当前页面索引和编辑状态执行相应的逻辑
            if (mViewModel.currentIndex == 0) {
                if (FollowTabFragment.isEmpty || FollowTabFragment.isLoading) {
                    return@registerClickAction
                }
                mViewBinding.tvTitle.text = "我的在追"
                if (FollowTabFragment.isEditBook) {
                    mViewBinding.tabbar.visibility = VISIBLE
                    mViewBinding.tvTitle.visibility = GONE
                    HomeME.get().editFavorite().post(false)
                } else {
                    mViewBinding.tabbar.visibility = GONE
                    mViewBinding.tvTitle.visibility = VISIBLE
                    HomeME.get().editFavorite().post(true)
                }
            } else {
                if (HistoryTabFragment.isEmpty || HistoryTabFragment.isLoading) {
                    return@registerClickAction
                }
                mViewBinding.tvTitle.text = "浏览记录"
                if (HistoryTabFragment.isEditBook) {
                    mViewBinding.tabbar.visibility = VISIBLE
                    mViewBinding.tvTitle.visibility = GONE
                    HomeME.get().editFavorite().post(false)
                } else {
                    mViewBinding.tabbar.visibility = GONE
                    mViewBinding.tvTitle.visibility = VISIBLE
                    HomeME.get().editFavorite().post(true)
                }
            }
        }
    }

    /**
     * 更新当前显示的Fragment的方法。
     */
    private fun updateCurrentFragment() {
        // 根据当前页面索引获取对应的Fragment
        currentFragment = mViewModel.getFragmentByIndex(mViewBinding.vp.currentItem)
        // 若当前Fragment不是ITracker接口的实现，则记录日志
        if (currentFragment !is ITracker) {
            LogUtil.e(
                TrackUtil.TAG,
                "追剧更新Fragment失败！currentItem:${mViewBinding.vp.currentItem} currentFragment:$currentFragment"
            )
        }
    }

    /**
     * 订阅观察者的回调方法。
     * @param lifecycleOwner 生命周期所有者
     */
    override fun subscribeObserver(lifecycleOwner: LifecycleOwner) {}

    /**
     * 设置频道的逻辑。
     * 主要包括初始化ViewPager适配器、绑定ViewPager和TabLayout、设置ViewPager的属性以及更新当前片段。
     * 这一方法确保了频道的视图和逻辑正确初始化并关联。
     */
    private fun setChannel() {
        // 初始化pagerAdapter并设置为FragmentViewPagerAdapter，用于管理各个片
        pagerAdapter = FragmentViewPagerAdapter(this@WatchingFragment, mViewModel.fragments)
        // 绑定TabLayout和ViewPager，实现标签和页面的联
        ViewPagerHelper.bind(mViewBinding.tabbar, mViewBinding.vp)
        // 设置ViewPager的离屏页面限制，确保滑动时两侧的页面不会被销毁
        mViewBinding.vp.offscreenPageLimit = mViewModel.list.size
        // 设置ViewPager的适配器
        mViewBinding.vp.adapter = pagerAdapter
        //设置默认位置
        mViewBinding.vp.setCurrentItem(mViewModel.currentIndex, false)
        updateCurrentFragment()
    }

    // 编辑书架的状态标志
    companion object {
        var isEditBook: Boolean = false//是否编辑书籍状态
    }

    override fun subscribeEvent(lifecycleOwner: LifecycleOwner, lifecycleTag: String) {
        //接受事件显示隐藏编辑按钮
        WatchingME.get().sendEventToShowEditButton().observe(lifecycleOwner, lifecycleTag) {
            when (mViewModel.currentIndex) {
                0 -> mViewBinding.llEdit.visibility = if (FollowTabFragment.isEmpty) GONE else VISIBLE
                1 -> mViewBinding.llEdit.visibility = if (HistoryTabFragment.isEmpty) GONE else VISIBLE
                else -> {}
            }
        }
        //书架页是否编辑书架
        HomeME.get().editFavorite().observe(lifecycleOwner, lifecycleTag) {
            isEditBook = it
            // 根据isEditBook的值，动态修改界面布局和按钮功能
            if (isEditBook) {
                mViewBinding.tabbar.visibility = GONE
                mViewBinding.tvTitle.visibility = VISIBLE
                mViewBinding.viewBottom.visibility = View.GONE
                mViewBinding.bottomLine.visibility = View.GONE
                mViewBinding.vp.isUserInputEnabled = false
                mViewBinding.tvEdit.text = "退出"
                mViewBinding.ivEdit.setImageResource(R.drawable.watching_ic_close)
            } else {
                mViewBinding.tabbar.visibility = VISIBLE
                mViewBinding.tvTitle.visibility = GONE
                mViewBinding.viewBottom.visibility = View.VISIBLE
                mViewBinding.bottomLine.visibility = View.VISIBLE
                mViewBinding.vp.isUserInputEnabled = true
                mViewBinding.tvEdit.text = "编辑"
                mViewBinding.ivEdit.setImageResource(R.drawable.watching_ic_edit)
            }
        }
        MainME.get().exitDetail().observe(lifecycleOwner) {
            //设置默认位置，收到消息后手动去浏览记录
            if (followBubbleStatus == 1) {
                mViewBinding.vp.setCurrentItem(1, true)
            }

        }

        MainME.get().bubbleDismiss().observe(lifecycleOwner) {
            //气泡消失后通知不去浏览记录
            if (followBubbleStatus == 2) {
                mViewBinding.vp.setCurrentItem(0, true)
            }

        }
    }

    /**
     * 获取当前页面的名称。
     * 如果当前片段实现了ITracker接口，则返回该接口中定义的页面名称；否则返回默认的页面名称。
     * 这个方法用于页面追踪和统计。
     * @return 当前页面的名称
     */
    override fun getPageName(): String {
        val title = (currentFragment as? ITracker)?.getPageName()
        if (!title.isNullOrEmpty()) {
            return "追剧-$title"
        }
        return "追剧"
    }

    override fun onVisibilityChanged(visible: Boolean) {
        super.onVisibilityChanged(visible)
        if (visible) {
            DzTrackEvents.get().hivePv()
                .pType("page_view")
                .withOmapSource(OmapNode().apply {
                    rgts = BBaseKV.regTime
                    nowChTime = BBaseKV.chTime
                    is_login = if (CommInfoUtil.hasLogin()) 1 else 0
                    pageName = "追剧"
                })
                .withQmapSource(QmapNode().apply {
                    eventType = "page_view"
                })
                .track()
        }

    }

    override fun getPageId(): String = PageConstant.PAGE_ID_FAVORITE_WATCHING
}