<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:ignore="MissingDefaultResource">

    <merge tools:parentTag="com.dz.foundation.ui.widget.DzConstraintLayout">

        <com.dz.foundation.ui.widget.DzConstraintLayout
            android:id="@+id/cl_root"
            android:layout_width="@dimen/common_dp0"
            android:layout_height="wrap_content"
            android:maxWidth="@dimen/common_dp110"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <com.dz.foundation.ui.widget.DzImageView
                android:id="@+id/iv_book"
                android:layout_width="@dimen/common_dp110"
                android:layout_height="@dimen/common_dp157.5"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.dz.foundation.ui.widget.DzTextView
                android:id="@+id/tv_rank_bg"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/common_dp28"
                android:background="@drawable/search_drama_bg"
                app:layout_constraintBottom_toBottomOf="@+id/iv_book"
                app:layout_constraintEnd_toEndOf="@+id/iv_book" />

            <com.dz.foundation.ui.widget.DzTextView
                android:id="@+id/tv_update_num"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/common_dp28"
                android:ellipsize="end"
                android:gravity="end|bottom"
                android:maxLines="1"
                android:paddingEnd="@dimen/common_dp6"
                android:paddingBottom="@dimen/common_dp6"
                android:textColor="@color/common_FFFFFFFF"
                android:textSize="@dimen/common_dp11"
                app:layout_constraintBottom_toBottomOf="@+id/iv_book"
                app:layout_constraintEnd_toEndOf="@+id/iv_book"
                tools:text="更新至100章" />

            <com.dz.foundation.ui.widget.DzImageView
                android:id="@+id/iv_fire"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/search_rank_pause"
                app:layout_constraintBottom_toBottomOf="@+id/tv_update_num"
                app:layout_constraintEnd_toStartOf="@+id/tv_update_num"
                app:layout_constraintTop_toTopOf="@+id/tv_update_num" />

            <com.dz.business.base.ui.component.PlayLetLabelComp
                android:id="@+id/label"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/common_dp16"
                android:layout_marginTop="@dimen/common_dp6"
                android:layout_marginEnd="@dimen/common_dp6"
                app:layout_constraintEnd_toEndOf="@+id/iv_book"
                app:layout_constraintTop_toTopOf="@+id/iv_book" />

            <com.dz.foundation.ui.widget.DzTextView
                android:id="@+id/tv_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/common_dp8"
                android:ellipsize="end"
                android:maxLines="1"
                android:textColor="@color/common_FF191919"
                android:textSize="@dimen/common_dp14"
                android:visibility="visible"
                app:layout_constraintStart_toStartOf="@+id/iv_book"
                app:layout_constraintTop_toBottomOf="@+id/iv_book"
                tools:text="最强战神最强战神最强战神" />

            <com.dz.foundation.ui.widget.DzTextView
                android:id="@+id/tv_category"
                android:layout_width="@dimen/common_dp0"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/common_dp4"
                android:ellipsize="end"
                android:includeFontPadding="false"
                android:maxLines="1"
                android:textColor="@color/common_959595"
                android:textSize="@dimen/common_dp12"
                app:layout_constraintEnd_toEndOf="@+id/iv_book"
                app:layout_constraintStart_toStartOf="@+id/iv_book"
                app:layout_constraintTop_toBottomOf="@+id/tv_name"
                tools:text="观看至10集" />

        </com.dz.foundation.ui.widget.DzConstraintLayout>
    </merge>
</layout>