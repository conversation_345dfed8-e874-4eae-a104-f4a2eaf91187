package com.dz.business.search.ui.component

import android.content.Context
import android.util.AttributeSet
import com.dz.business.base.search.data.BookSearchVo
import com.dz.business.search.R
import com.dz.business.search.databinding.SearchResultRecItemCompBinding
import com.dz.business.search.util.SearchUtil
import com.dz.foundation.base.utils.ScreenUtil
import com.dz.foundation.base.utils.dp
import com.dz.foundation.imageloader.load
import com.dz.foundation.imageloader.loadRoundImg
import com.dz.foundation.ui.view.custom.ActionListener
import com.dz.foundation.ui.view.custom.ActionListenerOwner
import com.dz.platform.common.base.ui.component.UIConstraintComponent

/**
 * @Author: guyh
 * @Date: 2023/4/28 15:44
 * @Description:
 * @Version:1.0
 */
class SearchResultRecItemComp @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int? = 0,
) : UIConstraintComponent<SearchResultRecItemCompBinding, BookSearchVo>(
    context,
    attrs,
    defStyleAttr!!
), ActionListenerOwner<SearchResultRecItemComp.ViewActionListener> {
    var titleTag = "猜你喜欢"
    var screenNameTag = "搜索页"

    interface ViewActionListener : ActionListener {
        fun onCoverClick(bookInfoVo: BookSearchVo?)
        fun onExpose(bookInfoVo: BookSearchVo?)
    }

    override fun initData() {
    }

    override fun initView() {
    }

    override fun initListener() {
        SearchUtil.ignoreAutoTrack(mViewBinding.ivCover)
        //交互：点击任意缩略图，跳转该剧二级播放。规则：1、如该剧无播放进度记录，播放该剧第一集；2、如有播放记录，从上次的播放进度继续播放
        mViewBinding.root.registerClickAction {
            mActionListener?.onCoverClick(mData)
            SearchUtil.trackViewAppClick(
                it,
                screenNameTag,
                titleTag,
                null,
                mData?.bookId,
                mData?.bookName
            )
        }
    }

    override fun onBindRecyclerViewItem(model: BookSearchVo?, position: Int) {
        super.onBindRecyclerViewItem(model, position)
        if (ScreenUtil.getScreenWidth() - 48.dp - 3 * 109.dp < 0) {
            var layoutParams = mViewBinding.root.layoutParams
            layoutParams.width = (ScreenUtil.getScreenWidth() - 48.dp) / 3
            mViewBinding.root.layoutParams = layoutParams
        }
        model?.run {
            //封面
            mViewBinding.ivCover.loadRoundImg(
                coverCutWap,
                5.dp,
                placeholder = R.drawable.bbase_ic_cover_default,
                error = R.drawable.bbase_ic_cover_default,
                width = 109, height = 156
            )
            //右下角角标 显示规则：更新进度及完结集数
            mViewBinding.tvUpdate.text = coverBottomTips
            //标题：剧名
            mViewBinding.tvName.text = bookName
            //标签：短剧标签，最多显示三个标签，标签简用空格隔开
            bookTags?.let {
                var marks = ""
                it.forEachIndexed { index, mark ->
                    if (index < 3) {
                        if (marks.isNotEmpty()) {
                            marks += " "
                        }
                        marks += mark
                    }
                }
                mViewBinding.tvMark.text = marks
            }
            //剧封面右上角标签
            if (kocFlag == true) {
                iconType = -1
            }
            mViewBinding.label.bindData(mData)
        }
    }

    override var mActionListener: ViewActionListener? = null

    override fun onExpose(isFirstExpose: Boolean) {
        mActionListener?.onExpose(mData)
    }
}