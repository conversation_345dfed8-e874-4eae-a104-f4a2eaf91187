<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/root"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.dz.foundation.ui.widget.DzView
            android:id="@+id/welfare_bg"
            android:layout_width="@dimen/common_dp355"
            android:layout_height="@dimen/common_dp73"
            android:layout_marginTop="@dimen/common_dp88"
            android:includeFontPadding="false"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:shape="rectangle"
            app:shape_radius="8dp"
            app:shape_solid_color="@color/common_white" />

        <ImageView
            android:id="@+id/iv_top"
            android:layout_width="@dimen/common_dp41"
            android:layout_height="@dimen/common_dp41"
            android:layout_marginStart="@dimen/common_dp12"
            android:src="@drawable/bcommon_ic_welfare_top"
            app:layout_constraintBottom_toBottomOf="@+id/welfare_bg"
            app:layout_constraintStart_toStartOf="@+id/welfare_bg"
            app:layout_constraintTop_toTopOf="@+id/welfare_bg" />

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/common_dp20"
            android:layout_marginStart="@dimen/common_dp8"
            android:ellipsize="end"
            android:lineHeight="@dimen/common_dp20"
            android:maxWidth="@dimen/common_dp175"
            android:maxLines="1"
            android:text="新人看剧送现金"
            android:textAlignment="center"
            android:textColor="@color/common_FF191919"
            android:textSize="@dimen/common_dp14"
            android:textStyle="bold"
            app:layout_constraintStart_toEndOf="@id/iv_top"
            app:layout_constraintTop_toTopOf="@+id/iv_top" />

        <TextView
            android:id="@+id/tv_info"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/common_dp20"
            android:layout_marginStart="@dimen/common_dp8"
            android:lineHeight="@dimen/common_dp20"
            android:textAlignment="center"
            android:textColor="@color/common_FF191919"
            android:textSize="@dimen/common_dp14"
            app:layout_constraintBottom_toBottomOf="@+id/iv_top"
            app:layout_constraintStart_toEndOf="@id/iv_top"
            tools:text="上次观看至第9集" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>