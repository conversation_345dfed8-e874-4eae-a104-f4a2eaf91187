package com.dz.business.bcommon

import com.dz.business.base.bcommon.BCommonMR
import com.dz.business.base.bcommon.BCommonMS
import com.dz.business.bcommon.ui.PushDialogComp
import com.dz.business.bcommon.ui.ShareDialogComp
import com.dz.business.bcommon.ui.WelfareCouponFloatComp
import com.dz.foundation.base.module.LibModule
import com.dz.foundation.base.service.DzServiceManager
import com.dz.foundation.router.registerTarget


class BCommonModule : LibModule() {

    override fun onCreate() {
        BCommonMR.get().apply {
            shareDialog().registerTarget(ShareDialogComp::class.java)
            pushDialog().registerTarget(PushDialogComp::class.java)
            welfareCouponComp().registerTarget(WelfareCouponFloatComp::class.java)
        }
        DzServiceManager.registerService(BCommonMS::class.java, BCommonMSImpl::class.java)
    }

}