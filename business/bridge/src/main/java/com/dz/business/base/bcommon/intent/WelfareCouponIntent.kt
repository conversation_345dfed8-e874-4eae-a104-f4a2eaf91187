package com.dz.business.base.bcommon.intent

import com.dz.business.base.data.bean.NewReadAwardNotice
import com.dz.business.base.ui.BaseDialogComp
import com.dz.platform.common.router.DialogRouteIntent

class WelfareCouponIntent : DialogRouteIntent() {
    var awardData: NewReadAwardNotice? = null

    private var onClose: ((dialogComp: BaseDialogComp<*, *>) -> Unit)? = null
    private var downBlock: ((dialogComp: BaseDialogComp<*, *>) -> Unit)? = null
    private var onSure: ((dialogComp: BaseDialogComp<*, *>) -> Unit)? = null

    // 向上方向的回调
    fun doClose(dialogComp: BaseDialogComp<*, *>) {
        onClose?.invoke(dialogComp)
    }

    // 向下方向的回调
    fun doDownBack(dialogComp: BaseDialogComp<*, *>) {
        downBlock?.invoke(dialogComp)
    }


    // 设置的回调
    fun doSure(dialogComp: BaseDialogComp<*, *>) {
        onSure?.invoke(dialogComp)
    }

    // 设置向上回调
    fun onClose(block: (dialogComp: BaseDialogComp<*, *>) -> Unit): WelfareCouponIntent {
        onClose = block
        return this
    }

    // 设置向下回调
    fun onDown(block: (dialogComp: BaseDialogComp<*, *>) -> Unit): WelfareCouponIntent {
        downBlock = block
        return this
    }


    // 设置的回调
    fun onSuret(block: (dialogComp: BaseDialogComp<*, *>) -> Unit): WelfareCouponIntent {
        onSure = block
        return this
    }
}
