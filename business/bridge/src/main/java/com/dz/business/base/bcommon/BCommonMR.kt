package com.dz.business.base.bcommon

import com.dz.business.base.bcommon.intent.*
import com.dz.foundation.router.IModuleRouter
import com.dz.foundation.router.annotation.RouteAction
import com.dz.foundation.router.get

interface BCommonMR : IModuleRouter {
    companion object {
        /**
         *运营弹窗
         */
        const val SHARE_DIALOG = "share_dialog"
        const val PUSH_DIALOG="push_dialog"
        //福利券弹窗
        const val WELFARE_COUPON_DIALOG = "welfare_coupon_dialog"

        private val instance = BCommonMR::class.java.get()
        fun get(): BCommonMR {
            return instance
        }
    }

    @RouteAction(SHARE_DIALOG)
    fun shareDialog(): ShareIntent

    @RouteAction(PUSH_DIALOG)
    fun pushDialog(): PushIntent
    /**
     * 福利券弹窗
     */
    @RouteAction(WELFARE_COUPON_DIALOG)
    fun welfareCouponComp(): WelfareCouponIntent
}