package com.dz.business.flutter

import BBaseME
import android.annotation.SuppressLint
import android.content.pm.PackageInfo
import android.content.pm.PackageManager
import androidx.annotation.Keep
import com.blankj.utilcode.util.GsonUtils
import com.dianzhong.common.util.JsonUtil
import com.dz.business.DzDataRepository
import com.dz.business.ReflectUtils
import com.dz.business.base.BBaseMC
import com.dz.business.base.BBaseMR
import com.dz.business.base.SpeedUtil
import com.dz.business.base.splash.SplashMS
import com.dz.business.base.bcommon.BCommonMS
import com.dz.business.base.bcommon.ShareListener
import com.dz.business.base.data.BBaseKV
import com.dz.business.base.data.bean.ChapterInfoVo
import com.dz.business.base.data.bean.ChapterInfoVoList
import com.dz.business.base.data.bean.ShareItemBean
import com.dz.business.base.data.bean.ShareResultBean
import com.dz.business.base.data.bean.VersionUpdateVo
import com.dz.business.base.data.bean.VideoDetailBean
import com.dz.business.base.data.bean.WxShareConfigVo
import com.dz.business.base.detail.DetailMR
import com.dz.business.base.download.DownloadMS
import com.dz.business.base.flutter.FlutterMS
import com.dz.business.base.home.HomeMC
import com.dz.business.base.home.HomeME
import com.dz.business.base.load.DBHelper
import com.dz.business.base.main.MainMR
import com.dz.business.base.main.intent.MainIntent
import com.dz.business.base.personal.PersonalMS
import com.dz.business.base.reader.ReaderMS
import com.dz.business.base.theatre.TheatreMC
import com.dz.business.base.track.TrackUtil
import com.dz.business.base.utils.ActionRecorder
import com.dz.business.base.utils.CommInfoUtil
import com.dz.business.base.utils.GsonUtil
import com.dz.business.base.video.VideoMS
import com.dz.business.flutter.network.FlutterNetworkUtil.doRequest
import com.dz.business.main.util.UpdateAppUtil
import com.dz.business.personal.util.DataCleanManagerUtils
import com.dz.business.reader.DataRepository
import com.dz.business.track.base.addParam
import com.dz.business.track.events.DzTrackEvents
import com.dz.business.track.events.hive.HiveExposureTE
import com.dz.business.track.events.hive.HivePVTE
import com.dz.business.track.events.sensor.ShareTE
import com.dz.business.track.monitor.MonitorMC
import com.dz.business.track.monitor.trackToSensor
import com.dz.business.track.trace.OmapNode
import com.dz.business.track.trace.QmapNode
import com.dz.business.track.tracker.Tracker
import com.dz.business.track.utis.AppPerformanceTrackUtil
import com.dz.flutter.plugin.plugin_call_native.plugin_call_native.IFlutterCallNativeCommon
import com.dz.foundation.base.manager.task.TaskManager
import com.dz.foundation.base.module.AppModule
import com.dz.foundation.base.utils.AudioUtil
import com.dz.foundation.base.utils.CrashUtils
import com.dz.foundation.base.utils.KVXCache
import com.dz.foundation.base.utils.LogUtil
import com.dz.foundation.base.utils.LogUtil.Companion.e
import com.dz.foundation.base.utils.monitor.TimeMonitorManager
import com.dz.foundation.imageloader.GlideUtils
import com.dz.foundation.network.METHOD_POST
import com.dz.platform.common.router.ACTIVITY
import com.dz.platform.common.router.SchemeRouter
import com.dz.platform.common.router.onDismiss
import com.dz.platform.common.router.onShow
import com.dz.platform.common.toast.ToastManager
import com.dz.support.mmkv.XCache
import com.therouter.getApplicationContext
import com.therouter.inject.ServiceProvider
import io.flutter.plugin.common.MethodChannel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.json.JSONException
import org.json.JSONObject
import java.lang.reflect.Method


/**
 * Des:和原生端交互接口
 *
 *
 * Time:2023/9/21
 * Author:Gavin
 */
@ServiceProvider
@Keep
class FlutterCallNativeServiceImpl : IFlutterCallNativeCommon {
    //    获取搜索列表
    fun getSearchListInfo(result: MethodChannel.Result?, params: Map<String, Any>?): String? {
        return JsonUtil.objectToJson(BBaseMC.searchTheatreHotWords)
    }

    //引擎attach时通知
    fun onAttachedToEngine(result: MethodChannel.Result?, params: Map<String, Any>?) {
        LogUtil.d("flutter", "onAttachedToEngine!")
    }

    //引擎detach时通知
    fun onDetachedFromEngine(result: MethodChannel.Result?, params: Map<String, Any>?) {
        LogUtil.d("flutter", "onDetachedFromEngine!")
    }

    /**
     * "params": {
    "text": "xxx",
    "gravity": "center",
    "msgType": "text",
    "duration": 2000,
    "multiline": true
    }
     *
     * @param result
     * @param params
     */
    fun toast(result: MethodChannel.Result?, params: Map<String, Any>?) {
        params?.apply {
            val text = this["text"]
            if (text !is String || text.isEmpty()) {
                result?.error("-1", "text is null", "")
                return
            }
            val duration = this["duration"]
            if (duration is Long && duration > 0) {
                ToastManager.showToast(text, duration)
            } else {
                ToastManager.showToast(text)
            }
            result?.success("success")
        } ?: let {
            result?.error("-1", "params is null", "")
        }
    }

    // flutter 短剧添加取消收藏
    fun inBookShelf(result: MethodChannel.Result?, params: Map<String, Any>?) {
        params?.get("bookId")?.let { bookId ->
            val addToShelf = params["value"]
            if (bookId is String && addToShelf is Boolean) {
                if (addToShelf) {
                    HomeME.get().addFavoriteSuccess().post(bookId)
                    ActionRecorder.recordAction(ActionRecorder.ACTION_FAVORITE)
                } else {
                    HomeME.get().deleteFavoriteSuccess().post(listOf(bookId))
                }
                HomeME.get().refreshFavorite().post(Any())
                TaskManager.ioTask {
                    DBHelper.insertOrUpdateHistory(bookId, addToShelf)
                }
                FlutterMS.get()?.sendEventToFlutter(
                    "inBookShelf", mapOf("value" to addToShelf, "bookId" to bookId)
                )
            }
            result?.success("success")
        } ?: let {
            result?.error("-1", "params is null", "")
        }
    }

    /**
     * 获取静音状态
     *   "params": { }
     *
     * @param result
     * @param params
     */
    fun getMuteStatus(result: MethodChannel.Result?, params: Map<String, Any>?) {
        result?.success(BBaseKV.isMute)
    }

    /**
     * 获取静音状态
    "params": {
    "mute": 0/1
    }
     *
     * @param result
     * @param params
     */
    fun setMuteStatus(result: MethodChannel.Result?, params: Map<String, Any>?) {
        val mute = params?.get("mute")
        if (mute is Int) {
            BBaseKV.isMute = mute == 1
            BBaseME.get().onMuteChanged().post(BBaseKV.isMute)
            result?.success("success")
        } else {
            result?.error("-1", "mute type error", mute)
        }
    }

    /**
     * 上报错误信息
     *   "params": {
     *      "exception": {} // json
     *   }
     *
     * @param result
     * @param params
     */
    fun exportException(result: MethodChannel.Result?, params: Map<String, Any>?) {
        params?.apply {
            val type = this["type"]
            val exception = this["exception"]
            if (type !is Int || exception == null) {
                result?.error("-1", "params error", "")
                return
            }
            DzTrackEvents.get().error().type(type).message(exception.toString()).track()
            result?.success("success")
        } ?: let {
            result?.error("-1", "params is null", "")
        }
    }

    /**
     * 上报神策埋点信息
     *   "params": {
     *      "eventName": "事件名称", // String
     *      "data": {} // json
     *   }
     *
     * @param result
     * @param params
     */
    fun trackToSensor(result: MethodChannel.Result?, params: Map<String, Any>?) {
        params?.apply {
            val eventName = params["eventName"]
            if (eventName !is String || eventName.isEmpty()) {
                result?.error("-1", "eventName is null", "")
                return
            }
            val data = params["data"]
            try {
                if (data is String && data.isNotEmpty()) {
//                    Tracker.trackToSensor(eventName, JSONObject(data))
                    val dataObject = JSONObject(data)
                    if (eventName == "\$AppViewScreen") {
                        val title = dataObject.getString("\$title")
                        LogUtil.d(TrackUtil.TAG, "Flutter track AppViewScreen: $title")
                        if (title.isNotEmpty()) {
                            TrackUtil.updateCurrentPage(title)
                        }
                        dataObject.put("\$referrer_title", TrackUtil.previousPage)
                        // 大数据打点
                        DzTrackEvents.get().hivePv()
                            .pType("page_view")
                            .withOmapSource(OmapNode().apply {
                                rgts = BBaseKV.regTime
                                nowChTime = BBaseKV.chTime
                                is_login = if (CommInfoUtil.hasLogin()) 1 else 0
                                pageName = title
                            })
                            .withQmapSource(QmapNode().apply {
                                eventType = "page_view"
                            })
                            .track()
                    }
                    Tracker.trackToSensor(eventName, dataObject)
                    result?.success("success")
                } else {
                    result?.error("-1", "data is null", "")
                }
            } catch (e: JSONException) {
                e.printStackTrace()
                result?.error("-1", e.message, "")
            }
        } ?: let {
            result?.error("-1", "params is null", "")
        }
    }

    /**
     * 看剧喜好设置后同步
     */
    fun userPreferencesUpdateNotice(result: MethodChannel.Result?, params: Map<String, Any>?) {
        BBaseME.get().userLikeUpdateNotice().post(Any())
        result?.success("success")
    }

    /**
     * 上报Hive埋点信息
     *   "params": {
     *      "tag": {} // json
     *   }
     *
     * @param result
     * @param params
     */
    fun trackToHive(result: MethodChannel.Result?, params: Map<String, Any>?) {
        params?.apply {
            var omap: OmapNode? = null
            this["omap"]?.let {
                if (it is String) {
                    omap = JsonUtil.fromJson(it, OmapNode::class.java)
                }
            }
            LogUtil.d("trackToHive", "强插：omap:${omap?.fromType}")
            when (this["type"]) {
                "pv" -> {
                    val event = HivePVTE()
                    var qmap: QmapNode? = null
                    this["qmap"]?.let {
                        if (it is String) {
                            qmap = JsonUtil.fromJson(it, QmapNode::class.java)
                        }
                    }
                    var pType: String = ""
                    this["pType"]?.let {
                        if (it is String) {
                            pType = it
                        }
                    }
                    event.pType(pType).withOmapSource(omap).withQmapSource(qmap).track()
                    result?.success("success")
                }

                "exposure" -> {
                    val event = HiveExposureTE()
                    event.show(omap).track()
                    result?.success("success")
                }

                else -> {
                    result?.error("-1", "type:${this["type"]} not found", "")
                }
            }
        } ?: let {
            result?.error("-1", "params is null", "")
        }
    }

    //发送 flutter 网络请求
    fun postJson(result: MethodChannel.Result?, params: Map<String, Any>?) {
        try {
            CoroutineScope(Dispatchers.IO).launch {
                val apiCode=params?.get("apiCode").toString()
                val response = doRequest(
                    apiCode,
                    params?.get("apiParam") as Map<String, String>?,
                    METHOD_POST
                )

                //以接口请求结束作为Flutter剧场页内容可见的时间
                if (apiCode == "1125") {
                    SplashMS.get()?.updateColdLaunchPageContentDisplayTime(true)
                    AppPerformanceTrackUtil.trackAppColdLaunch()
                }

                result?.success(response)
            }
        } catch (e: Exception) {
            result?.error("ERROR", e.message, null)
        }
    }

    fun getHistoryFromBook(result: MethodChannel.Result?, params: Map<String, Any>?) {
        try {
            CoroutineScope(Dispatchers.IO).launch {
                val nextPageFlag = params?.get("nextPageFlag") ?: ""
                val pageSize = params?.get("pageSize") ?: 50
                // 默认需求，不分页。多余50条，删除多出来的。needMore=false时，做多余的删除操作。
                val needMore = params?.get("needMore") ?: false

                val response = ReaderMS.get()?.queryBookHistory(
                    nextPageFlag.toString(), pageSize as Int, needMore as Boolean
                )
                LogUtil.d("flutter", "getHistoryFromBook response:$response")
                result?.success(response)
            }
        } catch (e: Exception) {
            result?.error("ERROR", e.message, null)
        }
    }

    fun getHistoryForTheatre(result: MethodChannel.Result?, params: Map<String, Any>?) {
        try {
            CoroutineScope(Dispatchers.IO).launch {
                val nextPageFlag = params?.get("nextPageFlag") ?: ""
                val pageSize = params?.get("pageSize") ?: 50
                // 默认需求，不分页。多余50条，删除多出来的。needMore=false时，做多余的删除操作。
                val needMore = params?.get("needMore") ?: false
                val bookIds: List<String>? = params?.get("bookIds") as List<String>?

                val response = PersonalMS.get()?.queryTheatreHistory(
                    bookIds, nextPageFlag.toString(), pageSize as Int, needMore as Boolean
                )
                LogUtil.d("flutter", "getHistoryForTheatre response:$response")
                result?.success(response)
            }
        } catch (e: Exception) {
            result?.error("ERROR", e.message, null)
        }
    }

    fun getLikeList(result: MethodChannel.Result?, params: Map<String, Any>?) {
        try {
            CoroutineScope(Dispatchers.IO).launch {
                val response = PersonalMS.get()?.queryAllTheatreLikes()
                LogUtil.d("flutter", "getLikeList response:$response")
                result?.success(response)
            }
        } catch (e: Exception) {
            result?.error("ERROR", e.message, null)
        }
    }

    fun cleanLikeList(result: MethodChannel.Result?, params: Map<String, Any>?) {
        try {
            CoroutineScope(Dispatchers.IO).launch {
                val response = PersonalMS.get()?.deleteAllTheatreLikes()
                LogUtil.d("flutter", "cleanLikeList response:$response")
                result?.success(response)
            }
        } catch (e: Exception) {
            result?.error("ERROR", e.message, null)
        }
    }

    fun getBookInfoForTheatre(result: MethodChannel.Result?, params: Map<String, Any>?) {
        try {
            CoroutineScope(Dispatchers.IO).launch {
                val bookId = params?.get("bookId") ?: ""
                val bookEntity = DzDataRepository.bookDao().queryByBid(bookId.toString())
                if (bookEntity == null) {
                    result?.success("")
                } else {
                    val response = GsonUtils.toJson(bookEntity)
                    LogUtil.d("flutter", "getBookInfoForTheatre response:$response")
                    result?.success(response)
                }
            }
        } catch (e: Exception) {
            result?.error("ERROR", e.message, null)
        }
    }

    /**
     * 获取短剧当前播放进度的章节id
     */
    fun getTheaterChapterId(result: MethodChannel.Result?, params: Map<String, Any>?) {
        try {
            CoroutineScope(Dispatchers.IO).launch {
                val bookId = params?.get("bookId") ?: ""
                val bookEntity = DBHelper.queryBook(bookId.toString())
                if (bookEntity == null) {
                    result?.success("")
                } else {
                    bookEntity.cur_cid?.let { chapterId ->
                        val map = mutableMapOf<String, String>()
                        map["chapterId"] = chapterId
                        LogUtil.d("flutter", "getTheaterChapterId response:$map")
                        result?.success(map)
                    } ?: let {
                        result?.success("")
                    }
                }
            }
        } catch (e: Exception) {
            result?.error("ERROR", e.message, null)
        }
    }

    /**
     * 内容推荐开关是否开启 true：开启，false：关闭
     */
    fun isOpenRecommendContent(): Boolean {
        return BBaseKV.recommendContent
    }

    private val methodCache: MutableMap<String, Method> = HashMap()


    override fun callNativeMethod(
        methodName: String?, result: MethodChannel.Result?, params: Map<String, Any>?
    ): Any? {
        if (methodName == null) return "Error is null!!!"
        try {
            LogUtil.d("flutter", "callNativeMethod methodName:$methodName")

            // 先从缓存中查找方法
            val method: Method?
            if (methodCache.contains(methodName)) {
                method = methodCache[methodName]
                LogUtil.d("flutter", "callNativeMethod methodCache.contains($methodName)")

            } else {
                // 如果缓存中没有对应的方法，则进行反射获取并存入缓存
                method = ReflectUtils.reflect(this).methodGet(methodName, result, params)
                methodCache[methodName] = method
            }
            if (method == null) {
                return "method $methodName not implementation!!!"
            }

            // 调用方法
            val res = method.invoke(this, result, params)

            return if (res === this) {
//                无返回
                null
            } else {
                res
            }
        } catch (e: Exception) {
            e("flutter", "methodName:$methodName Exception: $e")
        }
        return null
    }

    fun getUserId(result: MethodChannel.Result?, params: Map<String, Any>?): String {
        return BBaseKV.userId
    }

    /**
     *  异步获取本地信息，native实现。主要来源于sp数据，文件数据。特点是快。
     *  params: 的key列表，对应要获取信息的集合。
     */
    fun getLocalInfo(result: MethodChannel.Result?, params: Map<String, Any>?) {
        try {
            CoroutineScope(Dispatchers.IO).launch {
                val map = mutableMapOf<String, Any>()
                params?.keys?.forEach { key ->
                    when (key) {
                        // 用户信息 来自1104->UserInfoVo
                        "userId" -> BBaseKV.userId
                        "userName" -> BBaseKV.name
                        "userAvatar" -> BBaseKV.avatar
                        "userType" -> BBaseKV.userType
                        "vipStatus" -> BBaseKV.vipStatus
                        "vipEndTime" -> BBaseKV.vipEndTime
                        "totalAmount" -> BBaseKV.totalAmount

                        // 通用配置，来自1104->ConfigVo
                        "recommendAd" -> BBaseKV.recommendAd
                        "recommendAdStatus" -> BBaseKV.recommendAdStatus
                        "recommendContent" -> BBaseKV.recommendContent
                        "recommendContentStatus" -> BBaseKV.recommendContentStatus
                        "autoPay" -> BBaseKV.autoPay
                        "autoPayRevised" -> BBaseKV.autoPayRevised
                        "autoPayChecked" -> BBaseKV.autoPayChecked
                        "autoPayCheckedRevised" -> BBaseKV.autoPayCheckedRevised

//                        "beContacted" -> BBaseKV.beContacted
                        "customerUrl" -> BBaseKV.customerUrl
                        "serviceTel" -> BBaseKV.tel
                        "companyName" -> BBaseKV.company
                        "year" -> BBaseKV.year
                        "subject" -> BBaseKV.subject


                        "questionUrl" -> BBaseMC.questionnaireSurveyUrl
                        "questionnaireSurveySwitch" -> BBaseMC.questionnaireSurveySwitch

                        "cacheSize" -> DataCleanManagerUtils.getTotalCacheSize(getApplicationContext())
                        "hasLogin" -> CommInfoUtil.hasLogin()
                        "appName" -> CommInfoUtil.getAppName()
                        "versionName" -> AppModule.getAppVersionName()

                        "searchTheatreHotWords" -> BBaseMC.searchTheatreHotWords
                        "searchShowTheatre" -> BBaseKV.searchShowTheatre

                        "userPersonalityContent" -> BBaseKV.recommendContent

                        "splashCold" -> BBaseKV.splashCold
                        //最近观看剧 是否发生有效观看
                        "watchedDrama" -> BBaseKV.watchedDrama

                        "clickCardTime" -> BBaseKV.clickTheaterCardTime

                        "multipleSpeed" -> (BBaseKV.multipleSpeed).toDouble()
                        "theaterChapterDownloadGuide" -> KVXCache.getValue(key, false)
                        "introductionStyle" -> VideoMS.get()?.getIntroductionStyle() ?: 0
                        else -> null
                    }?.let { result -> map[key] = result }
                }
                result?.success(map)
            }
        } catch (e: Exception) {
            result?.error("ERROR", e.message, null)
        }
    }

    /**
     * 异步设置本地信息。与上面方法同步。
     * params: 对应要设置信息的键值对。
     */
    fun setLocalInfo(result: MethodChannel.Result?, params: Map<String, Any>?) {
        try {
            CoroutineScope(Dispatchers.IO).launch {
                params?.forEach { kv ->
                    when (kv.key) {
                        // 设置登录token，为空字符串时，清理token退出登录
                        "userToken" -> BBaseKV.token = kv.value as String
                        // 用户信息 来自1104->UserInfoVo
                        "userId" -> {
                            BBaseKV.userId = kv.value as String
                            XCache.userId = BBaseKV.userId
                        }

                        "userName" -> BBaseKV.name = kv.value as String
                        "userAvatar" -> BBaseKV.avatar = kv.value as String
                        "userType" -> BBaseKV.userType = kv.value as Int
                        "vipStatus" -> BBaseKV.vipStatus = kv.value as Int
                        "vipEndTime" -> BBaseKV.vipEndTime = kv.value as String
                        "totalAmount" -> BBaseKV.totalAmount = (kv.value as Int).toLong()
                        // 通用配置，来自1104->ConfigVo
                        "recommendAd" -> BBaseKV.recommendAd = kv.value as Boolean
                        "recommendAdStatus" -> BBaseKV.recommendAdStatus = kv.value as Int
                        "recommendContent" -> BBaseKV.recommendContent = kv.value as Boolean
                        "recommendContentStatus" -> BBaseKV.recommendContentStatus = kv.value as Int
                        "autoPay" -> BBaseKV.autoPay = kv.value as Int
                        "autoPayRevised" -> BBaseKV.autoPayRevised = kv.value as Boolean
                        "autoPayChecked" -> BBaseKV.autoPayChecked = kv.value as Int
                        "autoPayCheckedRevised" -> BBaseKV.autoPayCheckedRevised =
                            kv.value as Boolean

//                        "beContacted" -> BBaseKV.beContacted = kv.value as Int
                        "customerUrl" -> BBaseKV.customerUrl = kv.value as String
                        "serviceTel" -> BBaseKV.tel = kv.value as String
                        "companyName" -> BBaseKV.company = kv.value as String
                        "year" -> BBaseKV.year = kv.value as String
                        "subject" -> BBaseKV.subject = kv.value as String

                        "splashCold" -> BBaseKV.splashCold = kv.value as Boolean
                        //最近观看剧卡片 是否发生有效观看
                        "watchedDrama" -> BBaseKV.watchedDrama = kv.value as Boolean

                        "clickCardTime" -> BBaseKV.clickTheaterCardTime = dartIntToLong(kv.value)

                        "multipleSpeed" -> {
                            BBaseKV.multipleSpeed = (kv.value as Double).toFloat()
                            BBaseME.get().updateMultipleSpeed().post(BBaseKV.multipleSpeed)
                        }

                        "theaterChapterDownloadGuide" -> KVXCache.setValue(kv.key, kv.value)
                    }
                }
                result?.success("success")
            }
        } catch (e: Exception) {
            result?.error("ERROR", e.message, null)
        }
    }

    /**
     * 路由跳转
     */
    fun doRouter(result: MethodChannel.Result?, params: Map<String, Any>?) {
        try {
            params?.forEach { kv ->
                when (kv.key) {
                    "schema" -> {
                        if (kv.value is String) {
                            val url = kv.value as String
                            SchemeRouter.doUriJump(url)
                        }
                    }
                }
            }
            result?.success("success")
        } catch (e: Exception) {
            result?.error("ERROR", e.message, null)
        }
    }

    /**
     * Flutter 调用弹窗
     */
    fun showCustomDialog(result: MethodChannel.Result?, params: Map<String, Any>?) {
        try {
            CoroutineScope(Dispatchers.IO).launch {
                val list = params as Map<*, *>
                val type: String = (list["type"] ?: "").toString()
                if (type == "score") {//评分弹窗
                    val intentBookId = (list["bookId"] ?: "").toString()
                    val intentScore = (list["score"] ?: 0) as Int
                    var intentMyScoreLimit = 0
                    if (intentScore <= 0) {
                        intentMyScoreLimit =
                            (DBHelper.queryVideoWatchTime(intentBookId) / 60.0).toInt()
                    }
                    DetailMR.get().showVideoScoreDialog().apply {
                        bookId = intentBookId
                        bookScore = (list["bookScore"] ?: "").toString()
                        bookScoreExtend = (list["bookScoreExtend"] ?: "").toString()
                        score = intentScore
                        unlockScoreLimit = (list["unlockScoreLimit"] ?: 0) as Int
                        commentRules = (list["commentRules"] ?: "").toString()
                        bookScoreStare = (list["bookScoreStare"] ?: 0.0) as Double
                        scoreCount = (list["scoreCount"] ?: "").toString()
                        myScoreLimit = intentMyScoreLimit
                    }.overridePendingTransition(
                        R.anim.common_ac_none, R.anim.common_ac_none
                    ).start()
                } else {//通用弹窗
                    BBaseMR.get().flutterDialog().apply {
                        title = (list["title"] ?: "").toString()
                        content = (list["content"] ?: "").toString()
                        cancelText = (list["cancelText"] ?: "").toString()
                        sureText = (list["sureText"] ?: "").toString()
                        sureActionUrl = (list["sureActionUrl"] ?: "").toString()
                        showClose = (list["sureActionUrl"] ?: true) == true
                        barrierDismissible = (list["barrierDismissible"] ?: true) == true
                        mode = ACTIVITY
                        showNavigationBar = true
                    }.onShow {

                    }.onSure { url, _ ->
                        SchemeRouter.doUriJump(url ?: "")
                    }.onDismiss {

                    }.start()
                }

                result?.success("success")
            }
        } catch (e: Exception) {
            result?.error("ERROR", e.message, null)
        }
    }

    /**
     * 异步设置本地信息。与上面方法同步。
     * params: 对应要设置信息的键值对。
     */
    fun doAction(result: MethodChannel.Result?, params: Map<String, Any>?) {
        try {
            CoroutineScope(Dispatchers.IO).launch {
                params?.forEach { kv ->
                    try {
                        when (kv.key) {
                            // 设置中清理缓存，不影响用户数据
                            "clearAllCache" -> DataCleanManagerUtils.clearAllCache(
                                getApplicationContext()
                            )

                            // 清理数据库
                            "clearData" -> DzDataRepository.clearData()

                            // 下载apk并发起安装
                            "downloadAndInstallApp" -> {
                                val map = kv.value as Map<*, *>
                                val updateVo = VersionUpdateVo(
                                    map["updateType"] as Int?,
                                    map["versionAfter"] as String?,
                                    map["introductionList"] as List<String>?,
                                    map["downloadUrl"] as String?
                                )
                                UpdateAppUtil.downloadAndInstallApp(
                                    AppModule.getApplication(), updateVo
                                )
                            }

                            // 删除短剧浏览记录
                            "deleteHistoryForTheatre" -> {
                                if (kv.value is List<*>) {
                                    val list: List<String> = kv.value as List<String>
                                    DzDataRepository.historyDao().deleteHistoryBooks(list)
                                }
                            }

                            // 删除书籍浏览记录
                            "deleteHistoryForBook" -> {
                                if (kv.value is List<*>) {
                                    val list: List<String> = kv.value as List<String>
                                    DataRepository.bookDao().deleteByBid(*list.toTypedArray())
                                }
                            }

                            // 设置音频焦点
                            "setAudioFocus" -> {
                                if (kv.value is Boolean) {
                                    val focus = kv.value as Boolean
                                    if (focus) {
                                        AudioUtil.requestAudioFocus()
                                    } else {
                                        AudioUtil.abandonAudioFocus()
                                    }
                                }
                            }

                            // 分享到微信
                            "doShare" -> {
                                if (kv.value is Map<*, *>) {
                                    val map = kv.value as Map<*, *>
                                    map["type"]?.let { type ->
                                        if (map["params"] is Map<*, *>) {
                                            val list = map["params"] as Map<*, *>
                                            shareToWx(result, list, type.toString())
                                        }

                                    }
                                }
                            }

                            // 弹窗
                            "showDialog" -> {
                                if (kv.value is Map<*, *>) {
                                    val map = kv.value as Map<*, *>
                                    map["type"]?.let { type ->
                                        if (type == "acceptInviteReward") {
                                            if (map["params"] is Map<*, *>) {
                                                val list = map["params"] as Map<*, *>
                                                if (list["status"] == 1) {
                                                    CommInfoUtil.onShowInvite(list["inviteResultImg"].toString(),//图片地址
                                                        list["inviteResult"].toString(),
                                                        false,
                                                        { //点击图片跳转
                                                            MainMR.get().main().apply {
                                                                selectedTab = MainIntent.TAB_HOME
                                                                homeTabPage = HomeMC.TAB_RECOMMEND
                                                            }.start()
                                                        },
                                                        { intent ->  //返回弹窗路由
                                                            intent?.let {
                                                                intent.start()
                                                            } ?: let {
                                                                ToastManager.showToast(list["inviteResult"].toString())
                                                            }
                                                        },
                                                        {
                                                            DzTrackEvents.get().popupShow()
                                                                .popupName("活动页邀请成功通知")
                                                                .addParam(
                                                                    "PositionName", "邀请活动页"
                                                                ).track()
                                                        })
                                                } else {
                                                    ToastManager.showToast(list["inviteResult"].toString())
                                                }

                                            }
                                        }
                                    }
                                }
                            }
                        }
                    } catch (e: Exception) {
                        e.printStackTrace()
                        result?.error("ERROR", e.message, null)
                        return@launch
                    }
                }
                result?.success("success")
            }
        } catch (e: Exception) {
            result?.error("ERROR", e.message, null)
        }
    }


    /**
     * dart int 和 long 的转换
     */
    private fun dartIntToLong(value: Any): Long {
        return when (value) {
            is Int -> {
                value.toLong()
            }

            is Long -> {
                value
            }

            else -> {
                -1L
            }
        }
    }

    /**
     * 使用原声缓存图片，并获取地址
     */
    fun getCacheImagePath(result: MethodChannel.Result?, params: Map<String, Any>?) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val url = params?.get("url")
                if (url != null && url is String) {
                    GlideUtils.getImageFilePath(getApplicationContext(), url)
                        ?.let { result?.success(it) }
                }
            } catch (e: Exception) {
                result?.error("ERROR", e.message, null)
            }
        }
    }

    /**
     * 添加下载队列
     */
    fun addDownloadQueue(result: MethodChannel.Result?, params: Map<String, Any>?) {
        CoroutineScope(Dispatchers.IO).launch {
            kotlin.runCatching {
                var videoDetail: VideoDetailBean? = null
                (params?.get("book") as? Map<*, *>)?.let { book ->
                    videoDetail = GsonUtil.fromJson(
                        GsonUtil.toJson(book), VideoDetailBean::class.java
                    )
                }
                val chapterList = mutableListOf<ChapterInfoVo>() //更新剧集
                val needDownloadList = mutableListOf<String>() //下载剧集
                (params?.get("chapterList") as? List<*>)?.let { chapters ->
                    chapters.forEach {
                        GsonUtil.fromJson(GsonUtil.toJson(it), ChapterInfoVo::class.java)
                            ?.let { chapter ->
                                if (!chapter.chapterId.isNullOrEmpty() && chapter.downloadState == BBaseMC.DOWNLOAD_STATE_WAITING) {
                                    needDownloadList.add(chapter.chapterId!!)
                                }
                                chapterList.add(chapter)
                            }
                    }
                }
                if (videoDetail != null && needDownloadList.isNotEmpty()) {
                    DownloadMS.get()?.downloadChapters(videoDetail!!, chapterList)
                } else {
                    LogUtil.d(
                        "downloader",
                        "Flutter下载参数异常 videoDetail:$videoDetail chapterList:${needDownloadList.toString()}"
                    )
                }
                result?.success(true)
            }.onFailure {
                result?.error("ERROR", it.message, null)
            }
        }
    }

    /**
     * 取消部分剧集下载
     */
    fun removeDownloadQueue(result: MethodChannel.Result?, params: Map<String, Any>?) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val bookId = params?.get("bookId").toString()
                if (params?.get("chapterIdList") is List<*>) {
                    val chapters = mutableListOf<String>()
                    for (item in (params["chapterIdList"] as List<*>)) {
                        chapters.add(item.toString())
                    }
                    if (chapters.isNotEmpty()) {
                        DownloadMS.get()?.deleteTasks(bookId, chapters, false)
                    }
                }
                result?.success(true)
            } catch (e: Exception) {
                result?.error("ERROR", e.message, null)
            }
        }
    }

    /**
     * 获取APP剩余可存储空间
     */
    @SuppressLint("UsableSpace")
    fun getAvailableDiskSize(result: MethodChannel.Result?, params: Map<String, Any>?) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val space = DownloadMS.get()?.getDownloadUsableSpace() ?: 0
                LogUtil.d("downloader", "Flutter 获取剩余可用空间:$space")
                val map = mapOf<String, Any>("byte" to space)
                result?.success(map)
            } catch (e: Exception) {
                result?.error("ERROR", e.message, null)
            }
        }
    }

    /**
     * 获取短剧本地章节
     */
    fun getTheaterChapters(result: MethodChannel.Result?, params: Map<String, Any>?) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val bookId:String = params?.get("bookId").toString()
                DownloadMS.get()?.getCacheChapters(bookId)?.let { chapters ->
//                    LogUtil.d("downloader", "本地缓存章节:$chapters")
                    if (chapters.isNotEmpty()) {
                        val response = GsonUtils.toJson(ChapterInfoVoList(chapterList = chapters))
                        result?.success(response)
                    } else {
                        result?.error("ERROR", "本地缓存为空", null)
                    }
                }?: run{
                    result?.error("ERROR", "本地缓存为空", null)
                }
            } catch (e: Exception) {
                result?.error("ERROR", e.message, null)
            }
        }
    }

    /**
     * 邀请活动分享
     * @param params  clientShareType 默认 image
     * @return
     */
    private fun shareToWx(result: MethodChannel.Result?, params: Map<*, *>?, shareType: String) {
        //测试复现用，提审前要关闭
//        ToastManager.showToast("点击了分享微信")
        if (!isWxInstalled()) {
            ToastManager.showToast("您暂未安装微信")
            return
        }
        val title = if ((params?.get("text") ?: "") is String) {
            (params?.get("text") ?: "").toString()
        } else {
            ""
        }

        val type: Int = if (shareType == "imageToWeChat") {
            1
        } else {
            2
        }

        val des = if ((params?.get("remark") ?: "") is String) {
            (params?.get("remark") ?: "").toString()
        } else {
            ""
        }

        val img = if ((params?.get("imageUri") ?: "") is String) {
            (params?.get("imageUri") ?: "").toString()
        } else {
            ""
        }

        val coverUrl = if ((params?.get("imageUri") ?: "") is String) {
            (params?.get("imageUri") ?: "").toString()
        } else {
            ""
        }

        val shareUrl = if ((params?.get("shareUrl") ?: "") is String) {
            (params?.get("shareUrl") ?: "").toString()
        } else {
            ""
        }

        val clientShareType = if ((params?.get("clientShareType") ?: "image") is String) {
            (params?.get("clientShareType") ?: "image").toString()
        } else {
            "image"
        }

        val wxShareConfigVo = WxShareConfigVo(
            wxShareAppId = BBaseKV.wxShareAppId, shareVoList = listOf(
                ShareItemBean(
                    shareType = type, clientShareType = clientShareType, coverUrl = coverUrl
                )
            )
        )
        wxShareConfigVo.from = WxShareConfigVo.FROM_WELFARE_TASK
        wxShareConfigVo.shareVoList?.forEachIndexed { _, shareItemBean ->
            shareItemBean?.needToastResult = false
            shareItemBean?.dismissShareDialogOnFail = true
            shareItemBean?.bookId = ""
            shareItemBean?.bookName = ""
            shareItemBean?.shareUrl = shareUrl
            shareItemBean?.img = img
            shareItemBean?.title = title
            shareItemBean?.content = des
            shareItemBean?.doNotifyRequest = false
            shareItemBean?.chapterId = ""
        }
        DzTrackEvents.get().shareTE().type().channelCode(CommInfoUtil.getAppChannel())
            .site(ShareTE.SITE_SHARE_BTN).track()
        TaskManager.mainTask {
            BCommonMS.get()?.share(wxShareConfigVo, object : ShareListener {
                override fun onShareStart(shareItemBean: ShareItemBean) {

                }

                override fun onFail(
                    shareItemBean: ShareItemBean,
                    errorMessage: String?,
                    shareResultBean: ShareResultBean?,
                ) {
                    TaskManager.delayTask(500) {
                        ToastManager.showToast("分享失败，请重试")
                    }
                }

                override fun onSuccess(
                    shareItemBean: ShareItemBean,
                    shareResultBean: ShareResultBean,
                ) {
                    TaskManager.delayTask(500) {
                        ToastManager.showToast("分享成功")
                    }
                }

                override fun onCancel(
                    shareItemBean: WxShareConfigVo,
                    clickCancelBtn: Boolean,
                ) {
                    if (clickCancelBtn) {
                        TaskManager.delayTask(500) {
                            ToastManager.showToast("取消分享")
                        }
                    }
                }

                override fun prohibitShare(shareItemBean: ShareItemBean) {
                    TaskManager.delayTask(500) {
                        ToastManager.showToast("分享频繁，请稍后重试")
                    }
                }
            }, bigImg = true)
        }
    }

    private fun isWxInstalled(): Boolean {
        val pm = AppModule.getApplication().packageManager
        var packageInfo: PackageInfo? = null
        try {
            packageInfo = pm.getPackageInfo("com.tencent.mm", PackageManager.GET_SIGNATURES)
            if (packageInfo != null) {
                return true
            }
        } catch (e: PackageManager.NameNotFoundException) {
            e.printStackTrace()
        }
        return false
    }

    private fun reportError(result: MethodChannel.Result?, params: Map<String, Any>?) {
        params?.let {
//            it 转 map
            val map = mutableMapOf<String, String>()
            it.forEach { (key, value) ->
                map[key] = value.toString()
            }
            CrashUtils.reportError("flutterError", null, map)
        }
    }

    private fun pageOpenDuration(result: MethodChannel.Result?, params: Map<String, Any>?) {
        params?.let {
            (params["pageName"] as? String)?.let { pageName ->
                TimeMonitorManager.getMonitor(pageName).apply {
                    (params["networkStart"] as? Long)?.let {
                        recordTime(
                            MonitorMC.STAGE_NET_START,
                            it
                        )
                    }
                    (params["networkEnd"] as? Long)?.let { recordTime(MonitorMC.STAGE_NET_END, it) }
                    (params["renderEnd"] as? Long)?.let { recordTime(MonitorMC.STAGE_END, it) }
                    recordTime(MonitorMC.STAGE_END)
                    trackToSensor()
                    end()
                }
            }

        }
    }

    /**
     * 获取剧场ocpc剧id
     * @param result Result?
     * @param params Map<String, Any>?
     */
    private fun getTheatreOcpcBookId(result: MethodChannel.Result?, params: Map<String, Any>?) {
        result?.success(TheatreMC.ocpcBookId)
        TheatreMC.ocpcBookId = null
    }
}