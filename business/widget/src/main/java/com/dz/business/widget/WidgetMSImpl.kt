package com.dz.business.widget

import BBaseME
import android.appwidget.AppWidgetManager
import android.content.ComponentName
import android.content.Intent
import android.os.Build
import com.dz.business.base.data.BBaseKV
import com.dz.business.base.data.bean.BaseBookInfo
import com.dz.business.base.widget.WidgetMC
import com.dz.business.base.widget.WidgetME
import com.dz.business.base.widget.WidgetMS
import com.dz.business.base.widget.WidgetStateVo
import com.dz.business.bcommon.appWidget.AppWidgetHistory
import com.dz.business.bcommon.appWidget.AppWidgetHot
import com.dz.business.bcommon.appWidget.AppWidgetIncome
import com.dz.business.bcommon.appWidget.AppWidgetRanking
import com.dz.business.bcommon.appWidget.AppWidgetShortcut
import com.dz.business.bcommon.appWidget.AppWidgetUpdateReceiver
import com.dz.business.bcommon.appWidget.AppWidgetWatching
import com.dz.business.bcommon.appWidget.VideoWidgetProvider
import com.dz.business.bcommon.appWidget.WelfareWidgetProvider
import com.dz.business.widget.data.Constant
import com.dz.business.widget.db.WidgetDatabase
import com.dz.business.widget.db.entity.WidgetState
import com.dz.business.widget.utils.AppWidgetUtils
import com.dz.foundation.base.module.AppModule
import com.dz.foundation.base.utils.LogUtil

/**
 *@Author: zhanggy
 *@Date: 2024-08-28
 *@Description:
 *@Version:1.0
 */
class WidgetMSImpl : WidgetMS {

    private val supportWidgetClass = listOf(
        AppWidgetHistory::class.java,
        AppWidgetHot::class.java,
        AppWidgetRanking::class.java,
        AppWidgetShortcut::class.java,
        AppWidgetWatching::class.java,
        VideoWidgetProvider::class.java,
        WelfareWidgetProvider::class.java,  // 福利中心组件
        AppWidgetIncome::class.java,
    )

    /**
     * 调用一键添加组件后，需要检测组件是否添加成功
     * 这个集合记录了待检测的组件
     * 在合适时机，一般是Activity onResume，重新进行检测
     */
    private val waitingConfirmWidget = mutableSetOf<Class<*>>()

    override fun pinWidget(serverType: Int?): Boolean {
        val isSupport = serverType?.let { type ->
            AppWidgetUtils.getWidgetClassByServerType(type)?.let { clazz ->
                val successAction = Constant.ACTION_PIN_WIDGET_SUCCESS
                val localType = AppWidgetUtils.convertTypeFromServer(type)
                waitingConfirmWidget.add(clazz)
                AppWidgetUtils.createLauncherWidget(clazz, successAction, localType)
            } ?: false
        } ?: false
        if (!isSupport) {
            LogUtil.d(TAG, "组件添加失败")
            BBaseME.get().onAppWidgetCreateResult().post(false)
        }
        return isSupport
    }

    override fun getWidgetPreview(serverType: Int?): Int? {
        return serverType?.let { type ->
            when (type) {
                WidgetMC.WIDGET_SERVER_TYPE_WATCHING -> R.drawable.img_watching_preview
                WidgetMC.WIDGET_SERVER_TYPE_HOT -> R.drawable.icon_hot_preview
                WidgetMC.WIDGET_SERVER_TYPE_RANKING -> R.drawable.img_ranking_preview
                WidgetMC.WIDGET_SERVER_TYPE_HISTORY -> R.drawable.img_history_preview
                WidgetMC.WIDGET_SERVER_TYPE_SHORTCUT -> R.drawable.widget_preview_shortcut
                WidgetMC.WIDGET_SERVER_TYPE_VIDEO -> R.drawable.img_watching_default_placeholder
                WidgetMC.WIDGET_SERVER_TYPE_WELFARE -> R.drawable.widget_preview_welfare
                else -> null
            }
        }
    }

    override fun widgetHasAdd(serverType: Int?): Boolean {
        val clazz =
            serverType?.let { AppWidgetUtils.getWidgetClassByServerType(it) } ?: return false
        return AppWidgetUtils.isAppWidgetOpen(AppModule.getApplication(), clazz)
    }

    override fun hasAppWidget(json: String?): Boolean {
        if (json == "NetIncomeWidget") {
            val clazz = AppWidgetIncome::class.java
            return AppWidgetUtils.isAppWidgetOpen(AppModule.getApplication(), clazz)
        }
        val context = AppModule.getApplication()
        return supportWidgetClass.any { widget ->
            AppWidgetUtils.isAppWidgetOpen(context, widget)
        }
    }

    override fun canPinAppWidget(): Boolean {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.O) return false

        val appWidgetManager = AppWidgetManager.getInstance(AppModule.getApplication())
        LogUtil.d(TAG, "canPinAppWidget == ${appWidgetManager.isRequestPinAppWidgetSupported}")

        if (!appWidgetManager.isRequestPinAppWidgetSupported) return false

        // 有些厂商虽然返回true，但实际不支持加桌
        val blockedManufacturers = setOf("xiaomi", "vivo", "oppo")
        val manufacturer = Build.MANUFACTURER?.lowercase()

        return manufacturer !in blockedManufacturers
    }


    override fun createLaunchWidget(json: String?) {
        println("js调用：桌面Widget添加->Native")
        AppWidgetUtils.createLauncherWidget(json)?.let {
            waitingConfirmWidget.add(it)
        }
    }

    /**
     * 刷新桌面Widget
     */
    override fun sendAppWidgetRefreshBroadcast() {
        val intent = Intent(AppModule.getApplication(), AppWidgetUpdateReceiver::class.java)
        intent.action = "hema.appwidget.action.APPWIDGET_REFRESH"
        AppModule
            .getApplication()
            .sendBroadcast(intent)
    }

    override suspend fun getAddedVideoWidgets(): List<WidgetStateVo> {
        val result = mutableListOf<WidgetStateVo>()
        WidgetDatabase.get().getWidgetDAO().getWidgetByLocalType(WidgetMC.WIDGET_TYPE_VIDEO)
            .forEach {
                result.add(WidgetStateVo().apply {
                    localType = it.localType
                    serverType = it.serverType
                    widgetId = it.widgetId
                    lastUpdateTime = it.lastUpdateTime
                    bookId = it.bookId
                    bookName = it.bookName
                    coverUrl = it.coverUrl
                })
            }
        return result
    }

    override suspend fun saveVideoWidgetInfo(bookInfo: BaseBookInfo): Boolean {
        return bookInfo.bookId?.let { bookId ->
            val dao = WidgetDatabase.get().getWidgetDAO()
            if (dao.getVideoWidgetByBookId(WidgetMC.WIDGET_TYPE_VIDEO, bookId).isEmpty()) {
                dao.insertWidget(WidgetState().apply {
                    localType = WidgetMC.WIDGET_TYPE_VIDEO
                    serverType = WidgetMC.WIDGET_SERVER_TYPE_VIDEO
                    lastUpdateTime = System.currentTimeMillis()
                    this.bookId = bookId
                    bookName = bookInfo.bookName
                    coverUrl = bookInfo.coverWap
                })
                true
            } else {
                false
            }
        } ?: false
    }

    override suspend fun deleteVideoInfo(bookId: String) {
        WidgetDatabase.get().getWidgetDAO().apply {
            deleteVideoInfo(bookId)
        }
    }

    override fun updateVideoWidget() {
        LogUtil.d(TAG, "更新视频组件")
        val intent = Intent(AppModule.getApplication(), AppWidgetUpdateReceiver::class.java)
        intent.action = WidgetMC.ACTION_UPDATE_VIDEO
        AppModule
            .getApplication()
            .sendBroadcast(intent)
    }

    override fun updateIncomeWidget() {
        if (AppWidgetUtils.isAppWidgetOpen(
                AppModule.getApplication(),
                AppWidgetIncome::class.java
            )
        ) {
            LogUtil.d(TAG, "更新网赚组件")
            BBaseKV.getWidgetLastTime = System.currentTimeMillis()
            val intent = Intent(AppModule.getApplication(), AppWidgetUpdateReceiver::class.java)
            intent.action = WidgetMC.ACTION_UPDATE_INCOME
            AppModule
                .getApplication()
                .sendBroadcast(intent)
        }

    }

    override suspend fun clearUnbindVideoWidget(): Int {
        return WidgetDatabase.get().getWidgetDAO().clearUnbindVideoInfo(WidgetMC.WIDGET_TYPE_VIDEO)
    }

    override fun getSupportWidget(): List<Class<*>> {
        return supportWidgetClass
    }

    override fun getAddedWidgetCount(): Int {
        val context = AppModule.getApplication()
        val widgetManager = AppWidgetManager.getInstance(context)
        var count = 0
        supportWidgetClass.forEach { clazz ->
            count += widgetManager.getAppWidgetIds(ComponentName(context, clazz)).size
        }
        return count
    }

    override fun checkWaitingConfirmWidget() {
        val context = AppModule.getApplication()
        waitingConfirmWidget.forEach {
            val type = AppWidgetUtils.convertTypeFromClass(it)
            if (type == WidgetMC.WIDGET_TYPE_WELFARE_PUSH) {
                if (!AppWidgetUtils.isAppWidgetOpen(context, it)) {
                    WidgetME.get().onWidgetAddRejected().post(type)
                }
            }
            if (type != WidgetMC.WIDGET_TYPE_VIDEO) {
                if (AppWidgetUtils.isAppWidgetOpen(context, it)) {
                    WidgetME.get().onWidgetAddSuccess().post(type)
                } else {
                    WidgetME.get().onWidgetAddFailed().post(type)
                }
            }
        }
        waitingConfirmWidget.clear()
    }

    override fun refreshWelfareWidget() {
        LogUtil.d(TAG, "刷新福利组件")
        val intent = Intent(AppModule.getApplication(), AppWidgetUpdateReceiver::class.java)
        intent.action = WidgetMC.ACTION_UPDATE_WELFARE
        AppModule
            .getApplication()
            .sendBroadcast(intent)
    }

    override fun hasAddIncomeWidget(): Boolean {
        val clazz = AppWidgetIncome::class.java
        return AppWidgetUtils.isAppWidgetOpen(AppModule.getApplication(), clazz)
    }
}

const val TAG = "widget"