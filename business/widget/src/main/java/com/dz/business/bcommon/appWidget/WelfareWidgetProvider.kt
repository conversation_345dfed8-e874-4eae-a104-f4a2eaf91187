package com.dz.business.bcommon.appWidget

import android.appwidget.AppWidgetManager
import android.appwidget.AppWidgetProvider
import android.content.Context
import android.content.Intent
import android.widget.RemoteViews
import com.blankj.utilcode.util.GsonUtils
import com.dz.business.base.main.MainMS
import com.dz.business.base.widget.WidgetMC
import com.dz.business.base.widget.WidgetME
import com.dz.business.widget.R
import com.dz.business.widget.data.Constant
import com.dz.business.widget.utils.AppWidgetUtils
import com.dz.foundation.base.module.AppInfoUtil
import com.dz.foundation.base.module.AppModule
import com.dz.foundation.base.utils.LogUtil
import java.net.URLEncoder

/**
 *@Author: zhanggy
 *@Date: 2024-09-26
 *@Description:福利中心组件
 *@Version:1.0
 */
class WelfareWidgetProvider : AppWidgetProvider() {

    override fun onUpdate(
        context: Context?,
        appWidgetManager: AppWidgetManager?,
        appWidgetIds: IntArray?
    ) {
        LogUtil.d("widget_welfare", "WelfareWidgetProvider onUpdate")
        super.onUpdate(context, appWidgetManager, appWidgetIds)
        appWidgetManager?.let { mgr ->
            appWidgetIds?.let { ids ->
                updateWelfareUI(mgr, ids, true)
            }
        }
    }

    override fun onReceive(context: Context?, intent: Intent?) {
        super.onReceive(context, intent)
        if (intent?.action == Constant.ACTION_PIN_WIDGET_SUCCESS) {
            WidgetME.get().onWidgetAddSuccess().post(WidgetMC.WIDGET_TYPE_WELFARE)
        }
    }
}

/**
 * 更新组件
 */
fun updateWelfareUI(
    appWidgetManager: AppWidgetManager,
    appWidgetIds: IntArray,
    updateBySystem: Boolean = false
) {
    appWidgetIds.forEach { widgetId ->
        updateWelfareUI(appWidgetManager, widgetId)
    }
}

private fun updateWelfareUI(appWidgetManager: AppWidgetManager, widgetId: Int) {
    val hasWelfareTab = MainMS.get()?.hasWelfareTab() == true
    LogUtil.d("widget_welfare", "更新剧信息福利组件信息. 包含福利tab:$hasWelfareTab")

    val remoteView = RemoteViews(AppInfoUtil.getPackageName(), R.layout.widget_welfare_layout)
    val coverImageResId =
        if (hasWelfareTab) R.drawable.widget_preview_welfare else R.drawable.widget_preview_app
    remoteView.setImageViewResource(R.id.iv_cover, coverImageResId)

    val url = buildUrl(hasWelfareTab)
    val pendingIntent = AppWidgetUtils.getDeepLinkIntent(AppModule.getApplication(), url)
    remoteView.setOnClickPendingIntent(R.id.iv_cover, pendingIntent)

    appWidgetManager.updateAppWidget(widgetId, remoteView)
}

private fun buildUrl(hasWelfareTab: Boolean): String {
    val baseUri = "dz://${AppInfoUtil.getPackageName()}?action=main"
    // 通用参数
    val commonParams = hashMapOf(
        "pluginsId" to WidgetMC.WIDGET_TYPE_WELFARE,
    )

    val additionalParams = if (hasWelfareTab) {
        hashMapOf(
            "selectedTab" to "welfare",
            "buttonId" to "WelfareCenterS"
        )
    } else {
        hashMapOf(
            "buttonId" to "NoWelfareCenter"
        )
    }

    val finalParams = commonParams.apply { putAll(additionalParams) }
    val jsonParams = URLEncoder.encode(GsonUtils.toJson(finalParams), "utf-8")
    return "$baseUri&param=$jsonParams&launchFrom=widget"
}
