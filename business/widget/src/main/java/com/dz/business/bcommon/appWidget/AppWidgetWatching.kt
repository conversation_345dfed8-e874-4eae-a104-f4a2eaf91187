package com.dz.business.bcommon.appWidget

import android.appwidget.AppWidgetManager
import android.appwidget.AppWidgetProvider
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.drawable.Drawable
import android.widget.RemoteViews
import com.blankj.utilcode.util.GsonUtils
import com.bumptech.glide.Glide
import com.bumptech.glide.load.MultiTransformation
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.bumptech.glide.request.RequestOptions.bitmapTransform
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.transition.Transition
import com.bumptech.glide.signature.ObjectKey
import com.dz.business.base.load.DBHelper
import com.dz.business.base.widget.WidgetMC
import com.dz.business.base.widget.WidgetME
import com.dz.business.repository.entity.HistoryEntity
import com.dz.business.widget.R
import com.dz.business.widget.data.Constant
import com.dz.business.widget.data.Constant.DEFAULT_THEATRE_LIST
import com.dz.business.widget.utils.AppWidgetUtils
import com.dz.foundation.base.module.AppInfoUtil
import com.dz.foundation.base.module.AppModule
import com.dz.foundation.base.utils.dp
import jp.wasabeef.glide.transformations.BlurTransformation
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.net.URLEncoder
import java.util.UUID


/**
 * 我的在看Widget
 */
class AppWidgetWatching : AppWidgetProvider() {

    override fun onUpdate(context: Context, appWidgetManager: AppWidgetManager, appWidgetIds: IntArray) {
        refreshWatchingWidget(appWidgetManager, appWidgetIds)
    }

    override fun onReceive(context: Context?, intent: Intent?) {
        super.onReceive(context, intent)
        if (intent?.action == Constant.ACTION_PIN_WIDGET_SUCCESS) {
            WidgetME.get().onWidgetAddSuccess().post(WidgetMC.WIDGET_TYPE_WATCHING)
        }
    }
}


/**
 * 刷新布局，绑定点击事件
 */
private fun generateRemoteViews(widgetData: HistoryEntity?, appWidgetId: IntArray, appWidgetManager: AppWidgetManager?): RemoteViews {
    val views = RemoteViews(AppModule.getApplication().packageName, R.layout.bcommon_widget_watching_layout)

    //没有数据,跳转默认页
    if (widgetData == null) {
        return views;
    }
    val progress = if (widgetData.cur_index <= 0) 1 else widgetData.cur_index;
    views.setTextViewText(R.id.bcommon_widget_watching_progress, "观看至第${progress}集")
    val map = hashMapOf("bookId" to widgetData.bid, "chapterId" to widgetData.cur_cid, "deduplication" to true, "pluginsId" to "Recently_watched", "buttonId" to "")
    val url = "dz://${AppInfoUtil.getPackageName()}?action=video_List_activity&param=${URLEncoder.encode(GsonUtils.toJson(map), "utf-8")}" + "&launchFrom=widget"
    val pendingIntent = AppWidgetUtils.getDeepLinkIntent(AppModule.getApplication(), url)
    views.setOnClickPendingIntent(R.id.bcommon_widget_watching_placeholder, pendingIntent)
    widgetData.coverurl?.let {
        val multi = MultiTransformation(CenterCrop(), BlurTransformation(1), RoundedCorners(62))
        Glide
            .with(AppModule.getApplication())
            .asBitmap()
            .load(widgetData.coverurl)
            .placeholder(R.drawable.img_watching_default_placeholder)
            .override(150.dp, 150.dp)
            .signature(ObjectKey(UUID.randomUUID()))
            .apply(bitmapTransform(multi))
            .into(object : CustomTarget<Bitmap>() {
                override fun onResourceReady(resource: Bitmap, transition: Transition<in Bitmap>?) {
                    views.setImageViewBitmap(R.id.bcommon_widget_watching_placeholder, resource)
                    for (id in appWidgetId) {
                        appWidgetManager?.updateAppWidget(appWidgetId, views)
                    }
                }

                override fun onLoadCleared(placeholder: Drawable?) {
                }

            })
    }
    for (id in appWidgetId) {
        appWidgetManager?.updateAppWidget(appWidgetId, views)
    }
    return views;
}

/**
 * 刷新组件数据
 */
fun refreshWatchingWidget(appWidgetManager: AppWidgetManager, appWidgetIds: IntArray) {
    runCatching {
        CoroutineScope(Dispatchers.IO).launch {
            val latestWatchingData = DBHelper
                .queryViewHistory(1)
                ?.firstOrNull()
            withContext(Dispatchers.Main) {
                var data = latestWatchingData
                if (latestWatchingData == null) {
                    data = DEFAULT_THEATRE_LIST
                        .first()
                        .let {
                            val widgetData = HistoryEntity(it.bookId)
                            widgetData.book_name = it.bookName
                            widgetData.coverurl = it.coverWap
                            widgetData.cur_index = 1
                            widgetData.cur_cid = ""
                            widgetData
                        }
                }
                generateRemoteViews(data, appWidgetIds, appWidgetManager)
            }
        }
    }
        .onFailure { it.printStackTrace() }
}

