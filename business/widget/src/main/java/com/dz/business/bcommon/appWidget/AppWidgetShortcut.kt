package com.dz.business.bcommon.appWidget

import android.appwidget.AppWidgetManager
import android.appwidget.AppWidgetProvider
import android.content.Context
import android.content.Intent
import android.widget.RemoteViews
import com.blankj.utilcode.util.GsonUtils
import com.dz.business.base.widget.WidgetMC
import com.dz.business.base.widget.WidgetME
import com.dz.business.widget.R
import com.dz.business.widget.data.Constant
import com.dz.business.widget.utils.AppWidgetUtils
import java.net.URLEncoder


/**
 * 快捷组件widget
 */
class AppWidgetShortcut : AppWidgetProvider() {

    override fun onUpdate(context: Context, appWidgetManager: AppWidgetManager, appWidgetIds: IntArray) {
        for (appWidgetId in appWidgetIds) {
            updateAppWidgetFollow(context, appWidgetManager, appWidgetId)
        }
    }

    override fun onReceive(context: Context?, intent: Intent?) {
        super.onReceive(context, intent)

        if (intent?.action == Constant.ACTION_PIN_WIDGET_SUCCESS) {
            WidgetME.get().onWidgetAddSuccess().post(WidgetMC.WIDGET_TYPE_SHORTCUT)
        }
    }
}

internal fun updateAppWidgetFollow(context: Context, appWidgetManager: AppWidgetManager, appWidgetId: Int) {
    val views = RemoteViews(context.packageName, R.layout.bcommon_widget_shortcut_layout)

    //在追
    val mapWatching=hashMapOf("bookId" to "", "buttonId" to "Currency_Chasing", "pluginsId" to "Currency")
    val watchingDeepLink = "hmjc://app.client?action=personal_collection&param=${URLEncoder.encode(GsonUtils.toJson(mapWatching), "utf-8")}&launchFrom=widget"
    val watchingPendingIntent = AppWidgetUtils.getDeepLinkIntent(context, watchingDeepLink)
    views.setOnClickPendingIntent(R.id.bcommon_widget_shortcut_text_watching, watchingPendingIntent)

    //搜索
    val mapSearch=hashMapOf("bookId" to "", "buttonId" to "Currency_search", "pluginsId" to "Currency")
    val searchDeepLink = "hmjc://app.client?action=search&param=${URLEncoder.encode(GsonUtils.toJson(mapSearch), "utf-8")}&launchFrom=widget"
    val searchPendingIntent = AppWidgetUtils.getDeepLinkIntent(context, searchDeepLink)
    views.setOnClickPendingIntent(R.id.bcommon_widget_shortcut_text_searching, searchPendingIntent)

    //剧单
    val mapSeries=hashMapOf("bookId" to "", "buttonId" to "Currency_Drama", "pluginsId" to "Currency","selectedTab" to "home","homeTabPage" to "dramaList")
    val seriesDeepLink = "hmjc://app.client?action=main&param=${URLEncoder.encode(GsonUtils.toJson(mapSeries), "utf-8")}&launchFrom=widget"
    val seriesPendingIntent = AppWidgetUtils.getDeepLinkIntent(context, seriesDeepLink)
    views.setOnClickPendingIntent(R.id.bcommon_widget_shortcut_text_series, seriesPendingIntent)

    //观看历史
    val mapHistory=hashMapOf("bookId" to "", "buttonId" to "Currency_History", "pluginsId" to "Currency")
    val historyDeepLink = "hmjc://app.client?action=flutter/HistoryHomePage&routerType=2&param=${URLEncoder.encode(GsonUtils.toJson(mapHistory), "utf-8")}&launchFrom=widget"
    val historyPendingIntent = AppWidgetUtils.getDeepLinkIntent(context, historyDeepLink)
    views.setOnClickPendingIntent(R.id.bcommon_widget_shortcut_text_history, historyPendingIntent)

    appWidgetManager.updateAppWidget(appWidgetId, views)
}