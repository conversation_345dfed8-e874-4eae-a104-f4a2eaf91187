package com.dz.business.bcommon.appWidget

import android.appwidget.AppWidgetManager
import android.appwidget.AppWidgetProvider
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.drawable.Drawable
import android.widget.RemoteViews
import com.blankj.utilcode.util.GsonUtils
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.MultiTransformation
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.RequestOptions.bitmapTransform
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.target.Target
import com.bumptech.glide.request.transition.Transition
import com.bumptech.glide.signature.ObjectKey
import com.dz.business.base.widget.WidgetMC
import com.dz.business.base.widget.WidgetME
import com.dz.business.widget.R
import com.dz.business.widget.data.Constant
import com.dz.business.widget.db.WidgetDatabase
import com.dz.business.widget.db.entity.WidgetState
import com.dz.business.widget.utils.AppWidgetUtils
import com.dz.foundation.base.module.AppInfoUtil
import com.dz.foundation.base.module.AppModule
import com.dz.foundation.base.utils.LogUtil
import com.dz.foundation.base.utils.dp
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.net.URLEncoder
import java.util.UUID

/**
 *@Author: zhanggy
 *@Date: 2024-08-28
 *@Description:1*1，剧组件
 *@Version:1.0
 */
class VideoWidgetProvider : AppWidgetProvider() {

    override fun onUpdate(
        context: Context?,
        appWidgetManager: AppWidgetManager?,
        appWidgetIds: IntArray?
    ) {
        LogUtil.d("widget_video", "VideoWidgetProvider onUpdate")
        super.onUpdate(context, appWidgetManager, appWidgetIds)
        appWidgetManager?.let { mgr ->
            appWidgetIds?.let { ids ->
                updateVideoUI(mgr, ids, true)
            }
        }
    }

    override fun onDeleted(context: Context?, appWidgetIds: IntArray?) {
        super.onDeleted(context, appWidgetIds)
        CoroutineScope(Dispatchers.IO).launch {
            val dao = WidgetDatabase.get().getWidgetDAO()
            appWidgetIds?.forEach { widgetId ->
                LogUtil.d("widget_video", "删除组件 video id:$widgetId")
                dao.deleteWidget(WidgetMC.WIDGET_TYPE_VIDEO, widgetId)
            }
        }
    }

    override fun onReceive(context: Context?, intent: Intent?) {
        super.onReceive(context, intent)
        if (intent?.action == Constant.ACTION_PIN_WIDGET_SUCCESS) {
            LogUtil.d("widget_video", "video widget 添加成功")
            WidgetME.get().onWidgetAddSuccess().post(WidgetMC.WIDGET_TYPE_VIDEO)
        }
    }
}

/**
 * 更新组件
 */
fun updateVideoUI(
    appWidgetManager: AppWidgetManager,
    appWidgetIds: IntArray,
    updateBySystem: Boolean = false
) {
    CoroutineScope(Dispatchers.IO).launch {
        val dao = WidgetDatabase.get().getWidgetDAO()
        var videoInfos = if (updateBySystem) {
            val unbindVideo = dao.getUnbindWidget(WidgetMC.WIDGET_TYPE_VIDEO)
            if (appWidgetIds.size == 1 && unbindVideo.isNotEmpty()) {
                unbindVideo
            } else {
                dao.getAllVideoInfo(WidgetMC.WIDGET_TYPE_VIDEO)
            }
        } else {
            dao.getAllVideoInfo(WidgetMC.WIDGET_TYPE_VIDEO)
        }
        if (videoInfos.isEmpty()) {  // 兜底剧信息
            videoInfos = Constant.DEFAULT_THEATRE_LIST.map { item ->
                WidgetState().apply {
                    localType = WidgetMC.WIDGET_TYPE_VIDEO
                    serverType = WidgetMC.WIDGET_SERVER_TYPE_VIDEO
                    lastUpdateTime = System.currentTimeMillis()
                    bookName = item.bookName
                    bookId = item.bookId
                    coverUrl = item.coverWap
                }
            }.toMutableList()
        }
        LogUtil.d("widget_video", "更新的剧信息:$videoInfos\n appWidgetIds:${appWidgetIds.joinToString()}")
        appWidgetIds.forEachIndexed { index, widgetId ->
            if (videoInfos.isNotEmpty() && videoInfos.indices.contains(index)) {
                LogUtil.d(
                    "widget_video", "VideoWidgetProvider onUpdate widgetId:$widgetId " +
                            "index:$index ${videoInfos[index]}"
                )
                withContext(Dispatchers.Main) {
                    updateVideoUI(widgetId, videoInfos[index], appWidgetManager)
                }
                videoInfos[index].widgetId = widgetId
                dao.insertWidget(videoInfos[index])
            }
        }
        LogUtil.d("widget_video", "更新结束")
    }
}

fun updateVideoUI(widgetId: Int, info: WidgetState, appWidgetManager: AppWidgetManager) {
    if (info.bookId.isNullOrEmpty() || info.bookName.isNullOrEmpty() || info.coverUrl.isNullOrEmpty()) {
        LogUtil.d("widget_video", "信息缺失，不更新.$info")
        return
    }
    LogUtil.d("widget_video", "更新剧信息:$info widgetId:$widgetId")
    val remoteView = RemoteViews(AppInfoUtil.getPackageName(), R.layout.widget_video_layout)
    remoteView.setTextViewText(R.id.tv_name, info.bookName)

    val map = hashMapOf(
        "bookId" to info.bookId,
        "deduplication" to true,
        "pluginsId" to "watching",
    )
    val url = "dz://${AppInfoUtil.getPackageName()}?action=video_List_activity&param=${
        URLEncoder.encode(
            GsonUtils.toJson(map), "utf-8"
        )
    }" + "&launchFrom=widget"
    val pendingIntent = AppWidgetUtils.getDeepLinkIntent(AppModule.getApplication(), url)
    remoteView.setOnClickPendingIntent(R.id.iv_cover, pendingIntent)

    val context = AppModule.getApplication()
    val multi = MultiTransformation(CenterCrop(), RoundedCorners(62))
    Glide.with(context).asBitmap()
        .load(info.coverUrl)
        .centerCrop()
        .placeholder(R.drawable.img_watching_default_placeholder)
        .override(150.dp, 150.dp)
        .signature(ObjectKey(UUID.randomUUID()))
        .addListener(object : RequestListener<Bitmap> {
            override fun onLoadFailed(
                e: GlideException?,
                model: Any?,
                target: Target<Bitmap>?,
                isFirstResource: Boolean
            ): Boolean {
                LogUtil.d("widget_video", "$widgetId 封面加桌失败 ${e?.message}")
                return false
            }

            override fun onResourceReady(
                resource: Bitmap?,
                model: Any?,
                target: Target<Bitmap>?,
                dataSource: DataSource?,
                isFirstResource: Boolean
            ): Boolean {
                return false
            }
        })
        .apply(bitmapTransform(multi))
        .into(object : CustomTarget<Bitmap?>() {

            override fun onResourceReady(
                resource: Bitmap,
                transition: Transition<in Bitmap?>?,
            ) {
                LogUtil.d("widget_video", "$widgetId 封面加载成功")
                remoteView.setImageViewBitmap(R.id.iv_cover, resource)
                appWidgetManager.updateAppWidget(widgetId, remoteView)
            }

            override fun onLoadCleared(placeholder: Drawable?) {
            }
        })
}