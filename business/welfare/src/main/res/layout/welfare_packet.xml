<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
>

    <com.dz.foundation.ui.widget.DzConstraintLayout
        android:id="@+id/cl_root"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.dz.foundation.ui.widget.DzImageView
            android:id="@+id/tv_cancel"
            android:layout_width="@dimen/common_dp24"
            android:layout_height="@dimen/common_dp24"
            android:layout_marginBottom="@dimen/common_dp11"
            android:src="@drawable/welfare_new_user_cancel"
            app:layout_constraintEnd_toEndOf="@+id/packet_root"
            app:layout_constraintBottom_toTopOf="@+id/packet_root"

            />

        <com.dz.foundation.ui.widget.DzConstraintLayout
            android:id="@+id/packet_root"
            android:layout_width="@dimen/common_dp295"
            android:layout_height="@dimen/common_dp380"
            android:layout_marginTop="@dimen/common_dp48.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent">

            <com.dz.foundation.ui.widget.DzImageView
                android:id="@+id/tv_packet"
                android:layout_width="@dimen/common_dp295"
                android:layout_height="@dimen/common_dp380"
                android:src="@drawable/welfare_packet_default"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                />









        </com.dz.foundation.ui.widget.DzConstraintLayout>





    </com.dz.foundation.ui.widget.DzConstraintLayout>

</layout>