<?xml version="1.0" encoding="utf-8"?>
<com.dz.foundation.ui.widget.DzConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="@dimen/common_dp100"
    android:clipChildren="true"
    android:paddingStart="@dimen/common_dp5"
    android:paddingBottom="@dimen/common_dp5">

    <com.dz.foundation.ui.widget.DzImageView
        android:id="@+id/iv_welfare_progress_close"
        android:layout_width="@dimen/common_dp15"
        android:layout_height="@dimen/common_dp15"
        android:layout_marginEnd="-10dp"
        android:scaleType="fitXY"
        android:src="@drawable/welfare_ic_entry_close"
        app:layout_constraintBottom_toTopOf="@+id/iv_welfare_progress_bkg"
        app:layout_constraintEnd_toEndOf="@+id/iv_welfare_progress_bkg" />

    <View
        android:id="@+id/v_reward_prompt_bkg1"
        android:layout_width="@dimen/welfare_pendant_diameter"
        android:layout_height="@dimen/welfare_pendant_diameter"
        android:background="@drawable/welfare_pendant_ani_bkg"
        android:scaleX="0"
        android:scaleY="0"
        app:layout_constraintBottom_toBottomOf="@+id/iv_welfare_progress_bkg"
        app:layout_constraintEnd_toEndOf="@+id/iv_welfare_progress_bkg"
        app:layout_constraintTop_toTopOf="@+id/iv_welfare_progress_bkg" />

    <FrameLayout
        android:id="@+id/fl_reward_prompt"
        android:layout_width="0.5dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/welfare_pendant_radius"
        android:background="@drawable/welfare_bkg_pendant_prompt"
        android:paddingEnd="@dimen/welfare_pendant_radius"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/iv_welfare_progress_bkg"
        app:layout_constraintEnd_toEndOf="@+id/iv_welfare_progress_bkg"
        app:layout_constraintTop_toTopOf="@+id/iv_welfare_progress_bkg">

        <com.dz.foundation.ui.widget.DzTextView
            android:id="@+id/tv_coin_reward_prompt"
            android:layout_width="@dimen/common_dp116.5"
            android:layout_height="@dimen/welfare_pendant_diameter"
            android:gravity="center_vertical"
            android:maxLines="2"
            android:paddingStart="@dimen/common_dp16"
            android:paddingEnd="@dimen/common_dp4"
            android:lineSpacingExtra="@dimen/common_dp1"
            android:shadowColor="#1f000000"
            android:shadowRadius="3.0"
            android:textColor="#ffffffff"
            android:textSize="13sp"
            android:textStyle="bold"
            tools:text="看剧任务已完成\n可领取金币" />
    </FrameLayout>

    <com.dz.foundation.ui.widget.DzView
        android:id="@+id/iv_welfare_progress_bkg"
        android:layout_width="@dimen/welfare_pendant_diameter"
        android:layout_height="@dimen/welfare_pendant_diameter"
        android:layout_marginEnd="@dimen/common_dp10"
        android:layout_marginBottom="@dimen/common_dp11"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:shape="oval"
        app:shape_radius="@dimen/common_dp30"
        app:shape_solid_color="#80000000" />

    <com.dz.foundation.ui.widget.DzImageView
        android:id="@+id/iv_welfare_content"
        android:layout_width="@dimen/common_dp34"
        android:layout_height="@dimen/common_dp34"
        android:scaleType="centerCrop"
        app:layout_constraintBottom_toBottomOf="@+id/circle_progress"
        app:layout_constraintEnd_toEndOf="@+id/circle_progress"
        app:layout_constraintStart_toStartOf="@+id/circle_progress"
        app:layout_constraintTop_toTopOf="@+id/circle_progress" />

    <com.dz.foundation.ui.view.CircleProgress
        android:id="@+id/circle_progress"
        android:layout_width="@dimen/welfare_pendant_diameter"
        android:layout_height="@dimen/welfare_pendant_diameter"
        app:circle_background="@color/common_transparent"
        app:circle_color="#FFFFFDA4"
        app:circle_stroke_width="@dimen/common_dp3"
        app:layout_constraintBottom_toBottomOf="@+id/iv_welfare_progress_bkg"
        app:layout_constraintEnd_toEndOf="@+id/iv_welfare_progress_bkg"
        app:layout_constraintStart_toStartOf="@+id/iv_welfare_progress_bkg"
        app:layout_constraintTop_toTopOf="@+id/iv_welfare_progress_bkg" />

    <com.dz.foundation.ui.widget.DzTextView
        android:id="@+id/tv_welfare_receive"
        android:layout_width="@dimen/common_dp68"
        android:layout_height="@dimen/common_dp21"
        android:background="@drawable/welfare_kg_pendant_content"
        android:gravity="center"
        android:includeFontPadding="false"
        android:text="看剧赚金币"
        android:textColor="#FFFFFBE8"
        android:textSize="@dimen/common_dp10"
        android:textStyle="bold"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@+id/circle_progress"
        app:layout_constraintStart_toStartOf="@+id/circle_progress" />

    <com.dz.foundation.ui.widget.DzLinearLayout
        android:id="@+id/ll_add_coins"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal|bottom"
        android:scaleX="0"
        android:scaleY="0"
        app:layout_constraintBottom_toBottomOf="@+id/circle_progress"
        app:layout_constraintEnd_toEndOf="@+id/circle_progress"
        app:layout_constraintStart_toStartOf="@+id/circle_progress"
        app:layout_constraintTop_toTopOf="@+id/circle_progress">

        <com.dz.foundation.ui.widget.DzImageView
            android:layout_width="@dimen/common_dp14"
            android:layout_height="@dimen/common_dp14"
            android:src="@drawable/welfare_ic_coin" />

        <com.dz.foundation.ui.widget.DzTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="+"
            android:textColor="@color/common_FFFFFFFF"
            android:textSize="@dimen/common_dp12"
            android:textStyle="bold" />

        <com.dz.foundation.ui.widget.DzTextView
            android:id="@+id/tv_add_coins"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/common_FFFFFFFF"
            android:textSize="@dimen/common_dp12"
            android:textStyle="bold" />
    </com.dz.foundation.ui.widget.DzLinearLayout>

</com.dz.foundation.ui.widget.DzConstraintLayout>