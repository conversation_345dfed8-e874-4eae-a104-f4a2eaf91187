<?xml version="1.0" encoding="utf-8"?>
<com.dz.foundation.ui.widget.DzConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:clipChildren="true"
    android:minWidth="@dimen/common_dp25">

    <com.dz.foundation.ui.widget.DzConstraintLayout
        android:id="@+id/cl_welfare"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/common_dp110"
        android:paddingStart="@dimen/common_dp5"
        android:paddingEnd="@dimen/common_dp8"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.dz.foundation.ui.widget.DzImageView
            android:id="@+id/iv_welfare_progress_close"
            android:layout_width="@dimen/common_dp15"
            android:layout_height="@dimen/common_dp15"
            android:layout_marginEnd="-10dp"
            android:scaleType="fitXY"
            android:src="@drawable/welfare_ic_entry_close"
            app:layout_constraintBottom_toTopOf="@+id/iv_welfare_progress_bkg"
            app:layout_constraintEnd_toEndOf="@+id/iv_welfare_progress_bkg" />

        <View
            android:id="@+id/v_reward_prompt_bkg"
            android:layout_width="@dimen/common_dp48"
            android:layout_height="@dimen/common_dp48"
            android:background="@drawable/welfare_pendant_drag_bkg"
            android:scaleX="0"
            android:scaleY="0"
            app:layout_constraintBottom_toBottomOf="@+id/iv_welfare_progress_bkg"
            app:layout_constraintEnd_toEndOf="@+id/iv_welfare_progress_bkg"
            app:layout_constraintTop_toTopOf="@+id/iv_welfare_progress_bkg" />

        <FrameLayout
            android:id="@+id/fl_reward_prompt"
            android:layout_width="0.5dp"
            android:layout_height="@dimen/common_dp48"
            android:layout_marginEnd="@dimen/common_dp24"
            android:background="@drawable/welfare_bkg_dynamic_prompt"
            android:paddingEnd="@dimen/common_dp24"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@+id/iv_welfare_progress_bkg"
            app:layout_constraintEnd_toEndOf="@+id/iv_welfare_progress_bkg"
            app:layout_constraintTop_toTopOf="@+id/iv_welfare_progress_bkg">

            <com.dz.foundation.ui.widget.DzTextView
                android:id="@+id/tv_coin_reward_prompt"
                android:layout_width="@dimen/common_dp107"
                android:layout_height="@dimen/common_dp48"
                android:gravity="center_vertical"
                android:maxLines="2"
                android:paddingStart="@dimen/common_dp24"
                android:paddingEnd="@dimen/common_dp4"
                android:shadowColor="@color/white"
                android:shadowRadius="3.0"
                android:textColor="#ffffffff"
                android:textSize="@dimen/common_dp12"
                tools:text="看剧任务已完成\n可领取金币" />
        </FrameLayout>

        <com.dz.foundation.ui.widget.DzView
            android:id="@+id/iv_welfare_progress_bkg"
            android:layout_width="@dimen/common_dp48"
            android:layout_height="@dimen/common_dp48"
            android:layout_marginEnd="@dimen/common_dp12"
            android:layout_marginBottom="@dimen/common_dp16"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:shape="oval"
            app:shape_radius="@dimen/common_dp30"
            app:shape_solid_color="#80000000" />

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/la_static_red_envelope"
            android:layout_width="@dimen/common_dp55"
            android:layout_height="@dimen/common_dp85"
            android:layout_gravity="center"
            android:visibility="visible"
            app:content="静态"
            app:layout_constraintBottom_toBottomOf="@+id/iv_welfare_progress_bkg"
            app:layout_constraintEnd_toEndOf="@+id/iv_welfare_progress_bkg"
            app:layout_constraintStart_toStartOf="@+id/iv_welfare_progress_bkg"
            app:lottie_autoPlay="false"
            app:lottie_fileName="startdrag.json"
            app:lottie_loop="false" />

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/la_start_red_envelope"
            android:layout_width="@dimen/common_dp55"
            android:layout_height="@dimen/common_dp85"
            android:layout_gravity="center"
            android:visibility="gone"
            app:content="开头"
            app:layout_constraintBottom_toBottomOf="@+id/iv_welfare_progress_bkg"
            app:layout_constraintEnd_toEndOf="@+id/iv_welfare_progress_bkg"
            app:layout_constraintStart_toStartOf="@+id/iv_welfare_progress_bkg"
            app:lottie_autoPlay="false"
            app:lottie_fileName="startdrag.json"
            app:lottie_loop="false" />

<!--        <com.airbnb.lottie.LottieAnimationView-->
<!--            android:id="@+id/la_loading_red_envelope"-->
<!--            android:layout_width="@dimen/common_dp55"-->
<!--            android:layout_height="@dimen/common_dp90"-->
<!--            android:layout_gravity="center"-->
<!--            android:visibility="gone"-->
<!--            app:content="加载"-->
<!--            app:layout_constraintBottom_toBottomOf="@+id/iv_welfare_progress_bkg"-->
<!--            app:layout_constraintEnd_toEndOf="@+id/iv_welfare_progress_bkg"-->
<!--            app:layout_constraintStart_toStartOf="@+id/iv_welfare_progress_bkg"-->
<!--            app:lottie_autoPlay="false"-->
<!--            app:lottie_fileName="loadingdrag.json"-->
<!--            app:lottie_loop="false" />-->

<!--        <com.airbnb.lottie.LottieAnimationView-->
<!--            android:id="@+id/la_close_red_envelope"-->
<!--            android:layout_width="@dimen/common_dp55"-->
<!--            android:layout_height="@dimen/common_dp90"-->
<!--            android:layout_gravity="center"-->
<!--            android:visibility="gone"-->
<!--            app:content="关闭"-->
<!--            app:layout_constraintBottom_toBottomOf="@+id/iv_welfare_progress_bkg"-->
<!--            app:layout_constraintEnd_toEndOf="@+id/iv_welfare_progress_bkg"-->
<!--            app:layout_constraintStart_toStartOf="@+id/iv_welfare_progress_bkg"-->
<!--            app:lottie_autoPlay="false"-->
<!--            app:lottie_fileName="closedrag.json"-->
<!--            app:lottie_loop="false" />-->

        <com.dz.foundation.ui.view.CircleProgress
            android:id="@+id/circle_progress"
            android:layout_width="@dimen/common_dp48"
            android:layout_height="@dimen/common_dp48"
            app:circle_background="@color/common_transparent"
            app:circle_color="@color/common_FFFED05E"
            app:circle_stroke_width="@dimen/common_dp3"
            app:layout_constraintBottom_toBottomOf="@+id/iv_welfare_progress_bkg"
            app:layout_constraintEnd_toEndOf="@+id/iv_welfare_progress_bkg"
            app:layout_constraintStart_toStartOf="@+id/iv_welfare_progress_bkg"
            app:layout_constraintTop_toTopOf="@+id/iv_welfare_progress_bkg" />

        <com.dz.business.welfare.widget.NumberAnimationView
            android:id="@+id/rtv"
            android:layout_width="@dimen/common_dp28"
            android:layout_height="@dimen/common_dp15"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:layout_marginBottom="@dimen/common_dp6"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="@+id/iv_welfare_progress_bkg"
            app:layout_constraintStart_toStartOf="@+id/iv_welfare_progress_bkg"
            app:layout_constraintBottom_toBottomOf="@+id/iv_welfare_progress_bkg" />

        <com.dz.foundation.ui.widget.DzTextView
            android:id="@+id/tv_welfare_receive"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/common_dp21"
            android:background="@drawable/welfare_dynamics_content_bkg"
            android:gravity="center"
            android:includeFontPadding="false"
            android:paddingHorizontal="@dimen/common_dp5"
            android:text="领99999金币"
            android:textColor="#FFFFFBE8"
            android:textSize="@dimen/common_dp10"
            android:textStyle="bold"
            android:visibility="gone"
            android:layout_marginBottom="@dimen/common_dp4"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="@+id/iv_welfare_progress_bkg"
            app:layout_constraintStart_toStartOf="@+id/iv_welfare_progress_bkg" />

        <com.dz.foundation.ui.widget.DzLinearLayout
            android:id="@+id/ll_add_coins"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal|bottom"
            android:scaleX="0"
            android:scaleY="0"
            app:layout_constraintBottom_toBottomOf="@+id/iv_welfare_progress_bkg"
            app:layout_constraintEnd_toEndOf="@+id/iv_welfare_progress_bkg"
            app:layout_constraintStart_toStartOf="@+id/iv_welfare_progress_bkg"
            app:layout_constraintTop_toTopOf="@+id/iv_welfare_progress_bkg">

            <com.dz.foundation.ui.widget.DzImageView
                android:layout_width="@dimen/common_dp14"
                android:layout_height="@dimen/common_dp14"
                android:src="@drawable/welfare_ic_coin" />

            <com.dz.foundation.ui.widget.DzTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="+"
                android:textColor="@color/common_FFFFFFFF"
                android:textSize="@dimen/common_dp12"
                android:textStyle="bold" />

            <com.dz.foundation.ui.widget.DzTextView
                android:id="@+id/tv_add_coins"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/common_FFFFFFFF"
                android:textSize="@dimen/common_dp12"
                android:textStyle="bold" />
        </com.dz.foundation.ui.widget.DzLinearLayout>
    </com.dz.foundation.ui.widget.DzConstraintLayout>

    <com.dz.foundation.ui.widget.DzConstraintLayout
        android:id="@+id/cl_welfare_progress_close"
        android:layout_width="0dp"
        android:layout_height="@dimen/common_dp48"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <ImageView
            android:id="@+id/iv_welfare_close"
            android:layout_width="@dimen/common_dp25"
            android:layout_height="@dimen/common_dp48"
            android:src="@drawable/welfare_ic_arrow_close"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

    </com.dz.foundation.ui.widget.DzConstraintLayout>
</com.dz.foundation.ui.widget.DzConstraintLayout>