package com.dz.business.welfare.report

import com.dz.business.base.data.bean.WelfarePlayingReadTask
import com.dz.business.base.network.BBaseNetWork
import com.dz.business.base.utils.GsonUtil
import com.dz.business.base.welfare.WelfareME
import com.dz.business.base.welfare.WelfareMR
import com.dz.business.welfare.WelfareMS
import com.dz.business.welfare.data.TaskReportResult
import com.dz.business.welfare.data.WelfareKV
import com.dz.business.welfare.interfaces.ReportCallback
import com.dz.business.welfare.network.WelfareNetWork
import com.dz.foundation.base.utils.LogUtil
import com.dz.foundation.network.onError
import com.dz.foundation.network.onResponse
import com.dz.platform.common.router.onDismiss
import com.dz.platform.common.router.onShow

/**
 *@Author: zhanggy
 *@Date: 2023-06-16
 *@Description: 活动任务上报管理
 *@Version:1.0
 */
object TaskReportManager {

    private var mTaskConfig: WelfarePlayingReadTask? = null
    private var mReportResponse: TaskReportResult? = null

    /**
     * 看剧时长任务相关配置信息
     */
    var playingTaskConfig: WelfarePlayingReadTask? = null

    fun reportPlayingDuration(
        duration: Int,
        bookId: String?,
        chapterId: String?,
        callback: ReportCallback?,
        bookReads: Map<String, Long>,
        needStageTask: Int
    ) {
        if (playingTaskConfig == null && WelfareKV.readTaskConfig.isNotEmpty()) {
            LogUtil.d(TAG, "Read task config is null. use cache: ${WelfareKV.readTaskConfig}")
            if (WelfareKV.readTaskConfig != "None") {
                playingTaskConfig =
                    GsonUtil.fromJson(WelfareKV.readTaskConfig, WelfarePlayingReadTask::class.java)
            }
        }
        if (playingTaskConfig == null || playingTaskConfig?.taskId == null ||
            playingTaskConfig?.action == null || bookId == null || chapterId == null
        ) {
            val msg = "任务上报失败，参数异常。config:$playingTaskConfig, duration:${duration}秒"
            LogUtil.e(TAG, msg)
            callback?.onFailed(1, msg)
            if (playingTaskConfig == null && WelfareKV.readTaskConfig != "None") {
                LogUtil.d(TAG, "request 1150 for read task config.")
                BBaseNetWork.get().commonConfig().setParam(WelfareMS.get()?.getWelfareVersion())
                    .onResponse {
                    LogUtil.d(TAG, "request 1150 success")
                    it.data?.let { conf ->
                        WelfareMS.get()?.saveOperationConfig(conf)
                    }
                }.doRequest()
            }
            return
        }
        val taskConfig = playingTaskConfig!!
        val reportDuration = (playingTaskConfig!!.chapterMaxReadTime ?: 180).run {
            if (this in 1 until duration) {
                this
            } else {
                duration
            }
        }
        WelfareNetWork.get().welfareTaskReport()
            .addParams(taskConfig.action!!, taskConfig.taskId!!, reportDuration, bookId, chapterId, 1, bookReads, needStageTask)
            .onResponse { report ->
                LogUtil.d("打印","1302接口：任务上报： taskConfig.action=${taskConfig.action}  it=${report.data}")
                report.data?.apply {
                    LogUtil.d(
                        TAG,
                        "任务上报成功！当前累计看剧时长：${totalReadDuration}分钟"
                    )
//                    mReportResponse = reportResponse
//                    mTaskConfig = taskConfig
                    if (status == 0) {
                        callback?.onSuccess(report.data)
                    } else {
                        callback?.onFailed(status ?: 3, msg ?: "服务端报错")
                    }
                } ?: let {
                    callback?.onFailed(2, "response data is null")
                }
            }.onError {
                callback?.onFailed(3, "request error ${it.errorCode} ${it.message}")
            }.doRequest()
    }

    fun openWelfDialog() {
        mReportResponse?.let { reportResponse ->
            if (reportResponse.awardNum > 0 && !reportResponse.msg.isNullOrEmpty()) {  // 获取奖励
                WelfareMR.get().rewardSuccess().apply {
                    playingDuration = reportResponse.totalReadDuration
                    taskId = mTaskConfig?.taskId!!
                    rewardCoin = reportResponse.awardNum

                }
                    .onShow {
                        LogUtil.d(TAG, "任务奖励弹窗 展示")
                        WelfareME.get().awardDialog().post(true)
                    }
                    .onDismiss {
                        LogUtil.d(TAG, "任务奖励弹窗 隐藏")
                        mReportResponse = null
                        mTaskConfig = null
                        WelfareME.get().awardDialog().post(false)
                    }
                    .start()
            }
        }
    }

}

const val TAG = "welfare_report"