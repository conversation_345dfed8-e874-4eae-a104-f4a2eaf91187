package com.dz.business.welfare.data

import com.dz.business.base.data.bean.BaseBean
import com.dz.business.base.data.bean.NewReadAwardNotice

/**
 *@Author: zhanggy
 *@Date: 2023-06-16
 *@Description:
 *@Version:1.0
 */
data class TaskReportResult(
    var totalReadDuration: Long = 0,  // 当天阅读时长，单位：
    var status: Int?,  // 状态
    var msg: String?,  // 信息
    var awardNum: Long = 0,  // 金币数
    var stageReadAwardList: List<StageReadAward>? = null,
    var actualReadTimeSeconds: Long? = null,  // 阅读时长(秒)
    var recStageCoinTip: Int?,  // 0-不开启引导配置， 1-开启引导配置
    var newReadAwardNotice: NewReadAwardNotice?,  // 2.10.0 新手看剧通知
) : BaseBean()

data class StageReadAward(
    val award: Int = 0,  // 奖励金币数
    val duration: Int = 0,  // 阅读时长
    val status: Int = 0,  // 状态：1.未完成 2.待领取 3.已领取
    var index: Int = -1,  // 阶段index
): BaseBean()