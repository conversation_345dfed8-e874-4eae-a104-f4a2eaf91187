package com.dz.business.web.vm

import android.app.Activity
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.dianzhong.base.eventbus.AdType
import com.dianzhong.common.util.DzLog
import com.dz.business.base.ad.AdMC.AD_REWARD_FAILED
import com.dz.business.base.ad.AdMS
import com.dz.business.base.ad.callback.DzAdShowCallback
import com.dz.business.base.vm.PageVM
import com.dz.business.base.web.intent.FlipCardsDialogIntent
import com.dz.business.track.events.sensor.AdTE
import com.dz.business.web.data.CardDataBean
import com.dz.business.web.network.WebNetWork
import com.dz.business.web.util.TaskUtils
import com.dz.business.web.util.TaskUtils.isHidePopUp
import com.dz.foundation.base.utils.LogUtil
import com.dz.foundation.network.onError
import com.dz.foundation.network.onResponse
import com.dz.foundation.network.onStart
import com.dz.platform.ad.data.AdLoadConfig
import com.dz.platform.ad.vo.AdConstant
import com.dz.platform.common.toast.ToastManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * @Description: 福利页翻卡任务
 * @Version:1.0
 */
class RewardFileCardsDialogVM : PageVM<FlipCardsDialogIntent>() {

    companion object {
        const val TAG = "RewardFileCardsDialogVM"
        const val STATUS_SHOW = 0
        const val STATUS_DISMISS = 1

        const val STATUS_NO_AD = 0//填充失败
        const val STATUS_NO_REWARD = 1//未达到奖励条件
        const val STATUS_AD_SUCCESS = 2//达到奖励条件

    }

    private val _status = MutableLiveData<Int>()
    val status: LiveData<Int> = _status

    private var ecpm: Int? = null// 广告的ecpm

    private var taskId: Int? = null
    private var taskAction: Int? = null


    private val _afterAwardVideoTaskVo = MutableLiveData<Pair<Boolean,CardDataBean?>>()
    val afterAwardVideoTaskVo: LiveData<Pair<Boolean,CardDataBean?>?> = _afterAwardVideoTaskVo

    private val _cardDataBean = MutableLiveData<CardDataBean>()
    val cardDataBean: LiveData<CardDataBean> = _cardDataBean

    //翻卡看视频结果
    private val _callRewardAd = MutableLiveData<Int>()
    val callRewardAd: LiveData<Int> = _callRewardAd


    //翻卡领奖励请求1303后返回在看一个广告数据
    private val _afterAwardDataBean = MutableLiveData<CardDataBean>()
    val afterAwardDataBean: LiveData<CardDataBean> = _afterAwardDataBean

    //在看一个广告的结果
    private val _afterCallRewardAd = MutableLiveData<Boolean>()
    val afterCallRewardAd: LiveData<Boolean> = _afterCallRewardAd

    fun callRewardAd(activity: Activity?, config: CardDataBean?) {
        if (activity == null || config == null) {
            LogUtil.d(TAG, "callRewardVideo activity or config is null")
            ToastManager.showToast("请重试", 3000)
            return
        }
        taskId = config.taskId
        taskAction = config.taskAction
        if (config.adConfExt?.adInterstitialId.isNullOrEmpty()) {
            // 纯激励
            LogUtil.d(TAG, "callRewardVideo 纯激励视频")
            showRewardOnly(activity, config)
        } else {
            // 混合竞价
            LogUtil.d(TAG, "callRewardVideo 混合竞价")
            val loadConfigs = listOf(
                AdLoadConfig(
                    AdType.REWARD,
                    config.adPositionId?:"",
                    needPreload = config.adPositionId == TaskUtils.rewardId,//是否预加载要和保存的激励预加载的soltId对比一下
                    preLoadNum = TaskUtils.rewardNum,
                    loadTimeOut = config.getAdLoading(),
                ),
                AdLoadConfig(
                    AdType.INTERSTITIAL,
                    config.adConfExt?.adInterstitialId ?: "",
                    needPreload = config.adConfExt?.adInterstitialId == TaskUtils.interstitialId,
                    preLoadNum = TaskUtils.interstitialNum,
                    loadTimeOut = config.getAdLoading(),
                    bgTipText = config.adConfExt?.mixbiddingAwardDoc,
                    isNeedInterstitialBg = config.adConfExt?.isNeedInterstitialBg(),
                )
            )
            showMixed(activity, config, loadConfigs)
        }
    }


    private fun showRewardOnly(activity: Activity, config: CardDataBean) {
        invokeShow(
            activity = activity,
            adConfig = config,
            loadConfigs = null,
            trackParams = null,
            mixTimeout = 0L
        )
    }

    private fun showMixed(
        activity: Activity,
        config: CardDataBean,
        loadConfigs: List<AdLoadConfig>
    ) {
        invokeShow(
            activity = activity,
            adConfig = config,
            loadConfigs = loadConfigs,
            trackParams = null,
            mixTimeout = config.adConfExt?.mixbiddingTs ?: 0L
        )
    }

    private fun invokeShow(
        activity: Activity,
        adConfig: CardDataBean,
        loadConfigs: List<AdLoadConfig>?,
        trackParams: Map<String, Any?>?,
        mixTimeout: Long? = 0L,
    ) {
        try {
            val welfareUserClickLoadMillis = System.currentTimeMillis() // 记录用户点击开始时间
            val showCallback = object : DzAdShowCallback {
                override fun onStartLoad() {
                    _status.value = STATUS_SHOW
                    LogUtil.d(TAG, "callRewardVideo onStartLoad")
                }

                override fun onShowSuccess(eCpm: Int?, reqId: String) {
                    LogUtil.d(TAG, "onShowSuccess cm=$eCpm")
                    _status.value = STATUS_DISMISS
                    ecpm = eCpm ?: 0
                    isHidePopUp = true
                    _callRewardAd.value = STATUS_AD_SUCCESS
                    if (adConfig.isAfterAwardVideoTask) {
                        //再看一个 领奖
                        requestAfterAwardVideo(
                            adConfig.taskId,
                            adConfig.taskAction,
                            adConfig.awardVideoToken,
                            reqId,
                        )
                    } else {
                        //翻卡 领奖
                        requestCardData(adConfig.taskId, adConfig.taskAction, 1) // 请求翻卡数据
                    }
                }

                override fun onShowError(code: Int, msg: String) {
                    _status.value = STATUS_DISMISS
                    LogUtil.d(TAG, "callRewardVideo onShowError code=$code msg=$msg")
                    if (adConfig.isAfterAwardVideoTask) {
                        // 在看一个广告任务
                        if (code == AD_REWARD_FAILED) {//广告未观看完
                            _afterCallRewardAd.value = true // 设置在看一个广告的结果为失败
                        } else {
                            ToastManager.showToast("暂无可用视频，请稍后重试", 3000)
                        }
                    } else {
                        if (code == AD_REWARD_FAILED) {//广告未观看完
                            _callRewardAd.value = STATUS_NO_REWARD
                        } else {
                            _callRewardAd.value = STATUS_NO_AD
                        }
                    }

                }

                override fun onShowing(ecpmCent: Int?) {
                    _status.value = STATUS_DISMISS
                    LogUtil.d(TAG, "TaskUtils callRewardVideo onShowing")
                }

                override fun onGetReportParams(): Map<String, Any> {
                    return mapOf<String, Any>(AdConstant.WELFARE_USER_CLICK_LOAD_MILLIS to welfareUserClickLoadMillis)
                }
            }

            LogUtil.d(TAG, "callRewardVideo 用户点击")
            if (loadConfigs == null) {
                // 纯激励
                LogUtil.d(
                    TAG,
                    "invokeShow 纯激励 "
                )
                val needPreload =
                    TaskUtils.rewardId.equals(adConfig.adPositionId) && TaskUtils.rewardNum > 0
                AdMS.get()?.showAd(
                    activity = activity,
                    type = AdTE.TYPE_GET_WELFARE_COINS,
                    adId = adConfig.adPositionId?:"",
                    blockConfigId = null,
                    commonTrackParam = null,
                    showCallback = showCallback,
                    timeout = adConfig.getAdLoading(),
                    needPreload = needPreload,
                    needPreNum = TaskUtils.rewardNum,
                    position = getAdPosition(adConfig),
                )
            } else {
                // 混合
                LogUtil.d(TAG, "invokeShow 混合")
                AdMS.get()?.mixAdShow(
                    activity = activity,
                    configList = loadConfigs.toMutableList(),
                    blockConfigId = null,
                    commonTrackParam = null,
                    showCallback = showCallback,
                    loadTimeout = adConfig.getAdLoading(),
                    mixTimeout = mixTimeout,
                    position = getAdPosition(adConfig),
                )
            }
        } catch (e: Exception) {
            e.printStackTrace()
            LogUtil.d(TAG, "callRewardVideo异常：${e.message}")
        }
    }

    private fun getAdPosition(adConfig: CardDataBean): Int {
        // 获取广告位位置
        return if (adConfig.isAfterAwardVideoTask) {
            AdTE.WELFARE_CARD_REWARD//暂定都是155
        } else {
            AdTE.WELFARE_CARD_REWARD
        }
    }

    //翻卡请求1302接口
    fun requestCardData(taskId: Int?, taskAction: Int?, adType: Int) {
        DzLog.d(
            TAG,
            "requestCardData taskId=$taskId  taskAction=$taskAction adType=$adType ecpm=$ecpm"
        )
        if (taskId == null || taskAction == null) {
            DzLog.e(TAG, "requestCardData taskId or taskAction is null")
            return
        }
        viewModelScope.launch(Dispatchers.IO) {
            WebNetWork.get()
                .webTaskReport()
                .addParams(
                    ecpm = ecpm,
                    taskAction = taskAction,
                    taskId = taskId,
                    adType = adType
                )
                .onStart {
                    _status.value = STATUS_SHOW
                    DzLog.d(TAG, "requestCardData onStart")
                }.onResponse { response ->
                    LogUtil.d("打印","1302接口：翻卡请求1302接口： taskAction=${taskAction}  it=${response.data}")
                    DzLog.d(TAG, "requestCardData response=${response.data}")
                    ecpm = null // 重置ecpm
                    _status.value = STATUS_DISMISS
                    response.data?.apply {
                        if (taskVo != null) {
                            //请求成功
                            taskVo?.let {
                                _cardDataBean.value = it
                            }
                        } else {
                            //请求失败
                            ToastManager.showToast("请重试", 3000)
                        }
                    }
                }.onError {
                    _status.value = STATUS_DISMISS
                    DzLog.d(TAG, "requestCardData onError")
                }.doRequest()
        }
    }


    /**
     * 领取翻卡奖励
     * */
    fun requestReward(taskId: Int?, taskAction: Int?) {
        DzLog.d(TAG, "requestReward taskId=$taskId  taskAction=$taskAction")
        if (taskId == null || taskAction == null) {
            DzLog.e(TAG, "requestCardData taskId or taskAction is null")
            return
        }
        viewModelScope.launch(Dispatchers.IO) {
            WebNetWork.get()
                .webCardTaskReward()
                .addParams(
                    taskAction = taskAction,
                    taskId = taskId,
                )
                .onStart {
                    _status.value = STATUS_SHOW
                    DzLog.d(TAG, "welfareRewardonStart")
                }.onResponse { response ->
                    DzLog.d(TAG, "welfareReward response=${response.data}")
                    response.data?.apply {
                        _status.value = STATUS_DISMISS
                        if (code == 0) {
                            //领取成功
//                            ToastManager.showToast(message ?: "领取成功", 3000)
                            afterAwardVideoTaskVo?.let {
                                it.awardVideoToken = awardVideoToken // 设置在看一个广告的token
                                it.isAfterAwardVideoTask = true // 设置为在看一个广告任务
                                it.urgeCoinsType = urgeCoinsType // 设置在看一个广告的类型
                            }
                            _afterAwardVideoTaskVo.value =
                                Pair(afterAwardVideoTaskVo != null, afterAwardVideoTaskVo)
                        } else {
                            //领取失败
                            ToastManager.showToast("领取失败", 3000)
                        }
                    }
                }.onError {
                    _status.value = STATUS_DISMISS
                    DzLog.d(TAG, "welfareReward onError")
                }.doRequest()
        }

    }

    /**
     * 领取在看一个广告的奖励
     * */
    fun requestAfterAwardVideo(
        taskId: Int?,
        taskAction: Int?,
        awardVideoToken: String? = null,
        requestId: String,
    ) {
        DzLog.d(TAG, "requestAfterAwardVideo taskId=$taskId ecpm=$ecpm  requestId=$requestId")
        if (taskId == null || taskAction == null) {
            DzLog.e(TAG, "requestAfterAwardVideo taskId or taskAction is null")
            return
        }
        viewModelScope.launch(Dispatchers.IO) {
            WebNetWork.get()
                .webCardTaskReward()
                .addParams(
                    ecpm = ecpm,
                    taskAction = taskAction,
                    taskId = taskId,
                    awardVideoToken = awardVideoToken,
                    adRequestId = requestId,
                )
                .onStart {
                    DzLog.d(TAG, "welfareAfterAwardVideo onStart")
                }.onResponse { response ->
                    DzLog.d(TAG, "welfareAfterAwardVideo response=${response.data}")
                    ecpm = null // 重置ecpm
                    response.data?.apply {
                        if (code == 0) {
                            //领取成功
                            ToastManager.showToast(message ?: "领取成功", 3000)
                            _afterCallRewardAd.value = true
                        } else {
                            //领取失败
                            ToastManager.showToast(message ?: "请重试", 3000)
                            _afterCallRewardAd.value = false
                        }
                    }
                }.onError {
                    DzLog.d(TAG, "welfareAfterAwardVideo onError")
                }.doRequest()
        }
    }


}