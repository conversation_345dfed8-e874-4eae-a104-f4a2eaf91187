package com.dz.business.web.util

import android.os.Handler
import android.os.Looper
import android.webkit.WebView
import com.dz.business.base.data.BBaseKV
import com.dz.business.base.splash.OptSceneType
import com.dz.business.base.splash.SplashMS
import com.dz.business.base.track.TrackUtil
import com.dz.business.base.ui.web.WebConst
import com.dz.business.base.ui.web.WebManager
import com.dz.business.base.web.WebMR
import com.dz.business.track.events.DzTrackEvents
import com.dz.business.track.tracker.Tracker
import com.dz.business.web.data.ExternalDataBean
import com.dz.business.web.network.WebNetWork
import com.dz.business.web.ui.page.WebActivity.Companion.TAG_WEB
import com.dz.business.web.util.TaskUtils.callWebViewJsMethod
import com.dz.business.web.util.TaskUtils.jsonToHashMap
import com.dz.foundation.base.utils.LogUtil
import com.dz.foundation.network.onError
import com.dz.foundation.network.onResponse
import com.dz.platform.common.base.ui.dialog.PDialogComponent
import com.dz.platform.common.router.onDismiss
import com.dz.platform.common.router.onShow
import com.dz.platform.common.toast.ToastManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import org.json.JSONException
import org.json.JSONObject

object ExternalTaskUtils {
    private const val TAG = "ExternalTaskUtils"
    const val ACTION = "authCallback"
    const val ACTION_TOKEN = "tokenCallback"
    var showPrivacyDialogDialog: PDialogComponent<*>? = null
    fun showAuthAlert(
        webManager: WebManager,
        externalData: ExternalDataBean? = null
    ) {
        try {
            //只有三方调用
            LogUtil.d(
                TAG,
                "showAuthAlert: externalData = $externalData   "
            )
            //三方调用
            if(showPrivacyDialogDialog == null) {
                val mainScope = MainScope()
                mainScope.launch(Dispatchers.Main) {
                    WebMR.get().showPrivacyDialog().apply {
                        img = externalData?.shopVo?.shopBgUrl
                    }.onShow {
                        trackExposure(TaskUtils.origin)
                        showPrivacyDialogDialog = it
                    }.onSure {
                        reportPrivacy(webManager)
                    }.onClose {
                        callBackForJs(ACTION, false, webManager)
                    }.onDismiss {
                        showPrivacyDialogDialog = null
                    }.start()
                }
            }



        } catch (e: JSONException) {
            LogUtil.e(TAG, "showAuthAlert: e = $e ")
            e.printStackTrace()
        }

    }

    private fun trackExposure(origin: String) {
        DzTrackEvents.get().popupShow().popupName("兑换商城").origin(origin).contentSource("支付页面").track()
    }


    private fun reportPrivacy( webManager: WebManager) {
        WebNetWork.get().authCode().addParams(1).onResponse {
            LogUtil.d(TAG_WEB, "ExternalTaskUtils reportPrivacy成功  data : $it")
            showPrivacyDialogDialog?.dismiss()
            callBackForJs(ACTION, true, webManager)
        }.onError {
            LogUtil.d(TAG_WEB, "reportPrivacy失败 : $it")
        }.doRequest()
    }


    fun callBackForJsExternal(
        webView: WebView,
        isAuth: Boolean = false
    ) {
        callWebViewJsMethod(
            webView,
            WebConst.JS_METHOD_NATIVE_CALL_BACK,
            formatJson(ACTION, isAuth = isAuth)
        )
    }


    fun shopTrack(json: String) {
        LogUtil.d(TAG, "ExternalTaskUtils (sensorTrack) json:$json")
        val map: HashMap<String, String>? = jsonToHashMap(json)
        val eventName: String? = map?.get("eventName")
        val data: String? = map?.get("data")
        try {
            if (data != null && eventName != null) {
                Tracker.trackToSensor(eventName, JSONObject(data))
            }
        } catch (e: JSONException) {
            e.printStackTrace()
        }
    }

    fun getTempToken(
        webManager: WebManager
    ) {
        WebNetWork.get().getTempToken()
            .onResponse {
                it.data?.tempToken?.let { tempToken ->
                    LogUtil.d(TAG_WEB, "ExternalTaskUtils getTempToken  data : $tempToken")
                    callBackForJsToken(ACTION_TOKEN, tempToken, webManager = webManager)
                }

            }.onError {
                LogUtil.d(TAG_WEB, "ExternalTaskUtils getTempToken失败 : $it")
            }
            .doRequest()

        return
    }

    fun notifyShumeng(
    ) {
        LogUtil.d(TAG_WEB, "ExternalTaskUtils notifyShumeng  BBaseKV.shuMengId : ${BBaseKV.shuMengId}")
        WebNetWork.get().notifyShumeng().addParams().onResponse {
            LogUtil.d(TAG_WEB, "ExternalTaskUtils notifyShumeng  data : $it")
        }.onError {
            LogUtil.d(TAG_WEB, "ExternalTaskUtils notifyShumeng失败 : $it")
        }.doRequest()
    }

    private fun callBackForJsToken(
        action: String?,
        tempToken: String,
        webManager: WebManager,
    ) {
        LogUtil.d(
            TAG,
            "page:" + webManager.activity?.localClassName + "\n action:" + action + "\n tempToken:" + tempToken
        )
        callWebViewJsMethod(
            webManager.webViewComponent.getWebView(),
            WebConst.JS_METHOD_NATIVE_CALL_BACK,
            formatJsonToken(action, tempToken)
        )
    }

    private fun formatJsonToken(
        action: String?,
        tempToken: String,
    ): String {
        val jsonObject = JSONObject()
        try {
            jsonObject.put("action", action)
            val params = JSONObject()
            params.put("tempToken", tempToken)
            jsonObject.put("params", params)
            LogUtil.d(
                TAG,
                "formatJson:  jsonObject = $jsonObject"
            )
        } catch (e: JSONException) {
            e.printStackTrace()
        }
        return jsonObject.toString()
    }

    private fun callBackForJs(
        action: String?,
        isAuth: Boolean,
        webManager: WebManager,
    ) {
        LogUtil.d(
            TAG,
            "page:" + webManager.activity?.localClassName + "\n action:" + action + "\n isAuth:" + isAuth
        )
        callWebViewJsMethod(
            webManager.webViewComponent.getWebView(),
            WebConst.JS_METHOD_NATIVE_CALL_BACK,
            formatJson(action, isAuth)
        )
    }

    private fun formatJson(
        action: String?,
        isAuth: Boolean,
    ): String {
        val jsonObject = JSONObject()
        try {
            jsonObject.put("action", action)
            val params = JSONObject()
            params.put("isAuth", isAuth)
            jsonObject.put("params", params)
            LogUtil.d(
                TAG,
                "formatJson:  jsonObject = $jsonObject"
            )
        } catch (e: JSONException) {
            e.printStackTrace()
        }
        return jsonObject.toString()
    }


}