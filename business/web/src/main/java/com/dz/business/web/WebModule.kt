package com.dz.business.web

import com.dz.business.base.web.WebMR
import com.dz.business.base.web.WebMS
import com.dz.business.web.network.WelfareTaskInterceptor
import com.dz.business.web.ui.dialog.RewardFlipCardsDialog
import com.dz.business.web.ui.dialog.ShareCodeWXDialog
import com.dz.business.web.ui.dialog.ShowPrivacyDialog
import com.dz.business.web.ui.page.WebActivity
import com.dz.business.web.ui.page.WelfareActivity
import com.dz.foundation.base.module.LibModule
import com.dz.foundation.base.service.DzServiceManager
import com.dz.foundation.network.DzNetWorkManager
import com.dz.foundation.router.registerTarget

/**
 *@Author: shidz
 *@Date: 2022/11/13 22:51
 *@Description: web 模块承接 H5 页面功能
 *@Version:1.0
 */
class WebModule : LibModule() {
    override fun onCreate() {
        WebMR.get().run {
            webViewPage().registerTarget(WebActivity::class.java)
            welfarePage().registerTarget(WelfareActivity::class.java)
            shareCodeWXDialog().registerTarget(ShareCodeWXDialog::class.java)
            flipCardDialog().registerTarget(RewardFlipCardsDialog::class.java)
            showPrivacyDialog().registerTarget(ShowPrivacyDialog::class.java)
        }

        DzServiceManager.registerService(WebMS::class.java, WebMSImpl::class.java)

        // 网络拦截器
        DzNetWorkManager.addInterceptor(WelfareTaskInterceptor())
    }
}