package com.dz.business.web

import com.dz.business.base.SpeedUtil
import com.dz.business.base.ui.web.jsinterface.DzJsBridge
import com.dz.business.base.ui.web.jsinterface.DzJsManager
import com.dz.business.web.jsh.CommonJSH
import com.dz.business.web.jsh.ExternalWebInterface
import com.dz.business.web.jsh.LogJSH
import com.dz.business.web.jsh.NetworkJSH
import com.dz.business.web.jsh.RechargeJSH
import com.dz.business.web.jsh.WebInterface
import com.dz.foundation.base.utils.LogUtil

/**
 *@Author: zhanggy
 *@Date: 2024-02-19
 *@Description:
 *@Version:1.0
 */
fun initWebView() {
    LogUtil.d("StartUp", "initWebView start")
    val startTime = System.currentTimeMillis()

    //注册js交互处理
    DzJsManager.getInstance().addJsObj(DzJsBridge::class.java)
    DzJsManager.getInstance().addJsObj(WebInterface::class.java)
    DzJsManager.getInstance().addJSInvokeHandler(LogJSH::class.java)
    DzJsManager.getInstance().addJSInvokeHandler(CommonJSH::class.java)
    DzJsManager.getInstance().addJSInvokeHandler(NetworkJSH::class.java)
    DzJsManager.getInstance().addJSInvokeHandler(RechargeJSH::class.java)
    LogUtil.d(SpeedUtil.TAG, "initWebView 耗时:${System.currentTimeMillis() - startTime}. Thread:${Thread.currentThread().name}")
}

fun releaseWebView() {
    LogUtil.d("StartUp", "releaseWebView start")

    // 取消 JS 对象注册
    DzJsManager.getInstance().removeJsObj(DzJsBridge::class.java)
    DzJsManager.getInstance().removeJsObj(WebInterface::class.java)

    // 取消 JS Handler 注册
    DzJsManager.getInstance().removeJSInvokeHandler(LogJSH::class.java)
    DzJsManager.getInstance().removeJSInvokeHandler(CommonJSH::class.java)
    DzJsManager.getInstance().removeJSInvokeHandler(NetworkJSH::class.java)
    DzJsManager.getInstance().removeJSInvokeHandler(RechargeJSH::class.java)

    LogUtil.d("StartUp", "releaseWebView complete. Thread:${Thread.currentThread().name}")
}


fun initExternalWebView() {
    LogUtil.d("StartUp", "initExternalWebView start")
    val startTime = System.currentTimeMillis()

    //注册与三方js交互处理
    DzJsManager.getInstance().addJsObj(ExternalWebInterface::class.java)
    LogUtil.d(SpeedUtil.TAG, "initExternalWebView 耗时:${System.currentTimeMillis() - startTime}. Thread:${Thread.currentThread().name}")
}