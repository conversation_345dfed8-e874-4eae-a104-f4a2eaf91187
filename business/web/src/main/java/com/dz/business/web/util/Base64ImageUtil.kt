import android.app.Activity
import android.content.ContentValues
import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.provider.MediaStore
import android.util.Base64
import com.dz.business.base.personal.PersonalMR
import com.dz.business.base.personal.PersonalMS
import com.dz.business.base.utils.RequestPermissionUtil
import com.dz.business.base.welfare.WelfareMC.failToast
import com.dz.business.web.R
import com.dz.foundation.base.utils.LogUtil
import com.dz.foundation.base.utils.PermissionUtils
import com.dz.platform.common.toast.ToastManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import java.io.ByteArrayInputStream
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.io.OutputStream

class Base64ImageUtil {

    /**
     * 将 Base64 编码的字符串保存为图片文件
     *
     * @param context 上下文，用于保存图片
     * @param base64String Base64 编码的字符串
     * @param fileName 文件名，不带扩展名
     * @return 保存后的图片 URI，失败时返回 null
     */
    fun saveBase64ToImage(context: Context, base64String: String, fileName: String): Uri? {
        val decodedBytes = try {
            Base64.decode(base64String, Base64.DEFAULT)
        } catch (e: IllegalArgumentException) {
            e.printStackTrace()
            LogUtil.e("Base64ImageUtil", "Base64 解码失败")
            ToastManager.showToast("保存失败，请稍后重试")
            return null
        }

        val inputStream = ByteArrayInputStream(decodedBytes)
        val bitmap = BitmapFactory.decodeStream(inputStream) ?: return null

        val filename = "$fileName.jpg"
        var uri: Uri? = null

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            // Android 10 及以上版本
            val values = ContentValues().apply {
                put(MediaStore.Images.Media.DISPLAY_NAME, filename)
                put(MediaStore.Images.Media.MIME_TYPE, "image/jpeg")
                put(MediaStore.Images.Media.RELATIVE_PATH, "Pictures/MyApp")
            }
            kotlin.runCatching {
                uri = context.contentResolver.insert(
                    MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
                    values
                )
            }.onFailure { it.printStackTrace() }

            if (uri == null) {
                LogUtil.e("Base64ImageUtil", "权限问题：Failed to insert image into MediaStore")
                return null
            }
            saveImageToStream(context, bitmap, uri!!)
        } else {
            // Android 10 以下版本
            val directory = File(
                Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES),
                "MyApp"
            )
            if (!directory.exists() && !directory.mkdirs()) {
                LogUtil.e(
                    "Base64ImageUtil",
                    "权限问题：Failed to create directory: ${directory.absolutePath}"
                )
                return null
            }

            val file = File(directory, filename)
            try {
                FileOutputStream(file).use { fos ->
                    bitmap.compress(Bitmap.CompressFormat.JPEG, 100, fos)
                    fos.flush()
                }
                uri = Uri.fromFile(file)
            } catch (e: IOException) {
                e.printStackTrace()
                return null
            }
        }
        return uri
    }

    private fun saveImageToStream(context: Context, bitmap: Bitmap, uri: Uri) {
        context.contentResolver.openOutputStream(uri)?.use { outputStream ->
            if (!bitmap.compress(Bitmap.CompressFormat.JPEG, 100, outputStream)) {
                LogUtil.e("Base64ImageUtil", "权限问题：Failed to compress and save image")
            }
        }
    }

    fun checkAndSavePhoto(
        activity: Activity,
        context: Context,
        base64String: String,
        fileName: String
    ) {
        MainScope().launch(Dispatchers.Main) {
            PersonalMS.get()?.checkPhotoAndSendMessage(
                activity,
                context,
                base64String,
                fileName,
                listener = object : RequestPermissionUtil.OnPermissionRequest {
                    override fun onPermissionGranted() {
                        saveBase64ToImage(context, base64String, fileName)
                        ToastManager.showToast("成功保存到相册")
                    }

                    override fun onPermissionDenied() {
                        PersonalMS.get()?.setFeedbackPermission(true)
                    }

                    override fun onCustomApply() {

                        PersonalMR.get().permissionDialog()
                            .apply {
                                title =
                                    activity.getString(R.string.bbase_request_store_permission)
                                content =
                                    activity.getString(R.string.bbase_request_store_permission_dec)
                            }.start()

                    }

                })

        }
    }


}
