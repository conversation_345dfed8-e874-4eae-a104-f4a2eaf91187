package com.dz.business.web

import android.app.Activity
import androidx.fragment.app.Fragment
import com.dz.business.base.BBaseMC
import com.dz.business.base.data.bean.PreloadAdVo
import com.dz.business.base.data.bean.TechnologyConfig
import com.dz.business.base.data.bean.TransJumpOutPushCon
import com.dz.business.base.utils.GsonUtil
import com.dz.business.base.web.WebMS
import com.dz.business.base.web.data.WelfareMonitorResult
import com.dz.business.base.welfare.WelfareMS2
import com.dz.business.track.base.addParam
import com.dz.business.track.events.DzTrackEvents
import com.dz.business.web.data.WebKV
import com.dz.business.web.manager.WelfareADPreloadManager
import com.dz.business.web.monitor.WelfareMonitor
import com.dz.business.web.ui.page.WelfareFragment
import com.dz.business.web.ui.page.WelfareTabFragment
import com.dz.business.web.util.ExternalTaskUtils
import com.dz.business.web.util.GoldPushManager
import com.dz.business.web.util.RequestCalendarUtil
import com.dz.business.web.util.TransJumpOutPush
import com.dz.foundation.base.utils.LogUtil

/**
 *@Author: zhanggy
 *@Date: 2023-05-18
 *@Description:
 *@Version:1.0
 */
class WebMSImpl : WebMS {

    override fun saveTecConfig(config: TechnologyConfig) {
        WebKV.welfareMonitorEnable = config.welfareMonitorEnable
        // 福利页白屏重试次数
        WebKV.welfarePageRetryCount = config.welfarePageRetryCount
    }

    override fun getWelfareFragment(): Fragment {
        if (WelfareMS2.get()?.hasWelfareMall() == true) {
            BBaseMC.hasWelfareMall = true
            return WelfareTabFragment()
        }
        BBaseMC.hasWelfareMall = false
        return WelfareFragment()
    }

    override fun saveConfig(config: TransJumpOutPushCon?) {
        if (config != null) {
            TransJumpOutPush.saveConfig(config)
        }
    }

    override fun reportWelfareMonitorResult(monitorResult: WelfareMonitorResult?) {
        runCatching {
            val result = monitorResult ?: GsonUtil.fromJson(
                WebKV.welfareMonitorResult,
                WelfareMonitorResult::class.java
            )
            LogUtil.d(WelfareMonitor.TAG, "reportWelfareMonitorResult:$result")
            if (result == null) {
                LogUtil.d(WelfareMonitor.TAG, "上报失败，本地存储为空")
                return
            }
            // 检查极大值
            if (hasExtremeValue(result)) {
                LogUtil.e(WelfareMonitor.TAG, "取消上报，出现极大值！$result")
                return
            }
            DzTrackEvents.get().pageOpenDuration().apply {
                addParam("pageName", result.pageName)
                addParam("Tag", result.tag)
                addParam("ApiPort", result.apiPort)
                addParam("WebView", result.webView)
                addParam("network", result.network)
                addParam("Duration", result.duration)
                addParam("WebViewDuration", result.webviewDuration)
                addParam("render", result.render)
                addParam("Result", result.result)
                addParam("FailReason", result.failReason)
                addParam("message", result.message)
                addParam("IsFirstActive", result.isFirstActive)
                addParam("IsPreRendered", result.isPreRendered)
                addParam("Scene", result.scene)
            }.track()
        }.onFailure { e ->
            e.printStackTrace()
            LogUtil.e(WelfareMonitor.TAG, "上报失败。异常:${e.message}")
        }
    }

    /**
     * 判断WelfareMonitorResult中Long类型的参数是否出现了极大值
     * @param result 需要检测的WelfareMonitorResult对象
     * @param maxValueMillis 极大值阈值，默认为1小时的毫秒数
     * @return 是否存在极大值，true表示存在，false表示不存在
     */
    private fun hasExtremeValue(
        result: WelfareMonitorResult,
        maxValueMillis: Long = 3600000L // 1小时 = 3600000毫秒
    ): Boolean {
        // 使用序列操作处理多个字段的检测
        return sequenceOf(
            result.webView to "webView",
            result.network to "network",
            result.duration to "duration",
            result.render to "render",
            result.webviewDuration to "webviewLoadDuration"
        ).firstOrNull { (value, name) ->
            value?.takeIf { it > maxValueMillis }?.also {
                LogUtil.d(WelfareMonitor.TAG, "极大值检测：${name}值 $it 超过阈值")
            } != null
        } != null
    }

    override fun setCalendarPermission(
        activity: Activity, mainTitle: String?,
        subTitle: String?,
        startTime: Int?,
        duration: Int?,
        alertTime: Int?
    ) {
        RequestCalendarUtil.requestAndSetCalendar(
            activity,
            mainTitle,
            subTitle,
            startTime,
            duration,
            alertTime
        )
    }

    override fun checkCalendar(activity: Activity): Boolean {
        return RequestCalendarUtil.isCalendarEventExist(activity)
    }

    override fun deleteCalendarEvent(activity: Activity) {
        RequestCalendarUtil.deleteCalendarEvent(activity)
    }

    override fun testRequest(type: Int) {
        if (type == 1) {
//            ExternalTaskUtils.getTempToken()
        } else {
            ExternalTaskUtils.notifyShumeng()
        }
    }



    override fun preloadWelfareRewardAd(fromType: Int) {
        WelfareADPreloadManager.startPreload(fromType)
    }

    override fun cancelPreloadWelfareRewardAd(fromType: Int) {
        WelfareADPreloadManager.cancelPreload(fromType)
    }

    override fun isCancelPreloadInPlayDetail(): Boolean {
       return WelfareADPreloadManager.isCancelPreloadInPlayDetail()
    }

    override fun initPreloadManager(tag: String) {
        WelfareADPreloadManager.init(tag)
    }

    override fun destroyPreloadManager() {
        WelfareADPreloadManager.destroy()
    }


    override fun saveDeepLinkConfig(jobid: String?) {
        GoldPushManager.saveDeepLinkFromLaunch(jobid)
    }
}