package com.dz.business.web.util

import android.app.Activity
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import com.dz.business.base.BBaseMC
import com.dz.business.base.data.BBaseKV
import com.dz.business.base.data.bean.UserTacticsVo
import com.dz.business.track.events.DzTrackEvents
import com.dz.business.track.events.sensor.AdTE
import com.dz.business.web.R
import com.dz.foundation.base.utils.LogUtil
import com.dz.foundation.base.utils.RandomUtils
import com.dz.foundation.base.utils.ScreenUtil
import com.dz.platform.ad.AdManager
import com.dz.platform.ad.callback.FeedAdCallback
import com.dz.platform.ad.data.BannerAdKV
import com.dz.platform.ad.sky.FeedAd
import com.dz.platform.ad.sky.SkyAd
import com.dz.platform.ad.sky.getShowStyles

/**
 * 福利页翻卡任务弹窗信息流
 * */
object BannerAdManager {

    private const val TAG = "BannerAdManager"

    private var adId: String = ""
    private var mWidthTemplate: Int = 0
    private var mHeightTemplate: Int = 0
    private var userTacticsVo: UserTacticsVo? = null

    private var preAdFeedAdCallback: BannerAdCallback? = null

    // 每个 Activity 的广告状态
    private val adStates = mutableMapOf<String, BannerAdState>()

    fun init(adId: String, userTacticsVo: UserTacticsVo?) {
        this.adId = adId
        this.userTacticsVo = userTacticsVo
    }

    private fun getKey(activity: Activity) = activity::class.java.simpleName

    private fun getState(activity: Activity): BannerAdState {
        return adStates.getOrPut(getKey(activity)) { BannerAdState() }
    }

    private fun clearState(activity: Activity) {
        getState(activity).ad?.destroy()
        adStates.remove(getKey(activity))
    }

    private fun preloadBannerAd(activity: Activity) {
        val state = getState(activity)
        if (adId.isEmpty()) {
            LogUtil.d(TAG, "广告id为空，预加载结束")
            return
        }

        val ad = state.ad
        if (ad != null && !ad.isShow && ad.isValid(activity)) {
            LogUtil.d(TAG, "已有可用广告，预加载跳过")
            return
        }

        if (state.isLoading) {
            LogUtil.d(TAG, "广告正在加载中，跳过")
            return
        }

        state.isLoading = true
        requestBannerAd(activity, object : FeedAdCallback {
            override fun onStartLoad(feedAd: FeedAd?) {}
            override fun onFeedSkyLoaded(feedAd: FeedAd?) {
                LogUtil.d(TAG, "preloadBannerAd: onFeedSkyLoaded")
                state.ad = feedAd
                state.isLoading = false
            }

            override fun onFail(feedAd: FeedAd?, message: String?, code: String?) {
                LogUtil.d(TAG, "preloadBannerAd: onFail message=$message code=$code")
                state.isLoading = false
            }

            override fun onShow(feedAd: FeedAd?) {
                LogUtil.d(TAG, "preloadBannerAd: onShow")
                preAdFeedAdCallback?.onAdShown(feedAd)
            }

            override fun onClick(feedAd: FeedAd?) {
                LogUtil.d(TAG, "preloadBannerAd: onClick")
                preAdFeedAdCallback?.onAdClicked(feedAd)
            }

            override fun onClose(feedAd: FeedAd?) {
                LogUtil.d(TAG, "preloadBannerAd: onClose")
                preAdFeedAdCallback?.onAdClosed(feedAd)
            }

            override fun onVideoStart(feedAd: FeedAd?) {}
            override fun onVideoComplete(feedAd: FeedAd?) {}
            override fun loadStart(sky: SkyAd?) {}
            override fun loadStatus(sky: SkyAd?) {}
        })
    }

    fun checkHaveCacheAd(activity: Activity?): Boolean {
        if (activity == null) {
            LogUtil.e(TAG, "loadAndShowBannerAd: activity is null")
            return false
        }
        val state = getState(activity)
        val cachedAd = state.ad
        LogUtil.d(TAG, "checkHaveAd: cachedAd=$cachedAd")
        if (cachedAd != null && !cachedAd.isShow && cachedAd.isValid(activity)) {
            LogUtil.d(TAG, "checkHaveAd: 使用缓存广告")
            return true
        } else {
            LogUtil.d(TAG, "checkHaveAd: 没有缓存广告，预加载新广告")
            preloadBannerAd(activity)
            return false
        }
    }

    fun loadAndShowBannerAd(
        activity: Activity?,
        adContainer: ViewGroup,
        adCallback: BannerAdCallback
    ) {
        if (activity == null) {
            LogUtil.e(TAG, "loadAndShowBannerAd: activity is null")
            return
        }
        val state = getState(activity)
        val cachedAd = state.ad

        if (cachedAd != null && !cachedAd.isShow && cachedAd.isValid(activity)) {
            LogUtil.d(TAG, "loadAndShow: 使用缓存广告")
            adCallback.onAdLoaded(cachedAd)
            preAdFeedAdCallback = adCallback
            showAdInternal(activity, adContainer, cachedAd, adCallback)
            return
        }

        if (state.isLoading) {
            LogUtil.d(TAG, "loadAndShow: 广告正在加载中，忽略请求")
            return
        }

        state.isLoading = true
        requestBannerAd(activity, object : FeedAdCallback {
            override fun onStartLoad(feedAd: FeedAd?) {

            }

            override fun onFeedSkyLoaded(feedAd: FeedAd?) {
                LogUtil.d(TAG, "loadAndShow: onFeedSkyLoaded")
                state.ad = feedAd
                state.isLoading = false
                showAdInternal(activity, adContainer, feedAd, adCallback)
                adCallback.onAdLoaded(feedAd)
            }

            override fun onFail(feedAd: FeedAd?, message: String?, code: String?) {
                LogUtil.d(TAG, "loadAndShow: onFail message=$message code=$code")
                state.isLoading = false
                adCallback.onAdFailed(feedAd, message, code)
                preloadBannerAd(activity)
            }

            override fun onShow(feedAd: FeedAd?) {
                LogUtil.d(TAG, "loadAndShow: onShow")
                adCallback.onAdShown(feedAd)
                preloadBannerAd(activity)
            }

            override fun onClick(feedAd: FeedAd?) {
                LogUtil.d(TAG, "loadAndShow: onClick")
                adCallback.onAdClicked(feedAd)
            }

            override fun onClose(feedAd: FeedAd?) {
                LogUtil.d(TAG, "loadAndShow: onClose")
                adCallback.onAdClosed(feedAd)
            }

            override fun onVideoStart(feedAd: FeedAd?) {

            }

            override fun onVideoComplete(feedAd: FeedAd?) {

            }

            override fun loadStart(sky: SkyAd?) {

            }

            override fun loadStatus(sky: SkyAd?) {
            }
        })
    }

    private fun showAdInternal(
        activity: Activity,
        container: ViewGroup,
        ad: FeedAd?,
        adCallback: BannerAdCallback?
    ) {
        if (ad == null || ad.isShow) {
            LogUtil.d(TAG, "showAdInternal: 无可用广告")
            return
        }

        try {
            val templateView = ad.mFeedAdHolder?.getTemplateView(activity)
            container.removeAllViews()
            templateView?.parent?.let {
                (it as? ViewGroup)?.removeView(templateView)
            }
            container.addView(templateView)
            ad.isShow = true
            ad.showTime = System.currentTimeMillis()
            adCallback?.onAdShown(ad)
        } catch (e: Exception) {
            ad.isShow = true
            container.visibility = View.INVISIBLE
            LogUtil.e(TAG, "showAdInternal: 广告展示异常 ${e.message}")
        }
    }

    private fun requestBannerAd(
        activity: Activity,
        adCallback: FeedAdCallback
    ) {
        val requestTime = System.currentTimeMillis()
        val requestId = "${BBaseKV.userId}_${requestTime}_${RandomUtils.getRandumNum(999, 100)}"

        mWidthTemplate = ScreenUtil.getScreenWidth()
        mHeightTemplate = ScreenUtil.dip2px(activity, BannerAdKV.bannerHeight)

        AdManager.loadFeedAd(
            activity = activity,
            widthImgDp = ScreenUtil.px2dipByDensity(
                activity,
                BBaseMC.originalDensity,
                mWidthTemplate
            ),
            heightImgDp = BannerAdKV.bannerHeight,
            widthTemplatePx = mWidthTemplate,
            heightTemplatePx = mHeightTemplate,
            feedAdDimen = null,
            adId = adId,
            blockConfigId = "",
            backgroundColor = ContextCompat.getColor(activity, R.color.common_FF0F0F0F),
            adDrawReqSeq = 0,
            isNightMode = false,
            isDrawAd = false,
            bookId = null,
            chapterId = null,
            physicalPosId = AdTE.WELFARE_CARD_FEED,
            requestId = requestId,
            videoMute = true,
            minPDReqEcpmYuan = -1.0,
            callback = object : FeedAdCallback {

                var loadedTime = 0L
                var showTime = 0L
                var playComplete = false
                var playTime = 0L

                override fun onStartLoad(feedAd: FeedAd?) {
                    adCallback.onStartLoad(feedAd)
                }

                override fun onFeedSkyLoaded(feedAd: FeedAd?) {
                    getState(activity).apply {
                        ad = feedAd
                        isLoading = false
                    }
                    loadedTime = System.currentTimeMillis()
                    DzTrackEvents.get().adResponse().setAdInfo(feedAd)
                        .timeSpent(loadedTime - requestTime)
                        .showStyles(feedAd?.getShowStyles())
                        .commonTrack(feedAd?.mRequestId ?: requestId)
                    adCallback.onFeedSkyLoaded(feedAd)
                }

                override fun onFail(feedAd: FeedAd?, message: String?, code: String?) {
                    getState(activity).isLoading = false
                    DzTrackEvents.get().adResponse().setAdInfo(feedAd)
                        .adErrorCode("$message$code")
                        .timeSpent(System.currentTimeMillis() - requestTime)
                        .commonTrack(feedAd?.mRequestId ?: requestId)
                    adCallback.onFail(feedAd, message, code)
                }

                override fun onShow(feedAd: FeedAd?) {
                    showTime = System.currentTimeMillis()
                    DzTrackEvents.get().adShow().setHostInfo(feedAd).setAdInfo(feedAd)
                        .adVideoIsEnd(playComplete.toString())
                        .adVideoplayTime(System.currentTimeMillis() - playTime)
                        .adVideoEndTime(System.currentTimeMillis() - showTime)
                        .timeSpent(System.currentTimeMillis() - loadedTime)
                        .showStyles(feedAd?.getShowStyles())
                        .commonTrack(feedAd?.mRequestId ?: requestId)
                    adCallback.onShow(feedAd)
                }

                override fun onClick(feedAd: FeedAd?) {
                    DzTrackEvents.get().adClick().setAdInfo(feedAd)
                        .adVideoIsEnd(playComplete.toString())
                        .adVideoplayTime(System.currentTimeMillis() - playTime)
                        .adShowTime(System.currentTimeMillis() - showTime)
                        .adVideoEndTime(System.currentTimeMillis() - showTime)
                        .timeSpent(System.currentTimeMillis() - showTime)
                        .showStyles(feedAd?.getShowStyles())
                        .commonTrack(feedAd?.mRequestId ?: requestId)
                    adCallback.onClick(feedAd)
                }

                override fun onClose(feedAd: FeedAd?) {
                    DzTrackEvents.get().adClose().setAdInfo(feedAd)
                        .adVideoIsEnd(playComplete.toString())
                        .adVideoplayTime(System.currentTimeMillis() - playTime)
                        .adShowTime(System.currentTimeMillis() - showTime)
                        .adVideoEndTime(System.currentTimeMillis() - showTime)
                        .timeSpent(System.currentTimeMillis() - showTime)
                        .commonTrack(feedAd?.mRequestId ?: requestId)
                    adCallback.onClose(feedAd)
                }

                override fun onVideoStart(feedAd: FeedAd?) {
                    playTime = System.currentTimeMillis()
                    adCallback.onVideoStart(feedAd)
                }

                override fun onVideoComplete(feedAd: FeedAd?) {
                    playComplete = true
                    adCallback.onVideoComplete(feedAd)
                }

                override fun loadStart(sky: SkyAd?) {}
                override fun loadStatus(sky: SkyAd?) {}

                private fun AdTE.commonTrack(requestId: String) {
                    track(requestId)
                }
            }
        )
    }

    fun AdTE.track(
        requestId: String,
    ) {
        requestId(requestId)
            .adPosition(AdTE.WELFARE_CARD_FEED)
            .adSotId(adId)
            .adType(AdTE.TYPE_FEED)
            .userTacticInfo(userTacticsVo)
            .track()
    }

    fun onDestroy(activity: Activity) {
        LogUtil.d(TAG, "onDestroy: ${getKey(activity)}")
        clearState(activity)
    }

    fun cleanAdCache() {
        adStates.values.forEach { it.ad?.destroy() }
        adStates.clear()
    }

    private data class BannerAdState(
        var ad: FeedAd? = null,
        var isLoading: Boolean = false
    )

    interface BannerAdCallback {
        fun onAdLoaded(ad: FeedAd?)
        fun onAdFailed(feedAd: FeedAd?, message: String?, code: String?)
        fun onAdClicked(ad: FeedAd?)
        fun onAdClosed(ad: FeedAd?)
        fun onAdShown(ad: FeedAd?)
    }
}