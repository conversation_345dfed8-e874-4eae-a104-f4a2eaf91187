package com.dz.business.web.util

import android.app.*
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.Handler
import android.os.Looper
import androidx.core.app.NotificationCompat
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ProcessLifecycleOwner
import androidx.lifecycle.lifecycleScope
import com.blankj.utilcode.util.GsonUtils
import com.dz.business.base.BBaseMC
import com.dz.business.base.data.bean.AdJumpOutPushTip
import com.dz.business.base.data.bean.TransJumpOutPushCon
import com.dz.business.base.detail.DetailMC
import com.dz.business.base.main.MainME
import com.dz.business.base.main.intent.MainIntent
import com.dz.business.base.notification.NotificationMC
import com.dz.business.base.notification.NotificationMS
import com.dz.business.web.R
import com.dz.foundation.base.module.AppInfoUtil
import com.dz.foundation.base.module.AppModule
import com.dz.foundation.base.utils.LogUtil
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.net.URLEncoder
import kotlin.random.Random

object TransJumpOutPush : DefaultLifecycleObserver {

    private const val TAG = "TransJumpOutPush"
    private const val CHANNEL_ID = "trans_jump_out_channel"
    private const val CHANNEL_NAME = "换量跳转通知"
    private const val NOTIFICATION_ID = 1001
    private const val DEFAULT_DELAY_TIME = 30_000L
    private const val DEFAULT_TITLE = "【待领取】大额金币奖励等你领，手慢无！"
    private const val DEFAULT_CONTENT = "点击查看>>"

    private var application: Application? = null
    private var handler: Handler? = null
    private var config: TransJumpOutPushCon? = null

    fun init(app: Application) {
        try {
            if (application == null) {
                application = app
                handler = Handler(Looper.getMainLooper())
                createNotificationChannel(app)
                ProcessLifecycleOwner.get().lifecycle.addObserver(this)
            }
        } catch (e: Exception) {
            LogUtil.e(TAG, "Jump trans init: 发生未知异常: ${e.message}")
        }

    }

    fun saveConfig(cfg: TransJumpOutPushCon) {
        LogUtil.d(TAG, "TransJumpOutPush，cfg = $cfg ")
        config = cfg
    }

    private var coroutineScope: CoroutineScope? = CoroutineUtils.getCoroutineScope()
    fun startPushDelay() {
        val delay = (config?.behavioralInterval?.times(1000)) ?: DEFAULT_DELAY_TIME
//        if (handler == null) return
        //todo 延迟推送换协程
//        pushRunnable = Runnable { sendPush() }
//        handler?.postDelayed(pushRunnable!!, delay)

        coroutineScope = if (coroutineScope == null) {
            CoroutineUtils.getCoroutineScope()
        } else {
            coroutineScope?.cancel()
            coroutineScope = null
            CoroutineUtils.getCoroutineScope()
        }
        coroutineScope?.launch {
            LogUtil.d(TAG, "已启动延迟推送任务，$delay ms 后触发")
            delay(delay)
            sendPush()
        }

    }

    private fun cancelPush() {
        coroutineScope?.cancel()
        coroutineScope = null
        LogUtil.d(TAG, "coroutineScope协程cancel 已取消推送任务")
    }
    var redDotCount = 0
    private fun sendPush() {
        val context = application ?: return
        val data = chooseData()

        val pendingIntent = createClickPendingIntent(0)
        val notification = NotificationCompat.Builder(context, CHANNEL_ID)
            .setContentTitle(data?.title ?: DEFAULT_TITLE)
            .setContentText(data?.content ?: DEFAULT_CONTENT)
            .setSmallIcon(R.drawable.push)
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .build()
        redDotCount += (config?.redDotNumber ?: 0)
        if (redDotCount > 100) {
            redDotCount = 99
        }
        NotificationMS.get()?.setBadgeCount(
            AppModule.getApplication(),
            redDotCount,
            BBaseMC.LAUNCHER_CLASS_NAME
        )

        (context.getSystemService(Context.NOTIFICATION_SERVICE) as? NotificationManager)?.notify(
            NOTIFICATION_ID,
            notification
        )
        LogUtil.d(TAG, "发送 trans push：data=$data")
    }

    private fun chooseData(): AdJumpOutPushTip? {
        val list = config?.pushConfInfoVoList.orEmpty()
        if (list.isEmpty()) {
            LogUtil.e(TAG, "通知标题文案异常！使用默认标题")
            return null
        }
        return list
            .sortedBy { it.sort }
            .groupBy { it.sort }
            .minByOrNull { it.key }
            ?.value
            ?.randomOrNull()
    }

    private fun createClickPendingIntent(requestCode: Int): PendingIntent? {
        val intent = Intent(NotificationMC.ACTION_NOTIFICATION_CLICK).apply {
            putExtra(NotificationMC.KEY_REQUEST, requestCode)
            flags = Intent.FLAG_ACTIVITY_NEW_TASK
            action = Intent.ACTION_VIEW
            val map = mapOf("selectedTab" to MainIntent.TAB_WELFARE)

            val uri: Uri = Uri.parse(
                "dz://${AppInfoUtil.getPackageName()}?" +
                        "action=main&param=${URLEncoder.encode(GsonUtils.toJson(map), "utf-8")}&" +
                        "launchFrom=localpush"
            )
            data = uri
        }
        val flags = PendingIntent.FLAG_UPDATE_CURRENT or
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) PendingIntent.FLAG_IMMUTABLE else 0

        return PendingIntent.getActivity(AppModule.getApplication(), requestCode, intent, flags)
    }

    private fun createNotificationChannel(context: Context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                CHANNEL_NAME,
                NotificationManager.IMPORTANCE_DEFAULT
            )
            (context.getSystemService(NotificationManager::class.java))?.createNotificationChannel(
                channel
            )
        }
    }

    override fun onStop(owner: LifecycleOwner) {
        LogUtil.d(TAG, "App 退到后台，延迟推送继续运行中")
    }

    override fun onStart(owner: LifecycleOwner) {
        cancelPush()
        LogUtil.d(TAG, "App 回到前台，取消推送任务")
    }

    override fun onDestroy(owner: LifecycleOwner) {
        cancelPush()
        LogUtil.d(TAG, "App 杀死，取消推送任务")
    }
}
