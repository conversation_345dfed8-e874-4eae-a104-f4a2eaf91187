package com.dz.business.web.ui.page

import BBaseME
import android.app.Activity
import android.os.Build
import android.os.Bundle
import android.view.View
import android.widget.FrameLayout
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import com.dz.business.base.data.BBaseKV
import com.blankj.utilcode.util.Utils.Task
import com.dz.business.base.data.BBaseKV.hasShowPacketComp
import com.dz.business.base.data.PageConstant
import com.dz.business.base.personal.PersonalMR
import com.dz.business.base.splash.OptSceneType
import com.dz.business.base.splash.SplashMS
import com.dz.business.base.track.IWebPage
import com.dz.business.base.track.TrackUtil
import com.dz.business.base.ui.BaseActivity
import com.dz.business.base.ui.component.WebViewComp
import com.dz.business.base.ui.web.JsCallNativeBridge
import com.dz.business.base.ui.web.jsinterface.DzJsBridge
import com.dz.business.base.utils.ApkInstallHelper
import com.dz.business.base.utils.RequestPermissionUtil
import com.dz.business.base.web.WebME
import com.dz.business.base.web.WebMR
import com.dz.business.base.web.intent.WebViewIntent
import com.dz.business.base.welfare.WelfareME
import com.dz.business.base.welfare.WelfareMS2
import com.dz.business.base.widget.WidgetMC
import com.dz.business.base.widget.WidgetME
import com.dz.business.track.events.DzTrackEvents
import com.dz.business.track.tracker.SensorTracker
import com.dz.business.web.R
import com.dz.business.web.data.JSNetWorkRequestParam
import com.dz.business.web.data.TestRequest
import com.dz.business.web.data.WebKV
import com.dz.business.web.databinding.WebActivityBinding
import com.dz.business.web.initExternalWebView
import com.dz.business.web.initWebView
import com.dz.business.web.monitor.WelfareMonitor
import com.dz.business.web.ui.page.WelfareFragment.Companion.TAG
import com.dz.business.web.network.WebNetWork
import com.dz.business.web.releaseWebView
import com.dz.business.web.util.ExternalTaskUtils
import com.dz.business.web.util.ExternalTaskUtils.ACTION
import com.dz.business.web.util.TaskUtils
import com.dz.business.web.vm.WebActivityVM
import com.dz.business.welfare.network.WelfareNetWork
import com.dz.foundation.base.utils.AppActiveManager
import com.dz.foundation.base.utils.LocalActivityMgr
import com.dz.foundation.base.utils.LogUtil
import com.dz.foundation.base.utils.PermissionUtils
import com.dz.foundation.network.onError
import com.dz.foundation.network.onResponse
import com.dz.platform.common.base.ui.dialog.PDialogComponent
import com.dz.platform.common.router.onDismiss
import com.dz.platform.common.router.onShow
import com.dz.platform.common.toast.ToastManager
import com.gyf.immersionbar.BarHide
import com.sensorsdata.analytics.android.sdk.SensorsDataIgnoreTrackAppViewScreen
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.json.JSONObject
import java.net.URI

@SensorsDataIgnoreTrackAppViewScreen
class WebActivity : BaseActivity<WebActivityBinding, WebActivityVM>(),
    AppActiveManager.OnAppActiveListener, IWebPage {

    companion object {
        const val TAG_WEB = "WebActivity"
    }

    init {
        initWebView()
    }

    private lateinit var webViewComp: WebViewComp

    private var webTitle: String = ""
    private var useWebTitle = true

    private var hasShowDialog = false

    //标识是否为弹窗展示时候的dialog
    private var activityAlive = true
    /**
     * 网页标题
     */
    private var htmlTitle: String = ""

    /**
     * 是否是福利页的主页面
     */
    private var isWelfareMain: Boolean? = null

    //js是否接管返回键时间
    private var mIsTakeOverBackPressEvent = false

    private var welfareMonitor: WelfareMonitor? = null

    private var isVisible = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        checkWebInstanceToFinishFirst()
    }

    override fun onStart() {
        super.onStart()
        isVisible = true
    }

    override fun onStop() {
        super.onStop()
        isVisible = false
    }

    /**
     * 检查当前页面 url 所允许存在的最多activity 页面数 若超过则关闭最早加入栈中的activity
     */
    private fun checkWebInstanceToFinishFirst() {
        val url = mViewModel.routeIntent?.url
        val activityList = LocalActivityMgr.getActivityByTag(getUiTag())
        activityList.forEach {
            if (it is WebActivity && it != this@WebActivity) {
                if (((it.getRouteIntent() as WebViewIntent).url == url) && (!it.isFinishing && !it.isDestroyed)) {
                    it.finish()
                }
            }
        }
    }

    override fun maxInstanceSize(): Int {
        return Int.MAX_VALUE
    }

    override fun initImmersionBar() {
        if (mViewModel.isNativeTitle()) {
            getImmersionBar().statusBarColor(com.dz.platform.common.R.color.common_card_FFFFFFFF)
                    .navigationBarColor(com.dz.platform.common.R.color.common_card_FFFFFFFF)
                    .navigationBarDarkIcon(true)
                    .statusBarDarkFont(true).fitsSystemWindows(true)
                    .hideBar(BarHide.FLAG_SHOW_BAR).init()
        } else {
            getImmersionBar()
                .transparentStatusBar()
                .navigationBarColor(R.color.common_card_FFFFFFFF)
                .navigationBarDarkIcon(true)
                .statusBarDarkFont(true)
                .hideBar(BarHide.FLAG_SHOW_BAR)
                .init()
        }
    }

    override fun initData() {
        val isNative = mViewModel.routeIntent?.isNative ?: true
        //是否为点众内部的H5页面，用于和涉及客户端JS交互的三方H5页面标识，标识为false则不注册现有JS交互，不对外暴露
        LogUtil.d(TAG_WEB, "WebActivity 加载H5 isNative = $isNative")
        if (!isNative)  {
            //三方页面调用web  只初始化ExExternalWebInterface
            LogUtil.d(TAG_WEB, "三方页面调用web  只初始化ExExternalWebInterface")
            //这里因为init发生顺序导致无法使用vm的数据，
            //所以新增一个取消注册的方法，在进入三方页面的时候防止其访问客户端现有方法
            releaseWebView()
            initExternalWebView()
        }
        mViewModel.routeIntent?.title?.let { text ->
            setActivityTitle(text)
            if (text.isNotEmpty()) {
                webTitle = text
                useWebTitle = false
            } else {
                useWebTitle = true
            }
//            SensorTracker.trackViewScreen(this@WebActivity)
        }
        if (mViewModel.isNativeTitle()) {
            mViewBinding.titleBar.visibility = View.VISIBLE
            if (!mViewModel.routeIntent?.title.isNullOrEmpty()) {
                mViewBinding.titleBar.setTitle(mViewModel.routeIntent?.title)
            }
        } else {
            mViewBinding.titleBar.visibility = View.GONE
        }
        initImmersionBar()
        webViewComp = WebViewComp(this).apply {

            setWebTitleListener {
                htmlTitle = it
                if (!useWebTitle) {
                    return@setWebTitleListener
                }
                <EMAIL>(it)
                setActivityTitle(it)
                LogUtil.d(TAG_WEB, "H5更新页面标题:$it")
                webTitle = it
                TrackUtil.updateCurrentPage(it)
                SensorTracker.trackViewScreen(this@WebActivity)
            }

            showLoadingBar = mViewModel.isNativeTitle()
            addJsBridgeInterface(JsCallNativeBridge(this@WebActivity))

            onPageFinished = {
                if (mViewModel.routeIntent?.isNative == false) {
                    //有相关参数才进行下面操作
                    LogUtil.d(
                        "onPageFinished",
                        "activity是否存活 activityAlive: $activityAlive   "
                    )
                    if(mViewModel.routeIntent?.isAuth == false && !hasShowDialog && ExternalTaskUtils.showPrivacyDialogDialog == null && activityAlive) {
                        hasShowDialog = true
                        LogUtil.d(
                            "onPageFinished",
                            "showAuthAlert: showPrivacyDialog   "
                        )
                        //无权限，端内自行弹出弹窗
                        WebMR.get().showPrivacyDialog().apply {
                            img = mViewModel.routeIntent?.img
                        }.onShow {
                            trackExposure(TaskUtils.origin)
                            ExternalTaskUtils.showPrivacyDialogDialog = it
                        }.onSure {
                            reportPrivacy()
                        }.onClose {
                            ExternalTaskUtils.callBackForJsExternal(webViewComp.getWebView(),false)
                            ExternalTaskUtils.showPrivacyDialogDialog?.dismiss()
                        }.onDismiss {
                            ExternalTaskUtils.showPrivacyDialogDialog = null
                        }.start()
                    }
                    if(mViewModel.routeIntent?.taskAction != -1) {
                        WelfareNetWork.get().welfareTaskReport()
                            .addParams(mViewModel.routeIntent?.taskAction ?: -1)
                            .onResponse {
                                LogUtil.d("打印","1302接口：WebActivity： mViewModel.routeIntent?.taskActio=${mViewModel.routeIntent?.taskAction}  it=${it.data}")
                            }
                            .doRequest()
                    }

                }
                if (SplashMS.get()?.isAgreePrivacyPolicy() == true) {
                    SplashMS.get()?.getQueryID(OptSceneType.WelfarePage)
                }
            }
            if (WebKV.welfareMonitorEnable && isWelfareMain() ) {
                // 正常情况下福利页不会进入到WebActivity。非正常情况下进入，不监听。
                val callback = object : WelfareMonitor.Callback {
                    override fun onWhiteScreen(scene: String, url: String?) {

                    }

                    override fun isVisible(): Boolean = isVisible

                    override fun refresh(scene: String) {
                        webViewComp.getWebView().reload()
                    }

                }
                welfareMonitor = WelfareMonitor("二级-福利", lifecycleScope, callback).also {
                    it.bindWebView(this)
                }
            }
        }
        welfareMonitor?.onPageVisible()
    }

    private fun trackExposure(origin: String) {
        DzTrackEvents.get().popupShow().popupName("兑换商城").origin(origin)
            .contentSource("进入商城").track()
    }

    private fun reportPrivacy() {
        WebNetWork.get().authCode().addParams(1).onResponse {
            LogUtil.d(TAG_WEB, "WebActivity reportPrivacy成功  data : $it")
            ExternalTaskUtils.showPrivacyDialogDialog?.dismiss()
            mViewModel.routeIntent?.isAuth = true
            ExternalTaskUtils.callBackForJsExternal(webViewComp.getWebView(),true)
        }.onError {
            ToastManager.showToast("网络异常请稍后重试")
            LogUtil.d(TAG_WEB, "WebActivity reportPrivacy失败 : $it")
        }.doRequest()
    }

    override fun initView() {
        mViewBinding.contentRoot.addView(
            webViewComp,
            FrameLayout.LayoutParams(
                FrameLayout.LayoutParams.MATCH_PARENT,
                FrameLayout.LayoutParams.MATCH_PARENT
            )
        )
        var html = mViewModel.getHtml()
        if (html.isNullOrEmpty()) {
            webViewComp.bindData(mViewModel.getLoadUrl())
        } else {
            webViewComp.loadDataWithBaseURL(html)
        }

        // testInvoke(webViewComp)


    }

    private fun testInvoke(webViewComp: WebViewComp) {
        val testRequestJson = TestRequest().apply {
            module = "NetWork"
            action = "request"
            params = JSNetWorkRequestParam().apply {
                apiCode = "1013"
            }
        }.toJson()
        DzJsBridge.doInvoke(webViewComp.getWebManager(), testRequestJson)
    }

    override fun initListener() {
        AppActiveManager.addAppActiveListener(
            AppActiveManager.TAG_APP,
            this
        )
        mViewBinding.titleBar.onClickBackListener = {
            if (!webViewComp.isCanGoBack()) {
                finish()
            }
        }
    }

    override fun onResume() {
        super.onResume()
        webViewComp.onResume()
        pageShowCallBack()
        if (webTitle.isNotEmpty()) {
            SensorTracker.trackViewScreen(this@WebActivity)
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val apkUri = ApkInstallHelper.pendingApkUri
            if (apkUri != null && packageManager.canRequestPackageInstalls()) {
                ApkInstallHelper.pendingApkUri = null  // 清除记录
                ApkInstallHelper.installApk(this, apkUri) // 继续安装
            }
        }
    }

    private fun pageShowCallBack() {
        var params = mutableMapOf<String, String>()
        BBaseKV.widgetFrom?.takeIf { it.isNotEmpty() }?.let {
            params["launchFrom"] = it
            params["scene"] = "viewDidAppear"
            BBaseKV.widgetFrom = ""
        }

        LogUtil.d(TAG, "调用 pageshow params = $params")
        webViewComp?.getWebView()?.let {
            TaskUtils.callBackForJs(
                it,
                TaskUtils.ACTION_PAGE_SHOW,
                params
            )
        }
    }



    override fun onPause() {
        super.onPause()
        webViewComp.onPause()
    }

    override fun onDestroy() {
        super.onDestroy()
        activityAlive = false
        AppActiveManager.removeListener(this)
        ApkInstallHelper.pendingApkUri = null  // 清除记录
        welfareMonitor?.reset(WelfareMonitor.SCENE_DESTROY)
        if (mViewModel.routeIntent?.isNative == false) {
            initWebView()
        }
    }

    override fun subscribeEvent(lifecycleOwner: LifecycleOwner, lifecycleTag: String) {
        //自动添加桌面Widget的回调
        BBaseME.get().onAppWidgetCreateResult().observe(lifecycleOwner) {
            println("Activity 桌面Widget添加-->结果:$it")
            val result = if (it) TaskUtils.SUCCESS else TaskUtils.FAIL
            TaskUtils.noActionCallBackForJs(webViewComp.getWebView(), TaskUtils.ACTION_APP_WIDGET_CREATE_RESULT, result)
        }
        WelfareME.get().signInDialogShow().observe(lifecycleOwner) {
            if (isWelfareMain()) {
                TaskUtils.callBackForJs(
                    webViewComp.getWebView(),
                    TaskUtils.HIDE_WEB_POPUP,
                    emptyMap()
                )
            }
        }
        WelfareME.get().signInRewardResult().observe(lifecycleOwner) {
            if (it == 1 && isWelfareMain()) {
                TaskUtils.callBackForJs(
                    webViewComp.getWebView(),
                    TaskUtils.ACTION_PAGE_SHOW,
                    emptyMap()
                )
            }
        }

        //自动添加桌面Widget的回调
        WidgetME.get().onWidgetAddSuccess().observe(lifecycleOwner) {
            println("Fragment 桌面Widget添加-->结果:$it")
            val result =
                if (it == WidgetMC.WIDGET_TYPE_WELFARE_PUSH) "${TaskUtils.SUCCESS}_NetIncomeWidget" else "${TaskUtils.FAIL}_NetIncomeWidget"
            webViewComp?.getWebView()?.let { it1 ->
                TaskUtils.noActionCallBackForJs(
                    it1,
                    TaskUtils.ACTION_APP_WIDGET_CREATE_RESULT,
                    result
                )
            }
        }
        //日历权限回调 1:权限授予并设置成功 2:各类操作失败 3:权限被拒绝 4:删除成功 5:删除失败

        WebME.get().onActivityBackPermissionCalendar().observeForever { result ->
            LogUtil.d("BaseVisibilityFragment","onActivityBackPermissionCalendar:${result}")
            when (result) {
                1 -> {
                    LogUtil.d("PermissionObserver", "权限授予并设置成功")
                    callBackForJsCalendar("1")
                }

                2 -> {
                    LogUtil.d("PermissionObserver", "查询失败")
                    callBackForJsCalendar("2")
                }
                3 -> {
                    LogUtil.d("PermissionObserver", "权限被拒绝")
                    callBackForJsCalendar("3")
                }

                4 -> {
                    LogUtil.d("PermissionObserver", "删除成功")
                    callBackForJsCalendar("4")
                }

                5 -> {
                    LogUtil.d("PermissionObserver", "删除失败")
                    callBackForJsCalendar("5")
                }
            }
        }

        WidgetME.get().onWidgetAddRejected().observeForever {
            if (it == WidgetMC.WIDGET_TYPE_WELFARE_PUSH) {
                println("Fragment println 桌面Widget添加-->结果:$it")
                val result = "${TaskUtils.FAIL}_NetIncomeWidget"
                webViewComp?.getWebView()?.let { it1 ->
                    TaskUtils.noActionCallBackForJs(
                        it1,
                        TaskUtils.ACTION_APP_WIDGET_CREATE_RESULT,
                        result
                    )
                }

            }

        }
        WebME.get().takeoverBackPressEvent().observe(lifecycleOwner,lifecycleTag){
            LogUtil.d("BaseVisibilityFragment","收到takeOverBackPressEvent事件:${it}")
                mIsTakeOverBackPressEvent = it
            }
        WebME.get().onActivityPermissionDialog().observe(lifecycleOwner,lifecycleTag){
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                RequestPermissionUtil.checkPermission(
                    this,
                    "用于保存图片",
                    PermissionUtils.CODE_STORAGE_REQUEST1,
                    PermissionUtils.loadingStorageList(),
                    WebKV.webPhotoPermission,
                    object : RequestPermissionUtil.OnPermissionRequest {
                        override fun onPermissionGranted() {
                            WebME.get().onActivityBackPermission().post(1)
                        }

                        override fun onPermissionDenied() {
                            WebKV.webPhotoPermission = true
                            ToastManager.showToast("需要存储权限才能保存图片")
                        }

                        override fun onCustomApply() {
                            // 显示权限说明对话框
                            PersonalMR.get().permissionDialog()
                                .apply {
                                    title = "为了您正常使用此功能\\n需要您允许使用存储权限"
                                    content =
                                        "1. 点击 [去开启] 后,跳转至应用信息页面\\n2. 点击 [权限] 按钮,进入权限设置页面\\n3. 开启存储权限"
                                }.start()
                        }
                    }
                )
            }
        }
    }

    fun getSource(): String? {
        return mViewModel.routeIntent?.routeSource
    }

    override fun getPageId(): String? {
        return if (isWelfareMain()) {
            PageConstant.WELFARE_ACTIVITY_ID
        } else {
            null
        }
    }

    override fun getPageName(): String {
        return webTitle
    }

    override fun autoTrack(): Boolean {
        return webTitle.isNotEmpty()
    }

    private fun callBackForJsCalendar(result: String) {
        lifecycleScope.launch(Dispatchers.Main) {
            webViewComp?.getWebView()?.let{ it ->
                TaskUtils.callBackForJsCalendar(result, it)
            }
        }
    }

    private fun isWelfareMain(): Boolean {
        isWelfareMain?.let {
            return it
        }

        val welfareCenterUrl = WelfareMS2.get()?.getCenterUrl()
        if (welfareCenterUrl.isNullOrEmpty()) {
            return false.also { isWelfareMain = it }
        }

        return kotlin.runCatching {
            val welfareUrl = URI(welfareCenterUrl)
            val currentUrl = URI(mViewModel.getLoadUrl())
            welfareUrl.path == currentUrl.path
        }.getOrElse {
            false
        }.also {
            isWelfareMain = it
        }
    }

    override fun onBackPressAction() {
        if (mIsTakeOverBackPressEvent) {
            TaskUtils.noActionCallBackForJs(webViewComp.getWebView(), TaskUtils.JS_METHOD_ON_BACK_CLICK, "")
            return
        } else {
            super.onBackPressAction()
        }
    }

    override fun onForeground(activity: Activity) {
    }

    override fun onActivityActive(activeActivity: Activity) {
    }

    override fun onBackground(activity: Activity) {
        //[1.7.0]红包雨需求，切换后台时通知js关闭特效
        val params= mutableMapOf("enterBackground" to true)
        TaskUtils.callBackForJs(
            webViewComp.getWebView(),
            TaskUtils.JS_METHOD_VIEW_DID_DISAPPEAR,
            params
        )
    }
    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        PermissionUtils.onRequestPermissionsResult(requestCode, permissions, grantResults)
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
    }

    override fun getHtmlTitle(): String = htmlTitle
}