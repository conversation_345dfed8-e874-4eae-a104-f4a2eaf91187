package com.dz.business.web.util

import android.text.Spannable
import android.text.SpannableString
import android.text.style.ForegroundColorSpan

class SpannableUtils {


    companion object {

        /**
         * 获取高亮文本的 SpannableString
         * @param fullText 完整文本
         * @param highlightText 需要高亮的文本
         * @param highlightColor 高亮颜色
         * @return SpannableString 包含高亮文本的字符串
         */
        @JvmStatic
        fun getColoredSpanText(
            fullText: String,
            highlightText: String,
            highlightColor: Int
        ): SpannableString {
            val spannable = SpannableString(fullText)
            val start = fullText.indexOf(highlightText)
            if (start >= 0) {
                val end = start + highlightText.length
                spannable.setSpan(
                    ForegroundColorSpan(highlightColor),
                    start,
                    end,
                    Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                )
            }
            return spannable
        }

    }
}