package com.dz.business.web.util

import android.view.View
import android.view.animation.AccelerateDecelerateInterpolator

class ViewAnimateUtil {

    companion object {

        /**
         * 将 View 缩小并移动到目标 View 的位置
         * @param view 要缩小并移动的 View
         * @param targetView 目标 View，缩小后的 View 将移动到这个 View 的位置
         * @param onEnd 动画结束时的回调
         */
        @JvmStatic
        fun shrinkAndMoveView(view: View, targetView: View, onEnd: (() -> Unit)? = null) {
//        // 获取 View 当前中心坐标
//        val centerX = view.x + view.width / 2f
//        val centerY = view.y + view.height / 2f

            // 获取目标 View 在屏幕上的位置
            val location = IntArray(2)
            targetView.getLocationOnScreen(location)
            //中心点
//            val targetCenterX = location[0] + targetView.width / 2f
//            val targetCenterY = location[1] + targetView.height / 2f
            //左上
            val targetCenterX = location[0]
            val targetCenterY = location[1]

            // 获取当前 View 在屏幕上的位置（确保坐标系一致）
            val selfLocation = IntArray(2)
            view.getLocationOnScreen(selfLocation)
            val offsetX = targetCenterX - (selfLocation[0] + view.width / 2f)
            val offsetY = targetCenterY - (selfLocation[1] + view.height / 2f)

            view.animate()
                .scaleX(0.1f)
                .scaleY(0.1f)
                .translationXBy(offsetX)
                .translationYBy(offsetY)
                .alpha(0.1f) // 淡出
                .setDuration(500)
                .setInterpolator(AccelerateDecelerateInterpolator())
                .withEndAction {
                    view.scaleX = 1f
                    view.scaleY = 1f
                    view.translationX = 0f
                    view.translationY = 0f
                    view.alpha = 1f
                    onEnd?.invoke()
                }
                .start()
        }
    }


}