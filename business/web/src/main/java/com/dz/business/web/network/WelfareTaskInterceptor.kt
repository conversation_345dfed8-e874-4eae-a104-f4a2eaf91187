package com.dz.business.web.network

import com.dz.business.base.data.bean.BaseBean
import com.dz.business.base.network.BaseDataRequest
import com.dz.business.base.network.HttpResponseModel
import com.dz.business.base.utils.GsonUtil
import com.dz.business.base.welfare.WelfareMC
import com.dz.business.base.welfare.WelfareME
import com.dz.business.bcommon.utils.PlayingStatisticsMgr
import com.dz.foundation.base.utils.LogUtil
import com.dz.foundation.network.DataRequest
import com.dz.foundation.network.requester.HttpInterceptor

/**
 *@Author: zhanggy
 *@Date: 2024-08-24
 *@Description:
 *@Version:1.0
 */
class WelfareTaskInterceptor : HttpInterceptor {
    override fun onResponse(
        dataRequest: DataRequest<*>,
        model: Any?,
        interceptCallback: HttpInterceptor.InterceptCallback?
    ): Boolean {
        if (dataRequest.getUrl().endsWith("1301") &&
            (model as? HttpResponseModel<*>)?.isSuccess() == true
        ) {
            val requestContent = GsonUtil.gson.fromJson(
                (dataRequest as? BaseDataRequest)?.getOriginPostContent(),
                Request1301::class.java
            )
            LogUtil.d("打印","拦截到1301请求成功： 请求体:$requestContent")
            LogUtil.d(WelfareMC.TAG_REPORT, "拦截到1301请求成功 请求体:$requestContent")
            // 打印值：{"signText":1,"preload":1,"bookReads":{"41000010043":10.599995}}
            if (requestContent.bookReads != null) {
                WelfareME.get().watchingDurationReport().post(1)
                PlayingStatisticsMgr.clearLocalPlayingDurationAndSave()
            }
        }
        return false
    }

    data class Request1301(
        var bookReads: Map<String, Double>? = null
    ) : BaseBean()
}