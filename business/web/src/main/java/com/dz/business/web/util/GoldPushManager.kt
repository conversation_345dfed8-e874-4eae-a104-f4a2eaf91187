package com.dz.business.web.util

import androidx.lifecycle.DefaultLifecycleObserver
import com.dz.business.base.data.BBaseKV
import com.dz.foundation.base.utils.LogUtil

object GoldPushManager : DefaultLifecycleObserver {

    private const val TAG = "GoldPushManager"
    //使用两个变量分别保存登录和未登录下的dp，然后只要上传过久删除
    private var loginPushDeepLink = ""
    private var unLoginPushDeepLink = ""
    private var fromGoldPush = false

    fun saveDeepLinkFromLaunch(jobid: String?) {
        jobid?.let {
            loginPushDeepLink = jobid
            unLoginPushDeepLink = jobid
            LogUtil.d("goldPush", "11111 loginPushDeepLink = $loginPushDeepLink unLoginPushDeepLink = $unLoginPushDeepLink")
        }
        fromGoldPush = true
    }

    fun fromGoldPush():Boolean {
        return fromGoldPush
    }

    fun sendDeeplinkToServer(): String {
        LogUtil.d("goldPush", "loginPushDeepLink = $loginPushDeepLink unLoginPushDeepLink = $unLoginPushDeepLink")
        if (BBaseKV.token.isNullOrEmpty()) {
            val dp = unLoginPushDeepLink
            unLoginPushDeepLink = ""
            return dp
        } else {
            val dp = loginPushDeepLink
            loginPushDeepLink = ""
            return dp
        }
    }



}
