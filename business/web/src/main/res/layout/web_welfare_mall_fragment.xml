<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:ignore="ResourceName">

    <com.dz.business.base.ui.viewpager2.ViewPager2ChildFrameLayout
        android:id="@+id/clRoot"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/common_00F7F8FA">

        <com.dz.foundation.ui.widget.DzConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <!--//顶部-->
            <com.dz.foundation.ui.widget.DzConstraintLayout
                android:id="@+id/flHead"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/common_dp5"
                app:layout_constraintTop_toTopOf="parent"
                android:background="@drawable/web_welfare_mall_bg">

                <!--跑马灯-->
                <com.dz.business.base.ui.component.SpaceMarqueeView
                    android:id="@+id/SpaceMarqueeView"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/adp_18"
                    android:layout_marginStart="@dimen/adp_24"
                    android:layout_marginTop="@dimen/adp_87"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:visibility="visible" />

                <com.dz.foundation.ui.widget.DzImageView
                    android:id="@+id/imgNotice"
                    android:layout_width="@dimen/adp_25"
                    android:layout_height="@dimen/adp_14"
                    android:layout_marginStart="@dimen/adp_16"
                    android:layout_marginTop="@dimen/adp_89"
                    android:src="@drawable/web_ic_notice"
                    android:visibility="gone"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:visibility="visible" />

                <!--任务条-->
                <com.dz.foundation.ui.widget.DzConstraintLayout
                    android:id="@+id/clTaskProgress"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/adp_90"
                    android:layout_marginHorizontal="@dimen/adp_10"
                    android:layout_marginTop="@dimen/adp_11"
                    android:background="@drawable/web_welfare_task_bg"
                    android:paddingStart="@dimen/adp_16"
                    android:paddingEnd="@dimen/adp_12"
                    app:layout_constraintBottom_toBottomOf="parent"
                    android:layout_marginBottom="@dimen/common_dp5"
                    app:layout_constraintTop_toBottomOf="@id/SpaceMarqueeView"
                    app:layout_goneMarginTop="@dimen/adp_87">


                    <com.dz.foundation.ui.widget.DzTextView
                        android:id="@+id/tvCoinDec"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/adp_17"
                        android:text="@string/web_receive_it_today"
                        android:textColor="@color/white"
                        android:textSize="@dimen/adp_12"
                        app:layout_constraintTop_toTopOf="parent" />

                    <!--可领取金币数-->
                    <com.dz.foundation.ui.widget.DzTextView
                        android:id="@+id/tvCoinNum"
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/adp_33"
                        android:inputType="number"
                        android:maxLength="5"
                        android:textColor="@color/white"
                        android:textSize="@dimen/adp_28"
                        android:textStyle="bold"
                        app:layout_constraintStart_toStartOf="@id/tvCoinDec"
                        app:layout_constraintTop_toBottomOf="@+id/tvCoinDec"
                        tools:text="21800" />

                    <com.dz.foundation.ui.widget.DzTextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/adp_3"
                        android:text="@string/web_gold_coin"
                        android:textColor="@color/white"
                        android:textSize="@dimen/adp_12"
                        app:layout_constraintBottom_toBottomOf="@+id/tvCoinNum"
                        app:layout_constraintStart_toEndOf="@id/tvCoinNum" />

                    <com.dz.foundation.ui.widget.DzConstraintLayout
                        android:id="@+id/taskGroup1"
                        android:layout_width="@dimen/adp_50"
                        android:layout_height="@dimen/adp_72"
                        android:layout_marginEnd="@dimen/adp_16"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@+id/taskGroup2"
                        app:layout_constraintTop_toTopOf="parent">

                        <include
                            android:id="@+id/taskItem1"
                            layout="@layout/web_welfare_task_item" />
                    </com.dz.foundation.ui.widget.DzConstraintLayout>

                    <com.dz.foundation.ui.widget.DzConstraintLayout
                        android:id="@+id/taskGroup2"
                        android:layout_width="@dimen/adp_50"
                        android:layout_height="@dimen/adp_72"
                        android:layout_marginEnd="@dimen/adp_16"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@+id/taskGroup3"
                        app:layout_constraintTop_toTopOf="parent">

                        <include
                            android:id="@+id/taskItem2"
                            layout="@layout/web_welfare_task_item" />
                    </com.dz.foundation.ui.widget.DzConstraintLayout>


                    <com.dz.foundation.ui.widget.DzConstraintLayout
                        android:id="@+id/taskGroup3"
                        android:layout_width="@dimen/adp_50"
                        android:layout_height="@dimen/adp_72"
                        android:layout_marginEnd="@dimen/adp_12"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent">

                        <include
                            android:id="@+id/taskItem3"
                            layout="@layout/web_welfare_task_item" />
                    </com.dz.foundation.ui.widget.DzConstraintLayout>


                </com.dz.foundation.ui.widget.DzConstraintLayout>
            </com.dz.foundation.ui.widget.DzConstraintLayout>


            <com.dz.foundation.ui.widget.DzConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/flHead">

                <!--广告-->
                <FrameLayout
                    android:id="@+id/adContainer"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    app:layout_constraintBottom_toTopOf="@+id/view_bottom"
                    app:layout_constraintTop_toTopOf="parent"/>

                <!--宝箱-->
                <com.dz.business.web.ui.compoent.CircularProgressLayout
                    android:id="@+id/circularProgressLayout"
                    android:layout_width="@dimen/adp_70"
                    android:layout_height="@dimen/adp_82"
                    android:layout_marginRight="@dimen/adp_10"
                    android:layout_marginBottom="@dimen/adp_113"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    tools:visibility="visible" />

                <!--上滑-->
                <com.airbnb.lottie.LottieAnimationView
                    android:id="@+id/lavScroll"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/common_dp300"
                    android:layout_marginTop="@dimen/common_dp50"
                    android:visibility="gone"
                    app:layout_constraintTop_toTopOf="@+id/adContainer"
                    app:lottie_autoPlay="false"
                    app:lottie_imageAssetsFolder="images"
                    app:lottie_loop="false"
                    app:lottie_rawRes="@raw/web_welfare_slide"
                    app:lottie_repeatCount="1"
                    tools:visibility="visible" />

                <!--点击-->
                <com.airbnb.lottie.LottieAnimationView
                    android:id="@+id/lavClick"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/common_dp220"
                    android:layout_marginTop="@dimen/common_dp150"
                    android:visibility="gone"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:lottie_autoPlay="false"
                    app:lottie_imageAssetsFolder="images"
                    app:lottie_loop="false"
                    app:lottie_rawRes="@raw/web_welfare_click"
                    app:lottie_repeatCount="1"
                    tools:visibility="visible" />

                <com.dz.foundation.ui.widget.DzTextView
                    android:id="@+id/tvScrollDec"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/common_dp35"
                    android:layout_marginTop="@dimen/common_dp35"
                    android:background="@color/common_FF5E6267"
                    android:gravity="center"
                    android:paddingHorizontal="@dimen/common_dp12"
                    android:text="@string/web_scroll_to_receive_reward"
                    android:textColor="@color/white"
                    android:textSize="@dimen/common_dp13"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/lavScroll"
                    app:shape_radius="@dimen/common_dp6"
                    app:shape_solid_color="@color/common_FF5E6267"
                    tools:visibility="visible" />

<!--                <com.dz.foundation.ui.widget.DzImageView-->
<!--                    android:id="@+id/tvScrollDecDown"-->
<!--                    android:layout_width="@dimen/common_dp27"-->
<!--                    android:layout_height="@dimen/common_dp6"-->
<!--                    android:layout_marginTop="-1dp"-->
<!--                    android:scaleType="fitXY"-->
<!--                    android:src="@drawable/web_ic_gray_down"-->
<!--                    android:visibility="gone"-->
<!--                    app:layout_constraintEnd_toEndOf="@+id/tvScrollDec"-->
<!--                    app:layout_constraintStart_toStartOf="@+id/tvScrollDec"-->
<!--                    app:layout_constraintTop_toBottomOf="@+id/tvScrollDec"-->
<!--                    tools:visibility="gone" />-->

                <com.dz.foundation.ui.widget.DzTextView
                    android:id="@+id/tvClickDec"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/common_dp35"
                    android:layout_marginLeft="@dimen/common_dp70"
                    android:layout_marginTop="@dimen/common_dp38"
                    android:gravity="center"
                    android:paddingHorizontal="@dimen/common_dp12"
                    android:text="@string/web_click_to_view_discounted_products"
                    android:textColor="@color/white"
                    android:textSize="@dimen/common_dp13"
                    android:visibility="gone"
                    app:layout_constraintStart_toStartOf="@+id/lavClick"
                    app:layout_constraintTop_toTopOf="@+id/lavClick"
                    app:shape_radius="@dimen/common_dp6"
                    app:shape_solid_color="@color/common_FF5E6267"
                    tools:visibility="visible" />

<!--                <com.dz.foundation.ui.widget.DzImageView-->
<!--                    android:id="@+id/tvClickDecDown"-->
<!--                    android:layout_width="@dimen/common_dp27"-->
<!--                    android:layout_height="@dimen/common_dp6"-->
<!--                    android:layout_marginTop="-1dp"-->
<!--                    android:scaleType="fitXY"-->
<!--                    android:src="@drawable/web_ic_gray_down"-->
<!--                    android:visibility="gone"-->
<!--                    app:layout_constraintEnd_toEndOf="@+id/tvClickDec"-->
<!--                    app:layout_constraintStart_toStartOf="@+id/tvClickDec"-->
<!--                    app:layout_constraintTop_toBottomOf="@+id/tvClickDec"-->
<!--                    tools:visibility="gone" />-->

                <com.dz.foundation.ui.widget.DzView
                    android:id="@+id/view_bottom"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/common_dp60"
                    app:layout_constraintBottom_toBottomOf="parent" />
            </com.dz.foundation.ui.widget.DzConstraintLayout>

            <!--弹窗部分-->
            <!--首次提示弹窗-->
            <com.dz.foundation.ui.widget.DzConstraintLayout
                android:id="@+id/dlStartDialog"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/black_75"
                android:clickable="true"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <com.dz.foundation.ui.widget.DzConstraintLayout
                    android:id="@+id/clStartLayoutGroup"
                    android:layout_width="@dimen/common_dp290"
                    android:layout_height="@dimen/common_dp380"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <com.dz.foundation.ui.widget.DzImageView
                        android:id="@+id/imgStartClose"
                        android:layout_width="@dimen/common_dp24"
                        android:layout_height="@dimen/common_dp24"
                        android:src="@drawable/web_ic_close"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <com.dz.foundation.ui.widget.DzConstraintLayout
                        android:id="@+id/clStartLayout"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/common_dp320"
                        android:background="@drawable/web_welfare_start_bg"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent">

                        <com.dz.foundation.ui.widget.DzTextView
                            android:id="@+id/tvStartDec"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/common_dp30"
                            android:textColor="@color/black"
                            android:textSize="@dimen/common_dp21"
                            android:textStyle="bold"
                            app:layout_constraintLeft_toLeftOf="parent"
                            app:layout_constraintRight_toRightOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:text="浏览页面30秒领奖励" />

                        <com.dz.foundation.ui.widget.DzButton
                            android:id="@+id/btnStartOk"
                            android:layout_width="match_parent"
                            android:layout_height="@dimen/common_dp48"
                            android:layout_marginHorizontal="@dimen/common_dp24"
                            android:layout_marginBottom="@dimen/common_dp48"
                            android:gravity="center"
                            android:text="@string/web_got_it"
                            android:textColor="@color/white"
                            android:textSize="@dimen/common_dp17"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:shape_radius="@dimen/common_dp32"
                            app:shape_solid_gradient_end_color="@color/common_FF005F"
                            app:shape_solid_gradient_orientation="left_right"
                            app:shape_solid_gradient_start_color="@color/common_FF2626" />

                        <com.dz.foundation.ui.widget.DzTextView
                            android:layout_width="match_parent"
                            android:layout_height="@dimen/common_dp20"
                            android:layout_marginBottom="@dimen/common_dp20"
                            android:gravity="center"
                            android:text="@string/web_distribute_rewards_dec"
                            android:textColor="@color/common_FF7B8288"
                            android:textSize="@dimen/common_dp14"
                            app:layout_constraintBottom_toBottomOf="parent" />
                    </com.dz.foundation.ui.widget.DzConstraintLayout>
                </com.dz.foundation.ui.widget.DzConstraintLayout>
            </com.dz.foundation.ui.widget.DzConstraintLayout>

            <!--不满足条件时提示弹窗-->
            <com.dz.foundation.ui.widget.DzConstraintLayout
                android:id="@+id/dlContinueDialog"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/black_75"
                android:clickable="true"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <com.dz.foundation.ui.widget.DzConstraintLayout
                    android:layout_width="@dimen/common_dp290"
                    android:layout_height="@dimen/common_dp290"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <com.dz.foundation.ui.widget.DzImageView
                        android:id="@+id/imgContinueClose"
                        android:layout_width="@dimen/common_dp24"
                        android:layout_height="@dimen/common_dp24"
                        android:src="@drawable/web_ic_close"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <com.dz.foundation.ui.widget.DzConstraintLayout
                        android:id="@+id/clContinueLayout"
                        android:layout_width="match_parent"
                        android:layout_height="0dp"
                        android:layout_marginTop="@dimen/common_dp24"
                        android:background="@drawable/web_welfare_continue_bg"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent">

                        <com.dz.foundation.ui.widget.DzTextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="@dimen/common_dp120"
                            android:text="@string/web_not_satisfied"
                            android:textColor="@color/black"
                            android:textSize="@dimen/common_dp21"
                            android:textStyle="bold"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintLeft_toLeftOf="parent"
                            app:layout_constraintRight_toRightOf="parent" />

                        <com.dz.foundation.ui.widget.DzButton
                            android:id="@+id/btnContinueOk"
                            android:layout_width="match_parent"
                            android:layout_height="@dimen/common_dp48"
                            android:layout_marginHorizontal="@dimen/common_dp24"
                            android:layout_marginBottom="@dimen/common_dp20"
                            android:gravity="center"
                            android:text="@string/web_go_to_mall"
                            android:textColor="@color/white"
                            android:textSize="@dimen/common_dp17"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:shape_radius="@dimen/common_dp32"
                            app:shape_solid_gradient_end_color="@color/common_FF005F"
                            app:shape_solid_gradient_orientation="left_right"
                            app:shape_solid_gradient_start_color="@color/common_FF2626" />

                        <com.dz.foundation.ui.widget.DzTextView
                            android:id="@+id/tvContinueDec"
                            android:layout_width="match_parent"
                            android:layout_height="@dimen/common_dp20"
                            android:layout_marginBottom="@dimen/common_dp90"
                            android:gravity="center"
                            android:textColor="@color/common_FF7B8288"
                            android:textSize="@dimen/common_dp14"
                            app:layout_constraintBottom_toBottomOf="parent"
                            tools:text="预览商城60s可获取180金币" />
                    </com.dz.foundation.ui.widget.DzConstraintLayout>

                </com.dz.foundation.ui.widget.DzConstraintLayout>

            </com.dz.foundation.ui.widget.DzConstraintLayout>


            <!--领取奖励成功提示弹窗-->
            <com.dz.foundation.ui.widget.DzConstraintLayout
                android:id="@+id/dlSuccessDialog"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/black_75"
                android:clickable="true"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <com.dz.foundation.ui.widget.DzConstraintLayout
                    android:layout_width="@dimen/common_dp290"
                    android:layout_height="@dimen/common_dp346"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <com.dz.foundation.ui.widget.DzImageView
                        android:id="@+id/imgSuccessClose"
                        android:layout_width="@dimen/common_dp24"
                        android:layout_height="@dimen/common_dp24"
                        android:src="@drawable/web_ic_close"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <com.dz.foundation.ui.widget.DzConstraintLayout
                        android:id="@+id/clSuccessLayout"
                        android:layout_width="match_parent"
                        android:layout_height="0dp"
                        android:layout_marginTop="@dimen/common_dp26"
                        android:background="@drawable/web_welfare_success_bg"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent">

                        <com.dz.foundation.ui.widget.DzTextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="@dimen/common_dp175"
                            android:text="@string/web_get_rewards"
                            android:textColor="@color/black"
                            android:textSize="@dimen/common_dp21"
                            android:textStyle="bold"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintLeft_toLeftOf="parent"
                            app:layout_constraintRight_toRightOf="parent" />

                        <com.dz.foundation.ui.widget.DzButton
                            android:id="@+id/btnSuccessOk"
                            android:layout_width="match_parent"
                            android:layout_height="@dimen/common_dp48"
                            android:layout_marginHorizontal="@dimen/common_dp24"
                            android:layout_marginBottom="@dimen/common_dp20"
                            android:gravity="center"
                            android:text="@string/web_go_to_mall"
                            android:textColor="@color/white"
                            android:textSize="@dimen/common_dp17"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:shape_radius="@dimen/common_dp32"
                            app:shape_solid_gradient_end_color="@color/common_FF005F"
                            app:shape_solid_gradient_orientation="left_right"
                            app:shape_solid_gradient_start_color="@color/common_FF2626" />

                        <com.dz.foundation.ui.widget.DzTextView
                            android:id="@+id/tvSuccessDec"
                            android:layout_width="match_parent"
                            android:layout_height="@dimen/common_dp20"
                            android:layout_marginBottom="@dimen/common_dp146"
                            android:gravity="center"
                            android:textColor="@color/common_FF7B8288"
                            android:textSize="@dimen/common_dp14"
                            app:layout_constraintBottom_toBottomOf="parent"
                            tools:text="成功浏览商城60S奖励" />

                        <com.dz.foundation.ui.widget.DzTextView
                            android:id="@+id/tvSuccessIconNum"
                            android:layout_width="wrap_content"
                            android:layout_height="@dimen/common_dp48"
                            android:layout_marginRight="@dimen/common_dp21"
                            android:layout_marginBottom="@dimen/common_dp88"
                            android:gravity="center"
                            android:text="1"
                            android:textColor="@color/common_FFFF045A"
                            android:textSize="@dimen/common_dp40"
                            android:textStyle="bold"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent" />

                        <com.dz.foundation.ui.widget.DzTextView
                            android:layout_width="@dimen/common_dp28"
                            android:layout_height="@dimen/common_dp20"
                            android:layout_marginLeft="@dimen/common_dp2"
                            android:layout_marginBottom="@dimen/common_dp92"
                            android:gravity="center"
                            android:text="@string/web_gold_coin"
                            android:textColor="@color/common_FFFF045A"
                            android:textSize="@dimen/common_dp14"
                            android:textStyle="bold"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintStart_toEndOf="@id/tvSuccessIconNum" />
                    </com.dz.foundation.ui.widget.DzConstraintLayout>

                </com.dz.foundation.ui.widget.DzConstraintLayout>

            </com.dz.foundation.ui.widget.DzConstraintLayout>


        </com.dz.foundation.ui.widget.DzConstraintLayout>

    </com.dz.business.base.ui.viewpager2.ViewPager2ChildFrameLayout>

</layout>