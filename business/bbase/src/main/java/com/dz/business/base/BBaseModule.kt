package com.dz.business.base


import com.dz.business.base.ui.component.AlertDialogComp
import com.dz.business.base.ui.component.CommonButtonDialogComp
import com.dz.business.base.ui.component.CommonDialogComp
import com.dz.business.base.ui.component.CommonImageDialogComp
import com.dz.business.base.ui.component.FlutterDialogComp
import com.dz.business.base.ui.component.PushDialog
import com.dz.business.base.ui.component.RecommendGuideComp
import com.dz.foundation.base.module.LibModule
import com.dz.foundation.router.registerTarget

/**
 *@Author: shidz
 *@Date: 2022/8/23 15:19
 *@Description: 业务模块基类
 *@Version:1.0
 */
class BBaseModule : LibModule() {

    override fun onCreate() {
        BBaseMR.get().apply {
            alertDialog().registerTarget(AlertDialogComp::class.java)
            commonAlertDialog().registerTarget(CommonDialogComp::class.java)
            commonButtonDialog().registerTarget(CommonButtonDialogComp::class.java)
            commonImageDialog().registerTarget(CommonImageDialogComp::class.java)
            flutterDialog().registerTarget(FlutterDialogComp::class.java)
            recommendGuide().registerTarget(RecommendGuideComp::class.java)
            pushOpenDialog().registerTarget(PushDialog::class.java)
        }
    }
}