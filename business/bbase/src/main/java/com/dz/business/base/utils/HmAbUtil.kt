package com.dz.business.base.utils

import BBaseME
import com.dz.business.base.data.BBaseKV
import com.dz.business.base.data.ab.config.MediaLoaderTestConfig
import com.dz.business.base.data.ab.config.WelfareTestConfig
import com.dz.business.base.data.ab.config.WidgetSwitchConfig
import com.dz.business.base.data.bean.ConfigBean
import com.dz.business.base.data.bean.TechnologyConfig
import com.dz.business.base.network.BBaseNetWork
import com.dz.business.base.network.HttpResponseModel
import com.dz.foundation.base.module.AppModule
import com.dz.foundation.base.utils.LogUtil
import com.dz.foundation.network.onEnd
import com.dz.foundation.network.onError
import com.dz.foundation.network.onResponse
import com.dz.platform.ab.AbSDK
import com.dz.platform.ab.DzAbConfig
import com.dz.platform.ab.bean.AbConfig
import com.dz.platform.ab.bean.BaseResult
import com.dz.platform.ab.inter.AbSdkListener
import com.dz.platform.ab.inter.ReqCallback
import com.dz.platform.ad.data.AdKV
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.util.concurrent.ConcurrentHashMap
import kotlin.math.roundToInt

/**
 * 河马业务 配置工具类
 * 规范:https://alidocs.dingtalk.com/i/nodes/MNDoBb60VL9MvdXAS4gyQxE7JlemrZQ3
 * 增加key或者修改key需要更新文档。维护文档
 */
object HmAbUtil {
    //配置获取对象
    private var dzAbConfig: DzAbConfig? = null

    const val TAG = "DzAbSdkConfig"

    private const val pollTime = "pollTime"//循环请求时间
    private const val hotStartReq = "hotStartReq"//热启动标识
    private const val detailConf = "detailConf"//配置内容

    //首页列表缓存的AB key
    const val TEST_KEY_HOME_LIST_CACHE = "homeListCache"

    //冷启动优化实验
    const val TEST_KEY_COLD_LAUNCH = "coldLaunch_Android"

    //大数据配置实验
    const val TEST_KEY_BIG_DATA_CONFIG = "bigDataConfig_Android"

    //多播优化实验
    const val TEST_KEY_MULTIPLE_INSTANCES_ANDROID = "multiple_Instances_Android"

    //Android设备评分合格分数线
    const val TEST_KEY_EQUIPMENT_PASS_SCORE = "equipment_pass_score"

    //mediaLoader开关
    const val TEST_KEY_MEDIA_LOADER_SWITCH = "mediaLoader_switch"

    // 广告快应用拦截开关
    private const val TEST_KEY_ENABLE_AD_HOOK_FASTAPP = "enable_ad_hook_fastapp"

    // 激励视频跨页面复用开关
    private const val TEST_KEY_ENABLE_REWARD_REUSE_CROSS_PAGE = "enable_reward_reuse_cross_page"

    //灰色模式开关
    const val TEST_KEY_GRAY_MODE = "grayMode_Android"

    //音频焦点暂停开关
    const val TEST_KEY_AUDIO_FOCUS_PAUSE_ENABLE = "audioFocusPauseEnable"

    //lottie动画渲染类型开关
    const val TEST_KEY_LOTTIE_RENDER_MODE_SWITCH = "lottieRenderModeSwitch"

    //下发缓存监控的实验。
    const val TEST_KEY_CACHE_SIZE_MONITOR = "hmCacheSizeMonitor_Android"

    //下发网赚组件是否更新定时任务业务开关
    private const val TEST_HARMONY_INCOME_WIDGET_SWITCH = "harmonyConfig_Android"

    // 一二级福利页复用开关
    private const val TEST_WELFARE_REUSE_ANDROID = "WelfareReuse_Android"

    //请求的key的配置
    private val requestKeys = mutableListOf(
        TEST_KEY_HOME_LIST_CACHE,
        TEST_KEY_COLD_LAUNCH,
        TEST_KEY_BIG_DATA_CONFIG,
        TEST_KEY_MULTIPLE_INSTANCES_ANDROID,
        TEST_KEY_EQUIPMENT_PASS_SCORE,
        TEST_KEY_MEDIA_LOADER_SWITCH,
        TEST_KEY_ENABLE_AD_HOOK_FASTAPP,
        TEST_KEY_GRAY_MODE,
        TEST_KEY_AUDIO_FOCUS_PAUSE_ENABLE,
        TEST_KEY_LOTTIE_RENDER_MODE_SWITCH,
        TEST_KEY_CACHE_SIZE_MONITOR,
        TEST_HARMONY_INCOME_WIDGET_SWITCH,
        TEST_WELFARE_REUSE_ANDROID,
        TEST_KEY_ENABLE_REWARD_REUSE_CROSS_PAGE
    )

    //初始化
    @Synchronized
    fun initSDK(configLoaded: () -> Unit) {
        CoroutineScope(Dispatchers.IO).launch {
            kotlin.runCatching {
                if (dzAbConfig == null) {
                    val config = requestMapConfig(requestKeys)?.data?.dataMap
                    if (dzAbConfig == null && config != null) {
                        LogUtil.d(TAG, "初始化成功")
                        //开启日志
                        AbSDK.setLogMode(false)
                        //初始化
                        dzAbConfig = AbSDK.initSDK(
                            hiveReportConfig = AbConfig(
                                businessSign = TAG,
                                configMap = config,
                                listener = listener,
                                pollTime = pollTime,
                                hotStartReq = hotStartReq,
                                detailConf = detailConf,
                            ),
                            context = AppModule.getApplication()
                        )
                        initConfigBean()
                    }
                }
                configLoaded.invoke()
            }.onFailure {
                it.printStackTrace()
            }
        }
    }

    /**
     * 初始化1156配置数据 代替1150的配置代码
     * */
    private fun initConfigBean() {
        val config = getConfigContentByKey<TechnologyConfig>(TEST_KEY_BIG_DATA_CONFIG)
        LogUtil.d(
            TAG,
            "上报神策打点 ${config?.bigDataUploadSwitch}"
        )
        //大数据上报配置
        config?.let {
            if (BBaseKV.bigDataUploadSwitch && it.bigDataUploadSwitch != true) {
                //开关从打开到关闭 上传所有数据，用旧的打点策略
                HmHiveSDK.trackDBData(HmHiveSDK.SwitchSwitching)
            }
            BBaseKV.bigDataUploadSwitch = it.bigDataUploadSwitch ?: true
            BBaseKV.realtime = it.realtime?.toJson() ?: "{}"
            BBaseKV.nonrealtime = it.nonrealtime?.toJson() ?: "{}"
            HmHiveSDK.changeConfig(BBaseKV.realtime, BBaseKV.nonrealtime)
        } ?: let {
            //配置为空或者服务端错误，直接关闭SDK的使用
            BBaseKV.bigDataUploadSwitch = false
        }

        // 广告快应用拦截开关
        getConfigContentByKey<MediaLoaderTestConfig>(TEST_KEY_ENABLE_AD_HOOK_FASTAPP)?.let {
            LogUtil.d("SkyLoader", "广告快应用拦截开关 $it")
            AdKV.enableAdHookFastApp = it.open
        } ?: let {
            AdKV.enableAdHookFastApp = false
        }

        // 激励视频跨页面复用开关
        getConfigContentByKey<MediaLoaderTestConfig>(TEST_KEY_ENABLE_REWARD_REUSE_CROSS_PAGE)?.let {
            LogUtil.d("SkyLoader", "激励视频跨页面复用开关 $it")
            AdKV.enableRewardAdReuseCrossPage = it.open
        } ?: let {
            AdKV.enableRewardAdReuseCrossPage = false
        }

        getConfigContentByKey<WidgetSwitchConfig>(TEST_HARMONY_INCOME_WIDGET_SWITCH)?.let {
            val value = it.harmonyWidgetSwitch
            BBaseKV.incomeWidgetSwitch = if (value == 1) 1 else 0
            LogUtil.d(TAG, "TEST_HARMONY_INCOME_WIDGET_SWITCH incomeWidgetSwitch: ${BBaseKV.incomeWidgetSwitch}   value: $value  it = $it  ")
        }

        // 福利页配置
        getConfigContentByKey<WelfareTestConfig>(TEST_WELFARE_REUSE_ANDROID)?.let {
            BBaseKV.welfareReuseSwitch = it.isReuse
        }

        // 灰色模式开关
        getConfigContentByKey<TechnologyConfig>(TEST_KEY_GRAY_MODE)?.let {
            LogUtil.d("GrayModeUtils", "已应用灰色主题，中心区域保持彩色 ${it.grayMode}")
            if (it.grayMode) {
                GrayModeUtils.isGrayModeEnabled = true
                BBaseME.get().appGrayMode().postSticky(true)
            }
        }

        // 音频焦点暂停开关
        getConfigContentByKey<TechnologyConfig>(TEST_KEY_AUDIO_FOCUS_PAUSE_ENABLE)?.let {
            LogUtil.d("PlayerDelegate", "失去音频焦点时是否暂停:${it.audioFocusPauseEnable}")
            BBaseKV.audioFocusPauseEnable = it.audioFocusPauseEnable
        }
        // lottie动画渲染模式开关
        getConfigContentByKey<TechnologyConfig>(TEST_KEY_LOTTIE_RENDER_MODE_SWITCH)?.let {
            LogUtil.d(
                "LottieRenderModeSwitch",
                "lottie动画加速模式开关:${it.lottieRenderModeSwitch}"
            )
            BBaseKV.lottieRenderModeSwitch = it.lottieRenderModeSwitch
        }
    }

    //1156接口获取自定义配置
    @Synchronized
    fun requestMapConfig(keys: List<String>? = null): HttpResponseModel<ConfigBean>? {
        var response: HttpResponseModel<ConfigBean>? = null
        BBaseNetWork.get().mapConfig().setParam(keys).onResponse {
            response = it
            LogUtil.d(TAG, "mapConfig==onResponse，onResponse=${it.data.toString()}")
            response?.data?.dataMap?.keys?.forEach { key ->
                if (key == TEST_KEY_MULTIPLE_INSTANCES_ANDROID) {
                    kotlin.runCatching {
                        response?.data?.dataMap?.get(key)?.let { item ->
                            if (item is Map<*, *> && item["detailConf"] is Map<*, *>) {
                                val data = item["detailConf"] as Map<*, String>
                                if (data.keys.contains("switch")) {
                                    val switch = data["switch"]
                                    BBaseKV.sensorNewIsMultipleInstances = when (switch) {
                                        "0" -> 0
                                        "1" -> 1
                                        "2" -> 2
                                        else -> 0
                                    }
                                    if (BBaseKV.sensorNewIsMultipleInstances == 2) {
                                        CommInfoUtil.attemptScore()
                                    }
                                    LogUtil.d(TAG,"多播试验开关为：${BBaseKV.sensorNewIsMultipleInstances}")
                                } else {
                                    BBaseKV.sensorNewIsMultipleInstances = 0
                                    LogUtil.d(TAG,"多播配置未获取到开关，多播试验开关为：${BBaseKV.sensorNewIsMultipleInstances}")
                                }
                            } else {
                                BBaseKV.sensorNewIsMultipleInstances = 0
                                LogUtil.d(TAG,"多播配置未获取到detailConf，多播试验开关为：${BBaseKV.sensorNewIsMultipleInstances}")
                            }
                        } ?: let {
                            BBaseKV.sensorNewIsMultipleInstances = 0
                            LogUtil.d(TAG,"多播配置未获取到，多播试验开关为：${BBaseKV.sensorNewIsMultipleInstances}")
                        }
                    }.onFailure {
                        BBaseKV.sensorNewIsMultipleInstances = 0
                        LogUtil.d(TAG,"多播配置获取异常，多播试验开关为：${BBaseKV.sensorNewIsMultipleInstances}")
                    }
                } else if (key == TEST_KEY_EQUIPMENT_PASS_SCORE) {
                    kotlin.runCatching {
                        response?.data?.dataMap?.get(key)?.let { item ->
                            if (item is Map<*, *> && item["detailConf"] is Map<*, *>) {
                                val data = item["detailConf"] as Map<*, Double>
                                if (data.keys.contains("score")) {
                                    val score = data["score"]
                                    score?.let { score ->
                                        BBaseKV.devicePassScore = score.roundToInt()
                                        LogUtil.d(
                                            TAG,
                                            "获取评分及格线成功：${BBaseKV.devicePassScore}"
                                        )
                                    }
                                }
                            }
                        }
                    }.onFailure {
                        LogUtil.d(TAG,"获取评分及格线失败：${BBaseKV.devicePassScore}")
                    }
                }
            }
            LogUtil.d(TAG, "mapConfig==onResponse，onResponse=${it.data.toString()}")
        }.onError {
            LogUtil.d(TAG, "mapConfig==onError，Error=${it.message}")
        }.onEnd {
            LogUtil.d(TAG, "mapConfig==onEnd")
        }.doSyncRequest2()
        return response
    }

    //热启动更新配置
    fun hotStartUpdateConfig() {
        LogUtil.d(TAG, "热启动更新配置")
        CoroutineScope(Dispatchers.IO).launch {
            runCatching {
                dzAbConfig?.hotStartUpdate()
                hotStartUpdateConfigWithKeys()
            }.onFailure {
                it.printStackTrace()
            }
        }
    }

    private fun hotStartUpdateConfigWithKeys() {
        LogUtil.d(TAG, "热启动获取到配置后更新数据")
        // 福利页配置
        getConfigContentByKey<WelfareTestConfig>(TEST_WELFARE_REUSE_ANDROID)?.let {
            BBaseKV.welfareReuseSwitch = it.isReuse
        }
    }

    //更新需要的配置
    fun refreshConfig() {
        CoroutineScope(Dispatchers.IO).launch {
            val config = requestMapConfig(requestKeys)?.data?.dataMap
            config?.let {
                LogUtil.d(TAG, "refreshConfig 更新需要的配置")
                dzAbConfig?.setConfig(it)
            }
        }
    }

    //根据key获取相应的配置
    private fun getConfigForKeys(): ConcurrentHashMap<String, ConcurrentHashMap<String, Any?>?>? {
        return dzAbConfig?.fetchConfigForKeys(requestKeys)
    }


    /**
     * 根据 key 获取配置对象
     * @param key Ab key
     * @return T? 期望返回的数据结构
     */
    inline fun <reified T> getConfigContentByKey(key: String): T? {
        val config = getConfigByKey(key) ?: return null
        return try {
            if (config.containsKey("detailConf") && config["detailConf"] is Map<*, *>) {
                val detailConf = config["detailConf"] as Map<*, *>
                GsonUtil.fromJson(GsonUtil.toJson(detailConf), T::class.java)
            } else null
        } catch (e: Exception) {
            LogUtil.e(TAG, "getConfigContentByKey error,message==${e.message}")
            e.printStackTrace()
            null
        }
    }

    /**
     * 根据key获取相应的AB配置
     * @param key String 实验key
     * @return ConcurrentHashMap<String, Any?>? 配置内容
     */
    fun getConfigByKey(key: String): ConcurrentHashMap<String, Any?>? {
        if (key.isBlank() || !requestKeys.contains(key)) {
            return null
        }
        return getConfigForKeys()?.get(key)
    }


    //监听打点
    private val listener = object : AbSdkListener {
        override fun configChange(key: String, map: ConcurrentHashMap<String, Any?>) {
            LogUtil.d("DzAbSdkConfig", "configChange==${key}")
//            ToastManager.showToast("配置更新==key=${key} value=${map}")
        }

        override fun requestData(config: AbConfig, keys: List<String>, callback: ReqCallback) {
            val response = requestMapConfig(keys)
            callback.onBack(
                BaseResult(
                    code = response?.code ?: -1,
                    message = response?.msg ?: "",
                    data = response?.userId ?: "",
                )
            )
            if (response?.code == 0) {
                response.data?.dataMap?.keys?.forEach {
                    dzAbConfig?.setConfigForKey(
                        it,
                        response.data?.dataMap?.get(it) ?: ConcurrentHashMap(),
                    )
                }
            }
        }

        override fun resultError(message: String, config: AbConfig, error: Throwable) {
            LogUtil.d("DzAbSdkConfig", "resultError==${message}")
//            ToastManager.showToast("有异常==message=${message}")
        }
    }

    //业务侧根据业务需求判断是否可以使用SDK
    fun canUseSDK(): Boolean {
        return (dzAbConfig != null)
    }

    //关闭循环获取配置
    fun stopTimer() {
        dzAbConfig?.stopCirculate()
    }


    //清空SDK缓存
    fun cleanSDK() {
        dzAbConfig?.clean()
    }

}