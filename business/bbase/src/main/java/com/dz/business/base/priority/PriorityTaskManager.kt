package com.dz.business.base.priority

import com.dz.foundation.base.utils.LocalActivityMgr
import com.dz.foundation.base.utils.LogUtil
import com.dz.platform.common.base.ui.UIPage

/**
 *@Author: zhanggy
 *@Date: 2024-07-26
 *@Description: 优先级任务执行器
 *@Version:1.0
 */
object PriorityTaskManager {

    private const val TAG = "PriorityTask"

    private var runningTasks = mutableMapOf<String, PriorityTask>()

    private val tasks = mutableSetOf<PriorityTask>()

    fun addTask(task: PriorityTask) {
        LogUtil.d(TAG, "addTask:$task")
        synchronized(tasks) {
            tasks.add(task)
        }
    }

    fun getTask(name: String): PriorityTask? = synchronized(tasks) {
        tasks.find { it.name == name }
    }

    fun executeTask(immediately: Boolean = false) {
        val currentPage = (LocalActivityMgr.getTopActivity() as? UIPage)?.getPageId()
        if (currentPage.isNullOrEmpty()) {
            LogUtil.e(TAG, "Task execute failed！Unknown page:$currentPage")
            return
        }
        val activityName = currentPage.split("/").first()
        synchronized(tasks) {
            val currentTask = runningTasks[activityName]
            if (immediately || currentTask?.condition(currentPage) != true) {
                currentTask?.let {
                    it.cancel()
                    it.finish()
                }
            }

            if (!immediately && currentTask != null) {
                LogUtil.d(TAG, "Has running task! ${currentTask.name}")
                return
            }
            LogUtil.d(TAG, "ExecuteTask page:$currentPage, all tasks:$tasks")
            tasks.asSequence()
                .filter { it.condition(currentPage) }
                .sortedBy { it.priority }
                .firstOrNull { it.status == PriorityConstants.STATUS_READY }
                ?.apply {
                    runningTasks[activityName] = this
                    status = PriorityConstants.STATUS_RUNNING
                    LogUtil.d(TAG, "$name has been executed")
                    action()
                }
        }
    }

    fun removeTask(name: String) {
        synchronized(tasks) {
            val keysToRemove = runningTasks.filter { it.value.name == name }.map { it.key }
            for (key in keysToRemove) {
                runningTasks.remove(key)?.cancel()
            }
            tasks.removeAll { it.name == name }
            LogUtil.d(TAG, "removeTask:$name")
        }
    }

    fun removeTaskByPageId(page: String?) {
        synchronized(tasks) {
            page?.split("/")?.let { pages ->
                val tasksToRemove = tasks.filter { task ->
                    task.pagePriorities.size == 1 &&
                            task.pagePriorities.keys.any { pages.contains(it) }
                }
                tasks.removeAll(tasksToRemove.toSet())
            }
            LogUtil.d(TAG, "Remove task belong to $page. Remaining tasks:$tasks")
        }
    }

    fun reset() {
        synchronized(tasks) {
            LogUtil.d(TAG, "reset")
            tasks.clear()
            runningTasks.values.forEach {
                it.cancel()
            }
            runningTasks.clear()
        }
    }

    /**
     * 手动刷新页面
     */
    fun onPageChanged() {
        val currentPageId = (LocalActivityMgr.getTopActivity() as? UIPage)?.getPageId().orEmpty()
        if (currentPageId.isEmpty()) {
            LogUtil.e(TAG, "Task execute failed！Unknown page:$currentPageId")
            return
        }
        val activityName = currentPageId.split("/").first()
        synchronized(tasks) {
            runningTasks[activityName]?.also { task ->
                if (!task.condition(currentPageId)) {
                    task.finish()
                }
            }
        }
    }
}