package com.dz.business.base

import com.dz.business.base.intent.AlertDialogIntent
import com.dz.business.base.intent.CommonAlertDialogIntent
import com.dz.business.base.intent.CommonButtonDialogIntent
import com.dz.business.base.intent.CommonImageDialogIntent
import com.dz.business.base.intent.FlutterDialogIntent
import com.dz.business.base.intent.RecommendGuideIntent
import com.dz.foundation.router.IModuleRouter
import com.dz.foundation.router.annotation.RouteAction
import com.dz.foundation.router.get

interface BBaseMR : IModuleRouter {
    companion object {
        //有标题 内容 弹窗
        const val ALERT_DIALOG = "alert_dialog"

        //只有标题弹窗
        const val COMMON_ALERT_DIALOG = "common_alert_dialog"

        //只有按钮弹窗
        const val COMMON_BUTTON_DIALOG = "common_button_dialog"

        //只有大图弹窗
        const val COMMON_IMAGE_DIALOG = "common_image_dialog"

        //Flutter 通用弹窗
        const val FLUTTER_DIALOG = "flutter_common_dialog"
        //推荐页点击弹窗
        const val RECOMMEND_GUIDE_DIALOG = "recommend_guide_dialog"

        //打开push弹窗
        const val PUSH_OPEN_DIALOG = "open_push_dialog"

        private val instance by lazy { BBaseMR::class.java.get() }
        fun get(): BBaseMR {
            return instance
        }
    }

    @RouteAction(ALERT_DIALOG)
    fun alertDialog(): AlertDialogIntent

    @RouteAction(COMMON_ALERT_DIALOG)
    fun commonAlertDialog(): CommonAlertDialogIntent

    /**
     * 通用按钮弹窗
     */
    @RouteAction(COMMON_BUTTON_DIALOG)
    fun commonButtonDialog(): CommonButtonDialogIntent

    /**
     * 通用图片弹窗
     */
    @RouteAction(COMMON_IMAGE_DIALOG)
    fun commonImageDialog(): CommonImageDialogIntent

    /**
     * 通用Flutter弹窗
     */
    @RouteAction(FLUTTER_DIALOG)
    fun flutterDialog(): FlutterDialogIntent

    /**
     * 推荐页点击弹窗
     */
    @RouteAction(RECOMMEND_GUIDE_DIALOG)
    fun recommendGuide(): RecommendGuideIntent

    /**
     * push开启弹窗
     */
    @RouteAction(PUSH_OPEN_DIALOG)
    fun pushOpenDialog(): AlertDialogIntent

}