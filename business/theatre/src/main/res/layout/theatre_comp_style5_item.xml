<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:ignore="MissingDefaultResource">

    <merge tools:parentTag="com.dz.foundation.ui.widget.DzConstraintLayout">

        <com.dz.foundation.ui.widget.DzConstraintLayout
            android:id="@+id/root"
            android:layout_width="@dimen/common_dp0"
            android:layout_height="wrap_content"
            android:maxWidth="@dimen/common_dp167.5"
            android:layout_marginBottom="@dimen/common_dp12"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:shape="rectangle"
            app:shape_solid_color="@color/common_FFFFFFFF"
            app:shape_left_bottom_radius="@dimen/common_dp6"
            app:shape_right_bottom_radius="@dimen/common_dp6"
            android:paddingBottom="@dimen/common_dp12"
            >

            <com.dz.foundation.ui.widget.DzImageView
                android:id="@+id/iv_cover"
                android:layout_width="match_parent"
                android:layout_height="@dimen/common_dp0"
                android:adjustViewBounds="true"
                android:scaleType="centerCrop"
                app:layout_constraintDimensionRatio="h,167.5:239.5"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.dz.foundation.ui.widget.DzImageView
                android:id="@+id/iv_tags"
                android:layout_width="@dimen/common_dp44"
                android:layout_height="@dimen/common_dp16"
                app:layout_constraintEnd_toEndOf="@+id/iv_cover"
                app:layout_constraintTop_toTopOf="@+id/iv_cover" />

            <com.dz.foundation.ui.widget.DzTextView
                android:id="@+id/tv_corner_mark"
                android:layout_width="@dimen/common_dp32"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/common_dp4"
                android:layout_marginEnd="@dimen/common_dp4"
                android:gravity="center"
                android:paddingStart="@dimen/common_dp6"
                android:paddingEnd="@dimen/common_dp6"
                android:textColor="@color/common_FFFFFF_FFFFFF"
                android:textSize="@dimen/common_dp10"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="@+id/iv_cover"
                app:layout_constraintTop_toTopOf="@+id/iv_cover"
                app:shape="rectangle"
                app:shape_radius="@dimen/common_dp2"
                app:shape_solid_color="@color/common_FFE1442E"
                tools:ignore="SpUsage"
                tools:text="在追" />

            <com.dz.foundation.ui.widget.DzView
                android:layout_width="@dimen/common_dp0"
                android:layout_height="@dimen/common_dp28"
                app:shape="rectangle"
                app:shape_solid_gradient_orientation="top_bottom"
                app:shape_solid_gradient_start_color="@color/common_transparent"
                app:shape_solid_gradient_end_color="@color/common_B3000000"
                app:layout_constraintBottom_toBottomOf="@+id/iv_cover"
                app:layout_constraintEnd_toEndOf="@+id/iv_cover"
                app:layout_constraintStart_toStartOf="@+id/iv_cover" />

            <com.dz.foundation.ui.widget.DzTextView
                android:id="@+id/tv_update"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/common_dp4"
                android:layout_marginBottom="@dimen/common_dp6"
                android:gravity="end"
                android:textColor="@color/common_FFFFFF_FFFFFF"
                android:textSize="@dimen/common_dp11"
                app:layout_constraintBottom_toBottomOf="@+id/iv_cover"
                app:layout_constraintEnd_toEndOf="@+id/iv_cover"
                tools:text="更新至100集" />

            <com.dz.foundation.ui.widget.DzTextView
                android:id="@+id/tv_name"
                android:layout_width="@dimen/common_dp0"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/common_dp8"
                android:layout_marginStart="@dimen/common_dp8"
                android:ellipsize="end"
                android:gravity="start"
                android:lines="1"
                android:textColor="@color/common_FF161718"
                android:textSize="@dimen/common_dp15"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="@+id/iv_cover"
                app:layout_constraintStart_toStartOf="@+id/iv_cover"
                app:layout_constraintTop_toBottomOf="@+id/iv_cover"
                tools:text="超级女保安" />

            <com.dz.foundation.ui.widget.DzTextView
                android:id="@+id/tv_mark"
                android:layout_width="@dimen/common_dp0"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/common_dp4"
                android:ellipsize="end"
                android:gravity="start"
                android:lines="1"
                android:textColor="@color/common_FF929AA1"
                android:textSize="@dimen/common_dp12"
                app:layout_constraintEnd_toEndOf="@+id/tv_name"
                app:layout_constraintStart_toStartOf="@+id/tv_name"
                app:layout_constraintTop_toBottomOf="@+id/tv_name"
                tools:text="逆袭 纯爱" />


            <com.dz.foundation.ui.widget.DzFrameLayout
                android:id="@+id/fl_chase"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingRight="@dimen/common_dp20"
                android:paddingBottom="@dimen/common_dp20"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <com.dz.foundation.ui.widget.DzImageView
                    android:id="@+id/iv_chase"
                    android:layout_width="@dimen/common_dp20"
                    android:layout_height="@dimen/common_dp20"
                    android:layout_marginStart="@dimen/common_dp4"
                    android:layout_marginTop="@dimen/common_dp4"
                    android:src="@drawable/theatre_ic_chase_star_normal" />

            </com.dz.foundation.ui.widget.DzFrameLayout>


        </com.dz.foundation.ui.widget.DzConstraintLayout>
    </merge>
</layout>