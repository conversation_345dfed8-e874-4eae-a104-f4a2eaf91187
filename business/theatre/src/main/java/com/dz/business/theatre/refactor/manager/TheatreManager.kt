package com.dz.business.theatre.refactor.manager

import android.net.Uri
import androidx.lifecycle.MutableLiveData
import com.dz.business.base.BBaseMC
import com.dz.business.base.data.BBaseKV
import com.dz.business.base.data.bean.BaseBookInfo
import com.dz.business.base.detail.DetailMR
import com.dz.business.base.detail.intent.VideoListIntent
import com.dz.business.base.reader.ReaderMS
import com.dz.business.base.theatre.data.BookInfoVo
import com.dz.business.base.theatre.data.RankInfoVO
import com.dz.business.base.theatre.data.ReservationInfoVo
import com.dz.business.base.utils.CommInfoUtil
import com.dz.business.theatre.refactor.component.TheatreCardFactory.CARD_TYPE_NOVEL_MORE
import com.dz.business.theatre.refactor.component.TheatreCardFactory.CARD_TYPE_NOVEL_RANKING
import com.dz.business.theatre.refactor.component.TheatreCardFactory.CARD_TYPE_OPERATION
import com.dz.business.theatre.refactor.component.TheatreCardFactory.CARD_TYPE_RANKING_SINGLE_COLUMN
import com.dz.business.theatre.refactor.network.bean.ColumnDataItem
import com.dz.business.theatre.refactor.network.bean.ImageInfoVo
import com.dz.business.theatre.refactor.network.bean.TheatreInfo
import com.dz.business.theatre.util.ChannelFilterMenuCacheUtil
import com.dz.business.track.base.addParam
import com.dz.business.track.events.DzTrackEvents
import com.dz.business.track.trace.OmapNode
import com.dz.business.track.trace.SourceNode
import com.dz.business.track.tracker.Tracker
import com.dz.foundation.base.utils.LogUtil
import com.dz.platform.common.router.SchemeRouter
import com.therouter.TheRouter
import org.json.JSONObject
import java.util.concurrent.ConcurrentHashMap

// 处理打点和刷新枚举
object TheatreManager {
    val TAG = "TheatreManager"

    val firstRenderComplete: MutableLiveData<Boolean> = MutableLiveData<Boolean>()


    enum class RefreshType(val value: Int) {
        refresh(1), // 1：下拉刷新；
        load(2), // 2：启动App
        refreshError(3), // 3：错误页点按钮刷新
        check(4), // 4：个性化开关
        channel(5), // 5：切换tab
        login(6), // 6：登录后刷新
        logout(7), // 7：退出登录后刷新
        ocpc(8), // 8：ocpc拉起后刷新
        mainTabbar(9), // 9：点击底bar首页或剧场按钮刷新
        autoRefreshOnPageshow(10), // 10：剧场离开当前页面后返回，满足时间间隔，自动刷新
        userPreferences(11), // 11：看剧喜好修改
        forceLaunch(13), // 13：热转冷
        filter(14),//筛选按钮选择的标签
        loadPage(100), // 100：翻页加载刷新
        wait(200), // 200：未刷新
    }

    enum class RefreshResult(val value: Int) {
        success(1),  // 1：成功返回结果
        empty(2),  // 2：结果为空
        error(3) //3：接口请求失败
    }

    //首页VM大量数据会导致MainActivity内存问题。拆出来使用。
    var theatreInfo: TheatreInfo? = null

    /**
     * item点击跳转二级播放器
     * @param bookInfoVo
     */
    fun onItemClick(
        bookInfoVo: BookInfoVo?,
        thirdTierPlay: String? = null
    ) {
        kotlin.runCatching {
            var intent: VideoListIntent? = null
            if (CommInfoUtil.isTeenMode()) {  // 青少年模式
                intent = DetailMR.get().videoListTeen()
            } else {
                intent = DetailMR.get().videoList()
                val jsonObject = JSONObject().apply {
                    put("\$element_id", (bookInfoVo?.elementId ?: "").toString())
                    put("\$element_type", bookInfoVo?.styleTypeCn ?: "")
                    put("\$element_content", bookInfoVo?.bookName)
                    put(
                        "\$element_position",
                        bookInfoVo?.columnTitle
                            ?: "${bookInfoVo?.styleTypeCn}${bookInfoVo?.columnPos}"
                    )
                    put("\$screen_name", "剧场")
                    put("\$title", "剧场")
                    put("Origin", bookInfoVo?.groupName)
                    put("BookID", bookInfoVo?.bookId ?: "")
                    put("BookName", bookInfoVo?.bookName ?: "")
                    put("ColumnName", bookInfoVo?.channelName ?: "")
                    put("RankName", bookInfoVo?.rankActionTips ?: "")
                    put("RankType", (bookInfoVo?.rankId ?: "").toString())
                    put("recReasonType", (bookInfoVo?.recReasonType ?: "").toString())
                    put("recReason", (bookInfoVo?.recReason ?: "").toString())
                }
                Tracker.trackToSensor("\$AppClick", jsonObject)
            }
            intent.apply {
                channelGroupId = (bookInfoVo?.groupId ?: "").toString()
                val subItem =
                    ChannelFilterMenuCacheUtil.channelGroupId2SubItem(channelGroupId?.toLongOrNull())
                if (subItem != null) {
                    originName = "剧场" //对应神策 Origin 字段
                    secondTierPlaySource = "剧场-分类筛选"
                    channelName = "剧场-分类筛选"//频道名称 对应神策 ColumnName 字段
                    thirdTierPlaySource = "分类筛选-${subItem.name}"
                } else {
                    originName = BBaseMC.origin_name_nsc
                    secondTierPlaySource = "剧场-${bookInfoVo?.groupName}"
                    channelName = bookInfoVo?.channelName//频道名称
                    thirdTierPlaySource = thirdTierPlay
                        ?: if (bookInfoVo?.channelName.isNullOrBlank()) "剧场-${bookInfoVo?.groupName}" else "${bookInfoVo?.groupName}-${bookInfoVo?.channelName}"
                }
                type = 0
                bookId = bookInfoVo?.bookId
                //   bookName = bookInfoVo?.bookName
                chapterId = bookInfoVo?.chapterId
//                chapterIndex = bookInfoVo?.chapterIndex
                updateNum = bookInfoVo?.updateNum
                videoStarsNum = bookInfoVo?.videoStarsNum
                playPosition = bookInfoVo?.progress
                firstPlaySource = "nsc_${bookInfoVo?.columnId}"
                firstTierPlaySource = "剧场"
                channelGroupPos = (bookInfoVo?.groupPos ?: "").toString()
                channelGroupName = bookInfoVo?.groupName
                bookInfoVo?.omap?.run {
                    scene = "nsc_${bookInfoVo.columnId}"
                    originName = BBaseMC.origin_name_nsc
                    channelName = bookInfoVo.channelName
                }
                cOmap = bookInfoVo?.omap
                contentPos = bookInfoVo?.contentPos//content位置
                origin = BBaseMC.origin_nsc//场景
                channelId = bookInfoVo?.channelId?.takeIf { it > 0 }?.toString() ?: ""//频道id
                channelPos = bookInfoVo?.channelPos//频道位置
                columnId = bookInfoVo?.columnId//栏目id
                columnName = bookInfoVo?.columnTitle//栏目名称
                columnPos = bookInfoVo?.columnPos//栏目位置
                backToRecommend = false
                fromType = bookInfoVo?.fromType

                playletPosition = bookInfoVo?.playletPosition ?: -1
                recPageNum = bookInfoVo?.recPageNum ?: -1
                isRecPlaylet = bookInfoVo?.isRecPlaylet ?: -1
                bookIndex = (bookInfoVo?.mPosition ?: 1) - 1
                recReasonType = bookInfoVo?.recReasonType
                recReason = bookInfoVo?.recReason
                rankId = bookInfoVo?.rankId
                rankActionTips = bookInfoVo?.rankActionTips
                showInfoType = bookInfoVo?.showInfoType
            }.start()
        }.onFailure {
            it.printStackTrace()
        }
    }

    fun onNovelClick(
        bookInfoVo: BookInfoVo?
    ) {
        kotlin.runCatching {
            val source = when (bookInfoVo?.styleType) {
                CARD_TYPE_NOVEL_MORE -> {
                    "xs_nsc_cnxh"
                }

                CARD_TYPE_NOVEL_RANKING -> {
                    "xs_nsc_${bookInfoVo.columnPos}-${bookInfoVo.rankPos}"
                }

                else -> {
                    "xs_nsc_${bookInfoVo?.columnPos ?: 0}"
                }
            }
            LogUtil.d("columnTitle", "bookInfoVo = $bookInfoVo}")
            BBaseKV.chooseNovelColumnName = bookInfoVo?.novelColumnName ?: ""
            BBaseKV.chooseNovelOrigin = bookInfoVo?.channelGroupName ?: ""
            LogUtil.d(
                "columnTitle",
                "chooseNovelColumnName = ${BBaseKV.chooseNovelColumnName}  bookInfoVo?.columnName?:  = ${BBaseKV.chooseNovelOrigin} ,bookInfoVo=$bookInfoVo "
            )
            TheRouter.build("flutter/container?url=flutter/BookDetailIndexPage").withSerializable(
                "url_param", hashMapOf(
                    "bookId" to bookInfoVo?.bookId,
                    "origin" to SourceNode.origin_xs_nsc,
                    "origin_name" to SourceNode.origin_name_xs_nsc,
                    "channel_id" to bookInfoVo?.channelId,
                    "channel_name" to bookInfoVo?.channelName,
                    "channel_pos" to bookInfoVo?.channelPos,
                    "column_id" to bookInfoVo?.columnId,
                    "column_name" to bookInfoVo?.columnName,
                    "column_pos" to bookInfoVo?.columnPos,
                    "firstPlaySource" to source,//xs_nsc_动态栏目id xs_nsc_cnxh
                    "book_type" to SourceNode.BOOK_TYPE_XS,

                    //2.5.0 tts新增参数，用以区分进入阅读器的来源
                    "reader_first_reading_source" to "剧场-${bookInfoVo?.groupName}",
                    "reader_second_reading_source" to "${bookInfoVo?.groupName}-${bookInfoVo?.channelName}",
                    "reader_third_reading_source" to "${bookInfoVo?.channelName ?: bookInfoVo?.groupName}-${bookInfoVo?.channelName}",
                    "reader_column_id" to bookInfoVo?.columnId,
                    "reader_column_pos" to bookInfoVo?.columnPos,
                    "audio_column_name" to bookInfoVo?.channelName,

                )
            ).navigation()
        }.onFailure {
            it.printStackTrace()
        }
    }

    // 排行榜链接点击
    fun onRankUrlClick(bookData: BookInfoVo?) {
        kotlin.runCatching {
            val jsonObject = JSONObject().apply {
                put("\$element_id", (bookData?.elementId ?: "").toString())
                put("\$element_type", "排行榜")
                put("\$element_content", bookData?.bookName)
                put("\$element_position", bookData?.styleTypeCn)
                put("\$screen_name", "剧场")
                put("\$title", "剧场")
                put("Origin", bookData?.groupName)
                put("BookID", bookData?.bookId ?: "")
                put("BookName", bookData?.bookName ?: "")
                put("ColumnName", bookData?.columnName ?: "")
                put("RecReasonStyle", bookData?.recReasonType ?: "")
                put("RecReasonText", bookData?.recReason ?: "")
                put("RankName", bookData?.rankActionTips ?: "")
                put("RankType", (bookData?.rankId ?: "").toString())
            }
            Tracker.trackToSensor("\$AppClick", jsonObject)
            SchemeRouter.doUriJump(bookData?.rankAction)
        }.onFailure { it.printStackTrace() }
    }

    //曝光
    fun onExpose(list: MutableSet<Any>) {
        for (item in list) {
            if (item is BookInfoVo) {
                if (item.statusTips != null || item.bookScore != null) {
                    exposeNovel(item)
                } else {
                    exposeBook(item)
                }
            } else if (item is ColumnDataItem) {
                if (item.styleType == CARD_TYPE_OPERATION) {
                    item.imageData?.forEach {
                        exposeImage(it)
                    }
                } else if (item.styleType == CARD_TYPE_RANKING_SINGLE_COLUMN) {
                    val position = "${item.columnPos}-0"
                    exposeRankingCard(columnDataItem = item, position = position)
                }
            } else if (item is List<*>) {
                item.forEach {
                    if (it is BookInfoVo) {
                        exposeNovel(it)
                    }
                }
            } else if (item is RankInfoVO) {
                val position = "${item.columnPos}-${item.listIndex}"
                exposeRankingCard(rankInfo = item, position = position)
            }
        }
    }

    fun exposeBook(item: BookInfoVo) {
        if (item.isExposed != true) {
            if (item is ReservationInfoVo) {
                exposeReservation(item)
            } else if (item.bookType != 0) {
                exposeOperation(item, if (item.bookType == 1) "剧单" else "排行榜")
            } else {
                item.isExposed = true
                LogUtil.d(
                    TAG,
                    "剧的卡片曝光: bookname=${item.bookName},styleTypeCn=${item.styleTypeCn} ${item.bookType} omap=${item.omap.toString()}   is_catch=${item.is_cache}  item.getTags()=${item.getTags()}  item.getTagIds()=${item.getTagIds()}"
                )

                DzTrackEvents.get().bookViewShow().origin(SourceNode.origin_nsc)
                    .addParam("origin_name", SourceNode.origin_name_nsc)
                    .addParam("channel_group_id", item.groupId ?: "")
                    .channelGroupName(item.groupName).addParam("channel_group_pos", item.groupPos)
                    .addParam("channel_id", item.channelId?.takeIf { it > 0 }?.toString() ?: "")
                    .addParam("channel_name", item.channelName)
                    .addParam("channel_pos", (item.channelPos ?: "").toString())
                    .addParam("column_id", item.columnId).addParam("column_name", item.columnName)
                    .columnName(item.channelName)
                    .addParam("column_pos", (item.columnPos ?: "").toString()).bookId(item.bookId)
                    .bookName(item.bookName).bookIndex(item.accumulatePosition)
                    .playletPosition(if (item.playletPosition == -1) null else item.playletPosition)
                    .recPageNum(if (item.recPageNum == -1) null else item.recPageNum).addParam(
                        "isRecPlaylet", if (item.isRecPlaylet == -1) null else item.isRecPlaylet
                    ).addParam("IsNewContent", item.isNewVideo())
                    .addParam("cpPartnerId", (item.cpPartnerId ?: "").toString())
                    .addParam("\$title", "剧场")
                    .addParam("ContentSource", (item.fromType ?: "").toString())
                    .recReasonStyle(item.recReasonType).recReasonText(item.recReason)
                    .rankName(item.rankActionTips)
                    .addParam("ContentType", item.mark ?: "")
                    .addParam("RankType", (item.rankId ?: "").toString())
                    .cornerMarkType(item.customIconType)
                    .cornerMarkName(item.iconType, item.iconName).track()
                DzTrackEvents.get().hiveExposure().show(OmapNode().apply {
                    origin = SourceNode.origin_nsc
                    originName = SourceNode.origin_name_nsc
                    channelId = item.channelId?.takeIf { it > 0 }?.toString() ?: ""
                    channelName = (item.channelName ?: "").toString()
                    channelPos = (item.channelPos ?: "").toString()
                    columnId = (item.columnId ?: "").toString()
                    columnName = (item.columnName ?: "").toString()
                    columnPos = (item.columnPos ?: "").toString()
                    contentId = (item.bookId ?: "").toString()
                    contentName = (item.bookName ?: "").toString()
                    contentPos = (item.mPosition ?: 1) - 1
                    playletId = (item.bookId ?: "").toString()
                    playletName = (item.bookName ?: "").toString()
//                    firstPlaySource = "nsc_${item.columnId}"
//                    lastPlaySource = "nsc_${item.columnId}"
                    strategyId = (item.omap?.strategyId ?: "").toString()
                    strategyName = (item.omap?.strategyName ?: "").toString()
                    channelGroupName = item.groupName
                    channelGroupPos = (item.groupPos ?: "").toString()
                    channelGroupId = (item.groupId ?: "").toString()
                    playletSrcType = item.fromType
                    bookIndex = item.accumulatePosition
                    playlet_id = item.bookId
                    playletPosition = item.playletPosition.toString()
                    recPageNum = item.recPageNum.toString()
                    isRecPlaylet = item.isRecPlaylet.toString()
                    followNum = item.videoStarsNumActual
                    //12月4日 新增大数据打点
                    firstCanFree = item.firstCanFree ?: ""
                    tag = item.getTags()
                    tagId = item.getTagIds()
                    finishStatus = item.finishStatusCn ?: ""
                    is_cache = if (item.is_cache == true) "1" else "0"
                    partnerId = (item.cpPartnerId ?: "").toString()
                    setStrategyInfo(item.omap)
                    when (item.showInfoType) {
                        BaseBookInfo.SHOW_INFO_RANK -> {  // 排行版
                            is_rec_content = 0
                            rec_reason_content = ""
                            is_top_list = 1
                            top_list = item.rankActionTips
                        }

                        BaseBookInfo.SHOW_INFO_RECOMMEND_REASON -> {  // 推荐理由
                            is_rec_content = 1
                            rec_reason_content = item.recReason
                            is_top_list = 0
                            top_list = ""
                        }

                        else -> {
                            is_rec_content = 0
                            rec_reason_content = ""
                            is_top_list = 0
                            top_list = ""
                        }
                    }
                }).track()
            }
        }
    }

    //小说曝光
    val exposureCache = ConcurrentHashMap<Long?, MutableList<String>>()
    fun exposeNovel(item: BookInfoVo) {
        if (item.isExposed != true) {
            var catchId: String? = (item.rankId ?: "").toString()
            if (catchId.isNullOrBlank()) {
                catchId = item.columnId
            }
            catchId?.let { catchId ->
                LogUtil.d(
                    TAG,
                    "小说的卡片曝光: columnId=${catchId},exposureCache=${
                        exposureCache[item.channelId]?.contains(
                            catchId
                        )
                    }  ${exposureCache.toString()} "
                )
                if (exposureCache[item.channelId]?.contains(catchId) != true) {
                    if (exposureCache[item.channelId] == null) {
                        exposureCache[item.channelId] = mutableListOf()
                    }
                    exposureCache[item.channelId]?.add(catchId)
                    kotlin.runCatching {
                        val jsonObject = JSONObject().apply {
                            put("Origin", "xs_${SourceNode.origin_nsc}")
                            put("origin_name", "小说书城")
                            put("channel_id", item.channelId?.takeIf { it > 0 }?.toString() ?: "")
                            put("channel_name", item.channelName)
                            put("channel_pos", (item.channelPos ?: "").toString())
                            put("column_id", catchId)
                            put("column_name", item.rankTitle ?: item.columnName)
                            put("column_pos", (item.rankPos ?: item.columnPos ?: "").toString())
                        }
                        Tracker.trackToSensor("ColumnShow", jsonObject)
                    }.onFailure { e ->
                        e.printStackTrace()
                    }
                }
            }
            item.isExposed = true
            LogUtil.d(
                TAG,
                "小说的卡片曝光: bookname=${item.bookName},styleTypeCn=${item.styleTypeCn} ${item.bookType}  item.channelPos=${item.channelPos} item.channelName=${item.channelName}  item.columnPos=${item.columnPos} omap=${item.omap.toString()}  is_catch=${item.is_cache}"
            )
            DzTrackEvents.get().bookViewShow().origin("xs_${SourceNode.origin_nsc}")
                .addParam("origin_name", "小说书城").channelGroupId((item.groupId ?: "").toString())
                .channelGroupName(item.groupName).channelGroupPos(item.groupPos.toString())
                .addParam("channel_id", item.channelId?.takeIf { it > 0 }?.toString() ?: "")
                .addParam("channel_name", item.channelName)
                .addParam("channel_pos", (item.channelPos ?: "").toString())
                .addParam("column_id", (item.rankId ?: item.columnId ?: "").toString())
                .addParam("column_name", (item.rankTitle ?: item.channelName ?: "").toString())
                .addParam("ContentType", item.mark ?: "")
                .columnName(item.channelName)
                .addParam(
                    "column_pos",
                    "${item.columnPos ?: ""}${item.rankPos?.let { "-$it" } ?: ""}")
                .bookId(item.bookId)
                .bookName(item.bookName)
                .playletPosition(item.playletPosition)
                .addParam("\$title", "剧场")
                .cornerMarkType(item.customIconType)
                .cornerMarkName(item.iconType, item.iconName)
                .track()
            DzTrackEvents.get().hiveExposure().show(OmapNode().apply {
                origin = "xs_${SourceNode.origin_nsc}"
                originName = "小说书城"
                tagId = item.getTagId()
                sceneId = item.omap?.sceneId ?: ""
                channelId = item.channelId?.takeIf { it > 0 }?.toString() ?: ""//频道id
                channelName = (item.channelName ?: "").toString()
                channelPos = (item.channelPos ?: "").toString()
                columnId = (item.rankId ?: item.columnId ?: "").toString()
                columnName = (item.rankTitle ?: item.channelName ?: "").toString()
                columnPos =
                    "${item.columnPos ?: ""}${item.rankPos?.let { "-$it" } ?: ""}"
                contentId = (item.bookId ?: "").toString()
                contentName = (item.bookName ?: "").toString()
                contentPos = item.listIndex - 1
                playletId = (item.bookId ?: "").toString()
                playletName = (item.bookName ?: "").toString()
//                firstPlaySource = "nsc_${item.columnId}"
                finishStatus = item.statusTips ?: ""
                strategyId = (item.omap?.strategyId ?: "").toString()
                strategyName = (item.omap?.strategyName ?: "").toString()
                channelGroupName = item.groupName
                channelGroupPos = (item.groupPos ?: "").toString()
                channelGroupId = (item.groupId ?: "").toString()
                playletSrcType = item.fromType
                bookIndex = item.accumulatePosition
                playlet_id = item.bookId
                playletPosition = item.playletPosition.toString()
                recPageNum = item.recPageNum.toString()
                isRecPlaylet = item.isRecPlaylet.toString()
                //12月4日 新增大数据打点
                firstCanFree = item.firstCanFree ?: ""
                tag = item.getTags()
                finishStatus = item.statusTips ?: item.finishStatusCn ?: ""
                is_cache = if (item.is_cache == true) "1" else "0"
                partnerId = (item.cpPartnerId ?: "").toString()
                setStrategyInfo(item.omap)
            }).track()
        }
    }


    /**
     * 排行榜新样式卡片曝光
     */
    fun exposeRankingCard(
        rankInfo: RankInfoVO? = null, columnDataItem: ColumnDataItem? = null,
        position: String? = null
    ) {
        LogUtil.d(
            "XXX",
            "排行榜曝光:rankInfo=$rankInfo,expose=${rankInfo?.isExposed}, columnDataItem=$columnDataItem,expose=${columnDataItem?.isExposed}"
        )
        kotlin.runCatching {
            // 处理 RankInfoVO 曝光
            rankInfo?.let { info ->
                if (info.isExposed != true) {
                    info.isExposed = true
                    val jsonObject = JSONObject().apply {
                        put("OperationName", "排行榜多列")
                        put("OperationType", "排行榜多列")
                        put("OperationPosition", position)
                    }
                    Tracker.trackToSensor("OperationExposure", jsonObject)
                }
            }

            // 处理 ColumnDataItem 曝光
            columnDataItem?.let { item ->
                if (item.isExposed != true) {
                    item.isExposed = true
                    val jsonObject = JSONObject().apply {
                        put("OperationName", "排行榜单行")
                        put("OperationType", "排行榜单行")
                        put("OperationPosition", position)
                    }
                    Tracker.trackToSensor("OperationExposure", jsonObject)
                }

            }

        }.onFailure { e ->
            e.printStackTrace()
        }
    }


    fun exposeImage(it: ImageInfoVo) {
        if (it.isExposed != true) {
            it.isExposed = true
            LogUtil.d(
                TAG, "运营位的图片曝光: bookname=${it.jumpUrl},styleTypeCn=${it.img}"
            )
            DzTrackEvents.get().operationExposureTE().positionName("剧场")
                .operationName("剧场图片栏目").operationPosition("剧场图片栏目")
                .operationType(getOperationType(it.jumpUrl)).track()
        }
    }

    private fun getOperationType(jumpUrl: String?): String {
        if (!jumpUrl.isNullOrEmpty()) {
            val uri = Uri.parse(jumpUrl)
            val queryParameters =
                uri.queryParameterNames.associateWith { uri.getQueryParameter(it) }
            return when (queryParameters["action"] ?: "") {
                "flutter/HistoryHomePage" -> "跳转历史记录"
                "web" -> "跳转福利中心"
                "recharge" -> "跳转充值页"
                else -> ""
            }
        }
        return ""
    }

    fun exposeOperation(item: BookInfoVo, operationType: String) {
        LogUtil.d(
            TAG,
            "运营位的卡片曝光: operationType=${operationType},styleTypeCn=${item.styleTypeCn} item.isExposed=${item.isExposed}"
        )
        if (item.isExposed != true) {
            item.isExposed = true
            var operationPosition: String = "${item.columnPos}-${(item.mPosition ?: 1) - 1}"
            if (!item.columnName.isNullOrBlank()) {
                operationPosition = "${item.columnName}-$operationPosition"
            }

            var positionName: String = "剧场"
            if (!item.groupName.isNullOrBlank()) {
                positionName += "-${item.groupName}"
            }
            if (!item.channelName.isNullOrBlank()) {
                positionName += "-${item.channelName}"
            }
            DzTrackEvents.get().operationExposureTE().operationName("剧场信息流中插")
                .operationPosition(operationPosition).operationType(operationType)
                .positionName(positionName).addParam(
                    "ButtonContent",
                    if (operationType == "剧单") item.playlistInfo?.buttonName else null
                ).addParam(
                    "OperatioTitle",
                    if (operationType == "剧单") item.playlistInfo?.title else item.rankInfo?.title
                        ?: ""
                ).addParam(
                    "ActID", if (operationType == "剧单") item.playlistInfo?.playlistId else null
                ).addParam("BookName", if (operationType == "剧单") item.bookName else null)
                .addParam("BookID", if (operationType == "剧单") item.bookId else null).track()
        }
    }

    //预约剧卡片曝光
    private fun exposeReservation(item: ReservationInfoVo) {
        LogUtil.d(
            TAG,
            "预约剧卡片曝光: bookName=${item.bookName},styleTypeCn=${item.styleTypeCn} item.isExposed=${item.isExposed}"
        )
        if (item.isExposed != true) {
            item.isExposed = true
            DzTrackEvents.get().bookViewShow().origin(SourceNode.origin_nsc)
                .addParam("origin_name", SourceNode.origin_name_nsc)
                .addParam("channel_group_id", item.groupId ?: "")
                .channelGroupName(item.groupName)
                .addParam("channel_group_pos", item.groupPos)
                .addParam("channel_id", item.channelId?.takeIf { it > 0 }?.toString() ?: "")
                .addParam("channel_name", item.channelName)
                .addParam("channel_pos", (item.channelPos ?: "").toString())
                .addParam("column_id", item.columnId).addParam("column_name", item.columnName)
                .columnName(item.channelName)
                .addParam("column_pos", (item.columnPos ?: "").toString())
                .bookId(item.bookId)
                .bookName(item.bookName)
                .bookIndex(item.mPosition)
                .addParam("cpPartnerId", (item.cpPartnerId ?: "").toString())
                .rankName(item.rankActionTips)
                .addParam("RankType", (item.rankId ?: "").toString())
                .bookStatus("可预约")
                .addParam("\$title", "剧场")
                .track()
        }
    }

    //运营位点击
    fun clickOperation(item: BookInfoVo, operationType: String) {
        LogUtil.d(
            TAG, "运营位的卡片点击: bookname=${item.bookName},styleTypeCn=${item.styleTypeCn}"
        )
        var operationPosition: String = "${item.columnPos}-${(item.mPosition ?: 1) - 1}"
        if (!item.columnName.isNullOrBlank()) {
            operationPosition = "${item.columnName}-$operationPosition"
        }

        var positionName: String = "剧场"
        if (!item.groupName.isNullOrBlank()) {
            positionName += "-${item.groupName}"
        }
        if (!item.channelName.isNullOrBlank()) {
            positionName += "-${item.channelName}"
        }
        DzTrackEvents.get().operationClickTE().operationName("剧场信息流中插")
            .operationPosition(operationPosition).operationType(operationType)
            .positionName(positionName).addParam(
                "ButtonContent",
                if (operationType == "剧单") item.playlistInfo?.buttonName else null
            ).addParam(
                "OperatioTitle",
                if (operationType == "剧单") item.playlistInfo?.title else item.rankInfo?.title
                    ?: ""
            )
            .addParam("ActID", if (operationType == "剧单") item.playlistInfo?.playlistId else null)
            .addParam("BookName", if (operationType == "剧单") item.bookName else null)
            .addParam("BookID", if (operationType == "剧单") item.bookId else null).track()
        SchemeRouter.doUriJump(if (operationType == "剧单") item.playlistInfo?.deeplink else item.rankInfo?.deeplink)
    }

    //轮播卡轮播打点去重曝光记录
    val bannerAlreadyTrackPositionSet = ConcurrentHashMap<Long?, MutableList<String>>()

    //服务端下发的提示语
    var tipMessage: String? = null

    //有声书点击
    fun onAudioClick(mData: BookInfoVo?) {

        val columnName = mData?.columnName?.takeUnless { it.isBlank() } ?: "剧场-有声书"
        val routeSource = JSONObject()
        routeSource.put("reader_first_reading_source", "剧场")
        routeSource.put("reader_second_reading_source", "剧场-有声书")
        routeSource.put("reader_third_reading_source", columnName)

        ReaderMS.get()?.openAudioPage(
            bookId = mData?.bookId ?: "",
            chapterId = mData?.chapterId,
            bookSource = routeSource.toString(),
            bookCover = mData?.coverWap,
        )
    }
}
