package com.dz.business.theatre.refactor.component.doubleCardComp

import android.content.Context
import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.util.AttributeSet
import android.view.View
import androidx.core.graphics.ColorUtils
import androidx.recyclerview.widget.RecyclerView
import com.dz.business.base.theatre.data.BookInfoVo
import com.dz.business.theatre.R
import com.dz.business.theatre.databinding.TheatreCompDoubleCardDramaListItemBinding
import com.dz.business.theatre.refactor.manager.TheatreManager
import com.dz.foundation.base.utils.ScreenUtil
import com.dz.foundation.base.utils.dp
import com.dz.foundation.imageloader.loadRoundImg
import com.dz.foundation.ui.utils.ShapeDrawableUtil
import com.dz.foundation.ui.view.recycler.DzRecyclerView
import com.dz.platform.common.base.ui.component.UIConstraintComponent

/**
 * 双卡样式-播单卡片
 */
class DoubleCardDramaListItemComp :
    UIConstraintComponent<TheatreCompDoubleCardDramaListItemBinding, BookInfoVo> {

    @JvmOverloads
    constructor(
        context: Context,
        attrs: AttributeSet? = null,
        defStyleAttr: Int = 0,
    ) : super(context, attrs, defStyleAttr)

    override fun initData() {

    }

    override fun initView() {

    }

    override fun initListener() {
        mViewBinding.rootLayout.registerClickAction {
            mData?.let {
                onCoverClick(it)
            }
        }
    }

    override fun onExpose(isFirstExpose: Boolean) {
        super.onExpose(isFirstExpose)
        mData?.let {
            TheatreManager.exposeOperation(it, "剧单")
        }
    }


    private fun onCoverClick(bookInfo: BookInfoVo) {
        TheatreManager.clickOperation(bookInfo, "剧单")
    }

    /**
     * 根据服务端配置，更新view的背景色
     */
    private fun updateViewBackgroundColor() {
        val bgColor = mData?.playlistInfo?.bgColor?.toInt() ?: Color.parseColor("#FF486866")
        
        // 1. rootLayout background: gradient from 80% opacity bgColor to white
        val colors = intArrayOf(
            ColorUtils.setAlphaComponent(bgColor, (255 * 0.8).toInt()),
            Color.WHITE
        )
        val gradientDrawable = GradientDrawable(
            GradientDrawable.Orientation.TOP_BOTTOM,
            colors
        ).apply {
            cornerRadius = 8f.dp
        }
        mViewBinding.rootLayout.background = gradientDrawable

        // 2. viewSeries1 background: 20% opacity bgColor with bottom corners rounded
        mViewBinding.viewSeries1.background = ShapeDrawableUtil.createGradientDrawable(
            solidColor = ColorUtils.setAlphaComponent(bgColor, (255 * 0.2).toInt()),
            leftBottomRadius = 8f.dp,
            rightBottomRadius = 8f.dp
        )

        // 3. viewSeries2 background: 10% opacity bgColor with bottom corners rounded
        mViewBinding.viewSeries2.background = ShapeDrawableUtil.createGradientDrawable(
            solidColor = ColorUtils.setAlphaComponent(bgColor, (255 * 0.1).toInt()),
            leftBottomRadius = 8f.dp,
            rightBottomRadius = 8f.dp
        )

        // 4. tvDetail background and text color
        mViewBinding.tvDetail.apply {
            background = ShapeDrawableUtil.createGradientDrawable(
                solidColor = ColorUtils.setAlphaComponent(bgColor, (255 * 0.15).toInt()),
                radius = 8f.dp
            )
            setTextColor(bgColor)
        }
    }

    override fun onCreateRecyclerViewItem(
        parent: DzRecyclerView?,
        itemView: View?
    ): RecyclerView.LayoutParams {
        val width = (ScreenUtil.getScreenWidth() - ScreenUtil.dip2px(context, 27)) / 2
        return RecyclerView.LayoutParams(
            width,
            ScreenUtil.dip2px(context, 313),
        )
    }

    private fun updateView() {
        updateViewBackgroundColor()
        mViewBinding.tvBookName.text = mData?.playlistInfo?.title
        mViewBinding.tvDetail.text = mData?.playlistInfo?.buttonName ?: "查看详情"
        mViewBinding.ivCover.loadRoundImg(
            mData?.coverWap ?: R.drawable.bbase_ic_cover_default,
            radius = ScreenUtil.dip2px(context, 8),
            placeholder = R.drawable.bbase_ic_cover_default,
            error = R.drawable.bbase_ic_cover_default,
            width = 147,
            height = 212
        )
    }

    override fun bindData(data: BookInfoVo?) {
        super.bindData(data)
        updateView()
    }

}