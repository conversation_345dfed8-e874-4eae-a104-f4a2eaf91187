package com.dz.business.theatre.refactor.component.bannerCardComp

import android.content.Context
import android.util.AttributeSet
import com.dz.business.base.theatre.data.BookInfoVo
import com.dz.business.theatre.R
import com.dz.business.theatre.databinding.TheatreCompBannerListItemBinding
import com.dz.business.theatre.refactor.manager.TheatreManager
import com.dz.business.track.utis.ElementClickUtils
import com.dz.foundation.base.utils.ScreenUtil
import com.dz.foundation.base.utils.dp
import com.dz.foundation.imageloader.load
import com.dz.foundation.imageloader.loadRoundImg
import com.dz.platform.common.base.ui.component.UIConstraintComponent

/**
 *@Description: banner的item布局
 */
class BannerListItemComp : UIConstraintComponent<TheatreCompBannerListItemBinding, BookInfoVo> {
    @JvmOverloads
    constructor(
        context: Context,
        attrs: AttributeSet? = null,
        defStyleAttr: Int = 0
    ) : super(context, attrs, defStyleAttr)

    override fun initData() {

    }

    override fun initView() {

    }

    override fun initListener() {
        //继续观看按钮点击事件
        ElementClickUtils.ignoreAutoTrack(mViewBinding.clLayout)
        ElementClickUtils.ignoreAutoTrack(mViewBinding.ivCover)
        ElementClickUtils.ignoreAutoTrack(mViewBinding.root)
        mViewBinding.root.registerClickAction {
            TheatreManager.onItemClick(mData)
        }
    }

    override fun bindData(data: BookInfoVo?) {
        super.bindData(data)
        data?.let {
            updateTypeStyle(it)
            mViewBinding.tvBookName.text = it.bookName
            mViewBinding.tvPlayCount.text = it.coverBottomTips
            //标签
            it.bookTags?.let { bookTags ->
                mViewBinding.tvTags.visibility = VISIBLE
                mViewBinding.tvTags.text = bookTags.take(3).joinToString(separator = " ")
            }
            //简介
            it.introduction?.let { introduction ->
                mViewBinding.tvIntroduction.visibility = VISIBLE
                mViewBinding.tvIntroduction.text = introduction
            }
            //剧封
            mViewBinding.ivCover.loadRoundImg(
                img = it.coverWap,
                radius = ScreenUtil.dip2px(context, 8),
                placeholder = R.drawable.bbase_ic_cover_default,
                error = R.drawable.bbase_ic_cover_default,
                width = 109,
                height = 156
            )
        }
    }

    /**
     * 剧封右上角类型的样式
     */
    private fun updateTypeStyle(data: BookInfoVo) {
        mViewBinding.label.bindData(data)
    }
}