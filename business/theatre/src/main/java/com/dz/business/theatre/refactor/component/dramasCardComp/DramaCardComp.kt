package com.dz.business.theatre.refactor.component.dramasCardComp

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import android.view.View
import androidx.recyclerview.widget.RecyclerView
import com.dz.business.base.theatre.data.BookInfoVo
import com.dz.business.theatre.R
import com.dz.business.theatre.databinding.TheatreCompDramaCardBinding
import com.dz.business.theatre.refactor.manager.TheatreManager
import com.dz.foundation.base.utils.ScreenUtil
import com.dz.foundation.base.utils.dp
import com.dz.foundation.imageloader.load
import com.dz.foundation.imageloader.loadRoundImg
import com.dz.foundation.ui.view.recycler.DzRecyclerView
import com.dz.platform.common.base.ui.component.UIConstraintComponent

/**
 * 剧单卡片
 * @constructor
 */
class DramaCardComp @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int? = 0
) : UIConstraintComponent<TheatreCompDramaCardBinding, BookInfoVo>(
    context,
    attrs,
    defStyleAttr!!
) {
    override fun initData() {
    }

    override fun initView() {
    }

    override fun onExpose(isFirstExpose: Boolean) {
        super.onExpose(isFirstExpose)
        mData?.let {
            TheatreManager.exposeBook(it)
        }
    }

    override fun onCreateRecyclerViewItem(
        parent: DzRecyclerView?,
        itemView: View?
    ): RecyclerView.LayoutParams {
        return RecyclerView.LayoutParams(
            ScreenUtil.dip2px(context, 98),
            ScreenUtil.dip2px(context, 175),
        )
    }

    override fun initListener() {
        //观看点击
        registerClickAction {
            TheatreManager.onItemClick(mData)
        }
    }

    override fun onBindRecyclerViewItem(model: BookInfoVo?, position: Int) {
        super.onBindRecyclerViewItem(model, position)
        model?.run {
            //1、标题:取内容{title}。规则：最多显示两行文案+省略号
            initTitle()
//            //2、左上角角标
            initRank()
//            //7、封面。规则：1.封面缩略图；
            coverWap?.let {

                mViewBinding.ivCover.loadRoundImg(
                    img = it,
                    radius = ScreenUtil.dip2px(context, 8),
                    placeholder = R.drawable.theatre_ic_comp1_default,
                    error = R.drawable.theatre_ic_comp1_default,
                    width = 90, height = 129
                )
            }
//            //更新提示
            mViewBinding.tvUpdate.text =
                if (finishStatusCn == context.getString(R.string.theatre_closed)) {
                    String.format(context.getString(R.string.theatre_whole), updateNum)
                } else {
                    String.format(
                        context.getString(R.string.theatre_update_to),
                        updateNum
                    )
                }
//            //新剧角标
            initNewVideoMark()
        }
    }

    private fun BookInfoVo.initTitle() {
        mViewBinding.tvTitle.text = bookName
    }

    @SuppressLint("SetTextI18n")
    private fun BookInfoVo.initRank() {
        if (showSort == true && mPosition in 1..99) {
            mViewBinding.ivRank.visibility = VISIBLE
            mViewBinding.tvRank.visibility = VISIBLE
            mViewBinding.ivRank.setImageResource(R.drawable.bbase_ic_rank0)
            mViewBinding.tvRank.text = (mPosition ?: "").toString()
        } else {
            mViewBinding.ivRank.visibility = GONE
            mViewBinding.tvRank.visibility = GONE
        }
    }

    private fun BookInfoVo.initNewVideoMark() {
        mViewBinding.label.bindData(this)
    }
}