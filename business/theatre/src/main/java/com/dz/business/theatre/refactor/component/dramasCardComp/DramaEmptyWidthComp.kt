package com.dz.business.theatre.refactor.component.dramasCardComp

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.widget.FrameLayout
import androidx.recyclerview.widget.RecyclerView
import com.dz.business.theatre.databinding.TheatreCompEmptyBinding
import com.dz.foundation.base.utils.dp
import com.dz.foundation.ui.view.recycler.DzRecyclerView
import com.dz.platform.common.base.ui.component.UIConstraintComponent

/**
 * @Description: 占位view
 * @Version:1.0
 */
class DramaEmptyWidthComp @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int? = 0,
) : UIConstraintComponent<TheatreCompEmptyBinding, Int>(
    context,
    attrs,
    defStyleAttr!!
) {
    override fun initData() {
    }

    override fun initView() {
    }

    override fun initListener() {
    }
    override fun onCreateRecyclerViewItem(
        parent: DzRecyclerView?,
        itemView: View?
    ): RecyclerView.LayoutParams {
        return RecyclerView.LayoutParams(
            8.dp, FrameLayout.LayoutParams.MATCH_PARENT
        )
    }
}