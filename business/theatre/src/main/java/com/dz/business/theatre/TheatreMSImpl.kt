package com.dz.business.theatre

import androidx.fragment.app.Fragment
import com.dz.business.base.theatre.TheatreMS
import com.dz.business.theatre.data.TheatreKV
import com.dz.business.theatre.refactor.page.theatre.TheatreFragment
import com.dz.business.theatre.ui.page.TheatreTeenFragment

/**
 * @Author: guyh
 * @Date: 2023/1/31 19:07
 * @Description:
 * @Version:1.0
 */
class TheatreMSImpl : TheatreMS {

    override fun getTheatreFragment(): Fragment {
        /// 根据服务端配置tabBar和搜索框
//        return TheRouter.build("flutter/fragment?url=flutter/marketTab").withSerializable("url_param", hashMapOf("bottomHeight" to 60)).createFragment()!!
//        return TheRouter.build("flutter/fragment?url=flutter/BookMarketPage").createFragment()!!
        return TheatreFragment()
    }

    override fun getTeenTheatreFragment(): Fragment {
        return TheatreTeenFragment()
    }

    override fun isNewCollect(): Boolean {
        return TheatreKV.isNewCollect
    }

    override fun setNewCollect(newCollect: Boolean) {
        TheatreKV.isNewCollect = newCollect
    }
}