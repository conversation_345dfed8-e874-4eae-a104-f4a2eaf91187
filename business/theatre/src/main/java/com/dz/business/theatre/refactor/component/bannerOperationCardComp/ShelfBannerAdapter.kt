package com.dz.business.theatre.refactor.component.bannerOperationCardComp


import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.dz.business.theatre.refactor.network.bean.ActivityInfoVo

import com.dz.foundation.ui.view.banner.adapter.BannerAdapter


class ShelfBannerAdapter(bannerList: List<ActivityInfoVo>?) :
    BannerAdapter<ActivityInfoVo, ShelfBannerAdapter.ShelfBannerVH>(bannerList) {

    override fun onCreateHolder(parent: ViewGroup, viewType: Int): ShelfBannerVH {
        val itemView = BannerListItemComp(parent.context)
        val params = ViewGroup.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.MATCH_PARENT
        )
        itemView.layoutParams = params
        return ShelfBannerVH(itemView)
    }


    override fun onBindView(holder: <PERSON><PERSON><PERSON><PERSON><PERSON>V<PERSON>, data: ActivityInfoVo, position: Int, size: Int) {
        holder.viewItem.bindData(data)
    }

    class ShelfBannerVH(var viewItem: BannerListItemComp) : RecyclerView.ViewHolder(viewItem) {

    }
}