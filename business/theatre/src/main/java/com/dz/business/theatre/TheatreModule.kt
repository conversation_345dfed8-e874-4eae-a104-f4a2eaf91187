package com.dz.business.theatre

import com.dz.business.base.theatre.TheatreMR
import com.dz.business.base.theatre.TheatreMS
import com.dz.business.theatre.refactor.page.subTab.TheatreFilterDialogComp
import com.dz.business.theatre.ui.component.NewCollectionDialogComp
import com.dz.business.theatre.ui.page.RankActivity
import com.dz.foundation.base.module.LibModule
import com.dz.foundation.base.service.DzServiceManager
import com.dz.foundation.router.registerTarget

/**
 * @Author: guyh
 * @Date: 2023/1/31 19:05
 * @Description:
 * @Version:1.0
 */
class TheatreModule : LibModule() {
    override fun onCreate() {
        TheatreMR.get().apply {
            newCollectDialog().registerTarget(NewCollectionDialogComp::class.java)
            rank().registerTarget(RankActivity::class.java)
            theatreFilter().registerTarget(TheatreFilterDialogComp::class.java)
        }
        DzServiceManager.registerService(TheatreMS::class.java, TheatreMSImpl::class.java)
    }
}