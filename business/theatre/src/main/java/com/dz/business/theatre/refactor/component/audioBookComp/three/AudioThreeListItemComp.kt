package com.dz.business.theatre.refactor.component.audioBookComp.three

import android.content.Context
import android.util.AttributeSet
import android.view.View
import androidx.lifecycle.LifecycleOwner
import androidx.recyclerview.widget.RecyclerView
import com.dz.business.base.home.HomeME
import com.dz.business.base.theatre.data.BookInfoVo
import com.dz.business.theatre.R
import com.dz.business.theatre.databinding.TheatreAudioThreeItemCompBinding
import com.dz.business.theatre.refactor.manager.TheatreManager
import com.dz.foundation.base.utils.ScreenUtil
import com.dz.foundation.imageloader.loadRoundImg
import com.dz.foundation.ui.view.recycler.DzRecyclerView
import com.dz.platform.common.base.ui.component.UIConstraintComponent

/**
 *@Description:有声书三卡-item
 *@Version:2.8.0
 */
class AudioThreeListItemComp :
    UIConstraintComponent<TheatreAudioThreeItemCompBinding, BookInfoVo> {
    private var mContentPos = 0

    @JvmOverloads
    constructor(
        context: Context,
        attrs: AttributeSet? = null,
        defStyleAttr: Int = 0,
    ) : super(context, attrs, defStyleAttr)

    override fun initData() {
    }

    override fun initView() {

    }

    override fun onExpose(isFirstExpose: Boolean) {
        super.onExpose(isFirstExpose)
        mData?.let {
            TheatreManager.exposeBook(it)
        }
    }

    override fun initListener() {
        mViewBinding.root.registerClickAction {
            TheatreManager.onAudioClick(mData)
        }
    }

    override fun subscribeObserver(lifecycleOwner: LifecycleOwner) {
        super.subscribeObserver(lifecycleOwner)

        HomeME.get().addFavoriteSuccess().observe(lifecycleOwner) { bookId ->
//            if (bookId == mData?.bookId) {
//                contract?.onBookFavoriteStateChanged(true)
//            }
        }

        HomeME.get().deleteFavoriteSuccess().observe(lifecycleOwner) { bookIds ->
//            if (bookIds.contains(mData?.bookId)) {
//                contract?.onBookFavoriteStateChanged(false)
//            }
        }

    }

    override fun onBindRecyclerViewItem(model: BookInfoVo?, position: Int) {
        super.onBindRecyclerViewItem(model, position)
        mContentPos = position
        updateView(model)
    }

    override fun onCreateRecyclerViewItem(
        parent: DzRecyclerView?,
        itemView: View?
    ): RecyclerView.LayoutParams {
        val width = (ScreenUtil.getScreenWidth() - ScreenUtil.dip2px(context, 13 + 10 + 10)) / 3
        return RecyclerView.LayoutParams(
            width,
            ScreenUtil.dip2px(context, 150 + 48),
        )
    }

    private fun updateView(data: BookInfoVo?) {
        data?.run {
            //TODO 播放状态 根据播放器的值初始化是否正在播放
            mViewBinding.acPlay.bindData(data.bookId, data.isPlaying, this.coverWap)
            mViewBinding.tvBookName.text = bookName ?: ""
            if (bookTags.isNullOrEmpty()) {
                mViewBinding.tvTags.visibility = GONE
            } else {
                mViewBinding.tvTags.visibility = VISIBLE
                mViewBinding.tvTags.text = bookTags!!.take(3).joinToString(separator = " ")
            }
            mViewBinding.ivCover.loadRoundImg(
                img = mData?.coverWap,
                radius = ScreenUtil.dip2px(context, 8),
                placeholder = R.drawable.bbase_ic_cover_default,
                error = R.drawable.bbase_ic_cover_default,
                width = 114,
                height = 150,
            )
        }
    }

}