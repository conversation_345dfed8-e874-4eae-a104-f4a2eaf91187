package com.dz.business.theatre.refactor.component.novelDoubleCardComp

import android.content.Context
import android.util.AttributeSet
import com.dz.business.base.theatre.data.BookInfoVo
import com.dz.business.theatre.databinding.TheatreCompHorizontalCardBinding
import com.dz.business.theatre.refactor.component.TheatreCardComp
import com.dz.business.theatre.refactor.component.TheatreCardFactory.CARD_TYPE_NOVEL_FOUR_H
import com.dz.business.theatre.refactor.component.novelDoubleCardComp.bookItem.NovelHorizontalItemComp
import com.dz.business.theatre.refactor.network.bean.ColumnDataItem
import com.dz.foundation.ui.view.custom.ActionListenerOwner
import com.dz.foundation.ui.view.recycler.DzRecyclerViewCell

/**
 * 横卡
 * @constructor
 */
class NovelHorizontalCardComp @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int? = 0
) : TheatreCardComp<TheatreCompHorizontalCardBinding>(
    context, attrs, defStyleAttr!!
), ActionListenerOwner<TheatreCardComp.ViewActionListener> {
    /**
     * 更新标题
     */
    private fun updateTitle(data: ColumnDataItem?) {
        if (data?.columnTitle?.isBlank() == true || data?.needConcat == true) {
            mViewBinding.tvTitle.visibility = GONE
        } else {
            mViewBinding.tvTitle.visibility = VISIBLE
            mViewBinding.tvTitle.text = data?.columnTitle
        }

        data?.moreTips?.let {
            mViewBinding.tvMoreAction.visibility = VISIBLE
            mViewBinding.tvMoreAction.text = it
            mViewBinding.tvMoreAction.setOnClickListener {
                mActionListener?.moreActionContentClick(mData)
            }
        }
    }

    override fun onBindRecyclerViewItem(model: ColumnDataItem?, position: Int) {
        super.onBindRecyclerViewItem(model, position)
        updateTitle(model)
        val cellList = mutableListOf<DzRecyclerViewCell<BookInfoVo>>()
        model?.bookData?.forEachIndexed { index, item ->
            item.listIndex = index
            item.groupId = model.bdChannelGroupId
            item.groupName = model.bdChannelGroupName
            item.groupPos = model.bdChannelGroupPos
            item.channelPos = model.currentChannel?.index
            item.channelName = model.currentChannel?.channelName
            item.channelId = model.currentChannel?.channelId?.toLong()
            item.columnId = (model.columnId?:"").toString()
            item.columnName = model.columnTitle
            item.columnPos = model.columnPos
            item.styleTypeCn = model.styleTypeCn
            item.styleType = model.styleType
            item.columnTitle = model.columnTitle
            val cell = DzRecyclerViewCell<BookInfoVo>().apply {
                viewClass = NovelHorizontalItemComp::class.java
                viewData = item
//                spanSize = 1
            }
            cellList.add(cell)
        }
        mViewBinding.recyclerView.removeAllCells()
        mViewBinding.recyclerView.addCells(cellList)
        if(cellList.isEmpty()){
            mViewBinding.clLayout.visibility = GONE
        }else{
            mViewBinding.clLayout.visibility = VISIBLE
        }
    }
    override fun getCardType(): Int {
        return CARD_TYPE_NOVEL_FOUR_H
    }
}