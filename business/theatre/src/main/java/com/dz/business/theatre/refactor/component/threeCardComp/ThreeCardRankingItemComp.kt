package com.dz.business.theatre.refactor.component.threeCardComp

import android.content.Context
import android.util.AttributeSet
import android.view.View
import androidx.recyclerview.widget.RecyclerView
import com.dz.business.base.theatre.data.BookInfoVo
import com.dz.business.theatre.databinding.TheatreCompThreeCardRankingItemBinding
import com.dz.business.theatre.refactor.manager.TheatreManager
import com.dz.foundation.base.utils.ScreenUtil
import com.dz.foundation.imageloader.load
import com.dz.foundation.ui.view.recycler.DzRecyclerView
import com.dz.platform.common.base.ui.component.UIConstraintComponent
import com.dz.platform.common.router.SchemeRouter

/**
 * 三卡样式-排行榜卡片
 */
class ThreeCardRankingItemComp :
    UIConstraintComponent<TheatreCompThreeCardRankingItemBinding, BookInfoVo> {

    @JvmOverloads
    constructor(
        context: Context,
        attrs: AttributeSet? = null,
        defStyleAttr: Int = 0,
    ) : super(context, attrs, defStyleAttr)

    override fun initData() {

    }

    override fun initView() {

    }

    override fun onExpose(isFirstExpose: Boolean) {
        super.onExpose(isFirstExpose)
        mData?.let {
            TheatreManager.exposeOperation(it, "排行榜")
        }
    }

    override fun initListener() {

        mViewBinding.rootLayout.registerClickAction {
            mData?.let {
                TheatreManager.clickOperation(it, "排行榜")
            }
        }
    }


    private fun updateView() {
        mViewBinding.tvRankingTitle.text = mData?.rankInfo?.title
        // 图标
        mData?.rankInfo?.iconUrl?.let {
            mViewBinding.ivFire1.load(it)
            mViewBinding.ivFire2.load(it)
            mViewBinding.ivFire3.load(it)
            mViewBinding.ivFire4.load(it)
        }

        //剧名及热度
        mData?.rankInfo?.bookRankInfo?.forEachIndexed { index, bookRankInfo ->
            when (index) {
                0 -> {
                    mViewBinding.tvRankingName1.text = bookRankInfo.bookName
                    mViewBinding.tvCount1.text = bookRankInfo.rankSrc
                }

                1 -> {
                    mViewBinding.tvRankingName2.text = bookRankInfo.bookName
                    mViewBinding.tvCount2.text = bookRankInfo.rankSrc
                }

                2 -> {
                    mViewBinding.tvRankingName3.text = bookRankInfo.bookName
                    mViewBinding.tvCount3.text = bookRankInfo.rankSrc
                }

                3 -> {
                    mViewBinding.tvRankingName4.text = bookRankInfo.bookName
                    mViewBinding.tvCount4.text = bookRankInfo.rankSrc
                }
            }
        }


    }

    override fun onCreateRecyclerViewItem(
        parent: DzRecyclerView?,
        itemView: View?
    ): RecyclerView.LayoutParams {
        val width = (ScreenUtil.getScreenWidth() - ScreenUtil.dip2px(context, 33)) / 3
        return RecyclerView.LayoutParams(
            width,
            ScreenUtil.dip2px(context, 211),
        )
    }

    override fun bindData(data: BookInfoVo?) {
        super.bindData(data)
        updateView()
    }
}