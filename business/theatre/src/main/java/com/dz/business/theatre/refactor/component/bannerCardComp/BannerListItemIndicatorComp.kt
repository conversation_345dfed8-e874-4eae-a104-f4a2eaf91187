package com.dz.business.theatre.refactor.component.bannerCardComp

import android.content.Context
import android.util.AttributeSet
import android.view.View
import androidx.recyclerview.widget.RecyclerView
import com.dz.business.base.theatre.data.BookInfoVo
import com.dz.business.repository.entity.HistoryEntity
import com.dz.business.theatre.R
import com.dz.business.theatre.databinding.TheatreCompBannerIndicatorBinding
import com.dz.foundation.base.utils.ScreenUtil
import com.dz.foundation.base.utils.dp
import com.dz.foundation.ui.view.recycler.DzRecyclerView
import com.dz.platform.common.base.ui.component.UIConstraintComponent

/**
 * 指示器红点
 * @constructor
 */

class BannerListItemIndicatorComp @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int? = 0,
) : UIConstraintComponent<TheatreCompBannerIndicatorBinding, BookInfoVo>(
    context,
    attrs,
    defStyleAttr!!
) {
    override fun initData() {}

    override fun initView() {

    }

    override fun onCreateRecyclerViewItem(
        parent: DzRecyclerView?,
        itemView: View?
    ): RecyclerView.LayoutParams {
        return RecyclerView.LayoutParams(
            ScreenUtil.dip2px(context, 15),
            ScreenUtil.dip2px(context, 15),
        )
    }

    override fun initListener() {

    }

    override fun bindData(data: BookInfoVo?) {
        super.bindData(data)
        if (data?.isSelect == 1) {
            mViewBinding.view.setShapeBackground(
                radius = 4f.dp,
                solidColor = getColor(R.color.common_E1442E)
            )
        } else {
            mViewBinding.view.setShapeBackground(
                radius = 4f.dp,
                solidColor = getColor(R.color.d8d8d8)
            )
        }
    }
}