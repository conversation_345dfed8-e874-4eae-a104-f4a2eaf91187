package com.dz.business.theatre.refactor.component

import android.content.Context
import android.util.AttributeSet
import com.dz.business.theatre.databinding.TheatreFilterDialogCompItemBinding
import com.dz.business.theatre.util.ChannelFilterMenuCacheUtil.TagListItem
import com.dz.foundation.ui.view.custom.ActionListener
import com.dz.foundation.ui.view.custom.ActionListenerOwner
import com.dz.platform.common.base.ui.component.UIConstraintComponent

/**
 * 筛选项item的View。
 */
class TheatreFilterDialogItemComp @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int? = 0,
) : UIConstraintComponent<TheatreFilterDialogCompItemBinding, TagListItem>(
    context,
    attrs,
    defStyleAttr!!
), ActionListenerOwner<TheatreFilterDialogItemComp.ViewActionListener> {

    override var mActionListener: ViewActionListener? = null

    interface ViewActionListener : ActionListener {
        fun onItemClick(isSelected: Boolean, data: TagListItem.SubItem?)
    }

    override fun initData() {

    }

    override fun initView() {

    }

    override fun initListener() {
        mViewBinding.btnSubItem.registerClickAction {
            if (mData is TagListItem.SubItem) {
                mViewBinding.btnSubItem.isSelected = !mViewBinding.btnSubItem.isSelected
                mActionListener?.onItemClick(
                    mViewBinding.btnSubItem.isSelected,
                    mData as TagListItem.SubItem
                )
            }

        }
    }

    override fun bindData(data: TagListItem?) {
        super.bindData(data)
        mViewBinding.tvTitle.visibility = if (data is TagListItem.Header) VISIBLE else GONE
        mViewBinding.btnSubItem.visibility = if (data is TagListItem.SubItem) VISIBLE else GONE
        mViewBinding.footer.visibility = if (data is TagListItem.Footer) VISIBLE else GONE
        data?.let {
            when (data) {
                is TagListItem.Header -> {
                    mViewBinding.tvTitle.text = data.name
                }

                is TagListItem.SubItem -> {
                    mViewBinding.btnSubItem.text = data.name
                    mViewBinding.btnSubItem.isSelected = data.isSelected
                }

                else -> {}
            }
        }
    }
}