package com.dz.business.theatre.refactor.page.subTab

import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.MutableLiveData
import com.dz.business.base.BBaseMR
import com.dz.business.base.SpeedUtil
import com.dz.business.base.livedata.CommLiveData
import com.dz.business.base.splash.SplashMS
import com.dz.business.base.theatre.TheatreMR
import com.dz.business.base.theatre.data.BookInfoVo
import com.dz.business.base.track.TrackUtil
import com.dz.business.base.vm.PageVM
import com.dz.business.theatre.refactor.component.TheatreCardComp
import com.dz.business.theatre.refactor.component.TheatreCardFactory.getCardViewClass
import com.dz.business.theatre.refactor.manager.TheatreManager
import com.dz.business.theatre.refactor.network.api.TheatreRequest1125
import com.dz.business.theatre.refactor.network.bean.ChannelDataItem
import com.dz.business.theatre.refactor.network.bean.ChannelDataVo
import com.dz.business.theatre.refactor.network.bean.ColumnDataItem
import com.dz.business.theatre.refactor.network.bean.TheatreInfo
import com.dz.business.theatre.refactor.page.theatre.TheatreRepository
import com.dz.business.theatre.util.ChannelFilterMenuCacheUtil
import com.dz.business.theatre.util.ChannelPointTrackUtil
import com.dz.business.track.base.addParam
import com.dz.business.track.events.DzTrackEvents
import com.dz.business.track.utis.AppPerformanceTrackUtil
import com.dz.foundation.base.utils.LogUtil
import com.dz.foundation.router.RouteIntent
import com.dz.foundation.ui.view.recycler.DzRecyclerViewCell
import com.dz.platform.common.router.ACTIVITY
import com.dz.platform.common.router.SchemeRouter
import com.dz.platform.common.router.onDismiss
import com.dz.platform.common.router.onShow
import com.dz.platform.common.toast.ToastManager
import com.sensorsdata.analytics.android.sdk.SensorsDataAPI
import org.json.JSONObject
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.CopyOnWriteArrayList

/**
 * 剧场页Fragment的ViewModel
 */
class TabBarFragmentVM : PageVM<RouteIntent>(), TheaterTabVMContract {
    private val TAG: String = "TheatreTabBarFragment"
    override var theatreCells = CommLiveData<MutableList<DzRecyclerViewCell<*>>>()
    override var moreCells = CommLiveData<MutableList<DzRecyclerViewCell<*>>>()
    override val tabs: MutableLiveData<MutableList<ChannelDataVo>?> = MutableLiveData()
    override var channelGroup: ChannelDataItem? = null
    override val menuSelectedChannelId: CommLiveData<Long> = CommLiveData()
    override val filterSelectedSubTagId: CommLiveData<Long> = CommLiveData()
    private var origin: String = ""
    private var originName: String = ""
    private var theatreInfo: TheatreInfo? = null
    private var theaterSubscriptSwitch: Boolean? = null
    private var storePageId: Int = 0
    override var hasMore: Boolean? = true
    private var hasData = false //页面有数据
    private val listTheatreInfo: MutableList<ColumnDataItem> = mutableListOf()
    private val initDataMap: ConcurrentHashMap<Long, TheatreInfo?> =
        ConcurrentHashMap()
    private var currentChannel: ChannelDataVo =
        ChannelDataVo(channelId = 0, channelName = "", checkedFlag = false) //当前二级频道的id

    /**
     * 默认的二级频道。
     */
    private var initChannel: ChannelDataVo? = null

    /**
     * 当前选择的标签页id。
     */
    private var currentSubTagId = -1L

    /**
     * 缓存的筛选按钮下的数据。
     */
    private val initFilterDataMap: ConcurrentHashMap<Long, TheatreInfo?> =
        ConcurrentHashMap()

    /**
     * 获取缓存的map
     */
    private fun getCacheDataMap(): ConcurrentHashMap<Long, TheatreInfo?> {
        return if (currentSubTagId == -1L) {
            initDataMap
        } else {
            initFilterDataMap
        }
    }


    /**
     * 获取缓存的频道数据
     */
    private fun getCacheTheatreInfo(): TheatreInfo? {
        return if (currentSubTagId == -1L) {
            initDataMap[currentChannel.channelId]
        } else {
            initFilterDataMap[currentSubTagId]
        }
    }

    override fun onCreate(owner: LifecycleOwner) {
        super.onCreate(owner)
        // 初始化生成缓存数据
        channelGroup = arguments?.getSerializable("channelGroup") as ChannelDataItem?
        origin = arguments?.getString("origin") ?: ""
        originName = arguments?.getString("originName") ?: ""
//        val serializedTheatreInfo = arguments?.getSerializable("theatreInfo") as? TheatreInfo
//        theatreInfo = serializedTheatreInfo?.copy()
        if (channelGroup?.checkedFlag == true) {
            theatreInfo = TheatreManager.theatreInfo
        }
        theaterSubscriptSwitch = arguments?.getBoolean("theaterSubscriptSwitch") ?: false
        storePageId = arguments?.getInt("storePageId") ?: 0
        // tabs
        tabs.value?.clear()
        channelGroup?.channelData?.let {
            tabs.value = it
        }
        initCacheData()
    }

    override fun onResume(owner: LifecycleOwner) {
        super.onResume(owner)
        if (!hasData) {
            //页面无缓存数据 请求网络
            loadTheatreData(type = TheatreManager.RefreshType.channel, true)
        }
        trackAppViewScreen(ChannelPointTrackUtil.channelGroupShouldRedPointUpload(channelGroup))
    }

    // 请求网络数据
    private fun loadTheatreData(type: TheatreManager.RefreshType, showLoading: Boolean? = true) {
        TheatreManager.bannerAlreadyTrackPositionSet[channelGroup?.channelGroupId]?.clear()
        val params = TheatreRequest1125.I1125Params(
            storePageId = storePageId,
            theaterSubscriptSwitch = theaterSubscriptSwitch ?: false,
            channelGroupId = channelGroup?.channelGroupId?.toString(),
        )
        if (currentChannel.channelId != 0L) {
            params.channelId = currentChannel.channelId
            params.channelName = currentChannel.channelName
        }
        val currentId = currentChannel.channelId
        TheatreRepository.getTheatreListFromNet(
            params,
            object : TheatreRepository.Callback<TheatreInfo> {
                override fun onStart() {
                    if (showLoading == true) {
                        statusPoster.statusLoading().post()
                    }
                }

                override fun onFail(e: Exception) {
                    SplashMS.get()?.updateColdLaunchPageContentDisplayTime(false)
                    SplashMS.get()?.updateColdPlayerPrepareDisplayTime(false)
                    if (currentId == currentChannel.channelId) {
                        if (showLoading == true) {
                            statusPoster.statusNetError(e)
                                .actionText("刷新").actionListener {
                                    loadTheatreData(TheatreManager.RefreshType.refreshError)
                                }.post()
                        } else {
                            theatreCells.value = mutableListOf()
                        }
                        ToastManager.showToast("网络请求失败")
                    } else {
                        statusPoster.statusDismiss().post()
                    }
                    trackRefresh(type, TheatreManager.RefreshResult.error.value)
                    e.printStackTrace()
                }

                override fun onEnd() {
                }

                override fun onSuccess(data: TheatreInfo?) {
                    if (currentId == currentChannel.channelId) {
                        if (data?.columnData.isNullOrEmpty()) {
                            listTheatreInfo.clear()
                            hasMore = if (type == TheatreManager.RefreshType.filter) {
                                initDataMap[currentChannel.channelId]?.hasMore
                            } else {
                                initFilterDataMap[currentSubTagId]?.hasMore
                            }
                            theatreCells.value?.clear()
                            theatreCells.value = mutableListOf()
                            if (showLoading == true) {
                                statusPoster.statusDataEmpty()
                                    .actionText("刷新").actionListener {
                                        loadTheatreData(TheatreManager.RefreshType.refreshError)
                                    }.post()
                            } else {
                                ToastManager.showToast("网络请求失败")
                            }

                            trackRefresh(type, TheatreManager.RefreshResult.empty.value)
                        } else {
                            statusPoster.statusDismiss().post()
                            trackRefresh(type, TheatreManager.RefreshResult.success.value)
                            data?.lastColumnStyle = data?.columnData?.last()?.styleType
                            if (type == TheatreManager.RefreshType.filter) {
                                initFilterDataMap[currentSubTagId] = data
                            } else {
                                initDataMap[currentChannel.channelId] = data
                            }
                            var accumulatePosition = 0
                            data?.columnData?.forEachIndexed { columnPos, it ->
                                it.bdChannelGroupName = channelGroup?.channelGroupName
                                it.bdChannelGroupId = channelGroup?.channelGroupId
                                it.bdChannelGroupPos = channelGroup?.bdChannelGroupPos ?: 0
                                currentChannel.index =
                                    channelGroup?.channelData?.indexOfFirst { it.channelId == currentChannel.channelId }
                                it.currentChannel = currentChannel
                                it.columnPos = columnPos
                                it.accumulateIndex = accumulatePosition
                                it.videoData?.forEachIndexed { index, item ->
                                    if (item.bookType == 0) {
                                        item.accumulatePosition = accumulatePosition
                                        it.accumulateIndex = accumulatePosition
                                        LogUtil.d(
                                            TAG,
                                            "累计位置 初始化数据 ：videoData accumulatePosition:${item.bookName}  ${item.accumulatePosition}"
                                        )
                                        accumulatePosition += 1
                                    }
                                    item.mPosition = index + 1
                                    item.columnPos = it.columnPos
                                    item.elementId = it.columnId
                                    item.styleTypeCn = it.styleTypeCn
                                }
                                it.rankInfoVO?.forEachIndexed { i, rankInfoVO ->
                                    rankInfoVO.bookRankInfo?.forEachIndexed { index, item ->
                                        item.accumulatePosition = accumulatePosition
                                        it.accumulateIndex = accumulatePosition
                                        LogUtil.d(
                                            TAG,
                                            "累计位置 初始化数据 ：rankInfoVO accumulatePosition:${item.bookName}  ${item.accumulatePosition}"
                                        )
                                        accumulatePosition += 1
                                        item.mPosition = index + 1
                                        item.columnPos = it.columnPos
                                        item.elementId = it.columnId
                                        item.styleTypeCn = it.styleTypeCn
                                    }
                                }
                            }
                            createTheatreCells()
                        }
                    } else {
                        statusPoster.statusDismiss().post()
                    }
                }
            })
    }

    override fun onPause(owner: LifecycleOwner) {
        super.onPause(owner)
        LogUtil.d(TAG, "onPause")
        setCacheData()
    }

    // 请求网络数据
    override fun loadPage() {
        val item: TheatreInfo? = getCacheTheatreInfo()
        val params = TheatreRequest1125.I1125Params(
            pageFlag = item?.pageFlag ?: "0",
            lastColumnStyle = item?.lastColumnStyle,
            storePageId = storePageId,
            theaterSubscriptSwitch = theaterSubscriptSwitch ?: false,
            channelGroupId = channelGroup?.channelGroupId?.toString(),
            fromColumnId = item?.fromColumnId,
            fromItemId = item?.fromItemId,
            excludeBookIds = item?.excludeBookIds
        )
        if (currentChannel.channelId != 0L) {
            params.channelId = currentChannel.channelId
            params.channelName = currentChannel.channelName
        }

        TheatreRepository.getTheatreListFromNet(
            params,
            object : TheatreRepository.Callback<TheatreInfo> {
                override fun onStart() {
                }

                override fun onFail(e: Exception) {
                    e.printStackTrace()
                    trackRefresh(
                        TheatreManager.RefreshType.loadPage,
                        TheatreManager.RefreshResult.error.value
                    )
                }

                override fun onEnd() {
                }

                override fun onSuccess(data: TheatreInfo?) {
                    kotlin.runCatching {
                        val cacheTheatreInfo = getCacheTheatreInfo()
                        if (!data?.columnData.isNullOrEmpty()) {
                            cacheTheatreInfo?.lastColumnStyle =
                                data?.columnData?.last()?.styleType
                            trackRefresh(
                                TheatreManager.RefreshType.loadPage,
                                TheatreManager.RefreshResult.success.value
                            )
                        } else {
                            trackRefresh(
                                TheatreManager.RefreshType.loadPage,
                                TheatreManager.RefreshResult.empty.value
                            )
                        }
                        cacheTheatreInfo?.hasMore = data?.hasMore
                        cacheTheatreInfo?.fromColumnId = data?.fromColumnId
                        cacheTheatreInfo?.storePageId = data?.storePageId
                        cacheTheatreInfo?.fromItemId = data?.fromItemId
                        cacheTheatreInfo?.pageFlag = data?.pageFlag
                        cacheTheatreInfo?.theaterSubscriptSwitch =
                            data?.theaterSubscriptSwitch
                        //拼接打点信息
                        splicingDoting(data)
                        cacheTheatreInfo?.columnData?.addAll(
                            data?.columnData ?: mutableListOf()
                        )

                        createMoreCells(data?.columnData)
                    }.onFailure { it.printStackTrace() }
                }
            })
    }

    override fun onBookFavorite(bookId: String) {
        // 遍历 initDataMap 中的所有频道数据
        getCacheDataMap().forEach { (_, theatreInfo) ->
            // 遍历每个频道中的所有栏目数据
            theatreInfo?.columnData?.forEach { columnData ->
                // 遍历栏目中的所有视频数据
                columnData.videoData?.forEach { videoItem ->
                    // 如果找到匹配的 bookId，更新其 inBookShelf 状态为 true
                    if (videoItem.bookId == bookId) {
                        videoItem.inBookShelf = true
                    }
                }
            }
        }
    }

    override fun onBookDeleteFavorite(bookId: List<String>) {
        // 遍历 initDataMap 中的所有频道数据
        getCacheDataMap().forEach { (_, theatreInfo) ->
            // 遍历每个频道中的所有栏目数据
            theatreInfo?.columnData?.forEach { columnData ->
                // 遍历栏目中的所有视频数据
                columnData.videoData?.forEach { videoItem ->
                    // 如果在待删除列表中找到匹配的 bookId，更新其 inBookShelf 状态为 false
                    if (bookId.contains(videoItem.bookId)) {
                        videoItem.inBookShelf = false
                    }
                }
            }
        }
    }

    override fun deleteReservation(bookIds: List<String>) {
        // 遍历 initDataMap 中的所有频道数据
        getCacheDataMap().forEach { (_, theatreInfo) ->
            // 遍历每个频道中的所有栏目数据
            theatreInfo?.columnData?.forEach { columnData ->
                // 遍历栏目中的所有视频数据
                columnData.reserveData?.forEach { videoItem ->
                    // 如果在待删除列表中找到匹配的 bookId，更新其 inBookShelf 状态为 false
                    if (bookIds.contains(videoItem.initBookId)) {
                        if (videoItem.reservationStatus == 1) {
                            videoItem.reservationStatus = 0
                            videoItem.reservationNum = (videoItem.reservationNum ?: 0) - 1
                        }
                    }
                }
            }
        }
    }

    override fun getInitChannelDataVo(): ChannelDataVo? {
        return initChannel
    }

    override fun revertCurrentChannelDataVo() {
        initChannel?.let {
            currentChannel = it
        }
    }

    override fun showFilterImg(): Boolean {
        val theatreInfo = ChannelFilterMenuCacheUtil.theatreInfo
        return theatreInfo?.videoTagData != null && theatreInfo.channelGroupData != null && channelGroup?.tagSieving == 1
    }

    override fun addReservation(bookIds: List<String>) {
        // 遍历 initDataMap 中的所有频道数据
        getCacheDataMap().forEach { (_, theatreInfo) ->
            // 遍历每个频道中的所有栏目数据
            theatreInfo?.columnData?.forEach { columnData ->
                // 遍历栏目中的所有视频数据
                columnData.reserveData?.forEach { videoItem ->
                    // 如果在待删除列表中找到匹配的 bookId，更新其 inBookShelf 状态为 false
                    if (bookIds.contains(videoItem.initBookId)) {
                        if (videoItem.reservationStatus != 1) {
                            videoItem.reservationStatus = 1
                            videoItem.reservationNum = (videoItem.reservationNum ?: 0) + 1
                        }
                    }
                }
            }
        }
    }

    // 初始化缓存数据
    /**
     * 初始化缓存数据
     * 根据默认选中频道生成缓存数据
     */
    private fun initCacheData() {
        // 如果没有频道数据，直接返回
        if (channelGroup?.channelData.isNullOrEmpty()) {
            processTheatreInfo()
            return
        }

        // 查找选中的频道
        currentChannel = findSelectedChannel() ?: channelGroup?.channelData?.firstOrNull() ?: return
        initChannel = currentChannel

        // 如果是选中的频道组，处理剧场信息
        if (channelGroup?.checkedFlag == true) {
            processTheatreInfo()
        }
    }

    /**
     * 查找选中的频道
     */
    private fun findSelectedChannel(): ChannelDataVo? {
        return channelGroup?.channelData?.find { it.checkedFlag }
    }

    /**
     * 处理剧场信息数据
     */
    private fun processTheatreInfo() {
        if (theatreInfo?.columnData.isNullOrEmpty()) return

        // 设置最后一列样式
        theatreInfo?.lastColumnStyle = theatreInfo?.columnData?.last()?.styleType

        var accumulatePosition = 0
        // 处理每一列数据
        theatreInfo?.columnData?.forEachIndexed { columnPos, columnData ->
            // 设置频道组信息
            columnData.bdChannelGroupName = channelGroup?.channelGroupName
            columnData.bdChannelGroupId = channelGroup?.channelGroupId
            columnData.bdChannelGroupPos = channelGroup?.bdChannelGroupPos ?: 0
            // 设置当前频道信息
            currentChannel.index = channelGroup?.channelData?.indexOfFirst {
                it.channelId == currentChannel.channelId
            } ?: 0
            columnData.currentChannel = currentChannel
            columnData.columnPos = columnPos
            columnData.accumulateIndex = accumulatePosition
            // 处理视频数据
            columnData.videoData?.forEachIndexed { index, item ->
                item.mPosition = index + 1
                item.columnPos = columnData.columnPos
                item.elementId = columnData.columnId
                item.styleTypeCn = columnData.styleTypeCn
                if (item.bookType == 0) {
                    item.accumulatePosition = accumulatePosition
                    columnData.accumulateIndex = accumulatePosition
                    LogUtil.d(
                        TAG,
                        "累计位置 参数透传：videoData accumulatePosition: ${item.bookName} $accumulatePosition"
                    )
                    accumulatePosition += 1
                }
            }
            columnData.rankInfoVO?.forEachIndexed { i, rankInfoVO ->
                rankInfoVO.bookRankInfo?.forEachIndexed { index, item ->
                    item.accumulatePosition = accumulatePosition
                    columnData.accumulateIndex = accumulatePosition
                    LogUtil.d(
                        TAG,
                        "累计位置 参数透传 ：rankInfoVO accumulatePosition:${item.bookName}  ${item.accumulatePosition}"
                    )
                    accumulatePosition += 1
                    item.mPosition = index + 1
                    item.columnPos = columnData.columnPos
                    item.elementId = columnData.columnId
                    item.styleTypeCn = columnData.styleTypeCn
                }
            }
        }

        // 保存到缓存
        if (currentSubTagId == -1L) {
            initDataMap[currentChannel.channelId] = theatreInfo
        } else {
            initFilterDataMap[currentSubTagId] = theatreInfo
        }

        createTheatreCells()
    }

    // 生成 列表数据
    private fun createTheatreCells() {
        listTheatreInfo.clear()
        if (!getCacheTheatreInfo()?.columnData.isNullOrEmpty()) {
            getCacheTheatreInfo()?.columnData?.let {
                listTheatreInfo.addAll(it)
                hasData = true
            }
        }
        val allCell = mutableListOf<DzRecyclerViewCell<*>>()
        listTheatreInfo.forEachIndexed { index, item ->
            DzRecyclerViewCell<ColumnDataItem>().apply {
                viewClass = getCardViewClass(item.styleType)
                viewData = item
                setActionListener(object : TheatreCardComp.ViewActionListener {
                    override fun moreActionContentClick(bookInfoVo: ColumnDataItem?) {
                        toMoreAction(bookInfoVo)
                    }

                    override fun onBookFavoriteClick(bookInfoVo: BookInfoVo?) {

                    }

                    override fun onBookCancelFavoriteClick(bookInfoVo: BookInfoVo?) {

                    }
                })
                allCell.add(this)
            }
        }
        hasMore = getCacheTheatreInfo()?.hasMore
        theatreCells.value?.clear()
        theatreCells.value = allCell
    }

    // 生成下一页列表数据
    private fun createMoreCells(columnData: MutableList<ColumnDataItem>?) {
        if (!columnData.isNullOrEmpty()) {
            listTheatreInfo.addAll(columnData)
            hasData = true
        }
        val allCell = mutableListOf<DzRecyclerViewCell<*>>()
        columnData?.forEachIndexed { index, item ->
            DzRecyclerViewCell<ColumnDataItem>().apply {
                viewClass = getCardViewClass(item.styleType)
                viewData = item
                setActionListener(object : TheatreCardComp.ViewActionListener {
                    override fun moreActionContentClick(bookInfoVo: ColumnDataItem?) {
                        toMoreAction(bookInfoVo)
                    }

                    override fun onBookFavoriteClick(bookInfoVo: BookInfoVo?) {

                    }

                    override fun onBookCancelFavoriteClick(bookInfoVo: BookInfoVo?) {

                    }
                })
                allCell.add(this)
            }
        }
        hasMore = getCacheTheatreInfo()?.hasMore
        moreCells.value = allCell
    }

    // 点击二级tab
    override fun clickTab(item: ChannelDataVo) {
        kotlin.runCatching {
            statusPoster.statusDismiss().post()
            currentChannel = item
            //重置当前标签，当前标签没有选择了。
            currentSubTagId = -1
            if (getCacheTheatreInfo()?.columnData.isNullOrEmpty()) {
                listTheatreInfo.clear()
                theatreCells.value?.clear()
                theatreCells.value = mutableListOf()
                loadTheatreData(TheatreManager.RefreshType.channel)
            } else {
                setCacheData()
                createTheatreCells()
            }
            trackAppViewScreen(ChannelPointTrackUtil.channelGroupShouldRedPointUpload(channelGroup))
//            1.15版本 和线上flutter对比，去掉点击二级tab打点
//            val jsonObject = JSONObject().apply {
//                put("\$elementPosition", "二级频道")
//                put("\$elementContent", item.channelName)
//                put("\$elementType", "二级频道")
//                put("\$screen_name", "剧场")
//                put("\$title", "剧场-${channelGroup?.channelGroupName}-${item.channelName}")
//            }
//            TrackUtil.track("\$AppClick", jsonObject)
        }.onFailure {
            it.printStackTrace()
        }
    }

    override fun clickFilter(subTagId: Long) {
        kotlin.runCatching {
            statusPoster.statusDismiss().post()
            currentSubTagId = subTagId
            if (initFilterDataMap[currentChannel.channelId]?.columnData.isNullOrEmpty()) {
                listTheatreInfo.clear()
                theatreCells.value?.clear()
                theatreCells.value = mutableListOf()
                loadTheatreData(TheatreManager.RefreshType.filter)
            } else {
                setCacheData(1)
                createTheatreCells()
            }
        }.onFailure {
            it.printStackTrace()
        }
    }

    /**
     * 为大数据打点设置缓存字段
     * @param cacheType 0:二级频道缓存 1: 筛选项缓存
     */
    private fun setCacheData(cacheType: Int = 0) {
        val data = getCacheTheatreInfo()
        data?.columnData?.forEachIndexed { columnPos, it ->
            it.videoData?.forEachIndexed { index, item ->
                if (item.bookType == 0) {
                    item.is_cache = true
                }
            }
            it.rankInfoVO?.forEachIndexed { i, rankInfoVO ->
                rankInfoVO.bookRankInfo?.forEachIndexed { index, item ->
                    item.is_cache = true
                }
            }
            it.rankData?.forEach { rankData ->
                rankData.bookData?.forEachIndexed { index, item ->
                    item.is_cache = true
                }
            }
        }
    }

    /**
     * @param isRedPoint:是否是红点频道。需求要求:第一次显示后，然后点击消失的时候上传此字段。
     */
    private fun trackAppViewScreen(isRedPoint:Boolean) {
        kotlin.runCatching {
            var title = "剧场-${channelGroup?.channelGroupName}"
            if ((currentChannel.channelId) > 0) {
                title += "-${currentChannel.channelName}"
            }
            val jsonObject = JSONObject()
            jsonObject.put("\$screen_name", "剧场")
            jsonObject.put("\$title", title)
            jsonObject.put("Origin", "书城")
            jsonObject.put("ColumnName", currentChannel.channelName)
            jsonObject.put("channel_group_name", channelGroup?.channelGroupName ?: "")
            jsonObject.put("channel_group_id", (channelGroup?.channelGroupId ?: "").toString())
            jsonObject.put("channel_group_pos", (channelGroup?.bdChannelGroupPos ?: "").toString())
            ChannelPointTrackUtil.redPointInfoAdd(isRedPoint, jsonObject)
            SensorsDataAPI.sharedInstance().track("\$AppViewScreen", jsonObject)
        }.onFailure { it.printStackTrace() }

    }

    // 根据事件刷新列表
    override fun refreshPage(list: CopyOnWriteArrayList<TheatreManager.RefreshType>) {
        var type = TheatreManager.RefreshType.wait
        if (list.isNotEmpty()) {
            if (list.contains(TheatreManager.RefreshType.autoRefreshOnPageshow)) {
                type = TheatreManager.RefreshType.autoRefreshOnPageshow
            }
            if (list.contains(TheatreManager.RefreshType.userPreferences)) {
                type = TheatreManager.RefreshType.userPreferences
            }
            if (list.contains(TheatreManager.RefreshType.check)) {
                type = TheatreManager.RefreshType.check
            }
            if (list.contains(TheatreManager.RefreshType.logout)) {
                type = TheatreManager.RefreshType.logout
            }
            if (list.contains(TheatreManager.RefreshType.login)) {
                type = TheatreManager.RefreshType.login
            }
            if (list.contains(TheatreManager.RefreshType.mainTabbar)) {
                type = TheatreManager.RefreshType.mainTabbar
            }
        }
        if (type == TheatreManager.RefreshType.wait) {
            type = TheatreManager.RefreshType.refresh
        }
        LogUtil.d(TAG, "refreshPage: ${list.size} type=${type}")
        loadTheatreData(showLoading = false, type = type)
    }

    // 刷新事件打点
    private fun trackRefresh(type: TheatreManager.RefreshType, result: Int) {
        DzTrackEvents.get().refreshPage().refreshType(type.value).refreshResult(result)
            .pageType("剧场页")
            .origin("剧场")
            .channelGroupId(channelGroup?.channelGroupId.toString())
            .channelGroupName(channelGroup?.channelGroupName)
            .columnName(currentChannel.channelName)
            .addParam("channel_group_pos", channelGroup?.bdChannelGroupPos)
            .addParam("channel_name", currentChannel.channelName)
            .track()
    }

    // 点击更多
    fun toMoreAction(mData: ColumnDataItem?) {
        if (mData?.moreActionType == 1) {
            //路由跳转
            val title =
                "剧场-${if (mData.columnTitle.isNullOrEmpty()) "剧场-${mData.bdChannelGroupName}" else "${mData.columnTitle}-${mData.bdChannelGroupName}"}"
            SchemeRouter.doUriJump(
                mData.moreActionContent + "&param={\"firstTierPlaySource\":\"剧场\",\"secondTierPlaySource\":\"剧场-${mData.bdChannelGroupName}\",\"thirdTierPlaySource\":\"${title.toString()}\"}"
            )
        } else if (!mData?.moreActionContent.isNullOrEmpty()) {
            //弹窗
            BBaseMR.get().flutterDialog().apply {
                title = mData?.moreTips
                content = mData?.moreActionContent
                showClose = true
                barrierDismissible = true
                mode = ACTIVITY
            }.onShow {

            }.onSure { url, _ ->
                SchemeRouter.doUriJump(url ?: "")
            }.onDismiss {

            }.start()
        }
    }

    // 拼接打点信息
    private fun splicingDoting(data: TheatreInfo?) {
        var index = 0
        if (data?.needConcat == true) {
            if (!data.columnData.isNullOrEmpty()) {
                data.columnData?.get(0)?.needConcat = true
            }
            if (!getCacheTheatreInfo()?.columnData.isNullOrEmpty()) {
                if (!getCacheTheatreInfo()?.columnData?.last()?.videoData.isNullOrEmpty()) {
                    if (!data.columnData.isNullOrEmpty()) {
                        val item =
                            getCacheTheatreInfo()?.columnData?.last()?.videoData?.last()
                        index = item?.columnPos ?: 0
                        data.columnData?.get(0)?.columnPos = index
                        data.columnData?.get(0)?.videoData?.forEachIndexed { i, itemData ->
                            itemData.mPosition = (item?.mPosition ?: 0) + i + 1
                            itemData.columnPos = item?.columnPos
                            LogUtil.d(
                                TAG,
                                "累计位置 拼接数据 ：accumulatePosition:  ${itemData.bookName}  ${itemData.accumulatePosition}  item.columnPos=${item?.columnPos}"
                            )
                        }
                    }
                }
            }
        }
        if (index == 0) {
            index = (getCacheTheatreInfo()?.columnData?.size ?: 1) - 1
        }
        var accumulatePosition = 0
        var last: ColumnDataItem? = null
        if (!getCacheTheatreInfo()?.columnData.isNullOrEmpty()) {
            last = getCacheTheatreInfo()?.columnData?.last()
            accumulatePosition = last?.accumulateIndex ?: 0
        }
        if (!data?.columnData.isNullOrEmpty()) {
            data?.columnData?.forEachIndexed { v, it ->
                it.bdChannelGroupName = channelGroup?.channelGroupName
                it.bdChannelGroupId = channelGroup?.channelGroupId
                it.bdChannelGroupPos = channelGroup?.bdChannelGroupPos ?: 0
                currentChannel.index =
                    channelGroup?.channelData?.indexOfFirst { it.channelId == currentChannel.channelId }
                it.currentChannel = currentChannel
                if (data.needConcat != true) {
                    it.columnPos = index + v
                } else {
                    index = last?.columnPos ?: 0
                    it.columnPos
                }
                it.accumulateIndex = accumulatePosition
                it.videoData?.forEachIndexed { i, item ->
                    item.columnPos = it.columnPos
                    item.elementId = it.columnId
                    item.styleTypeCn = it.styleTypeCn
                    if (data.needConcat == true) {
                        if (v != 0) {
                            item.mPosition = i + 1
                        }
                    } else {
                        item.mPosition = i + 1
                    }
                    if (item.bookType == 0) {
                        accumulatePosition += 1
                        item.accumulatePosition = accumulatePosition
                        it.accumulateIndex = accumulatePosition
                        LogUtil.d(
                            TAG,
                            "累计位置 ：拼接 videoData accumulatePosition: ${item.bookName}  $accumulatePosition  item.columnPos=${item.columnPos}"
                        )
                    }
                }
                it.rankInfoVO?.forEachIndexed { i, rankInfoVO ->
                    rankInfoVO.bookRankInfo?.forEachIndexed { j, item ->
                        accumulatePosition += 1
                        item.accumulatePosition = accumulatePosition
                        it.accumulateIndex = accumulatePosition
                        LogUtil.d(
                            TAG,
                            "累计位置 拼接 ：rankInfoVO accumulatePosition:${item.bookName}  ${item.accumulatePosition}"
                        )
                        item.mPosition = j + 1
                        item.columnPos = it.columnPos
                        item.elementId = it.columnId
                        item.styleTypeCn = it.styleTypeCn
                    }
                }
                index += 1
            }
        }
    }

    // 曝光打点
    override fun reportExposedItems(list: MutableSet<Any>) {
        super.reportExposedItems(list)
        TheatreManager.onExpose(list)
    }

    override fun jump2FilterDialog(tagPosition: Int?) {
        TheatreMR.get().theatreFilter().apply {
            channelGroupId = channelGroup?.channelGroupId
            channelId = tagPosition?.let {
                if (tagPosition >= 0) {
                    tabs.value?.get(it)?.channelId
                } else {
                    null
                }
            }
            onMenuSelected = { channelId ->
                //重置当前标签，当前标签没有选择了。
                currentSubTagId = -1
                val realChannelId = channelId ?: initChannel?.channelId
                realChannelId?.let {
                    menuSelectedChannelId.value = it
                }
            }
            onFilterSelected = { subTagId ->
                //重置当前频道，当前频道没有选择了。
                currentChannel = ChannelDataVo(channelId = 0, channelName = "", checkedFlag = false)
                filterSelectedSubTagId.value = subTagId
            }
        }.start()


        kotlin.runCatching {
            val jsonObject = JSONObject().apply {
                put("\$element_content", "${channelGroup?.channelTypeName}+筛选入口")
                put("\$element_position", "${channelGroup?.channelTypeName}")
                put("\$element_type", "筛选入口")
                put("\$screen_name", "剧场")
                put("\$title", "剧场-${channelGroup?.channelGroupName}")
            }
            TrackUtil.track("\$AppClick", jsonObject)
        }.onFailure {
            it.printStackTrace()
        }
    }

    // 有声书播放
    override fun playAudioBook(bookId: String) {
        // 遍历 initDataMap 中的所有频道数据
        initDataMap.forEach { (_, theatreInfo) ->
            theatreInfo?.columnData?.forEach { columnData ->
                columnData.videoData?.forEach { videoItem ->
                    videoItem.isPlaying = bookId == videoItem.bookId
                }
            }
        }
    }

    // 有声书暂停
    override fun pauseAudioBook() {
        // 遍历 initDataMap 中的所有频道数据
        initDataMap.forEach { (_, theatreInfo) ->
            theatreInfo?.columnData?.forEach { columnData ->
                columnData.videoData?.forEach { videoItem ->
                    videoItem.isPlaying = false
                }
            }
        }
    }
}

