package com.dz.business.theatre.refactor.component

import androidx.lifecycle.LiveData
import com.dz.business.base.theatre.data.BookInfoVo

/**
 * 组件通用事件处理
 */
interface BaseCompContract {

    val bookFavoriteState: LiveData<Boolean>


    /**
     * 短剧收藏点击事件
     */
    fun onBookFavoriteClick(bookInfoVo: BookInfoVo?)

    /**
     * 短剧取消收藏点击事件
     */
    fun onBookCancelFavoriteClick(bookInfoVo: BookInfoVo?)


    /**
     * 剧的收藏状态变化
     */
    fun onBookFavoriteStateChanged(isFavorite:Boolean)

}