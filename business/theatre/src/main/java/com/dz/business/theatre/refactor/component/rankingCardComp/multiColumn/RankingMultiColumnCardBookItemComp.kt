package com.dz.business.theatre.refactor.component.rankingCardComp.multiColumn

import android.content.Context
import android.util.AttributeSet
import com.dz.business.base.theatre.data.BookInfoVo
import com.dz.business.theatre.R
import com.dz.business.theatre.databinding.TheatreCompRankingMultiColumnBookItemBinding
import com.dz.business.theatre.refactor.manager.TheatreManager
import com.dz.foundation.base.utils.dp
import com.dz.foundation.imageloader.GlideUtils
import com.dz.foundation.imageloader.loadRoundImg
import com.dz.platform.common.base.ui.component.UIConstraintComponent
import com.dz.platform.common.router.SchemeRouter

/**
 * 多列排行榜 单部剧的信息
 */
class RankingMultiColumnCardBookItemComp :
    UIConstraintComponent<TheatreCompRankingMultiColumnBookItemBinding, BookInfoVo> {
    private var mContentPos = 0

    @JvmOverloads
    constructor(
        context: Context,
        attrs: AttributeSet? = null,
        defStyleAttr: Int = 0,
    ) : super(context, attrs, defStyleAttr)

    override fun initData() {

    }

    override fun initView() {

    }

    override fun onExpose(isFirstExpose: Boolean) {
        super.onExpose(isFirstExpose)
        mData?.let {
            TheatreManager.exposeBook(it)
        }
    }

    override fun initListener() {
        mViewBinding.clLayout.registerClickAction {
            val thirdTierPlay = "${mData?.channelName}-${mData?.rankName}"
            TheatreManager.onItemClick(mData, thirdTierPlay)
        }
    }

    override fun onBindRecyclerViewItem(itemData: BookInfoVo?, position: Int) {
        super.onBindRecyclerViewItem(itemData, position)
        mContentPos = position
        updateView(itemData)
    }


    private fun updateView(data: BookInfoVo?) {
        data?.let {
            mViewBinding.tvBookName.text = it.bookName
            mViewBinding.tvRankingNum.text = (it.listIndex + 1).toString()
            mViewBinding.ivCover.loadRoundImg(
                img = mData?.coverWap,
                radius = 4.dp,
                placeholder = R.drawable.bbase_ic_cover_default,
                error = R.drawable.bbase_ic_cover_default,
                width = 48,
                height = 69
            )
            //标签
            mViewBinding.tvTags.visibility = GONE
            it.bookTags?.let { bookTags->
                mViewBinding.tvTags.text = bookTags.joinToString(separator = " ")
                mViewBinding.tvTags.visibility = VISIBLE
            }


            // 热度值
            it.rankSrc?.let { rankSrc ->
                mViewBinding.tvRank.visibility = VISIBLE
                mViewBinding.tvRank.text = rankSrc
            }

            // 设置排名图标
            it.iconUrl?.let { url ->
                GlideUtils.loadImage(
                    context,
                    url,
                    mViewBinding.ivRank,
                    width = width,
                    height = height,
                )
            }

        }
    }

}