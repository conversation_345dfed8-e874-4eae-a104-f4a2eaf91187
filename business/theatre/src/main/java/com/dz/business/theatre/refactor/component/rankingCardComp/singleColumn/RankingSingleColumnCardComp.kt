package com.dz.business.theatre.refactor.component.rankingCardComp.singleColumn

import android.content.Context
import android.util.AttributeSet
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.dz.business.base.theatre.data.BookInfoVo
import com.dz.business.base.theatre.data.RankInfoVO
import com.dz.business.theatre.databinding.TheatreCompRankingSingleColumnCardBinding
import com.dz.business.theatre.refactor.adapter.RankingTagAdapter
import com.dz.business.theatre.refactor.adapter.SpaceItemDecoration
import com.dz.business.theatre.refactor.component.TheatreCardComp
import com.dz.business.theatre.refactor.component.TheatreCardFactory.CARD_TYPE_RANKING_SINGLE_COLUMN
import com.dz.business.theatre.refactor.manager.TheatreManager
import com.dz.business.theatre.refactor.network.bean.ColumnDataItem
import com.dz.business.theatre.refactor.page.subTab.ScrollTracker
import com.dz.foundation.base.utils.dp
import com.dz.foundation.ui.view.recycler.DzRecyclerViewCell

/**
 * 单列排行榜卡片
 * @constructor
 */
class RankingSingleColumnCardComp @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int? = 0
) : TheatreCardComp<TheatreCompRankingSingleColumnCardBinding>(
    context, attrs, defStyleAttr!!
) {
    private var selectedPosition = 0
    private var hasAddedDecoration = false
    private var tagAdapter: RankingTagAdapter? = null

    override fun initListener() {

        addScrollExposeListener(mViewBinding.recyclerView1)
        addScrollExposeListener(mViewBinding.recyclerView2)
        addScrollExposeListener(mViewBinding.recyclerView3)
        addScrollExposeListener(mViewBinding.recyclerView4)


    }

    private fun addScrollExposeListener(recyclerView: RecyclerView) {
        ScrollTracker(recyclerView, object : ScrollTracker.ExposeCallback {
            override fun onExposed(list: MutableSet<Any>) {
                TheatreManager.onExpose(list)
            }
        })
    }


    override fun onExpose(isFirstExpose: Boolean) {
        super.onExpose(isFirstExpose)
        val position="${mData?.columnPos}-0"
        TheatreManager.exposeRankingCard(columnDataItem = mData, position = position)
    }


    private fun initRecyclerView(rankList: MutableList<RankInfoVO>?) {
        if (rankList.isNullOrEmpty()) {
            mViewBinding.recyclerView1.visibility = GONE
            mViewBinding.recyclerView2.visibility = GONE
            mViewBinding.recyclerView3.visibility = GONE
            mViewBinding.recyclerView4.visibility = GONE
            return
        }

        rankList.forEachIndexed { index, item ->
            val cellList = mutableListOf<DzRecyclerViewCell<*>>()
            val size = item.bookRankInfo?.size ?: 0
            item.bookRankInfo?.forEachIndexed { bookIndex, bookItem ->
                bookItem.listIndex = bookIndex
                bookItem.groupId = mData?.bdChannelGroupId
                bookItem.groupName = mData?.bdChannelGroupName
                bookItem.groupPos = mData?.bdChannelGroupPos
                bookItem.channelPos = mData?.currentChannel?.index
                bookItem.channelName = mData?.currentChannel?.channelName
                bookItem.channelId = mData?.currentChannel?.channelId?.toLong()
                bookItem.columnId = (mData?.columnId ?: "").toString()
                bookItem.columnName = mData?.columnTitle
                bookItem.columnPos = mData?.columnPos
                bookItem.styleTypeCn = mData?.styleTypeCn
                bookItem.columnTitle = mData?.columnTitle
                bookItem.rankName = item.rankName
                val cell = DzRecyclerViewCell<BookInfoVo>().apply {
                    viewClass = RankingSingleColumnCardBookItemComp::class.java
                    viewData = bookItem
                }
                cellList.add(cell)
                //最后一个查看更多卡片
                if (bookIndex == size - 1) {
                    val moreCell = DzRecyclerViewCell<String>().apply {
                        viewClass = RankingSingleColumnCardMoreItemComp::class.java
                        viewData = item.deeplink
                    }
                    cellList.add(moreCell)
                }
            }



            when (index) {
                0 -> {
                    mViewBinding.recyclerView1.removeAllCells()
                    mViewBinding.recyclerView1.addCells(cellList)
                    mViewBinding.recyclerView1.scrollToPosition(0)
                }

                1 -> {
                    mViewBinding.recyclerView2.removeAllCells()
                    mViewBinding.recyclerView2.addCells(cellList)
                    mViewBinding.recyclerView2.scrollToPosition(0)
                }

                2 -> {
                    mViewBinding.recyclerView3.removeAllCells()
                    mViewBinding.recyclerView3.addCells(cellList)
                    mViewBinding.recyclerView3.scrollToPosition(0)
                }

                3 -> {
                    mViewBinding.recyclerView4.removeAllCells()
                    mViewBinding.recyclerView4.addCells(cellList)
                    mViewBinding.recyclerView4.scrollToPosition(0)
                }

            }


        }
    }

    private fun showSelectedRecyclerView(index: Int) {
        mViewBinding.recyclerView1.visibility = if (index == 0) View.VISIBLE else View.GONE
        mViewBinding.recyclerView2.visibility = if (index == 1) View.VISIBLE else View.GONE
        mViewBinding.recyclerView3.visibility = if (index == 2) View.VISIBLE else View.GONE
        mViewBinding.recyclerView4.visibility = if (index == 3) View.VISIBLE else View.GONE
    }


    /**
     * 初始化标签
     * @param list MutableList<ChannelDataVo>?
     */
    private fun initTagView(list: List<RankInfoVO>?) {
        if (list.isNullOrEmpty()) {
            mViewBinding.nsLayout.visibility = View.GONE
            return
        }
        mViewBinding.nsLayout.visibility = View.VISIBLE

        if (tagAdapter == null) {
            // 首次创建adapter
            tagAdapter = RankingTagAdapter(list).apply {
                setOnItemClickListener { rankId ->
                    list.forEachIndexed { index, rankInfoVO ->
                        if (rankInfoVO.rankId == rankId) {
                            selectedPosition = index
                            showSelectedRecyclerView(index)
                            val position="${mData?.columnPos}-${index}"
                            TheatreManager.exposeRankingCard(rankInfoVO, position = position)
                            return@forEachIndexed
                        }
                    }
                }
            }
            
            // 首次设置LayoutManager和adapter
            mViewBinding.rvTabs.layoutManager =
                LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
            mViewBinding.rvTabs.adapter = tagAdapter
            
            // 添加间距装饰
            if (!hasAddedDecoration) {
                mViewBinding.rvTabs.addItemDecoration(SpaceItemDecoration(9.dp))
                hasAddedDecoration = true
            }
        } else {
            // 复用已有adapter，只更新数据
            tagAdapter?.updateData(list)
        }
        
        // 更新选中位置
        tagAdapter?.selectedPosition = selectedPosition
        showSelectedRecyclerView(selectedPosition)
    }

    override fun bindData(data: ColumnDataItem?) {
        super.bindData(data)
        initRecyclerView(data?.rankInfoVO)
        initTagView(data?.rankInfoVO)
    }


    override fun getCardType(): Int {
        return CARD_TYPE_RANKING_SINGLE_COLUMN
    }
}