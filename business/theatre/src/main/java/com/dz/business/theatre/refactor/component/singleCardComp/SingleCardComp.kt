package com.dz.business.theatre.refactor.component.singleCardComp

import android.content.Context
import android.util.AttributeSet
import com.dz.business.base.theatre.data.BookInfoVo
import com.dz.business.theatre.databinding.TheatreCompSingleCardBinding
import com.dz.business.theatre.refactor.component.TheatreCardComp
import com.dz.business.theatre.refactor.component.TheatreCardFactory.CARD_TYPE_SINGLE
import com.dz.business.theatre.refactor.component.singleCardComp.bookItem.SingleCardBookItemComp
import com.dz.business.theatre.refactor.network.bean.ColumnDataItem
import com.dz.foundation.ui.view.custom.ActionListenerOwner
import com.dz.foundation.ui.view.recycler.DzRecyclerViewCell

/**
 * 单卡
 * @constructor
 */
class SingleCardComp @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int? = 0
) : TheatreCardComp<TheatreCompSingleCardBinding>(
    context, attrs, defStyleAttr!!
), ActionListenerOwner<TheatreCardComp.ViewActionListener> {


    /**
     * 更新标题
     */
    private fun updateTitle(data: ColumnDataItem?) {
        if (data?.columnTitle?.isBlank() == true || data?.needConcat == true) {
            mViewBinding.tvTitle.visibility = GONE
        } else {
            mViewBinding.tvTitle.visibility = VISIBLE
            mViewBinding.tvTitle.text = data?.columnTitle
        }

        data?.moreTips?.let {
            mViewBinding.tvMoreAction.visibility = VISIBLE
            mViewBinding.tvMoreAction.text = it
            mViewBinding.tvMoreAction.setOnClickListener {
                mActionListener?.moreActionContentClick(mData)
            }
        }
    }

    override fun bindData(data: ColumnDataItem?) {
        super.bindData(data)
        updateTitle(data)
        val cellList = mutableListOf<DzRecyclerViewCell<BookInfoVo>>()
        data?.videoData?.forEachIndexed { index, item ->
            item.listIndex = index
            item.groupId = data.bdChannelGroupId
            item.groupName = data.bdChannelGroupName
            item.groupPos = data.bdChannelGroupPos
            item.channelPos = data.currentChannel?.index
            item.channelName = data.currentChannel?.channelName
            item.channelId = data.currentChannel?.channelId?.toLong()
            item.columnId = (data.columnId ?: "").toString()
            item.columnName = data.columnTitle
            item.columnPos = data.columnPos
            item.styleTypeCn = data.styleTypeCn
            item.columnTitle = data.columnTitle
            val cell = DzRecyclerViewCell<BookInfoVo>().apply {
                viewClass = SingleCardBookItemComp::class.java
                viewData = item
            }
            cellList.add(cell)
        }
        mViewBinding.recyclerView.removeAllCells()
        mViewBinding.recyclerView.addCells(cellList)
    }

    override fun getCardType(): Int {
        return CARD_TYPE_SINGLE
    }
}