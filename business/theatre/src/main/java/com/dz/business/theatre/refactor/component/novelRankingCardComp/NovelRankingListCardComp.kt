package com.dz.business.theatre.refactor.component.novelRankingCardComp

import android.content.Context
import android.util.AttributeSet
import com.dz.business.base.theatre.data.BookInfoVo
import com.dz.business.base.track.TrackUtil
import com.dz.business.theatre.databinding.TheatreCompNovelRankListCardBinding
import com.dz.business.theatre.refactor.component.TheatreCardComp
import com.dz.business.theatre.refactor.component.TheatreCardFactory.CARD_TYPE_NOVEL_RANKING
import com.dz.business.theatre.refactor.manager.TheatreManager
import com.dz.business.theatre.refactor.network.bean.ColumnDataItem
import com.dz.business.theatre.refactor.network.bean.RankDataInfo
import com.dz.business.theatre.refactor.page.subTab.ScrollTracker
import com.dz.foundation.ui.view.recycler.DzExposeRvItemUtil
import com.dz.foundation.ui.view.recycler.DzRecyclerViewCell
import com.therouter.TheRouter
import org.json.JSONObject
import java.util.concurrent.ConcurrentHashMap

/**
 * 小说榜单卡片列表
 * @constructor
 */
class NovelRankingListCardComp @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int? = 0
) : TheatreCardComp<TheatreCompNovelRankListCardBinding>(
    context,
    attrs,
    defStyleAttr!!
) {
    private val initDataMap: ConcurrentHashMap<Int, MutableList<BookInfoVo>?> =
        ConcurrentHashMap()
    private var dzExposeRvItemUtil: DzExposeRvItemUtil = DzExposeRvItemUtil()
    private var scrollTracker: ScrollTracker? = null
    override fun initView() {
        super.initView()
        mViewBinding.rv.itemAnimator = null
        mViewBinding.rvTabs.itemAnimator = null
    }

    override fun initListener() {
        //更多
        mViewBinding.tvMoreAction.registerClickAction {
            trackToRankPage()
        }
        mViewBinding.ivMore.registerClickAction {
            trackToRankPage()
        }
        scrollTracker =
            ScrollTracker(mViewBinding.rv, object : ScrollTracker.ExposeCallback {
                override fun onExposed(list: MutableSet<Any>) {
                    TheatreManager.onExpose(list)
                }
            })
    }

    private fun trackToRankPage() {
        kotlin.runCatching {
            TheRouter.build("flutter/container?url=flutter/BookRankPage").navigation()
            val jsonObject = JSONObject().apply {
                put("\$element_content", "全部榜单")
                put("\$element_type", "小说tabbar")
                put("\$screen_name", "小说书城")
                put("\$title", "排行榜")
            }
            TrackUtil.track("\$AppClick", jsonObject)
        }.onFailure {
            it.printStackTrace()
        }
    }

    override fun onExpose(isFirstExpose: Boolean) {
        super.onExpose(isFirstExpose)
        dzExposeRvItemUtil.directExposeRecyclerItem(mViewBinding.rv)
    }

    override fun onBindRecyclerViewItem(model: ColumnDataItem?, position: Int) {
        super.onBindRecyclerViewItem(model, position)
        model?.run {
            initDataMap.clear()
            val tabsCell = mutableListOf<DzRecyclerViewCell<*>>()
            val allCells = mutableListOf<DzRecyclerViewCell<*>>()
            rankData?.forEachIndexed { q, m ->
                val allItem = mutableListOf<BookInfoVo>()
                if (m.bookData != null && !m.bookData.isNullOrEmpty()) {
                    m.bookData?.let { bookData ->
                        for (i in 0 until bookData.size step 4) {
                            val cardItems = mutableListOf<BookInfoVo>()
                            for (j in 0 until 4) {
                                if (i + j < bookData.size) {
                                    bookData[i + j].let { item ->
                                        item.listIndex = i + j + 1
                                        item.rankPos = q
                                        item.groupId = model.bdChannelGroupId
                                        item.groupName = model.bdChannelGroupName
                                        item.groupPos = model.bdChannelGroupPos
                                        item.channelPos = model.currentChannel?.index
                                        item.channelName = model.currentChannel?.channelName
                                        item.channelId =
                                            model.currentChannel?.channelId?.toLong()
                                        item.columnId = (model.columnId ?: "").toString()
                                        item.columnName = model.columnTitle
                                        item.columnPos = model.columnPos
                                        item.rankTitle = m.rankTitle
                                        item.rankId = m.rankId
                                        item.styleTypeCn = model.styleTypeCn
                                        item.styleType = model.styleType
                                        item.columnTitle = model.columnTitle
                                    }
                                    cardItems.add(bookData[i + j])
                                }
                            }
                            allItem.addAll(cardItems)
                            if (q == 0) {
                                DzRecyclerViewCell<List<BookInfoVo>>().apply {
                                    viewClass = NovelRankingCardComp::class.java
                                    viewData = cardItems
                                    allCells.add(this)
                                }
                            }
                        }
                    }
                }
                initDataMap[m.rankId ?: 0] = allItem
                DzRecyclerViewCell<RankDataInfo>().apply {
                    if (q == 0) {
                        m.select = true
                    }
                    viewClass = NovelRankingTabComp::class.java
                    viewData = m
                    tabsCell.add(this)
                    this.setActionListener(object : NovelRankingTabComp.ViewActionListener {
                        override fun tabClick(data: RankDataInfo?) {
                            refreshCells(data, model)
                        }
                    })
                }
            }
            if (tabsCell.isEmpty()) {
                mViewBinding.clLayout.visibility = GONE
                mViewBinding.rv.visibility = GONE
            } else {
                mViewBinding.clLayout.visibility = VISIBLE
                mViewBinding.rv.visibility = VISIBLE
                mViewBinding.rvTabs.visibility = VISIBLE
                mViewBinding.rvTabs.removeAllCells()
                mViewBinding.rvTabs.addCells(tabsCell)
                mViewBinding.rv.removeAllCells()
                mViewBinding.rv.addCells(allCells)
                mViewBinding.rv.scrollToPosition(0)
                mViewBinding.rvTabs.scrollToPosition(0)
            }
        }
    }

    fun refreshCells(data: RankDataInfo?, model: ColumnDataItem) {
        kotlin.runCatching {
            if (!initDataMap[data?.rankId ?: 0].isNullOrEmpty()) {
                val allCells = mutableListOf<DzRecyclerViewCell<*>>()
                initDataMap[data?.rankId ?: 0]?.let { list ->
                    for (i in 0 until list.size step 4) {
                        val cardItems = mutableListOf<BookInfoVo>()
                        for (j in 0 until 4) {
                            if (i + j < list.size) {
                                cardItems.add(list[i + j])
                            }
                        }
                        DzRecyclerViewCell<List<BookInfoVo>>().apply {
                            viewClass = NovelRankingCardComp::class.java
                            viewData = cardItems
                            allCells.add(this)
                        }
                    }
                }
                mViewBinding.rv.removeAllCells()
                mViewBinding.rv.addCells(allCells)
                dzExposeRvItemUtil.directExposeRecyclerItem(mViewBinding.rv)
                mViewBinding.rv.scrollToPosition(0)
                mData?.rankData?.forEach {
                    it.select = it.rankId == data?.rankId
                }
                mViewBinding.rvTabs.notifyDataSetChanged()
                val jsonObject = JSONObject().apply {
                    put("\$element_content", data?.rankTitle)
                    put("\$element_type", "小说tab")
                    put("\$screen_name", "小说书城")
                    put("\$title", "${model.currentChannel?.channelName ?: " "}-排行榜")
                }
                TrackUtil.track("\$AppClick", jsonObject)
            }
        }.onFailure {
            it.printStackTrace()
        }
    }

    override fun getCardType(): Int {
        return CARD_TYPE_NOVEL_RANKING
    }
}