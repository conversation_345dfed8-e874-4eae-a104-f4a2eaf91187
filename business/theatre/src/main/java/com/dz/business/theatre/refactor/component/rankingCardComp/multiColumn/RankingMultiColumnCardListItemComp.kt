package com.dz.business.theatre.refactor.component.rankingCardComp.multiColumn

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.dz.business.base.theatre.data.BookInfoVo
import com.dz.business.base.theatre.data.RankInfoVO
import com.dz.business.theatre.R
import com.dz.business.theatre.databinding.TheatreCompRankingMultiColumnListItemBinding
import com.dz.business.theatre.refactor.manager.TheatreManager
import com.dz.business.theatre.refactor.page.subTab.ScrollTracker
import com.dz.foundation.base.utils.ScreenUtil
import com.dz.foundation.ui.view.recycler.DzRecyclerView
import com.dz.foundation.ui.view.recycler.DzRecyclerViewCell
import com.dz.platform.common.base.ui.component.UIConstraintComponent
import com.dz.platform.common.router.SchemeRouter

/**
 * 多列排行榜的 item 布局
 */
class RankingMultiColumnCardListItemComp :
    UIConstraintComponent<TheatreCompRankingMultiColumnListItemBinding, RankInfoVO> {
    private var mContentPos = 0

    @JvmOverloads
    constructor(
        context: Context,
        attrs: AttributeSet? = null,
        defStyleAttr: Int = 0,
    ) : super(context, attrs, defStyleAttr)

    override fun initData() {

    }

    override fun initView() {

    }

    override fun onExpose(isFirstExpose: Boolean) {
        super.onExpose(isFirstExpose)
        val position = "${mData?.columnPos}-${mData?.listIndex}"
        TheatreManager.exposeRankingCard(rankInfo = mData, position = position)
    }

    override fun initListener() {

        mViewBinding.tvTitle.registerClickAction {
            SchemeRouter.doUriJump(mData?.deeplink)
        }

        ScrollTracker(mViewBinding.recyclerView, object : ScrollTracker.ExposeCallback {
            override fun onExposed(list: MutableSet<Any>) {
                TheatreManager.onExpose(list)
            }
        })


    }

    override fun onCreateRecyclerViewItem(
        parent: DzRecyclerView?,
        itemView: View?
    ): RecyclerView.LayoutParams {
        return RecyclerView.LayoutParams(
            ScreenUtil.dip2px(context, 224),
            ViewGroup.LayoutParams.WRAP_CONTENT,
        )
    }

    override fun onBindRecyclerViewItem(data: RankInfoVO?, position: Int) {
        super.onBindRecyclerViewItem(data, position)
        mContentPos = position
        updateView(data)
    }


    private fun updateView(data: RankInfoVO?) {
        val linearLayoutManager: LinearLayoutManager = object : LinearLayoutManager(context) {
            override fun canScrollVertically(): Boolean {
                return false
            }
        }
        data?.let {
            mViewBinding.tvTitle.text = it.rankName
            mViewBinding.viewHeaderBg.background = ContextCompat.getDrawable(
                context,
                if ((it.listIndex?.rem(2)) == 0)
                    R.drawable.theatre_ranking_multi_column_header_bg_orange
                else
                    R.drawable.theatre_ranking_single_column_rank_bg_blue
            )
            val cellList = mutableListOf<DzRecyclerViewCell<BookInfoVo>>()
            val topThreeBooks = it.bookRankInfo?.take(3) ?: emptyList()
            topThreeBooks.forEachIndexed { index, bookItem ->
                bookItem.listIndex = index
                //一级
                bookItem.groupId = data.groupId
                bookItem.groupName = data.groupName
                bookItem.groupPos = data.groupPos
                //二级
                bookItem.channelPos = data.channelPos
                bookItem.channelName = data.channelName
                bookItem.channelId = data.channelId
                //栏目大卡
                bookItem.columnId = (data.columnId ?: "").toString()
                bookItem.columnName = data.columnTitle
                bookItem.columnPos = data.columnPos
                //栏目类型和栏目标题
                bookItem.styleTypeCn = data.styleTypeCn
                bookItem.columnTitle = data.columnTitle

                //榜单名称
                bookItem.rankName = data.rankName
                bookItem.iconUrl = data.iconUrl
                val cell = DzRecyclerViewCell<BookInfoVo>().apply {
                    viewClass = RankingMultiColumnCardBookItemComp::class.java
                    viewData = bookItem
                }
                cellList.add(cell)

            }

            mViewBinding.recyclerView.layoutManager = linearLayoutManager
            mViewBinding.recyclerView.removeAllCells()
            mViewBinding.recyclerView.addCells(cellList)
        }
    }

}