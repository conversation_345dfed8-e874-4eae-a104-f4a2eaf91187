package com.dz.business.theatre.refactor.component

import com.dz.business.base.ui.component.EmptyComp
import com.dz.business.theatre.refactor.component.audioBookComp.double.AudioDoubleListCardComp
import com.dz.business.theatre.refactor.component.audioBookComp.three.AudioThreeListCardComp
import com.dz.business.theatre.refactor.component.bannerCardComp.BannerCardComp
import com.dz.business.theatre.refactor.component.community.discussArticleCardComp.DiscussArticleCardComp
import com.dz.business.theatre.refactor.component.community.discussOperationCardComp.DiscussOperationCardComp
import com.dz.business.theatre.refactor.component.community.discussVideoCardComp.DiscussVideoCardComp
import com.dz.business.theatre.refactor.component.doubleCardComp.DoubleCardComp
import com.dz.business.theatre.refactor.component.dramasCardComp.DramaListCardComp
import com.dz.business.theatre.refactor.component.novelDoubleCardComp.NovelHorizontalCardComp
import com.dz.business.theatre.refactor.component.novelRankingCardComp.NovelRankingListCardComp
import com.dz.business.theatre.refactor.component.novelSigleCardComp.NovelSingleCardComp
import com.dz.business.theatre.refactor.component.operationCardComp.OperationCardComp
import com.dz.business.theatre.refactor.component.rankingCardComp.RankingListCardComp
import com.dz.business.theatre.refactor.component.rankingCardComp.multiColumn.RankingMultiColumnCardComp
import com.dz.business.theatre.refactor.component.rankingCardComp.singleColumn.RankingSingleColumnCardComp
import com.dz.business.theatre.refactor.component.rankingCardComp.threeColumn.RankingThreeColumnCardComp
import com.dz.business.theatre.refactor.component.reservation.ReservationListCardComp
import com.dz.business.theatre.refactor.component.singleCardComp.SingleCardComp
import com.dz.business.theatre.refactor.component.threeCardComp.ThreeCardComp
import com.dz.foundation.ui.view.recycler.DzRecyclerViewItem

/**
 * 剧场页卡片样式工厂类
 */
object TheatreCardFactory {

    //大卡
    const val CARD_TYPE_SINGLE = 1

    //双卡
    const val CARD_TYPE_DOUBLE = 2

    //三卡
    const val CARD_TYPE_THREE = 3

    //播单
    const val CARD_TYPE_DRAMAS = 4

    //排行榜
    const val CARD_TYPE_RANKING = 6

    //轮播图
    const val CARD_TYPE_BANNER = 7

    //排行榜单列
    const val CARD_TYPE_RANKING_SINGLE_COLUMN = 8

    //排行榜多列
    const val CARD_TYPE_RANKING_MULTI_COLUMN = 9


    //运营位
    const val CARD_TYPE_OPERATION = 20

    //排行榜三卡
    const val CARD_TYPE_RANKING_THREE_COLUMN = 10

    // 小说排行榜
    const val CARD_TYPE_NOVEL_RANKING = 4

    // 小说猜你喜欢
    const val CARD_TYPE_NOVEL_MORE = 1

    // 小说横 4*N
    const val CARD_TYPE_NOVEL_FOUR_H = 2

    // 小说竖 4*N
    const val CARD_TYPE_NOVEL_FOUR_V = 5


    // 社区 图文
    const val CARD_TYPE_COMMUNITY_ARTICLE = 0

    // 社区 视频
    const val CARD_TYPE_COMMUNITY_VIDEO = 1

    // 社区 运营位
    const val CARD_TYPE_COMMUNITY_OPERATION = 2

    // 社区 聚合话题
    const val CARD_TYPE_COMMUNITY_AGGREGATE = 3

    //新片预约
    const val CARD_TYPE_RESERVATION = 11

    //有声书三卡
    const val CARD_TYPE_AUDIO_THREE  = 22

    //有声书双卡
    const val CARD_TYPE_AUDIO_DOUBLE = 21


    /**
     * 获取组件的UI实例
     */
    fun getCardViewClass(styleType: Int?): Class<out DzRecyclerViewItem<Any>>? {
        return when (styleType) {
            CARD_TYPE_SINGLE -> SingleCardComp::class.java as Class<out DzRecyclerViewItem<Any>>
            CARD_TYPE_DOUBLE -> DoubleCardComp::class.java as Class<out DzRecyclerViewItem<Any>>
            CARD_TYPE_THREE -> ThreeCardComp::class.java as Class<out DzRecyclerViewItem<Any>>
            CARD_TYPE_RANKING -> RankingListCardComp::class.java as Class<out DzRecyclerViewItem<Any>>
            CARD_TYPE_RANKING_SINGLE_COLUMN -> RankingSingleColumnCardComp::class.java as Class<out DzRecyclerViewItem<Any>>
            CARD_TYPE_RANKING_MULTI_COLUMN -> RankingMultiColumnCardComp::class.java as Class<out DzRecyclerViewItem<Any>>
            CARD_TYPE_BANNER -> BannerCardComp::class.java as Class<out DzRecyclerViewItem<Any>>
            CARD_TYPE_OPERATION -> OperationCardComp::class.java as Class<out DzRecyclerViewItem<Any>>
            CARD_TYPE_DRAMAS -> DramaListCardComp::class.java as Class<out DzRecyclerViewItem<Any>>
            CARD_TYPE_RANKING_THREE_COLUMN -> RankingThreeColumnCardComp::class.java as Class<out DzRecyclerViewItem<Any>>
            CARD_TYPE_RESERVATION -> ReservationListCardComp::class.java as Class<out DzRecyclerViewItem<Any>>
            CARD_TYPE_AUDIO_THREE -> AudioThreeListCardComp::class.java as Class<out DzRecyclerViewItem<Any>>
            CARD_TYPE_AUDIO_DOUBLE -> AudioDoubleListCardComp::class.java as Class<out DzRecyclerViewItem<Any>>
            else -> EmptyComp::class.java
        }
    }

    /**
     * 获取组件的UI实例
     */
    fun getNovelCardViewClass(styleType: Int?): Class<out DzRecyclerViewItem<Any>>? {
        return when (styleType) {
            CARD_TYPE_NOVEL_MORE -> NovelSingleCardComp::class.java as Class<out DzRecyclerViewItem<Any>>
            CARD_TYPE_NOVEL_FOUR_H -> NovelHorizontalCardComp::class.java as Class<out DzRecyclerViewItem<Any>>
            CARD_TYPE_NOVEL_FOUR_V -> NovelSingleCardComp::class.java as Class<out DzRecyclerViewItem<Any>>
            CARD_TYPE_NOVEL_RANKING -> NovelRankingListCardComp::class.java as Class<out DzRecyclerViewItem<Any>>
            else -> EmptyComp::class.java
        }
    }

    /**
     * 获取社区页组件的UI实例
     */
    fun getCommunityCardViewClass(ctype: Int?): Class<out DzRecyclerViewItem<Any>>? {
        return when (ctype) {
            CARD_TYPE_COMMUNITY_ARTICLE -> DiscussArticleCardComp::class.java as Class<out DzRecyclerViewItem<Any>>
            CARD_TYPE_COMMUNITY_VIDEO -> DiscussVideoCardComp::class.java as Class<out DzRecyclerViewItem<Any>>
            CARD_TYPE_COMMUNITY_OPERATION -> DiscussOperationCardComp::class.java as Class<out DzRecyclerViewItem<Any>>
            CARD_TYPE_COMMUNITY_AGGREGATE -> DiscussOperationCardComp::class.java as Class<out DzRecyclerViewItem<Any>>
            else -> EmptyComp::class.java
        }
    }
}