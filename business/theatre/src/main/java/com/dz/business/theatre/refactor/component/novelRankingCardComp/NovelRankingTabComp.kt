package com.dz.business.theatre.refactor.component.novelRankingCardComp

import android.content.Context
import android.graphics.Typeface
import android.util.AttributeSet
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.dz.business.base.R
import com.dz.business.theatre.databinding.TheatreNovelTabItemBinding
import com.dz.business.theatre.refactor.network.bean.RankDataInfo
import com.dz.business.track.utis.ElementClickUtils
import com.dz.foundation.base.utils.ScreenUtil
import com.dz.foundation.ui.view.custom.ActionListener
import com.dz.foundation.ui.view.custom.ActionListenerOwner
import com.dz.foundation.ui.view.recycler.DzRecyclerView
import com.dz.platform.common.base.ui.component.UIConstraintComponent

/**
 * 榜单卡片
 * @constructor
 */
class NovelRankingTabComp @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int? = 0
) : UIConstraintComponent<TheatreNovelTabItemBinding, RankDataInfo>(
    context,
    attrs,
    defStyleAttr!!
), ActionListenerOwner<NovelRankingTabComp.ViewActionListener> {
    interface ViewActionListener : ActionListener {
        /**
         * tab点击
         */
        fun tabClick(data: RankDataInfo?)
    }

    override var mActionListener: ViewActionListener? = null

    override fun initData() {}

    override fun initView() {}

    override fun onCreateRecyclerViewItem(
        parent: DzRecyclerView?,
        itemView: View?
    ): RecyclerView.LayoutParams {
        return RecyclerView.LayoutParams(
            ViewGroup.LayoutParams.WRAP_CONTENT,
            ScreenUtil.dip2px(context, 22),
        )
    }

    override fun initListener() {
        ElementClickUtils.ignoreAutoTrack(mViewBinding.clLayout)
        mViewBinding.clLayout.registerClickAction {
            mActionListener?.tabClick(mData)
        }
    }

    override fun onBindRecyclerViewItem(model: RankDataInfo?, position: Int) {
        super.onBindRecyclerViewItem(model, position)
        model?.let { m ->
            mViewBinding.tvTag.text = m.rankTitle
            mViewBinding.tvTag.setTextColor(
                if (model.select == true) {
                    ContextCompat.getColor(context, R.color.common_FF191919)
                } else {
                    ContextCompat.getColor(context, R.color.common_FF959595)
                }
            )
            mViewBinding.tvTag.setTypeface(
                null,
                if (model.select == true) {
                    Typeface.BOLD
                } else {
                    Typeface.NORMAL
                }
            )
        }
    }
}