package com.dz.business.theatre.refactor.component.novelDoubleCardComp.bookItem

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import com.dz.business.base.theatre.data.BookInfoVo
import com.dz.business.base.vm.getViewModel
import com.dz.business.theatre.R
import com.dz.business.theatre.databinding.TheatreCompHorizontalCardNovelItemBinding
import com.dz.business.theatre.refactor.manager.TheatreManager
import com.dz.business.track.utis.ElementClickUtils
import com.dz.foundation.base.utils.ScreenUtil
import com.dz.foundation.imageloader.loadRoundImg
import com.dz.platform.common.base.ui.component.UIConstraintComponent
import com.therouter.TheRouter

/**
 * 双卡卡样式-横卡小说
 */
class NovelHorizontalItemComp :
    UIConstraintComponent<TheatreCompHorizontalCardNovelItemBinding, BookInfoVo> {
    private var viewModel: NovelHorizontalItemVM? = null
    private var contract: NovelHorizontalItemContract? = null

    @JvmOverloads
    constructor(
        context: Context,
        attrs: AttributeSet? = null,
        defStyleAttr: Int = 0,
    ) : super(context, attrs, defStyleAttr)

    override fun initData() {
        viewModel = getViewModel(NovelHorizontalItemVM::class.java)
        contract = viewModel
    }

    override fun initView() {

    }

    override fun onExpose(isFirstExpose: Boolean) {
        super.onExpose(isFirstExpose)
        mData?.let {
            TheatreManager.exposeNovel(it)
        }
    }

    override fun initListener() {
        ElementClickUtils.ignoreAutoTrack(mViewBinding.clLayout)
        mViewBinding.clLayout.registerClickAction {
            TheatreManager.onNovelClick(mData)
        }
    }

    override fun onBindRecyclerViewItem(model: BookInfoVo?, position: Int) {
        super.onBindRecyclerViewItem(model, position)
        updateView(model)
    }

    @SuppressLint("SetTextI18n")
    private fun updateView(data: BookInfoVo?) {
        data?.let { m ->
            mViewBinding.tvBookName.text = m.bookName
            mViewBinding.tvBookScore.text = "${m.bookScore}分"
            mViewBinding.ivCover.loadRoundImg(
                img = m.coverWap,
                radius = ScreenUtil.dip2px(context, 4),
                placeholder = R.drawable.bbase_ic_cover_default,
                error = R.drawable.bbase_ic_cover_default,
                width = 78,
                height = 102
            )
        }
    }
}