package com.dz.business.theatre.refactor.component.bannerOperationCardComp

import android.content.Context
import android.util.AttributeSet
import com.dz.business.theatre.R
import com.dz.business.theatre.databinding.TheatreCompCommunityBannerListItemBinding
import com.dz.business.theatre.refactor.network.bean.ActivityInfoVo
import com.dz.business.theatre.util.CommunityTrackUtils
import com.dz.business.track.utis.ElementClickUtils
import com.dz.foundation.base.utils.ScreenUtil
import com.dz.foundation.imageloader.loadRoundImg
import com.dz.platform.common.base.ui.component.UIConstraintComponent
import com.dz.platform.common.router.SchemeRouter

/**
 * 社区顶部运营位的 item布局
 */
class BannerListItemComp : UIConstraintComponent<TheatreCompCommunityBannerListItemBinding, ActivityInfoVo> {
    @JvmOverloads
    constructor(
        context: Context,
        attrs: AttributeSet? = null,
        defStyleAttr: Int = 0
    ) : super(context, attrs, defStyleAttr)

    override fun initData() {

    }

    override fun initView() {

    }

    override fun initListener() {
        ElementClickUtils.ignoreAutoTrack(mViewBinding.clLayout)
        ElementClickUtils.ignoreAutoTrack(mViewBinding.ivCover)
        ElementClickUtils.ignoreAutoTrack(mViewBinding.root)
        mViewBinding.root.registerClickAction {
//            TheatreManager.onItemClick(mData)
        }
    }

    override fun bindData(data: ActivityInfoVo?) {
        super.bindData(data)
        data?.let {
            //剧封
            mViewBinding.ivCover.loadRoundImg(
                img = it.img,
                radius = ScreenUtil.dip2px(context, 8),
                placeholder = R.drawable.bbase_ic_cover_default_new,
                error = R.drawable.bbase_ic_cover_default_new,
            )
            mViewBinding.ivCover.registerClickAction {
                CommunityTrackUtils.trackOperationClick(mData)
                SchemeRouter.doUriJump(data.deeplink)
            }
        }
    }

}