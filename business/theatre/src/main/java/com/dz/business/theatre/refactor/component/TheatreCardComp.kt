package com.dz.business.theatre.refactor.component

import android.content.Context
import android.util.AttributeSet
import androidx.databinding.ViewDataBinding
import com.dz.business.base.theatre.data.BookInfoVo
import com.dz.business.theatre.refactor.network.bean.ColumnDataItem
import com.dz.foundation.ui.view.custom.ActionListener
import com.dz.foundation.ui.view.custom.ActionListenerOwner
import com.dz.platform.common.base.ui.component.UIConstraintComponent

/**
 * 基础卡片类型
 */
open class TheatreCardComp<VB : ViewDataBinding> @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int? = 0,
) : UIConstraintComponent<VB, ColumnDataItem>(
    context, attrs, defStyleAttr!!
), ActionListenerOwner<TheatreCardComp.ViewActionListener> {

    interface ViewActionListener : ActionListener {

        /**
         * 卡片右上角MoreAction点击事件
         */
        fun moreActionContentClick(bookInfoVo: ColumnDataItem?)

        /**
         * 短剧收藏点击事件
         */
        fun onBookFavoriteClick(bookInfoVo: BookInfoVo?)

        /**
         * 短剧取消收藏点击事件
         */
        fun onBookCancelFavoriteClick(bookInfoVo: BookInfoVo?)

    }

    override fun initData() {
    }

    override fun initView() {
    }

    override fun initListener() {
    }

    override var mActionListener: ViewActionListener? = null


    open fun getCardType():Int?{
        return null
    }

}