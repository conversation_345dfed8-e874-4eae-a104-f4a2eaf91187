package com.dz.business.theatre.refactor.component.bannerOperationCardComp

import android.content.Context
import android.util.AttributeSet
import android.util.DisplayMetrics
import androidx.lifecycle.LifecycleOwner
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.LinearSmoothScroller
import com.dz.business.base.theatre.TheatreME
import com.dz.business.theatre.databinding.TheatreCompCommunityBannerListBinding
import com.dz.business.theatre.refactor.network.bean.ActivityInfoVo
import com.dz.business.theatre.refactor.network.bean.OperActivityInfo
import com.dz.business.theatre.util.CommunityTrackUtils
import com.dz.foundation.base.utils.LogUtil
import com.dz.foundation.base.utils.RandomUtils.generateString
import com.dz.foundation.base.utils.ScreenUtil
import com.dz.foundation.base.utils.ViewUtils
import com.dz.foundation.ui.view.banner.listener.OnPageChangeListener
import com.dz.foundation.ui.view.recycler.DzRecyclerViewCell
import com.dz.platform.common.base.ui.component.UIConstraintComponent
import java.util.concurrent.ConcurrentHashMap
import kotlin.math.abs

/**
 * 社区顶部运营位轮播图
 * @constructor
 */
class OperationBannerCardComp @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int? = 0
) : UIConstraintComponent<TheatreCompCommunityBannerListBinding, OperActivityInfo>(
    context,
    attrs,
    defStyleAttr!!
) {

    private val TAG = "OperationBannerCardComp"

    //轮播卡轮播打点去重曝光记录
    val bannerAlreadyTrackPositionSet = ConcurrentHashMap<String?, MutableList<String>>()
    override fun initData() {
    }

    override fun initListener() {

        mViewBinding.banner.addOnPageChangeListener(object : OnPageChangeListener {
            override fun onPageScrolled(
                position: Int,
                positionOffset: Float,
                positionOffsetPixels: Int
            ) {
            }

            override fun onPageSelected(position: Int) {
                trackBanner(position)
                mData?.activityList?.forEachIndexed { i, it ->
                    if (i == position) {
                        it.isSelect = 1
                    } else {
                        it.isSelect = 0
                    }
                }
                mViewBinding.rvIndicators.notifyDataSetChanged()

                smoothScrollToCenter(position)
            }

            override fun onPageScrollStateChanged(state: Int) {
                resetBannerStatus()
            }
        })
        //拦截指示器点击
        mViewBinding.interView.registerClickAction {

        }
    }

    //判断轮播是否继续轮播
    private fun resetBannerStatus() {
        val now = ViewUtils.exposeAreaRatio(mViewBinding.root, tabBarHeight)
        if (now < 1.0) {
            mViewBinding.banner.stop()
        } else {
            mViewBinding.banner.start()
        }
    }

    private fun trackBanner(position: Int) {
        //曝光重复判断 剧id 栏目id 一级标题id
        val tag = mData?.activityList?.get(position)?.img ?: generateString(6)
        if (needTrack(tag, tag)) {
            LogUtil.d(TAG,"顶部运营位 trackBanner trackBanner,position=$position")
            mData?.activityList?.get(position)?.let {
                CommunityTrackUtils.exposeOperationItem(it)
            }
        }
    }

    override fun onExpose(isFirstExpose: Boolean) {
        super.onExpose(isFirstExpose)
        mData?.let {
            if ((mData?.activityList?.size ?: 0) >= mViewBinding.banner.currentItem) {
                trackBanner(mViewBinding.banner.currentItem - 1)
            }
        }
    }

    override fun initView() {
        mViewBinding.rvIndicators.itemAnimator = null
        mViewBinding.banner.post {
            mViewBinding.banner.start()
        }
    }

    private val tabBarHeight = ScreenUtil.dip2px(context, 60)

    override fun subscribeEvent(lifecycleOwner: LifecycleOwner, lifecycleTag: String) {
        TheatreME.get().rvScroll().observe(lifecycleOwner) {
            resetBannerStatus()
        }
    }


    override fun bindData(data: OperActivityInfo?) {
        super.bindData(data)
        LogUtil.d(TAG, "bindData,data=$data")
        trackBanner(0)
        if (data?.activityList?.isNotEmpty() == true) {
            val allCell = mutableListOf<DzRecyclerViewCell<*>>()
            data?.activityList?.forEachIndexed { index, item ->
                DzRecyclerViewCell<ActivityInfoVo>().apply {
                    item.isSelect = (if (index == 0) 1 else 0)
                    viewClass = CommunityBannerListItemIndicatorComp::class.java
                    viewData = item
                    allCell.add(this)
                }
            }
            val adapter = ShelfBannerAdapter(data?.activityList)
            mViewBinding.banner.setAdapter(adapter)
            mViewBinding.banner.visibility = VISIBLE
            mViewBinding.rvIndicators.allCells.clear()
            mViewBinding.rvIndicators.visibility = if (allCell.size > 1) VISIBLE else GONE
            mViewBinding.rvIndicators.addCells(allCell)
        } else {
            mViewBinding.rvIndicators.visibility = GONE
            mViewBinding.root.visibility = GONE
        }
    }

    private fun smoothScrollToCenter(position: Int) {
        val layoutManager = mViewBinding.rvIndicators.layoutManager as LinearLayoutManager
        val smoothScroller = object : LinearSmoothScroller(context) {
            override fun calculateSpeedPerPixel(displayMetrics: DisplayMetrics): Float {
                return 100f / displayMetrics.densityDpi
            }

            override fun calculateTimeForScrolling(dx: Int): Int {
                return abs(dx) * if (position == 0) 1 else 5
            }

            override fun calculateDtToFit(
                viewStart: Int, viewEnd: Int, boxStart: Int, boxEnd: Int, snapPreference: Int
            ): Int {
                return (boxStart + (boxEnd - boxStart) / 2) - (viewStart + (viewEnd - viewStart) / 2)
            }
        }
        smoothScroller.targetPosition = position
        layoutManager.startSmoothScroll(smoothScroller)
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        LogUtil.d(TAG, "onAttachedToWindow")
        mViewBinding.banner.addBannerLifecycleObserver(getContainerFragment())
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        mViewBinding.banner.removeBannerLifecycleObserver()
    }


    /**
     * 判断是否需要曝光打点
     */
    private fun needTrack(imgUrl: String?, bannerTag: String): Boolean {
        return if (bannerAlreadyTrackPositionSet[imgUrl]?.contains(bannerTag) == true) {
            false
        } else {
            if (bannerAlreadyTrackPositionSet[imgUrl] == null) {
                bannerAlreadyTrackPositionSet[imgUrl] = mutableListOf()
            }
            bannerAlreadyTrackPositionSet[imgUrl]?.add(bannerTag)
            true
        }
    }
}