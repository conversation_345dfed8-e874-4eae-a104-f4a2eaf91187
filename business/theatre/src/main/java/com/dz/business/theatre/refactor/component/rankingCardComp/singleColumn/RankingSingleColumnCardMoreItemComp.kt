package com.dz.business.theatre.refactor.component.rankingCardComp.singleColumn

import android.content.Context
import android.util.AttributeSet
import android.view.View
import androidx.recyclerview.widget.RecyclerView
import com.dz.business.base.theatre.data.RankInfoVO
import com.dz.business.theatre.databinding.TheatreCompRankingSingleColumnCardMoreItemBinding
import com.dz.foundation.base.utils.ScreenUtil
import com.dz.foundation.ui.view.recycler.DzRecyclerView
import com.dz.platform.common.base.ui.component.UIConstraintComponent
import com.dz.platform.common.router.SchemeRouter

/**
 * 查看更多的item
 */
class RankingSingleColumnCardMoreItemComp :
    UIConstraintComponent<TheatreCompRankingSingleColumnCardMoreItemBinding, String> {

    @JvmOverloads
    constructor(
        context: Context,
        attrs: AttributeSet? = null,
        defStyleAttr: Int = 0,
    ) : super(context, attrs, defStyleAttr)

    override fun onCreateRecyclerViewItem(
        parent: DzRecyclerView?,
        itemView: View?
    ): RecyclerView.LayoutParams {
        return RecyclerView.LayoutParams(
            ScreenUtil.dip2px(context, 90),
            ScreenUtil.dip2px(context, 129),
        )
    }

    override fun initData() {

    }

    override fun initView() {

    }


    override fun onExpose(isFirstExpose: Boolean) {
        super.onExpose(isFirstExpose)

    }

    override fun initListener() {
        mViewBinding.ivCover.registerClickAction {
            SchemeRouter.doUriJump(mData)
        }
    }


}