package com.dz.business.theatre.refactor.component.rankingCardComp.singleColumn

import android.content.Context
import android.util.AttributeSet
import android.view.View
import androidx.recyclerview.widget.RecyclerView
import com.dz.business.base.theatre.data.BookInfoVo
import com.dz.business.theatre.R
import com.dz.business.theatre.databinding.TheatreCompRankingSingleColumnCardBookItemBinding
import com.dz.business.theatre.refactor.manager.TheatreManager
import com.dz.foundation.base.utils.ScreenUtil
import com.dz.foundation.base.utils.dp
import com.dz.foundation.imageloader.loadRoundImg
import com.dz.foundation.ui.view.recycler.DzRecyclerView
import com.dz.platform.common.base.ui.component.UIConstraintComponent

/**
 * 单列排行榜的剧item
 */
class RankingSingleColumnCardBookItemComp :
    UIConstraintComponent<TheatreCompRankingSingleColumnCardBookItemBinding, BookInfoVo> {
    private var mContentPos = 0

    @JvmOverloads
    constructor(
        context: Context,
        attrs: AttributeSet? = null,
        defStyleAttr: Int = 0,
    ) : super(context, attrs, defStyleAttr)

    override fun initData() {

    }

    override fun initView() {

    }
    override fun onCreateRecyclerViewItem(
        parent: DzRecyclerView?,
        itemView: View?
    ): RecyclerView.LayoutParams {
        return RecyclerView.LayoutParams(
            ScreenUtil.dip2px(context, 90),
            ScreenUtil.dip2px(context, 157),
        )
    }

    override fun onExpose(isFirstExpose: Boolean) {
        super.onExpose(isFirstExpose)
        mData?.let {
            TheatreManager.exposeBook(it)
        }
    }

    override fun initListener() {
        mViewBinding.root.registerClickAction {
            val thirdTierPlay = "${mData?.channelName}-${mData?.rankName}"
            TheatreManager.onItemClick(mData,thirdTierPlay)
        }
    }

    override fun onBindRecyclerViewItem(itemData: BookInfoVo?, position: Int) {
        super.onBindRecyclerViewItem(itemData, position)
        mContentPos = position
        updateView(itemData)
    }


    private fun updateView(data: BookInfoVo?) {
        data?.let {
            mViewBinding.tvPlayCount.text = it.rankSrc
            mViewBinding.tvBookName.text = it.bookName
            mViewBinding.tvRankingNum.text = (it.listIndex + 1).toString()
            mViewBinding.ivCover.loadRoundImg(
                img = mData?.coverWap,
                radius = 8.dp,
                placeholder = R.drawable.bbase_ic_cover_default,
                error = R.drawable.bbase_ic_cover_default,
                width = 114,
                height = 163
            )
        }
    }

}