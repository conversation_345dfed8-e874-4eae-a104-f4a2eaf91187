package com.dz.business.theatre.refactor.component

import com.dz.business.DzDataRepository
import com.dz.business.base.data.bean.FollowSourceType
import com.dz.business.base.data.bean.StrategyInfo
import com.dz.business.base.data.bean.TierPlaySourceVo
import com.dz.business.base.load.DBHelper
import com.dz.business.base.network.BBaseNetWork
import com.dz.business.base.theatre.data.BookInfoVo
import com.dz.foundation.base.manager.task.TaskManager
import com.dz.foundation.network.onError
import com.dz.foundation.network.onResponse
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

object BaseCompRepository {

    /**
     * 更新短剧收藏的状态
     * @param bookInfoVo 短剧信息
     * @param viewModelScope 协程作用域
     * @param callBack 回调
     */
    fun updateBookFavoriteState(
        bookInfoVo: BookInfoVo?,
        viewModelScope: CoroutineScope,
        callBack: RepositoryCallBack
    ) {
        val bookId = bookInfoVo?.bookId ?: ""
        if (bookId.isBlank()) {
            callBack.onFail("短剧信息获取失败")
            return
        }
        viewModelScope.launch(Dispatchers.IO) {
            val bookEntity = DzDataRepository.bookDao().queryByBid(bookId)
            //TODO 需补充一级、二级、三级来源
            val tierPlaySource = TierPlaySourceVo(
                "剧场页", "剧场页-", "剧场页-"
            )
            var omap = bookInfoVo?.omap
            var mOmp = StrategyInfo()
            if (bookEntity != null && !bookEntity.first_play_source.isNullOrEmpty()) {
                if (omap != null) {
                    omap.scene = bookEntity.first_play_source
                    mOmp = omap
                } else {
                    mOmp.scene = bookEntity.first_play_source
                }
            } else {
                if (omap != null) {
                    omap.scene = "剧场"
                    mOmp = omap
                } else {
                    mOmp.scene = "剧场"
                }
            }
            BBaseNetWork.get().addFavorites().setParams(
                bookId, bookInfoVo?.chapterId, "6", mOmp, tierPlaySource, FollowSourceType.ORIGINAL
            ).onResponse {
                it.data?.run {
                    if (status == 1) {
                        TaskManager.ioTask {
                            DBHelper.insertOrUpdateHistory(bookId, true)
                        }
                        callBack.onSuccess()
                    }
                }
            }.onError {
                callBack.onFail(it.message)
            }.doRequest()
        }
    }

    /**
     * 更新短剧取消收藏的状态
     * @param bookInfoVo 短剧信息
     * @param callBack 回调
     */
    fun updateBookFavoriteCancelState(
        bookInfoVo: BookInfoVo?,
        callBack: RepositoryCallBack
    ) {
        //TODO 需补充一级、二级、三级来源
        val tileSource = TierPlaySourceVo(
            "剧场页", "剧场页-", "剧场页-"
        )
        val bookIds = mutableListOf<String>()
        bookIds.add(bookInfoVo?.bookId ?: "")
        BBaseNetWork.get().deleteFavorites().setParams(bookIds, "6", tileSource).onResponse {
            it.data?.run {
                if (status == 1) {
                    TaskManager.ioTask {
                        DBHelper.insertOrUpdateHistory(bookInfoVo?.bookId, false)
                    }
                    callBack.onSuccess()
                }
            }

        }.onError {
            callBack.onFail(it.message)
        }.doRequest()
    }
}