package com.dz.business.theatre.refactor.component.rankingCardComp.multiColumn

import android.content.Context
import android.util.AttributeSet
import com.dz.business.base.theatre.data.RankInfoVO
import com.dz.business.theatre.databinding.TheatreCompRankingMultiColumnCardBinding
import com.dz.business.theatre.refactor.component.TheatreCardComp
import com.dz.business.theatre.refactor.component.TheatreCardFactory.CARD_TYPE_RANKING_MULTI_COLUMN
import com.dz.business.theatre.refactor.network.bean.ColumnDataItem
import com.dz.foundation.ui.view.recycler.DzRecyclerViewCell

/**
 * 多排行榜卡片
 * @constructor
 */
class RankingMultiColumnCardComp @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int? = 0
) : TheatreCardComp<TheatreCompRankingMultiColumnCardBinding>(
    context, attrs, defStyleAttr!!
) {

    override fun initListener() {

    }


    override fun bindData(data: ColumnDataItem?) {
        super.bindData(data)
        val cellList = mutableListOf<DzRecyclerViewCell<RankInfoVO>>()
        data?.rankInfoVO?.forEachIndexed { index, ranInfo ->
            ranInfo.listIndex = index
            //一级
            ranInfo.groupId = data.bdChannelGroupId
            ranInfo.groupName = data.bdChannelGroupName
            ranInfo.groupPos = data.bdChannelGroupPos
            //二级
            ranInfo.channelPos = data.currentChannel?.index
            ranInfo.channelName = data.currentChannel?.channelName
            ranInfo.channelId = data.currentChannel?.channelId?.toLong()
            //栏目大卡
            ranInfo.columnId = (data.columnId ?: "").toString()
            ranInfo.columnName = data.columnTitle
            ranInfo.columnPos = data.columnPos
            //栏目类型和栏目标题
            ranInfo.styleType = data.styleType
            ranInfo.styleTypeCn = data.styleTypeCn
            ranInfo.columnTitle = data.columnTitle

            val cell = DzRecyclerViewCell<RankInfoVO>().apply {
                viewClass = RankingMultiColumnCardListItemComp::class.java
                viewData = ranInfo
            }
            cellList.add(cell)
        }
        mViewBinding.recyclerView.removeAllCells()
        mViewBinding.recyclerView.addCells(cellList)
    }


    override fun getCardType(): Int {
        return CARD_TYPE_RANKING_MULTI_COLUMN
    }
}