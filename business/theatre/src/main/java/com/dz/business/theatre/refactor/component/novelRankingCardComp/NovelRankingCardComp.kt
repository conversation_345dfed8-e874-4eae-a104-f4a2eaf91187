package com.dz.business.theatre.refactor.component.novelRankingCardComp

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import android.util.TypedValue
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.dz.business.base.theatre.data.BookInfoVo
import com.dz.business.theatre.R
import com.dz.business.theatre.databinding.TheatreNovelRankCardItemBinding
import com.dz.business.theatre.refactor.component.TheatreCardComp
import com.dz.business.theatre.refactor.manager.TheatreManager
import com.dz.business.theatre.refactor.network.bean.ColumnDataItem
import com.dz.business.track.trace.SourceNode
import com.dz.business.track.utis.ElementClickUtils
import com.dz.foundation.base.utils.LogUtil
import com.dz.foundation.base.utils.ScreenUtil
import com.dz.foundation.base.utils.dp
import com.dz.foundation.imageloader.loadRoundImg
import com.dz.foundation.ui.view.custom.ActionListener
import com.dz.foundation.ui.view.custom.ActionListenerOwner
import com.dz.foundation.ui.view.recycler.DzRecyclerView
import com.dz.platform.common.base.ui.component.UIConstraintComponent
import com.therouter.TheRouter

/**
 * 榜单卡片
 * @constructor
 */
class NovelRankingCardComp @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int? = 0
) : UIConstraintComponent<TheatreNovelRankCardItemBinding, List<BookInfoVo>>(
    context,
    attrs,
    defStyleAttr!!
) {
    override fun initData() {}
    override fun onExpose(isFirstExpose: Boolean) {
        super.onExpose(isFirstExpose)
        LogUtil.d("NovelRankingCardComp", "曝光累计位置 onExpose it")
        mData?.let { list ->
            list.forEach {
                LogUtil.d("NovelRankingCardComp", "曝光累计位置 onExpose it=${it.bookName} ")
                TheatreManager.exposeNovel(it)
            }
        }
    }

    override fun onCreateRecyclerViewItem(
        parent: DzRecyclerView?,
        itemView: View?
    ): RecyclerView.LayoutParams {
        return RecyclerView.LayoutParams(
            ScreenUtil.dip2px(context, 184),
            ScreenUtil.dip2px(context, 332),
        )
    }

    override fun initView() {
    }

    override fun initListener() {
        ElementClickUtils.ignoreAutoTrack(mViewBinding.clLayout)
        mViewBinding.clLayout1.registerClickAction {
            TheatreManager.onNovelClick(mData?.get(0))
        }

        mViewBinding.clLayout2.registerClickAction {
            TheatreManager.onNovelClick(mData?.get(1))
        }
        mViewBinding.clLayout3.registerClickAction {
            TheatreManager.onNovelClick(mData?.get(2))
        }
        mViewBinding.clLayout4.registerClickAction {
            TheatreManager.onNovelClick(mData?.get(3))
        }
    }

    @SuppressLint("SetTextI18n")
    private fun updateView(data: List<BookInfoVo>?) {
        data?.forEachIndexed { index, m ->
            when (index) {
                0 -> {
                    m.loadRound(
                        mViewBinding.tvIntroduction1,
                        mViewBinding.tvBookName1,
                        mViewBinding.ivCover1
                    )
                    m.initRank(mViewBinding.tvRank1, mViewBinding.ivRank1)
                }

                1 -> {
                    m.loadRound(
                        mViewBinding.tvIntroduction2,
                        mViewBinding.tvBookName2,
                        mViewBinding.ivCover2
                    )
                    m.initRank(mViewBinding.tvRank2, mViewBinding.ivRank2)
                }

                2 -> {
                    m.loadRound(
                        mViewBinding.tvIntroduction3,
                        mViewBinding.tvBookName3,
                        mViewBinding.ivCover3
                    )
                    m.initRank(mViewBinding.tvRank3, mViewBinding.ivRank3)
                }

                3 -> {
                    m.loadRound(
                        mViewBinding.tvIntroduction4,
                        mViewBinding.tvBookName4,
                        mViewBinding.ivCover4
                    )
                    m.initRank(mViewBinding.tvRank4, mViewBinding.ivRank4)
                }

                else -> {
                }
            }

        }
    }

    private fun BookInfoVo.loadRound(
        tvIntroduction: TextView,
        tvBookName: TextView,
        ivCover: ImageView
    ) {
        tvIntroduction.visibility = GONE
        tvBookName.text = bookName
        readNumTips?.let { readNumTips ->
            tvIntroduction.visibility = VISIBLE
            tvIntroduction.text = readNumTips
        }
        //剧封
        ivCover.loadRoundImg(
            img = coverWap,
            radius = ScreenUtil.dip2px(context, 3),
            placeholder = R.drawable.bbase_ic_cover_default,
            error = R.drawable.bbase_ic_cover_default,
            width = 52,
            height = 68
        )
    }

    @SuppressLint("SetTextI18n")
    private fun BookInfoVo.initRank(tvRank: TextView, ivRank: ImageView) {
        if (listIndex in 1..99) {
            ivRank.visibility = VISIBLE
            tvRank.visibility = VISIBLE
            when (listIndex) {
                1 -> ivRank.setImageResource(R.drawable.bbase_ic_rank1)
                2 -> ivRank.setImageResource(R.drawable.bbase_ic_rank2)
                3 -> ivRank.setImageResource(R.drawable.bbase_ic_rank3)
                else -> ivRank.setImageResource(R.drawable.bbase_ic_rank_normal)
            }
            tvRank.text = ((listIndex)).toString()
            if ((listIndex) > 9) {
                tvRank.setTextSize(
                    TypedValue.COMPLEX_UNIT_PX,
                    resources.getDimension(R.dimen.common_dp12)
                )
            } else {
                tvRank.setTextSize(
                    TypedValue.COMPLEX_UNIT_PX,
                    resources.getDimension(R.dimen.common_dp14)
                )
            }
        } else {
            ivRank.visibility = GONE
            tvRank.visibility = GONE
        }
    }

    override fun onBindRecyclerViewItem(model: List<BookInfoVo>?, position: Int) {
        super.onBindRecyclerViewItem(model, position)
        updateView(model)
    }
}