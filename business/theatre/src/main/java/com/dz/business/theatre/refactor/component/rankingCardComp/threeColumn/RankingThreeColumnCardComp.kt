package com.dz.business.theatre.refactor.component.rankingCardComp.threeColumn

import android.content.Context
import android.util.AttributeSet
import com.dz.business.base.BBaseMR
import com.dz.business.base.theatre.data.BookInfoVo
import com.dz.business.theatre.databinding.TheatreCompRankThreeCardBinding
import com.dz.business.theatre.refactor.component.TheatreCardComp
import com.dz.business.theatre.refactor.component.TheatreCardFactory.CARD_TYPE_RANKING_THREE_COLUMN
import com.dz.business.theatre.refactor.network.bean.ColumnDataItem
import com.dz.foundation.ui.view.custom.ActionListenerOwner
import com.dz.foundation.ui.view.recycler.DzRecyclerViewCell
import com.dz.platform.common.router.ACTIVITY
import com.dz.platform.common.router.SchemeRouter
import com.dz.platform.common.router.onDismiss
import com.dz.platform.common.router.onShow

/**
 * 排行榜三卡组件
 * @constructor
 */
class RankingThreeColumnCardComp @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int? = 0
) : TheatreCardComp<TheatreCompRankThreeCardBinding>(
    context, attrs, defStyleAttr!!
), ActionListenerOwner<TheatreCardComp.ViewActionListener> {

    override fun initListener() {
        super.initListener()
        mViewBinding.ivRules.setOnClickListener { rulesDialog() }
        mViewBinding.tvRules.setOnClickListener { rulesDialog() }
        mViewBinding.tvMoreAction.setOnClickListener { routeToRank() }
        mViewBinding.ivMore.setOnClickListener { routeToRank() }
    }

    /**
     * 更新标题
     */
    private fun updateTitle(data: ColumnDataItem?) {
        if (data?.columnTitle?.isNotBlank() == true && data.needConcat != true) {
            mViewBinding.tvTitle.visibility = VISIBLE
            mViewBinding.tvTitle.text = data.columnTitle
            if (data.moreTips?.isNotBlank() == true) {
                mViewBinding.tvMoreAction.visibility = VISIBLE
                mViewBinding.ivMore.visibility = VISIBLE
                mViewBinding.tvMoreAction.text = "更多榜单"
            } else {
                mViewBinding.tvMoreAction.visibility = GONE
                mViewBinding.ivMore.visibility = GONE
            }
            if (data.moreTips?.isNotBlank() == true) {
                mViewBinding.tvRules.visibility = VISIBLE
                mViewBinding.ivRules.visibility = VISIBLE
                mViewBinding.tvRules.text = data.moreTips
            } else {
                mViewBinding.tvRules.visibility = GONE
                mViewBinding.ivRules.visibility = GONE
            }
        } else {
            mViewBinding.tvTitle.visibility = GONE
            mViewBinding.tvRules.visibility = GONE
            mViewBinding.tvMoreAction.visibility = GONE
            mViewBinding.ivRules.visibility = GONE
            mViewBinding.ivMore.visibility = GONE
        }
    }

    private fun rulesDialog() {
        //规则弹窗
        BBaseMR.get().flutterDialog().apply {
            title = mData?.moreTips
            content = mData?.moreActionContent
            showClose = true
            barrierDismissible = true
            mode = ACTIVITY
        }.onShow {}.onSure { url, _ ->
            SchemeRouter.doUriJump(url ?: "")
        }.onDismiss {}.start()
    }

    private fun routeToRank() {
        SchemeRouter.doUriJump(mData?.rankAction)
    }

    override fun bindData(data: ColumnDataItem?) {
        super.bindData(data)
        updateTitle(data)
        val cellList = mutableListOf<DzRecyclerViewCell<BookInfoVo>>()
        data?.videoData?.forEachIndexed { index, item ->
            item.listIndex = index
            item.groupId = data.bdChannelGroupId
            item.groupName = data.bdChannelGroupName
            item.groupPos = data.bdChannelGroupPos
            item.channelPos = data.currentChannel?.index
            item.channelName = data.currentChannel?.channelName
            item.channelId = data.currentChannel?.channelId?.toLong()
            item.columnId = (data.columnId ?: "").toString()
            item.columnName = data.columnTitle
            item.columnPos = data.columnPos
            item.styleTypeCn = data.styleTypeCn
            item.columnTitle = data.columnTitle
            val cell = DzRecyclerViewCell<BookInfoVo>().apply {
                viewClass = RankingThreeColumnCardItemComp::class.java
                viewData = item
//                spanSize = 1
            }
            cellList.add(cell)
        }
        mViewBinding.recyclerView.removeAllCells()
        mViewBinding.recyclerView.addCells(cellList)
    }

    override fun getCardType(): Int {
        return CARD_TYPE_RANKING_THREE_COLUMN
    }
}