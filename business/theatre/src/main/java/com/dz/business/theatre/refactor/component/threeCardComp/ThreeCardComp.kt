package com.dz.business.theatre.refactor.component.threeCardComp

import android.content.Context
import android.util.AttributeSet
import com.dz.business.base.theatre.data.BookInfoVo
import com.dz.business.base.ui.component.EmptyComp
import com.dz.business.theatre.databinding.TheatreCompThreeCardBinding
import com.dz.business.theatre.refactor.component.TheatreCardComp
import com.dz.business.theatre.refactor.component.TheatreCardFactory.CARD_TYPE_THREE
import com.dz.business.theatre.refactor.network.bean.ColumnDataItem
import com.dz.foundation.ui.view.custom.ActionListenerOwner
import com.dz.foundation.ui.view.recycler.DzRecyclerViewCell
import com.dz.foundation.ui.view.recycler.DzRecyclerViewItem

/**
 * 三卡组件
 * @constructor
 */
class ThreeCardComp @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int? = 0
) : TheatreCardComp<TheatreCompThreeCardBinding>(
    context, attrs, defStyleAttr!!
), ActionListenerOwner<TheatreCardComp.ViewActionListener> {
    //单部剧
    private val CARD_TYPE_BOOK = 0

    //播单
    private val CARD_TYPE_DRAMA_LIST = 1

    //排行榜
    private val CARD_TYPE_RANKING = 2

    /**
     * 获取组件的UI实例
     */
    private fun getItemCardViewClass(styleType: Int?): Class<out DzRecyclerViewItem<Any>>? {
        return when (styleType) {
            CARD_TYPE_BOOK -> ThreeCardBookItemComp::class.java as Class<out DzRecyclerViewItem<Any>>
            CARD_TYPE_DRAMA_LIST -> ThreeCardDramaListItemComp::class.java as Class<out DzRecyclerViewItem<Any>>
            CARD_TYPE_RANKING -> ThreeCardRankingItemComp::class.java as Class<out DzRecyclerViewItem<Any>>
            else -> EmptyComp::class.java
        }
    }


    /**
     * 更新标题
     */
    private fun updateTitle(data: ColumnDataItem?) {
        if (data?.columnTitle?.isNotBlank() == true && data.needConcat != true) {
            mViewBinding.tvTitle.visibility = VISIBLE
            mViewBinding.tvTitle.text = data.columnTitle
            if (data.moreTips?.isNotBlank() == true) {
                mViewBinding.tvMoreAction.visibility = VISIBLE
                mViewBinding.tvMoreAction.text = data.moreTips
                mViewBinding.tvMoreAction.setOnClickListener {
                    mActionListener?.moreActionContentClick(mData)
                }
            } else {
                mViewBinding.tvMoreAction.visibility = GONE
            }
        } else {
            mViewBinding.tvTitle.visibility = GONE
        }
    }

    override fun bindData(data: ColumnDataItem?) {
        super.bindData(data)
        updateTitle(data)
        val cellList = mutableListOf<DzRecyclerViewCell<BookInfoVo>>()
        data?.videoData?.forEachIndexed { index, item ->
            item.listIndex = index
            item.groupId = data.bdChannelGroupId
            item.groupName = data.bdChannelGroupName
            item.groupPos = data.bdChannelGroupPos
            item.channelPos = data.currentChannel?.index
            item.channelName = data.currentChannel?.channelName
            item.channelId = data.currentChannel?.channelId?.toLong()
            item.columnId = (data.columnId ?: "").toString()
            item.columnName = data.columnTitle
            item.columnPos = data.columnPos
            item.styleTypeCn = data.styleTypeCn
            item.columnTitle = data.columnTitle
            val cell = DzRecyclerViewCell<BookInfoVo>().apply {
                viewClass = getItemCardViewClass(item.bookType)
                viewData = item
//                spanSize = 1
            }
            cellList.add(cell)
        }
        mViewBinding.recyclerView.removeAllCells()
        mViewBinding.recyclerView.addCells(cellList)
    }

    override fun getCardType(): Int {
        return CARD_TYPE_THREE
    }
}