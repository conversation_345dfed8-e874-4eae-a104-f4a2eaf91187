package com.dz.business.theatre.refactor.component.bannerOperationCardComp

import android.content.Context
import android.util.AttributeSet
import android.view.View
import androidx.recyclerview.widget.RecyclerView
import com.dz.business.theatre.R
import com.dz.business.theatre.databinding.TheatreCompCommunityBannerIndicatorBinding
import com.dz.business.theatre.refactor.network.bean.ActivityInfoVo
import com.dz.foundation.base.utils.ScreenUtil
import com.dz.foundation.base.utils.dp
import com.dz.foundation.ui.view.recycler.DzRecyclerView
import com.dz.platform.common.base.ui.component.UIConstraintComponent

/**
 * 指示器红点
 * @constructor
 */

class CommunityBannerListItemIndicatorComp @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int? = 0,
) : UIConstraintComponent<TheatreCompCommunityBannerIndicatorBinding, ActivityInfoVo>(
    context,
    attrs,
    defStyleAttr!!
) {
    override fun initData() {}

    override fun initView() {

    }

    override fun onCreateRecyclerViewItem(
        parent: DzRecyclerView?,
        itemView: View?
    ): RecyclerView.LayoutParams {
        return RecyclerView.LayoutParams(
            ScreenUtil.dip2px(context, 12),
            ScreenUtil.dip2px(context, 15),
        )
    }

    override fun initListener() {

    }

    override fun bindData(data: ActivityInfoVo?) {
        super.bindData(data)
        if (data?.isSelect == 1) {
            mViewBinding.view.setShapeBackground(
                radius = 2.5f.dp,
                solidColor = getColor(R.color.common_FF7F7F7F)
            )
        } else {
            mViewBinding.view.setShapeBackground(
                radius = 2.5f.dp,
                solidColor = getColor(R.color.common_FFD8D8D8)
            )
        }
    }
}