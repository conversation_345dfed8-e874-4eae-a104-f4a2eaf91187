package com.dz.business.theatre.refactor.component.audioBookComp.double

import android.content.Context
import android.util.AttributeSet
import com.dz.business.base.theatre.data.BookInfoVo
import com.dz.business.theatre.databinding.TheatreAudioDoubleCompBinding
import com.dz.business.theatre.refactor.component.TheatreCardComp
import com.dz.business.theatre.refactor.component.TheatreCardFactory
import com.dz.business.theatre.refactor.manager.TheatreManager
import com.dz.business.theatre.refactor.network.bean.ColumnDataItem
import com.dz.business.theatre.refactor.page.subTab.ScrollTracker
import com.dz.foundation.ui.view.recycler.DzRecyclerViewCell
import com.dz.platform.common.router.SchemeRouter

/**
 *@Description:有声书双卡
 *@Version:2.8.0
 */
class AudioDoubleListCardComp @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int? = 0
) : TheatreCardComp<TheatreAudioDoubleCompBinding>(
    context,
    attrs,
    defStyleAttr!!
) {
    override var mActionListener: ViewActionListener? = null
    private var scrollTracker: ScrollTracker? = null
    override fun initListener() {
        mViewBinding.tvMoreAction.setOnClickListener {
            SchemeRouter.doUriJump(
                mData?.moreActionContent
//                    ?: "hmjc://app.client?action=my_appointment&param={\"fromSource\":\"剧场\", \"selectedTab\":\"appoint\"}"
            )
        }
        mViewBinding.ivMore.setOnClickListener {
            SchemeRouter.doUriJump(
                mData?.moreActionContent
//                    ?: "hmjc://app.client?action=my_appointment&param={\"fromSource\":\"剧场\", \"selectedTab\":\"appoint\"}"
            )
        }
        scrollTracker =
            ScrollTracker(mViewBinding.recyclerView, object : ScrollTracker.ExposeCallback {
                override fun onExposed(list: MutableSet<Any>) {
                    TheatreManager.onExpose(list)
                }
            })
    }

    override fun bindData(data: ColumnDataItem?) {
        super.bindData(data)
        mData = data
        data?.run {
            // 标题
            updateTitle(this)
            // 数据
            val allCell = mutableListOf<DzRecyclerViewCell<*>>()
            videoData?.forEachIndexed { index, item ->
                item.showSort = this.showSort
                item.mPosition = index + 1
                item.listIndex = index
                item.groupId = this.bdChannelGroupId
                item.groupName = this.bdChannelGroupName
                item.groupPos = this.bdChannelGroupPos
                item.channelPos = this.currentChannel?.index
                item.channelName = this.currentChannel?.channelName
                item.channelId = this.currentChannel?.channelId?.toLong()
                item.columnId = (this.columnId ?: "").toString()
                item.columnName = this.columnTitle
                item.columnPos = this.columnPos
                item.styleTypeCn = this.styleTypeCn
                item.columnTitle = this.columnTitle
                DzRecyclerViewCell<BookInfoVo>().apply {
                    viewClass = AudioDoubleListItemComp::class.java
                    viewData = item
                    allCell.add(this)
                }
            }
            if (allCell.isEmpty()) {
                mViewBinding.recyclerView.visibility = GONE
                mViewBinding.layoutRoot.visibility = GONE
            } else {
                mViewBinding.recyclerView.visibility = VISIBLE
                mViewBinding.layoutRoot.visibility = VISIBLE
                mViewBinding.recyclerView.removeAllCells()
                mViewBinding.recyclerView.addCells(allCell)
                mViewBinding.recyclerView.scrollToPosition(0)
            }
        }
    }

    override fun onBindRecyclerViewItem(model: ColumnDataItem?, position: Int) {
        super.onBindRecyclerViewItem(model, position)

    }

    private fun updateTitle(data: ColumnDataItem?) {
        if (data?.columnTitle?.isNotBlank() == true && data.needConcat != true) {
            mViewBinding.tvTitle.visibility = VISIBLE
            mViewBinding.tvTitle.text = data.columnTitle
            if (data.moreTips?.isNotBlank() == true) {
                mViewBinding.tvMoreAction.visibility = VISIBLE
                mViewBinding.tvMoreAction.text = data.moreTips
                mViewBinding.tvMoreAction.setOnClickListener {
                    mActionListener?.moreActionContentClick(mData)
                }
            } else {
                mViewBinding.tvMoreAction.visibility = GONE
            }
        } else {
            mViewBinding.tvTitle.visibility = GONE
        }
    }

    override fun getCardType(): Int {
        return TheatreCardFactory.CARD_TYPE_AUDIO_DOUBLE
    }
}