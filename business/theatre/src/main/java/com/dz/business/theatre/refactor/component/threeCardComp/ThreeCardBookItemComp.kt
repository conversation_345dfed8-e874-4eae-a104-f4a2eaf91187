package com.dz.business.theatre.refactor.component.threeCardComp

import android.content.Context
import android.util.AttributeSet
import android.view.View
import androidx.recyclerview.widget.RecyclerView
import com.dz.business.base.data.bean.BaseBookInfo
import com.dz.business.base.theatre.data.BookInfoVo
import com.dz.business.theatre.R
import com.dz.business.theatre.databinding.TheatreCompThreeCardBookItemBinding
import com.dz.business.theatre.refactor.manager.TheatreManager
import com.dz.business.track.utis.ElementClickUtils
import com.dz.foundation.base.utils.ScreenUtil
import com.dz.foundation.base.utils.dp
import com.dz.foundation.imageloader.load
import com.dz.foundation.imageloader.loadRoundImg
import com.dz.foundation.ui.view.recycler.DzRecyclerView
import com.dz.platform.common.base.ui.component.UIConstraintComponent

/**
 * 三卡样式-单剧卡片
 */
class ThreeCardBookItemComp :
    UIConstraintComponent<TheatreCompThreeCardBookItemBinding, BookInfoVo> {
    private var mContentPos = 0

    @JvmOverloads
    constructor(
        context: Context,
        attrs: AttributeSet? = null,
        defStyleAttr: Int = 0,
    ) : super(context, attrs, defStyleAttr)

    override fun initData() {

    }

    override fun initView() {

    }

    override fun onExpose(isFirstExpose: Boolean) {
        super.onExpose(isFirstExpose)
        mData?.let {
            TheatreManager.exposeBook(it)
        }
    }

    override fun initListener() {
        ElementClickUtils.ignoreAutoTrack(mViewBinding.tvOcpc)
        mViewBinding.root.registerClickAction { TheatreManager.onItemClick(mData) }
    }

    override fun onCreateRecyclerViewItem(
        parent: DzRecyclerView?,
        itemView: View?
    ): RecyclerView.LayoutParams {
        val width = (ScreenUtil.getScreenWidth() - ScreenUtil.dip2px(context, 33)) / 3
        return RecyclerView.LayoutParams(
            width,
            ScreenUtil.dip2px(context, 211),
        )
    }

    override fun onBindRecyclerViewItem(itemData: BookInfoVo?, position: Int) {
        super.onBindRecyclerViewItem(itemData, position)
//        val layoutParams = mViewBinding.root.layoutParams
//        layoutParams.width = (ScreenUtil.getScreenWidth() ) / 3
//        mViewBinding.root.layoutParams = layoutParams
        mContentPos = position
        updateView(itemData)
    }

    private fun getSize(dp: Int): Int {
        return ScreenUtil.dip2px(context, dp)
    }


    /**
     * 更新右上角类型的样式
     */
    private fun updateTypeStyle(data: BookInfoVo) {
        mViewBinding.label.bindData(data)
    }


    /**
     * 底部排行或标签样式
     */
    private fun updateTagsAndRankingStyle(data: BookInfoVo?) {
        data?.let { itemData ->
            listOf(mViewBinding.tvTags, mViewBinding.tvRank, mViewBinding.tvOcpc).onEach {
                it.visibility = GONE
            }
            if (!itemData.guideTip.isNullOrEmpty()) {
                mViewBinding.tvOcpc.text = itemData.guideTip
                mViewBinding.tvOcpc.visibility = VISIBLE
            } else if (!itemData.rankActionTips.isNullOrEmpty()) {
                mViewBinding.tvTags.visibility = GONE
                mViewBinding.tvRank.visibility = VISIBLE
                mViewBinding.tvRank.text = itemData.rankActionTips
                mViewBinding.tvRank.setCompoundDrawablesWithIntrinsicBounds(
                    0,
                    0,
                    R.drawable.theatre_ic_arrow_right,
                    0
                )
                mViewBinding.tvRank.registerClickAction {
                    itemData.rankAction?.let {
                        TheatreManager.onRankUrlClick(mData)
                    }
                }
                itemData.showInfoType = BaseBookInfo.SHOW_INFO_RANK
            } else if (!itemData.recReason.isNullOrEmpty()) {
                mViewBinding.tvTags.visibility = GONE
                mViewBinding.tvRank.visibility = VISIBLE
                mViewBinding.tvRank.text = itemData.recReason
                mViewBinding.tvRank.setCompoundDrawablesWithIntrinsicBounds(0, 0, 0, 0)
                itemData.showInfoType = BaseBookInfo.SHOW_INFO_RECOMMEND_REASON
            } else if (!itemData.bookTags.isNullOrEmpty()) {
                mViewBinding.tvRank.visibility = GONE
                mViewBinding.tvTags.visibility = VISIBLE
                mViewBinding.tvTags.text = itemData.bookTags!!.take(3).joinToString(separator = " ")
            } else {
                mViewBinding.tvRank.visibility = GONE
                mViewBinding.tvTags.visibility = GONE
            }
        }

    }

    private fun updateView(data: BookInfoVo?) {
        data?.let {
            updateTypeStyle(it)
            updateTagsAndRankingStyle(it)
            mViewBinding.tvPlayCount.text = it.coverBottomTips
            mViewBinding.tvBookName.text = it.bookName
            mViewBinding.ivCover.loadRoundImg(
                img = mData?.coverWap,
                radius = 8.dp,
                placeholder = R.drawable.bbase_ic_cover_default,
                error = R.drawable.bbase_ic_cover_default,
                width = 114,
                height = 163
            )
        }
    }

}