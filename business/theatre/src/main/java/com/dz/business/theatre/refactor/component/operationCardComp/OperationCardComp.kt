package com.dz.business.theatre.refactor.component.operationCardComp

import android.content.Context
import android.net.Uri
import android.util.AttributeSet
import com.dz.business.theatre.R
import com.dz.business.theatre.databinding.TheatreCompOperationCardBinding
import com.dz.business.theatre.refactor.component.TheatreCardComp
import com.dz.business.theatre.refactor.component.TheatreCardFactory.CARD_TYPE_OPERATION
import com.dz.business.theatre.refactor.manager.TheatreManager
import com.dz.business.theatre.refactor.network.bean.ColumnDataItem
import com.dz.business.theatre.refactor.network.bean.ImageInfoVo
import com.dz.business.track.events.DzTrackEvents
import com.dz.business.track.utis.ElementClickUtils
import com.dz.foundation.base.utils.ScreenUtil
import com.dz.foundation.imageloader.loadRoundImg
import com.dz.platform.common.router.SchemeRouter

/**
 * 运营位卡片
 * @constructor
 */
class OperationCardComp @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int? = 0
) : TheatreCardComp<TheatreCompOperationCardBinding>(
    context, attrs, defStyleAttr!!
) {

    private var imageData: ImageInfoVo? = null

    override fun initListener() {
        ElementClickUtils.ignoreAutoTrack(mViewBinding.ivCover)
        mViewBinding.ivCover.registerClickAction {
            imageData?.jumpUrl?.let {
                DzTrackEvents.get().operationClickTE()
                    .positionName("剧场")
                    .operationName("剧场图片栏目")
                    .operationPosition("剧场图片栏目")
                    .operationType(getOperationType(it))
                    .track()
                SchemeRouter.doUriJump(it)
            }
        }
    }

    override fun onExpose(isFirstExpose: Boolean) {
        super.onExpose(isFirstExpose)
        mData?.imageData?.firstOrNull()?.let {
            TheatreManager.exposeImage(it)
        }
    }

    private fun getOperationType(jumpUrl: String?): String {
        if (!jumpUrl.isNullOrEmpty()) {
            val uri = Uri.parse(jumpUrl)
            val queryParameters = uri.queryParameterNames.associateWith { uri.getQueryParameter(it) }
            return when (queryParameters["action"] ?: "") {
                "flutter/HistoryHomePage" -> "跳转历史记录"
                "web" -> "跳转福利中心"
                "recharge" -> "跳转充值页"
                else -> ""
            }
        }
        return ""
    }

    override fun bindData(data: ColumnDataItem?) {
        super.bindData(data)
        imageData = mData?.imageData?.firstOrNull()
        mViewBinding.ivCover.loadRoundImg(
            imageData?.img,
            radius = ScreenUtil.dip2px(context, 8),
            placeholder = R.drawable.theater_bg_card,
            error = R.drawable.theater_bg_card,
        )
        updateTitle(data)
    }

    /**
     * 更新标题
     */
    private fun updateTitle(data: ColumnDataItem?) {
        mViewBinding.ivMore.visibility = GONE
        mViewBinding.tvMoreAction.visibility = GONE
        if (data?.columnTitle?.isBlank() == true) {
            mViewBinding.tvTitle.visibility = GONE
        } else {
            mViewBinding.tvTitle.visibility = VISIBLE
            mViewBinding.tvTitle.text = data?.columnTitle
        }

        data?.moreTips?.let {
            mViewBinding.tvMoreAction.visibility = VISIBLE
            mViewBinding.ivMore.visibility = VISIBLE
            mViewBinding.tvMoreAction.text = it
            mViewBinding.tvMoreAction.setOnClickListener {
                mActionListener?.moreActionContentClick(mData)
            }
        }
    }

    override fun getCardType(): Int {
        return CARD_TYPE_OPERATION
    }
}