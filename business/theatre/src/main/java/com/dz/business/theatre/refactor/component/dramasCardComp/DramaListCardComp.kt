package com.dz.business.theatre.refactor.component.dramasCardComp

import android.content.Context
import android.util.AttributeSet
import com.dz.business.base.theatre.data.BookInfoVo
import com.dz.business.theatre.databinding.TheatreCompDramaListCardBinding
import com.dz.business.theatre.refactor.component.TheatreCardComp
import com.dz.business.theatre.refactor.component.TheatreCardFactory.CARD_TYPE_DRAMAS
import com.dz.business.theatre.refactor.manager.TheatreManager
import com.dz.business.theatre.refactor.network.bean.ColumnDataItem
import com.dz.business.theatre.refactor.page.subTab.ScrollTracker
import com.dz.foundation.base.utils.dp
import com.dz.foundation.ui.view.recycler.DzRecyclerViewCell

/**
 * 剧单列表大卡
 * @constructor
 */
class DramaListCardComp @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int? = 0
) : TheatreCardComp<TheatreCompDramaListCardBinding>(
    context,
    attrs,
    defStyleAttr!!
) {
    override var mActionListener: ViewActionListener? = null
    private var scrollTracker: ScrollTracker? = null

    override fun initListener() {
        //继续观看
        mViewBinding.tvMore.registerClickAction {
            mActionListener?.moreActionContentClick(mData)
        }
        scrollTracker = ScrollTracker(mViewBinding.rv, object : ScrollTracker.ExposeCallback {
            override fun onExposed(list: MutableSet<Any>) {
                TheatreManager.onExpose(list)
            }
        })
    }

    override fun onBindRecyclerViewItem(model: ColumnDataItem?, position: Int) {
        super.onBindRecyclerViewItem(model, position)
        model?.run {
            mViewBinding.tvTitle.text = columnTitle
            mViewBinding.tvMore.text = moreTips
            //Recycle
            val allCell = mutableListOf<DzRecyclerViewCell<*>>()
            if (!videoData.isNullOrEmpty()) {
                DzRecyclerViewCell<Int>().apply {
                    viewClass = DramaEmptyWidthComp::class.java
                    viewData = 10.dp
                    allCell.add(this)
                }
            }
            videoData?.forEachIndexed { index, item ->
                item.showSort = model.showSort
                item.mPosition = index + 1
                item.listIndex = index
                item.groupId = model.bdChannelGroupId
                item.groupName = model.bdChannelGroupName
                item.groupPos = model.bdChannelGroupPos
                item.channelPos = model.currentChannel?.index
                item.channelName = model.currentChannel?.channelName
                item.channelId = model.currentChannel?.channelId?.toLong()
                item.columnId = (model.columnId ?: "").toString()
                item.columnName = model.columnTitle
                item.columnPos = model.columnPos
                item.styleTypeCn = model.styleTypeCn
                item.columnTitle = model.columnTitle
                DzRecyclerViewCell<BookInfoVo>().apply {
                    viewClass = DramaCardComp::class.java
                    viewData = item
                    allCell.add(this)
                }
            }
            if (!videoData.isNullOrEmpty()) {
                DzRecyclerViewCell<Int>().apply {
                    viewClass = DramaEmptyWidthComp::class.java
                    viewData = 10.dp
                    allCell.add(this)
                }
            }
            if (allCell.isEmpty()) {
                mViewBinding.rv.visibility = GONE
                mViewBinding.title.visibility = GONE
                mViewBinding.clLayout.visibility = GONE
            } else {
                mViewBinding.rv.visibility = VISIBLE
                mViewBinding.title.visibility = VISIBLE
                mViewBinding.clLayout.visibility = VISIBLE
                mViewBinding.rv.removeAllCells()
                mViewBinding.rv.addCells(allCell)
                mViewBinding.rv.scrollToPosition(0)
            }
        }
    }

    override fun getCardType(): Int {
        return CARD_TYPE_DRAMAS
    }
}