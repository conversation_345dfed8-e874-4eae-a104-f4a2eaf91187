package com.dz.business.theatre.refactor.component

import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.dz.business.base.flutter.FlutterMS
import com.dz.business.base.home.HomeME
import com.dz.business.base.theatre.data.BookInfoVo
import com.dz.business.base.vm.ComponentVM
import com.dz.platform.common.toast.ToastManager

/**
 * 组件基类VM，放置通用功能
 */
open class BaseCompVM : ComponentVM(), DefaultLifecycleObserver, BaseCompContract {
    override val bookFavoriteState: MutableLiveData<Boolean> = MutableLiveData<Boolean>()


    override fun onBookFavoriteClick(bookInfoVo: BookInfoVo?) {
        BaseCompRepository.updateBookFavoriteState(bookInfoVo,
            viewModelScope,
            object : RepositoryCallBack {
                override fun onSuccess() {
                    ToastManager.showToast("已追剧，可在【首页-在追】中查看")
                    bookFavoriteState.value = true
                    //同步收藏状态
                    val bookId = bookInfoVo?.bookId ?: ""
                    HomeME.get().addFavoriteSuccess().post(bookId)
                    FlutterMS.get()?.sendEventToFlutter(
                        "inBookShelf", mapOf("value" to true, "bookId" to bookInfoVo?.bookId)
                    )
                }

                override fun onFail(err: String) {
                    ToastManager.showToast(err)
                }

            })
    }

    override fun onBookCancelFavoriteClick(bookInfoVo: BookInfoVo?) {
        BaseCompRepository.updateBookFavoriteCancelState(bookInfoVo, object : RepositoryCallBack {
            override fun onSuccess() {
                bookFavoriteState.value = false

                val bookId = bookInfoVo?.bookId ?: ""
                HomeME.get().deleteFavoriteSuccess().post(listOf(bookId))
                FlutterMS.get()?.sendEventToFlutter(
                    "inBookShelf", mapOf("value" to false, "bookId" to bookInfoVo?.bookId)
                )
            }

            override fun onFail(err: String) {
                ToastManager.showToast(err)
            }

        })
    }

    override fun onBookFavoriteStateChanged(isFavorite: Boolean) {
        bookFavoriteState.value = isFavorite
    }
}