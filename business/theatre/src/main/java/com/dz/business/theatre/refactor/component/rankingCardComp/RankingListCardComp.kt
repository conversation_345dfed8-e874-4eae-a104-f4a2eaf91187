package com.dz.business.theatre.refactor.component.rankingCardComp

import android.content.Context
import android.util.AttributeSet
import com.dz.business.base.theatre.data.BookInfoVo
import com.dz.business.theatre.databinding.TheatreCompRankListCardBinding
import com.dz.business.theatre.refactor.component.TheatreCardComp
import com.dz.business.theatre.refactor.component.TheatreCardFactory.CARD_TYPE_RANKING
import com.dz.business.theatre.refactor.network.bean.ColumnDataItem
import com.dz.foundation.ui.view.custom.ActionListenerOwner
import com.dz.foundation.ui.view.recycler.DzRecyclerViewCell

/**
 * 榜单卡片列表
 * @constructor
 */
class RankingListCardComp @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int? = 0
) : TheatreCardComp<TheatreCompRankListCardBinding>(
    context,
    attrs,
    defStyleAttr!!
), ActionListenerOwner<TheatreCardComp.ViewActionListener> {
    override var mActionListener: ViewActionListener? = null
    override fun initListener() {
        //更多
        mViewBinding.tvMore.registerClickAction {
            mActionListener?.moreActionContentClick(mData)
        }
        mViewBinding.ivMore.registerClickAction {
            mActionListener?.moreActionContentClick(mData)
        }
    }

    override fun onBindRecyclerViewItem(model: ColumnDataItem?, position: Int) {
        super.onBindRecyclerViewItem(model, position)
        model?.run {
            if (columnTitle.isNullOrEmpty() || needConcat == true) {
                mViewBinding.title.visibility = GONE
            } else {
                mViewBinding.title.visibility = VISIBLE
                mViewBinding.tvTitle.text = columnTitle
                mViewBinding.tvMore.text = moreTips
            }
            //RecycleView
            val allCell = mutableListOf<DzRecyclerViewCell<*>>()
            videoData?.forEachIndexed { index, item ->
                item.listIndex = index
                item.groupId = model.bdChannelGroupId
                item.groupName = model.bdChannelGroupName
                item.groupPos = model.bdChannelGroupPos
                item.channelPos = model.currentChannel?.index
                item.channelName = model.currentChannel?.channelName
                item.channelId = model.currentChannel?.channelId?.toLong()
                item.columnId = (model.columnId?:"").toString()
                item.columnName = model.columnTitle
                item.columnPos = model.columnPos
                item.styleTypeCn = model.styleTypeCn
                item.columnTitle = model.columnTitle
                DzRecyclerViewCell<BookInfoVo>().apply {
                    viewClass = RankingCardComp::class.java
                    viewData = item
                    allCell.add(this)
                }
            }
            if (allCell.isEmpty()) {
                mViewBinding.rv.visibility = GONE
            } else {
                mViewBinding.rv.removeAllCells()
                mViewBinding.rv.addCells(allCell)
            }
        }
    }

    override fun getCardType(): Int {
        return CARD_TYPE_RANKING
    }
}