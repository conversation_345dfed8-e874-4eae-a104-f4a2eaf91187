package com.dz.business.theatre.refactor.component.novelSigleCardComp.bookItem

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import com.dz.business.base.theatre.data.BookInfoVo
import com.dz.business.base.vm.getViewModel
import com.dz.business.theatre.R
import com.dz.business.theatre.databinding.TheatreCompSingleCardNovelItemBinding
import com.dz.business.theatre.refactor.manager.TheatreManager
import com.dz.business.track.utis.ElementClickUtils
import com.dz.foundation.base.utils.ScreenUtil
import com.dz.foundation.imageloader.loadRoundImg
import com.dz.platform.common.base.ui.component.UIConstraintComponent
import com.therouter.TheRouter

/**
 * 单卡
 * @constructor
 */
class NovelSingleCardBookItemComp :
    UIConstraintComponent<TheatreCompSingleCardNovelItemBinding, BookInfoVo> {
    private var viewModel: NovelSingleCardBookItemVM? = null
    private var contract: NovelSingleCardBookItemContract? = null

    @JvmOverloads
    constructor(
        context: Context,
        attrs: AttributeSet? = null,
        defStyleAttr: Int = 0,
    ) : super(context, attrs, defStyleAttr)

    override fun initData() {
        viewModel = getViewModel(NovelSingleCardBookItemVM::class.java)
        contract = viewModel
    }

    override fun onExpose(isFirstExpose: Boolean) {
        super.onExpose(isFirstExpose)
        mData?.let {
            TheatreManager.exposeNovel(it)
        }
    }

    override fun initView() {
    }

    override fun initListener() {
        ElementClickUtils.ignoreAutoTrack(mViewBinding.clLayout)
        //继续观看按钮点击事件
        mViewBinding.clLayout.registerClickAction {
            TheatreManager.onNovelClick(mData)
        }
    }

    @SuppressLint("SetTextI18n")
    private fun updateView(data: BookInfoVo?) {
        data?.let { m ->
            mViewBinding.tvTags.visibility = GONE
            mViewBinding.tvIntroduction.visibility = GONE
            mViewBinding.tvBookScore.visibility = GONE
            mViewBinding.tvBookName.text = m.bookName
            //分数
            m.bookScore?.let { bookScore ->
                mViewBinding.tvBookScore.visibility = VISIBLE
                mViewBinding.tvBookScore.text = "${bookScore}分"
            }
            //标签
            var str: String = ""
            str += m.getTwoTags()
            if (!m.statusTips.isNullOrBlank()) {
                if (str.isNotBlank()) {
                    str += '·';
                }
                str += m.statusTips;
            }
            if (!m.totalWordSize.isNullOrBlank()) {
                if (str.isNotBlank()) {
                    str += '·';
                }
                str += m.totalWordSize
            }
            mViewBinding.tvTags.visibility = VISIBLE
            mViewBinding.tvTags.text = str
            //简介
            m.introduction?.let { introduction ->
                mViewBinding.tvIntroduction.visibility = VISIBLE
                mViewBinding.tvIntroduction.text = introduction
            }
            //剧封
            mViewBinding.ivCover.loadRoundImg(
                img = m.coverWap,
                radius = ScreenUtil.dip2px(context, 4),
                placeholder = R.drawable.bbase_ic_cover_default,
                error = R.drawable.bbase_ic_cover_default,
                width = 78,
                height = 102
            )
        }
    }

    override fun onBindRecyclerViewItem(model: BookInfoVo?, position: Int) {
        super.onBindRecyclerViewItem(model, position)
        updateView(model)
    }
}