package com.dz.business.theatre.refactor.component.bannerCardComp

import android.content.Context
import android.util.AttributeSet
import android.util.DisplayMetrics
import android.view.ViewGroup
import androidx.lifecycle.LifecycleOwner
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.LinearSmoothScroller
import com.dz.business.base.theatre.TheatreME
import com.dz.business.base.theatre.data.BookInfoVo
import com.dz.business.theatre.databinding.TheatreCompBannerListBinding
import com.dz.business.theatre.refactor.component.TheatreCardComp
import com.dz.business.theatre.refactor.component.TheatreCardFactory.CARD_TYPE_BANNER
import com.dz.business.theatre.refactor.manager.TheatreManager
import com.dz.business.theatre.refactor.network.bean.ColumnDataItem
import com.dz.foundation.base.utils.LogUtil
import com.dz.foundation.base.utils.ScreenUtil
import com.dz.foundation.base.utils.ViewUtils
import com.dz.foundation.ui.view.banner.listener.OnPageChangeListener
import com.dz.foundation.ui.view.custom.ActionListenerOwner
import com.dz.foundation.ui.view.recycler.DzRecyclerViewCell
import kotlin.math.abs

/**
 * 轮播图
 * @constructor
 */
class BannerCardComp @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int? = 0
) : TheatreCardComp<TheatreCompBannerListBinding>(
    context,
    attrs,
    defStyleAttr!!
), ActionListenerOwner<TheatreCardComp.ViewActionListener> {
    override var mActionListener: ViewActionListener? = null

    override fun initListener() {
        //继续观看
        mViewBinding.tvMoreAction.registerClickAction {
            mActionListener?.moreActionContentClick(mData)
        }
        mViewBinding.banner.addOnPageChangeListener(object : OnPageChangeListener {
            override fun onPageScrolled(
                position: Int,
                positionOffset: Float,
                positionOffsetPixels: Int
            ) {
            }

            override fun onPageSelected(position: Int) {
                trackBanner(position)
                mData?.videoData?.forEachIndexed { i, it ->
                    if (i == position) {
                        it.isSelect = 1
                    } else {
                        it.isSelect = 0
                    }
                }
                mViewBinding.rvIndicators.notifyDataSetChanged()
                smoothScrollToCenter(position)
            }

            override fun onPageScrollStateChanged(state: Int) {
                resetBannerStatus()
            }
        })
        //拦截指示器点击
        mViewBinding.interView.registerClickAction {

        }
    }

    //判断轮播是否继续轮播
    private fun resetBannerStatus() {
        val now = ViewUtils.exposeAreaRatio(mViewBinding.root, tabBarHeight)
        if (now < 1.0) {
            mViewBinding.banner.stop()
        } else {
            mViewBinding.banner.start()
        }
    }

    private fun trackBanner(position: Int) {
        //曝光重复判断 剧id 栏目id 一级标题id
        val tag = "$position${mData?.videoData?.get(position)?.bookId}${mData?.columnId}${
            mData?.videoData?.get(position)?.groupId
        }"
        if (needTrack(mData?.videoData?.get(position)?.groupId, tag)) {
            mData?.videoData?.get(position)?.let {
                TheatreManager.exposeBook(it)
            }
        }
    }

    override fun onExpose(isFirstExpose: Boolean) {
        super.onExpose(isFirstExpose)
        mData?.let {
            if ((mData?.videoData?.size ?: 0) >= mViewBinding.banner.currentItem) {
                trackBanner(mViewBinding.banner.currentItem - 1)
            }
        }
    }

    override fun initView() {
        mViewBinding.rvIndicators.itemAnimator = null
        mViewBinding.banner.post {
            resetBannerStatus()
        }
    }

    private val tabBarHeight = ScreenUtil.dip2px(context, 60)

    override fun subscribeEvent(lifecycleOwner: LifecycleOwner, lifecycleTag: String) {
        TheatreME.get().rvScroll().observe(lifecycleOwner) {
            resetBannerStatus()
        }
    }

    /**
     * 更新标题
     */
    private fun updateTitle(data: ColumnDataItem?) {
        if (data?.columnTitle?.isBlank() == true || data?.needConcat == true) {
            mViewBinding.tvTitle.visibility = GONE
        } else {
            mViewBinding.tvTitle.visibility = VISIBLE
            mViewBinding.tvTitle.text = data?.columnTitle
        }
        mViewBinding.tvMoreAction.visibility = GONE
        data?.moreTips?.let {
            mViewBinding.tvMoreAction.visibility = VISIBLE
            mViewBinding.tvMoreAction.text = it
        }
    }

    override fun bindData(data: ColumnDataItem?) {
        super.bindData(data)
        if (!data?.videoData.isNullOrEmpty()) {
            updateTitle(data)
            val allCell = mutableListOf<DzRecyclerViewCell<*>>()
            data?.videoData?.forEachIndexed { index, item ->
                item.listIndex = index
                item.groupId = data.bdChannelGroupId
                item.groupName = data.bdChannelGroupName
                item.groupPos = data.bdChannelGroupPos
                item.channelPos = data.currentChannel?.index
                item.channelName = data.currentChannel?.channelName
                item.channelId = data.currentChannel?.channelId?.toLong()
                item.columnId = (data.columnId ?: "").toString()
                item.columnName = data.columnTitle
                item.columnPos = data.columnPos
                item.styleTypeCn = data.styleTypeCn
                item.columnTitle = data.columnTitle
                item.isSelect = (if (index == 0) 1 else 0)
                DzRecyclerViewCell<BookInfoVo>().apply {
                    viewClass = BannerListItemIndicatorComp::class.java
                    viewData = item
                    allCell.add(this)
                }
            }
            val adapter = ShelfBannerAdapter(data?.videoData)
            mViewBinding.banner.setAdapter(adapter)
            mViewBinding.banner.visibility = VISIBLE
            val layoutParams = mViewBinding.rvIndicators.layoutParams
            if ((data?.videoData?.size ?: 0) < 5) {
                layoutParams.width = ViewGroup.LayoutParams.WRAP_CONTENT
            } else {
                layoutParams.width = ScreenUtil.dip2px(context, 76)
            }
            mViewBinding.rvIndicators.layoutParams = layoutParams
            mViewBinding.rvIndicators.allCells.clear()
            mViewBinding.rvIndicators.visibility = if (allCell.size > 1) VISIBLE else GONE
            mViewBinding.rvIndicators.addCells(allCell)
        } else {
            mViewBinding.rvIndicators.visibility = GONE
            mViewBinding.root.visibility = GONE
        }
    }

    private fun smoothScrollToCenter(position: Int) {
        val layoutManager = mViewBinding.rvIndicators.layoutManager as LinearLayoutManager
        val smoothScroller = object : LinearSmoothScroller(context) {
            override fun calculateSpeedPerPixel(displayMetrics: DisplayMetrics): Float {
                return 100f / displayMetrics.densityDpi
            }

            override fun calculateTimeForScrolling(dx: Int): Int {
                return abs(dx) * if (position == 0) 1 else 5
            }

            override fun calculateDtToFit(
                viewStart: Int, viewEnd: Int, boxStart: Int, boxEnd: Int, snapPreference: Int
            ): Int {
                return (boxStart + (boxEnd - boxStart) / 2) - (viewStart + (viewEnd - viewStart) / 2)
            }
        }
        smoothScroller.targetPosition = position
        layoutManager.startSmoothScroll(smoothScroller)
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        mViewBinding.banner.addBannerLifecycleObserver(getContainerFragment())
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        mViewBinding.banner.removeBannerLifecycleObserver()
    }

    override fun getCardType(): Int {
        return CARD_TYPE_BANNER
    }

    /**
     * 判断是否需要曝光打点
     */
    private fun needTrack(groupId: Long?, bannerTag: String): Boolean {
        return if (TheatreManager.bannerAlreadyTrackPositionSet[groupId]?.contains(bannerTag) == true) {
            false
        } else {
            if (TheatreManager.bannerAlreadyTrackPositionSet[groupId] == null) {
                TheatreManager.bannerAlreadyTrackPositionSet[groupId] = mutableListOf()
            }
            TheatreManager.bannerAlreadyTrackPositionSet[groupId]?.add(bannerTag)
            true
        }
    }
}