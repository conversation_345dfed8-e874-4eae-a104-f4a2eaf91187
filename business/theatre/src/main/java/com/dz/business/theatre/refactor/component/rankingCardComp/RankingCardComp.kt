package com.dz.business.theatre.refactor.component.rankingCardComp

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import android.util.TypedValue
import com.dz.business.base.theatre.data.BookInfoVo
import com.dz.business.theatre.R
import com.dz.business.theatre.databinding.TheatreCompRankCardBinding
import com.dz.business.theatre.refactor.manager.TheatreManager
import com.dz.foundation.base.utils.LogUtil
import com.dz.foundation.base.utils.ScreenUtil
import com.dz.foundation.base.utils.dp
import com.dz.foundation.imageloader.load
import com.dz.foundation.imageloader.loadRoundImg
import com.dz.platform.common.base.ui.component.UIConstraintComponent

/**
 * 榜单卡片
 * @constructor
 */
class RankingCardComp @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int? = 0
) : UIConstraintComponent<TheatreCompRankCardBinding, BookInfoVo>(
    context,
    attrs,
    defStyleAttr!!
) {
    override fun initData() {

    }

    override fun initView() {

    }

    override fun onExpose(isFirstExpose: Boolean) {
        super.onExpose(isFirstExpose)
        mData?.let {
            TheatreManager.exposeBook(it)
        }
    }

    override fun initListener() {
        //观看点击
        registerClickAction {
            TheatreManager.onItemClick(mData)
        }
    }

    override fun onBindRecyclerViewItem(model: BookInfoVo?, position: Int) {
        super.onBindRecyclerViewItem(model, position)
        model?.run {
            //1、标题:取内容{title}。规则：最多显示两行文案+省略号
            initTitle()
//            //2、左上角角标
            initRank(position)
//            //3、内容标签。规则：标签之间用空格隔开。最多显示三个标签
            initMarkView(bookTags)
//            //4、内容描述：取内容{description}。规则：最多显示三行文案+省略号
            initDesc()
//            //7、封面。规则：1.封面缩略图；
            coverWap?.let {
                mViewBinding.ivCover.loadRoundImg(
                    img = it,
                    radius = ScreenUtil.dip2px(context, 8),
                    placeholder = R.drawable.theatre_ic_comp1_default,
                    error = R.drawable.theatre_ic_comp1_default,
                    width = 92, height = 132
                )
            }
//            //更新提示
            mViewBinding.tvUpdate.text =
                if (finishStatusCn == context.getString(R.string.theatre_closed)) {
                    String.format(context.getString(R.string.theatre_whole), updateNum)
                } else {
                    String.format(
                        context.getString(R.string.theatre_update_to),
                        updateNum
                    )
                }
//            //新剧角标
            initNewVideoMark()
        }
    }

    private fun BookInfoVo.initTitle() {
        mViewBinding.tvTitle.text = bookName
        if (!rankSrc.isNullOrEmpty() && !rankSrcIcon.isNullOrEmpty()) {
            mViewBinding.ivHeat.load(rankSrcIcon, width = 24, height = 24)
            mViewBinding.tvHeat.text = rankSrc
        } else {
            mViewBinding.tvHeat.visibility = GONE
            mViewBinding.ivHeat.visibility = GONE
        }
    }

    private fun BookInfoVo.initDesc() {
        mViewBinding.tvHeat1.visibility = GONE
        mViewBinding.tvHeat2.visibility = GONE
        mViewBinding.tvDesc.text = introduction
        hotTips?.forEachIndexed { index, item ->
            if (index == 0) {
                mViewBinding.tvHeat1.visibility = VISIBLE
                mViewBinding.tvHeat1.text = item
            } else if (index == 1) {
                mViewBinding.tvHeat2.visibility = VISIBLE
                mViewBinding.tvHeat2.text = item
            }
        }
        mViewBinding.llHotTips.visibility = if (hotTips.isNullOrEmpty()) GONE else VISIBLE
    }

    @SuppressLint("SetTextI18n")
    private fun BookInfoVo.initRank(position: Int) {
        if (mPosition in 1..99) {
            mViewBinding.ivRank.visibility = VISIBLE
            mViewBinding.tvRank.visibility = VISIBLE
            when (mPosition) {
                1 -> mViewBinding.ivRank.setImageResource(R.drawable.bbase_ic_small_rank0)
                2 -> mViewBinding.ivRank.setImageResource(R.drawable.bbase_ic_small_rank1)
                3 -> mViewBinding.ivRank.setImageResource(R.drawable.bbase_ic_small_rank2)
                else -> mViewBinding.ivRank.setImageResource(R.drawable.bbase_ic_small_rank3)
            }
            mViewBinding.tvRank.text = ((mPosition ?: 0)).toString()
            if ((mPosition ?: 0) > 9) {
                mViewBinding.tvRank.setTextSize(
                    TypedValue.COMPLEX_UNIT_PX,
                    resources.getDimension(R.dimen.common_dp12)
                )
            } else {
                mViewBinding.tvRank.setTextSize(
                    TypedValue.COMPLEX_UNIT_PX,
                    resources.getDimension(R.dimen.common_dp14)
                )
            }
        } else {
            mViewBinding.ivRank.visibility = GONE
            mViewBinding.tvRank.visibility = GONE
        }
    }

    private fun initMarkView(marks: MutableList<String>?) {
        var mark = ""
        marks?.forEachIndexed { index, item ->
            if (index < 3) {
                if (mark.isNotEmpty()) {
                    mark += " "
                }
                mark += item
            }
        }
        mViewBinding.tvMark.text = mark
        if (marks.isNullOrEmpty() || marks.size == 0) {
            mViewBinding.tvMark.visibility = GONE
        } else {
            mViewBinding.tvMark.visibility = VISIBLE
        }
    }

    private fun BookInfoVo.initNewVideoMark() {
        mViewBinding.label.bindData(this)
    }
}