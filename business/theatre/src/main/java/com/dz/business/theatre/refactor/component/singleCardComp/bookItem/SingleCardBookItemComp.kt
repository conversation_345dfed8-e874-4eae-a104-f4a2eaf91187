package com.dz.business.theatre.refactor.component.singleCardComp.bookItem

import android.content.Context
import android.util.AttributeSet
import androidx.lifecycle.LifecycleOwner
import com.dz.business.base.data.bean.BaseBookInfo
import com.dz.business.base.home.HomeME
import com.dz.business.base.home.HomeMR
import com.dz.business.base.theatre.data.BookInfoVo
import com.dz.business.base.vm.getViewModel
import com.dz.business.theatre.R
import com.dz.business.theatre.databinding.TheatreCompSingleCardBookItemBinding
import com.dz.business.theatre.refactor.manager.TheatreManager
import com.dz.business.track.utis.ElementClickUtils
import com.dz.foundation.base.utils.ScreenUtil
import com.dz.foundation.base.utils.dp
import com.dz.foundation.imageloader.load
import com.dz.foundation.imageloader.loadRoundImg
import com.dz.platform.common.base.ui.component.UIConstraintComponent

/**
 * 单卡
 * @constructor
 */
class SingleCardBookItemComp :
    UIConstraintComponent<TheatreCompSingleCardBookItemBinding, BookInfoVo> {
    private var viewModel: SingleCardBookItemVM? = null
    private var contract: SingleCardBookItemContract? = null

    //是否是收藏状态
    private var isFavorite = false

    @JvmOverloads
    constructor(
        context: Context,
        attrs: AttributeSet? = null,
        defStyleAttr: Int = 0,
    ) : super(context, attrs, defStyleAttr)

    override fun initData() {
        viewModel = getViewModel(SingleCardBookItemVM::class.java)
        contract = viewModel
    }

    override fun onExpose(isFirstExpose: Boolean) {
        super.onExpose(isFirstExpose)
        mData?.let {
            TheatreManager.exposeBook(it)
        }
    }

    override fun initView() {
    }

    override fun initListener() {
        //继续观看按钮点击事件
        ElementClickUtils.ignoreAutoTrack(mViewBinding.clLayout)
        ElementClickUtils.ignoreAutoTrack(mViewBinding.ivCover)
        ElementClickUtils.ignoreAutoTrack(mViewBinding.tvRankInfo)
        ElementClickUtils.ignoreAutoTrack(mViewBinding.tvOcpc)
        mViewBinding.clLayout.registerClickAction {
            TheatreManager.onItemClick(mData)
        }

        //收藏按钮点击事件
        mViewBinding.llChase.registerClickAction {
            if (isFavorite) {
                HomeMR.get().favoriteDialog().apply {
                    cancelText = "再想想"
                    sureText = "确认"
                    title = "确认取消追剧吗？"
                    content = "取消后可能找不到本剧哦~"
                }.onSure {
                    contract?.onBookCancelFavoriteClick(mData)
                }.start()
            } else {
                contract?.onBookFavoriteClick(mData)
            }
        }

        //排行榜信息点击事件
        mViewBinding.tvRankInfo.registerClickAction {
            if (mData?.rankActionTips?.isNotBlank() == true && mData?.rankAction?.isNotBlank() == true) {
                TheatreManager.onRankUrlClick(mData)
            }
        }
    }

    private fun updateBookFavoriteState() {
        if (isFavorite) {
            mViewBinding.ivChase.visibility = GONE
            mViewBinding.tvChase.text = "已追"
            mViewBinding.tvChase.setTextColor(getColor(R.color.common_959595))
        } else {
            mViewBinding.ivChase.visibility = VISIBLE
            mViewBinding.tvChase.text = "追"
            mViewBinding.tvChase.setTextColor(getColor(R.color.common_FF161718))
        }
    }


    override fun subscribeObserver(lifecycleOwner: LifecycleOwner) {
        super.subscribeObserver(lifecycleOwner)
        contract?.bookFavoriteState?.observe(lifecycleOwner) {
            isFavorite = it
            updateBookFavoriteState()
        }
        HomeME.get().addFavoriteSuccess().observe(lifecycleOwner) { bookId ->
            if (bookId == mData?.bookId) {
                contract?.onBookFavoriteStateChanged(true)
            }
        }

        HomeME.get().deleteFavoriteSuccess().observe(lifecycleOwner) { bookIds ->
            if (bookIds.contains(mData?.bookId)) {
                contract?.onBookFavoriteStateChanged(false)
            }
        }
    }

    /**
     * 剧封右上角类型的样式
     */
    private fun updateTypeStyle(data: BookInfoVo) {
        mViewBinding.label.bindData(data)
    }


    private fun updateView(data: BookInfoVo?) {
        data?.let {
            updateTypeStyle(it)
            mViewBinding.tvBookName.text = it.bookName
            mViewBinding.tvPlayCount.text = it.coverBottomTips

            isFavorite = it.inBookShelf == true
            updateBookFavoriteState()
            //标签
            it.bookTags?.let { bookTags ->
                mViewBinding.tvTags.visibility = VISIBLE
                mViewBinding.tvTags.text = bookTags.take(3).joinToString(separator = " ")
            }

            //简介
            it.introduction?.let { introduction ->
                mViewBinding.tvIntroduction.visibility = VISIBLE
                mViewBinding.tvIntroduction.text = introduction
            }

            // OCPC来源文案
            if (!it.guideTip.isNullOrEmpty()) {
                mViewBinding.tvRankInfo.visibility = GONE
                mViewBinding.tvOcpc.text = it.guideTip
                mViewBinding.tvOcpc.visibility = VISIBLE
            } else {
                //排行榜
                if (it.rankActionTips?.isNotBlank() == true) {
                    mViewBinding.tvRankInfo.visibility = VISIBLE
                    mViewBinding.tvRankInfo.text = it.rankActionTips
                    it.showInfoType = BaseBookInfo.SHOW_INFO_RANK
                } else if (it.recReason?.isNotBlank() == true) {
                    mViewBinding.tvRankInfo.visibility = VISIBLE
                    mViewBinding.tvRankInfo.text = it.recReason
                    it.showInfoType = BaseBookInfo.SHOW_INFO_RECOMMEND_REASON
                }
            }

            //剧封
            mViewBinding.ivCover.loadRoundImg(
                img = it.coverWap,
                radius = ScreenUtil.dip2px(context, 8),
                placeholder = R.drawable.bbase_ic_cover_default,
                error = R.drawable.bbase_ic_cover_default,
                width = 165,
                height = 214
            )
        }
    }

    override fun onBindRecyclerViewItem(itemData: BookInfoVo?, position: Int) {
        super.onBindRecyclerViewItem(itemData, position)
        updateView(itemData)
    }


}