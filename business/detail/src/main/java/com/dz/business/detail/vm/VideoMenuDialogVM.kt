package com.dz.business.detail.vm

import BBaseME
import com.blankj.utilcode.util.GsonUtils
import com.dz.business.base.data.BBaseKV
import com.dz.business.base.data.bean.ShareItemBean
import com.dz.business.base.data.bean.WxShareConfigVo
import com.dz.business.base.detail.intent.VideoMenuIntent
import com.dz.business.base.track.TrackUtil
import com.dz.business.base.utils.toEnableFlag
import com.dz.business.video.danmu.VideoDanMuManager
import org.json.JSONObject

/**
 * @Description: 竖屏菜单弹窗
 */
class VideoMenuDialogVM : ResolutionVM<VideoMenuIntent>() {

    //微信分享配置信息
    var mWxShareConfigVo: WxShareConfigVo? = null

    fun initShareData() {
        mWxShareConfigVo = GsonUtils.fromJson(BBaseKV.shareConfig, WxShareConfigVo::class.java)
    }

    /**
     * 三点菜单里弹幕按钮是否可见
     */
    fun getMenuDanmuVisible(): Boolean {
        val danmuSwitchV2 = VideoDanMuManager.getDanmuSwitchV2()
        // 实验组，要求三点菜单里的开关不仅控制视频播放页面的弹幕按钮显示隐藏，也控制弹幕的显示和隐藏。所以实验组只要云端没有禁止弹幕功能则总是显示。
        return if (danmuSwitchV2) {
            VideoDanMuManager.isEnable()
        } else {// 对照组，三点菜单里的开关隐藏。
            false
        }
    }

    /**
     * 三点菜单里弹幕打开和关闭点击，不仅控制视频播放页面的弹幕显示隐藏，也控制弹幕功能的显示隐藏。只有实验组可以操作此按钮。
     */
    fun menuDanmuSwitch(menuOn: Boolean) {
        BBaseKV.danmuSwitch = menuOn.toEnableFlag()
        BBaseME.get().onDanMuEnableLocalChanged().post(menuOn)
        trackButtonClick(menuOn)
    }

    /**
     * 获取三点菜单里的弹幕check状态
     */
    fun getDanmuMenuChecked(): Boolean {
        return VideoDanMuManager.isOn()
    }

    /**
     * 微信及朋友圈分享按钮是否可用。
     * @return first:微信分享是否可用 second:朋友圈分享是否可用。
     */
    private fun getShareEnable(): Pair<Boolean, Boolean> {
        var showInMenu = true

        mWxShareConfigVo?.let { config ->
            showInMenu = 1 == config.iconPosition
        }
        if (!showInMenu) {
            return Pair(false, false)
        }
        val isWxShare = mWxShareConfigVo?.isWxShare
        when (isWxShare) {
            1 ->
                return Pair(true, true)

            2 ->
                return Pair(false, false)

            3 ->
                return Pair(true, false)

            4 ->
                return Pair(false, true)
        }
        return Pair(true, true)
    }


    /**
     * 微信及朋友圈分享按钮是否有可分享的项。
     * @return first:微信分享是否可用 second:朋友圈分享是否可用。
     */
    private fun getShareItemSupport(): Pair<Boolean, Boolean> {
        var wechatShareItemSupport = false
        var momentShareItemSupport = false
        val shareVoList = mWxShareConfigVo?.shareVoList
        shareVoList?.forEach { item ->
            if (item?.shareType == ShareItemBean.SHARE_SCENE_WX_FRIEND) {
                wechatShareItemSupport = true
            } else if (item?.shareType == ShareItemBean.SHARE_SCENE_WX_FRIEND_CIRCLE) {
                momentShareItemSupport = true
            }
        }
        return Pair(wechatShareItemSupport, momentShareItemSupport)
    }

    /**
     * 分享按钮是否可见完全按照开关，和有没有下发分享的项来决定。
     */
    fun getShareShow(): Pair<Boolean, Boolean> {
        val shareEnablePair = getShareEnable()
        val shareItemSupportPair = getShareItemSupport()
        return Pair(
            shareEnablePair.first && shareItemSupportPair.first,
            shareEnablePair.second && shareItemSupportPair.second
        )
    }


    //打点
    fun trackAppClick() {
        val danmuMenuChecked = getDanmuMenuChecked()
        kotlin.runCatching {
            val jsonObject = JSONObject().apply {
                put("\$element_content", "弹幕")
                put("BulletSwitch", if (danmuMenuChecked) "开" else "关")
            }
            TrackUtil.track("\$AppClick", jsonObject)
        }.onFailure {
            it.printStackTrace()
        }
    }

    //打点
    fun trackButtonClick(menuOn: Boolean) {
        kotlin.runCatching {
            val jsonObject = JSONObject().apply {
                put("ButtonName", "弹幕")
                put("ButtonContent", if (menuOn) "开启弹幕" else "关闭弹幕")
            }
            TrackUtil.track("ButtonClick", jsonObject)
        }.onFailure {
            it.printStackTrace()
        }
    }
}