package com.dz.business.detail.ui.component.selections

import android.content.Context
import androidx.lifecycle.LifecycleOwner
import androidx.recyclerview.widget.RecyclerView
import com.dz.business.base.R
import com.dz.business.base.data.bean.ChapterInfoVo
import com.dz.business.base.data.bean.VideoSubChapter
import com.dz.business.base.ui.BaseDialogComp
import com.dz.business.detail.databinding.DetailCompDramaListLandScapeDialogBinding
import com.dz.business.detail.vm.DramaListLandSpaceDialogVM
import com.dz.foundation.ui.view.recycler.DzRecyclerViewCell
import kotlin.math.roundToInt

/**
 * @Author: guyh
 * @Date: 2023/12/12 16:21
 * @Description:横屏选集dialog
 * @Version:1.0
 */
class LandScapeDialogComp(context: Context) :
    BaseDialogComp<DetailCompDramaListLandScapeDialogBinding, DramaListLandSpaceDialogVM>(context) {
    private var totalScrollY: Int = 0
    private var currentTabPosition: Int = 0
    private var tabSelected: Boolean = false
    private val scrollListener = object : RecyclerView.OnScrollListener() {
        override fun onScrolled(rv: RecyclerView, dx: Int, dy: Int) {
            totalScrollY += dy
            if (tabSelected || mViewModel.rvPageHeight == 0) {
                tabSelected = false
                return
            }
            var tempTabPosition = (totalScrollY.toFloat() / mViewModel.rvPageHeight).roundToInt()
            var size = mViewModel.listData.value?.size ?: 0
            if ((mViewModel.listData.value?.size ?: 0) < 60 && isBottom()) {
                tempTabPosition = 1
            }
            var tabData = mViewModel.partition(
                size,
                mViewModel.currentChapter,
                30
            )
            if (tabData.size > 1 && tempTabPosition >= tabData.size) {
                tempTabPosition = tabData.size - 1
            }
            if (currentTabPosition != tempTabPosition) {
                tabData.apply {
                    forEachIndexed { index, item ->
                        if (index == tempTabPosition) {
                            item.isDefault = 1
                        } else {
                            item.isDefault = 0
                        }
                    }
                    currentTabPosition = tempTabPosition
                }
                mViewBinding.tabList.updateData(tabData)
            }
        }
    }

    private val tabListener = object : LandScapeTabComp.ViewActionListener {
        override fun onTabSelected(tabBean: VideoSubChapter) {
            tabSelected = true
            mViewBinding.rv.post {
                currentTabPosition = tabBean.position
                mViewBinding.rv.scrollBy(
                    0,
                    mViewModel.rvPageHeight * currentTabPosition - totalScrollY
                )
            }
        }
    }

    override fun initData() {
        mViewModel.getData(context)
    }

    override fun initView() {
        dialogSetting.backgroundColor = getColor(R.color.common_transparent)
        mViewModel.routeIntent?.let {
            if (it.showPermanentFree == true) {
                mViewBinding.ivFree.visibility = VISIBLE
            } else {
                mViewBinding.ivFree.visibility = GONE
            }
        }
    }

    override fun initListener() {
        mViewBinding.rv.addOnScrollListener(scrollListener)
        mViewBinding.viewSpace.registerClickAction { dismiss() }
        mViewBinding.tabList.setActionListener(tabListener)
    }

    private fun isBottom(): Boolean {//1代表底部,返回true表示没到底部,还可以滑
        return !mViewBinding.rv.canScrollVertically(1)
    }

    override fun subscribeObserver(lifecycleOwner: LifecycleOwner) {
        super.subscribeObserver(lifecycleOwner)
        mViewModel.listData.observe(lifecycleOwner) { listData ->
            //标题
            initTitle()
            //tab栏目
            initTab(listData)
            //剧集列表
            initList(listData)
        }
    }

    /**
     *  标题
     */
    private fun initTitle() {
        //标题
        mViewBinding.tvTitle.text = mViewModel.bookName
        //状态
        if (mViewModel.finishStatus == 0) {
            mViewBinding.ivLebal.setImageResource(R.drawable.bbase_ic_updata_lebal_h)
        } else {
            mViewBinding.ivLebal.setImageResource(R.drawable.bbase_ic_end_lebal_h)
        }
    }

    /**
     *  tab栏目
     * @param listData
     */
    private fun initTab(listData: MutableList<ChapterInfoVo>) {
        if (listData.size < 31) {
            mViewBinding.tabList.visibility = GONE
        } else {
            mViewBinding.tabList.visibility = VISIBLE
            mViewBinding.tabList.bindData(
                mViewModel.partition(
                    listData.size,
                    mViewModel.currentChapter,
                    30
                )
            )
        }
    }

    /**
     *  剧集列表
     * @param listData
     */
    private fun initList(listData: MutableList<ChapterInfoVo>?) {
        val allCell = mutableListOf<DzRecyclerViewCell<*>>()
        allCell.addAll(createList(listData))
        allCell.add(createBottom())
        mViewBinding.rv.addCells(allCell)
        mViewBinding.rv.post {
            val row: Int = mViewModel.currentChapter / 6 -
                    if (mViewModel.currentChapter % 6 == 0) 1
                    else 0
            currentTabPosition = row
            mViewBinding.rv.scrollBy(0, mViewModel.rowWH * row)
        }
    }

    /**
     * 创建列表
     * @param chapters
     * @return
     */
    private fun createList(chapters: MutableList<ChapterInfoVo>?): MutableList<DzRecyclerViewCell<*>> {
        val cellList = mutableListOf<DzRecyclerViewCell<*>>()
        chapters?.forEach { data ->
            DzRecyclerViewCell<ChapterInfoVo>().apply {
                viewClass = LandScapeListItemComp::class.java
                viewData = data
                setActionListener(object : LandScapeListItemComp.ViewActionListener {
                    override fun onChecked(dramaBean: ChapterInfoVo?) {
                        dramaBean?.let {
                            dismiss()
                            mViewModel.routeIntent?.doOnSelect(it)
                        }
                    }
                })
                cellList.add(this)
            }
        }
        return cellList
    }

    private fun createBottom(): DzRecyclerViewCell<*> {
        return DzRecyclerViewCell<Any>().apply {
            viewClass = LandScapeBottomComp::class.java
            viewData = Any()
            spanSize = 6
        }
    }

    override fun getEnterAnim(): Int {
        return R.anim.common_ac_in_from_right
    }

    override fun getExitAnim(): Int {
        return R.anim.common_ac_out_from_right
    }

}