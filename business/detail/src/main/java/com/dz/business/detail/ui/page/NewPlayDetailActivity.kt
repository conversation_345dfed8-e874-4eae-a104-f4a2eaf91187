package com.dz.business.detail.ui.page

import BBaseME
import Coroutine<PERSON>tils
import android.animation.ObjectAnimator
import android.annotation.SuppressLint
import android.app.Activity
import android.content.ComponentCallbacks
import android.content.res.Configuration
import android.graphics.Bitmap
import android.graphics.PixelFormat
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.text.TextUtils
import android.view.KeyEvent
import android.view.MotionEvent
import android.view.OrientationEventListener
import android.view.View
import android.view.View.GONE
import android.view.View.SYSTEM_UI_FLAG_FULLSCREEN
import android.view.View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
import android.view.View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
import android.view.View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
import android.view.View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
import android.view.View.SYSTEM_UI_FLAG_LAYOUT_STABLE
import android.view.View.VISIBLE
import android.view.ViewConfiguration
import android.view.ViewGroup
import android.view.WindowManager
import android.view.animation.DecelerateInterpolator
import androidx.core.content.ContextCompat
import androidx.core.view.children
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.widget.ViewPager2
import com.blankj.utilcode.util.GsonUtils
import com.dianzhong.base.data.bean.sky.RewardCloseParams
import com.dz.business.DzDataRepository
import com.dz.business.base.BBaseMC
import com.dz.business.base.BBaseMC.TAG_RESOLUTION
import com.dz.business.base.ad.AdMC
import com.dz.business.base.ad.AdMS
import com.dz.business.base.ad.callback.DzAdShowCallback
import com.dz.business.base.ad.callback.IAdBehavior
import com.dz.business.base.bcommon.BCommonMC
import com.dz.business.base.bcommon.BCommonME
import com.dz.business.base.bcommon.BCommonMS
import com.dz.business.base.bcommon.ShareDialogListener
import com.dz.business.base.bcommon.ShareListener
import com.dz.business.base.data.BBaseKV
import com.dz.business.base.data.BBaseKV.detailGuideStatus
import com.dz.business.base.data.BBaseKV.followBubbleStatus
import com.dz.business.base.data.BBaseKV.needFollowBubble
import com.dz.business.base.data.PageConstant
import com.dz.business.base.data.bean.ChapterInfoVo
import com.dz.business.base.data.bean.FollowSourceType
import com.dz.business.base.data.bean.PlayEventBean
import com.dz.business.base.data.bean.PreLoadFunSwitchVo
import com.dz.business.base.data.bean.REWARD_SCENE_LOOK_AGAIN
import com.dz.business.base.data.bean.REWARD_SCENE_NORMAL
import com.dz.business.base.data.bean.RecommendVideoInfo
import com.dz.business.base.data.bean.RemoveAdWayVo
import com.dz.business.base.data.bean.ResolutionRateVo
import com.dz.business.base.data.bean.ShareItemBean
import com.dz.business.base.data.bean.ShareResultBean
import com.dz.business.base.data.bean.StrategyInfo
import com.dz.business.base.data.bean.SwitchState
import com.dz.business.base.data.bean.TierPlaySourceVo
import com.dz.business.base.data.bean.VideoDetailBean
import com.dz.business.base.data.bean.VideoInfoVo
import com.dz.business.base.data.bean.WelfarePendantConfigVo
import com.dz.business.base.data.bean.WxShareConfigVo
import com.dz.business.base.detail.DetailMC
import com.dz.business.base.detail.DetailMC.Companion.AD_MALL_TAG
import com.dz.business.base.detail.DetailMC.Companion.TAG_PLAYER
import com.dz.business.base.detail.DetailMC.Companion.UNLOCK
import com.dz.business.base.detail.DetailME
import com.dz.business.base.detail.DetailMR
import com.dz.business.base.detail.DetailMS
import com.dz.business.base.detail.intent.ResolutionIntent
import com.dz.business.base.detail.intent.VideoListIntent
import com.dz.business.base.dialog.DialogME
import com.dz.business.base.dialog.DialogMS
import com.dz.business.base.download.DownloadMS
import com.dz.business.base.experiment.ExperimentMC
import com.dz.business.base.flutter.FlutterMS
import com.dz.business.base.helper.FloatWindowManage.Companion.PLAY_SOURCE
import com.dz.business.base.home.HomeMC
import com.dz.business.base.home.HomeME
import com.dz.business.base.home.HomeMR
import com.dz.business.base.home.HomeMS
import com.dz.business.base.home.PlayEventReportCallback
import com.dz.business.base.home.data.LikesInfo
import com.dz.business.base.load.DBHelper
import com.dz.business.base.main.MainMC
import com.dz.business.base.main.MainME
import com.dz.business.base.main.MainMR
import com.dz.business.base.main.intent.MainIntent
import com.dz.business.base.monitor.MonitorMS
import com.dz.business.base.operation.OperationMC
import com.dz.business.base.operation.OperationMS
import com.dz.business.base.operation.interfaces.OperationReportCallback
import com.dz.business.base.personal.PersonalME
import com.dz.business.base.priority.PriorityConstants
import com.dz.business.base.priority.PriorityMC
import com.dz.business.base.priority.PriorityTaskManager
import com.dz.business.base.priority.tasks.PriorityDialogTask
import com.dz.business.base.priority.tasks.TheatreRetainDialogTask
import com.dz.business.base.reader.ReaderMS
import com.dz.business.base.recharge.RechargeMC
import com.dz.business.base.recharge.RechargeMR
import com.dz.business.base.recharge.intent.RechargeIntent
import com.dz.business.base.recharge.intent.RechargeVipDialogIntent
import com.dz.business.base.search.SearchMR
import com.dz.business.base.track.ThirdSDKTrack
import com.dz.business.base.ui.IPlayDetailHotSplash
import com.dz.business.base.ui.component.status.StatusComponent
import com.dz.business.base.ui.player.PrerenderConfig
import com.dz.business.base.ui.player.adapter.BaseViewHolder
import com.dz.business.base.ui.player.listener.INewPlayerListener
import com.dz.business.base.ui.viewpager2.OnPageChangeCallbackCompat
import com.dz.business.base.ui.viewpager2.ViewPager2Helper
import com.dz.business.base.utils.ActionRecorder
import com.dz.business.base.utils.CommInfoUtil
import com.dz.business.base.utils.DeviceInfoHelper
import com.dz.business.base.utils.GsonUtil
import com.dz.business.base.utils.OCPCManager
import com.dz.business.base.video.VideoMC
import com.dz.business.base.video.VideoMC.TAG_PLAYING_DURATION
import com.dz.business.base.video.VideoME
import com.dz.business.base.video.VideoMS
import com.dz.business.base.video.data.CommentNumBean
import com.dz.business.base.video.data.CommentNumCheckDatabaseBean
import com.dz.business.base.video.intent.CommentIntent
import com.dz.business.base.vm.event.RequestEventCallback
import com.dz.business.base.welfare.WelfareMC
import com.dz.business.base.welfare.WelfareME
import com.dz.business.base.welfare.widget.FloatWidgetListener
import com.dz.business.base.welfare.widget.IProgressDragPendantComp
import com.dz.business.base.welfare.widget.IProgressDynamicPendantComp
import com.dz.business.base.welfare.widget.IProgressPendantComp
import com.dz.business.base.welfare.widget.IProgressPendantComp2
import com.dz.business.base.welfare.widget.PendantComp
import com.dz.business.base.widget.WidgetMS
import com.dz.business.bcommon.data.BCommonKV
import com.dz.business.bcommon.utils.PlayingStatisticsMgr
import com.dz.business.detail.R
import com.dz.business.detail.adapter.AdVideoViewHolder
import com.dz.business.detail.adapter.NewDetailPlayerPageAdapter
import com.dz.business.detail.adapter.NewDetailVideoViewHolder
import com.dz.business.detail.data.ChapterUnlockBean
import com.dz.business.detail.data.DetailKV
import com.dz.business.detail.databinding.DetailActivityPlayNewDetailBinding
import com.dz.business.detail.delegate.FollowTipManager
import com.dz.business.detail.delegate.comment.DetailCommentDelegate
import com.dz.business.detail.delegate.exit.ExitDelegate
import com.dz.business.detail.delegate.guide.GuideDelegate
import com.dz.business.detail.enums.BottomStyle
import com.dz.business.detail.enums.Orientation
import com.dz.business.detail.enums.PlayMode
import com.dz.business.detail.layer.BackLayer
import com.dz.business.detail.layer.BottomLayer
import com.dz.business.detail.layer.GuideLayer
import com.dz.business.detail.layer.NewPlayerControllerLayer
import com.dz.business.detail.layer.PreviewUnlockLayer
import com.dz.business.detail.layer.PreviewUnlockLayer.Companion.isPreviewChapter
import com.dz.business.detail.listener.RatingObserver
import com.dz.business.detail.persenter.LoadResultPresenter
import com.dz.business.detail.presenter.NewBannerAdPresenter
import com.dz.business.detail.presenter.OrientationManager
import com.dz.business.detail.ui.component.AdUnlockedDialogComp
import com.dz.business.detail.ui.component.DetailPlayerController
import com.dz.business.detail.ui.component.FinalRecommendStyle2
import com.dz.business.detail.ui.component.FinalRecommendStyle3
import com.dz.business.detail.ui.component.RatingComp
import com.dz.business.detail.ui.component.ad.PauseAdComp
import com.dz.business.detail.util.AngleUtils
import com.dz.business.detail.util.DetailRecommendUtil
import com.dz.business.detail.util.DrawAdManager
import com.dz.business.detail.util.DrawAdTimeManager
import com.dz.business.detail.util.FinalRecommendManager
import com.dz.business.detail.util.VideoInfoGetter
import com.dz.business.detail.util.VideoTrackUtils
import com.dz.business.detail.util.WelfareAnchorAdLoader
import com.dz.business.detail.vm.TrackUtil
import com.dz.business.detail.vm.VideoListVM
import com.dz.business.repository.entity.BookEntity
import com.dz.business.track.base.addParam
import com.dz.business.track.events.DzTrackEvents
import com.dz.business.track.events.sensor.AdTE
import com.dz.business.track.events.sensor.ErrorTE
import com.dz.business.track.events.sensor.OperationClickTE
import com.dz.business.track.events.sensor.OperationExposureTE
import com.dz.business.track.events.sensor.ReadingTE
import com.dz.business.track.events.sensor.ShareTE
import com.dz.business.track.monitor.MonitorMC
import com.dz.business.track.monitor.trackToSensor
import com.dz.business.track.trace.OmapNode
import com.dz.business.track.trace.QmapNode
import com.dz.business.track.tracker.SensorTracker
import com.dz.business.track.utis.ElementClickUtils
import com.dz.business.video.VideoDetailDelegate
import com.dz.business.video.danmu.VideoDanMuManager
import com.dz.business.video.data.OperateReportBean
import com.dz.business.video.data.VideoKV
import com.dz.business.video.data.WelfareReward
import com.dz.business.video.enums.GestureType
import com.dz.business.video.enums.PlayState
import com.dz.business.video.interfaces.VideoLifecycle
import com.dz.business.video.track.VideoTrackUtil
import com.dz.business.video.utils.OrientationUtil
import com.dz.business.video.utils.VideoPlayTimeManager
import com.dz.business.welfare.WelfareMS
import com.dz.business.welfare.floatting.FloatWidgetManager.marginTop
import com.dz.foundation.base.friendly.noOpDelegate
import com.dz.foundation.base.manager.task.Task
import com.dz.foundation.base.manager.task.TaskManager
import com.dz.foundation.base.manager.task.TaskManager.Companion.delayTask
import com.dz.foundation.base.module.AppModule
import com.dz.foundation.base.utils.AppActiveManager
import com.dz.foundation.base.utils.CrashUtils
import com.dz.foundation.base.utils.DeviceInfoUtil
import com.dz.foundation.base.utils.LocalActivityMgr
import com.dz.foundation.base.utils.LogUtil
import com.dz.foundation.base.utils.NetWorkUtil
import com.dz.foundation.base.utils.NotificationUtil
import com.dz.foundation.base.utils.PermissionUtils
import com.dz.foundation.base.utils.ScreenUtil
import com.dz.foundation.base.utils.SystemTimeUtils
import com.dz.foundation.base.utils.TimeUtils
import com.dz.foundation.base.utils.ViewUtils
import com.dz.foundation.base.utils.monitor.PlayerMonitorManager
import com.dz.foundation.base.utils.monitor.TimeMonitorManager
import com.dz.foundation.event.DzEventManager
import com.dz.foundation.network.requester.RequestException
import com.dz.foundation.ui.utils.AlphaUtil
import com.dz.foundation.ui.widget.OnItemTouchListener
import com.dz.platform.ad.CoinsDropIntervalUtil
import com.dz.platform.ad.callback.BaseAdLoadCacheCallback
import com.dz.platform.ad.callback.BaseAdShowCallback
import com.dz.platform.ad.data.BannerAdKV
import com.dz.platform.ad.data.DrawAdKV
import com.dz.platform.ad.draw.DrawSession
import com.dz.platform.ad.lifecycle.PlayDetailLifeCycle
import com.dz.platform.ad.lifecycle.PlayScene
import com.dz.platform.ad.manager.MineMallAdManager
import com.dz.platform.ad.sky.InterstitialAd
import com.dz.platform.ad.sky.SkyAd
import com.dz.platform.ad.vo.MallAdVo
import com.dz.platform.common.base.ui.dialog.PDialogComponent
import com.dz.platform.common.router.addDismissListener
import com.dz.platform.common.router.addShowListener
import com.dz.platform.common.router.onDismiss
import com.dz.platform.common.router.onShow
import com.dz.platform.common.toast.ToastManager
import com.dz.platform.player.listener.ConvertURLCallback
import com.dz.platform.player.listener.OnInfoListener
import com.dz.platform.player.listener.OnSnapShotListener
import com.dz.platform.player.listener.OnStateChangedListener
import com.dz.platform.player.player.BasePlayerManager
import com.dz.platform.player.player.PlayerInfo
import com.google.gson.reflect.TypeToken
import com.gyf.immersionbar.BarHide
import com.therouter.TheRouter
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import me.jessyan.autosize.internal.CustomAdapt
import org.json.JSONException
import org.json.JSONObject
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import kotlin.math.abs
import com.dz.foundation.base.utils.RandomUtils
import com.dz.business.base.web.WebMS


/**
 * @Author: guyh
 * @Date: 2024/9/4
 * @Description:
 * @Version:1.0
 */
class NewPlayDetailActivity : BaseDetailPlayerActivity<DetailActivityPlayNewDetailBinding, VideoListVM>(),
    CustomAdapt, FollowTipManager.FollowTipListener, AppActiveManager.OnAppActiveListener,
    VideoInfoGetter, IPlayDetailHotSplash, IAdBehavior {

    companion object {
        const val TAG = "player_detail"
    }

    private var vpState = ViewPager2.SCROLL_STATE_IDLE

    /**
     * 章节切换中的标识
     */
    private var chapterSwitching = false

    private lateinit var mPageAdapter: NewDetailPlayerPageAdapter

    private var currentHolder: NewDetailVideoViewHolder? = null

    private var isPlayerPrePrepared: Boolean = false//预加载的播放器是否已经prepared

    private var slideDirection = 0//滑动方向，默认向下滑动0，向上滑动1
    private val localPlayingDuration: Float  // 本地播放时长
        get() {
            return PlayingStatisticsMgr.getLocalPlayingDurationFloat()
        }

    private var mTouchSlop = 20
    /**
     * 当前正在播放的剧id
     * 当二级页被多次打开时，如果要打开的剧与正在播的剧一样。那么根据bookId做排重处理。
     */
    var mBookId: String? = null

    /**
     * 播放时长
     */
    var currentDuration: Long = 0L

    /**
     * 真实的首次播放来源
     */
    private var realFirstPlaySource: String? = null

    /**
     * 上一个页面传递过来的首次播放来源
     */
    private var mFirstPlaySource: String? = null

    /**
     * 是否在后台
     */
    private var mIsOnBackground = false

    /**
     * 是否继续播放
     */
    private var mIsKeepPlaying = true

    /**
     * 试看引导蒙层是否显示？
     */
    private var isPreviewGuideShowing: Boolean = false

    /**
     * 当前选中位置.
     * 在整个列表（包含广告和视频）中的位置
     */
    private var mCurrentPosition = 0

    /**
     * 当前播放位置
     */
    private var mCurrentPlayPosition = -1

    /**
     * 由于网络原因导致的暂停
     */
    private var pausedByNetwork = false

    /**
     * 正常滑动，上一个被暂停的位置
     * 上一个被划走的位置。处理重复选中的问题；
     */
    private var mLastStopPosition = -1
    private var pipChangeVideo = false
    private var activePause: Boolean = false

    //第一帧是否已经渲染
    private var firstRendering = false

    //用户是否在本次二级页进入过解锁页面
    private var firstLock = false

    //是否播放完成
    private var isPlayCompletion = false
    private var playCount = 0
    private var startPlayTime = 0L

    /**
     * 当前视频的时长
     */
    private var currentVideoDuration = 0L

    //自动切换的视频索引
    private var isAutoSelectIndex = -1

    /**
     * 当前视频是否正在播放
     */
    private var isPlaying = false

    /**
     * 用户在播放中的时长
     */
    private var playingTime = 0L

    /**
     * 试看下是否禁止手动暂停
     */
    private var previewNotAllowPause = false

    /**
     * 解锁模块重构后要删掉
     */
    var detailDelegate: VideoDetailDelegate? = null

    /**
     * 负责各种引导提示的代理
     */
    private var guideDelegate: GuideDelegate? = null

    /**
     * 退出挽留
     */
    var exitDelegate: ExitDelegate? = null

    /**
     * 解锁当前集所需的看点
     */
    private var curChapterCoins: Int? = null

    /**
     * 福利挂件
     */
    private var welfareWidget: View? = null

    /**
     * 列表中 正在显示的 页面 是否是沉浸式广告
     */
    private var isDrawAdPageShowing = false

    /**
     * 当屏幕回到竖屏状态下是否要显示挂件
     * 挂件配置信息是异步获取的 && 横屏下不显示挂件
     * 当在横屏下获取到配置信息后，要记录下来，当转到竖屏时再将挂件添加到屏幕上
     */
    private var needAddWelfareWidgetWhenPort = false

    /**
     * 屏幕方向监听器
     */
    private var orientationListener: OrientationEventListener? = null

    /**
     * 当前屏幕的水平角度
     */
    private var screenAngle: Int = AngleUtils.DIRECTION_1

    /**
     * 是否有挂件的配置信息
     */
    private var hasWelfareConfig = false

    private val mVideoLifecycle: VideoLifecycle = VideoLifecycle()
    private val mAdLifecycle = PlayDetailLifeCycle()
    private var mOnPageChangeCallbackCompat: OnPageChangeCallbackCompat? = null

    /**
     * 负责弹幕相关的操作
     */
    private var videoDanMuManger: VideoDanMuManager? = null

    /**
     * 负责视频评论列表弹窗
     */
    private val commentDelegate by lazy {
        DetailCommentDelegate(mViewModel).apply {
            bind(this@NewPlayDetailActivity, null)
        }
    }

    //福利锚点
    private val welfareAnchorAdLoader by lazy {
        val welfareAdVo = mViewModel.mVideoInfo?.welfarePointAdVo
        LogUtil.d(DetailMC.AD_WELFARE_TAG, "welfareAdVo=${welfareAdVo}")
        if (welfareAdVo != null) {
            WelfareAnchorAdLoader(
                welfareAdVo,
                mViewModel.mVideoInfo?.chapterList?.size ?: 0,
                mViewModel
            )
        } else {
            null
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        // 添加耗时统计：统计从页面打开至视频起播各个阶段的时长
        // 有如下阶段：网络请求、UI渲染、视频起播
        TimeMonitorManager.getMonitor(MonitorMC.SCENE_DETAIL).startMonitor()
        TimeMonitorManager.getMonitor(MonitorMC.SCENE_DETAIL)
            .recordTime(MonitorMC.STAGE_START)
        super.onCreate(savedInstanceState)
        MonitorMS.get()?.startPerformanceMonitor("二级页onCreate")
    }

    var mScrollPointerId: Int = -1
    var mInitialTouchY = -1
    var mLastTouchY = -1
    var mScrollState = 0//滑动状态，0：未滑动，1：滑动
    var downX = 0f//当前item按下的X轴坐标
    var downY = 0f//当前item按下的Y轴坐标

    //广告自动跳过相关
    private var showingIsAd = false
    private var drawAdComplete = false

    //recyclerview的item触摸监听
    private val itemOnTouchListener = object : RecyclerView.OnItemTouchListener {
        override fun onInterceptTouchEvent(rv: RecyclerView, event: MotionEvent): Boolean {
            return interceptTouchEvent(event)
        }

        override fun onTouchEvent(rv: RecyclerView, event: MotionEvent) {
        }

        override fun onRequestDisallowInterceptTouchEvent(disallowIntercept: Boolean) {
        }
    }

    //framlayout的item触摸监听
    private val interceptTouchListener = object : OnItemTouchListener {
        override fun onInterceptTouchEvent(event: MotionEvent): Boolean {
            return interceptTouchEvent(event)
        }
    }

    private fun interceptTouchEvent(event: MotionEvent): Boolean {
        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                downX = event.x
                downY = event.y
            }
        }
        if (mDrawAdCanScroll) {
            return false
        } else {
            val holder = ViewPager2Helper.findViewHolderByPosition(
                mViewBinding.vp,
                mViewBinding.vp.currentItem
            )
            if (holder is NewDetailVideoViewHolder) {
                startScroll()
                mDrawAdCanScroll = true
                return false
            }
        }
        val actionIndex: Int = event.actionIndex
        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                mScrollPointerId = event.getPointerId(0)
                mInitialTouchY = (event.y + 0.5f).toInt().also { mLastTouchY = it }
            }

            MotionEvent.ACTION_MOVE -> {
                val index: Int = event.findPointerIndex(mScrollPointerId)
                if (index >= 0) {
                    val y: Int = (event.getY(index) + 0.5f).toInt()
                    if (mScrollState != 1) {
                        val dy: Int = y - mInitialTouchY
                        var startScroll = false
                        if (Math.abs(dy) > 8) {
                            mLastTouchY = y
                            startScroll = true
                        }
                        if (startScroll) {
                            mScrollState = 1
                            mInitialTouchY = y
                            ToastManager.showToast("倒计时结束后可滑动")
                            val holder = lastHolder
                            if (holder is AdVideoViewHolder) {
                                holder.forbidSlideByForceSeeTime = true
                                DrawAdManager.interactiveTrack(
                                    downX,
                                    downY,
                                    holder.itemView.width.coerceAtLeast(1),
                                    holder.itemView.height.coerceAtLeast(1),
                                    mViewModel.isLandMode(),
                                    holder,
                                )
                            }
                        }
                    }
                    val difY = abs(event.y - downY)
                    val difX = abs(event.x - downX)
                    if (!mDrawAdCanScroll && ((difY > mTouchSlop && difY > 0.4 * difX) || difY > 80)) {
                        mScrollState = 0
                        return true
                    }
                }
            }

            MotionEvent.ACTION_CANCEL, MotionEvent.ACTION_UP -> {
                mScrollState = 0
            }

            MotionEvent.ACTION_POINTER_UP -> {
                val actionIndex: Int = event.actionIndex
                if (event.getPointerId(actionIndex) == mScrollPointerId) {
                    val newIndex = if (actionIndex == 0) 1 else 0
                    mScrollPointerId = event.getPointerId(newIndex)
                    mLastTouchY = (event.getY(newIndex) + 0.5f).toInt()
                    mInitialTouchY = mLastTouchY
                }
            }

            MotionEvent.ACTION_POINTER_DOWN -> {
                mScrollPointerId = event.getPointerId(actionIndex)
                mInitialTouchY = (event.getY(actionIndex) + 0.5f).toInt().also {
                    mLastTouchY = it
                }
            }
        }
        return false
    }

    override fun initData() {
        //初始化路由中的一级、二级、三级来源
        mViewModel.routeIntent?.let { intent ->
            mViewModel.videoTrackInfo.apply {
                firstTierPlaySource = intent.firstTierPlaySource
                secondTierPlaySource = intent.secondTierPlaySource
                thirdTierPlaySource = intent.thirdTierPlaySource
                contentSource=intent.fromType
            }
        }
        DetailRecommendUtil.isInDetail = true
        mTouchSlop = ViewConfiguration.get(this).scaledTouchSlop
        mVideoLifecycle.addObserver(VideoPlayTimeManager.getVideoPlayTimeObserver())
        mVideoLifecycle.addObserver(RatingObserver(getUiId()))
        mAdLifecycle.initData()
        mViewModel.setAdLifeCycle(mAdLifecycle)
        initDanMuManager()  // 初始化弹幕
        FollowTipManager.reset()  // 初始化加追浮窗管理器

        //判断重建后报错
        if (mViewModel.saveRvAllCells != null) {
            mViewModel.mPosition = mViewModel.savePosition
            currentDuration = mViewModel.saveDuration
            mViewModel.routeIntent?.chapterIndex = mViewModel.saveChapterIndex
            mViewModel.routeIntent?.chapterId = mViewModel.saveChapterId
            mViewModel.routeIntent?.playPosition = currentDuration
            mViewModel.restoreSaveCells()
            mViewModel.cancelPause("welfare")
            mViewModel.cancelPause("push")
            mViewModel.keepPlayStatus = false
        }
        // 初始化分享。将分享的配置从kv中反序列号到内存中
        mViewModel.initShareData()
        mViewModel.routeIntent?.run {  // 解析传入的参数
            // 解析开始的剧集
//            if (type == 1) {
//                chapterIndex = chapterIndex?.plus(1)
//            }
            if (type == DetailMC.LAUNCH_TYPE_NEXT) { //播放下一集，当前集进度重置为0，避免划回上一级进度异常
                playPosition = 0
            }
            if (chapterIndex != null && mViewModel.currentChapterIndex == null) {
                // 判断当前从第几集开始播放
                mViewModel.currentChapterIndex = if (type == DetailMC.LAUNCH_TYPE_NEXT) {
                    chapterIndex!!.plus(1)  // 在推荐页点击下一集按钮进入二级页，要加1集
                } else {
                    chapterIndex!!
                }
            }

            mBookId = bookId
            mFirstPlaySource = firstPlaySource
            if (mViewModel.getHasDataFlag() && !isInPip()) {  // 页面已经有数据了，就不继续执行下面的处理了
                return
            }
            if (!kocChannelCode.isNullOrEmpty() && !bookId.isNullOrEmpty() && !columnName.isNullOrEmpty()) {
                mViewModel.kocAscribeRequest(kocChannelCode!!, bookId!!, columnName!!)
            } else {
                mViewModel.initVideoDetailInfo()  // 获取剧集信息。1131、本地下载信息等。
            }
            if (mViewModel.isFirstPlay) {
                recordUserSenseTime(0)
                // 耗时打点
                PlayerMonitorManager.getPlayerMonitor(DetailMC.DETAIL_API_TRACK_TAG)
                    .recordTime(PlayerMonitorManager.TAG_REQUEST_TIME_START)
                // 请求1139接口，开始解锁，去获取播放链接
                LogUtil.d(DetailMC.PLAYER_START_PLAY_TIME_TAG, "开始请求1131接口")
            }
            mViewModel.mBackToRecommend = backToRecommend
            mViewModel.showActor = showActor  // 是否显示演员信息
            mViewModel.mOmap = cOmap ?: GsonUtil.fromJson(omap, StrategyInfo::class.java)
            if (cOmap != null) omap = GsonUtil.toJson(cOmap)
        }
        setActivityTitle("二级播放器")  // 设置页面标题，神策打点会用到。
        DrawAdTimeManager.register(lifecycle)
        DrawAdManager.registerBackgroundListener()
        DrawAdManager.onVideoComplete = {
            if (mViewModel.canAutoJumpDrawAd()) {
                drawAdComplete = true
                LogUtil.d(DetailMC.AUTO_JUMP_AD_TAG, "沉浸式广告播放结束，自动滑动到下一集")
                mViewModel.autoJumpDrawAdCountAdd1()
                downX = 0F
                downY = 0F
                if (mDrawAdCanScroll) {
                    selectChapter(mCurrentPosition + 1, VideoMC.PLAY_END_AUTO_SWAP, true)
                }
            } else {
                LogUtil.d(DetailMC.AUTO_JUMP_AD_TAG, "沉浸式广告播放结束，不可以自动滑动到下一集")
            }
        }
        super.initData()
    }

    override fun getViewModel(): VideoListVM = mViewModel

    override fun getWelfarePendant(): View? = welfareWidget

    private var inputBoxShowing = false

    /**
     * 初始化弹幕
     */
    private fun initDanMuManager() {
        val danMuManager = VideoDanMuManager()
        danMuManager.init(getUiId())
        mVideoLifecycle.addObserver(danMuManager.videoLifecycleObserver)
        danMuManager.setPlaySpeed(mViewModel.playSpeed)
        danMuManager.setActionCallback(object : VideoDanMuManager.DanMuActionCallback {
            override fun onInputBoxDismiss() {
                // 输入框、键盘关闭后，要恢复播放
                inputBoxShowing = false
                mViewModel.cancelPause(VideoMC.PLAY_END_SEND_DANMU)
            }

            override fun onInputBoxShow() {
                inputBoxShowing = true
                if (!layerPauseStatus()) {
                    mViewModel.pausePlay(VideoMC.PLAY_END_SEND_DANMU)
                }
            }
        })
        videoDanMuManger = danMuManager
    }

    override fun initView() {
        // 获取状态栏的高度，动态调整返回按钮的位置
        val lp = mViewBinding.layerBack.getPortBackView().layoutParams
        if (lp is ViewGroup.MarginLayoutParams) {
            lp.topMargin = ScreenUtil.getStatusHeight(this)
        }
        mViewBinding.layerBack.getPortBackView().layoutParams = lp
        //初始化底部banner高度、播放器适配模式
        initBannerAndPlayerView()
        mPlayerController.initPlayerConfig(
            resources.configuration.orientation == Configuration.ORIENTATION_PORTRAIT,
            PrerenderConfig(CommInfoUtil.isPlayerPreload(), BBaseMC.PLAYER_PRELOAD_BUFFER_B, getUiId()),
            this.lifecycle
        )
        //初始化滑动ViewPager2
        initViewPager2()
        //初始化屏幕旋转监听
        orientationManager.initOrientationWatchdog()
        if (!BBaseKV.testRecording) {
            ScreenUtil.prohibitScreenRecording(window)
        }
        //防止边界误触
        mViewBinding.topIntercept.registerClickAction {}
        mViewBinding.rightIntercept.registerClickAction {}
        try {
            // 保存原始的像素密度
            // onCreate会触发AutoSize改动像素密度。
            // 但广告需要原始的像素密度，所以这里要保存原始的密度。
            if (BBaseMC.density == 0f) {
                BBaseMC.density = resources.displayMetrics.density
            }
        } catch (e: Exception) {
            // 异常上报
            CrashUtils.reportError("播放二级页错误", e)
        }
        logPlayerConfig()

        // 小窗下要隐藏的View
        pipViews.apply {
            add(mViewBinding.clAdBottom)
            add(mViewBinding.clFinalChapter1)
            add(mViewBinding.clFinalChapter2)
            add(mViewBinding.clFinalChapter3)
            add(mViewBinding.ratingComp)
            add(mViewBinding.comPauseAd)
            add(mViewBinding.compLoading)
        }
        globalLayers.apply {
            add(mViewBinding.layerBack)
            add(mViewBinding.layerGuide)
            add(mViewBinding.bottomLayer)
        }
        mViewModel.eventListeners.add(this)
    }

    private fun logPlayerConfig() {
        kotlin.runCatching {
            LogUtil.d(
                "player_config_detail",
                "当前为：${CommInfoUtil.multicastDesc()} " +
                        "\n使用的：${if (BBaseKV.multipleInstancesTypeTest == -1) "神策配置" else "本地配置"}" +
                        "\n设备评分开关为：${if (CommInfoUtil.scoreSwitch()) "开启" else "关闭"}" +
                        "\n设备评分为：${BBaseKV.equipmentScore}" +
                        "\n预加载开关为：${if (CommInfoUtil.isPlayerPreload()) "开启" else "关闭"}"
            )
        }
    }

    private var playerHeight = 0
    private var playerWidth = 0
    //初始化底部banner高度、播放器适配模式
    private fun initBannerAndPlayerView() {
        // 初始化底部banner高度
        val bannerHeight = getBannerHeight()
        val layoutParams = mViewBinding.clAdBottom.layoutParams
        layoutParams.height = bannerHeight
        mViewBinding.clAdBottom.layoutParams = layoutParams
        // 根据播放器的显示区域，设置缩放模式
        mViewBinding.vp.post {
            val ratio =
                (mViewBinding.vp.measuredHeight.toFloat() - bannerHeight) / mViewBinding.vp.measuredWidth.toFloat()
//            BBaseKV.playerDetailHeightWidthRatio = ratio
            playerWidth = mViewBinding.vp.measuredWidth
            playerHeight = mViewBinding.vp.measuredHeight - bannerHeight
            ///TODO:播放器创建时进行播放器的适配模式填写
//            LogUtil.d(
//                TAG_PLAYER,
//                "二级页播放器高度：${mViewBinding.vp.measuredHeight}，宽度：${mViewBinding.vp.measuredWidth}"
//            )
//            LogUtil.d(TAG_PLAYER, "二级页播放器宽高比：${ratio * 9} : 9")
//            try {
//                mListPlayerView.updateScaleMode(ratio, mViewModel.isLandscapeVideo)
//            } catch (e: Exception) {
//                e.printStackTrace()
//                LogUtil.e(TAG_PLAYER, "二级页更新播放器适配方式出现异常：${e.message}")
//            }
        }
    }

    /**
     * 获取底部banner高度
     * @return Int
     */
    private fun getBannerHeight() = ScreenUtil.dip2px(this, BannerAdKV.bannerHeight)

    override fun initStatusComponent(): StatusComponent {
        return super.initStatusComponent()
            .background(R.color.common_transparent)//设置状态组件背景
    }

    /**
     * 是否循环播放
     */
    private var mIsLoopPlay = false


    @SuppressLint("ClickableViewAccessibility")
    override fun initListener() {
        mViewBinding.layerBack.listener = object : BackLayer.Listener {
            override fun onBackClick(view: View) {
                // 左上角的返回按钮被点击
                DzTrackEvents.get().buttonClick()
                    .buttonName("二级播放器返回")
                    .title(getPageName())
                    .bookId(mViewModel.mVideoInfo?.videoInfo?.bookId)
                    .bookName(mViewModel.mVideoInfo?.videoInfo?.bookName)
                    .chapterId(mViewModel.mChapterInfoVo?.chapterId)
                    .chapterName(mViewModel.mChapterInfoVo?.chapterName)
                    .track()
                // 调用返回按钮方法
                onBackPressAction()
            }

            override fun onDramaClick(view: View) {
                //点击选集按钮 隐藏菜单提示
                mViewBinding.comVipTips.hideMenuTips()
                startDramaListDialog()
            }

            override fun onSpeedClick(view: View) {
                // 点击倍速按钮
                startDoubleSpeedDialog()  // 展示弹窗
                // 打点上报
                ElementClickUtils.trackViewAppClick(
                    view,
                    bookID = mViewModel.mVideoInfo?.videoInfo?.bookId,
                    bookName = mViewModel.mVideoInfo?.videoInfo?.bookName,
                    chaptersNum = mViewModel.mChapterInfoVo?.chapterIndex,
                    chaptersId = mViewModel.mChapterInfoVo?.chapterId,
                    content = "倍速入口",
                    elementType = "倍速入口",
                )
            }

            override fun onMenuClick(view: View) {  // 竖屏模式中右上角三个点被点击
                // 打点
                DzTrackEvents.get().buttonClick()
                    .buttonName("二级播放页多选按钮")
                    .track()
                //点击菜单按钮 隐藏菜单提示
                BBaseKV.isClickMenu = true
                mViewBinding.comVipTips.hideMenuTips()
                // 弹窗菜单弹窗
                startMenuDialog()
            }

            override fun currentIsVideo(): Boolean {
                val holder =
                    ViewPager2Helper.findViewHolderByPosition(mViewBinding.vp, mCurrentPosition)
                return holder == null || holder is NewDetailVideoViewHolder
            }

            override fun isSharing(): Boolean = isSharing

            override fun getBottomStyle(): BottomStyle? {
                return mViewModel.bottomStyle
            }

            override fun isPipMode(): Boolean = isInPip()
            override fun keepImmersive(): Boolean = mViewModel.keepImmersive
            override fun isPreviewChapter(): Boolean? = mViewModel.mChapterInfoVo?.isPreviewChapter()
        }
        mViewBinding.bottomLayer.listener = object : BottomLayer.Listener {
            override fun getBottomStyle(): BottomStyle? {
                return mViewModel.bottomStyle
            }

            override fun onBottomClick(view: View) {
                //点击选集 隐藏菜单提示
                mViewBinding.comVipTips.hideMenuTips()
                if (!isLandScape() && VideoKV.introRecSwitch) {
                    DzTrackEvents.get().collectedWorksClick()
                        .title(PageConstant.PAGE_INTRO_NAME)
                        .positionName(PageConstant.PAGE_INTRO_DRAMA_NAME)
                        .recoButtonStatus(BBaseKV.recommendContent)
                        .addParam("\$title", PageConstant.PAGE_INTRO_NAME)
                        .addParam("\$referrer_title", "二级播放页")
                        .track()
                    showIntroDialog(PageConstant.PAGE_INTRO_DRAMA_ID)
                } else {
                    startDramaListDialog()
                }
            }

            override fun isSharing(): Boolean = isSharing

            override fun isPipMode(): Boolean = isInPip()
            override fun keepImmersive(): Boolean = mViewModel.keepImmersive
            override fun isPreviewChapter(): Boolean? = mViewModel.mChapterInfoVo?.isPreviewChapter()
        }
        mViewBinding.layerGuide.listener = object : GuideLayer.Listener {
            override fun isSharing(): Boolean = isSharing
            override fun getBottomStyle(): BottomStyle? = mViewModel.bottomStyle
            override fun isPipMode(): Boolean = isInPip()
            override fun keepImmersive(): Boolean = mViewModel.keepImmersive
            override fun isPreviewChapter(): Boolean? = mViewModel.mChapterInfoVo?.isPreviewChapter()
        }
        mViewBinding.clFinalChapter1.registerClickAction { }
        mViewModel.setEventCallback(this, object : RequestEventCallback {
            override fun onRequestStart(hasData: Boolean) {
                statusDismiss()
                mViewBinding.compLoading.show(isLandscape = isLandScape())
            }

            override fun onResponse() {
                statusDismiss()
            }

            override fun onRequestError(e: RequestException, hasData: Boolean) {
                statusDismiss()
                if (downLoadUrlCanPlay(mViewModel.mChapterInfoVo)) {//请求异常，但是当前集正在播放下载的视频，不能显示loading，否则会影响用户看剧
                } else {
                    statusRefresh {
                        mViewModel.getVideoDetailInfo()
                        if (mViewModel.isFirstPlay) {
//                            recordUserSenseTime(0)
                            // 耗时打点
                            PlayerMonitorManager.getPlayerMonitor(DetailMC.DETAIL_API_TRACK_TAG)
                                .recordTime(PlayerMonitorManager.TAG_REQUEST_TIME_START)
                            // 请求1139接口，开始解锁，去获取播放链接
                            LogUtil.d(DetailMC.PLAYER_START_PLAY_TIME_TAG, "开始请求1131接口")
                        }
                    }
                }
            }
        })

        mViewBinding.comPauseAd.setActionListener(object : PauseAdComp.ViewActionListener {

            override fun onClose(resumePlay: Boolean, notifySdkAdClose: Boolean) {
                cancelPauseAd(resumePlay, notifySdkAdClose)
            }
        })
        AppModule.getApplication().registerComponentCallbacks(configChangedCallback)

        FollowTipManager.registerFollowTipListener(this)

        AppActiveManager.addAppActiveListener(AppActiveManager.TAG_APP, this)


        val view = mViewBinding.vp.getChildAt(0)
        if (view is RecyclerView) {
            view.addOnItemTouchListener(itemOnTouchListener)
        }
        mViewBinding.clRoot.addOnItemTouchListener(interceptTouchListener)
        mOnPageChangeCallbackCompat = object : OnPageChangeCallbackCompat(mViewBinding.vp) {
            override fun onPageRelease(
                isNext: Boolean,
                position: Int,
                currentPosition: Int,
                view: View?
            ): Boolean {
                super.onPageRelease(isNext, position, currentPosition, view)
                LogUtil.d(DetailMC.DETAIL_VP_TAG,"onPageRelease    position==$position   currentPosition==$currentPosition")
                LogUtil.d(NewPlayDetailActivity.TAG, "切换页面 new mViewModel.mChapterInfoVo?.isCharge = ${mViewModel.getChapterInfoVo(currentPosition)?.isCharge}")
                mViewModel.clearPause()
                if((mViewModel.getChapterInfoVo(currentPosition)?.isCharge == 1 || mViewModel.getChapterInfoVo(currentPosition)?.isCharge == 4)  && mViewModel.lockTimes >= 0 && (mViewModel.scene == 2 || mViewModel.scene == 3))  //用户在本次二级页生命周期内进入解锁页
                {
                    if(!firstLock){
                        BBaseKV.lockTimes ++
                        firstLock = true
                    }
                    LogUtil.d(
                        OperationMC.TAG_PENDANT,
                        "需要的解锁页次数 BBaseKV.lockTimes = ${BBaseKV.lockTimes} "
                    )
                }
                // 页面离开了屏幕的回调
                val curHolder =
                    ViewPager2Helper.findViewHolderByPosition(mViewBinding.vp, currentPosition)
                if (curHolder is BaseViewHolder<*, *> && curHolder.mData == currentHolder?.mData && curHolder == currentHolder) {
                    //列表动态刷新造成的重新选中，只需要将position重置即可
                    LogUtil.d(DetailMC.DETAIL_VP_TAG,"onPageRelease    重新选中     ${mViewModel.getChapterInfoVo(currentPosition)?.chapterName}     ${currentHolder?.mData?.chapterName}")
                    mCurrentPosition = currentPosition
                    mViewModel.mPosition = currentPosition
                    resetLayerPosition(currentPosition)
                    return false
                }
                chapterSwitching = true
                onPlayStop()
                activePause = false
                // 页面离开了屏幕的回调
                LogUtil.d(TAG_PLAYER, "页面被划走 onPageRelease position:$position")
                if (mCurrentPosition == position) {
                    LogUtil.d(DetailMC.DETAIL_VP_TAG,"onPageRelease    页面离开")
                    view?.let { holderView ->
                        val holder =
                            ViewPager2Helper.findViewHolderByView(mViewBinding.vp, holderView)
                        if (holder is NewDetailVideoViewHolder) {//当前是视频holder，则记录停止的position，并打点停止播放、解绑control
                            mLastStopPosition = position
                            holder.onPageRelease()
                            //播放完成会上报打点，这里不做重复上报
                            if (!isPlayCompletion && isPlaying) {
                                track(1, triggerScenario = VideoMC.PLAY_END_MANUAL_SWAP)
                            }
                            //关闭福利锚点广告
                            tryCloseWelfareAnchorAd(true, "切集")
                            unbindViewHolder()
                        } else if (holder is AdVideoViewHolder) {
                            // 打点：广告被划走
                            DrawAdManager.interactiveTrack(
                                downX,
                                downY,
                                holderView.width,
                                holderView.height,
                                mViewModel.isLandMode(),
                                holder,
                            )
                        }
                    } ?: let {
                        mLastStopPosition = position
                        currentHolder?.onPageRelease()
                        //播放完成会上报打点，这里不做重复上报
                        if (!isPlayCompletion && isPlaying) {
                            track(1, triggerScenario = "208")
                        }
                        unbindViewHolder()
                    }
                    // 预加载沉浸式广告
                    mViewModel.preloadDrawAd(position, isNext, this@NewPlayDetailActivity)
                } else {
                    LogUtil.d(
                        DetailMC.DETAIL_VP_TAG,
                        "onPageRelease    mCurrentPosition==$mCurrentPosition   position==$position   mViewBinding.vp.position==${mViewBinding.vp.currentItem}"
                    )
                    view?.let { holderView ->
                        val holder =
                            ViewPager2Helper.findViewHolderByView(mViewBinding.vp, holderView)
                        if (holder is NewDetailVideoViewHolder) {//当前是视频holder，则记录停止的position，并打点停止播放、解绑control
                            mLastStopPosition = position
                            holder.onPageRelease()
                            //播放完成会上报打点，这里不做重复上报
                            if (!isPlayCompletion && isPlaying) {
                                track(1, triggerScenario = "207")
                            }
                            unbindViewHolder()
                        } else if (holder is AdVideoViewHolder) {  // 是广告Holder且广告内容是视频
                            // 打点：视频广告被划走
                            DrawAdManager.interactiveTrack(
                                downX,
                                downY,
                                holderView.width,
                                holderView.height,
                                mViewModel.isLandMode(),
                                holder,
                            )
                        }
                    } ?: let {
                        mLastStopPosition = position
                        currentHolder?.onPageRelease()
                        //播放完成会上报打点，这里不做重复上报
                        if (!isPlayCompletion && isPlaying) {
                            track(1, triggerScenario = "206")
                        }
                        unbindViewHolder()
                    }
                    // 预加载沉浸式广告
                    mViewModel.preloadDrawAd(position, isNext, this@NewPlayDetailActivity)
                }

                // 未解锁剧集要隐藏挂件
                if (canShowPendantVisible()) {  // 判断关键是否可以展示
                    LogUtil.d(OperationMC.TAG_PENDANT, "ad holder release show pendant")
                    welfareWidget?.visibility = VISIBLE  // 线是否福利挂件
                }
                if (mViewModel.getPreviewConfig() != null) {
                    // 如果是预览集，且当前页面被划走了，那么需要取消预览
                    mViewModel.removePauseModel(VideoMC.PLAY_END_PREVIEW_FINISH)
                    mViewModel.removePauseModel(VideoMC.PLAY_END_PREVIEW_PEEK)
                    previewNotAllowPause = false
                }

                return super.onPageRelease(isNext, position, currentPosition, view)
            }

            override fun onPageLoadMore(pager: ViewPager2, position: Int) {
                super.onPageScrollStateChanged(pager, position)
                // 二级页的数据是一次性全部加到列表中的。当回调到此方法时，说明已经滑动到最底部了。
                // 就需要出章末推荐、剧末承接等内容了。
                LogUtil.d(TAG, "onPageLoadMore 章末推荐/剧末承接")
                // 如果当前正在播放，那么需要暂停播放
                if (isPlaying) {
                    // 执行暂停操作
                    // 触发打点（神策和大数据）。传入值1代表视频停止播放
                    track(1, triggerScenario = "205")
                    stopPlay()
                }

                // 让播放器回到开始位置
                showFinalChapterPrompt(mViewModel.recommendLiveData.value)

            }

            override fun onPageSelected(pager: ViewPager2, position: Int, forceSelected: Boolean) {
                super.onPageSelected(pager, position, forceSelected)
                <EMAIL>(position, forceSelected)
            }

            override fun onPageScrollStateChanged(pager: ViewPager2, state: Int) {
                super.onPageScrollStateChanged(pager, state)
                vpState = state
                temporaryDataList?.let { dataList ->
                    if (vpState == ViewPager2.SCROLL_STATE_IDLE) {
                        mViewBinding.vp.post { updateItemsAsync(dataList) }
                    }
                }
            }
            override fun onPagePeekStart(pager: ViewPager2, position: Int, peekPosition: Int) {
                super.onPagePeekStart(pager, position, peekPosition)
                slideDirection = if (peekPosition > position) {
                    0
                } else {
                    1
                }
                onPagePeekStart(peekPosition)
                if (mViewModel.mChapterInfoVo?.isPreviewChapter() == true) {
                    // 如果当前是试看剧集，拖动时需要暂停
                    mViewModel.pausePlay(VideoMC.PLAY_END_PREVIEW_PEEK)
                }
            }

            override fun onPagePeekEnd(pager: ViewPager2, position: Int, peekPosition: Int) {
                super.onPagePeekEnd(pager, position, peekPosition)
                if (mViewModel.mChapterInfoVo?.isPreviewChapter() == true) {
                    // 如果当前是试看剧集，拖动时需要暂停
                    mViewModel.cancelPause(VideoMC.PLAY_END_PREVIEW_PEEK)
                }
            }

            override fun onPageScrollStateChanged(state: Int) {
                super.onPageScrollStateChanged(state)
                //禁止沉浸式广告滑动
                stopDrawAdScroll(state)
            }
        }
        mOnPageChangeCallbackCompat?.let {
            mViewBinding.vp.registerOnPageChangeCallback(it)
        }
    }

    /**
     * 滑动开始
     */
    private fun onPagePeekStart(peekPosition: Int) {
//        if (BBaseKV.realTimeRender && BBaseKV.playerPrerender && BBaseKV.playerPrerenderTest) {//开启播放器实时预渲染
//            val holder =
//                ViewPager2Helper.findViewHolderByPosition(mViewBinding.vp, peekPosition)
//            if (holder is NewDetailVideoViewHolder) {
//                holder.preRenderVideo(
//                    mPlayerController.getPlayer(
//                        holder.mData?.chapterIndex ?: 0,
//                        false,
//                        holder.mData?.chapterId
//                    ),
//                    peekPosition
//                )
//            }
//        }
    }

    /**
     * 禁止沉浸式广告滑动
     */
    private fun stopDrawAdScroll(state: Int) {
        if (state == ViewPager2.SCROLL_STATE_IDLE) {
            val holder = ViewPager2Helper.findViewHolderByPosition(
                mViewBinding.vp,
                mViewBinding.vp.currentItem
            )
            if (holder is AdVideoViewHolder) {
                // 是否需要强制观看
                val checkForceShow = holder.checkForceShow()
                // 是否需要禁止滑动
                val needLockScroll = checkForceShow && holder.hasAdView()  // 在广告Holder && 还在强制观看时间内
                if (needLockScroll) {
                    stopScroll()  // 停止滑动
                }
            }
        }
    }

    private val configChangedCallback = object : ComponentCallbacks {
        override fun onConfigurationChanged(newConfig: Configuration) {
            resetAutoSize(newConfig)  // 重置AutoSize的配置
        }

        override fun onLowMemory() {

        }
    }

    override fun initImmersionBar() {
        if (isLandScape()) {  // 横屏下隐藏沉浸式状态栏
            enterLandImmersive()
        } else {
            // 竖屏显示状态栏
            getImmersionBar().transparentStatusBar().navigationBarColor(R.color.common_FF0F0F0F)
//            .navigationBarColor(R.color.common_card_FFFFFFFF)
                .navigationBarDarkIcon(DeviceInfoUtil.isDarkTheme(this))  // 根据是否是深色模式来显示状态栏文字颜色
                .statusBarDarkFont(DeviceInfoUtil.isDarkTheme(this))
                .hideBar(BarHide.FLAG_SHOW_BAR)  // 显示状态栏
                .init()
        }
    }

    @SuppressLint("SetTextI18n")
    override fun subscribeObserver(lifecycleOwner: LifecycleOwner) {
        mViewModel.loadCommLiveData.observe(lifecycleOwner) {
            loadResultPresenter.handlerResult(it)
        }

        //刷新预加载配置
        mViewModel.preLoadLiveData.observe(lifecycleOwner) {
            updatePlayerPreLoad(it)
        }

        mViewModel.videoListLiveData.observe(lifecycleOwner) {
            // 所有的章节信息获取成功
            LogUtil.d("interval_chapter_detail", "监听到videoListLiveData发生变化")
            mViewModel.setHasInsertAd(it)
            // 返回按钮显示当前剧集信息
            mViewBinding.layerBack.bindData(mViewModel.mVideoInfo?.videoInfo)
            // 底通选集UI是否展示？
            mViewBinding.bottomLayer.bindData(mViewModel.mVideoInfo?.videoInfo)
            // 打点，记录开始渲染时间
            TimeMonitorManager.getMonitor(MonitorMC.SCENE_DETAIL)
                .recordTime(MonitorMC.STAGE_RENDER_START)
            if (!mViewModel.keepPlayStatus) {
                stopPlay()
                lifecycleScope.launch {  // 延迟5s后开始加载沉浸式广告
                    delay(5000)
                    initDrawAd()  // 加载沉浸式广告
                    delay(5000)
                    preLoadRewardAd() //预加载免广激励
                    delay(5000)
                    initWelfareAnchorAd()
                }
                // 这里面处理的东西还挺多。具体到方法里去看吧。
                // 主要是拿到了所有剧集信息，然后将数据更新到列表中。
                refreshVideoList(it)
                // 更新底部广告占位区域大小
                currentHolder?.onScaleModeChanged()
                LogUtil.d(DetailMC.START_PLAY_TAG, "createVideoList")
                LogUtil.d(
                    DetailMC.PLAYER_DOWNLOAD_TAG,
                    " 创建列表   mCurrentPosition--$mCurrentPosition "
                )
            } else {
                val isAd = isAd(mCurrentPosition)  // 当前是广告Holder吗？
                LogUtil.i(
                    "interval_chapter_detail",
                    "isAd:${isAd} mCurrentPosition：$mCurrentPosition viewModelPosition:${mViewModel.mPosition} vipStatus:${BBaseKV.vipStatus} freeDrawAd:${BBaseKV.freeDrawAd}"
                )
                //防止退出后台之后，再次调用start方法，导致视频播放
                if (mCurrentPlayPosition == mCurrentPosition) {
                    mCurrentPlayPosition = mViewModel.mPosition
                }
                mCurrentPosition = mViewModel.mPosition
                mViewModel.keepPlayStatus = false
                val newSize = it.size
                val oldSize = mPageAdapter.itemCount
                if (newSize < oldSize && isAd) { // 这里更准确的判断方式是:当前是广告 && 因为包括免广，开通VIP等原因要删除掉当前的广告，所以要调到下一集
                    LogUtil.i(
                        "interval_chapter_detail",
                        "检测到免广告了，且当前是广告，播放广告后的下一剧集 mCurrentPosition=${mCurrentPosition}"
                    )
                    refreshVideoList(it, true)
                } else {
                    updateItemsAsync(it)
                    LogUtil.d(
                        "interval_chapter_detail",
                        "addOrRemoveCells oldSize:$oldSize newSize:$newSize isAd:$isAd"
                    )
                }

                LogUtil.d(
                    DetailMC.PLAYER_DOWNLOAD_TAG,
                    "刷新列表 mCurrentPosition--$mCurrentPosition "
                )
            }
            // 初始化屏幕方向监听器
            initAngleListener()
            welfareAnchorAdLoader?.initAdShowIndices()
//            if (mCurrentPosition >= 0 && mCurrentPosition < mViewModel.getDataList().size) {
//                if (mViewModel.getDataList()[mCurrentPosition].m3u8720pUrl.isNullOrEmpty()) {
//                    mViewModel.getDataList()[mCurrentPosition].chapterId?.let {
//                        LogUtil.d(DetailMC.PLAYER_DOWNLOAD_TAG, "列表创建或刷新后，当前集没有播放链接，解锁当前集")
//                        mViewModel.unlockChapter(it, false, currentChapterId = it)
//                        return@observe
//                    }
//                }
//            }
//            mViewModel.preLoadChapters(mCurrentPosition)
        }
        mViewModel.unlockChapterLiveData.observe(lifecycleOwner) {
            // 接口请求解锁信息后的回调
            it.handlerUnlock()
        }
        mViewModel.favoriteLiveData.observe(lifecycleOwner) {
            mViewModel.mVideoInfo?.videoInfo?.run {
                inBookShelf = it
                if (inBookShelf == true) {
                    //二级播放页只要收藏了就不再显示追剧浮窗/弹窗
                    FollowTipManager.markFollowTipHasShown(true)

                    TaskManager.delayTask(1000L) {
                        ToastManager.showToast(R.string.bbase_add_favorite_hint)
                    }
                    mViewBinding.vp.postDelayed({
                        // 当用户点击追剧后，要弹出提醒用户打开消息推送开关的弹窗
                        BCommonMS.get()?.openPush(
                            BCommonMC.PUSH_SHOW_LOCATION_DETAIL,
                            BCommonMC.PUSH_SHOW_TYPE_COLLECT,
                            true
                        )
                    }, 1500)

                    //收藏成功的动画,通过浮窗收藏的，要等浮窗动画结束后再播放收藏动画
                    if (DetailMC.followSource == FollowSourceType.FOLLOW_TIP) {
                        TaskManager.delayTask(DetailMC.FOLLOW_TIP_EXIT_ANIMATION_DURATION) {
                            sendDetailViewHolderEvent(DetailMC.EVENT_HOLDER_PLAY_FAVORITE_ANIMATION)
                        }
                    } else {
                        sendDetailViewHolderEvent(DetailMC.EVENT_HOLDER_PLAY_FAVORITE_ANIMATION)
                    }
                }
                // 使用PlayLoad的方式通知holder局部刷新。刷新在追状态。
                mPageAdapter.notifyItemRangeChanged(
                    0, mPageAdapter.itemCount, DetailMC.PAYLOAD_FAVORITE
                )
            }
            // 发通知，所有收藏相关的位置都要刷新
            HomeME.get().refreshFavorite().post(null)
        }
        //点赞与取消点赞接口请求成功，数据回调
        mViewModel.likesLiveData.observe(lifecycleOwner) {
            if (it?.status == 1) {//请求成功
                val likesKey =
                    mViewModel.mVideoInfo?.videoInfo?.bookId + "_" + mViewModel.mChapterInfoVo?.chapterId
                val isLikeTo = (currentHolder?.isLikes() != true)
                val likesNum = mViewModel.mChapterInfoVo?.getRealLikesNum(if (isLikeTo) 1 else -1)

                mViewModel.mChapterInfoVo?.isLiked = isLikeTo
                LogUtil.d(
                    "likes_Status",
                    "详情页面，删除点赞：key==$likesKey,,,isLiked==$isLikeTo,,,likesNum==$likesNum"
                )
                LogUtil.d(BBaseMC.TAG_LIKES, "点赞与取消点赞接口请求成功==$isLikeTo")
                currentHolder?.layerFunction?.likesStatus(isLikeTo, likesNum)
                HomeME.get().addLikesSuccess().post(
                    LikesInfo(
                        likesKey, isLikeTo, likesNum, mViewModel.mChapterInfoVo?.likesNumActual ?: 0
                    )
                )
                mViewModel.updateLikes(isLikeTo, likesKey)
                mViewModel.mVideoInfo?.userVideoVo?.apply {
                    if (isLikeTo) {
                        chapterLikesNum++
                    } else {
                        chapterLikesNum--
                    }
                }
                if (isLikeTo) {//当前是已点赞状态，则表示本次操作取消点赞成功
                    // 打开push弹窗，引导用户打开系统通知开关
                    BCommonMS.get()?.openPush(
                        BCommonMC.PUSH_SHOW_LOCATION_DETAIL, BCommonMC.PUSH_SHOW_TYPE_LIKE, true
                    )
                }
                // 点赞后要通知Flutter，Flutter里要根据点赞状态变化做出相应处理。
                FlutterMS.get()?.sendEventToFlutter(
                    "theaterLikeAction",
                    mapOf(
                        "value" to isLikeTo,
                        "bookId" to mViewModel.mVideoInfo?.videoInfo?.bookId,
                        "chapterId" to mViewModel.mChapterInfoVo?.chapterId
                    )
                )
            } else {//请求失败

            }
        }

        mViewModel.errorCode.observe(lifecycleOwner) {  // 监听一些错误情况
            when (it) {
                VideoListVM.ERROR_VIDEO_OFF_SHELF -> {  // 下架
                    mViewModel.routeIntent?.bookId?.let { bookId ->
                        TaskManager.ioTask {
                            val result = DBHelper.deleteBookById(bookId)  // 删除观看历史里保存的内容
                        }
                    }
                    // 展示Toast，提示用户
                    ToastManager.showToast(mViewModel.errorMsg)
                    // 剧已下架，延迟1.5秒后关闭Activity
                    getContentView().postDelayed({
                        finish()
                    }, 1500) // 保持Toast消失后再finish
                }

                VideoListVM.ERROR_VIDEO_LOAD_FAILED -> {
                    // 展示Toast，提示用户
                    ToastManager.showToast(mViewModel.errorMsg)
                    getContentView().postDelayed({
                        finish()
                    }, 1500) // 保持Toast消失后再finish
                }

                VideoListVM.ERROR_UNLOCK_FAILED -> {  // 解锁失败
                    ToastManager.showToast(mViewModel.errorMsg)
                }
            }
        }

        mViewModel.videoInfoLiveData.observe(lifecycleOwner) {  // 获取剧集信息成功
//            detailDelegate = VideoDetailDelegate(this, mViewModel)
//            detailDelegate!!.bind(this, null)
            // 将播放器代理绑定到当前Activity
            detailDelegate?.run {
                if (!hasBind) {
                    bind(this@NewPlayDetailActivity, null)
                }
            } ?: let {
                // 重新创建播放器代理类
                detailDelegate = VideoDetailDelegate(this, mViewModel)
                detailDelegate!!.bind(this, null)
            }
            // 创建引导代理类，并绑定到当前Activity
            guideDelegate?.run {
                if (!hasBind) {
                    // 绑定Activity的生命周期
                    bind(this@NewPlayDetailActivity, null)
                }
            } ?: let {
                guideDelegate = GuideDelegate(mViewBinding.layerGuide, mViewModel).apply {
                    // 绑定Activity的生命周期
                    bind(this@NewPlayDetailActivity, null)
                }
            }
            mPageAdapter.notifyItemRangeChanged(0, mPageAdapter.itemCount, DetailMC.PAYLOAD_SHARE)
            lifecycleScope.launch(Dispatchers.Main) {
                // 延迟5秒，加载底部banner广告
                delay(5000)
                bannerAdPresenter?.tryLoadBannerAd(it)
            }
            it.operlocationConf?.apply {
                LogUtil.d(OperationMC.TAG, "二级页配置信息获取成功")
                // 保存挂件配置信息
                OperationMS.get()?.savePendantConfig(WelfareMC.POSITION_PLAYER, playerPendant)
//                OperationMS.get()
//                    ?.savePopupConfig(OperationMC.POSITION_PLAYER_DETAIL, quitPlayPagePopVo)
                playerPendant?.let { config ->
                    // 触发挂件曝光
                    onPendantConfigSuccess(config)
                }
            } ?: let {
                OperationMS.get()?.savePendantConfig(WelfareMC.POSITION_PLAYER, null)
                OperationMS.get()?.savePopupConfig(OperationMC.POSITION_PLAYER_DETAIL, null)
            }
        }

        mViewModel.playing.observe(lifecycleOwner) { playing ->
            // 控制播放器播放暂停
            if (playing) {
                resumePlay()  // 恢复播放
            } else {
                pausePlay("")  // 暂停播放
            }
        }
        mViewModel.recommendLiveData.observe(lifecycleOwner) { data ->
            if (mViewModel.isLandMode()) {
                data?.isLandScape = true
            }
            if (isInPip()) return@observe  // 小窗下不处理。由TailRcmdDelegate处理。
        }
        mViewModel.toastCommLiveData.observe(lifecycleOwner) { data ->
            mViewBinding.vp.postDelayed({
                ToastManager.showToast(
                    ViewUtils.highLightText(
                        data.text,
                        data.hotWords,
                        ContextCompat.getColor(this, R.color.common_FFFB8E27)
                    )
                )
            }, 200)
            mViewModel.adLoading = false
        }
        mViewModel.playMode.observe(lifecycleOwner) { mode ->
            kotlin.runCatching {
                if (mViewModel.previousPlayMode == PlayMode.PIP && mode == PlayMode.NORMAL) {
                    TaskManager.delayTask(80) {
                        onPlayModeChanged(mode)
                    }
                } else {
                    onPlayModeChanged(mode)
                }
            }
        }

        mViewModel.pauseAdCommLiveData.observe(lifecycleOwner) {
            // TODO: 为什么这里要切main，而不是直接通过liveData.post来切主线程？
            lifecycleScope.launch(Dispatchers.Main) {
                // 延迟5秒加载暂停广告
                delay(5000)
                loadPauseAd()
            }
        }

        mViewModel.orientation.observe(lifecycleOwner) {
            // 剧的方向发生改变
            // 通知返回按钮
            mViewBinding.layerBack.orientationChanged(it)
            mViewBinding.layerGuide.orientationChanged(it)
            mPageAdapter.notifyItemRangeChanged(
                0, mPageAdapter.itemCount, DetailMC.PAYLOAD_ORIENTATION
            )
            currentHolder?.updateOrientation(it)
            // 重新启动沉浸式
            startImmersiveTimer("屏幕方向变更")
        }
        mViewModel.rewardStatus.observe(lifecycleOwner) {
            // 随着看剧时长的增加，奖励的状态也会发生变化，要跟随更新UI
            LogUtil.d(WelfareMC.TAG_REPORT, "福利奖励状态变更：$it")
            // 这里重新计算当前的阶段任务阶段，以及任务的状态
            updatePendantState(mViewModel.lastStageUpdateScene)
            // 上面计算出最新的状态后，刷新进度图挂件的UI
            refreshProgressPendant()
            // 打点上报
            if (it != null && waitRewardStatusToReport) {
                // 标记下次不用再曝光了
                waitRewardStatusToReport = false
                // 上报挂件曝光打点
                reportPendantExposure(
                    // 获取进度条挂件的配置信息
                    OperationMS.get()?.getPendantConfig(WelfareMC.POSITION_PLAYER)
                )
            }
        }
        mViewModel.updateRewardProgress.observe(lifecycleOwner) {
            LogUtil.d(
                WelfareMC.TAG_REPORT, "updateRewardProgress progress:${mViewModel.taskProgress}"
            )
            // 更新进度条挂件的进度
            (welfareWidget as? IProgressPendantComp)?.setProgress(mViewModel.taskProgress)
//            if (mViewModel.taskProgress >= 1.0) {
//                if (mViewModel.rewardStatus.value == WelfareReward.STATUS_INCOMPLETE) {
//                    mViewModel.rewardStatus.postValue(WelfareReward.STATUS_UNCLAIMED)
//                }
//            } else {
//                mViewModel.rewardStatus.postValue(WelfareReward.STATUS_INCOMPLETE)
//            }
        }
        mViewModel.bannerHeightLiveData.observe(lifecycleOwner) {
            initBannerAndPlayerView()
        }
        mViewModel.playerConfig.observe(lifecycleOwner) {
            it?.operlocationConf?.quitPlayPagePopVo?.let {
                // 二级页退出的运营配置不为空，就初始化退出的代理类，由exitDelegate负责后续新增的退出逻辑
                exitDelegate?.run {
                    if (!hasBind) {
                        bind(this@NewPlayDetailActivity, null)
                    }
                } ?: let {
                    exitDelegate = ExitDelegate(mViewModel)
                    exitDelegate!!.bind(this@NewPlayDetailActivity, null)
                }
            }
        }
        mViewModel.statusPosterLiveData.observe(lifecycleOwner) {
            when(it.status){
                DetailMC.STATUS_NEW_POSTER_HIDE_LOADING->{
                    statusDismiss()
                }
                DetailMC.STATUS_NEW_POSTER_SHOW_LOADING->{
                    statusDismiss()
                    mViewBinding.compLoading.show(isLandscape = isLandScape())
                }
                DetailMC.STATUS_NEW_POSTER_SHOW_REFRESH->{
                    if (mViewModel.mChapterInfoVo?.m3u8720pUrl.isNullOrEmpty() && !downLoadUrlCanPlay(
                            mViewModel.mChapterInfoVo
                        )
                    ) {
                        stopPlay()
                        statusDismiss()
                        statusRefresh {
                            mViewModel.mChapterInfoVo?.chapterId?.let { id ->
                                mViewModel.unlockChapter(id, false)
                            }
                        }
                    } else {
                        if (mViewModel.mChapterInfoVo?.chapterId == it.chapterId) {
                            if (NetWorkUtil.isNetConnected(this)) {
                                if ((mViewModel.mChapterInfoVo?.isCharge == 1 || mViewModel.mChapterInfoVo?.isCharge == 4)) {
                                    ToastManager.showToast(getString(R.string.detail_unlock_fail))
                                } else {
                                    ToastManager.showToast(getString(R.string.detail_network_error))
                                }
                            } else {
                                if (!downLoadUrlCanPlay(mViewModel.mChapterInfoVo)) {
                                    ToastManager.showToast(getString(R.string.bbase_not_network))
                                }
                            }
                        }
                    }
                }
            }
        }
        mViewModel.subscribeStatus.observe(lifecycleOwner) {
            if (it) {
                sendDetailViewHolderEvent(DetailMC.EVENT_HOLDER_CHANGE_SUBSCRIBE_ON)
                ToastManager.showToast("将以推送的形式提醒")
            } else {
                sendDetailViewHolderEvent(DetailMC.EVENT_HOLDER_CHANGE_SUBSCRIBE_OFF)
                ToastManager.showToast("将不再发送推送通知")
            }
        }
        //评论接口返回
        mViewModel.commentNumLiveData.observe(lifecycleOwner) { data ->
            updateCommentNum(data)
            data?.commentNumMap.let {
                it?.forEach { item ->
                    updateCommentNum(
                        CommentNumBean(
                            commentNum = item.value,
                            chapterId = item.key,
                            bookId = data?.bookId
                        )
                    )

                }
            }
        }

        //刷新预加载配置
        mViewModel.preLoadChapterList.observe(lifecycleOwner) { data ->
            //取消请求评论数3106接口 reqCommentNum
            //mViewModel.reqCommentNum(mViewModel.mChapterInfoVo, data)
            mViewModel.commentLocalDataInit(mViewModel.mChapterInfoVo)
        }

        //刷新预加载配置
        mViewModel.commentLocalData.observe(lifecycleOwner) { data ->
            LogUtil.d("commentLocalData", "动态预加载 chapterlistlocal = $data")
            mViewModel.getCommentCountByIdentifier(data)
        }

        //刷新本地数据预加载配置
        mViewModel.commentLocalItemData.observe(lifecycleOwner) { data ->
            LogUtil.d("commentLocalData", "动态预加载 commentLocalItemData = $data")
            updateLocalCommentLocalNum(data)
            notifyComment()
        }
        //刷新本地数据预加载配置
        mViewModel.commentLocalItemDataFirst.observe(lifecycleOwner) { data ->
            LogUtil.d("commentLocalData", "动态预加载 commentLocalItemData = $data")
            updateLocalCommentLocalNum(data)
            notifyComment()
        }
    }

    private fun notifyComment() {
        fillCommentNum2ChapterInfoVo(mPageAdapter.getData())
    }

    override fun onEvent(event: String) {
        LogUtil.d(TAG, "收到event：$event")
        (ViewPager2Helper.findViewHolderByPosition(mViewBinding.vp, mCurrentPosition) as? NewDetailVideoViewHolder)?.onEvent(event)
        globalLayers.forEach { it.onEvent(event) }
        when (event) {
            DetailMC.EVENT_SHOW_END_RECOMMEND -> {  // 显示剧末推荐
                showFinalChapterPrompt(mViewModel.recommendLiveData.value)
            }
            DetailMC.EVENT_CHANGE_VIDEO -> { // 切剧
                mCurrentPosition = 0
                mLastStopPosition = 0
                pipChangeVideo = true
                initData()
            }
        }
    }

    private var temporaryDataList: MutableList<ChapterInfoVo>? = null
    private fun updateItemsAsync(dataList: MutableList<ChapterInfoVo>) {
        fillCommentNum2ChapterInfoVo(dataList)
        if (vpState == ViewPager2.SCROLL_STATE_IDLE) {
            LogUtil.d(DetailMC.DETAIL_VP_TAG, "duffUtil真正开始执行")
            mPageAdapter.updateItemsAsync(dataList)
            temporaryDataList = null
        } else {
            temporaryDataList = dataList
        }
    }

    /**
     * 更新播放器预加载策略
     */
    private fun updatePlayerPreLoad(preLoadFunInfoVo: PreLoadFunSwitchVo?) {
        preLoadFunInfoVo?.let { info ->
            mPlayerController.setPreloadCount(info.preLoadNum)
        }
    }

    /**
     * 初始化屏幕旋转监听器
     * 如果当前是横屏剧，当点击"全屏观看"按钮后，要自动旋转的当前旋转角度最接近的方向
     */
    private fun initAngleListener() {
        if (mViewModel.mVideoInfo?.videoInfo?.isLandscapeVideo() == true && orientationListener == null) {
            orientationListener = object : OrientationEventListener(this) {
                override fun onOrientationChanged(orientation: Int) {
                    screenAngle = orientation  // 随时记录的当前旋转角度
                }
            }
            orientationListener!!.enable()
        }
    }

    private fun refreshVideoList(it: MutableList<ChapterInfoVo>, isAdRefresh: Boolean = false) {
        setData(it)
        mCurrentPosition = mViewModel.mPosition
        if (!isAdRefresh) {
            selectChapter(mCurrentPosition, "init")
        } else {
            mViewBinding.vp.post {
                if (mCurrentPosition != mViewBinding.vp.currentItem) {
                    mOnPageChangeCallbackCompat?.init()
                }
                selectChapter(mCurrentPosition, "init")
            }
        }
        mViewModel.routeIntent?.run {
            if (type == DetailMC.LAUNCH_TYPE_SHOW_DRAMA) {
                type = DetailMC.LAUNCH_TYPE_CURRENT  // 防止下次更新剧集数据后，再次弹窗
                startDramaListDialog()
            }
        }
        if (isLandScape()) {
            mViewModel.orientation.value = Orientation.Land_Forward
            orientationManager.startWatch()
            mViewBinding.vp.postDelayed({
                goneBottomRoot()
                goneBannerAd()
            }, 100)
        } else if (mViewModel.routeIntent?.type == DetailMC.LAUNCH_TYPE_FULLSCREEN) {
            mViewModel.routeIntent?.type = DetailMC.LAUNCH_TYPE_CURRENT
            mViewBinding.vp.postDelayed({
                changedToLandForwardScape()
                // TODO: 这两个方法有很多个地方调用，为什么不合并到一个方法里？
                // 隐藏底部“河马剧场”、banner广告
                goneBottomRoot()
                goneBannerAd()
            }, 100)
        } else {
            mViewModel.orientation.value = Orientation.Port
        }
    }

    private val loadResultPresenter by lazy { LoadResultPresenter(mViewModel) }
    private var debounceJob: Job? = null
    override fun subscribeEvent(lifecycleOwner: LifecycleOwner, lifecycleTag: String) {
        super.subscribeEvent(lifecycleOwner, lifecycleTag)
        BBaseME.get().onDanMuEnableChanged().observe(lifecycleOwner) {
            // 弹幕开关变化
            mPageAdapter.notifyItemRangeChanged(
                0,
                mPageAdapter.itemCount,
                DetailMC.PAYLOAD_UPDATE_DANMU
            )
        }

        BBaseME.get().onDanMuEnableLocalChanged().observe(lifecycleOwner){enable->
            // 弹幕开关变化
            mPageAdapter.notifyItemRangeChanged(
                0,
                mPageAdapter.itemCount,
                DetailMC.PAYLOAD_UPDATE_DANMU
            )
        }
        BBaseME.get().onVipStatusChanged().observe(lifecycleOwner) {
            mViewModel.saveBeforeRefreshPlayInfo(
                mViewModel.mChapterInfoVo?.chapterId,
                currentDuration
            )
            mViewModel.refreshDataList()
            if (CommInfoUtil.isVip()) {  // 新状态是VIP，那么刷新解锁状态
                LogUtil.d(DetailMC.UNLOCK, "VIP状态变化，刷新剧集解锁状态")
                mViewModel.initUnlockChapter(mCurrentPosition)
                PriorityTaskManager.removeTask(PriorityMC.TASK_PAY_DIALOG)
            }
            bannerAdPresenter?.tryLoadBannerAd(mViewModel.videoInfoLiveData.value)
        }
        DetailME.get().showDramaListDialog().observe(lifecycleOwner) {
            // 显示选集列表
            startDramaListDialog()
        }
        DetailME.get().showDetailToast().observeForever() {
            LogUtil.d(DetailMC.UNLOCK, "二级页弹出toast = $it")
            ToastManager.showToast(it.toast, (it.showDuration * 1000).toLong())
        }
        PersonalME.get().onUserAccountChanged().observe(lifecycleOwner) {  // 用户信息发生变更。通常是账号在其它设备登录
            LogUtil.d(DetailMC.UNLOCK, "用户改变，刷新剧集解锁状态")
            mViewModel.updateChapterList()
        }
        DetailME.get().playCurrentChapter().observe(lifecycleOwner) {
            // 开始播放
            startPlay(mCurrentPosition)
        }
        DetailME.get().playNextChapter().observe(lifecycleOwner) {
            // 播放下一集
            selectChapter(mCurrentPosition + 1, "event")
        }
        DetailME.get().dismissVipComp().observeSticky(lifecycleOwner) {
            // 恢复播放
            LogUtil.d(DetailMC.UNLOCK, "第一个弹窗消失")
            sendVipDialog?.dismiss()
        }
        DetailME.get().showRecommendToast().observe(lifecycleOwner) {
            LogUtil.d(
                "getDetailRecommendSwitch",
                "getDetailRecommendSwitch showRecommendToast = $it"
            )
            val showToast = DetailRecommendUtil.getRealToast()
            getDetailRecommendSwitch(it)
        }
        DetailME.get().resetUnlockStatus().observe(lifecycleOwner) {
            // 恢复当剧集的带锁状态
            resetUnlockStatus(mCurrentPosition)
        }
        DetailME.get().playing().observe(lifecycleOwner) {
            mIsKeepPlaying = it
            if (it) {
                resumePlay()
            } else {
                pausePlay("playing event")
            }
        }
        DetailME.get().updateUnlockDialogLoading().observe(lifecycleOwner) {
            // 解锁弹窗谈起的时候，内部也会包含一个loading。通过event控制弹窗内部loading是否显示
            mViewModel.continueAdUnlockDialog?.get()?.apply {
                if (this is AdUnlockedDialogComp) {
                    if (it) {
                        //显示弹窗内部的loading
                        showLoading()
                    } else {
                        // 隐藏弹窗内部的loading
                        dismissLoading()
                    }
                }
            }
        }
        MainME.get().onNetworkChanged().observe(lifecycleOwner) {
            if (it == MainMC.NETWORK_CONNECTED) {  // 网络恢复连接
                if (!mViewModel.initBookInfo) {
                    mViewModel.reqDetailInfo()
                }
                // 当前是暂停状态 && 由于网络原因导致的暂停 && 处于前台
                if (layerPauseStatus() && pausedByNetwork && !mIsOnBackground) {
                    // 取消之前的延迟任务
                    debounceJob?.cancel()
                    // 启动新的延迟任务
                    debounceJob = CoroutineScope(Dispatchers.Main).launch {
                        delay(1000) // 延迟1秒
                        mViewModel.removePauseModel(VideoMC.PLAY_END_ERROR)
                        stopPlay()
                        mViewBinding.vp.post {
                            setStartTime(currentDuration)
                            startPlay(mCurrentPosition, isRealBegin = true)
                        }
                    }
                } else {
//                    unlockOrResumePlay()
                    resumePlay()
                }
            }
        }

        MainME.get().showKocComp().observe(lifecycleOwner) { showing ->
            if (showing) {
                mViewModel.pausePlay("KocComp")
            } else {
                mViewModel.cancelPause("KocComp")
            }
        }

        WelfareME.get().awardDialog().observe(lifecycleOwner) { showing ->
            // 当福利奖励弹窗谈起的时候，要暂停视频
            if (showing) {
                mViewModel.pausePlay("welfare")
//                VideoTrackUtil.trackPause(
//                    mViewModel.videoTrackInfo,
//                    VideoTrackUtil.PAUSE_SCENE_DIALOG_COIN
//                )
            } else {
                mViewModel.cancelPause("welfare")
                if (needOpenPushDialog) {
                    openPushDialog()
                }
            }
        }
        WelfareME.get().watchingDurationReport().observe(lifecycleOwner) {
            mViewModel.syncRewardStatus("watching_report")
        }
        BCommonME.get().pusDialog().observe(lifecycleOwner) { showing ->
            if (showing) {
                mViewModel.pausePlay("push")
//                VideoTrackUtil.trackPause(
//                    mViewModel.videoTrackInfo,
//                    VideoTrackUtil.PAUSE_SCENE_DIALOG_PUSH
//                )
            } else {
                mViewModel.cancelPause("push")
                if (needOpenWelfDialog) {
                    openWelfDialog()
                }
            }
        }
        //微信分享配置信息
        BCommonME.get().refreshWxShare().observeSticky(lifecycleOwner) { data ->
            var config: WxShareConfigVo? = null
            if (data != null) {
                config = GsonUtils.fromJson(data, WxShareConfigVo::class.java)
            }
            mViewModel.mWxShareConfigVo = config
            config?.shareCompleteTime?.let { time ->
                BBaseMC.shareCompleteTime = time
            }
            mPageAdapter.notifyItemRangeChanged(0, mPageAdapter.itemCount, DetailMC.PAYLOAD_SHARE)
        }
        BBaseME.get().httpDnsResponse().observeSticky(lifecycleOwner) {
            if (it) {
                LogUtil.d(
                    ExperimentMC.HTTP_DNS_TAG, "二级播放器收到abtest数据获取成功消息，打开httpDns"
                )
                enableHttpDns(it)
            }
        }
        DetailME.get().pauseRewardAd().observe(lifecycleOwner) {
            mViewModel.adLoaderDestroy()
        }

        DetailME.get().showRewardAd().observe(lifecycleOwner) { info ->
            info?.rewardScene = REWARD_SCENE_NORMAL // 非再看一次弹窗内触发
            showRewardAd(info)
        }

        DetailME.get().showMallAd().observe(lifecycleOwner) { info ->
            showMallAd(info)
        }

        DetailME.get().closePauseAd().observe(lifecycleOwner) {
            if (it == false && mViewBinding.comPauseAd.isShowing()) {
                DetailMC.allowPlay = false
            }
            visibleBannerAd()
            hidePauseAd()
        }

        DetailME.get().toLoadBottomAd().observe(lifecycleOwner) {
            mViewModel.videoInfoLiveData.value?.let {
                bannerAdPresenter?.tryLoadBannerAd(it)
            }

        }
        //监听删除收藏成功
        HomeME.get().deleteFavoriteSuccess().observeForever(lifecycleTag) { bookIds ->
            if (mViewModel.mVideoInfo?.videoInfo?.bookId in bookIds) {
                mViewModel.mVideoInfo?.videoInfo?.inBookShelf = false
                mViewModel.mVideoInfo?.videoInfo?.setFavoriteNum(false)
                mPageAdapter.notifyItemRangeChanged(
                    0, mPageAdapter.itemCount, DetailMC.PAYLOAD_FAVORITE
                )
            }
        }
        //监听增加收藏成功
        HomeME.get().addFavoriteSuccess().observeForever(lifecycleTag) {
            if (mViewModel.mVideoInfo?.videoInfo?.bookId == it) {
                mViewModel.mVideoInfo?.videoInfo?.inBookShelf = true
                mViewModel.mVideoInfo?.videoInfo?.setFavoriteNum(true)
                mPageAdapter.notifyItemRangeChanged(
                    0, mPageAdapter.itemCount, DetailMC.PAYLOAD_FAVORITE
                )
            }
        }
        //点赞数据库删除成功后，刷新点赞数据
        BBaseME.get().updateLikes().observe(lifecycleOwner) { likeList ->
            LogUtil.d(BBaseMC.TAG_LIKES, "二级页面刷新")
            mPageAdapter.getData().forEachIndexed { index, videoInfoVo ->
                if (likeList.contains(videoInfoVo.bookId + "_" + videoInfoVo.chapterId)) {
                    videoInfoVo.isLiked = true
                    mPageAdapter.setItem(index, videoInfoVo)
                }
            }
        }
        DetailME.get().likeButtonExposed().observe(lifecycleOwner) {
            if (DetailMC.likeBtnY > 0 && mViewModel.playerPendantConfig != null && !isLandScape() && !isInPip()) {
                addWelfareWidget(exposureReport = true, autoShow = false)
            }
        }
        mViewModel.closeEvent.observe(lifecycleOwner) {
            if (it) {
                close()
            }
        }
        BBaseME.get().downloadChapterSuccess().observeForever(lifecycleTag) { chapterInfoVo ->
            LogUtil.d(
                DetailMC.PLAYER_DOWNLOAD_TAG,
                "下载当前集完成，更新章节列表数据  ${chapterInfoVo.chapterName}"
            )
            mViewModel.updateDownLoadChapter(chapterInfoVo)
            mViewModel.getDataList().forEach { infoVo ->
                infoVo.apply {
                    //重复添加地址播放器播放会触发多次onPrepared
                    mPlayerController.updatePlayerInfo(
                        PlayerInfo(
                            mViewModel.mVideoInfo?.videoInfo?.bookId,
                            mViewModel.mVideoInfo?.videoInfo?.bookId + chapterInfoVo.chapterId,
                            if (downLoadUrlCanPlay(this)) {
                                downLoadUrl
                            } else {
                                m3u8720pUrl
                            },
                            chapterInfoVo.chapterIndex ?: 0,
                            chapterInfoVo.chapterId
                        )
                    )
                }
            }
        }
        BBaseME.get().deleteLoadChapters().observeForever(lifecycleTag) {
            LogUtil.d(DetailMC.PLAYER_DOWNLOAD_TAG, "删除下载剧集完成，更新章节列表数据  $it")
            mViewModel.deleteLoadChapters()
        }
        DialogME.get().onAppstoreGuideShow().observe(lifecycleOwner) {
            if (it) {
                mViewModel.pausePlay("AppStore")
            } else {
                mViewModel.cancelPause("AppStore")
            }
        }
        DetailME.get().onResolutionChanged().observe(lifecycleOwner) {
            if (hasShowLayTips && it.rate == BBaseMC.RATE_540P) {
                DetailKV.isHideLayTips = true
            }
            changeResolution(it)
        }
        VideoME.get().configChanged().observe(lifecycleOwner) {
            // 接口配置发生变化时要更新对应的UI
            LogUtil.d(TAG, "配置发生变化:$it")
            when (it) {
                VideoMC.FUNC_DOWNLOAD -> {
                    // 下载配置发生变化
                    mViewBinding.layerBack.refreshView()
                }

                VideoMC.FUNC_RESOLUTION -> {
                    // 清晰度配置发生变化
                    // 更新UI
                    mViewBinding.layerBack.refreshView()
                    // 当前清晰度开关已关闭，切当前不是默认720P
                    if (!BBaseKV.resolutionSwitch && !mViewModel.currentResolution.equals(
                            BBaseMC.RATE_720P,
                            true
                        )
                    ) {
                        // 将当前分辨率切回720P
                        changeResolution(
                            ResolutionRateVo(
                                rate = BBaseMC.RATE_720P,
                                needVip = 0,
                                scene = BBaseMC.SCENE_SWITCH_OFF
                            )
                        )
                    }
                }

                VideoMC.FUNC_INTRO -> {
                    // 短剧简介弹窗配置发生变化
                    if (!VideoKV.introRecSwitch) {
                        // 如果有正在展示的弹窗，要主动关闭
                        introDialog?.dismiss()
                    }
                }
            }
        }
        BBaseME.get().preloadPlayer().observe(lifecycleOwner) {
            if (it == getUiId() && mViewBinding.vp.currentItem + 1 < mPageAdapter.itemCount) {
                mPageAdapter.notifyItemRangeChanged(
                    mViewBinding.vp.currentItem + 1,
                    1,
                    DetailMC.PAYLOAD_UPDATE_PLAYER_BIND
                )
            }
        }
        DetailME.get().exitDialogFailed().observe(lifecycleOwner) {
            if (it.isNullOrEmpty()) return@observe
            LogUtil.e(TAG, "退出弹窗失败：$it")
            close()
        }
    }

    private fun showRewardAd(removeAdVo: RemoveAdWayVo?) {
        if (mViewModel.adLoading) {
            LogUtil.d(DetailMC.FREE_DRAW_TAG, "重复点击")
            return
        }
        val adId = removeAdVo?.incentiveAdId() ?: return
        val rewardScene = removeAdVo.rewardScene
        val adPosInt = if (removeAdVo.adScene == VideoMC.SCENE_PLAYER_BOTTOM) {
            AdTE.EXEMPT_BOTTOM_DETAIL
        } else {
            AdTE.EXEMPT_DRAW_DETAIL
        }
        val adPosStr = if (removeAdVo.adScene == VideoMC.SCENE_PLAYER_BOTTOM) {
            OperationExposureTE.POSITION_DETAIL_BOTTOM_AD
        } else {
            OperationExposureTE.POSITION_DETAIL_IMMERSIVE_AD_BOTTOM
        }
        LogUtil.d(
            DetailMC.FREE_DRAW_TAG,
            "免广告时间激励视频-广告流量请求 adPosition=$adPosInt adId=${removeAdVo.incentiveAdId()} adScene=${removeAdVo.adScene} maxLodTime=${removeAdVo.maxLodTime()}"
        )
        mViewModel.senADTrafficReachEvent(adPosInt, adId, removeAdVo.blockConfigId())
        mViewModel.adLoading = true
        mViewModel.adLoader = AdMS.get()?.showAd(
            this,
            AdTE.TYPE_REWARD,
            adId,
            removeAdVo.blockConfigId(),
            mapOf("ADPosition" to adPosInt),
            object : DzAdShowCallback {
                override fun onStartLoad() {//开始加载
                    LogUtil.d(DetailMC.FREE_DRAW_TAG, "开始加载广告")
                    statusDismiss()
                    mViewBinding.compLoading.show(isLandscape = isLandScape())
                }

                override fun onShowSuccess(ecpmCent: Int?, reqId: String) { // 广告观看完成可以领取奖励
                    LogUtil.d(DetailMC.FREE_DRAW_TAG, "广告观看完成，可以领取奖励")
                    mViewModel.rewardReport(
                        removeAdVo.operationId,
                        (removeAdVo.exemptTime() ?: "0").toInt(),
                        removeAdVo.adScene,
                        mViewModel.mChapterInfoVo?.chapterId,
                        currentDuration,
                        rewardScene = rewardScene,
                        showOneMoreDialogAction = showOneMoreDialog(removeAdVo, adPosStr),
                    )
                    mViewModel.adLoading = false
                }

                /**
                 *  广告报错
                 * @param code      AD_LOAD_FAILED = 20  // 广告加载失败    AD_SHOW_FAILED = 21  // 广告展示失败    AD_REWARD_FAILED = 22  // 广告获取奖励失败
                 * @param msg
                 */
                override fun onShowError(code: Int, msg: String) {
                    if (code == AdMC.AD_LOAD_FAILED || code == AdMC.AD_SHOW_FAILED || code == AdMC.AD_TIMEOUT) {// 广告加载失败
                        ToastManager.showToast("暂无可用视频，请稍后重试")
                    }
                    mViewModel.tryRemoveAdsAndPlayVideo()
                    statusDismiss()
                    mViewModel.adLoading = false
                    mViewModel.cancelAdOperationTrack(
                        removeAdVo.adScene,
                        (removeAdVo.exemptTime() ?: "0").toInt(),
                        false
                    )
                }

                override fun onShowing(ecpmCent: Int?) {
                    statusDismiss()
                    mViewModel.adLoading = false
                }
            },
            (removeAdVo.maxLodTime() ?: "0").toLong() * 1000,
            adPosInt,
            mViewModel.checkPreloadRewardAd(),
            removeAdVo.getPreloadNum()
        )
    }

    private fun showMallAd(mallAdVo: MallAdVo?) {
        LogUtil.d(AD_MALL_TAG, "showMallAd")
        if (mViewModel.adMallLoading) {
            LogUtil.d(DetailMC.FREE_DRAW_TAG, "重复点击")
            return
        }
        val requestTime = System.currentTimeMillis()
        val requestId: String =
            BBaseKV.userId + "_" + requestTime + "_" + RandomUtils.getRandumNum(999, 100)

        val sotId = mallAdVo?.getAdId()
        if (mallAdVo == null || sotId.isNullOrEmpty()) {
            statusDismiss()
            return
        }
        mViewModel.adMallLoading = true
        //显示弹窗内部的loading
        showLoading()
        MineMallAdManager.loadAd(
            this,
            sotId,
            requestId = requestId,
            mallAdVo.blockConfigId,
            mallAdVo.getLoadTimeout().toLong(),
            ScreenUtil.getScreenWidth(),
            ScreenUtil.getScreenHeight(),
            callback = object : BaseAdLoadCacheCallback<InterstitialAd> {
                var loadedTime = 0L
                override fun onLoadSuccess(ad: InterstitialAd, fromCache: Boolean?) {
                    LogUtil.d(AD_MALL_TAG, "加载-onLoadSuccess fromCache=$fromCache")
                    loadedTime = System.currentTimeMillis()
                    if (fromCache == false) {
                        mViewModel.sensorMallAdResponse(ad, requestTime, sotId)
                    }
                    statusDismiss()
                    MineMallAdManager.showAd(ad, showCallback = object : BaseAdShowCallback<InterstitialAd> {
                        var showTime = 0L //开始播放时间
                        override fun onShow(ad: InterstitialAd) {
                            mViewModel.adMallLoading = false
                            LogUtil.d(AD_MALL_TAG, "AdShowCall-onShow")
                            mViewModel.sensorMallAdShow(ad, sotId, mallAdVo)
//                    mViewModel.preloadMallAd(this@NewPlayDetailActivity, sotId)
                        }

                        override fun onShowError(ad: InterstitialAd, code: Int, msg: String) {
                            LogUtil.d(AD_MALL_TAG, "AdShowCall-onShowError")
                            ToastManager.showToast("页面加载失败，请稍后再试")
                        }

                        override fun onClick(ad: InterstitialAd) {
                            LogUtil.d(AD_MALL_TAG, "AdShowCall-onClick")
                            mViewModel.sensorMallAdClick(ad, sotId, mallAdVo)
                        }

                        override fun onReward(ad: InterstitialAd) {
                        }

                        override fun onClose(ad: InterstitialAd, rewardCloseParams: RewardCloseParams?) {
                            LogUtil.d(AD_MALL_TAG, "AdShowCall-onClose")
                            mViewModel.adMallLoading = false
                        }
                    })
                }

                override fun onStartLoad() {
                    LogUtil.d(AD_MALL_TAG, "加载-onStartLoad")
                }

                override fun onLoadError(code: Int, msg: String) {
                    statusDismiss()
                    mViewModel.adMallLoading = false
                    LogUtil.d(AD_MALL_TAG, "加载-onLoadError $code $msg")
                    ToastManager.showToast("页面加载失败，请稍后再试")
                }

                override fun onMaterialStartLoad(sky: SkyAd?) {

                }

                override fun onMaterialStatusChanged(sky: SkyAd?) {

                }

                override fun onLoadSuccess(ad: InterstitialAd) {
                    statusDismiss()
                    mViewModel.adMallLoading = false
                    LogUtil.i(AD_MALL_TAG, "加载-onLoadSuccess-应该用带fromCache参数的回调")
                }

            }
        )

    }

    private fun tryCloseWelfareAnchorAd(needLoadAd: Boolean, msg: String) {
        welfareAnchorAdLoader?.tryCloseWelfareAnchorAd(
            this@NewPlayDetailActivity,
            null,
            needLoadAd,
            msg
        )
    }

    private fun showOneMoreDialog(
        info: RemoveAdWayVo,
        adPosStr: String
    ): (OperateReportBean) -> Unit {
        return { operateBean ->
            pausePlay("再看弹窗出现") // 停止播放
            // 奖励领取成功，出再来一次弹窗
            DetailMR.get().adLookAgainDialog().apply {
                titleStr = ViewUtils.highLightText(
                    operateBean.getWinTitleDoc(),
                    operateBean.getHotWords(),
                    ContextCompat.getColor(
                        this@NewPlayDetailActivity,
                        R.color.common_FFE1442E
                    )
                )
                subTitleStr = ViewUtils.highLightText(
                    operateBean.getLookAgainContentDoc(),
                    operateBean.getHotWordsLimit(),
                    ContextCompat.getColor(
                        this@NewPlayDetailActivity,
                        R.color.common_FFE1442E
                    )
                )
                btnStr = operateBean.getLookAgainDoc()
                this.adPosStr = adPosStr
            }.onSure {
                info.rewardScene = REWARD_SCENE_LOOK_AGAIN
                showRewardAd(info)
            }.onClose { viewType, _ ->
                mViewModel.cancelPause("再看弹窗出现")
                mViewModel.tryRemoveAdsAndPlayVideo()
                resumePlay()
            }.start()

            LogUtil.i(DetailMC.FREE_DRAW_TAG, "再看一个弹窗展示")
        }
    }

    /**
     *  刷新返回按钮显示与隐藏
     * @param show 是否显示返回按钮  true：显示返回按钮  false：隐藏返回按钮
     */
    private fun updateBack(show: Boolean) {
        mViewBinding.layerBack.visibility = if (show) {
            VISIBLE
        } else {
            GONE
        }
    }

    //播放器截图成功回调
    private var snapShotListener: OnSnapShotListener = object : OnSnapShotListener {
        override fun onSnapShot(bitmap: Bitmap?, width: Int, height: Int) {
            //播放器截图回调
            bitmap?.let { mViewBinding.comPauseAd.setImageBitmap(it) }
        }
    }

    private var maxRemainBuffer = 0L

    /**
     * 播放报错打点
     */
    private fun trackPlayError(errorCode: Int, errorMsg: String, extra: String?) {
        if (mViewModel.mChapterInfoVo?.switchState == SwitchState.SWITCH_ED) {// 切换完成播放报错打点
            //获取 当前报错的url
            var url = mViewModel.mChapterInfoVo?.m3u8720pUrl
            if ((mViewModel.mChapterInfoVo?.contentUlrIndex ?: -1) > -1) {
                url = mViewModel.mChapterInfoVo?.mp4720pSwitchUrl?.get(
                    mViewModel.mChapterInfoVo?.contentUlrIndex ?: 0
                )
            }
            //报错上报
            switchStatusTrack(ErrorTE.CODE_REPLACE_FAILED, url)
        } else {  //正常报错上报：默认地址报错
            mViewModel.errorReport(errorCode, errorMsg, currentDuration, extra, true)
        }
    }

    /**
     * 提示布局（卡顿提示>菜单提示>正在下载>会员提示）
     * 主要是一些引导类的提示
     */
    fun updateTips(detailRecommendSwitch: Boolean? = false) {
        LogUtil.d(
            "updateTips",
            "updateTips  detailRecommendSwitch = ${detailRecommendSwitch}"
        )
        mViewModel.mChapterInfoVo?.let {
            mViewBinding.comVipTips.updateTips(
                isPayVideo = mViewModel.mVideoInfo?.videoInfo?.isPayVideo(),
                chapterId = it.chapterId,
                playLocalUrl = downLoadUrlCanPlay(it),
                isMenuShow = isMenuCanShow(),
                bookName = mViewModel.mVideoInfo?.videoInfo?.bookName,
                bookId = mViewModel.mVideoInfo?.videoInfo?.bookId,
                isPlotTipsShow = (TextUtils.equals(
                    mViewModel.routeIntent?.origin,
                    BBaseMC.origin_tfsj
                ) && BBaseKV.isFirstShowPlotOcpcTips && OCPCManager.getPlotOcpcBookId() == mViewModel.mVideoInfo?.videoInfo?.bookId),
                isRecommendTipsShow = detailRecommendSwitch
            )
        }
        if (detailRecommendSwitch == true) {
            lifecycleScope.delayTask(500) {
                FinalRecommendManager.onExposeNew(
                    DetailRecommendUtil.from,
                    DetailRecommendUtil.firstData,
                    DetailRecommendUtil.payInfo
                )
            }
        }

    }

    private fun getDetailRecommendSwitch(scene: String) {
        LogUtil.d("NPRecommend", "getDetailRecommendSwitch = $scene")
        LogUtil.d(
            "updateTips",
            "updateTips111"
        )
        when (scene) {
            DetailMC.SHOW_TOAST -> updateTips(mViewModel.isInLastPage)
            DetailMC.CLOSE_TOAST -> updateTips(false)
//            2 -> if(!mViewModel.hasShowRecommendInVideoStart) { updateTips(false) }
//            3 -> updateTips(false)
        }
    }


    //触发卡顿 可以显示卡顿提示
    private var canShowLayToast = false

    //卡顿没有持续到3秒
    private var isLayTimeRunning = false

    //当前集是否已显示了卡顿
    private var hasShowLayTips =false

    /**
     * 负责处理卡顿提醒
     */
    private fun createLayTimeTask() {
        canShowLayToast = true
        //提示布局（会员提示，正在下载，卡顿提示，菜单提示）
        val isMenuCanShow = mViewBinding.layerBack.getMenuVisible() // 菜单按钮是否显示
        // 有分辨率列表,且可以切换540P
        var hasRates = false
        mViewModel.mVideoInfo?.videoInfo?.content?.resolutionRates?.forEach {
            if (it.rate == BBaseMC.RATE_540P) {
                hasRates = true
            }
        }
        // 当前分辨率不是540P
        val is540P = mViewModel.currentResolution == BBaseMC.RATE_540P
        // 判断是否显示卡顿提示：有分辨率列表可切换540P 分辨率不为540P 菜单按钮显示 是竖屏 可切换分辨率 卡顿提示开关打开 是否不再显示卡顿提示 卡顿提示显示时间是今天且卡顿提示已显示3次及以上 当集未显示过卡顿提示 提示文案不为空
        LogUtil.d(
            "Lay_Tips_tag",
            "hasRates==$hasRates\nis540P==$is540P\nisMenuCanShow==$isMenuCanShow\nisLandScape()==${isLandScape()}\nmViewModel.resolutionEnable==${mViewModel.resolutionEnable}\n" +
                    "DetailKV.isHideLayTips==${DetailKV.isHideLayTips}\n弹窗时间==${DetailKV.layTipsShowDate}\nDetailKV.layTipsShowTimes==${DetailKV.layTipsShowTimes}\nhasShowLayTips=$hasShowLayTips\n" +
                    "DetailMC.resolutionRateConfig?.netBlockSwitch==${DetailMC.resolutionRateConfig?.netBlockSwitch}\nDetailMC.resolutionRateConfig?.tips.isNullOrEmpty()==${DetailMC.resolutionRateConfig?.tips.isNullOrEmpty()}"
        )
        if (!hasRates || is540P || !isMenuCanShow || isLandScape() || !mViewModel.resolutionEnable
            || DetailMC.resolutionRateConfig?.netBlockSwitch == false
            || (DetailKV.isHideLayTips && CommInfoUtil.dayDiffToday(DetailKV.layTipsShowDate) < 7)
            || (DetailKV.layTipsShowDate == SimpleDateFormat(
                "yyyy-MM-dd",
                Locale.CHINA
            ).format(Date()) && DetailKV.layTipsShowTimes >= 3)
            || hasShowLayTips
            || DetailMC.resolutionRateConfig?.tips.isNullOrEmpty()
        ) {
            return
        }

        lifecycleScope.launch(Dispatchers.IO) {
            // 延迟3秒显示卡顿提示
            delay(3000L)
            withContext(Dispatchers.Main) {
                canShowLayToast = false
                DetailKV.isHideLayTips = false
                if (isLayTimeRunning) {
                    if (mViewModel.playMode.value == PlayMode.IMMERSIVE) {  // 如果是沉浸式需要退出沉浸式
                        cancelImmersive("卡顿提示 取消沉浸式")
                        startImmersiveTimer("卡顿提示 取消沉浸式 显示提示后恢复沉浸式")
                    }
                    mViewBinding.comVipTips.showLayTips()  // 显示卡顿提示信息
                    hasShowLayTips = true
                    if (DetailKV.layTipsShowDate == SimpleDateFormat(
                            "yyyy-MM-dd",
                            Locale.CHINA
                        ).format(Date())
                    ) {
                        DetailKV.layTipsShowTimes++
                    } else {
                        DetailKV.layTipsShowDate =
                            SimpleDateFormat("yyyy-MM-dd", Locale.CHINA).format(Date())
                        DetailKV.layTipsShowTimes = 1
                    }
                }
            }
        }
    }

    // 是否第一次显示菜单
    private var isFirstShowMenu = true

    /**
     * 负责处理菜单功能提醒
     */
    private fun isMenuCanShow(): Boolean {
        // 菜单按钮是否显示
        var isMenuCanShow = mViewBinding.layerBack.getMenuVisible()
        // 第一次显示菜单
        isMenuCanShow = isMenuCanShow && isFirstShowMenu
        // 菜单提示文案是否为空
        isMenuCanShow = isMenuCanShow && !DetailMC.functionTipsConfig?.tips.isNullOrEmpty()
        // 菜单提示开关打开
        isMenuCanShow = isMenuCanShow && DetailMC.functionTipsConfig?.tipsSwitch == true
        // 菜单提示次数
        isMenuCanShow =
            isMenuCanShow && (DetailMC.functionTipsConfig?.tipsTimes ?: 0) > BBaseKV.menuTipsTimes
        // 当前不是沉浸模式
        isMenuCanShow = isMenuCanShow && mViewModel.playMode.value != PlayMode.IMMERSIVE
        // 当前不是横屏
        isMenuCanShow = isMenuCanShow && !isLandScape()
        // 当天第一次需要弹出
        isMenuCanShow = isMenuCanShow && !TimeUtils.isSameDate(
            SystemTimeUtils.currentTimeMills(),
            DetailKV.menuTipsShowTime
        )
        // 是否主动点击过菜单
        isMenuCanShow = isMenuCanShow && !BBaseKV.isClickMenu
        // 不是小窗
        isMenuCanShow = isMenuCanShow && !isInPip()
        if (isMenuCanShow) {
            isFirstShowMenu = false
        }
        return isMenuCanShow
    }

    /**
     * 播放报错：检测是否需要重置备用地址下标 或者 停止播放
     */
    private fun checkSwitchStateAndStop() {
        if (mViewModel.mChapterInfoVo != null &&
            !mViewModel.mChapterInfoVo?.mp4720pSwitchUrl.isNullOrEmpty() &&
            //备用地址数量 是否大于 要切换的备用地址下标加一
            (mViewModel.mChapterInfoVo?.mp4720pSwitchUrl?.size
                ?: 0) > ((mViewModel.mChapterInfoVo?.contentUlrIndex ?: 0) + 1) &&
            NetWorkUtil.isNetConnected(this)
        ) {
            //停止播放，不移除Surface
            stopPlay()
            //重置备用地址下标（下标+1）
            mViewModel.mChapterInfoVo?.contentUlrIndex =
                mViewModel.mChapterInfoVo?.contentUlrIndex!! + 1
            //设置需要切换地址的状态
            mViewModel.mChapterInfoVo?.switchState = SwitchState.NEED_SWITCH
            //控制进度
            setStartTime(currentDuration)
            mViewModel.played = false
            LogUtil.d(DetailMC.APP_ERROR_TAG, "播放器报错，播放器切换备用地址，played ==false")
            if (currentDuration > 0) {
                mViewModel.loadingScene = ReadingTE.LOADING_SCENE_AUTO_CHANGE_PROGRESS
            }
            LogUtil.d(
                DetailMC.APP_LOADING_TAG,
                "播放器报错，播放器切换备用地址，会调用setStartTime，loadingScene设置为自动变更进度  currentDuration==$currentDuration"
            )
            //重新播放
            changePlayerUrl(mViewModel.mChapterInfoVo?.m3u8720pUrl)
//            recordUserSenseTime(0)
            recordPrepared(0)
            LogUtil.d(
                DetailMC.PLAYER_START_PLAY_TIME_TAG,
                "播放器报错，播放器切换备用地址，代码调用moveto"
            )
        } else {
            //设置需要切换地址的状态
            mViewModel.mChapterInfoVo?.switchState = SwitchState.NO_SWITCH
            if (mCurrentPosition < 0 || mCurrentPosition >= mViewModel.getDataList().size || !downLoadUrlCanPlay(
                    mViewModel.getChapterInfoVo(mCurrentPosition)
                ) || !NetWorkUtil.isNetConnected(this@NewPlayDetailActivity)
            ) {
                // 网络问题 用该值拦截用户点击请求等操作
                pausedByNetwork = true
            }
            mViewModel.pausePlay(VideoMC.PLAY_END_ERROR)
        }
    }

    /**
     * 上报切换地址事件
     */
    fun switchStatusTrack(type: String, url: String?) {
        val msgObj = JSONObject()
        try {
            msgObj.put("url", url)
            msgObj.put("errorMessage", type)
            msgObj.put("IsSmallWindow", isInPip())
        } catch (e: JSONException) {

        }
        DzTrackEvents.get().error().type(type).pageInitFirstPlay(mViewModel.isFirstPlay)
            .message(msgObj.toString()).track()
    }

    private fun reqBitrate() {
        getRenderFPS()?.let {
            LogUtil.d("Bitrate_Tag", "二级播放页renderFps=$it")
            if (it != 0f) {
                mViewModel.renderFps = it
            }
        }
        getAudioBitrate()?.let {
            LogUtil.d("Bitrate_Tag", "二级播放页audioBitrate=$it")
            if (it != 0f) {
                mViewModel.audioBitrate = it
            }
        }
        getVideoBitrate()?.let {
            LogUtil.d("Bitrate_Tag", "二级播放页videoBitrate=$it")
            if (it != 0f) {
                mViewModel.videoBitrate = it
            }
        }
        getDownloadBitrate()?.let {
            LogUtil.d("Bitrate_Tag", "二级播放页downloadBitrate=$it")
            if (it != 0f) {
                mViewModel.downloadBitrate = it
            }
        }
    }

    private var cReportBookId: String? =
        null//上报打点bookid，如果为空且上报开始，可以上报，不为空且上报为结束，可以上报；1、开始播放第一帧时或者上报结束时，初始化为空；2、上报开始播放时，设置为当前bookid
    private var startReportDuration: Long = -1L//开始播放的进度，开始播放后初始化，当播放结束恢复-1

    /**
     * 上报播放事件
     * @param playStatus  播放状态0-播放开始，1-播放结束
     */
    private fun playEventReport(playStatus: Int, value: Long) {
        if ((!cReportBookId.isNullOrEmpty() && playStatus == 0) || (cReportBookId.isNullOrEmpty() && playStatus == 1)) {
            return
        }
        if (playStatus == 1) {
            BCommonMS.get()?.openPush(
                BCommonMC.PUSH_SHOW_LOCATION_DETAIL, BCommonMC.PUSH_SHOW_TYPE_PLAY, false
            )
        }
        mViewModel.mVideoInfo?.videoInfo?.let { videoInfo ->
            videoInfo.bookId?.let { bid ->
                cReportBookId = bid
                if (playStatus == 1) {
                    cReportBookId = null
                    startReportDuration = -1L
                }
                lifecycleScope.launch(Dispatchers.IO) {
                    if (realFirstPlaySource == null) {
                        val bookEntity = DzDataRepository.bookDao().queryByBid(bid)
                        realFirstPlaySource = if (!bookEntity?.first_play_source.isNullOrEmpty()) {
                            bookEntity?.first_play_source!!
                        } else {
                            mFirstPlaySource ?: mViewModel.mOmap?.scene
                        }
                        if (bookEntity == null && playStatus == 0) {
                            DzDataRepository.bookDao().insertOrUpdateBooks(BookEntity(bid).apply {
                                book_name = videoInfo.bookName
                                first_play_source = realFirstPlaySource
                                ext1Bean.ratingFlag =
                                    if (mViewModel.mVideoInfo?.userVideoVo?.scoreFlag == true) 1 else 0
                            })
                        }
                    }
                    HomeMS.get()?.playEventReport(
                        playletId = bid,
                        playletName = videoInfo.bookName,
                        partId = mViewModel.mChapterInfoVo?.chapterId,
                        partNum = mViewModel.mChapterInfoVo?.chapterIndex,
                        firstPlaySource = realFirstPlaySource,
                        playStatus = playStatus,
                        origin = mViewModel.routeIntent?.origin,
                        channelId = mViewModel.routeIntent?.channelId,
                        channelName = mViewModel.routeIntent?.channelName,
                        channelPos = mViewModel.routeIntent?.channelPos,
                        columnId = mViewModel.routeIntent?.columnId,
                        columnName = mViewModel.routeIntent?.columnName,
                        columnPos = mViewModel.routeIntent?.columnPos,
                        omap = videoInfo.omap ?: mViewModel.mOmap,
                        contentId = bid,
                        contentPos = mViewModel.routeIntent?.contentPos,
                        playScene = "二级播放页",
                        playingDuration = if (playStatus == 1 && startPlayTime > 0) {
                            // 废弃使用 ：累计播放+有效播放 给大数据播放时长
                            TimeUtils.convertDurationToMS(playingTime + (System.currentTimeMillis() - startPlayTime))
                        } else "0",
                        tierPlaySource = TierPlaySourceVo(
                            mViewModel.videoTrackInfo.firstTierPlaySource,
                            mViewModel.videoTrackInfo.secondTierPlaySource,
                            mViewModel.videoTrackInfo.thirdTierPlaySource
                        ),
                        callback = object : PlayEventReportCallback {
                            override fun onStart() {
                            }

                            override fun onSuccess(data: PlayEventBean?) {
                                //处理自动加入书架
                                mViewModel.mVideoInfo?.videoInfo?.let {
                                    LogUtil.d(
                                        DetailMC.START_PLAY_TAG,
                                        "1701接口返回回调，it.inBookShelf==${it.inBookShelf}   data?.isInBookShelf==${data?.inBookShelf}"
                                    )
                                    if (it.inBookShelf == false && data?.inBookShelf == true) {
                                        mViewModel.favoriteLiveData.value = true
                                        HomeME.get().addFavoriteSuccess().post(it.bookId)
                                        ActionRecorder.recordAction(ActionRecorder.ACTION_FAVORITE)
                                        FlutterMS.get()?.sendEventToFlutter(
                                            "inBookShelf",
                                            mapOf("value" to true, "bookId" to it.bookId)
                                        )
                                        TaskManager.ioTask {
                                            DBHelper.insertOrUpdateHistory(it.bookId, true)
                                        }
                                        it.inBookShelf = data.inBookShelf
                                    }
                                }
                            }

                            override fun onFail(e: RequestException) {
                            }

                            override fun onEnd() {
                            }

                        })
                }
            }
        }
    }

    /**
     * 结束拖动进度条
     */
    private fun stopDrag() {
        noSliding()
        if (!mIsOnBackground && mViewModel.playMode.value == PlayMode.NORMAL) {
            startImmersiveTimer("进度条拖动")
        }
        currentHolder?.onGestureEnd(GestureType.DRAGGING)
        startWatch()
    }

    /**
     *  停止陀螺仪监听
     */
    private fun stopWatch() {
        orientationManager.stopWatch()
    }

    /**
     *  开启陀螺仪监听
     */
    private fun startWatch() {
        if (isLandScape()) {
            orientationManager.startWatch()
        }
    }

    /**
     * 执行点赞或者取消点赞的操作
     * @param isLikes Boolean：当前状态是喜欢还是不喜欢
     */
    private fun likesClick(isLikes: Boolean) {
        mViewModel.addSubtractLikes(isLikes)
    }

    /**
     * 初始化滑动ViewPager2
     */
    private fun initViewPager2() {
        mPageAdapter = NewDetailPlayerPageAdapter(this, mPlayerController).apply {
            layerListener = object : NewDetailVideoViewHolder.LayerListener {
                override fun beforeJumpToTheatre() {
                    if (!mViewModel.jumpTheaterImg.isNullOrEmpty()
                        && mViewModel.routeIntent?.firstTierPlaySource == BBaseMC.SOURCE_TF
                        && (mViewModel.mChapterInfoVo?.isCharge == 1 || mViewModel.mChapterInfoVo?.isCharge == 4)) {
                        (PriorityTaskManager.getTask(PriorityMC.TASK_THEATRE_RETAIN) as? TheatreRetainDialogTask)?.apply {
                            updateImageUrl(mViewModel.jumpTheaterImg)
                            status = PriorityConstants.STATUS_READY
                        }
                    } else {
                        PriorityTaskManager.removeTask(PriorityMC.TASK_THEATRE_RETAIN)
                    }
                }

                override fun onDramaClick(view: View) {
                    //点击选集 隐藏菜单提示
                    mViewBinding.comVipTips.hideMenuTips()
                    if (!isLandScape() && VideoKV.introRecSwitch) {  // 竖屏下如果后台配置打开了短剧简介开关，那么显示短剧简介弹窗里的选集列表
                        // 打点
                        DzTrackEvents.get().collectedWorksClick()
                            .title(PageConstant.PAGE_INTRO_NAME)
                            .positionName(PageConstant.PAGE_INTRO_DRAMA_NAME)
                            .recoButtonStatus(BBaseKV.recommendContent)
                            .addParam("\$title", PageConstant.PAGE_INTRO_NAME)
                            .addParam("\$referrer_title", "二级播放页")
                            .track()
                        // 显示短剧简介。传入参数的意思是打开弹窗后，跳转到选集列表。
                        showIntroDialog(PageConstant.PAGE_INTRO_DRAMA_ID)
                    } else {
                        // 显示老的选集弹窗
                        startDramaListDialog()
                    }
                }

                override fun onSpeedClick(view: View) {
                    // 用户点击了倍速按钮
                    startDoubleSpeedDialog()  // 显示倍速弹窗
                    // 打点上报
                    ElementClickUtils.trackViewAppClick(
                        view,
                        bookID = mViewModel.mVideoInfo?.videoInfo?.bookId,
                        bookName = mViewModel.mVideoInfo?.videoInfo?.bookName,
                        chaptersNum = mViewModel.mChapterInfoVo?.chapterIndex,
                        chaptersId = mViewModel.mChapterInfoVo?.chapterId,
                        content = "倍速入口",
                        elementType = "倍速入口",
                    )
                }

                /**
                 * 获取当前清晰度、分辨率
                 * @return String?
                 */
                override fun getCurrentResolution(): String? {
                    return mViewModel.currentResolution
                }

                override fun onLandResolutionClick(view: View) {
                    lifecycleScope.launch(Dispatchers.IO) {
                        val hasDownloaded = mViewModel.chapterHasDownloaded(
                            mViewModel.mVideoInfo?.videoInfo?.bookId ?: "",
                            mViewModel.mChapterInfoVo?.chapterId ?: ""
                        )
                        withContext(Dispatchers.Main) {
                            DetailMR.get().landResolutionDialog().apply {
                                bookId = mViewModel.mVideoInfo?.videoInfo?.bookId
                                bookName = mViewModel.mVideoInfo?.videoInfo?.bookName
                                chapterId = mViewModel.mChapterInfoVo?.chapterId
                                isDownLoaded = hasDownloaded
                                resolutionRates =
                                    mViewModel.mVideoInfo?.videoInfo?.content?.resolutionRates
                                callback = resolutionCallback
                                currentResolution = mViewModel.currentResolution
                            }.onShow {
                                playerLoop(true)
                                mViewModel.setPlayMode(PlayMode.IMMERSIVE)
                            }.onDismiss {
                                mIsLoopPlay = false
                                playerLoop(false)
                            }.start()
                        }
                    }?.start()
                }

                override fun getSpeed(): Float {
                    return mViewModel.playSpeed
                }

                override fun getBottomStyle(): BottomStyle? {
                    return mViewModel.bottomStyle
                }

                override fun onHeroClick(view: View) {
                    SearchMR.get().collection().apply {
                        leadName = mViewModel.mVideoInfo?.videoInfo?.performerInfo?.actor
                        leadSex = 1
                        bookId = mViewModel.mVideoInfo?.videoInfo?.bookId
                        bookName = mViewModel.mVideoInfo?.videoInfo?.bookName
                        chapterIndex = mViewModel.mChapterInfoVo?.chapterIndex
                        cover = mViewModel.mVideoInfo?.videoInfo?.performerInfo?.actorPhoto
                        size = mViewModel.mVideoInfo?.videoInfo?.performerInfo?.actorVideoNum
                    }.start()
                    ElementClickUtils.trackViewAppClick(
                        view,
                        null,
                        null,
                        null,
                        mViewModel.mVideoInfo?.videoInfo?.bookId,
                        mViewModel.mVideoInfo?.videoInfo?.bookName,
                        mViewModel.mChapterInfoVo?.chapterIndex
                    )
                }

                override fun onHeroineClick(view: View) {
                    SearchMR.get().collection().apply {
                        leadName = mViewModel.mVideoInfo?.videoInfo?.performerInfo?.actress
                        leadSex = 2
                        bookId = mViewModel.mVideoInfo?.videoInfo?.bookId
                        bookName = mViewModel.mVideoInfo?.videoInfo?.bookName
                        chapterIndex = mViewModel.mChapterInfoVo?.chapterIndex
                        cover = mViewModel.mVideoInfo?.videoInfo?.performerInfo?.actressPhoto
                        size = mViewModel.mVideoInfo?.videoInfo?.performerInfo?.actressVideoNum
                    }.start()
                    // 打点操作
                    ElementClickUtils.trackViewAppClick(
                        view,
                        null,
                        null,
                        null,
                        mViewModel.mVideoInfo?.videoInfo?.bookId,
                        mViewModel.mVideoInfo?.videoInfo?.bookName,
                        mViewModel.mChapterInfoVo?.chapterIndex
                    )
                }

                override fun onBookNameClick(view: View) {
                    // 打点操作
                    ElementClickUtils.trackViewAppClick2(
                        elementPosition = "二级播放器",
                        content = mViewModel.mVideoInfo?.videoInfo?.bookName,
                        elementType = "查看详情",
                        bookName = mViewModel.mVideoInfo?.videoInfo?.bookName,
                        bookID = mViewModel.mChapterInfoVo?.chapterId,
                        chaptersNum = mViewModel.mChapterInfoVo?.chapterIndex
                    )
                    mViewModel.routeIntent?.let { intent ->
                        intent.playPosition = currentDuration
                        intent.chapterId = mViewModel.mChapterInfoVo?.chapterId
                        intent.chapterIndex = mViewModel.mChapterInfoVo?.chapterIndex
                        intent.coverBgColor = mViewModel.mVideoInfo?.videoInfo?.coverBgColor
                        intent.coverWap = mViewModel.mVideoInfo?.videoInfo?.coverWap
                        intent.alias = mViewModel.routeIntent?.alias
                        intent.bookAlias = mViewModel.mVideoInfo?.videoInfo?.bookName
                        VideoMS.get()?.goToVideoDetail(intent.getIntentJson())
                    } ?: let {
                        // 跳转到Flutter的短剧详情页面
                        VideoMS.get()?.goToVideoDetail(
                            mViewModel.mVideoInfo?.videoInfo?.bookId,
                            mViewModel.mVideoInfo?.videoInfo?.bookName,
                            mViewModel.mChapterInfoVo?.chapterId,
                            mViewModel.mChapterInfoVo?.chapterIndex,
                            progress = currentDuration,
                            mViewModel.mVideoInfo?.videoInfo?.coverBgColor,
                            mViewModel.mVideoInfo?.videoInfo?.coverWap,
                            mViewModel.routeIntent?.alias,
                            mViewModel.mVideoInfo?.videoInfo?.bookName,
                        )
                    }
                }

                override fun onDescSwitchClick(): Boolean {
                    // 用户点击了简介的展开或者收起按钮
                    startImmersiveTimer("点击简介展开收起")
                    val isEnabled = VideoKV.introRecSwitch  // 当前简介弹窗是否可用？
                    if (isEnabled) {  // 显示简介弹窗，不显示展开收起
                        // 显示简介弹窗
                        showIntroDialog(PageConstant.PAGE_INTRO_RCMD_ID)
                    }
                    // 这里的返回值：true显示简介弹窗；false不显示简介弹窗，继续执行之前的展开收起逻辑；
                    return isEnabled
                }

                /**
                 * 是否正在分享中
                 * @return Boolean
                 */
                override fun isSharing(): Boolean = isSharing

                override fun onFullscreenClick() {
                    changedToLandForwardScape()
                    tryCloseWelfareAnchorAd(true, "全屏观看")
                    mViewModel.mVideoInfo?.videoInfo?.let { video ->
                        DzTrackEvents.get().buttonClick().buttonName("全屏观看")
                            .positionName("二级页").bookId(video.bookId).bookName(video.bookName)
                            .chapterId(mViewModel.mChapterInfoVo?.chapterId)
                            .chapterIndex(mViewModel.mChapterInfoVo?.chapterIndex)
                            .chapterName(mViewModel.mChapterInfoVo?.chapterName).track()
                    }
                }

                override fun unlockChapter(chapter: ChapterInfoVo) {
                    DetailKV.lookAgainNum = 0  // 清空连续看广告次数
                    // 触发后续看广告解锁流程
                    mViewModel.watchVideoAdUnlock(chapterInfo = chapter, null)
                }

                override fun onPreviewFinished(chapter: ChapterInfoVo?) {
                    pausePlay(VideoMC.PLAY_END_PREVIEW_FINISH)
                }

                override fun onPreviewUIChanged(state: Int) {
                    // 更新预览引导状态
                    isPreviewGuideShowing = (state == PreviewUnlockLayer.PREVIEW_UI_STATE_GUIDE)

                    when (state) {
                        PreviewUnlockLayer.PREVIEW_UI_STATE_HAVE_UNLOCK -> {
                            previewNotAllowPause = true
                            if (canShowPendantVisible()) {
                                mViewModel.cancelPause(VideoMC.PLAY_END_PREVIEW_GUIDE_SHOW)
                                welfareWidget?.visibility = VISIBLE
                            }
                        }

                        PreviewUnlockLayer.PREVIEW_UI_STATE_GUIDE -> {
                            mViewModel.pausePlay(VideoMC.PLAY_END_PREVIEW_GUIDE_SHOW)
                            welfareWidget?.visibility = GONE
                        }

                        else -> if (canShowPendantVisible()) {
                            mViewModel.cancelPause(VideoMC.PLAY_END_PREVIEW_GUIDE_SHOW)
                            welfareWidget?.visibility = VISIBLE
                        }
                    }
                }

                override fun onShareClick() {
                    //点击分享按钮 隐藏菜单提示
                    mViewBinding.comVipTips.hideMenuTips()
                    shareClick()
                }

                override fun onFavoriteClick() {
                    favoriteClick(FollowSourceType.ORIGINAL)
                }

                override fun onLikeClick(isLike: Boolean) {
                    // 用户点击了喜欢/点赞
                    likesClick(isLike)
                }

                override fun onDanMuClick() {
                    // 用户点击了弹幕
                    mViewModel.pausePlay(VideoMC.PLAY_END_SEND_DANMU)  // 暂停视频播放
                    //点击弹幕按钮 隐藏菜单提示
                    mViewBinding.comVipTips.hideMenuTips()
                    videoDanMuManger?.let {
                        // 标记弹幕输入框已经显示
//                        inputBoxShowing = true
                        // 展示弹幕输入框
                        it.showDanMuInput()
                        // 打点
                        VideoDanMuManager.trackDanMuButtonClick("二级页")
                    }

                }

                override fun onCommentClick(view: View, guideContent: String?) {
                    // 神策打点
                    kotlin.runCatching {
                        val jsonObj = JSONObject().apply {
                            put("\$element_content", "评论入口")
                            put("PositionName", "二级页")
                            put("BookID", mViewModel.mVideoInfo?.videoInfo?.bookId)
                            put("BookName", mViewModel.mVideoInfo?.videoInfo?.bookName)
                            put("ChaptersID", mViewModel.mChapterInfoVo?.chapterId)
                            put("ChaptersNum", mViewModel.mChapterInfoVo?.chapterIndex)
                            if (guideContent.isNullOrEmpty()) {
                                put("IsCommentGuide", "无")
                            } else {
                                put("IsCommentGuide", "有")
                                put("GuideContent", guideContent)
                            }
                        }
                        SensorTracker.trackViewAppClick(view, jsonObj)
                    }
                    showCommentList()
                }

                override fun onPortFavoriteLayout() {
                    sendDetailViewHolderEvent(DetailMC.EVENT_HOLDER_UPDATE_FAVORITE_TIP_POSITION)
                }

                override fun onFavoriteTipClick() {
                    // 用户点击了追剧浮窗
                    FollowTipManager.markTipIsExist(false)  // 标记浮窗已经消失
                    onHideFollowTip()  // 隐藏追剧浮窗
                    favoriteClick(FollowSourceType.FOLLOW_TIP)  // 执行追剧操作
                }

                override fun onPlayPause(curState: PlayState) {
                    // 用户手动点击了暂停
                    pauseClickByUser()  // 执行暂停操作
                }

                override fun previousVideoClick() {
                    // 横屏模式下，点击了上一集
                    if (mCurrentPosition > 0) {
                        selectChapter(mCurrentPosition - 1, VideoMC.PLAY_END_MANUAL_SWAP)  // 跳转到上一集
                    } else {
//                        ToastManager.showToast("当前已为第一集")
                    }
                }

                override fun nextVideoClick() {
                    // 横屏模式下，点击下一集。如果是最后一集走剧末推荐逻辑
                    selectChapter(mCurrentPosition + 1, VideoMC.PLAY_END_MANUAL_SWAP)  // 跳转到下一集
                }

                override fun getPreviewState(): Int? {
                    return currentHolder?.getPreviewUnlockLayer()?.previewState
                }

                override fun isPipMode(): Boolean = isInPip()
                override fun keepImmersive(): Boolean = mViewModel.keepImmersive
            }
        }
        mPageAdapter.setItemClickListener(object : NewDetailPlayerPageAdapter.ItemClickListener {
            override fun onVideoClick(unlockType: Int, data: ChapterInfoVo) {
                DetailKV.lookAgainNum = 0
                //视频解锁
//                data.chapterId?.let { mViewModel.watchVideoAdUnlock(it, null) }
                mViewModel.watchVideoAdUnlock(data, null)
                mViewModel.sensorOperationClick(unlockType, OperationClickTE.BUTTON_NAME_AD)
            }

            override fun onPayClick(unlockType: Int, data: ChapterInfoVo) {
                //支付解锁
                startPayActivity(true)
//                data.chapterId?.let { mViewModel.unlockChapter(it, false) }
                mViewModel.sensorOperationClick(unlockType, OperationClickTE.BUTTON_NAME_PAY)
            }

            override fun onContinueClick(data: ChapterInfoVo) {
                //继续解锁本集
                data.chapterId?.let { mViewModel.unlockChapter(it, false) }
            }

            override fun onSubscribeClick() {
                mViewModel.onSubscribeClick()
            }
        })
        ViewPager2Helper.setup(mViewBinding.vp)
        mViewBinding.vp.offscreenPageLimit = ViewPager2.OFFSCREEN_PAGE_LIMIT_DEFAULT
        mViewBinding.vp.setOrientation(ViewPager2.ORIENTATION_VERTICAL)
        mViewBinding.vp.setAdapter(mPageAdapter)
    }

    /**
     * 展示评论列表
     */
    private fun showCommentList(originalCommentId: Int? = null, replyCommentId: Int? = null) {
        commentDelegate.showDialog()?.apply {
            this.originalCommentId = originalCommentId
            this.replyCommentId = replyCommentId
            callback = object : CommentIntent.Callback {
                override fun onDialogHeightChanged(current: Int, total: Int) {
                    mPlayerController.layer?.adjustVideoForDialog(current, total)
                    updateViewAlpha(1 - current.toFloat() / total)
                }

                override fun onCommentNumChanged(num: Long) {
                    LogUtil.d(VideoMC.TAG_COMMENT, "NewPlayDetailActivity 评论数量变化：$num")
                    mViewModel.currentChapter.commentNum = num
                    updateCommentNum(
                        CommentNumBean(
                            commentNum = num,
                            bookId = bookId,
                            chapterId = chapterId
                        ),true
                    )
                }

                override fun onDialogShow(currentHeight: Int, totalHeight: Int) {
                }

                override fun onInputClick(view: View) {
                    SensorTracker.customTrackViewAppClick(
                        view,
                        content = "输入评论",
                        position = "二级页",
                        bookID = mViewModel.mVideoInfo?.videoInfo?.bookId,
                        bookName = mViewModel.mVideoInfo?.videoInfo?.bookName,
                        chaptersNum = mViewModel.mChapterInfoVo?.chapterIndex,
                        chaptersID = mViewModel.mChapterInfoVo?.chapterId
                    )
                }

            }
            addShowListener {
                // 循环播放
                mPlayerController.playerLoop(true)
                mViewModel.setPlayMode(PlayMode.IMMERSIVE)
            }
            addDismissListener {
                mPlayerController.playerLoop(false)
                cancelImmersive("评论弹窗消失")
                startImmersiveTimer("评论弹窗消失")
                updateViewAlpha(1f)
            }
        }?.start()
    }

    private fun updateViewAlpha(alpha: Float) {
        val mAlpha = if (alpha > 0.95f) {
            1f
        } else {
            alpha
        }
        if (welfareWidget?.visibility == VISIBLE) {
            AlphaUtil.setAlpha(welfareWidget, mAlpha)
        }
        if (mViewBinding.comVipTips.visibility == VISIBLE) {
            AlphaUtil.setAlpha(mViewBinding.comVipTips, mAlpha)
        }
        mPlayerController.updateViewAlpha(mAlpha)
    }

    /**
     * 更新评论数
     */
    private fun updateCommentNum(data: CommentNumBean?, isAddLocal : Boolean? = false) {
        mPageAdapter.getData().forEachIndexed { index, videoInfoVo ->
            if (data?.chapterId == videoInfoVo.chapterId) {
                videoInfoVo.serverCommentNum = data?.commentNum?:0L
                if( videoInfoVo.localCommentNum != -1L && isAddLocal == false){
                    videoInfoVo.commentNum =
                        videoInfoVo.localCommentNum + videoInfoVo.serverCommentNum
                    getCurrentNum()
                }
                if(isAddLocal == true)
                {
                    videoInfoVo.commentNum = videoInfoVo.serverCommentNum
                }
                mPageAdapter.setItem(index, videoInfoVo)
                mPageAdapter.notifyItemRangeChanged(
                    index, 1, DetailMC.PAYLOAD_COMMENT
                )
            }
        }
    }

    fun getCurrentNum()
    {
        mPageAdapter.getData().forEachIndexed { index, videoInfoVo ->
            if(mViewModel.currentChapter.chapterId ==  videoInfoVo.chapterId){
                LogUtil.d(HomeMC.START_PLAY_TAG, "动态预加载 当前章节 = ${mViewModel.currentChapter.chapterId}  commentNum = ${videoInfoVo.commentNum}")
                videoInfoVo.commentNum?.let { mViewModel.syncCommentNum(it) }
            }
        }
    }

    /**
     * 更新本地评论数
     */
    private fun updateLocalCommentLocalNum(data: List<CommentNumCheckDatabaseBean?>) {
        LogUtil.d(HomeMC.START_PLAY_TAG, "动态预加载 本地评论数 data = $data")
        // 遍历每一条数据
        data.forEach { commentNumCheckDatabaseBean ->
            // 如果 commentNumCheckDatabaseBean 非空且存在 chapterId
            commentNumCheckDatabaseBean?.let {
                mPageAdapter.getData().forEachIndexed { index, videoInfoVo ->
                    if (it.chapterId == videoInfoVo.chapterId) {
                        // 更新本地评论数
                        videoInfoVo.localCommentNum = it.commentNum ?: 0L

                        // 如果服务器评论数不为 -1，则更新当前章节评论数
                        if (videoInfoVo.serverCommentNum != -1L) {
                            videoInfoVo.commentNum =
                                videoInfoVo.localCommentNum + videoInfoVo.serverCommentNum
                            getCurrentNum()
                        }

                        mPageAdapter.setItem(index, videoInfoVo)
                        mPageAdapter.notifyItemRangeChanged(
                            index, 1, DetailMC.PAYLOAD_COMMENT
                        )
                    }
                }
            }
        }
    }

    /**
     * 展示倍速播放弹窗
     * 让用户自己选择播放的速度
     */
    private fun startDoubleSpeedDialog() {
        mIsLoopPlay = true
        if (isLandScape()) {  // 横屏模式
            DetailMR.get().landDoubleSpeedDialogComp().apply {
                currentSpeed = mViewModel.playSpeed
                bookId = mViewModel.mVideoInfo?.videoInfo?.bookId
                bookName = mViewModel.mVideoInfo?.videoInfo?.bookName
                chapterIndex = mViewModel.mChapterInfoVo?.chapterIndex
                chapterId = mViewModel.mChapterInfoVo?.chapterId
            }.onShow {
                mViewModel.setPlayMode(PlayMode.IMMERSIVE)
                playerLoop(true)
            }.onDismiss {
                mIsLoopPlay = false
                playerLoop(false)
            }.onSelect { speed, speedStr ->
                mViewModel.playSpeed = speed
                setPlayerSpeed(speed)
                mViewBinding.layerBack.bindSpeed(speed)
                ToastManager.showToast(
                    String.format(
                        getString(R.string.bbase_switched_to_x_speed_playback), speedStr
                    )
                )
                mPageAdapter.notifyItemRangeChanged(
                    0, mPageAdapter.itemCount, DetailMC.PAYLOAD_SPEED_CHANGED
                )
            }.start()
        } else {  // 竖屏
            DetailMR.get().doubleSpeedDialogComp().apply {
                currentSpeed = mViewModel.playSpeed
                bookId = mViewModel.mVideoInfo?.videoInfo?.bookId
                bookName = mViewModel.mVideoInfo?.videoInfo?.bookName
                chapterIndex = mViewModel.mChapterInfoVo?.chapterIndex
                chapterId = mViewModel.mChapterInfoVo?.chapterId
            }.onShow {
                playerLoop(true)
                setImmersiveEnable(false, "显示倍速播放弹窗")
            }.onDismiss {
                mIsLoopPlay = false
                playerLoop(false)
                setImmersiveEnable(true, "关闭倍速播放弹窗")
            }.onSelect { speed, speedStr ->
                mViewModel.playSpeed = speed
                setPlayerSpeed(speed)
                mViewBinding.layerBack.bindSpeed(speed)
                ToastManager.showToast(
                    String.format(
                        getString(R.string.bbase_switched_to_x_speed_playback), speedStr
                    )
                )
//                mPageAdapter.notifyItemRangeChanged(
//                    0, mPageAdapter.itemCount, DetailMC.PAYLOAD_SPEED_CHANGED
//                )
            }.start()
        }
    }

    /**
     * 弹出菜单弹窗
     * 只有竖屏下才弹出
     */
    private fun startMenuDialog() {
        if (isLandScape()) {
            return
        }
        lifecycleScope.launch(Dispatchers.IO) {
            // 查询本地数据库，当前剧的下载情况，要在子线程
            val hasDownloaded = mViewModel.chapterHasDownloaded(
                mViewModel.mVideoInfo?.videoInfo?.bookId ?: "",
                mViewModel.mChapterInfoVo?.chapterId ?: ""
            )
            withContext(Dispatchers.Main) {  // 切回主线程弹UI
                // 弹窗菜单Dialog
                DetailMR.get().showMenuDialogComp().apply {
                    bookId = mViewModel.mVideoInfo?.videoInfo?.bookId
                    bookName = mViewModel.mVideoInfo?.videoInfo?.bookName
                    chapterIndex = mViewModel.mChapterInfoVo?.chapterIndex
                    chapterId = mViewModel.mChapterInfoVo?.chapterId
                    isDownLoaded = hasDownloaded
                    resolutionRates = mViewModel.mVideoInfo?.videoInfo?.content?.resolutionRates
                    callback = resolutionCallback
                    currentResolution = mViewModel.currentResolution
                }.onShow {
                    LogUtil.d("loop_play_tag", "设置循环播放")
                    mIsLoopPlay = true
                    playerLoop(true)  // 弹窗弹起后只能循环播放当前集，不能自动播放下一集
                    setImmersiveEnable(false, "显示竖屏菜单弹窗")  // 禁止进入沉浸式
                }.onDismiss {
                    mIsLoopPlay = false
                    playerLoop(false)  // 播放完当前集后，播放下一集
                    setImmersiveEnable(true, "关闭竖屏菜单弹窗")  // 允许重新进入沉浸式
                }.onSelect { bookId, bookName, chapterId, chapterIndex ->
                    // 由于最早菜单只有一个item，所以方法名就叫了select。
                    // 用户点击了下载按钮，跳转到剧集下载页

                    // 打点
                    DzTrackEvents.get().buttonClick()
                        .buttonName("二级播放页下载")
                        .track()
                    if (BBaseKV.vipStatus == 1) {  // 当前用户是VIP，直接跳转下载
                        startDownLoadActivity(chapterId)
                    } else {
                        // 如果当前不是VIP，先去充值，然后才能使用下载
                        RechargeMR.get().recharge().apply {
                            sourceType = RechargeMC.RECHARGE_SOURCE_TYPE_DOWNLOAD  // 来源
                            this.bookId = bookId
                            this.chapterId = chapterId
                            omap = mViewModel.mVideoInfo?.videoInfo?.omap ?: mViewModel.mOmap
                            sourceExtend = mapOf(
                                "bookId" to bookId as Any, "positionName" to "下载拦截" as Any
                            )
                            setCallback(getUiId(), object : RechargeIntent.ResultCallback {
                                override fun onPaySucceed() {
                                    LogUtil.d(UNLOCK, "二级播放页收到充值成功的回调")
                                    // 支付成功的回调
                                    // 重新跳转下载页面
                                    startDownLoadActivity(chapterId)
                                }
                            })
                        }.start()
                    }
                }.onWxShareSelect{
                    shareClick(0)
                }.onMomentShareSelect {
                    shareClick(1)
                }.start()
            }
        }
    }

    /**
     * 打开flutter下载页面 网络数据为空，用数据库数据跳转，数据库为空取路由数据，不用过滤所有参数
     */
    private fun startDownLoadActivity(chapterId: String) {
        mViewModel.videoInfoLiveData.value?.let { info ->
            // 去Flutter的下载页面
            toFlutterDownLoadActivity(info, chapterId)
        } ?: run {
            mViewModel.mVideoInfo?.let { item ->
                // 去Flutter的下载页面
                toFlutterDownLoadActivity(item, chapterId)
            } ?: run {
                val item = VideoDetailBean(videoInfo = VideoInfoVo().apply {
                    this.bookId = mViewModel.routeIntent?.bookId ?: ""
                    this.chapterId = mViewModel.routeIntent?.chapterId ?: ""
                })
                toFlutterDownLoadActivity(item, chapterId)
            }
        }
    }

    /**
     * 跳转Flutter下载页面
     * @param item VideoDetailBean
     * @param chapterId String
     */
    private fun toFlutterDownLoadActivity(item: VideoDetailBean, chapterId: String) {
        GsonUtil.toJson(item)?.let { infoJson ->
            val type = object : TypeToken<HashMap<String, Any>>() {}.type
            val map: HashMap<String, Any> =
                GsonUtil.gson.fromJson(infoJson, type)
            map["chapterId"] = chapterId
            map["downloadTips"] = BBaseKV.downloadTips
            // Flutter的跳转只能用TheRouter
            TheRouter.build("flutter/container?url=flutter/theaterChapterDownloadPage")
                .withSerializable("url_param", map)
                .navigation()
        } ?: run {
            ToastManager.showToast("网络异常，请检查网络")
        }
    }

    private var lastHolder: RecyclerView.ViewHolder? = null
    /**
     * 切换Item
     * @param position Int：将要被选中的位置。在列表中(包含广告和视频)的位置。
     */
    private fun onPageSelected(position: Int, forceSelected: Boolean) {
        LogUtil.d(DetailMC.DETAIL_VP_TAG,"onPageSelected    position==$position")
        // 日志，记录当前信息
        LogUtil.d(
            TAG_PLAYER, "onPageSelected position:$position \n" +
                    "current:$mCurrentPosition mLastStopPosition:$mLastStopPosition"
        )
        // 检查获取设备信息，用于读取IMEI。如果没有权限，会弹出弹窗提示用户要获取权限。
        checkPhonePermission(position)
        // 切集后恢复默认清晰度
        checkResolution()
        // 打点：记录UI渲染结束。多次打点只会记录最后一次。
        TimeMonitorManager.getMonitor(MonitorMC.SCENE_DETAIL)
            .recordTime(MonitorMC.STAGE_RENDER_END)
        LogUtil.d(
            "onPageSelected_tag",
            "mCurrentPosition==$mCurrentPosition   position==$position    mLastStopPosition==$mLastStopPosition   isInit==$forceSelected"
        )
        LogUtil.d(
            "onPageSelected_tag",
            " onPageRelease 章末推荐/剧末承接 position = $position  itemCount= ${mPageAdapter.itemCount}"
        )
        if (position == mPageAdapter.itemCount - 1) {
            mViewModel.isInLastPage = true
            //2.6.0 修改剧末推荐的获取时机 改为最后一集被选中的时候，此时获取并判断是什么时候弹出
            mViewModel.mVideoInfo?.videoInfo?.bookId?.let {
                LogUtil.d(
                    TAG,
                    "onPageLoadMore 章末推荐/剧末承接 mViewModel.mChapterInfoVo = ${mViewModel.mChapterInfoVo.toString()}"
                )
                if (!isFinalChapter() && mViewModel.mChapterInfoVo?.isCharge != DetailMC.CHAPTER_STATUS_PAY && mViewModel.mChapterInfoVo?.isCharge != DetailMC.CHAPTER_STATUS_TOMORROW) {  // 剧末内容未显示
                    // 请求终章推荐内容
                    mViewModel.requestRecommended(it, isLandScape())
                }
            }
        } else {
            mViewModel.isInLastPage = false
        }

        if (mCurrentPosition != position || position == mLastStopPosition || forceSelected || pipChangeVideo) {  // 发生了切集行为
            // 记录当前位置。由于历史原因有两个地方存储了当前列表Position，暂时保留。
            mCurrentPosition = position
            mViewModel.mPosition = position
            if (!mViewModel.keepImmersive) {
                // 取消沉浸式播放
                cancelImmersive("select new page")
            }
            // 请求1142接口获取沉浸式广告数据
            mViewModel.adShowRequest(
                this@NewPlayDetailActivity,
                mViewModel.mChapterInfoVo?.chapterId,
                currentDuration
            )
            //切集时关闭加追浮窗
            FollowTipManager.closeFollowTip()

            // 获取当前Holder，使用这个Holder来区分当前滑入的是广告还是视频
            val holder =
                ViewPager2Helper.findViewHolderByPosition(mViewBinding.vp, mCurrentPosition)
            if (holder is AdVideoViewHolder) { // 当前滑入的Holder是沉浸式广告
                LogUtil.d(TAG_PLAYER, "滑入到沉浸式广告Holder")
                //本次二级页广告判断刷到的个数
                mViewModel.immersiveADTimes ++
                LogUtil.d(TAG_PLAYER, "遇到的广告次数 mViewModel.immersiveADTimes = ${mViewModel.immersiveADTimes}")
                holder.onPageShow(mViewModel)  // 通知页面曝光，同时更新内容
                holder.setSkipAdListener {
                    LogUtil.d(DetailMC.AD_TAG, "DrawFeedbackAdComp-Draw SkipAd")
                    selectChapter(mCurrentPosition + 1, VideoMC.PLAY_END_MANUAL_SWAP, true)
                }
                updateBack(false)  // 隐藏返回按钮
                isDrawAdPageShowing = true  // 标记沉浸式广告正在展示
                updateDrawAdTips(holder)  // 在这里启动沉浸式广告底部的计时器
                // 沉浸式广告展示的时候，要全屏展示，把底部内容全部隐藏掉
                goneBottomRoot()  // 隐藏底部“河马剧场”横条
                goneBannerAd()  // 隐藏底部Banner广告
                // 曝光打点
                var skipAfterPip: Boolean? = null
                if (mViewModel.slideStateAfterPipExit == 1) {
                    mViewModel.slideStateAfterPipExit = 2
                    (getViewHolder(mCurrentPosition) as? AdVideoViewHolder)?.let {
                        skipAfterPip = it.hasAd
                    }
                }
                mViewModel.drawAdOperationTrack(
                    "ad show",
                    subSlotId = holder.getSubSlotId(),
                    skipAfterPip
                )
                LogUtil.d(DetailMC.AD_TAG, "广告曝光，开始预加载新的广告")
                // 显示一条沉浸式广告后，立马再去请求吓一条沉浸式广告
                mViewModel.loadDrawAd(this@NewPlayDetailActivity, "NewPlayDetailActivity->onPageSelected()")
                welfareWidget?.visibility = GONE  // 隐藏福利挂件
            } else {  // 当前滑入的Holder是可以播放的视频
                if (lastHolder is AdVideoViewHolder) {
                    (lastHolder as? AdVideoViewHolder)?.onPageHide()
                }
                LogUtil.d(TAG_PLAYER, "滑入到视频Holder")
                isDrawAdPageShowing = false  // 更新沉浸式广告是否正在展示的标志位
                // 检查沉浸式广告是否达到最大展示次数
                // 但这个方法里只是打印了一行日志，啥也没干。
                mViewModel.checkDrawMaxShowAndFreshDataList()
                // 沉浸式广告展示的时候，会有一个倒计时。在倒计时不允许划走。
                // 当滑动到视频时，停止沉浸式广告的计时器
                DrawAdTimeManager.cancel()
                // 让顶部返回按钮显示。沉浸式广告Holder是隐藏返回按钮的。从沉浸式返回到正常的视频需要重新显示返回按钮。
                updateBack(true)
                // 标记沉浸式广告可以滑动
                mDrawAdCanScroll = true
                // 显示底部“河马剧场”logo以及banner广告
                visibleBottomRoot()
                visibleBannerAd()
                // 是否显示过底部Banner广告？没显示过的话需要主动加载
                if (bannerAdPresenter?.bannerNeverShown() == true) {
                    LogUtil.i(DetailMC.AD_BANER_TAG, "banner未曾展示过，尝试加载")
                    bannerAdPresenter?.tryLoadBannerAd(mViewModel.videoInfoLiveData.value)
                } else if (bannerAdPresenter?.inEcpmTooLowTimeLimit(mViewModel.videoInfoLiveData.value?.bottomAdVo) == false) {
                    LogUtil.i(DetailMC.AD_BANER_TAG, "banner限制时间已过，尝试加载")
                    bannerAdPresenter?.tryLoadBannerAd(mViewModel.videoInfoLiveData.value)
                }
                // 当切集时，要检查当前用户的通知开关是否已经打开。
                // 如果没打开，而且切换的是视频Holder，那么要弹出弹窗，引导用户去系统设置打开通知开关。
                openPushDialog()
                //检查是否需要弹出弹窗
                LogUtil.d(
                    OperationMC.TAG_PENDANT,
                    "需要的广告次数 mViewModel.immersiveADLimit = ${mViewModel.immersiveADLimit}"
                )
                if(mViewModel.immersiveADLimit >= 0  && (mViewModel.scene == 1 || mViewModel.scene == 3)){
                    checkSendVipCompImmersiveAd()
                }
                // 判断是否可以显示悬浮挂件
                if (canShowPendantVisible()) {
                    LogUtil.d(
                        OperationMC.TAG_PENDANT,
                        "onPageSelected video holder show pendant"
                    )
                    // 隐藏侧边可滑动的挂件
                    welfareWidget?.visibility = VISIBLE
                }
            }
            lastHolder = holder
            // TODO: 上面的else处理的是什么holder？为什么不放到那里？ Holder除了 AdVideoViewHolder、NewDetailVideoViewHolder？
            if (holder is NewDetailVideoViewHolder) {
                // 获取评论数
//                mViewModel.reqCommentNum(holder.mData)

                // 重新添加弹幕UI
                videoDanMuManger?.onDanMuContainerShow(holder.controller.mViewBinding.danmuContainer)
            }

            // 处理SeekBar热区高度
            setSeekBarAreaTouchListener(holder)
            // 校验一些异常情况，当出现异常情况的时候要主动停止播放。
            val chapterInfoVo =
                mPageAdapter.getData(position) ?: mViewModel.getChapterInfoVo(position)
            if (!downLoadUrlCanPlay(chapterInfoVo) && chapterInfoVo?.m3u8720pUrl.isNullOrEmpty()) {// 没有可用的下载视频  网络地址也为空
                if (!isPlayCompletion && isPlaying) {  // 如果当前还在播放中，要停止播放，并打点上报
                    track(1, triggerScenario = "202")  // 打点上报
                    unbindViewHolder()
                }
            }
            // 调用这里开始真正的播放
            startPlay(position, isRealBegin = true)
            // 触发视频展示后续的一些操作
            onVideoPageShow()

            // 检查本来应该出沉浸式广告的位置，是出了沉浸式广告。如果没出要上报。
            mViewModel.checkDrawAdMiss(position)

            // 要放到最后一行
            checkAdIntervalAndRefreshDataList()
        } else {
            mCurrentPosition = position
            mViewModel.mPosition = position
        }
        pipChangeVideo = false
        mViewModel.hasReport = false
        // 预先解锁后续章节。获取后续章节的播放链接。
        mViewModel.unlockChapter(
            mViewModel.mChapterInfoVo?.chapterId,
            false,
            isPreload = true,
            isReport = true,
        )
        //切集隐藏菜单提示
        mViewBinding.comVipTips.hideMenuTips()
        // 更新提示信息，主要是一些引导提示
        LogUtil.d(
            "updateTips",
            "updateTips  DetailRecommendUtil.configPass = ${DetailRecommendUtil.configPass}"
        )
        updateTips((DetailRecommendUtil.getDetailRecommendSwitch() == 2 || DetailRecommendUtil.configPass) && mBookId == DetailRecommendUtil.showBookId && !mViewModel.hasShowToastInStart)
        mViewModel.hasShowToastInStart = true
    }

    private fun checkAdIntervalAndRefreshDataList() {
        if (!isAd(mCurrentPosition)) {
            // 检查沉浸式广告的位置是否发生变化。如果发生了变化就刷新数据列表
            mViewModel.checkAdIntervalAndFreshDataList()
            DrawSession.apply {
                nextPositionIsAd = {
                    isAd(mCurrentPosition + 1)  // 检查下一个位置是否是广告
                }
                checkAdIntervalAndRefreshDataList = {
                    checkAdIntervalAndRefreshDataList()
                }
                reportSessionEnd = {
                    // 上报上次session结束的点
                    DzTrackEvents.get().playerSession()
                        .bookId(mViewModel.mVideoInfo?.videoInfo?.bookId)
                        .bookName(mViewModel.mVideoInfo?.videoInfo?.bookName)
                        .chapterId(mViewModel.mChapterInfoVo?.chapterId)
                        .chapterNum(mViewModel.mChapterInfoVo?.chapterIndex.toString())
                        .startTime(DrawAdKV.lastSessionStartPlayTimeSec)
                        .duration(DrawAdKV.lastSessionWatchedDurationSec.toLong())
                        .lastEndTime(DrawAdKV.interruptPlayEndTimeMs / 1000)
                        .track()
                }
            }
            // 金币掉落
            CoinsDropIntervalUtil.selectInterval()
        }
    }

    var sendVipDialog: PDialogComponent<*>? = null

    private fun checkSendVipCompImmersiveAd() {
        LogUtil.d(
            OperationMC.TAG_PENDANT,
            "广告弹窗加载图片 mViewModel.checkADPic = ${mViewModel.checkADPic}"
        )

        LogUtil.d(
            OperationMC.TAG_PENDANT,
            "广告弹窗 mViewModel.immersiveADLimit = ${mViewModel.immersiveADLimit}  mViewModel.immersiveADTimes = ${mViewModel.immersiveADTimes}  mViewModel.isShowVIPComp = ${mViewModel.isShowVIPComp}"
        )

        LogUtil.d(
            OperationMC.TAG_PENDANT,
            "广告弹窗 BBaseKV.sendVipCompLastTimeInDetail.toInt() = ${BBaseKV.sendVipCompLastTimeInDetail}  mViewModel.vipCompIntervalTime = ${mViewModel.vipCompIntervalTime}  "
        )

        if (mViewModel.immersiveADLimit >= 0 && mViewModel.immersiveADTimes >= mViewModel.immersiveADLimit &&
            !mViewModel.isShowVIPComp && mViewModel.checkADPic >= 2 &&
            System.currentTimeMillis() - BBaseKV.sendVipCompLastTimeInDetail >= mViewModel.vipCompIntervalTime * 60 * 1000
            && !isLandScape() && !isInPip() && mViewModel.getPreviewConfig() == null) {
            LogUtil.d("sendVIPComp", "满足沉浸式广告条件，暂停播放弹出弹窗")
            mViewModel.isShowVIPComp = true //加锁防止两个弹窗抢资源
            mViewModel.report1141(31) //曝光给服务端
            DetailMR.get().sendVIPComp().apply {
                compName = "二级免广告页送会员弹窗"
                buttonName = "二级免广告页送会员弹窗点领取"
                imgUrl = mViewModel.popUpConfigVo?.advertImg
                type = 3
            }.onShow {
                BBaseKV.sendVipCompLastTimeInDetail = System.currentTimeMillis()
                sendVipDialog = it
                mViewModel.pausePlay(VideoMC.PLAY_END_DIALOG_SEND_VIP)
            }.onDismiss {
                sendVipDialog = null
                mViewModel.cancelPause(VideoMC.PLAY_END_DIALOG_SEND_VIP)
            }.onSure {
                LogUtil.d("sendVIPComp", "点击领取，弹下一个弹窗")
                //进行下一步操作后恢复播放
                mViewModel.sendVipCompNewComp("1")
            }.onClose {
                LogUtil.d("sendVIPComp", "点击关闭恢复播放")
            }.start()

        }
    }

    /**
     * 设置SeekBar热区的触摸监听
     * 让SeekBar热区可以拦截触摸事件，避免误触
     */
    private fun setSeekBarAreaTouchListener(holder: RecyclerView.ViewHolder?) {
        if (VideoMS.get()?.expandSeekBarArea() != true) {// 实验开关关闭
            return
        }
        (holder as? NewDetailVideoViewHolder)?.controller?.let { controller ->
            controller.interceptTouchEvent(mViewBinding.clAdBottom)
            controller.interceptTouchEvent(mViewBinding.flAdBottom)
            controller.interceptTouchEvent(mViewBinding.bottomRemoveAdComp)
            mViewBinding.bottomRemoveAdComp.children.forEach {
                controller.interceptTouchEvent(it)
            }
            controller.interceptTouchEvent(mViewBinding.bottomLayer)
        }
    }

    private fun checkSendVipCompUnLock() {
        LogUtil.d(
            OperationMC.TAG_PENDANT,
            "广告弹窗 BBaseKV.sendVipCompLastTimeInDetail.toInt() = ${BBaseKV.sendVipCompLastTimeInDetail}  mViewModel.vipCompIntervalTime = ${mViewModel.vipCompIntervalTime}  "
        )
        if (BBaseKV.lockTimes >= mViewModel.lockTimes && mViewModel.lockTimes >= 0 && mViewModel.checkLockPic >= 2
            && !mViewModel.isShowVIPComp
            && System.currentTimeMillis() - BBaseKV.sendVipCompLastTimeInDetail >= mViewModel.vipCompIntervalTime  * 60 * 1000
            && !isLandScape() && !isInPip() && mViewModel.getPreviewConfig() == null) {
            LogUtil.d("sendVIPComp", "满足解锁页条件，暂停播放弹出弹窗")
            mViewModel.isShowVIPComp = true
            mViewModel.report1141(31) //曝光给服务端
            val intent = DetailMR.get().sendVIPComp().apply {
                compName = "解锁页送会员弹窗"
                buttonName = "解锁页送会员弹窗点领取"
                imgUrl = mViewModel.popUpConfigVo?.lockPageImg
                type = 2
                BBaseKV.lockTimes = 0  //弹出后清零
            }.onShow {
                BBaseKV.sendVipCompLastTimeInDetail = System.currentTimeMillis()
                sendVipDialog = it
                mViewModel.pausePlay(VideoMC.PLAY_END_DIALOG_SEND_VIP)
            }.onDismiss {
                sendVipDialog = null
                mViewModel.cancelPause(VideoMC.PLAY_END_DIALOG_SEND_VIP)
            }
                .onSure {
                    LogUtil.d("sendVIPComp", "点击领取，进入第二个弹窗逻辑")
                    mViewModel.sendVipCompNewComp("2")
                    //进行下一步操作后恢复播放
                }.onClose {
                    LogUtil.d("sendVIPComp", "点击关闭恢复播放")
                    sendVipDialog?.dismiss()
                }
//                .start()
            PriorityTaskManager.addTask(
                PriorityDialogTask(
                    PriorityMC.TASK_SEND_VIP_DIALOG,
                    PageConstant.PAGE_ID_PLAYER,
                    PriorityMC.PRIORITY_DETAIL_SEND_VIP_DIALOG,
                    intent
                )
            )
            PriorityTaskManager.executeTask()
        }

    }

    private fun checkWelfareCompUnLock() {
        LogUtil.d(
            "sendWelfare",
            " mViewModel.giveVipConf = ${mViewModel.giveVipConf}  "
        )
        if (!isInPip() && mViewModel.giveVipConf?.hasShow == false && mViewModel.getPreviewConfig() == null) {
            LogUtil.d("sendVIPComp", "满足解锁页条件，弹出弹窗")
            val intent = DetailMR.get().sendVIPComp().apply {
                compName = "无版权剧赠送会员弹窗"
                buttonName = null
                imgUrl = mViewModel.giveVipConf?.imgUrl

            }.onShow {
                mViewModel.giveVipConf?.hasShow = true
            }.onDismiss {

            }.onSure {
                mViewModel.request1143(
                    scene = mViewModel.sendVipScene,
                    token = mViewModel.giveVipConf?.receiveToken
                )
            }.onClose {

            }
//                .start()
            PriorityTaskManager.addTask(
                PriorityDialogTask(
                    PriorityMC.TASK_SEND_WELFARE_DIALOG,
                    PageConstant.PAGE_ID_PLAYER,
                    PriorityMC.PRIORITY_DETAIL_SEND_WELFARE_DIALOG,
                    intent
                )
            )
            PriorityTaskManager.executeTask()
        }

    }



    /**
     * isEndRecommend 是否是终章页面
     * 页面展现，视频，广告，或解锁页
     */
    private fun onVideoPageShow(isEndRecommend: Boolean = false) {
        val bookId = getPlayingBookId() ?: ""
        val chapterId = mViewModel.mChapterInfoVo?.chapterId
        val isAd = mViewModel.mChapterInfoVo?.isAd == 1
        val isUnlock = (mViewModel.mChapterInfoVo?.isCharge == 1 || mViewModel.mChapterInfoVo?.isCharge == 4)
        mVideoLifecycle.onPageShow(bookId, chapterId, isAd, isUnlock, isEndRecommend)
    }
    private var coroutineScope: CoroutineScope? = CoroutineUtils.getCoroutineScope()

    /**
     * 播放视频
     * @param position Int  要播放视频的position
     * @param mergeData Boolean?
     * @param isRealBegin Boolean
     */
    private fun startPlay(
        position: Int,
        mergeData: Boolean? = false,
        isRealBegin: Boolean = false
    ) {
        loadingBegin = false
        isPlayerPrePrepared = false
        playCount = 0
        currentDuration = 0
        hasShowLayTips = false
        chapterSwitching = false
        statusDismiss()  // 隐藏一些通用UI：loading、网络异常、空数据页面；
        LogUtil.d("VideoListVM", "detail_draw_ad_tag: startPlay position:$position")
        if (position < 0 || position >= mViewModel.getDataList().size) {
            return
        }

        mViewModel.mChapterInfoVo =
            mPageAdapter.getData(position) ?: mViewModel.getChapterInfoVo(position)  // 保存当前章节信息
        if (mViewModel.firstPlayChapterId != null && mViewModel.firstPlayChapterId != mViewModel.mChapterInfoVo?.chapterId) {
            mViewModel.isFirstPlay = false
        }
        mViewModel.firstPlayChapterId = mViewModel.mChapterInfoVo?.chapterId
        LogUtil.d(
            "onPageSelected_tag",
            "mViewModel.mChapterInfoVo?.m3u8720pUrl==${mViewModel.mChapterInfoVo?.m3u8720pUrl}     position==$position"
        )
        if ((!mViewModel.mChapterInfoVo?.m3u8720pUrl.isNullOrEmpty() || downLoadUrlCanPlay(mViewModel.mChapterInfoVo)) && mViewModel.mChapterInfoVo?.isCharge != DetailMC.CHAPTER_STATUS_PREVIEW) {
            // 如果当前章节是可以播放的，要记录下来
            mViewModel.lastPlayChapterInfoVo = mViewModel.mChapterInfoVo
        }
        // 如果当前是试看剧集，需要退出沉浸式
        if (mViewModel.mChapterInfoVo?.isCharge == DetailMC.CHAPTER_STATUS_PREVIEW) {
            LogUtil.d(TAG_PLAYER, "当前剧集是试看剧集，退出沉浸式")
            mViewModel.setPlayMode(PlayMode.NORMAL)
            mViewBinding.bottomRemoveAdComp.visibility = GONE  // 隐藏底部的RemoveAdComp
        }
        // 保存章节信息到打点工具里
        VideoTrackUtils.mChapterInfoVo = mViewModel.mChapterInfoVo
        // 更新打点数据
        mViewModel.mChapterInfoVo?.let { chapterInfo ->
            mViewModel.videoTrackInfo.apply {
                bookId = mViewModel.mVideoInfo?.videoInfo?.bookId
                bookName = mViewModel.mVideoInfo?.videoInfo?.bookName
                chapterId = chapterInfo.chapterId
                chapterName = chapterInfo.chapterName
                chapterNum = chapterInfo.chapterIndex.toString()
                chapterIndex = chapterInfo.chapterIndex
                cpPartnerName = mViewModel.mVideoInfo?.videoInfo?.cpPartnerName
                cpPartnerId = mViewModel.mVideoInfo?.videoInfo?.cpPartnerId
            }
            // 更新返回按钮显示的文案
//            mViewBinding.layerBack.setChapterInfo("${mViewModel.mVideoInfo?.videoInfo?.bookName} 第${chapterInfo.chapterIndex}集")
            mViewBinding.layerBack.setChapterInfo(
                mViewModel.mVideoInfo?.videoInfo?.bookName ?: "",
                if (chapterInfo.chapterIndex != null) "第${chapterInfo.chapterIndex}集" else "",
                if (mViewModel.mVideoInfo?.videoInfo?.isPayVideo() == true) R.drawable.bbase_ic_pay else 0,
                if (chapterInfo.isCharge != null) chapterInfo.isCharge ?: 0 else 0,
            )
            coroutineScope = if (coroutineScope == null) {
                CoroutineUtils.getCoroutineScope()
            } else {
                coroutineScope?.cancel()
                coroutineScope = null
                CoroutineUtils.getCoroutineScope()
            }
            coroutineScope?.launch {
                delay(10000)
                MainME.get().detailToast().post(Any())
            }
            // 获取当前RV的Holder
            val holder = ViewPager2Helper.findViewHolderByPosition(mViewBinding.vp, position)
            LogUtil.d(
                "onPageSelected_tag",
                "holder    ====${holder is NewDetailVideoViewHolder}      holder==${holder}"
            )
            if (holder is NewDetailVideoViewHolder) {  // 当前是视频Holder
                mViewModel.autoSelectResolution()  // 自动选择合适的分辨率
                //恢复界面状态
                LogUtil.d("onPageSelected_tag", "bindViewHolder")
                LogUtil.d(DetailMC.DETAIL_VP_TAG,"切换holder  holder==${holder.mData?.chapterName}")
                bindViewHolder(holder)  // 记录当前Holder，以及controller
                initPlayerControllerLayer()  // 对播放器进行初始化
                initPlayerListener()  // 初始化播放器的回调监听
                if (mViewModel.showActor) {  // 如果需要展示演员信息，将演员信息展示出来
                    mViewModel.mVideoInfo?.videoInfo?.run {
                        holder.layerInfo?.getVideoInfoComp()?.setPerformerInfo(performerInfo)
                    }
                }
                LogUtil.d(
                    "VideoListVM",
                    "startPlay m3u8720pUrl== ${chapterInfo.m3u8720pUrl}    downLoadUrl==${chapterInfo.downLoadUrl}"
                )
                if (downLoadUrlCanPlay(chapterInfo) || !chapterInfo.m3u8720pUrl.isNullOrEmpty()) {  // 当前视频可以播放
                    if (holder.mUnlockLayout.visibility == VISIBLE) {
                        holder.mUnlockLayout.visibility = GONE
                        holder.mData = chapterInfo
                        holder.onEvent(DetailMC.EVENT_HOLDER_UNLOCK)  // 给Holder发可以播放的事件信息，holder会做出对应操作。
                    }
                    //防止退出后台之后，再次调用start方法，导致视频播放
                    mCurrentPlayPosition = position
                    if (position != isAutoSelectIndex) {
                        isAutoSelectIndex = -1
                    }
                    if (showingIsAd && drawAdComplete) {//上一个是否是广告且没有播放完城
                        LogUtil.d(DetailMC.AUTO_JUMP_AD_TAG, "上一个是广告并且自动播放完了")
                    } else {
                        if (position != isAutoSelectIndex) {
                            LogUtil.d(DetailMC.AUTO_JUMP_AD_TAG, "startPlay-手动切集")
                            mViewModel.resetAutoJumpDrawAdCount()
                        } else {
                            LogUtil.d(DetailMC.AUTO_JUMP_AD_TAG, "startPlay-自动切集")
                        }
                    }
                    showingIsAd = false
                    // 耗时打点
                    TimeMonitorManager.getMonitor(MonitorMC.SCENE_DETAIL)
                        .recordTime(MonitorMC.STAGE_VIDEO_PREPARE_START)
                    // 修改备用地址切换的状态
                    mViewModel.mChapterInfoVo?.switchState = SwitchState.NO_SWITCH
                    mViewModel.played = false
                    LogUtil.d(DetailMC.APP_ERROR_TAG, "切换剧集开始播放，played ==false")
                    mViewModel.loadingScene = ReadingTE.LOADING_SCENE_PLAYING
                    LogUtil.d(
                        DetailMC.APP_LOADING_TAG,
                        "切换剧集开始播放，loadingScene设置为播放过程中"
                    )
                    LogUtil.d(TAG_RESOLUTION, "清晰度状态:${mViewModel.resolutionState}")

                    if (mPlayerController.isPreRenderPlayer()) {
                        isPlayerPrePrepared = mPlayerController.isPlayerPrepared()
                    }
                    mPlayerController.getPlayer(holder.mData?.chapterIndex ?: 0,playerVid = holder.mData?.chapterId)?.let { player ->
                        holder.bindVideo(player, position)
                        mPlayerController.layer?.let { layer ->
                            detailDelegate2?.updatePlayer(player, layer.getTextureViewRoot())
                        }
                    }
                    if (isRealBegin) {
                        //NEW_VIEW_PAGER2 用户感知的计时、开始准备的计时开始记录点
                        if (!mViewModel.isFirstPlay) {
                            recordUserSenseTime(0)
                        }
                        recordPrepared(0)
                    }
                    mViewModel.routeIntent?.let { routeIntent ->
                        if ((mViewModel.playedVideoPlayTime[chapterInfo.chapterId ?: ""] ?: 0) > 0) {
                            setStartTime(mViewModel.playedVideoPlayTime[chapterInfo.chapterId ?: ""] ?: 0)
                            seekToPlay(mViewModel.playedVideoPlayTime[chapterInfo.chapterId ?: ""] ?: 0)
                        } else if (routeIntent.playPosition != null && (routeIntent.playPosition
                                ?: 0) > 0
                            && (chapterInfo.chapterId == routeIntent.chapterId || chapterInfo.chapterIndex == routeIntent.chapterIndex)
                        ) {
                            setStartTime(routeIntent.playPosition ?: 0)
                            LogUtil.d(TAG, "setStartTime    " + (routeIntent.playPosition ?: 0))
                            if ((routeIntent.playPosition ?: 0) > 0) {
                                mViewModel.loadingScene =
                                    ReadingTE.LOADING_SCENE_AUTO_CHANGE_PROGRESS
                            }
                            LogUtil.d(
                                DetailMC.APP_LOADING_TAG,
                                "切换剧集开始播放，调用setStartTime，loadingScene设置为自动变更进度  routeIntent.playPosition==${routeIntent.playPosition}"
                            )
                            routeIntent.playPosition = 0
                        } else if (mViewModel.needResetBeforeRefreshDuration(chapterInfo.chapterId)) {
                            setStartTime(mViewModel.beforeRefreshPlayDuration ?: 0)
                            mViewModel.clearBeforeRefreshPlayInfo()
                            if ((mViewModel.beforeRefreshPlayDuration ?: 0) > 0) {
                                mViewModel.loadingScene =
                                    ReadingTE.LOADING_SCENE_AUTO_CHANGE_PROGRESS
                            }
                            LogUtil.d(
                                DetailMC.APP_LOADING_TAG,
                                "切换剧集开始播放，调用setStartTime，loadingScene设置为自动变更进度   mViewModel.beforeRefreshPlayDuration==${mViewModel.beforeRefreshPlayDuration}"
                            )
                        } else {
                            if (isPlayCompletion) {
                                setStartTime(0)
                                LogUtil.d(TAG, "setStartTime    0")
                            }
                        }
                    }
                    prepareAndStart(mViewBinding.vp.currentItem, isRealBegin)
                    // 很重要的方法 moveTo，调用后，播放器会切换到要播放的视频
                    mPlayerController.moveTo(mViewModel.mVideoInfo?.videoInfo?.bookId + holder.mData?.chapterId)
                    LogUtil.d(TAG, "moveTo    chapterInfo.chapterId==${chapterInfo.chapterId}")
                    //处理继续播放
                    isPlayCompletion = false
                    statusDismiss()
                    // 通知当前holder视频开始播放了
                    holder.onEvent(DetailMC.EVENT_HOLDER_START_PLAY)
                    // 返回按钮layer不在RV列表里，要单独通知一下
                    mViewBinding.layerBack.onEvent(DetailMC.EVENT_HOLDER_START_PLAY)
                    LogUtil.d("compLoading", "compLoading show")
                    if (!mViewModel.canPlay()) {  // 如果当前还不能播放。有某些限制条件，还不允许播放。比如弹窗正在展示等。
                        pausePlay("should_not_play 4")
                    } else if (mViewModel.resolutionState == DetailMC.RESOLUTION_STATE_WAIT_TO_PLAY) {
                        LogUtil.d(TAG_RESOLUTION, "开始播放")
                        if (BBaseKV.resolutionSwitch) {  // 清晰度切换开关是打开状态
                            mViewModel.resolutionState = DetailMC.RESOLUTION_STATE_DONE
                            mPageAdapter.notifyItemRangeChanged(
                                0, mPageAdapter.itemCount, DetailMC.PAYLOAD_RESOLUTION_CHANGED
                            )
                        } else {  // 清晰度开关关了
                            mViewModel.closeResolution()
                        }
                    }
                } else if (chapterInfo.m3u8720pUrl.isNullOrEmpty() && !downLoadUrlCanPlay(
                        chapterInfo
                    )
                ) {  // 播放地址为空，而且本地没有已下载的视频
//                    if (BBaseKV.payCount == 0) {
//                        mViewModel.getDataList().forEachIndexed { index, chapterInfoVo ->
//                            if (chapterInfoVo.isCharge == DetailMC.CHAPTER_STATUS_PAY) {
//                                if (index == position) {
//                                    return
//                                }
//                                selectChapter(index)
//                                chapterInfoVo.chapterIndex?.let {
//                                    selectUnLockToast(it)
//                                }
//                                return
//                            }
//                        }
//                    }
                    LogUtil.d(
                        "VideoListVM",
                        "startPlay 自动解锁 isCharge--${chapterInfo.isCharge} 有余额--${DetailKV.totalAmount}"
                    )
                    curChapterCoins = 0
                    // 耗时打点
                    TimeMonitorManager.getMonitor(MonitorMC.SCENE_DETAIL)
                        .recordTime("prepare unlock")
                    chapterInfo.chapterId?.let {
                        // 耗时打点
                        if (!mViewModel.isFirstPlay) {
                            PlayerMonitorManager.getPlayerMonitor(DetailMC.DETAIL_API_TRACK_TAG)
                                .recordTime(PlayerMonitorManager.TAG_REQUEST_TIME_START)
                            // 请求1139接口，开始解锁，去获取播放链接
                            LogUtil.d(DetailMC.PLAYER_START_PLAY_TIME_TAG, "开始请求1139接口")
                        }
                        mViewModel.unlockChapter(it, false, allowDuplicateRequest = true)
                    }
                }
                if (isRealBegin) {
                    if (!mViewModel.isFirstPlay) {
                        recordUserSenseTime(0)
                    }
                    recordPrepared(0)
                    LogUtil.d(
                        DetailMC.PLAYER_START_PLAY_TIME_TAG,
                        "切换剧集开始播放，代码调用moveto"
                    )
                }
                // 同步当前在播剧集信息
                chapterInfo.syncVideoInfo(mViewModel.currentChapter)
                mViewModel.onChapterChanged.value = 1
                mViewModel.routeIntent?.run {
                    if (type == DetailMC.LAUNCH_TYPE_SHOW_COMMENT_LIST && chapterInfo.chapterId == chapterId) {
                        type = DetailMC.LAUNCH_TYPE_CURRENT  // 防止下次更新剧集数据后，再次弹窗
                        lifecycleScope.launch {
                            delay(200)
                            showCommentList(originalCommentId, replyCommentId)
                        }
                    }
                }
                //选中当前集并预加载后面几集
                mViewModel.preLoadChapters(position, mergeData)
                //福利锚点
                if (!isInPip() && !isLandScape() && !isAd(mCurrentPosition)) {
                    welfareAnchorAdLoader?.checkAdIntervalData()
                    val canShowWelfareAnchorAd = welfareAnchorAdLoader?.tryShowWelfareAnchorAd(
                        this,
                        holder.getAdConstraintLayout()
                    )
                    if (canShowWelfareAnchorAd == true) {
                        holder.initAdConstraintLayout(mViewModel.mVideoInfo?.welfarePointAdVo)
                        welfareAnchorAdLoader?.setWelfareAnchorAdShowListener(holder.welfareAnchorAdShowListener)
                    }
                }
            } else if (holder is AdVideoViewHolder) {  // 当前是广告Holder
                if (!holder.hasAdView()) {
                    //当广告为空时，默认跳转下一章
                    var newPos: Int
                    if (slideDirection == 0) {
                        // 向下再滑一个
                        newPos = position + 1
                        selectChapter(newPos, VideoMC.PLAY_END_AUTO_SWAP, true)  // 跳转到目标剧集
                    } else {  // 如果是向上滑，那么就继续向上再滑一个
                        newPos = position - 1
                        selectChapter(newPos, VideoMC.PLAY_END_AUTO_SWAP, true)  // 跳转到目标剧集
                    }
                    LogUtil.d(
                        DetailMC.AD_TAG,
                        "广告为空，默认跳转下一章 position:$position newPos:$newPos"
                    )
                } else {
                    bannerAdPresenter?.closeFeedbackPop()//尝试关闭反馈的弹窗
                    showingIsAd = true
                    drawAdComplete = false
                    LogUtil.d(DetailMC.AD_TAG, "广告不为空，展示广告 position:$position")
                }
            }
        }
    }

    private fun isAd(position: Int): Boolean {
        val holder = ViewPager2Helper.findViewHolderByPosition(mViewBinding.vp, position)
        return (holder is AdVideoViewHolder).also { isAd ->
            if (isAd) LogUtil.d("interval_chapter_detail", "position:$position 是广告")
        }
    }

    private fun statusDismiss() {
        dismissLoading()
        statusComponent.background(R.color.common_transparent)
        mViewBinding.layerBack.setBackgroundResource(R.color.common_transparent)
        mViewBinding.compLoading.dismiss()
    }

    private fun statusRefresh(callBack: (() -> Unit)) {
        changeHolderCover(mCurrentPosition, GONE)
        mViewBinding.comVipTips.hideTips()
        statusComponent.background(R.color.common_FF0F0F0F)
        mViewBinding.layerBack.setBackgroundResource(R.color.common_FF0F0F0F)
        mViewModel.statusPoster.statusDataEmpty().screenStatus(isLandScape())
            .iconRes(R.drawable.bbase_ic_net_error_dark)
            .actionTextColor(ContextCompat.getColor(mViewBinding.vp.context, R.color.common_white))
            .actionBgResource(R.drawable.common_refresh_btn_bg_dark)
            .des(getString(R.string.bbase_not_network)).desColor(
                ContextCompat.getColor(
                    this@NewPlayDetailActivity, R.color.common_FF5E6267
                )
            ).actionText(getString(R.string.bbase_refresh))
            .actionListener {
                callBack.invoke()
            }.post()
    }

    private fun selectUnLockToast(
        chapterIndex: Int,
    ) {
//        ToastManager.showToast(
//            String.format(
//                getString(R.string.detail_please_unlock_the),
//                chapterIndex
//            )
//        )
    }

    var isSharing = false
    private var isShareDirect = false

    /**
     * 分享剧
     * @param menuShareType -1:代表是默认分享方式（根据分享菜单列表shareVoList决定要不要弹出分享弹窗）。0：代表微信分享，不弹出弹窗，直接分享。1：朋友圈分享，不弹出弹窗，直接分享
     */
    private fun shareClick(menuShareType:Int?=-1) {
        mViewModel.mWxShareConfigVo?.let { wxShareConfigVo ->
            wxShareConfigVo.menuShareType = menuShareType
            wxShareConfigVo.firstPlaySource = realFirstPlaySource
            wxShareConfigVo.wxShareAppId = BBaseKV.wxShareAppId
//            VideoTrackUtil.trackPause(mViewModel.videoTrackInfo, VideoTrackUtil.PAUSE_SCENE_SHARE)
            mViewModel.pausePlay(VideoMC.PLAY_END_WECHAT_SHARE)
            wxShareConfigVo.shareVoList?.forEachIndexed { _, shareItemBean ->
                shareItemBean?.needToastResult = false
                shareItemBean?.dismissShareDialogOnFail = true
                shareItemBean?.bookId = mViewModel.mVideoInfo?.videoInfo?.bookId
                shareItemBean?.bookName = mViewModel.mVideoInfo?.videoInfo?.bookName
                shareItemBean?.coverUrl = mViewModel.mVideoInfo?.videoInfo?.coverWap
                shareItemBean?.chapterId = mViewModel.mChapterInfoVo?.chapterId
            }
            DzTrackEvents.get().shareTE().type().channelCode(CommInfoUtil.getAppChannel())
                .site(ShareTE.SITE_SHARE_BTN).bookId(mViewModel.mVideoInfo?.videoInfo?.bookId)
                .bookName(mViewModel.mVideoInfo?.videoInfo?.bookName).track()
            isSharing = true
            if (wxShareConfigVo.shareVoList?.size == 1) {
                isShareDirect = true
            }
            wxShareConfigVo.isLandscape = isLandScape()
            BCommonMS.get()?.share(wxShareConfigVo, object : ShareListener {
                override fun onShareStart(shareItemBean: ShareItemBean) {
                }

                override fun onFail(
                    shareItemBean: ShareItemBean,
                    errorMessage: String?,
                    shareResultBean: ShareResultBean?,
                ) {
                    isSharing = false
                    isShareDirect = false
                    ToastManager.showToast(errorMessage ?: "分享失败")
                    mViewModel.cancelPause(VideoMC.PLAY_END_WECHAT_SHARE)
                    ThirdSDKTrack.onError("二级播放页", errorMessage, "分享")
                    if (!isLandScape()) {
                        startImmersiveTimer("分享失败")
                    }
                }

                override fun onSuccess(
                    shareItemBean: ShareItemBean,
                    shareResultBean: ShareResultBean,
                ) {
                    isSharing = false
                    isShareDirect = false
                    ToastManager.showToast("分享成功")
                    mViewModel.cancelPause(VideoMC.PLAY_END_WECHAT_SHARE)
                    if (!shareResultBean.shareTimes.isNullOrEmpty()) {
                        mViewModel.mVideoInfo?.videoInfo?.setShareNum(shareResultBean.shareTimes)
                        mPageAdapter.notifyItemRangeChanged(
                            0, mPageAdapter.itemCount, DetailMC.PAYLOAD_SHARE
                        )
                    }
                    BCommonME.get().refreshShareNum().post(shareResultBean)
                    if (!isLandScape()) {
                        startImmersiveTimer("分享成功")
                    }
                }

                override fun onCancel(
                    shareItemBean: WxShareConfigVo,
                    clickCancelBtn: Boolean,
                ) {
                    isSharing = false
                    isShareDirect = false
                    if (clickCancelBtn) {
                        ToastManager.showToast("取消分享")
                    }
                    mViewModel.cancelPause(VideoMC.PLAY_END_WECHAT_SHARE)
                    if (!isLandScape()) {
                        startImmersiveTimer("取消分享")
                    }
                }

                override fun prohibitShare(shareItemBean: ShareItemBean) {
                    isSharing = false
                    isShareDirect = false
                    mViewModel.cancelPause(VideoMC.PLAY_END_WECHAT_SHARE)
                    if (!isLandScape()) {
                        startImmersiveTimer("分享频繁，请稍后重试")
                    }
                }
            }, shareDialogListener)
        }
    }

    private val shareDialogListener = object : ShareDialogListener {
        override fun onShow() {
            if (isLandScape()) {
                mViewModel.setPlayMode(PlayMode.IMMERSIVE)
            } else {
                cancelImmersive("start share")
            }
        }
    }

    /**
     * 点击追剧
     */
    private fun favoriteClick(followSourceType: FollowSourceType) {
        DetailMC.followSource = followSourceType
        mViewModel.mVideoInfo?.videoInfo?.run {
            if (inBookShelf == true) {
                HomeMR.get().favoriteDialog().apply {
                    cancelText = "再想想"
                    sureText = "确认"
                    title = "确认取消追剧吗？"
                    content = "取消后可能找不到本剧哦~"
                }.onShow {
                    setImmersiveEnable(false, "取消收藏弹窗")
                }.onDismiss {
                    setImmersiveEnable(true, "关闭 取消收藏弹窗")
                }.onSure {
                    mViewModel.deleteShelf(mBookId)
                }.start()

            } else {
                mViewModel.addShelf(
                    mBookId,
                    mViewModel.mChapterInfoVo?.chapterId,
                    realFirstPlaySource,
                    mViewModel.mOmap,
                    followSourceType
                )
            }
        }
    }


    private var isDramaListDialogShow = false
    private var dramaListDialog: PDialogComponent<*>? = null

    /**
     * 弹出剧集弹窗
     */
    private fun startDramaListDialog() {
        mIsLoopPlay = true
        cancelTimeOutTask()
        if (isLandScape()) {  // 横屏下的剧集列表
            DetailMR.get().selectionsLandScape().apply {
                bookName = mViewModel.mVideoInfo?.videoInfo?.bookName
                chapters = mViewModel.getDataList()
                currentChapter = mViewModel.mPosition
                finishStatus = mViewModel.mVideoInfo?.videoInfo?.finishStatus
                originalIntent = mViewModel.routeIntent
                coverBgColor = mViewModel.mVideoInfo?.videoInfo?.coverBgColor
                coverWap = mViewModel.mVideoInfo?.videoInfo?.coverWap
                showPermanentFree =
                    mViewModel.mVideoInfo?.videoInfo?.isPayVideo() == false && mViewModel.mVideoInfo?.videoInfo?.isPermanentFree() == true
            }.onSelect { dramaVo ->
                startScroll()
                mViewModel.getDataList().indexOf(dramaVo).let {
                    selectChapter(it, VideoMC.PLAY_END_CHANGE_CHAPTER_BY_LIST)
                }
            }.onShow {
                isDramaListDialogShow = true
                mViewModel.setPlayMode(PlayMode.IMMERSIVE)
                playerLoop(true)
            }.onDismiss {
                mIsLoopPlay = false
                isDramaListDialogShow = false
                resumeTimeOutTask()
                playerLoop(false)
            }.start()
        } else {  // 竖屏下的剧集列表
            DetailMR.get().dramaListDialog().apply {
                bookName = mViewModel.mVideoInfo?.videoInfo?.bookName
                chapters = mViewModel.getDataList()
                currentChapter = mViewModel.mPosition
                finishStatus = mViewModel.mVideoInfo?.videoInfo?.finishStatus
                bookId = mViewModel.mVideoInfo?.videoInfo?.bookId
                chapterId = mViewModel.mChapterInfoVo?.chapterId
                chapterIndex = mViewModel.mChapterInfoVo?.chapterIndex
                originalIntent = mViewModel.routeIntent
                coverBgColor = mViewModel.mVideoInfo?.videoInfo?.coverBgColor
                coverWap = mViewModel.mVideoInfo?.videoInfo?.coverWap
                showPermanentFree =
                    mViewModel.mVideoInfo?.videoInfo?.isPayVideo() == false && mViewModel.mVideoInfo?.videoInfo?.isPermanentFree() == true
            }.onSelect { dramaVo ->
//            if (dramaVo.isCharge == 1 && BBaseKV.payCount == 0) {//如果是未解锁并且用户是免费用户，则只能跳转到第一个未解锁章节
//                run loop@{
//                    mViewModel.getDataList().forEachIndexed { index, chapterInfoVo ->
//                        if (chapterInfoVo.isCharge == DetailMC.CHAPTER_STATUS_PAY) {
//                            selectChapter(index)
//                            chapterInfoVo.chapterIndex?.let {
//                                selectUnLockToast(it)
//                            }
//                            return@loop
//                        }
//                    }
//                }
//            } else {
                startScroll()
                mViewModel.getDataList().indexOf(dramaVo).let {
                    selectChapter(it, VideoMC.PLAY_END_CHANGE_CHAPTER_BY_LIST)
                }
//            }
            }.onShow {
                dramaListDialog = it
                isDramaListDialogShow = true
                setImmersiveEnable(false, "显示剧集弹窗")  // 禁止进入沉浸式
                playerLoop(true)  // 循环播放当前集
            }.onDismiss {
                dramaListDialog = null
                mIsLoopPlay = false
                isDramaListDialogShow = false
                resumeTimeOutTask()  // 恢复章末推荐、剧末承接的倒计时操作
                playerLoop(false)  // 自动播下一集
                setImmersiveEnable(true, "关闭剧集弹窗")  // 允许进入沉浸式
            }.start()
        }
    }

//    private var payConfirmDialog: PDialogComponent<*>? = null
//    private var payOrderPage: OrderPage? = null

//    /**
//     * 支付解锁弹窗
//     */
//    private fun startPayUnlockDialog(orderPage: OrderPage?) {
//        orderPage?.let { data ->
//            val holder = ViewPager2Helper.findViewHolderByPosition(mViewBinding.vp,mCurrentPosition)
//            if (holder is NewDetailVideoViewHolder) {
//                holder.mUnlockTypeView.visibility = GONE
//                holder.mUnlockLayout.visibility = VISIBLE
//                holder.mContinueUnlock.visibility = GONE
//            }
//            DetailMR.get().chapterUnlockDialog().apply {
//                bookName = data.bookName
//                chapterId = data.chapterId
//                chapterIndex = data.chapterIndex
//                chapterName = data.chapterName
//                price = "${data.price}${data.priceUnit}"
//                remain = "${data.remain}${data.priceUnit}"
//            }.onSure {
//                data.chapterId?.let { it1 -> mViewModel.unlockChapter(it1, true) }
//                if (holder is NewDetailVideoViewHolder) {
//                    holder.mUnlockTypeView.visibility = GONE
//                }
//            }.onShow {
//                payConfirmDialog = it
//                payConfirmDialog?.orientation = it.context.resources.configuration.orientation
//                payOrderPage = data
//            }.onDismiss {
//                payConfirmDialog = null
//            }.onClose {
//                if (holder is NewDetailVideoViewHolder) {
//                    holder.mUnlockTypeView.visibility = GONE
//                    holder.mUnlockLayout.visibility = VISIBLE
//                    holder.mContinueUnlock.visibility = VISIBLE
//                }
//            }.start()
//        }
//    }

//    fun selectChapter(chapterId: String?) {
//        chapterId ?: return
//        mViewModel.getDataList()
//            .indexOfFirst { it.chapterId == chapterId }
//            .takeIf { it != -1 && it != mCurrentPosition }
//            ?.let(::selectChapter)
//    }

    /**
     * 跳转章节
     * @param position Int 要跳转的位置。整个RV中的position，包含广告
     * @param triggerScenario String? 触发场景，打点用
     * @param slowSlid Boolean 是否要有滑动动画
     */
    private fun selectChapter(position: Int, triggerScenario: String, slowSlid: Boolean = false) {
        LogUtil.d("VideoListVM", "selectChapter--$position")
        LogUtil.d(
            "selectChapter_tag",
            "selectChapter--$position  当前position==${mViewBinding.vp.currentItem}"
        )
        if (position < 0) {
            ToastManager.showToast("剧集加载失败，请稍后重试")
            return
        }
        if (position < mPageAdapter.itemCount) {
            if (position != mViewBinding.vp.currentItem) {
                mViewModel.sendEvent(DetailMC.EVENT_CHANGE_CHAPTER)
            }
            if (slowSlid) { // 要有滚动动画
                startScroll() // 打开滚动交互开关
                LogUtil.d("selectChapter_tag", "setCurrentItem--$position   smoothScroll ==true")
                mViewBinding.vp.setCurrentItem(position, true) // 调用此方法让ViewPager跳转
            } else {
                //scrollTo会导致onRelease晚于initComplete,播放结束打点分开处理
                if (isPlaying) track(1, triggerScenario = triggerScenario) // 如果当前正在播放中，那么要停止播放，并上报结束播放事件
                LogUtil.d("selectChapter_tag", "setCurrentItem--$position   smoothScroll ==false")
                if (position == mViewBinding.vp.currentItem) {
                    onPageSelected(position, true)
                } else {
                    mViewBinding.vp.setCurrentItem(position, false) // 调用此方法让ViewPager跳转
                }
            }
            hideFinalChapterRecommend()  // 隐藏章末推荐
            cancelTimeOutTask()  // 取消终章推荐计时器
        } else {  // 已经是最后一集
            LogUtil.d(
                TAG,
                "已经是最后一集  position==" + position + "itemCount==" + mPageAdapter.itemCount
            )
            if (!isInPip()) {
                seekToPlay(0) // 回到视频开头
            }
//            mViewModel.mVideoInfo?.videoInfo?.bookId?.let {
//                if (!isFinalChapter()) { // 已经是最后一集了，但章末内容还没显示。开始请求章末推荐的内容
//                    mViewModel.requestRecommended(it, isLandScape())
//                }
//            }
            if (isPlaying) track(1, triggerScenario = "200")  // 结束播放打点
//            VideoTrackUtil.trackPause(
//                mViewModel.videoTrackInfo,
//                VideoTrackUtil.PAUSE_SCENE_ALL_PLAY_OUT
//            )
            onPauseClick()  // 暂停播放
        }
        introDialog?.dismiss()  // 跳转到章末推荐的是，要隐藏选集弹窗
    }


    /**
     * 显隐封面
     */
    private fun changeHolderCover(position: Int, visibility: Int) {
        val holder = ViewPager2Helper.findViewHolderByPosition(mViewBinding.vp, position)
        if (holder is NewDetailVideoViewHolder) {
            holder.controller.coverVisibility(
                if (isLandScape()) {  // 横屏不显示封面。没有横屏下的封面，所以不展示了。
                    GONE
                } else {
                    visibility
                }
            )
        }
    }

    /**
     * 重置当前解锁方式
     */
    private fun resetUnlockStatus(position: Int) {
        mViewModel.setPlayMode(PlayMode.LOCKED)
//        val holder =
//            ViewPager2Helper.findViewHolderByPosition(mViewBinding.vp,position)
//        if (holder is NewDetailVideoViewHolder) {
//            holder.updatePlayMode(PlayMode.LOCKED)
//        }
    }


    /**
     * 设置播放源
     */
    private fun setData(videoInfoList: MutableList<ChapterInfoVo>) {
        //遍历资源,添加到列表播放器当中,刷新先移除原来的地址
        mPlayerController.cancelAllPreload()  // 取消所有的预加载
        videoInfoList.forEach { chapterInfoVo ->
            LogUtil.d(TAG_RESOLUTION, "二级页 视频地址：${chapterInfoVo.content?.getUrl()}")
            chapterInfoVo.content?.getUrl()?.let { url ->
                mPlayerController.setPlayerInfo(
                    PlayerInfo(
                        mViewModel.mVideoInfo?.videoInfo?.bookId,
                        mViewModel.mVideoInfo?.videoInfo?.bookId + chapterInfoVo.chapterId,
                        url,
                        chapterInfoVo.chapterIndex ?: 0,
                        chapterInfoVo.chapterId
                    )
                )
            }
        }
        mPageAdapter.setViewModel(mViewModel)
        fillCommentNum2ChapterInfoVo(videoInfoList)
        mPageAdapter.setData(videoInfoList)
    }


    /**
     *把评论数据拼接到数据源上。
     */
    private fun fillCommentNum2ChapterInfoVo(videoInfoList: List<ChapterInfoVo>) {
        mViewModel.unlockChapterLiveData.value?.chapterInfo?.let { chapterInfoList ->
            for (item in chapterInfoList) {
                for (chapterItem in videoInfoList) {
                    if (item.chapterId == chapterItem.chapterId) {
                        item.commentNum?.let {
                            chapterItem.commentNum = it
                        }
                    }
                }
            }
        }

        mViewModel.videoInfoLiveData.value?.videoInfo?.let { videoInfo ->
            for (chapterItem in videoInfoList) {
                if (videoInfo.chapterId == chapterItem.chapterId) {
                    videoInfo.content?.commentNum?.let {
                        chapterItem.commentNum = it
                    }
                }
            }
        }
    }
    /**
     * 下载链接是否可以播放 true：可以播放；false：不可以播放
     */
    private fun downLoadUrlCanPlay(chapterInfo: ChapterInfoVo?): Boolean {
        chapterInfo?.let {
            return mViewModel.downLoadUrlCanPlay(chapterInfo)
        }
        return false
    }


    /**
     * 视频暂停/恢复的时候使用，
     */
    fun onPauseClick(pauseBlock: (() -> Unit)? = null) {
        if (layerPauseStatus()) {
            if (pausedByNetwork) {
                ToastManager.showToast("网络异常，请稍后重试")
                return
            }
            activePause = false
//            mViewModel.cancelPause(VideoMC.PLAY_END_PAUSE)
            mViewModel.clearPause()
            resumePlay()
        } else {
            activePause = true
            pausePlay(VideoMC.PLAY_END_PAUSE)
            pauseBlock?.invoke()
        }
    }


    /**
     * activity不可见或者播放页面不可见时调用该方法
     */
    private fun setOnBackground(isOnBackground: Boolean) {
        if (mIsOnBackground == isOnBackground) return
        mIsOnBackground = isOnBackground
        if (isOnBackground) {
            if (LocalActivityMgr.getForegroundActivityCount() == 1) {
                TaskManager.delayTask(1000) {
                    if (LocalActivityMgr.isBackground()) {
                        VideoTrackUtil.trackPause(
                            mViewModel.videoTrackInfo,
                            VideoTrackUtil.PAUSE_SCENE_BACKGROUND,
                            if (mViewModel.isLandscapeVideo) {
                                if (isLandScape()) "横屏" else "竖屏"
                            } else "",
                            detailIntent = mViewModel.routeIntent,
                            isMultipleInstances = true
                        )
                    }
                }
            }
            mViewModel.pausePlay(VideoMC.PLAY_END_BACKGROUND)
            currentHolder?.onEvent(DetailMC.EVENT_HOLDER_BACKGROUND)
            stopDrag()
        } else {
            if (NetWorkUtil.isNetConnected(this)) {
                if (pausedByNetwork) {
                    mViewModel.removePauseModel(VideoMC.PLAY_END_ERROR, VideoMC.PLAY_END_BACKGROUND)
                    stopPlay()
                    mViewBinding.vp.post {
                        setStartTime(currentDuration)
                        startPlay(mCurrentPosition, isRealBegin = true)
                    }
                    pausedByNetwork = false
                } else {
                    mViewModel.cancelPause(VideoMC.PLAY_END_BACKGROUND)
                }
            } else {
                if (downLoadUrlCanPlay(mViewModel.mChapterInfoVo)) {
                    mViewModel.cancelPause(VideoMC.PLAY_END_BACKGROUND)
                }
            }
        }
    }

    /**
     * 暂停播放
     */
    private fun pausePlay(scene: String) {
        mViewModel.printLog(TAG_PLAYER, "pausePlay scene:$scene")
        if (mViewModel.mChapterInfoVo?.isPreviewChapter() == true && scene == VideoMC.PLAY_END_PAUSE && previewNotAllowPause) {
            LogUtil.d(TAG_PLAYER, "试看模式已出现解锁的情况下，禁止手动暂停")
            return
        }
        if (!scene.startsWith("should_not_play")) {
            mViewModel.pausePlay(scene)
        }
        playerPause()
        if (!isLandScape() || scene != VideoMC.PLAY_END_WECHAT_SHARE) {
            cancelImmersive("player__ pause $scene")
        }
    }


    /**
     * 恢复播放
     */
    private fun resumePlay() {
        if (inputBoxShowing || isSharing || activePause || !mIsKeepPlaying || isFinalChapter() ||
            !mViewModel.canPlay() || mIsOnBackground || isPauseAdShow() || DetailMC.allowPlay == false
            || exitDelegate?.isVisible == true
        ) {
            mViewModel.printLog(
                "xxxxxxxx", "resumePlay blocked! isSharing:$isSharing " +
                        "activePause:$activePause mIsKeepPlaying:$mIsKeepPlaying isFinalChapter:${isFinalChapter()} " +
                        "canPlay:${mViewModel.canPlay()} mIsOnBackground:$mIsOnBackground pauseAdShow:${isPauseAdShow()} " +
                        "allowPlay:${DetailMC.allowPlay}"
            )
            DetailMC.allowPlay = null
            return
        }
        mViewModel.printLog(TAG_PLAYER, "player__ resumePlay")
        playerResume()
        pausedByNetwork = false
    }


    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        try {
            mViewModel.saveRvAllCells = mViewModel.videoListLiveData.value
            mViewModel.savePosition = mCurrentPosition
            mViewModel.saveDuration = currentDuration
            mViewModel.saveChapterIndex = mViewModel.mChapterInfoVo?.chapterIndex
            mViewModel.saveChapterId = mViewModel.mChapterInfoVo?.chapterId
            PlayingStatisticsMgr.saveLocalPlayingDurationToKV()
            LogUtil.d(
                WelfareMC.TAG_REPORT,
                "PlayerDetailActivity onSaveInstanceState save local duration ${BCommonKV.localPlayingDuration}秒"
            )
        }catch (e: Exception){
            e.printStackTrace()
            LogUtil.d(TAG, "onSaveInstanceState error:" + e.message)
        }
    }

    override fun onStart() {
        super.onStart()
        setOnBackground(false)
    }

    private var isPause = false
    override fun onResume() {
        isPause = false
        if (isShareDirect) {
            isSharing = false
            isShareDirect = false
        }
        //退出挽留弹窗： 请求接口刷新数据
        mViewModel.getOperationConfig(mBookId, mViewModel.routeIntent?.firstTierPlaySource)
        resetAutoSize(resources.configuration)
        super.onResume()
        BasePlayerManager.registerConvertURLCallback(convertURLCallback)
        setOnBackground(false)
        resumeTimeOutTask()
        if (mViewModel.initDrawAd) {
            LogUtil.d(DetailMC.AD_TAG, "页面切换loadDrawAd")
            mViewModel.loadDrawAd(this, "NewPlayDetailActivity.onResume()")
        }
        openPushDialog()
//        mFeedAd?.resume()
        orientationListener?.enable()
        if (hasWelfareConfig) {
            mViewModel.syncRewardStatus("Activity resume")
        }

        mVideoLifecycle.onUIResume()
        refreshVideoIndex()
        DzTrackEvents.get().hivePv()
            .pType("page_view")
            .withOmapSource(OmapNode().apply {
                rgts = BBaseKV.regTime
                nowChTime = BBaseKV.chTime
                is_login = if (CommInfoUtil.hasLogin()) 1 else 0
                pageName = getPageName()
            })
            .withQmapSource(QmapNode().apply {
                eventType = "page_view"
            })
            .track()

        checkSubscribeStatus()
        openScreenOn()
        ReaderMS.get()?.dismissAudioComp(PLAY_SOURCE)

        if (WebMS.get()?.isCancelPreloadInPlayDetail() == true) {
            WebMS.get()?.cancelPreloadWelfareRewardAd(1)
        }

    }

    /**
     * 页面显示时，通知权限没开，页面关闭订阅
     */
    private fun checkSubscribeStatus() {
        if (!mViewModel.isSubscribe) {
            return
        }
        val isNotifyEnabled = NotificationUtil.isNotifyEnabled(AppModule.getApplication())
        if (isNotifyEnabled) {
            sendDetailViewHolderEvent(DetailMC.EVENT_HOLDER_CHANGE_SUBSCRIBE_ON)
        } else {
            mViewModel.isSubscribe = false
            sendDetailViewHolderEvent(DetailMC.EVENT_HOLDER_CHANGE_SUBSCRIBE_OFF)
        }
    }

    private fun refreshVideoIndex() {
        val holder = ViewPager2Helper.findViewHolderByPosition(mViewBinding.vp, mCurrentPosition)
        if (holder is NewDetailVideoViewHolder) {
            holder.updateVideoIndex()
        }
    }

    private fun cleanAdFeedbackTip() {
        val holder = ViewPager2Helper.findViewHolderByPosition(mViewBinding.vp, mCurrentPosition)
        if (holder is AdVideoViewHolder) {
            holder.closeFeedbackTip()
        }
    }

    override fun onPause() {
        mVideoLifecycle.onUIPause()
        super.onPause()
        if (mViewModel.playMode.value != PlayMode.PIP) {
            // 暂停
            isPause = true
            setOnBackground(true)
        }
        //TODO:
//        mPageAdapter.releasePrePlayer(mViewModel.lastPlayChapterInfoVo?.chapterIndex)
        BasePlayerManager.unRegisterConvertURLCallback()
        DrawAdManager.onPause()
        cleanAdFeedbackTip()
//        if (!isInFinalChapter && !isPlayCompletion && isPlaying) {
//            track(1)
//        }
        cancelTimeOutTask()
//        mFeedAd?.pause()
        mViewModel.resetAutoJumpDrawAdCount()
        orientationListener?.disable()
        currentHolder?.onEvent(VideoMC.EVENT_PAGE_INVISIBLE)
    }

    override fun onStop() {
        isPause = true
        setOnBackground(true)
        PlayingStatisticsMgr.saveLocalPlayingDurationToKV()
        WidgetMS.get()?.sendAppWidgetRefreshBroadcast()
        super.onStop()
        mVideoLifecycle.onUIStop()
        closeScreenOn()
    }

    override fun recycleRes() {
        super.recycleRes()
        mViewModel.adLoaderDestroy()
        //回收横竖屏监听
        orientationManager.destroyWatch()
        mViewModel.onDrawAdDestroy()
        mViewModel.onPauseAdDestroy()
        mViewModel.onMallAdDestroy()
        welfareAnchorAdLoader?.destroy()
    }

    override fun onDestroy() {
        super.onDestroy()
        HomeME.get().refreshFavorite().post(null)
        debounceJob?.cancel()
        debounceJob = null
        mPlayerController.destroy()
        mOnPageChangeCallbackCompat?.let {
            mViewBinding.vp.unregisterOnPageChangeCallback(it)
        }
        cancelTimeOutTask()
        mViewModel.apply {
            adLoaderDestroy()
            onDestroy()
        }
        DetailRecommendUtil.isInDetail = false
        detailDelegate?.unbind()
        detailDelegate = null
        guideDelegate?.unbind()
        guideDelegate = null
        AppModule.getApplication().unregisterComponentCallbacks(configChangedCallback)

        PlayingStatisticsMgr.saveLocalPlayingDurationToKV()
        // PlayingStatisticsMgr.cancelPlayingTimer()
        LogUtil.d(
            WelfareMC.TAG_REPORT,
            "PlayerDetailActivity onDestroy save local duration ${BCommonKV.localPlayingDuration}秒"
        )
        (welfareWidget as? PendantComp)?.apply {
            if (getPendantType() == WelfareMC.PENDANT_TYPE_OPERATION) {
                close()
            }
        }
        WelfareMS.get()?.onActivityFinish(WelfareMC.POSITION_PLAYER, this)
        mVideoLifecycle.onUIDestroy()
        AppActiveManager.removeListener(this)
        MonitorMS.get()?.stopPerformanceMonitor("二级播放器关闭")
        DzEventManager.removeObservers(getUiId())
        DrawAdManager.onDestroy()
        FollowTipManager.reset()

        if (mViewModel.getPreviewConfig() != null) {
            DzTrackEvents.get().operationExposureTE().apply {
                operationPosition("IAA解锁")
                operationName("IAA解锁")
                addParam("ADDescription", "当次解锁集数")
                addParam("UnlockNum", mViewModel.adUnlockedTimes)
            }.track()
        }
        if (WebMS.get()?.isCancelPreloadInPlayDetail() == true) {
            WebMS.get()?.preloadWelfareRewardAd(1)
        }

    }

    override fun finish() {
        super.finish()
        WelfareMS.get()?.onActivityFinish(WelfareMC.POSITION_PLAYER, this)
        FollowTipManager.reset()
        MonitorMS.get()?.stopPerformanceMonitor("二级播放器关闭")
        PriorityTaskManager.removeTaskByPageId(getPageId())
    }

    private fun initDrawAd() {
        if (!mViewModel.initDrawAd) {
            LogUtil.d(DetailMC.AD_TAG, "页面启动loadDrawAd")
            mViewModel.loadDrawAd(this, "NewPlayDetailActivity.initDrawAd()")
        }
    }

    private fun initWelfareAnchorAd() {
        if (welfareAnchorAdLoader?.initWelfareAnchorAd == false) {
            LogUtil.d(DetailMC.AD_TAG, "页面启动loadWelfareAnchorAd")
            welfareAnchorAdLoader?.preLoadAd(this, "页面启动")
        }
    }

    private fun preLoadRewardAd() {
        mViewModel.preloadRewardAd(this)
    }


    /**
     * 处理解锁结果
     */
    private fun ChapterUnlockBean.handlerUnlock() {
        curChapterCoins = chaptersCoins
//        //处理自动加入书架
//        mViewModel.mVideoInfo?.videoInfo?.let {
//            if (it.inBookShelf == false && isInBookShelf == true) {
//                mViewModel.favoriteLiveData.value = true
//                HomeME.get().addFavoriteSuccess().post(it.bookId)
//                FlutterMS.get()?.sendEventToFlutter(
//                    "inBookShelf",
//                    mapOf("value" to true, "bookId" to it.bookId)
//                )
//                TaskManager.ioTask {
//                    DBHelper.insertOrUpdateHistory(it.bookId, true)
//                }
//                it.inBookShelf = isInBookShelf
//            }
//        }
        mViewModel.mPreloadNum = preloadNum ?: 0
        LogUtil.d("VideoListVM", "handlerUnlock 解锁返回状态status--$status")
        //更新点赞人数，并更新当前章节信息
        mViewModel.mChapterInfoVo = mViewModel.getChapterInfoVo(mCurrentPosition)
        if ((!mViewModel.mChapterInfoVo?.m3u8720pUrl.isNullOrEmpty() || downLoadUrlCanPlay(mViewModel.mChapterInfoVo)) && mViewModel.mChapterInfoVo?.isCharge != DetailMC.CHAPTER_STATUS_PREVIEW) {
            mViewModel.lastPlayChapterInfoVo = mViewModel.mChapterInfoVo
        }
        mViewModel.mChapterInfoVo?.run {
            if (!BBaseKV.syncLikes) {
                lifecycleScope.launch(Dispatchers.IO) {
                    isLiked =
                        DBHelper.hasLikes(mViewModel.mVideoInfo?.videoInfo?.bookId + "_" + chapterId)
                    LogUtil.d(BBaseMC.TAG_LIKES, "handlerUnlock,查询数据库并显示点赞==$isLiked")
                    withContext(Dispatchers.Main) {
                        currentHolder?.layerFunction?.likesStatus(
                            isLiked == true,
                            getRealLikesNum()
                        )
                    }
                }
            } else {
                LogUtil.d(BBaseMC.TAG_LIKES, "handlerUnlock,显示点赞==$isLiked")
                currentHolder?.layerFunction?.likesStatus(isLiked == true, getRealLikesNum())
            }
        }
        when (status) {
            -1 -> {//解锁失败，激励视频
                if (preload == false) {
                    resetUnlockStatus(mCurrentPosition)
                }
            }

            1 -> {//解锁成功
                LogUtil.d(DetailMC.UNLOCK, "解锁成功")
//                mergeData(preload == true)
                // 切换分辨率导致的重新解锁。如果当前视频是暂停状态，不要直接执行mergeData。
                // 因为mergeData会触发清除播放器的缓存，原本暂停的视频就会变黑。
                // 改为在恢复播放的时候，重新执行mergeData。
                if (mViewModel.resolutionState == DetailMC.RESOLUTION_STATE_SWITCHING) {
                    if (mViewModel.switchingResolution?.scene == BBaseMC.SCENE_USER) {
                        if (resolutionChangingShowTime > 0) {
                            val delay =
                                1500 - (System.currentTimeMillis() - resolutionChangingShowTime)
                            TaskManager.delayTask(delay) {
                                ToastManager.showToast("${BBaseKV.selectedResolution}切换成功")
                            }
                        } else {
                            ToastManager.showToast("${BBaseKV.selectedResolution}切换成功")
                        }
                    }
                    mergeData(preload = false)
                } else {
                    mergeData(preload == true)
                }
            }

            2 -> {//扣费失败-余额不足去充值
                LogUtil.d(DetailMC.UNLOCK, "解锁失败：2 扣费失败-余额不足去充值")
                if (preload == false) {
                    resetUnlockStatus(mCurrentPosition)
                    checkWelfareCompUnLock()
                    checkSendVipCompUnLock()
                    // 主动弹出半屏支付弹窗
                    if (!mViewModel.hasAutoShowPayDialog && mViewModel.payListResp?.autoPop == 1) {
                        startPayActivity()
                    }
                }
            }

            3 -> {//可能为剧集下架，提示用户
                LogUtil.d(DetailMC.UNLOCK, "解锁失败：3 可能为剧集下架，提示用户")
                if (preload == false) {
                    resetUnlockStatus(mCurrentPosition)
                }
                mViewModel.deleteViewHistory()
                ToastManager.showToast("剧集下架")
            }
//            2 -> {//不需要付费-免费集 包括限免剧集，包括集缺失
//                LogUtil.d(DetailMC.UNLOCK, "不需要付费-免费集")
//                mergeData()
//            }
//
//            3 -> {//不需要付费-之前已经付费过
//                LogUtil.d(DetailMC.UNLOCK, "不需要付费-之前已经付费过")
//                mergeData()
//            }
//
//            4 -> {//扣费失败-余额不足去充值
//                LogUtil.d(DetailMC.UNLOCK, "解锁失败：4 扣费失败-余额不足去充值")
//                resetUnlockStatus(mCurrentPosition)
////                startPayActivity()
//            }

//            5 -> {//需要确认弹窗（余额足的情况）
//                LogUtil.d(DetailMC.UNLOCK, "解锁失败：5 需要确认弹窗（余额足的情况）")
//                startPayUnlockDialog(orderPage)
//            }

//            6 -> {//可能为剧集下架，提示用户
//                LogUtil.d(DetailMC.UNLOCK, "解锁失败：6 可能为剧集下架，提示用户")
//                resetUnlockStatus(mCurrentPosition)
//                mViewModel.deleteViewHistory()
//                ToastManager.showToast("剧集下架")
//            }
        }
    }

    /**
     * 跳转充值付费
     */
    private fun startPayActivity(byUser: Boolean = false) {
        LogUtil.d(DetailMC.UNLOCK, "二级播放页跳转充值中心")
        if (!byUser && isLandScape()) {
            return
        }
//        resetUnlockStatus(mCurrentPosition)
        if (mViewModel.mChapterInfoVo == null) {
            mViewModel.mChapterInfoVo = mViewModel.getChapterInfoVo(mCurrentPosition)
            if ((!mViewModel.mChapterInfoVo?.m3u8720pUrl.isNullOrEmpty() || downLoadUrlCanPlay(mViewModel.mChapterInfoVo)) && mViewModel.mChapterInfoVo?.isCharge != DetailMC.CHAPTER_STATUS_PREVIEW) {
                mViewModel.lastPlayChapterInfoVo = mViewModel.mChapterInfoVo
            }
            VideoTrackUtils.mChapterInfoVo = mViewModel.mChapterInfoVo
        }
        if (isLandScape() || mViewModel.payListResp?.payStyleList.isNullOrEmpty()) {
            RechargeMR.get().recharge().apply {
                sourceType = RechargeMC.RECHARGE_SOURCE_TYPE_READING_REMOVE_AD  // 来源
                bookId = mBookId
                chapterId = mViewModel.mChapterInfoVo?.chapterId
                omap = mViewModel.mVideoInfo?.videoInfo?.omap ?: mViewModel.mOmap
                unlockAmount = curChapterCoins ?: 0  // 解锁需要的看点数
                sourceExtend = mapOf(
                    "bookId" to mBookId as Any, "positionName" to "登录拦截" as Any
                )

                setCallback(getUiId(), object : RechargeIntent.ResultCallback {
                    override fun onPaySucceed() {
                        LogUtil.d(UNLOCK, "二级播放页收到充值成功的回调")
                        // 支付成功的回调
                        if (BBaseKV.totalAmount >= (curChapterCoins ?: 0)) {
                            mViewModel.mChapterInfoVo?.let {
                                it.chapterId?.let { id ->
                                    LogUtil.d(UNLOCK, "二级播放页尝试解锁")
                                    mViewModel.unlockChapter(id, false)
                                }
                            }
                        }
                    }
                })
            }.start()
        } else {
            mViewModel.hasAutoShowPayDialog = true
            val intent = RechargeMR.get().rechargeVipDialog().apply {
                payListResp = mViewModel.payListResp
                sourceType = RechargeMC.RECHARGE_SOURCE_TYPE_READING_REMOVE_AD  // 来源
                bookId = mBookId
                chapterId = mViewModel.mChapterInfoVo?.chapterId
                omap = mViewModel.mVideoInfo?.videoInfo?.omap ?: mViewModel.mOmap
                unlockAmount = curChapterCoins ?: 0  // 解锁需要的看点数
                sourceExtend = mapOf(
                    "bookId" to mBookId as Any, "positionName" to "登录拦截" as Any
                )

                setCallback(getUiId(), object : RechargeVipDialogIntent.ResultCallback {
                    override fun onPaySucceed() {
                        LogUtil.d(UNLOCK, "二级播放页收到充值成功的回调")
                        // 支付成功的回调
                        if (BBaseKV.totalAmount >= (curChapterCoins ?: 0)) {
                            mViewModel.mChapterInfoVo?.let {
                                it.chapterId?.let { id ->
                                    LogUtil.d(UNLOCK, "二级播放页尝试解锁")
                                    mViewModel.unlockChapter(id, false)
                                }
                            }
                        }
                    }
                })
            }
            PriorityTaskManager.addTask(
                PriorityDialogTask(
                    PriorityMC.TASK_PAY_DIALOG,
                    PageConstant.PAGE_ID_PLAYER,
                    PriorityMC.PRIORITY_DETAIL_PAY_DIALOG,
                    intent
                )
            )
            PriorityTaskManager.executeTask(byUser)
        }
    }

    /**
     * 合并播放地址到列表数据
     */
    private fun ChapterUnlockBean.mergeData(preload: Boolean) {
        lifecycleScope.launch(Dispatchers.Main) {
            if (mViewModel.resolutionState == DetailMC.RESOLUTION_STATE_SWITCHING) {
                LogUtil.d(TAG_RESOLUTION, "切换清晰度，后台获取地址成功")
                mViewModel.resolutionState = DetailMC.RESOLUTION_STATE_WAIT_TO_PLAY
                mViewModel.currentResolution = mViewModel.switchingResolution?.rate
                mViewModel.switchingResolution = null
                stopPlay()
            }
            DetailKV.totalAmount = remain ?: 0
            chapterInfo?.forEach { chapterInfoVo ->
                run loop@{
                    mViewModel.getDataList().forEachIndexed { index, infoVo ->
                        if (infoVo.chapterId == chapterInfoVo.chapterId) {
                            infoVo.apply {
                                if (isCharge == DetailMC.CHAPTER_STATUS_PAY || isCharge == DetailMC.CHAPTER_STATUS_TOMORROW) {
                                    isCharge = DetailMC.CHAPTER_STATUS_UNLOCK
                                    if (!mViewModel.mVideoInfo?.videoInfo?.bookId.isNullOrEmpty() && !chapterId.isNullOrEmpty()) {
                                        lifecycleScope.launch(Dispatchers.IO) {
                                            DownloadMS.get()?.updateChapterChargeStatus(
                                                mViewModel.mVideoInfo?.videoInfo?.bookId!!,
                                                chapterId!!,
                                                isCharge!!
                                            )
                                        }
                                    }
                                }
                                chapterImg = chapterInfoVo.chapterImg
                                content = chapterInfoVo.content
                                //重复添加地址播放器播放会触发多次onPrepared
                                if (m3u8720pUrl.isNullOrEmpty()) {
                                    mp4720pSwitchUrl = chapterInfoVo.content?.getUrlList()
                                    m3u8720pUrl = chapterInfoVo.content?.getUrl()
                                    mPlayerController.updatePlayerInfo(
                                        PlayerInfo(
                                            mViewModel.mVideoInfo?.videoInfo?.bookId,
                                            mViewModel.mVideoInfo?.videoInfo?.bookId + chapterInfoVo.chapterId,
                                            if (downLoadUrlCanPlay(this)) {
                                                downLoadUrl
                                            } else {
                                                m3u8720pUrl
                                            },
                                            chapterInfoVo.chapterIndex ?: 0,
                                            chapterInfoVo.chapterId
                                        )
                                    )
                                    LogUtil.d(
                                        DetailMC.TAG_LIKES,
                                        "合并数据:index==" + index + ";isLiked==" + this.isLiked + ";likesNum==" + this.likesNum + ";likesNumActual==" + this.likesNumActual
                                    )
                                    mPageAdapter.setItem(index, this)
                                    mPageAdapter.notifyItemChanged(
                                        index,
                                        if (mViewBinding.vp.currentItem == index) {
                                            DetailMC.PAYLOAD_UPDATE_ITEM_DATA_NOT_SHOW_COVER
                                        } else {
                                            DetailMC.PAYLOAD_UPDATE_ITEM_DATA
                                        }
                                    )
//                                    if (mViewBinding.vp.currentItem + 1 == index) {
//                                        mPageAdapter.notifyItemChanged(
//                                            index,
//                                            DetailMC.PAYLOAD_UPDATE_PLAYER_BIND
//                                        )
//                                    }
                                }
                                payType = <EMAIL>
                                chaptersPayType = <EMAIL>
                                mViewModel.ecpmToPrice(this)
                            }
                            if (index == mViewBinding.vp.currentItem) {
                                mViewModel.mChapterInfoVo = infoVo
                            }
                            return@loop
                        }
                        if (index == mViewBinding.vp.currentItem) {
                            mViewModel.mChapterInfoVo = infoVo
                        }
                    }
                }
            }
            mPlayerController.moveTo(
                mViewModel.mVideoInfo?.videoInfo?.bookId + if (mViewModel.mChapterInfoVo?.isAd == 1) {
                    mViewModel.lastPlayChapterInfoVo?.chapterId
                } else {
                    mViewModel.mChapterInfoVo?.chapterId
                }
            )

//            mViewModel.getDataList().forEach {
            if (mViewModel.resolutionState == DetailMC.RESOLUTION_STATE_WAIT_TO_PLAY) {
                mPlayerController.stopAllPlayer()
                activePause = false
                updateLayerPauseStatus(false)
                mViewModel.clearPause()
                //控制进度
                setStartTime(currentDuration)
                mViewModel.played = false
                mViewModel.loadingScene = ReadingTE.LOADING_SCENE_AUTO_CHANGE_PROGRESS
                //重新播放
                playerChangeTo(mViewModel.mChapterInfoVo?.m3u8720pUrl)
//                    // 由于前面 mListPlayerView.clear() 将播放列表中所有的内容清空，这里需要将本地下载的视频重新加回来
//                    if (downLoadUrlCanPlay(it)) {
//                        mListPlayerView.addUrl(it.downLoadUrl, it.chapterId)
//                    }
//                }
//            }
            } else {
                LogUtil.d(
                    TAG, "mergeData isPlaying:$isPlaying activePause:$activePause " +
                            "mCurrentPlayPosition:$mCurrentPlayPosition mCurrentPosition:$mCurrentPosition " +
                            "preload:$preload resolutionState:${mViewModel.resolutionState}"
                )
                // 解锁完成后自动播放的场景：
                if (((!isPlaying && mCurrentPlayPosition == mCurrentPosition && !activePause) || (mCurrentPlayPosition != mCurrentPosition) || mViewModel.resolutionState == DetailMC.RESOLUTION_STATE_WAIT_TO_PLAY) && !preload) {
                    if (downLoadUrlCanPlay(mViewModel.mChapterInfoVo) || mViewModel.mChapterInfoVo?.m3u8720pUrl?.isEmpty() == false) {
                        LogUtil.d(TAG, "mergeData后 startPlay")
                        startPlay(mCurrentPosition, true)
                    } else {
                        ToastManager.showToast("播放链接获取异常，请稍后重试！")
                    }
                } else {
                    LogUtil.d(TAG, "mergeData后不支持自动播放")
                }
            }
            // 试看解锁
            if (mViewModel.getPreviewConfig() != null && !preload) {
                val startIndex = startChapterIndex
                val endIndex = endChapterIndex
                if (startIndex != null && startIndex >= 0 && endIndex != null && endIndex >= 0) {
                    var positionStart = -1
                    var positionEnd = -1
                    mViewModel.getDataList().forEachIndexed { index, chapter ->
                        val chapterIndex = chapter.chapterIndex
                        if (chapterIndex != null && chapterIndex in startIndex..endIndex) {
                            chapter.isCharge = if (chapterIndex == mViewModel.mChapterInfoVo?.chapterIndex) {
                                DetailMC.CHAPTER_STATUS_PREVIEW_UNLOCK
                            } else {
                                if (positionStart == -1) {
                                    positionStart = index
                                }
                                DetailMC.CHAPTER_STATUS_UNLOCK
                            }
                            LogUtil.d(UNLOCK, "发现试看解锁的章节：$chapterIndex, index:$index")
                            positionEnd = index
                        }
                    }
                    LogUtil.d(UNLOCK, "免费试看解锁成功")
                    // 解锁成功，移除试看结束的限制
                    mViewModel.removePauseModel(VideoMC.PLAY_END_PREVIEW_FINISH)
                    previewNotAllowPause = false
                    currentHolder?.onEvent(DetailMC.EVENT_PREVIEW_UNLOCK_SUCCESS)
                    LogUtil.d(UNLOCK, "Preview unlock result: positionStart=$positionStart positionEnd=$positionEnd")
                    if (positionStart >= 0 && positionEnd >= 0) {
                        mViewBinding.vp.postDelayed({
                            mPageAdapter.notifyItemRangeChanged(
                                positionStart.coerceAtLeast(0),
                                positionEnd - positionStart + 1,
                                DetailMC.PAYLOAD_IS_CHARGE_CHANGED
                            )
                        }, 200)
                    }
                }
            }
        }
    }

    /**
     * 检查权限。
     * 检查的是 READ_PHONE_STATE 权限。用于读取IMEI
     */
    fun checkPhonePermission(position: Int) {
        //权限申请处理
        HomeMS.get()?.checkPhonePermission(this, position)
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray,
    ) {
        PermissionUtils.onRequestPermissionsResult(requestCode, permissions, grantResults)
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
    }

    /**
     * 打点。神策和大数据都是在这里
     * @param status Int：0开始播放，1停止播放；
     * @param renderingStartTime Long?
     */
    private fun track(
        status: Int, renderingStartTime: Long? = 0L,
        isPreloadPlayer: Boolean = false,
        isPlayerPrePrepared: Boolean = false,
        triggerScenario: String? = null,
    ) {
        var playEndScene: String? = null
        if (status == 1) {
            playEndScene = triggerScenario ?: mViewModel.getPauseModels().firstOrNull()
            stopPlayingTimer()
        }
        if (status == 1 && getPlayerDuration() <= 0) {
            // 当本次播放时长是0时，不执行上报。此时的上报是无意义的。
            return
        }
        if (status == 1 && startPlayTime > 0) {
            playingTime += (System.currentTimeMillis() - startPlayTime)
        }
        val latestPlayingTime = playingTime
        val playerPrePrepared = isPlayerPrePrepared
        // 修改播放中的状态
        isPlaying = status == 0
        val duration = if (getPlayerDuration() > 0) getPlayerDuration() else currentVideoDuration
        val curDuration = currentDuration
        val lastPlaySource = mViewModel.mOmap?.scene ?: mFirstPlaySource
        val endPart =
            if (mViewModel.mChapterInfoVo?.chapterId == mViewModel.mVideoInfo?.videoInfo?.maxChapterId) 1 else 0
        val canReportInvalidPlay = mViewModel.allowReportInvalidPlay
        val routeIntent = TrackUtil.copyVideoListIntent(mViewModel.routeIntent)
        val mOmap = TrackUtil.copyStrategyInfo(mViewModel.mOmap)
        val mChapterInfoVo = TrackUtil.copyChapterInfoVo(
            if (mViewModel.mChapterInfoVo?.isVideo() == true) {
                mViewModel.mChapterInfoVo
            } else {
                mViewModel.lastPlayChapterInfoVo
            }
        )
        val mVideoInfo = TrackUtil.copyVideoInfoVo(mViewModel.mVideoInfo?.videoInfo)
        val mVolume = getVolume()
        val mSpeed = getSpeed()
        val mAutoPlay = mViewModel.routeIntent?.autoPlay
        val mFlipType = if (isAutoSelectIndex != -1) "自动进入" else "手动划入"
        val mVideoStyle = if (mViewModel.isLandscapeVideo) {
            if (isLandScape()) "横屏" else "竖屏"
        } else ""
        val mIsPayVideo = mViewModel.mVideoInfo?.videoInfo?.isPayVideo() ?: false
        val mIsDownload = downLoadUrlCanPlay(mViewModel.mChapterInfoVo)
        val mMaxRemainBuffer = maxRemainBuffer
        val mPlayMode = if (mViewModel.playMode.value == PlayMode.IMMERSIVE) "沉浸式" else "常规"
        val  resolutionEnable= mViewModel.resolutionEnable
        val currentResolution= mViewModel.currentResolution
        val deviceInfoHelper = DeviceInfoHelper(this)
        val mIsFirstPlay = mViewModel.isFirstPlay
        if (realFirstPlaySource.isNullOrEmpty()) {
            mViewModel.mVideoInfo?.videoInfo?.let { videoInfo ->
                videoInfo.bookId?.let { bid ->
                    lifecycleScope.launch(Dispatchers.IO) {
                        if (realFirstPlaySource == null) {
                            val bookEntity = DzDataRepository.bookDao().queryByBid(bid)
                            realFirstPlaySource =
                                if (!bookEntity?.first_play_source.isNullOrEmpty()) {
                                    bookEntity?.first_play_source!!
                                } else {
                                    mFirstPlaySource ?: mViewModel.mOmap?.scene
                                }
                            if (bookEntity == null && status == 0) {
                                DzDataRepository.bookDao()
                                    .insertOrUpdateBooks(BookEntity(bid).apply {
                                        book_name = videoInfo.bookName
                                        first_play_source = realFirstPlaySource
                                        ext1Bean.ratingFlag =
                                            if (mViewModel.mVideoInfo?.userVideoVo?.scoreFlag == true) 1 else 0
                                    })
                            }
                        }
                        // 大数据打点
                        hiveTrack(
                            status,
                            duration,
                            latestPlayingTime,
                            curDuration, lastPlaySource, endPart, canReportInvalidPlay,
                            routeIntent, mOmap, mChapterInfoVo,
                            mVideoInfo,
                            playEndScene,
                        )
                        // 神策打点
                        sensorTrack(
                            status,
                            duration,
                            latestPlayingTime,
                            renderingStartTime,
                            isPreloadPlayer,
                            isPlayerPrePrepared = playerPrePrepared,
                            resolutionEnable = resolutionEnable,
                            currentResolution = currentResolution,
                            mVolume = mVolume,
                            mSpeed = mSpeed,
                            mCurrentDuration = curDuration,
                            mAutoPlay = mAutoPlay,
                            mFlipType = mFlipType,
                            mVideoStyle = mVideoStyle,
                            mIsPayVideo = mIsPayVideo,
                            mIsDownload = mIsDownload,
                            mMaxRemainBuffer = mMaxRemainBuffer,
                            mIntent = routeIntent,
                            chapterInfoVo = mChapterInfoVo,
                            mPlayMode = mPlayMode,
                            mVideoInfo = mVideoInfo,
                            deviceInfoHelper  = deviceInfoHelper,
                            isFirstPlay = mIsFirstPlay,
                            triggerScenario = playEndScene,
                        )
                    }
                }
            }
        } else {
            // 大数据打点
            hiveTrack(
                status,
                duration,
                latestPlayingTime,
                curDuration, lastPlaySource, endPart, canReportInvalidPlay,
                routeIntent, mOmap, mChapterInfoVo,
                mVideoInfo,
                playEndScene,
            )
            // 神策打点
            sensorTrack(
                status,
                duration,
                latestPlayingTime,
                renderingStartTime,
                isPreloadPlayer,
                isPlayerPrePrepared = playerPrePrepared,
                resolutionEnable = resolutionEnable,
                currentResolution = currentResolution,
                mVolume = mVolume,
                mSpeed = mSpeed,
                mCurrentDuration = curDuration,
                mAutoPlay = mAutoPlay,
                mFlipType = mFlipType,
                mVideoStyle = mVideoStyle,
                mIsPayVideo = mIsPayVideo,
                mIsDownload = mIsDownload,
                mMaxRemainBuffer = mMaxRemainBuffer,
                mIntent = routeIntent,
                chapterInfoVo = mChapterInfoVo,
                mPlayMode = mPlayMode,
                mVideoInfo = mVideoInfo,
                deviceInfoHelper  = deviceInfoHelper,
                isFirstPlay = mIsFirstPlay,
                triggerScenario = playEndScene
            )
        }
        if (status == 1) {  // 如果是停止播放，需要将缓存数据清空
            startPlayTime = 0L
            playingTime = 0L
            //保存播放进度到数据库 IAA视频
            if (mChapterInfoVo.isCharge != DetailMC.CHAPTER_STATUS_PREVIEW) {
                mViewModel.updateViewHistoryProgress(
                    curDuration,
                    duration,
                    mVideoInfo,
                    mChapterInfoVo,
                )
            }
        }
    }

    private fun hiveTrack(
        status: Int, duration: Long, playingTime: Long,
        curDuration: Long, lastPlaySource: String?, endPart: Int, canReportInvalidPlay: Boolean,
        routeIntent: VideoListIntent, mOmap: StrategyInfo, mChapterInfoVo: ChapterInfoVo,
        mVideoInfo: VideoInfoVo, triggerScenario: String? = null
    ) {
        if (mViewModel.isFocusVideoPlay != null) {
            TrackUtil.hiveLog(
                playStatus = status,
                mFirstPlaySource = realFirstPlaySource,
                mLastPlaySource = lastPlaySource,
                currentDuration = curDuration,
                duration = duration,
                playingTime = playingTime,
                isEndPart = endPart,
                focusVideoPlay = mViewModel.isFocusVideoPlay!!,
                canReportInvalidPlay = canReportInvalidPlay,
                intent = routeIntent,
                cOmap = mOmap,
                chapterInfoVo = mChapterInfoVo,
                videoInfo = mVideoInfo,
                triggerScenario = triggerScenario,
            )
        } else {
            lifecycleScope.launch(Dispatchers.IO) {
                val historyEntity = DBHelper.queryBook(mVideoInfo.bookId)
                withContext(Dispatchers.Main) {
                    mViewModel.isFocusVideoPlay =
                        if (historyEntity?.focusVideoPlay == null) "0" else historyEntity.focusVideoPlay.toString()
                    TrackUtil.hiveLog(
                        playStatus = status,
                        mFirstPlaySource = realFirstPlaySource,
                        mLastPlaySource = lastPlaySource,
                        currentDuration = curDuration,
                        duration = duration,
                        playingTime = playingTime,
                        isEndPart = endPart,
                        focusVideoPlay = mViewModel.isFocusVideoPlay!!,
                        canReportInvalidPlay = canReportInvalidPlay,
                        intent = routeIntent,
                        cOmap = mOmap,
                        chapterInfoVo = mChapterInfoVo,
                        videoInfo = mVideoInfo,
                        triggerScenario = triggerScenario,
                    )
                }
            }
        }
    }

    private fun sensorTrack(
        status: Int,
        duration: Long,
        playingTime: Long,
        renderingStartTime: Long?,
        isPreloadPlayer: Boolean = false,
        isPlayerPrePrepared: Boolean = false,
        resolutionEnable: Boolean,
        currentResolution: String?,
        mVolume: Float,
        mSpeed: Float,
        mCurrentDuration: Long,
        mAutoPlay: Boolean?,
        mFlipType: String,
        mVideoStyle: String,
        mIsPayVideo: Boolean,
        mIsDownload: Boolean,
        mMaxRemainBuffer: Long,
        mIntent: VideoListIntent,
        chapterInfoVo: ChapterInfoVo,
        mPlayMode: String,
        mVideoInfo: VideoInfoVo,
        deviceInfoHelper: DeviceInfoHelper,
        isFirstPlay: Boolean,
        triggerScenario: String? = null,
    ) {
        TrackUtil.sensorPlaying(
            playStatus = status,
            duration = duration,
            volume = mVolume,
            speed = mSpeed,
            currentDuration = mCurrentDuration,//播放时长
            firstPlaySource = realFirstPlaySource,
            autoPlay = mAutoPlay,
            reckonByTime = playingTime,//累计播放时长
            flipType = mFlipType,
            videoStyle = mVideoStyle,  // 横版、竖版
            isPayVideo = mIsPayVideo, //是否付费剧
            renderingStartTime = renderingStartTime ?: 0,//启播时长
            isDownload = mIsDownload,//是否播放下载剧
            isMultipleInstances = true,
            maxRemainBuffer = mMaxRemainBuffer,
            isPreloadPlayer = isPreloadPlayer,
            isPlayerPrePrepared = isPlayerPrePrepared,
            intent = mIntent,
            mChapterInfoVo = chapterInfoVo,
            playMode = mPlayMode,
            videoInfo = mVideoInfo,
            resolutionEnable = resolutionEnable,
            currentResolution = currentResolution,
            deviceInfoHelper = deviceInfoHelper,
            pageInitFirstPlay = isFirstPlay,
            triggerScenario = triggerScenario,
            trackInfo = mViewModel.videoTrackInfo,
        )
    }

    //**********************************终章推荐相关内容
    private var timeOutTask: Task? = null//章末推荐倒计时
    var remainTime: Int = 0//暂停时还剩下多少时间没有走
    private var timingDuration: Int = DetailMC.FINAL_RECOMMEND_STYLE1_TIME//倒计时时间,单位s
    private fun finalChapterPrompt(data: RecommendVideoInfo?) {
        data?.from = mViewModel.mVideoInfo?.videoInfo
        FollowTipManager.markFollowTipHasShown(true)
        LogUtil.d(TAG, "显示章末推荐弹窗")
        guideDelegate?.invisible()
        showFinalChapterRecommend(data)
        if (timeOutTask == null) {
            createTimeOutTask(timingDuration + 1)
        }
    }

    fun finnalNewRecommend() {
        if (mViewModel.isInLastPage) {
            val data = mViewModel.recommendLiveData.value
            LogUtil.d(TAG, "recommendLiveData  data = $data")
            if (!data?.dataList.isNullOrEmpty()) {
                DetailRecommendUtil.passConfig()
                FinalRecommendManager.autoSkip(
                    mViewModel.getVideoInfo(),
                    data?.dataList?.firstOrNull(),
                    isLandscape = (isLandScape() && mViewModel.isLandscapeVideo)
                )
            } else {
                timingDuration = DetailMC.FINAL_RECOMMEND_STYLE1_TIME
                mViewBinding.clFinalChapter1.visibility = VISIBLE
                mViewBinding.clFinalChapter1.bindData(
                    RecommendVideoInfo(
                        false,
                        "",
                        null,
                        false,
                        null,
                        mViewModel.mVideoInfo?.videoInfo,
                        isLandScape()
                    )
                )
                createTimeOutTask(timingDuration + 1)
            }
        }
    }

    private fun showFinalChapterPrompt(data: RecommendVideoInfo?) {
        if (DetailRecommendUtil.getDetailRecommendSwitch() == 0) {
            finalChapterPrompt(data)
        } else {
            finnalNewRecommend()
        }
    }

    /**
     * 当前是否显示了剧末内容
     * 剧末有很多内容，只要有一个显示了就是算显示了。
     * @return Boolean：true显示了，false未显示
     */
    private fun isFinalChapter(): Boolean {
        return mViewBinding.clFinalChapter1.visibility == VISIBLE || mViewBinding.clFinalChapter2.visibility == VISIBLE || mViewBinding.clFinalChapter3.visibility == VISIBLE
    }

    /**
     *  刷新终章推荐
     * @param isLandscape 是否是横屏  true：横屏 false：竖屏
     */
    private fun updateFinalChapter(isLandscape: Boolean) {
        if (isFinalChapter()) {
            mViewBinding.clFinalChapter2.updateView(isLandscape)
            mViewBinding.clFinalChapter1.updateView(isLandscape)
        }
    }

    /**
     *  取消终章推荐计时器
     */
    private fun cancelTimeOutTask() {
        if (timeOutTask != null) {
            noSliding()
            enableGesture(true)
            timeOutTask?.cancel()
            timeOutTask = null
        }
    }

    private fun noSliding() {
        if (!isFinalChapter()) {
            startScroll()
        }
    }

    /**
     * 恢复章末推荐、剧末承接的倒计时操作
     */
    private fun resumeTimeOutTask() {
        if (isDramaListDialogShow) {
            return
        }
        if (mViewBinding.clFinalChapter1.visibility == VISIBLE) {
            createTimeOutTask(remainTime)
        } else if (mViewBinding.clFinalChapter2.visibility == VISIBLE) {
            createTimeOutTask(remainTime)
        } else if (mViewBinding.clFinalChapter3.visibility == VISIBLE) {
            createTimeOutTask(remainTime)
        }
    }

    private fun createTimeOutTask(totalTimes: Int) {
        timeOutTask = TaskManager.intervalTask(totalTimes, 0, 1000) { time ->
            remainTime = totalTimes - time
            if (remainTime - 1 > 0) {
                mViewBinding.clFinalChapter1.updateTime((remainTime - 1).toString())
                mViewBinding.clFinalChapter2.updateTime((remainTime - 1).toString())
                mViewBinding.clFinalChapter3.updateTime((remainTime - 1).toString())
            }
            if (time == totalTimes - 1) {
                if (mViewBinding.clFinalChapter1.visibility == VISIBLE) {
                    HomeME.get().finalChapterJump().post(mViewModel.mVideoInfo?.videoInfo?.bookId)
                    mViewModel.mBackToRecommend = true
                    mViewModel.synPlayProgress(currentDuration)
                    finish()
                } else if (mViewBinding.clFinalChapter2.visibility == VISIBLE) {
                    mViewBinding.clFinalChapter2.finish()
                } else if (mViewBinding.clFinalChapter3.visibility == VISIBLE) {
                    mViewBinding.clFinalChapter3.finish()
                }
            }
        }
        stopScroll()
        enableGesture(false)
    }

    private fun showFinalChapterRecommend(data: RecommendVideoInfo?) {
        when (data?.dataList?.size) {
            1 -> {
                mViewBinding.clFinalChapter2.visibility = VISIBLE
                timingDuration = DetailMC.FINAL_RECOMMEND_STYLE2_TIME
                mViewBinding.clFinalChapter2.mActionListener =
                    object : FinalRecommendStyle2.ViewActionListener {
                        override fun onExpose(from: VideoInfoVo?, data: VideoInfoVo?) {
                            FinalRecommendManager.onExpose(from, data)
                        }

                        override fun onClick(from: VideoInfoVo?, data: VideoInfoVo?) {
                            if (mViewModel.mBackToRecommend == true) {
                                mViewModel.mBackToRecommend = false
                                FinalRecommendManager.onClick(
                                    from,
                                    data,
                                    true,
                                    isLandscape = (isLandScape() && mViewModel.isLandscapeVideo)
                                )
                            } else {
                                FinalRecommendManager.onClick(
                                    from,
                                    data,
                                    isLandscape = (isLandScape() && mViewModel.isLandscapeVideo)
                                )
                            }
                            mViewModel.synPlayProgress(currentDuration)
                        }

                        override fun autoSkip(from: VideoInfoVo?, data: VideoInfoVo?) {
                            if (mViewModel.mBackToRecommend == true) {
                                mViewModel.mBackToRecommend = false
                                FinalRecommendManager.autoSkip(
                                    from,
                                    data,
                                    true,
                                    isLandscape = (isLandScape() && mViewModel.isLandscapeVideo)
                                )
                            } else {
                                FinalRecommendManager.autoSkip(
                                    from,
                                    data,
                                    isLandscape = (isLandScape() && mViewModel.isLandscapeVideo)
                                )
                            }
                            mViewModel.synPlayProgress(currentDuration)
                        }
                    }
                mViewBinding.clFinalChapter2.bindData(data)
            }

            4 -> {
                mViewBinding.clFinalChapter3.visibility = VISIBLE
                timingDuration = DetailMC.FINAL_RECOMMEND_STYLE3_TIME
                mViewBinding.clFinalChapter3.mActionListener =
                    object : FinalRecommendStyle3.ViewActionListener {
                        override fun onExpose(from: VideoInfoVo?, data: VideoInfoVo?) {
                            FinalRecommendManager.onExpose(from, data)
                        }

                        override fun onClick(from: VideoInfoVo?, data: VideoInfoVo?) {
                            if (mViewModel.mBackToRecommend == true) {
                                mViewModel.mBackToRecommend = false
                                FinalRecommendManager.onClick(
                                    from,
                                    data,
                                    true,
                                    isLandscape = (isLandScape() && mViewModel.isLandscapeVideo)
                                )
                            } else {
                                FinalRecommendManager.onClick(
                                    from,
                                    data,
                                    isLandscape = (isLandScape() && mViewModel.isLandscapeVideo)
                                )
                            }
                            mViewModel.synPlayProgress(currentDuration)
                        }

                        override fun autoSkip(from: VideoInfoVo?, data: VideoInfoVo?) {
                            if (mViewModel.mBackToRecommend == true) {
                                mViewModel.mBackToRecommend = false
                                FinalRecommendManager.autoSkip(
                                    from,
                                    data,
                                    true,
                                    isLandscape = (isLandScape() && mViewModel.isLandscapeVideo)
                                )
                            } else {
                                FinalRecommendManager.autoSkip(
                                    from,
                                    data,
                                    isLandscape = (isLandScape() && mViewModel.isLandscapeVideo)
                                )
                            }
                            mViewModel.synPlayProgress(currentDuration)
                        }
                    }
                mViewBinding.clFinalChapter3.bindData(data)
            }

            else -> {
                timingDuration = DetailMC.FINAL_RECOMMEND_STYLE1_TIME
                mViewBinding.clFinalChapter1.visibility = VISIBLE
                mViewBinding.clFinalChapter1.bindData(
                    RecommendVideoInfo(
                        false, "", null,false, null, mViewModel.mVideoInfo?.videoInfo, isLandScape()
                    )
                )
            }
        }
        mViewModel.setPlayMode(PlayMode.IMMERSIVE)
        onVideoPageShow(true)
    }

    var mDrawAdCanScroll: Boolean = true
    override fun onBackPressAction() {
        //返回上一页 隐藏菜单提示
        mViewBinding.comVipTips.hideMenuTips()
        FollowTipManager.mRealFirstPlaySource = realFirstPlaySource
        if (isPauseAdShow()) {
            cancelPauseAd(resumePlay = false, notifySdkAdClose = true)
        } else if (mViewModel.playMode.value == PlayMode.IMMERSIVE) {
            cancelImmersive("back button press")
            startImmersiveTimer("沉浸式中 点击返回按钮")
        } else if (isLandScape()) {
            //横屏模式
            //当前正在显示沉浸式广告，屏蔽物理返回键 切换为 竖屏模式
            if (!isDrawAdPageShowing) {
                changedToPortrait()
            }

        } else {
            mViewModel.allowReportInvalidPlay = true
            if (isDrawAdPageShowing) {
                close()
            } else if (exitDelegate != null) {
                //有挽留弹窗，优先显示挽留弹窗
                exitDelegate?.apply {
                    finalRcmdShowing = isFinalChapter()
                    visible()
                    if (!isVisible) {
                        close()
                    }
                }
            } else if (FollowTipManager.canShowFollowDialog()) {
                //没有挽留弹窗，显示加追弹窗
                FollowTipManager.showFollowDialog(onSureCallback = {
                    mViewModel.addShelf(
                        FollowTipManager.bookId,
                        mViewModel.mChapterInfoVo?.chapterId,
                        FollowTipManager.mRealFirstPlaySource,
                        mViewModel.mOmap,
                        FollowSourceType.FOLLOW_DIALOG
                    )
                }, onDismissCallback = {
                    mViewModel.closeEvent.postValue(true)
                })
            } else {
                close()
            }
        }
    }

    override fun close(trackEndPlay: Boolean?) {
        coroutineScope?.cancel()
        coroutineScope = null
        mViewModel.synPlayProgress(currentDuration)
        checkNewUser()
        mViewModel.pausePlay(VideoMC.PLAY_END_EXIT)
        if (trackEndPlay == true) {
            track(1, triggerScenario = VideoMC.PLAY_END_EXIT)
        }
        // 检查是否是投放进入的。如果是投放要跳转到福利页
        if (!mViewModel.jumpTheaterImg.isNullOrEmpty()
            && mViewModel.routeIntent?.firstTierPlaySource == BBaseMC.SOURCE_TF
            && (mViewModel.mChapterInfoVo?.isCharge == 1 || mViewModel.mChapterInfoVo?.isCharge == 4)) {
            (PriorityTaskManager.getTask(PriorityMC.TASK_THEATRE_RETAIN) as? TheatreRetainDialogTask)?.apply {
                updateImageUrl(mViewModel.jumpTheaterImg)
                status = PriorityConstants.STATUS_READY
            }
            // 跳转剧场
            MainMR.get().main().apply {
                selectedTab = MainIntent.TAB_THEATRE
            }.start()
        } else {
            PriorityTaskManager.removeTask(PriorityMC.TASK_THEATRE_RETAIN)
        }
        finish()
    }
    private fun checkNewUser() {
        //在退出二级页的时候做校验，
        // 看是否要给mainActivity发消息展示气泡
        LogUtil.d(TAG, "多播播放器退出")
        if (needFollowBubble == 1 && followBubbleStatus == 0) {
            followBubbleStatus = 1 //需要展示
        }
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        if (keyCode == KeyEvent.KEYCODE_BACK) {//返回键
            DzTrackEvents.get().sysBackBtnClick()
                .title(getPageName())
                .bookId(mViewModel.mVideoInfo?.videoInfo?.bookId)
                .bookName(mViewModel.mVideoInfo?.videoInfo?.bookName)
                .chapterId(mViewModel.mChapterInfoVo?.chapterId)
                .chapterName(mViewModel.mChapterInfoVo?.chapterName)
                .track()
        }
        return super.onKeyDown(keyCode, event)
    }

    private fun cancelPauseAd(resumePlay: Boolean, notifySdkAdClose: Boolean) {
        if (!isLandScape()) {
            visibleBannerAd()
        }
        hidePauseAd()
        if (notifySdkAdClose) {
            mViewBinding.comPauseAd.notifyAdClose()
        }

        VideoTrackUtil.trackPauseCancel(
            mViewModel.videoTrackInfo, VideoTrackUtil.PAUSE_SCENE_USER, true
        )
        if (resumePlay) {
            onPauseClick()
        }
        if (canShowPendantVisible()) {
            LogUtil.d(OperationMC.TAG_PENDANT, "cancelPauseAd show pendant")
            welfareWidget?.visibility = VISIBLE
        }
    }

    private fun hideFinalChapterRecommend() {
        mViewBinding.clFinalChapter1.visibility = GONE
        mViewBinding.clFinalChapter2.visibility = GONE
        mViewBinding.clFinalChapter3.visibility = GONE
    }

    //**********************************终章推荐相关内容
    private fun restoreWelfare(config: Configuration) {
        welfareWidget?.run {
            Handler(Looper.getMainLooper()).post {
                (parent as? ViewGroup)?.removeView(this)
                if (config.orientation != Configuration.ORIENTATION_LANDSCAPE && canShowPendantVisible()) {
                    addWelfareWidget(false)
                }
            }
        }
    }

    /**
     * 添加活动中心挂件
     */
    private fun addWelfareWidget(
        needCheck: Boolean = true,
        exposureReport: Boolean = false,
        autoShow: Boolean = true
    ) {
        WelfareMS.get()?.apply {
            if (!isLandScape() && (!needCheck || whetherAddWidget(
                    WelfareMC.POSITION_PLAYER, this@NewPlayDetailActivity
                ))
                && !isInPip()
            ) {
                var topMargin = 0
                var bottomMargin = 0
                mViewModel.playerPendantConfig?.let { config ->
                    if (config.actType == 4) {  // 进度条挂件
                        if (config.pendantStyle == 1) {  // 老版本样式
                            if (DetailMC.likeBtnY > 0) {
                                bottomMargin = DetailMC.likeBtnY + ScreenUtil.dip2px(
                                    this@NewPlayDetailActivity,
                                    24
                                )
                            } else {
                                LogUtil.e(
                                    OperationMC.TAG_PENDANT,
                                    "老版本进度条挂件，点赞按钮高度未知"
                                )
                                return
                            }
                        } else if (config.pendantStyle == 2) {
                            // 新版本
                            topMargin = ScreenUtil.dip2px(this@NewPlayDetailActivity, 113)
                        } else if (config.pendantStyle == 4) {
                            //拖拽版本样式
                            topMargin = ScreenUtil.dip2px(this@NewPlayDetailActivity, marginTop)
                        } else {
                            //动效版本样式
                            topMargin = ScreenUtil.dip2px(this@NewPlayDetailActivity, marginTop)
                        }
                    }
                }
                LogUtil.d(
                    OperationMC.TAG_PENDANT,
                    "likeBtnY:${DetailMC.likeBtnY} topMargin:$topMargin bottomMargin:$bottomMargin"
                )
                addWelfareWidget(
                    WelfareMC.POSITION_PLAYER,
                    this@NewPlayDetailActivity,
                    object : FloatWidgetListener by noOpDelegate() {
                        override fun onLoadSuccess(widget: View, config: WelfarePendantConfigVo?) {
                            LogUtil.d(
                                OperationMC.TAG_PENDANT,
                                "挂件回调成功 onLoadSuccess  welfareWidget:${widget==null}"
                            )
                            welfareWidget = widget
                            if (isLandScape()) {
                                (widget.parent as? ViewGroup)?.removeView(widget)
                                return
                            }
                            needAddWelfareWidgetWhenPort = false
                            if (mViewModel.playMode.value == PlayMode.IMMERSIVE) {
                                widget.visibility = GONE
                            }
                            if (welfareWidget is IProgressDragPendantComp) {
                                //禁用拖拽
                                (welfareWidget as? IProgressDragPendantComp)?.run {
                                    setIsDragEnable(false)
                                    if (BBaseKV.playFirstShow != SimpleDateFormat(
                                            "yyyy-MM-dd",
                                            Locale.CHINA
                                        ).format(Date())
                                    ) {
//                  每日首次冷启动显示侧滑引导
                                        mViewBinding.tcIcons.startPlay()
                                        lifecycleScope.delayTask(2000) {
                                            setIsDragEnable(true)
                                        }
                                    } else {
                                        setIsDragEnable(true)
                                    }
                                }
                            }
                            if (welfareWidget is IProgressPendantComp) {
                                refreshProgressPendant()
                                updatePendantState()
                            } else {
                                widget.visibility = VISIBLE
                            }
                            if (exposureReport) {
                                val needWaitReward =
                                    welfareWidget is IProgressPendantComp && mViewModel.rewardStatus.value == null
                                if (!needWaitReward) {
                                    reportPendantExposure(config)
                                } else {
                                    waitRewardStatusToReport = true
                                }
                            }
                        }

                        override fun onClick(
                            view: View, viewName: String,
                            config: WelfarePendantConfigVo?,
                        ) {
                            config?.let {
                                DzTrackEvents.get().operationClickTE().apply {
                                    operationID(config.id?.toString() ?: "")
                                    operationPosition(config.operationPosition)
                                    operationName(config.name)
                                    if (welfareWidget is IProgressDynamicPendantComp) {
                                        buttonName("福利中心挂件")
                                        pendantPage("播放页")
                                    }
                                    operationType(config.operationType)
                                    userTacticInfo(config.userTacticsVo)
                                    bookId(config.bookId)
                                    bookName(config.bookName)
                                    positionName(viewName)
                                    if (welfareWidget is IProgressPendantComp) {
                                        when (mViewModel.rewardStatus.value) {
                                            WelfareReward.STATUS_INCOMPLETE -> {
                                                buttonContent("coins_noreward")
                                            }

                                            WelfareReward.STATUS_UNCLAIMED -> {
                                                buttonContent("coins_reward")
                                            }

                                            WelfareReward.STATUS_COMPLETED -> {
                                                buttonContent("coins_complete")
                                            }
                                        }
                                    }
                                }.track()
                            }
                        }

                        override fun onClose(view: View) {
                            welfareWidget = null
                        }

                        override fun onMoveFloat(view: View,supClose:Boolean) {
                            widgetMove(view, supClose)
                        }

                        override fun onMoveStart(view: View,supClose:Boolean) {
                            setImmersiveEnable(false, "挂件开始拖动")
                            widgetMoveStart(view, supClose)
                        }

                        override fun onMoveEnd(view: View,supClose:Boolean) {
                            setImmersiveEnable(true, "挂件结束拖动")
                            widgetMoveEnd(view, supClose)
                        }
                    },
                    autoShow = autoShow,
                    topMargin = topMargin,
                    bottomMargin = bottomMargin
                )
            } else {
                LogUtil.e(OperationMC.TAG_PENDANT, "挂件不符合条件，不展示挂件")
            }
        } ?: let {
            LogUtil.e(OperationMC.TAG_PENDANT, "挂件配置为空，不展示挂件")
        }
    }

    private var hasTriggeredCondition = false
    //  挂件拖动
    private fun widgetMoveStart(view: View, supClose: Boolean) {
        if (supClose) {
            if (isFinishing || isDestroyed) return
            hasTriggeredCondition = false
            if ((welfareWidget as? IProgressDynamicPendantComp)?.getPendantStatus() != true) {
                mViewBinding.clWelfareClose.apply {
                    translationY = 0f
                    visibility = GONE
                }
            }
        }
    }

    //挂件拖动结束
    private fun widgetMoveEnd(view: View, supClose: Boolean) {
        if (supClose) {
            if (isFinishing || isDestroyed) return
            hasTriggeredCondition = false
            if ((welfareWidget as? IProgressDynamicPendantComp)?.getPendantStatus() == true) {
                if (ViewUtils.isViewsIntersecting(view, mViewBinding.clWelfareClose)) {
                    (welfareWidget as? PendantComp)?.close()
                }
            }
            mViewBinding.clWelfareClose.apply {
                translationY = 0f
                visibility = GONE
            }
        }
    }

    //挂件拖动中
    private fun widgetMove(view: View, supClose: Boolean) {
        if (supClose) {
            if (isFinishing || isDestroyed) return
            if ((welfareWidget as? IProgressDynamicPendantComp)?.getPendantStatus() == true) {
                val intersecting =
                    ViewUtils.isViewsIntersecting(view, mViewBinding.clWelfareClose)
                mViewBinding.tvWelfareClose.text =
                    if (intersecting) "松手收起挂件" else "拖动至此收起挂件"
                mViewBinding.ivWelfareCloseH.visibility =
                    if (intersecting) GONE else VISIBLE
                mViewBinding.ivWelfareCloseS.visibility =
                    if (intersecting) VISIBLE else GONE
            }
            val screenHeight = ScreenUtil.getScreenHeight()
            val viewLocation = IntArray(2)
            view.getLocationOnScreen(viewLocation)
            val thresholdPx = ScreenUtil.dip2px(this, 200f)
            val viewBottom = viewLocation[1] + view.height
            val triggerPosition = screenHeight - thresholdPx
            if (!hasTriggeredCondition && viewBottom > triggerPosition) {
                hasTriggeredCondition = true
                mViewBinding.clWelfareClose.apply {
                    translationY = height.toFloat()
                    visibility = View.INVISIBLE
                    post {
                        visibility = View.VISIBLE
                        ObjectAnimator.ofFloat(
                            this,
                            "translationY",
                            height.toFloat(),
                            0f
                        ).apply {
                            duration = 300
                            interpolator = DecelerateInterpolator()
                            start()
                        }
                    }
                }
            }
        }
    }

    private var needOpenWelfDialog = false

    private fun openWelfDialog() {
        mViewBinding.vp.postDelayed(
            {
                val holder =
                    ViewPager2Helper.findViewHolderByPosition(mViewBinding.vp, mCurrentPosition)
                if (!isPauseAdShow() && holder is NewDetailVideoViewHolder && (!isLandScape() || (isLandScape() && !activePause))) {
                    needOpenWelfDialog = if (mViewModel.canPlay()) {
                        WelfareMS.get()?.openWelfDialog()
                        false
                    } else {
                        true
                    }
                }
            }, 200
        )
    }

    private var needOpenPushDialog = false

    /**
     * 提醒用户打开通知开关的弹窗
     */
    private fun openPushDialog() {
        val holder = ViewPager2Helper.findViewHolderByPosition(mViewBinding.vp, mCurrentPosition)
        needOpenPushDialog = if (mViewModel.canPlay() && holder !is AdVideoViewHolder) {
            BCommonMS.get()?.openPush(
                BCommonMC.PUSH_SHOW_LOCATION_DETAIL, BCommonMC.PUSH_SHOW_TYPE_PLAY, true
            )
            false
        } else {
            true
        }
    }

    private var pauseAdShowing = false
    private fun showPauseAd() {
        val adId = mViewModel.mVideoInfo?.getPauseAdConfig()?.adId
        val blockConfigId = mViewModel.mVideoInfo?.getPauseAdConfig()?.blockConfigId
        if (mViewModel.getPauseAd() != null) {
            if (mViewBinding.comPauseAd.canShowPauseAd()) {
                LogUtil.d(
                    DetailMC.AD_TAG,
                    "二级播放页暂停-广告流量请求have pauseAd,pos=${AdTE.PAUSE_110} adId=$adId"
                )
                mViewModel.senADTrafficReachEvent(AdTE.PAUSE_110, adId ?: "", blockConfigId)
            } else {
                LogUtil.d(DetailMC.AD_PAUSE_TAG, "未达到暂停广告展示间隔，不显示广告")
                return
            }
            if (mViewModel.getPauseAd() != null) {
                mViewModel.getPauseAd()?.let { pauseAd ->
                    try {
                        mViewBinding.comPauseAd.show(pauseAd)
                        pauseAdShowing = true
                        LogUtil.d(DetailMC.TAG_IMMERSIVE, "showPauseAd hide welfare widget")
                        welfareWidget?.visibility = GONE
                        goneBannerAd()
                    } catch (e: Exception) {
                        pauseAd.isShow = true
                        LogUtil.d(DetailMC.AD_PAUSE_TAG, "广告显示异常")
                        visibleBannerAd()
                        hidePauseAd()
                        loadPauseAd()
                    }
                }
            } else {
                LogUtil.d(DetailMC.AD_PAUSE_TAG, "没有广告，无法展示，开始预加载")
                visibleBannerAd()
                hidePauseAd()
                loadPauseAd()
            }
        } else {
            LogUtil.d(DetailMC.AD_PAUSE_TAG, "没有广告，无法展示，开始预加载")
            visibleBannerAd()
            hidePauseAd()
            loadPauseAd()
            if (mViewBinding.comPauseAd.canShowPauseAd()) {
                LogUtil.d(
                    DetailMC.AD_TAG,
                    "二级播放页暂停-广告流量请求pauseAd is null,pos=${AdTE.PAUSE_110} adId=$adId"
                )
                mViewModel.senADTrafficReachEvent(AdTE.PAUSE_110, adId ?: "", blockConfigId)
            }
        }
    }

    /**
     * 加载暂停广告
     */
    private fun loadPauseAd() {

        mViewBinding.comPauseAd.loadPauseAd(mViewModel)

    }

    private fun hidePauseAd() {
        pauseAdShowing = false
        mViewBinding.comPauseAd.hide()
    }

    /**
     *  显示底部banner广告
     */
    private fun visibleBannerAd() {
        if (!isInPip()) {
            bannerAdPresenter?.proactiveResume()
        }
    }

    /**
     *  隐藏底部banner广告
     */
    private fun goneBannerAd() {
        bannerAdPresenter?.proactivePause()
    }

    private val bannerAdPresenter: NewBannerAdPresenter? by lazy {
        val bottomAdVo = mViewModel.mVideoInfo?.bottomAdVo
        if (bottomAdVo != null) {
            NewBannerAdPresenter(
                this,
                mViewModel,
                bottomAdVo,
                mViewBinding.flAdBottom,
                mViewBinding.bottomRemoveAdComp,
                callback = object : NewBannerAdPresenter.Callback {
                    override fun isPreviewChapter(): Boolean {
                        return mViewModel.mChapterInfoVo?.isCharge == DetailMC.CHAPTER_STATUS_PREVIEW
                    }
                },
            )
        } else {
            null
        }
    }

    private val orientationManager by lazy { OrientationManager(this) }

    // 进入沉浸式的倒计时器
    private var immersiveTimer: Task? = null
    private fun startImmersiveTimer(scene: String) {
        if (!mViewModel.immersiveLocalEnable || !mViewModel.isImmersiveEnable() || !isPlaying) return

        if (mViewModel.mChapterInfoVo?.isCharge == DetailMC.CHAPTER_STATUS_PREVIEW) {
            // 试看阶段，不进入沉浸式
            return
        }

        immersiveTimer?.cancel()
        if (mViewModel.getImmersiveWaitTime() > 0) {
            LogUtil.d(
                DetailMC.TAG_IMMERSIVE,
                "${mViewModel.getImmersiveWaitTime()}秒后启动沉浸式看剧, scene:$scene"
            )
            immersiveTimer = TaskManager.delayTask(mViewModel.getImmersiveWaitTime() * 1000L) {
                if(!isInPip()){
                    if (isPauseAdShow()) return@delayTask  // 如果暂停广告正在展示，就不进入沉浸式
                    LogUtil.d(DetailMC.TAG_IMMERSIVE, "启动沉浸式看剧")
//                if(detailGuideStatus == 0 && needFollowBubble == 1){
//                DetailMR.get().detailGuideComp().apply {
//                    showTime = 3
//                }.start()
//                }
                    if (detailGuideStatus == 0 && mViewModel.isGuideStatus == 1) {
                        Handler(Looper.getMainLooper()).post {
                            DetailMR.get().detailGuideComp().apply {
                                LogUtil.d(DetailMC.TAG_IMMERSIVE, "弹出新手引导弹窗")
                                showTime = mViewModel.guideTime
                            }.onSure {
                                cancelImmersive("点击新手引导弹窗")
                            }.start()
                        }

                    }
                    // 解锁成功的状态是临时状态，要在下次沉浸式后，变为永久解锁状态
                    if (mViewModel.mChapterInfoVo?.isCharge == DetailMC.CHAPTER_STATUS_PREVIEW_UNLOCK) {
                        mViewModel.mChapterInfoVo?.isCharge = DetailMC.CHAPTER_STATUS_UNLOCK
                    }

                    // 进入沉浸式
                    mViewModel.setPlayMode(PlayMode.IMMERSIVE )
                }else{
                    LogUtil.d(DetailMC.TAG_IMMERSIVE, "小窗状态，不进入沉浸式后续逻辑")
                }
            }
        }
    }

    /**
     * 取消沉浸式播放
     * @param scene String：取消的原因，触发场景
     * @param delayCancel Boolean：true立即取消沉浸式，false延迟200ms再取消。
     */
    private fun cancelImmersive(scene: String, delayCancel: Boolean = false) {
        LogUtil.d(
            DetailMC.TAG_IMMERSIVE,
            "取消沉浸式看剧 scene:$scene enable:${mViewModel.immersiveLocalEnable} " + "switch:${mViewModel.isImmersiveEnable()} currentPlayMode:${mViewModel.playMode.value}"
        )
        immersiveTimer?.cancel()  // 取消自动进入沉浸式的计时器
        if (!mViewModel.immersiveLocalEnable || !mViewModel.isImmersiveEnable() || mViewModel.playMode.value != PlayMode.IMMERSIVE || commentDelegate.isVisible) {
            // 屏蔽异常情况：沉浸式开关关闭 || 当前已经不在沉浸式
            return
        }

        val cancel = {  // 真正要执行取消沉浸式的操作
            LogUtil.d(DetailMC.TAG_IMMERSIVE, "沉浸式看剧已取消！ scene:$scene")
            if (mViewModel.mChapterInfoVo?.isCharge == 0 || mViewModel.mChapterInfoVo?.isCharge == 2 || CommInfoUtil.isVip()) {
                // 取消沉浸式
                mViewModel.setPlayMode(PlayMode.NORMAL)
            }
        }
        if (delayCancel) {  // 有些特殊场景需要延迟一下再取消沉浸式
            LogUtil.d(DetailMC.TAG_IMMERSIVE, "200ms后取消沉浸式看剧 scene:$scene")
            TaskManager.delayTask(200) {
                cancel.invoke()  // 执行取消沉浸式
            }
        } else {
            cancel.invoke()  // 执行取消沉浸式
        }
    }

    /**
     *  暂停广告是否正在显示  true：正在显示，false：未显示
     * @return
     */
    private fun isPauseAdShow(): Boolean {
        return mViewBinding.comPauseAd.isShowing()
    }

    private fun setImmersiveEnable(enable: Boolean, scene: String = "沉浸式开关变更") {
        if (mViewModel.immersiveLocalEnable == enable) return
        mViewModel.immersiveLocalEnable = enable
        if (enable) {
            startImmersiveTimer(scene)
        } else {
            cancelImmersive(scene)
        }
    }

    /**
     *  刷新隐藏沉浸式广告底部计时comp，并启动计时器
     */
    private fun updateDrawAdTips(holder: AdVideoViewHolder) {
        holder.updateDrawAdTips(isLandScape(), mViewModel) { time ->
            LogUtil.d(TAG, "沉浸式广告计时器，剩余时间:$time")
            if (time > 0) {  // 倒计时未结束，禁止上下滑动
                mDrawAdCanScroll = false
                stopScroll()  // 禁止上下滑动
            } else if (time == 0) {
                // 允许上下滑动
                mDrawAdCanScroll = true
                //广告倒计时结束，恢复列表滑动
                startScroll()
            }
        }
    }

    private var currentOrientation: Int = Configuration.ORIENTATION_PORTRAIT

    //横竖屏切换监听
    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        if (isInPip()) {
            return
        }
        LogUtil.d("AndroidAutoSize", "onConfigurationChanged ${newConfig.orientation}")
        // config变化时，需要重新初始化AutoSize的宽高适配规则。横竖屏切换的时候横竖屏会颠倒。
        resetAutoSize(newConfig)
        updatePlayerViewMode(newConfig)
        // 重新添加福利挂件
        restoreWelfare(newConfig)
        // 初始化状态栏
        initImmersionBar()
//        checkPayConfirmDialog(newConfig)

        if (newConfig.orientation != currentOrientation) {
            this.currentOrientation = newConfig.orientation
            onOrientationChanged(currentOrientation)
            //切换横屏 隐藏菜单提示
            if (newConfig.orientation == Configuration.ORIENTATION_LANDSCAPE) {
                mViewBinding.comVipTips.hideMenuTips()
            }
        }
    }

    private fun onOrientationChanged(orientation: Int) {
        LogUtil.d(
            DetailMC.AD_TAG, "onOrientationChanged   orientation=${orientation} "
        )
        mViewModel.loadDrawAd(this, "NewPlayDetailActivity.onOrientationChanged()")  // 重新加载沉浸式广告
        mVideoLifecycle.onOrientationChanged(orientation)
    }


    //屏幕旋转，设置旋转后的UI
    override fun updatePlayerViewMode(newConfig: Configuration) {
        if (newConfig.orientation == Configuration.ORIENTATION_PORTRAIT) {//竖屏UI
            // 更新Banner广告
            updateBannerAd(false)
            // 更新loading
            updateStatus(false)
            // 更新终章推荐
            updateFinalChapter(false)
            // 重新开始沉浸式的计时
            cancelImmersive("竖屏方向")
            startImmersiveTimer("竖屏方向")
            // 更新当前缓存的屏幕方向
            mViewModel.orientation.value = Orientation.Port
            // 横屏时不需要显示挂件，竖屏时需要显示福利挂件。
            // 当横屏切换到竖屏时，挂件也需要重新添加。这里加个判断，如果需要的话要重新添加。
            if (needAddWelfareWidgetWhenPort) {
                Looper.myQueue().addIdleHandler {
                    addWelfareWidget()
                    false
                }
            }
        } else if (newConfig.orientation == Configuration.ORIENTATION_LANDSCAPE) {//横屏UI
            // 显示横屏操作引导动画
            showLandGuide()
            // 更新Banner广告
            updateBannerAd(true)
            // 更新loading
            updateStatus(true)
            // 更新终章推荐
            updateFinalChapter(true)
            // 重新开始沉浸式的计时
            cancelImmersive("横屏方向")
            startImmersiveTimer("横屏方向")
            // 更新当前缓存的屏幕方向
            mViewModel.orientation.value = Orientation.Land_Forward
            // 横屏时不显示挂件，所以不用处理挂件问题
        }
    }

    override fun getViewHolder(position: Int): RecyclerView.ViewHolder? {
        return ViewPager2Helper.findViewHolderByPosition(mViewBinding.vp, position)
    }

    /**
     *  刷新底部banner广告显示
     * @param isLandscape 是否是横屏 true：横屏 false：竖屏
     */
    private fun updateBannerAd(isLandscape: Boolean) {
        if (isLandscape || isInPip()) {
            //隐藏底部广告，并暂停底部广告轮播
            goneBottomRoot()
            goneBannerAd()
        } else {
            //显示底部广告，并恢复底部广告轮播
            visibleBottomRoot()
            visibleBannerAd()
            if (!isPauseAdShow()) {
                bannerAdPresenter?.tryLoadBannerAd(mViewModel.videoInfoLiveData.value)
            }
        }
    }


    /**
     * 隐藏显示播放器底部的UI
     * BottomRoot：包含一个显示了“河马剧场”的图片，然后在图片上方覆盖了一个Banner广告
     */
    private fun goneBottomRoot() {
        mViewBinding.clAdBottom.visibility = GONE
    }

    /**
     * 竖屏状态时，显示播放器底部的UI
     * BottomRoot：包含一个显示了“河马剧场”的图片，然后在图片上方覆盖了一个Banner广告
     */
    private fun visibleBottomRoot() {
        if (!isLandScape() && !isInPip()) {
            mViewBinding.clAdBottom.visibility = VISIBLE
        }
    }

    /**
     *  刷新loading
     * @param isLandscape true：横屏  false：竖屏
     */
    private fun updateStatus(isLandscape: Boolean) {
        mViewModel.statusPoster.statusFresh().screenStatus(isLandscape).post()
    }

//    private fun checkPayConfirmDialog(newConfig: Configuration) {
//        if (payConfirmDialog != null && payOrderPage != null && payConfirmDialog!!.orientation != newConfig.orientation) {
//            startPayUnlockDialog(payOrderPage)
//        }
//    }

    /**
     *  当前是否是横屏
     */
    private fun isLandScape(): Boolean {
        return OrientationUtil.isLand(this)
    }

    /**
     *  切换到竖屏
     */
    private fun changedToPortrait() {
        orientationManager.changedToPortrait(true)
    }

    /**
     *  切换到横屏
     */
    fun changedToLandForwardScape() {
        val direction = AngleUtils.getDirectionByAngle(screenAngle)
        if (direction == AngleUtils.DIRECTION_2) {
            orientationManager.changedToLandReverseScape()
        } else {
            orientationManager.changedToLandForwardScape()
        }
    }

    /**
     *  横屏播放器截图，截取视频内容
     */
    fun landScapeSnapshot() {
        if (isLandScape()) {
            snapshot()
        }
    }

    /**
     *  第一次横屏展示引导页
     */
    private fun showLandGuide() {
        if (DetailKV.showLandGuide || statusComponent.visibility == VISIBLE) {
            return
        }
        mViewBinding.vp.postDelayed({
            if (isLandScape() && !DetailKV.showLandGuide) {
                DetailKV.showLandGuide = true
                DetailMR.get().landGuideDialog().apply {}.onDismiss {}.start()
            }
        }, 500)

    }

    /**
     * 单击
     */
    private fun onSingleTab() {
        if (isLandScape()) { // 横屏
            if (mViewModel.playMode.value == PlayMode.IMMERSIVE) {  // 沉浸式
                mViewModel.setPlayMode(PlayMode.NORMAL)
                currentHolder?.layerController?.showPauseIcon()
                startImmersiveTimer("沉浸式中单击")
            } else {  // 常规模式
                mViewModel.setPlayMode(PlayMode.IMMERSIVE)
            }
        } else {  // 竖屏
            if (mViewModel.playMode.value == PlayMode.IMMERSIVE) {  // 沉浸式
                mViewModel.setPlayMode(PlayMode.NORMAL)
                currentHolder?.layerController?.showPauseIcon()
                startImmersiveTimer("沉浸式中单击")
            } else {  // 常规模式
                pauseClickByUser()
            }
        }
    }

    /**
     * 处理用户双击屏幕的操作
     */
    private fun onDoubleTap() {
        // 如果当前是横屏或者是竖屏的沉浸式，那么执行双击暂停操作
        if (isLandScape() || mViewModel.playMode.value == PlayMode.IMMERSIVE) {
            // 执行用户触发的暂停操作
            pauseClickByUser()
            // 取消沉浸式
            cancelImmersive("double click", true)
        } else {
            // 竖屏非沉浸式时，双击执行喜欢/点赞操作
            if (!currentHolder?.isLikes()!!) {
                likesClick(false)
            }
        }
    }

    /**
     * 用户手动触发的播放、暂停
     */
    private fun pauseClickByUser() {
        DetailMC.allowPlay = null
        var oldStatusNotPause = !layerPauseStatus()
        onPauseClick()
        if (layerPauseStatus()) {
            landScapeSnapshot()
            if (oldStatusNotPause) {
                showPauseAd()
            }
        }
        // 暂停神策打点
        if (layerPauseStatus()) {
            VideoTrackUtil.trackPause(
                mViewModel.videoTrackInfo,
                VideoTrackUtil.PAUSE_SCENE_USER,
                if (mViewModel.isLandscapeVideo) {
                    if (isLandScape()) "横屏" else "竖屏"
                } else "",
                detailIntent = mViewModel.routeIntent,
                isMultipleInstances = true
            )
        } else {
            VideoTrackUtil.trackPauseCancel(
                mViewModel.videoTrackInfo, VideoTrackUtil.PAUSE_SCENE_USER, true
            )
        }
    }

    private fun enterLandImmersive() {
        window.decorView.systemUiVisibility =
            (SYSTEM_UI_FLAG_IMMERSIVE_STICKY or SYSTEM_UI_FLAG_FULLSCREEN or SYSTEM_UI_FLAG_HIDE_NAVIGATION or SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION or SYSTEM_UI_FLAG_LAYOUT_STABLE or SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION or SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN)
    }

    override fun isBaseOnWidth(): Boolean {
        return !OrientationUtil.isLand(this)
    }

    override fun getSizeInDp(): Float = 375.0F

    /**
     * 启动播放计时器
     * 用于记录在播时长。
     * 在播时长在打点、进度条挂件中都会用到。
     * @param reason String?
     */
    fun startPlayingTimer(reason: String? = null) {
        if (isFinishing || isDestroyed) {
            return //避免少量情况 页面onDestroy 的同时，单例的方法也在执行。导致数据错误。
        }
        mBookId?.let {
            PlayingStatisticsMgr.startPlayingTimer(PlayScene.DETAIL, reason, reportAward = {
                LogUtil.d(TAG, "startPlayingTimer reportAward")
                mViewModel.reportAward()
            }, conditionsNoMet = {
//                LogUtil.d(
//                    WelfareMC.TAG_REPORT,
//                    "timer 回调。 rewardStatus:${mViewModel.rewardStatus.value} targetDuration:${mViewModel.targetDuration}, activity hashCode = ${this.hashCode()}"
//                )
                // 当前状态：进行中
                if (mViewModel.rewardStatus.value == WelfareReward.STATUS_INCOMPLETE ||
                    (mViewModel.rewardStatus.value == WelfareReward.STATUS_UNCLAIMED && mViewModel.targetDuration > 0)
                ) {
                    mViewModel.countProgress()
                    (welfareWidget as? IProgressPendantComp)?.setProgress(mViewModel.taskProgress)
                    (welfareWidget as? IProgressDynamicPendantComp)?.setCoins(
                        mViewModel.taskProgress,
                        mViewModel.getAllPlayTime(),
                        mViewModel.notClaimedCoins,
                        mViewModel.targetCoins,
                        mViewModel.targetDuration,
                        mViewModel.nextStage?.duration,
                        mViewModel.playerPendantConfig?.speed,
                        mViewModel.playerPendantConfig?.beatMin,
                    )
                    if (mViewModel.taskProgress >= 0.99F || mViewModel.taskProgress <= 0.006666665F) {
//                                    mViewModel.rewardStatus.postValue(WelfareReward.STATUS_UNCLAIMED)
                        mViewModel.apply {
                            if (targetDuration > 0) {
                                val addCoins = (circleSecond * targetCoins / targetDuration).toInt()
                                if (addCoins > 0) {
                                    // 进度条挂件 拖拽挂件的金币显示交互调整
                                    if (welfareWidget is IProgressDragPendantComp) {
                                        if (mViewModel.playerPendantConfig?.speed == 1) {
                                            if (mViewModel.getAllPlayTime() > 60) {
                                                (welfareWidget as? IProgressPendantComp)?.showAddCoin(
                                                    addCoins
                                                )
                                            }
                                        } else {
                                            if (mViewModel.getAllPlayTime() > 180) {
                                                (welfareWidget as? IProgressPendantComp)?.showAddCoin(
                                                    addCoins
                                                )
                                            }
                                        }
                                    } else {
                                        (welfareWidget as? IProgressPendantComp)?.showAddCoin(
                                            addCoins
                                        )
                                    }
                                    if (getAllPlayTime() > ((mViewModel.nextStage?.duration
                                            ?: 1) * 60)
                                        && recStageCoinTip == 1
                                        && (mViewModel.nextStage?.award ?: 0) > 0
                                    ) {
//                                  阶段完成奖励引导图
                                        mViewBinding.clEarnedGoldCoins.visibility = VISIBLE
                                        mViewBinding.tvEarnedGoldCoins.text =
                                            "已赚${mViewModel.nextStage?.award ?: 0}金币"
                                        TaskManager.delayTask(1000) {
                                            mViewBinding.clEarnedGoldCoins.visibility = GONE
                                        }
                                    }
                                }
                            }
                            updateStage(WelfareMC.SCENE_ONE_CIRCLE)
                        }
                    }
                }
            }, bookId = it)
        }
    }

    /**
     * 停止播放中计时器
     * @param reason String?
     */
    fun stopPlayingTimer(reason: String? = null) {
        PlayingStatisticsMgr.stopPlayingTimer(reason, PlayScene.DETAIL)
    }

    /**
     * 刷新进度条挂件
     */
    private fun refreshProgressPendant() {
        when (mViewModel.rewardStatus.value) {
            WelfareReward.STATUS_INCOMPLETE -> {  // 当前阶段任务的状态是未完成
                mViewModel.countProgress()  // 重新计算当前的进度
                // 如果当前挂件是进度条挂件。挂件有多钟类型:进度条挂件v1、v2，还有运营位挂件(就是一张图片加一个关闭按钮)
                (welfareWidget as? IProgressPendantComp)?.apply {
                    setProgress(mViewModel.taskProgress) // 将进度设置给挂件中
                    when (welfareWidget) {
                        is IProgressPendantComp2 -> {
                            setText("")  // v2版本的进度条挂件要根据实时状态显示文案
                        }

                        is IProgressDynamicPendantComp -> {
                            setText("")
                        }

                        else -> {
                            setText("看剧赚钱中")  // v1版本的进度条挂件显示固定文案
                        }
                    }
                }
            }

            WelfareReward.STATUS_UNCLAIMED -> {  // 已完成阶段，但是未领取奖励
                if (mViewModel.targetDuration == 0L) {
                    //已完成所有阶段任务，但是未领取奖励
                    mViewModel.taskProgress = 1.0f
                    (welfareWidget as? IProgressPendantComp)?.setProgress(1.0f)
                    (welfareWidget as? IProgressDynamicPendantComp)?.setText("继续赚金币")
                } else {  // 更新进度条挂件的进度
                    //已完成当前阶段任务，但是未领取奖励
                    mViewModel.countProgress()  // 重新计算当前的进度
                    (welfareWidget as? IProgressPendantComp)?.setProgress(mViewModel.taskProgress)  // 将进度显示到进度条挂件上
                }
                if (mViewModel.notClaimedCoins > 0) {  // 如果有未领取的金币数，要显示可以领多少金币
                    (welfareWidget as? IProgressPendantComp)?.setText("领${mViewModel.notClaimedCoins}金币")
                } else {  // 没有可以领的金币显示固定文案
                    (welfareWidget as? IProgressPendantComp)?.setText("领取金币")
                }
            }

            WelfareReward.STATUS_COMPLETED -> {  // 所有阶段任务都已经完成
                // 任务全部完成后，进度条要转满。这里是避免发生异常情况。
                mViewModel.taskProgress = 1.0f
                (welfareWidget as? IProgressPendantComp)?.setProgress(1.0f)
                (welfareWidget as? IProgressPendantComp)?.setText("继续赚金币")
            }
        }

        // 判断是否可以显示进度条挂件
        if (canShowPendantVisible()) {  // 判断是否可以显示进度条挂件
            LogUtil.d(OperationMC.TAG_PENDANT, "refreshProgressPendant show pendant")
            welfareWidget?.visibility = VISIBLE
        } else {
            LogUtil.d(OperationMC.TAG_PENDANT, "welfareWidget?.visibility  = GONE  111")
            welfareWidget?.visibility = GONE
        }
    }

    /**
     * 当进度条挂件配置信息获取成功后，调用这里，将挂件显示出来
     * @param config WelfarePendantConfigVo
     */
    private fun onPendantConfigSuccess(config: WelfarePendantConfigVo) {
        mViewModel.playerPendantConfig = config  // 缓存配置信息
        if (isLandScape()) {  // 如果当前是横屏，那么不显示挂件。等转为竖屏的时候再显示
            needAddWelfareWidgetWhenPort = true  // 记录一个标志。等设备转为竖屏的时候判断这个变量如果为true，就显示挂件；
        } else {
            LogUtil.d("addWelfareWidget", "onPendantConfigSuccess  addWelfareWidget autoShow=false")
            addWelfareWidget(exposureReport = true, autoShow = false)
        }
        hasWelfareConfig = true  // 标记已经获取到挂件配置信息
        mViewModel.syncRewardStatus("welfareConfigReady already")  // 同步阶段任务的状态
    }

    /**
     * 判断当前Holder是不是视频。Holder可能是广告。
     * @return Boolean true视频，false广告
     */
    private fun currentIsVideo(): Boolean {
        val holder = ViewPager2Helper.findViewHolderByPosition(mViewBinding.vp, mCurrentPosition)
        return holder == null || holder is NewDetailVideoViewHolder
    }

    /**
     * 是否可以展示挂件
     * - 没有暂停广告
     * - 当前是视频holder
     * - 非沉浸式播放 且 沉浸式下允许消失
     * - 如果是进度条挂件，必须有多段阅读任务
     * - 非横屏
     * - 非小窗
     *
     * @return
     */
    private fun canShowPendantVisible(): Boolean {
        var progressEnable = true
        if (welfareWidget is IProgressPendantComp) {
            progressEnable =
                mViewModel.rewardStatus.value != WelfareReward.STATUS_ERROR && mViewModel.rewardStatus.value != WelfareReward.STATUS_UNKNOWN && mViewModel.rewardStatus.value != null
        }
        val result =
            !pauseAdShowing && currentIsVideo() && progressEnable && !isLandScape() && !isFinalChapter()
                    && (mViewModel.playerPendantConfig?.immersiveHide == 1 || mViewModel.playMode.value != PlayMode.IMMERSIVE)
                    && !isInPip() && !isPreviewGuideShowing
        LogUtil.d(
            OperationMC.TAG_PENDANT,
            "二级页是否可展示挂件:$result. progressEnable:$progressEnable playMode:${mViewModel.playMode.value} "
                    + "hide when immersive:${mViewModel.playerPendantConfig?.immersiveHide == 0} "
                    + "rewardStatus:${mViewModel.rewardStatus.value} currentIsVideo:${currentIsVideo()} "
                    + "pauseAdShowing:$pauseAdShowing isLandScape:${isLandScape()} isFinalChapter:${isFinalChapter()} " +
                    "isPreviewGuideShowing:$isPreviewGuideShowing"
        )
        return result
    }

    private var waitRewardStatusToReport = false
    private fun reportPendantExposure(config: WelfarePendantConfigVo?): Boolean {
        var result = false
        var progressEnable = true
        if (welfareWidget is IProgressPendantComp) {
            progressEnable =
                mViewModel.rewardStatus.value != WelfareReward.STATUS_ERROR && mViewModel.rewardStatus.value != WelfareReward.STATUS_UNKNOWN && mViewModel.rewardStatus.value != null
        }
        if (progressEnable) {
            result = true
            OperationMS.get()
                ?.operationExposureReport(config?.id, object : OperationReportCallback {
                    override fun onReportSuccess(data: com.dz.business.base.data.bean.OperateReportBean?) {
                        LogUtil.d(
                            OperationMC.TAG_PENDANT, "detail Pendant 曝光上报成功"
                        )
                    }

                    override fun onReportFailed() {

                    }

                })
            config?.let {
                DzTrackEvents.get().operationExposureTE().apply {
                    operationID(it.id?.toString() ?: "")
                    operationPosition(it.operationPosition).operationName(it.name)
                    operationType(it.operationType).userTacticInfo(it.userTacticsVo)
                    bookId(it.bookId).bookName(it.bookName)
                    if (welfareWidget is IProgressDynamicPendantComp) {
                        buttonName("福利中心挂件")
                        pendantPage("首页")
                    }
                }.track()
            }
        }
        return result
    }

    /**
     * 更新挂件的状态
     * 主要是针对v2版本的进度条挂件
     * @param scene String? 记录是哪里出发的挂件更新
     */
    private fun updatePendantState(scene: String? = null) {
        (welfareWidget as? IProgressPendantComp2)?.apply {
            if (mViewModel.nextStage?.index == 0) {  // 第一个阶段还没完成
                updateState(WelfareMC.EXTEND_STATE_WAITING)
            } else if (mViewModel.currentStage?.index == 0 && mViewModel.previousStage == null
                && mViewModel.rewardStatus.value == WelfareReward.STATUS_UNCLAIMED &&
                scene == WelfareMC.SCENE_ONE_CIRCLE
            // 这里的判断条件有些多，主要是判断场景：刚完成第一阶段时，要展示一个侧边弹出动画，提醒用户领金币。
            // 当进度条转满一圈 && 当前已完成第一阶段 && 第一阶段金币还未领取
            ) {
                // 通过更新State状态位，执行弹出动画
                updateState(WelfareMC.EXTEND_STATE_SHOWING, mViewModel.notClaimedCoins)
            } else {
                // 其它情况：不需要弹出侧边动画
                updateState(WelfareMC.EXTEND_STATE_NO_NEED)
            }
        } ?: let {
            (welfareWidget as? IProgressDynamicPendantComp)?.apply {
                if (mViewModel.nextStage?.index == 0) {  // 第一个阶段还没完成
                    updateState(WelfareMC.EXTEND_STATE_WAITING)
                } else if (mViewModel.currentStage?.index == 0 && mViewModel.previousStage == null
                    && mViewModel.rewardStatus.value == WelfareReward.STATUS_UNCLAIMED &&
                    scene == WelfareMC.SCENE_ONE_CIRCLE
                // 这里的判断条件有些多，主要是判断场景：刚完成第一阶段时，要展示一个侧边弹出动画，提醒用户领金币。
                // 当进度条转满一圈 && 当前已完成第一阶段 && 第一阶段金币还未领取
                ) {
                    // 通过更新State状态位，执行弹出动画
                    updateState(
                        WelfareMC.EXTEND_STATE_SHOWING,
                        mViewModel.notClaimedCoins,
                        mViewModel.recStageCoinTip ?: 0
                    )
                } else {
                    // 其它情况：不需要弹出侧边动画
                    updateState(WelfareMC.EXTEND_STATE_NO_NEED)
                }
                if (BBaseKV.playFirstShow != SimpleDateFormat("yyyy-MM-dd", Locale.CHINA).format(
                        Date()
                    ) && welfareWidget !is IProgressDragPendantComp
                ) {
//                  每日首次冷启动显示侧滑引导
                    coldStartDisplayWithSideSlipGuidance()
                }
            } ?: let {
                LogUtil.d(DetailMC.TAG_WELFARE, "非新版本进度条挂件. $welfareWidget")
            }
        }
    }

    /**
     * 隐藏选集列表弹窗
     */
    fun hideDramaListDialog() {
        dramaListDialog?.dismiss()
    }

    override fun getRatingComp(): RatingComp {
        return mViewBinding.ratingComp
    }

    /**
     * 获取当前页面标题
     * 打点用
     * @return String
     */
    override fun getPageName(): String {
        return "二级播放器"
    }

    override fun getPageId(): String = PageConstant.PAGE_ID_PLAYER

    /**
     * 显示追剧浮窗
     */
    override fun onShowFollowTip() {
        //通知ViewHolder显示加追浮窗
        sendDetailViewHolderEvent(DetailMC.EVENT_HOLDER_PLAY_FOLLOW_TIP_ENTER_ANIMATION)
    }

    /**
     * 隐藏追剧浮窗
     */
    override fun onHideFollowTip() {
        //通知ViewHolder隐藏加追浮窗
        sendDetailViewHolderEvent(DetailMC.EVENT_HOLDER_PLAY_FOLLOW_TIP_EXIT_ANIMATION)
    }

    override fun onFollowTipAutoClose() {
        //5秒倒计时结束，浮窗还在，说明用户没点收藏，此时浮窗动画结束以后需要播放收藏按钮渐显动画
        TaskManager.delayTask(DetailMC.FOLLOW_TIP_EXIT_ANIMATION_DURATION) {
            sendDetailViewHolderEvent(DetailMC.EVENT_HOLDER_PLAY_FOLLOW_STAR_ANIMATION)
        }
    }

    /**
     * 给当前ViewHolder发送事件
     * ViewHolder收到事件后，将事件分发给layer分层，各个layer里根据事件做出相应操作
     * @param event String
     */
    private fun sendDetailViewHolderEvent(event: String) {
        kotlin.runCatching {
            if (isDestroyed) {  // 如果Activity已经销毁，那么就不回调了。
                return
            }
            val holder = ViewPager2Helper.findViewHolderByPosition(
                mViewBinding.vp,
                mViewBinding.vp.currentItem
            )  // 获取当前holder
            if (holder is NewDetailVideoViewHolder) {
                holder.onEvent(event)  // 给Holder发通知
            }
        }.onFailure {
            LogUtil.e(TAG, "sendDetailViewHolderEvent error:$it")
        }
    }

    /**
     * 页面进入前台
     * @param activity Activity
     */
    override fun onForeground(activity: Activity) {
        val isNotifyEnable = NotificationUtil.isNotifyEnabled(this)  // 判断用户通知开关是否打开
        if (mViewModel.mIsShowToast && isNotifyEnable) {
            mViewModel.mIsShowToast = false
            mViewModel.isSubscribe = false
            mViewModel.onSubscribeClick()
        }
    }

    override fun onActivityActive(activeActivity: Activity) {
    }

    override fun onBackground(activity: Activity) {
    }

    /**
     * 分辨率弹窗回调
     */
    private val resolutionCallback by lazy {
        object : ResolutionIntent.Callback {
            override fun payVip(position: String, callback: RechargeIntent.ResultCallback) {
                RechargeMR.get().recharge().apply {  // 跳转充值页面。当用户切换的是VIP专享分辨率，需要先以引导去充值页面开通VIP
                    this.sourceType = when (position) {
                        DetailMC.CHANGE_RESOLUTION -> RechargeMC.RECHARGE_SOURCE_TYPE_RESOLUTION
                        else -> RechargeMC.RECHARGE_SOURCE_TYPE_UNKNOWN
                    }
                    this.bookId = mViewModel.videoTrackInfo.bookId
                    this.chapterId = mViewModel.videoTrackInfo.chapterId
                    omap = mViewModel.mVideoInfo?.videoInfo?.omap ?: mViewModel.mOmap
                    sourceExtend = mapOf(
                        "bookId" to bookId as Any,
                        "positionName" to position as Any
                    )
                    setCallback(getUiId(), callback)
                }.start()
            }
        }
    }

    private var resolutionChangingShowTime = 0L

    /**
     * 切换分辨率
     */
    private fun changeResolution(resolution: ResolutionRateVo) {
        LogUtil.d(
            TAG_RESOLUTION,
            "二级播放器切换分辨率.$resolution"
        )

        mViewModel.mChapterInfoVo?.let { chapterInfoVo ->
            mViewModel.switchingResolution = resolution


            mViewModel.resolutionState = DetailMC.RESOLUTION_STATE_SWITCHING
            if (resolution.scene == BBaseMC.SCENE_USER) {
                resolutionChangingShowTime = System.currentTimeMillis()
                ToastManager.showToast("${BBaseKV.selectedResolution}画质切换中，请稍后")
            }
            mViewModel.saveBeforeRefreshPlayInfo(
                mViewModel.mChapterInfoVo?.chapterId,
                currentDuration
            )
            if (BBaseKV.selectedResolution == BBaseMC.RATE_720P && mViewModel.downLoadUrlCanPlay(
                    chapterInfoVo
                )
            ) {
                chapterInfoVo.contentUlrIndex = -1
                mViewModel.resolutionState = DetailMC.RESOLUTION_STATE_WAIT_TO_PLAY
                mViewModel.currentResolution = resolution.rate
                mViewModel.switchingResolution = null
                if (resolution.scene == BBaseMC.SCENE_USER) {
                    ToastManager.showToast("${BBaseKV.selectedResolution}切换成功")
                }
                mPlayerController.stopAllPlayer()
                activePause = false
                updateLayerPauseStatus(false)
                //控制进度
                setStartTime(currentDuration)
                mViewModel.played = false
                mViewModel.loadingScene = ReadingTE.LOADING_SCENE_AUTO_CHANGE_PROGRESS
                //重新播放
                playerChangeTo(mViewModel.mChapterInfoVo?.downLoadUrl)
            } else {
                mViewModel.getDataList().forEach {
                    it.m3u8720pUrl = null
                    it.mp4720pSwitchUrl = null
                    it.contentUlrIndex = -1
                }
                chapterInfoVo.chapterId?.let { id ->
                    mViewModel.unlockChapter(id, false)
                } ?: let {
                    LogUtil.e(TAG_RESOLUTION, "分辨率切换失败，current chapter id is null！")
                }
            }
        } ?: let {
            LogUtil.e(TAG_RESOLUTION, "分辨率切换失败，当前章节信息为空!")
        }
    }

    /**
     * 恢复默认清晰度状态
     */
    private fun checkResolution() {
        if (!BBaseKV.resolutionSwitch) {
            return
        }
        mViewModel.resolutionState = DetailMC.RESOLUTION_STATE_AUTO
    }

    private var introDialog: PDialogComponent<*>? = null

    private fun showIntroDialog(tab: String) {
        mIsLoopPlay = true
        cancelTimeOutTask()
        DetailMR.get().videoIntro().apply {
            scene = PageConstant.PAGE_ID_PLAYER
            this.showTab = tab
            bookInfoVo = mViewModel.mVideoInfo?.videoInfo
            currentChapterId = mViewModel.mChapterInfoVo?.chapterId
            chapters = mViewModel.getDataList()
            tomorrowUnlockTip = mViewModel.tomorrowUnlockTipDetail
            goToVideoDetail = ::goToVideoDetail
            onChapterSelected = { chapter ->
                selectChapter(mViewModel.getDataList().indexOf(chapter), VideoMC.PLAY_END_CHANGE_CHAPTER_BY_LIST)
            }
            addShowListener {
                introDialog = it
                isDramaListDialogShow = true
                setImmersiveEnable(false, "显示简介弹窗")
                playerLoop(true)
                if (mViewModel.mChapterInfoVo?.isPreviewChapter() == true) {
                    mViewModel.pausePlay(VideoMC.PLAY_END_PREVIEW_INTRO)
                }
            }
            addDismissListener {
                introDialog = null
                mIsLoopPlay = false
                isDramaListDialogShow = false
                resumeTimeOutTask()
                playerLoop(false)
                setImmersiveEnable(true, "关闭简介弹窗")
                mViewModel.cancelPause("dialog_intro")
                mViewModel.cancelPause(VideoMC.PLAY_END_PREVIEW_INTRO)
            }
        }.start()
    }

    /**
     * 跳转Flutter剧集详情页
     */
    private fun goToVideoDetail() {
        ElementClickUtils.trackViewAppClick2(
            elementPosition = "二级播放器",
            content = mViewModel.mVideoInfo?.videoInfo?.bookName,
            elementType = "查看详情",
            bookName = mViewModel.mVideoInfo?.videoInfo?.bookName,
            bookID = mViewModel.mChapterInfoVo?.chapterId,
            chaptersNum = mViewModel.mChapterInfoVo?.chapterIndex
        )
        mViewModel.routeIntent?.let { intent ->
            intent.playPosition = currentDuration
            intent.chapterId = mViewModel.mChapterInfoVo?.chapterId
            intent.chapterIndex = mViewModel.mChapterInfoVo?.chapterIndex
            intent.coverBgColor = mViewModel.mVideoInfo?.videoInfo?.coverBgColor
            intent.coverWap = mViewModel.mVideoInfo?.videoInfo?.coverWap
            intent.alias = mViewModel.routeIntent?.alias
            intent.bookAlias = mViewModel.mVideoInfo?.videoInfo?.bookName
            VideoMS.get()?.goToVideoDetail(intent.getIntentJson())
        } ?: let {
            VideoMS.get()?.goToVideoDetail(
                mViewModel.mVideoInfo?.videoInfo?.bookId,
                mViewModel.mVideoInfo?.videoInfo?.bookName,
                mViewModel.mChapterInfoVo?.chapterId,
                mViewModel.mChapterInfoVo?.chapterIndex,
                progress = currentDuration,
                mViewModel.mVideoInfo?.videoInfo?.coverBgColor,
                mViewModel.mVideoInfo?.videoInfo?.coverWap,
                mViewModel.routeIntent?.alias,
                mViewModel.mVideoInfo?.videoInfo?.bookName,
            )
        }
    }


    private fun stopScroll() {
        mViewBinding.vp.setUserInputEnabled(false)
    }

    private fun startScroll() {
        mViewBinding.vp.setUserInputEnabled(true)
    }

    private val convertURLCallback = object : ConvertURLCallback {
        override fun convertURL(srcURL: String, srcFormat: String): String? {
            LogUtil.d(TAG_RESOLUTION, "视频播放的URL：$srcURL")
            mViewModel.getDataList().find {
                it.mp4720pSwitchUrl?.contains(srcURL) == true || srcURL == it.m3u8720pUrl || srcURL == it.downLoadUrl
            }?.apply {
                val holder =
                    ViewPager2Helper.findViewHolderByPosition(mViewBinding.vp, mCurrentPosition)
                if (holder is AdVideoViewHolder) {
                    return null
                }
                var url: String? = this.m3u8720pUrl
                //线上地址报错切换
                //判断备用地址下标有改变，需要切换播放地址 防止获取下标越界
                if (this.chapterId == mViewModel.mChapterInfoVo?.chapterId
                    && this.contentUlrIndex > -1 &&
                    (!this.mp4720pSwitchUrl.isNullOrEmpty())
                    && (this.mp4720pSwitchUrl?.contains(srcURL) == true || this.m3u8720pUrl == srcURL)
                    && (this.mp4720pSwitchUrl?.size ?: 0) > this.contentUlrIndex
                ) {
                    //取出当前要播放的备用地址
                    url = this.mp4720pSwitchUrl?.get(this.contentUlrIndex)
                    if (this.switchState == SwitchState.NEED_SWITCH) {
                        //修改状态为切换完成
                        this.switchState = SwitchState.SWITCH_ED
                    }
                    LogUtil.d(
                        DetailMC.PLAYER_DOWNLOAD_TAG,
                        "原始的url ==$srcURL    播放器报错    切换播放地址==${url}   第${mViewModel.mChapterInfoVo?.chapterIndex}集"
                    )
                    return url
                }
                if (this.chapterId === mViewModel.mChapterInfoVo?.chapterId && mViewModel.currentResolution != BBaseMC.RATE_720P) {//检测到当前集，当前的分辨率选择的不是720，直接选择线上地址进行播放
                    LogUtil.d(
                        DetailMC.PLAYER_DOWNLOAD_TAG,
                        "原始的url ==$srcURL    当前集清晰度为${mViewModel.currentResolution}    切换播放地址==${mViewModel.mChapterInfoVo?.m3u8720pUrl}   第${mViewModel.mChapterInfoVo?.chapterIndex}集"
                    )
                    return mViewModel.mChapterInfoVo?.m3u8720pUrl
                }
                //其他不是选中的集，按照下载能播则播，不能播则选择线上地址
                //下载完成切换
                if (srcURL == this.m3u8720pUrl && downLoadUrlCanPlay(this)) {
                    LogUtil.d(
                        DetailMC.PLAYER_DOWNLOAD_TAG,
                        "原始的url ==$srcURL    下载地址可以播放，切换到下载地址== ${this.downLoadUrl}   第${this.chapterIndex}集"
                    )
                    return this.downLoadUrl
                }
                //下载地址无法播放切换
                if (srcURL == this.downLoadUrl && !downLoadUrlCanPlay(this)) {
                    LogUtil.d(
                        DetailMC.PLAYER_DOWNLOAD_TAG,
                        "原始的url ==$srcURL    下载地址无法播放，切换到线上地址== ${this.m3u8720pUrl}   第${this.chapterIndex}集"
                    )
                    return this.m3u8720pUrl
                }
                LogUtil.d(
                    DetailMC.PLAYER_DOWNLOAD_TAG,
                    "原始的url ==$srcURL    不需要修改当前的播放链接      第${this.chapterIndex}集"
                )
            }
            LogUtil.d(
                DetailMC.PLAYER_DOWNLOAD_TAG,
                "原始的url ==$srcURL    不需要修改当前的播放链接"
            )
            return null
        }
    }

    /**
     * onPrepared开始结束时间记录
     */
    private fun recordPrepared(status: Int) {
        if (status == 0) {//onPrepared开始
            PlayerMonitorManager.getPlayerMonitor(DetailMC.DETAIL_TRACK_TAG)
                .recordTime(PlayerMonitorManager.TAG_START_PLAY_TIME_START)
        } else {//onPrepared结束
            PlayerMonitorManager.getPlayerMonitor(DetailMC.DETAIL_TRACK_TAG)
                .recordTime(PlayerMonitorManager.TAG_START_PLAY_TIME_END)
        }
    }

    /**
     * 首帧渲染开始结束时间记录
     */
    private fun recordRenderingStart(status: Int) {
        if (status == 0) {//首帧渲染开始
            PlayerMonitorManager.getPlayerMonitor(DetailMC.DETAIL_TRACK_TAG)
                .recordTime(PlayerMonitorManager.TAG_FIRST_RENDERED_TIME_START)
        } else {//首帧渲染结束
            PlayerMonitorManager.getPlayerMonitor(DetailMC.DETAIL_TRACK_TAG)
                .recordTime(PlayerMonitorManager.TAG_FIRST_RENDERED_TIME_END)
        }
    }

    /**
     * 用户感知的耗时开始结束时间记录
     */
    private fun recordUserSenseTime(status: Int) {
        if (status == 0) {//用户感知的耗时开始
            PlayerMonitorManager.getPlayerMonitor(DetailMC.DETAIL_TRACK_TAG)
                .recordTime(PlayerMonitorManager.TAG_USER_SENSE_TIME_START)
        } else {//用户感知的耗时结束
            PlayerMonitorManager.getPlayerMonitor(DetailMC.DETAIL_TRACK_TAG)
                .recordTime(PlayerMonitorManager.TAG_USER_SENSE_TIME_END)
        }
    }

    private fun onPlayStop() {
        playCount = 0
        if (!chapterSwitching) {
            cancelImmersive("player stopped")
        }
        currentHolder?.updatePlayState(PlayState.STOP)
        mViewModel.updatePlayerState(PlayState.STOP)
        mVideoLifecycle.onPlayStop()
        FollowTipManager.stopPlayDurationStatistics()
        notShowRegisterNumber() //备案号在清晰度切换期间不展示
    }

    override fun getPlayingBookId(): String? = mBookId

    override fun getPlayingDuration(): Long = currentDuration

    //备案号在清晰度切换期间不展示
    private fun notShowRegisterNumber() {
        mPlayerController.notShowRegisterNumber()
    }

    //设置播放器的循环播放属性
    private fun playerLoop(loop: Boolean) {
        mPlayerController.playerLoop(loop)
    }

    //获取controller中的暂停状态
    private fun layerPauseStatus(): Boolean {
        return mPlayerController.layerPauseStatus()
    }

    //修改controller中的暂停状态
    private fun updateLayerPauseStatus(isPause: Boolean) {
        mPlayerController.updateLayerPauseStatus(isPause)
    }


    /**
     * 停止视频播放
     */
    private fun stopPlay() {
        mPlayerController.stopPlay()
    }

    //设置开始播放的位置
    private fun setStartTime(position: Long) {
        mPlayerController.setStartTime(position)
    }

    //进度切换到某个位置播放
    private fun seekToPlay(position: Long) {
        mPlayerController.seekToPlay(position)
    }

    /**
     * 开启或关闭播放器httpDns
     */
    private fun enableHttpDns(it: Boolean) {
        mPlayerController.enableHttpDns(it)
    }

    /**
     * 开启关闭手势控制。
     * @param enable  开启
     */
    private fun enableGesture(enable: Boolean) {
        mPlayerController.enableGesture(enable)
    }

    /**
     * 获取当前视频长度
     */
    private fun getPlayerDuration(): Long {
        return mPlayerController.getPlayerDuration()
    }

    /**
     * 获取视频音量大小
     */
    private fun getVolume(): Float {
        return mPlayerController.getVolume() ?: 1F
    }

    /**
     * 获取播放器的倍速
     */
    private fun getSpeed(): Float {
        return mPlayerController.getSpeed() ?: 1F
    }

    /**
     * 暂停播放器播放
     */
    private fun playerPause() {
        mPlayerController.playerPause()
    }

    private fun playerResume() {
        mPlayerController.playerResume()
    }

    private fun playerChangeTo(url: String?) {
        mPlayerController.playerChangeTo(url)
    }

    /**
     * 设置进度条进度
     */
    private fun seekBarSeekTo(progress: Int, value: Long? = 0) {
        mPlayerController.seekBarSeekTo(progress, value)
        mViewModel.progressUp = mPlayerController.getProgress()
        currentHolder?.onPlayerProgressChanged(progress, value)
    }

    /**
     * 设置备案号
     */
    private fun registerNumber(value: Long) {
        mPlayerController.registerNumber(value)
    }

    /**
     * 改变播放链接进行播放
     */
    private fun changePlayerUrl(url: String?) {
        mPlayerController.changePlayerUrl(url)
    }

    private fun resetLayerPosition(position: Int) {
        mPlayerController.resetLayerPosition(position)
    }

    private fun getRenderFPS(): Float? {
        return mPlayerController.getRenderFPS()
    }

    private fun getAudioBitrate(): Float? {
        return mPlayerController.getAudioBitrate()
    }

    private fun getVideoBitrate(): Float? {
        return mPlayerController.getVideoBitrate()
    }

    private fun getDownloadBitrate(): Float? {
        return mPlayerController.getDownloadBitrate()
    }

    private fun prepareAndStart(currentPosition: Int, onPageSelected: Boolean) {
        mPlayerController.prepareAndStart(currentPosition, onPageSelected)
    }

    private fun setPlayerSpeed(speed: Float) {
        mPlayerController.setPlayerSpeed(speed)
    }

    /**
     * 记录当前Holder，以及当前Holder的 conroller
     * @param holder NewDetailVideoViewHolder
     */
    private fun bindViewHolder(holder: NewDetailVideoViewHolder) {
        currentHolder = holder
        mPlayerController.layer = holder.controller
    }

    private fun unbindViewHolder() {
        mPlayerController.unbindLayer(pausedByNetwork)
        currentHolder = null
    }

    private fun snapshot() {
        mPlayerController.snapshot()
    }

    private var loadingBegin = false

    private fun printIntercept(position: Int, vid: String?, method: String) {
        LogUtil.d(
            "detail_player_intercept", "播放器回调被拦截，当前position==$position   " +
                    "currentItem==${mViewBinding.vp.currentItem}" +
                    "vid==$vid   " +
                    "chapterId==${currentHolder?.mData?.chapterId}  " +
                    "method==${method}   " +
                    "url==${mPlayerController.layer?.getPlayerRenderView()?.getPlayer()?.mUrl}   " +
                    "currentUrl==$${currentHolder?.mData?.chapterId}"
        )
    }
    /**
     * 初始化播放器的回调监听
     * 初始化列表播放器
     */
    private fun initPlayerListener() {
        mPlayerController.initPlayerListener(snapShotListener, object : INewPlayerListener {
            override fun onPrepared(position: Int, vid: String?) {
                mPlayerController.layer?.setThumbnailListener()
                if (vid != currentHolder?.mData?.chapterId) {
                    printIntercept(position, vid, "onPrepared")
                    return
                }
                LogUtil.d(TAG, "onPrepared")
                recordPrepared(1)
                recordRenderingStart(0)
                LogUtil.d(
                    DetailMC.PLAYER_START_PLAY_TIME_TAG,
                    "onPrepared回调，打上onPrepared相关时间"
                )
                firstRendering = false
                playCount = 0
                noSliding()
                if (!layerPauseStatus() && !mIsOnBackground) {
                    resumePlay()
                }
                enableGesture(true)
                pausedByNetwork = false
                DialogMS.get()?.tryToShowCommentDialog(PageConstant.PAGE_ID_PLAYER)

                startPlayTime = 0L
                playingTime = 0L
                mViewModel.printLog(
                    TAG_PLAYING_DURATION,
                    "累计播放时长 onPlayStateChanged prepared 恢复初始值"
                )
                currentHolder?.updatePlayState(PlayState.PREPARED)
                mViewModel.updatePlayerState(PlayState.PREPARED)
            }

            override fun onInfo(
                code: Int,
                msg: String?,
                value: Long,
                position: Int,
                vid: String?,
                prohibitPlay: ((isCurrPlayer: Boolean) -> Unit)
            ) {
                prohibitPlay(vid == currentHolder?.mData?.chapterId)
                when (code) {
                    OnInfoListener.AutoPlayStart -> {
                        if (vid != currentHolder?.mData?.chapterId) {
                            printIntercept(position, vid, "OnInfoListener.AutoPlayStart")
                            return
                        }
                        // 视频已经开始播放，记录下当前视频的时长
                        currentVideoDuration = getPlayerDuration()
                    }

                    OnInfoListener.CurrentPosition -> {
                        if (vid != currentHolder?.mData?.chapterId) {
                            printIntercept(position, vid, "OnInfoListener.CurrentPosition")
                            LogUtil.d(
                                "xxxxx",
                                "非当前集在播放，position==$position    mViewBinding.vp.currentItem==${mViewBinding.vp.currentItem}    当前播放进度==$value"
                            )
                            return
                        }
                        if (activePause) {
                            activePause = false
                        }
                        mViewModel.played = true
                        LogUtil.d(DetailMC.APP_ERROR_TAG, "播放器进度回调，played ==true")
                        mVideoLifecycle.run {
                            onPlayProgressChanged(
                                value,
                                getPlayerDuration() ?: 0
                            )
                        }
                        currentDuration = value
                        var current = 0L
                        getPlayerDuration().let { duration ->
                            if (duration > 0) {
                                current = (value * 100 / duration)
                            }
                        }
                        seekBarSeekTo(current.toInt(), value)
                        registerNumber(value)

                        //上报播放事件
                        if (startReportDuration == -1L && value < getPlayerDuration() - 6 * 1000) {
                            startReportDuration = value
                        }
                        //播放开始事件上报，在开始后3秒上报  1701 1.5.1版本删除
//                        if (startReportDuration >= 0 && value - startReportDuration >= 3000L) {
//                            playEventReport(0, value)
//                        }
//                        //播放结束事件上报，在结束前3秒上报
//                        if (value >= mListPlayerView.getDuration() - 3 * 1000) {
//                            playEventReport(1, value)
//                        }
                        if (firstRendering || playCount <= 0) {
                            firstRendering = false
                            statusDismiss()
                            enableGesture(true)
                        }
                        if (playCount == 3) {
                            changeHolderCover(mCurrentPosition, GONE)
                        }
                        playCount++
                        reqBitrate()
                        if (isPause) {
                            pausePlay("should_not_play 1")
                        }
                        if (exitDelegate?.isVisible == true) {
                            if (!FollowTipManager.isFollowDialogShowing()) {
                                pausePlay("operation_popup")
                            }
                        }
                    }

                    OnInfoListener.BufferedPosition -> {
                        if (vid != currentHolder?.mData?.chapterId) {
                            printIntercept(position, vid, "OnInfoListener.BufferedPosition")
                            return
                        }
                        val bufferDuration = value - currentDuration
                        if (maxRemainBuffer < bufferDuration) {
                            maxRemainBuffer = bufferDuration
                        }
                    }
                }
            }

            override fun onSeekComplete(position: Int, vid: String?) {
                if (vid != currentHolder?.mData?.chapterId) {
                    printIntercept(position, vid, "onSeekComplete")
                    return
                }
                if (!loadingBegin) {
                    mViewModel.loadingScene = ReadingTE.LOADING_SCENE_PLAYING
                    LogUtil.d(
                        DetailMC.APP_LOADING_TAG,
                        "onSeekComplete结束，loadingScene设置为播放过程中"
                    )
                }
            }

            override fun onCompletion(position: Int, vid: String?) {
                if (vid != currentHolder?.mData?.chapterId) {
                    printIntercept(position, vid, "onCompletion")
                    kotlin.runCatching {
                        if (mPlayerController.layer?.isDragging == false) {
                            track(1, triggerScenario = VideoMC.PLAY_END_COMPLETE_SPECIAL)
                        }
                    }
                    return
                }
                LogUtil.d(TAG, "onCompletion 播放结束")
                FollowTipManager.stopPlayDurationStatistics()
                mViewModel.mVideoInfo?.userVideoVo?.apply {
                    chapterWatchedNum++
                }
                ActionRecorder.recordAction(ActionRecorder.ACTION_CHAPTER_COMPLETED)
                if (mPlayerController.layer?.isDragging == true) {
                    return
                }
                isPlayCompletion = true
                track(1, triggerScenario = VideoMC.PLAY_END_COMPLETE)
                currentHolder?.updatePlayState(PlayState.COMPLETION)
                mViewModel.updatePlayerState(PlayState.COMPLETION)
                mVideoLifecycle.onPlayCompletion(
                    getPlayingBookId() ?: "",
                    mViewModel.mChapterInfoVo?.chapterId ?: ""
                )
                if (mIsLoopPlay && mViewModel.playMode.value != PlayMode.PIP) {
                    LogUtil.d(TAG, "startPlay onCompletion()")
                    LogUtil.d("loop_play_tag", "startPlay onCompletion()")
                    stopPlay()
                    mViewBinding.vp.post {
                        startPlay(mCurrentPosition)
                    }
                } else {
                    if (mCurrentPosition != mViewBinding.vp.currentItem) {
                        mCurrentPosition = mViewBinding.vp.currentItem
                    }
                    mLastStopPosition = mCurrentPosition
                    isAutoSelectIndex = mCurrentPosition + 1
                    // 查找下一步剧
                    var nextPosition = mCurrentPosition + 1
                    var smoothScroll = true
                    if (isInPip()) {
                        smoothScroll = false
                        // 为自动跳过的广告添加埋点
                        (getViewHolder(nextPosition) as? AdVideoViewHolder)?.let { adHolder ->
                            if (adHolder.hasAd) {
                                val feedAd = adHolder.getFeedAd()
                                if (feedAd?.hasReportWhenPipSkip == false) {
                                    feedAd.hasReportWhenPipSkip = true
                                    DzTrackEvents.get().error()
                                        .type("小窗损失流量机会")
                                        .message("有广告winner")
                                        .adChnType(feedAd.chnType)
                                        .codeID(feedAd.codeId)
                                        .ecpm(feedAd.getEcpm().toString())
                                        .track()
                                }
                            } else {
                                DzTrackEvents.get().error()
                                    .type("小窗损失流量机会")
                                    .message("无广告winner")
                                    .track()
                            }
                        }
                        // 更新下一个视频的位置
                        nextPosition = mViewModel.getNextUnlockVideoChapterIndex(mCurrentPosition)
                    }
                    if (mViewModel.isInLastPage) {
                        showFinalChapterPrompt(mViewModel.recommendLiveData.value)
                    }
                    if (nextPosition != mCurrentPosition) {
                        chapterSwitching = true
                        selectChapter(nextPosition, VideoMC.PLAY_END_AUTO_SWAP, smoothScroll)
                    } else {
                        mViewModel.pausePlay(VideoMC.PLAY_END_COMPLETE)
                    }
                }
            }

            override fun onPlayStateChanged(status: Int, position: Int, vid: String?) {
                if (vid != currentHolder?.mData?.chapterId) {
                    printIntercept(position, vid, "onPlayStateChanged")
                    return
                }
                LogUtil.d(TAG, "onPlayStateChanged：$status")
                isPlaying = status == OnStateChangedListener.started
                when (status) {
                    OnStateChangedListener.initalized -> {  // 有时候 onRenderingStart 不回调
                        TimeMonitorManager.getMonitor(MonitorMC.SCENE_DETAIL).apply {
                            recordTime(MonitorMC.STAGE_VIDEO_PREPARE_END)
                            recordTime(MonitorMC.STAGE_END)
                            trackToSensor()
                            end()
                        }
                    }

                    OnStateChangedListener.prepared -> {  // 准备好
//                        startPlayTime = 0L
//                        playingTime = 0L
//                        mViewModel.printLog(
//                            TAG_PLAYING_DURATION,
//                            "累计播放时长 onPlayStateChanged prepared 恢复初始值"
//                        )
//                        currentHolder?.updatePlayState(PlayState.PREPARED)
//                        mViewModel.updatePlayerState(PlayState.PREPARED)
//                        mViewModel.beginPlayTrack()
                    }

                    OnStateChangedListener.started -> {  // 播放中
                        LogUtil.d(TAG, "started 播放中")
                        if (mViewModel.keepImmersive) {
                            mViewModel.setPlayMode(PlayMode.IMMERSIVE)
                        } else {
                            cancelImmersive("player started")
                            startImmersiveTimer("播放中回调")
                        }
                        FollowTipManager.startPlayDurationStatistics()
                        startPlayTime = System.currentTimeMillis()
                        startPlayingTimer()
                        currentHolder?.updatePlayState(PlayState.PLAYING)
                        mViewModel.updatePlayerState(PlayState.PLAYING)
                        mViewModel.printLog(
                            TAG_PLAYING_DURATION,
                            "累计播放时长 onPlayStateChanged started 记录startPlayTime"
                        )
                        if (!mViewModel.canPlay()) {
                            LogUtil.d(TAG, "视频开始播放，但此时逻辑上不允许播放，执行手动暂停")
                            pausePlay("should_not_play 2")
                        }

                        mVideoLifecycle.onPlayStart(
                            getPlayingBookId() ?: "",
                            mViewModel.mChapterInfoVo?.chapterId ?: ""
                        )
                        if ((mViewModel.mChapterInfoVo?.contentUlrIndex ?: -1) > -1 &&
                            (!mViewModel.mChapterInfoVo?.mp4720pSwitchUrl.isNullOrEmpty()) &&
                            (mViewModel.mChapterInfoVo?.mp4720pSwitchUrl?.size
                                ?: 0) > (mViewModel.mChapterInfoVo?.contentUlrIndex ?: 0)
                        ) {

                            //播放成功 重置为切换地址默认状态
                            mViewModel.mChapterInfoVo?.switchState = SwitchState.NO_SWITCH
                        }
                    }

                    OnStateChangedListener.paused -> {  // 暂停
                        playCount = 0
                        LogUtil.d(TAG, "暂停播放")
                        currentHolder?.updatePlayState(PlayState.PAUSING)
                        mViewModel.updatePlayerState(PlayState.PAUSING)
//                        if (startPlayTime > 0) {
//                            playingTime += (System.currentTimeMillis() - startPlayTime)
//                            startPlayTime = 0L
//                        }
                        track(1)  // 上报播放结束
//                        cancelImmersive(true)
                        mVideoLifecycle.onPlayPause(
                            getPlayingBookId() ?: "",
                            mViewModel.mChapterInfoVo?.chapterId ?: ""
                        )
                        FollowTipManager.stopPlayDurationStatistics()
                    }

                    OnStateChangedListener.stopped -> {  // 停止
                        // 播放中滑动切集时不回调此状态
                        onPlayStop()
                    }

                    OnStateChangedListener.error -> {  // 错误
                        playCount = 0
                        LogUtil.d(TAG, "播放错误")
                        cancelImmersive("player error")
                        currentHolder?.updatePlayState(PlayState.ERROR)
                        mViewModel.updatePlayerState(PlayState.ERROR)
                        mVideoLifecycle.onPlayError(
                            getPlayingBookId() ?: "",
                            mViewModel.mChapterInfoVo?.chapterId ?: ""
                        )
                    }
                }
            }

            override fun onRenderingStart(position: Int, vid: String?) {
                if (vid != currentHolder?.mData?.chapterId) {
                    printIntercept(position, vid, "onRenderingStart")
                    mViewModel.interceptStartPlayTrack(
                        mViewBinding.vp.currentItem,
                        position,
                        vid,
                        currentHolder?.mData?.chapterId
                    )
                    return
                }
                maxRemainBuffer = 0
                statusDismiss()
                mViewModel.played = true
                LogUtil.d(DetailMC.APP_ERROR_TAG, "播放器首帧回调，played ==true")
                LogUtil.d(TAG, "onRenderingStart")
//                track(0, System.currentTimeMillis() - prepareTime)
                recordRenderingStart(1)
                recordUserSenseTime(1)
                LogUtil.d(DetailMC.PLAYER_START_PLAY_TIME_TAG, "首帧回调，打上首帧显示当前点")
                track(
                    0,
                    isPreloadPlayer = mPlayerController.isPreRenderPlayer(),
                    isPlayerPrePrepared = isPlayerPrePrepared
                )
                isPlayerPrePrepared = false
                TimeMonitorManager.getMonitor(MonitorMC.SCENE_DETAIL).apply {
                    recordTime(MonitorMC.STAGE_VIDEO_PREPARE_END)
                    recordTime(MonitorMC.STAGE_END)
                    trackToSensor()
                    end()
                }
                changeHolderCover(mCurrentPosition, GONE)
//                mViewModel.mChapterInfoVo?.run {
//                    mListPlayerView.startPlaySuccess(mViewModel.mChapterInfoVo?.chapterId)
//                        ?.let { url ->
//                            if (url != m3u8720pUrl) {
//                                trackErrorRecovery(url)
//                                m3u8720pUrl = url
//                            }
//                        }
//                }
                startReportDuration = -1L
                cReportBookId = null
                firstRendering = true
                if (layerPauseStatus() || mIsOnBackground || !mIsKeepPlaying || isPauseAdShow()) {
                    pausePlay("should_not_play 3")
                }
//                //播放当前集并预加载后面几集
//                mViewModel.preLoadChapters(mCurrentPosition)
                mViewModel.updateViewHistory()
                if (isLandScape()) {
                    showLandGuide()
                }
                if (mLastStopPosition == mCurrentPosition) {
                    mLastStopPosition = -1
                }
                if (mViewBinding.vp.offscreenPageLimit == ViewPager2.OFFSCREEN_PAGE_LIMIT_DEFAULT) {
                    mViewBinding.vp.offscreenPageLimit = 1
                }
            }

            override fun onError(
                errorCode: Int,
                errorMsg: String,
                extra: String?,
                position: Int,
                vid: String?
            ) {
                if (vid != currentHolder?.mData?.chapterId) {
                    printIntercept(position, vid, "onError")
                    return
                }
                loadingBegin = false
                LogUtil.e(getUiTag(), "$errorCode --- $errorMsg")
//                if (NetWorkUtil.isNetConnected(this@PlayDetailActivity)) {
//                    mListPlayerView.changeUrl(mViewModel.mChapterInfoVo?.chapterId) {
//                        mViewModel.statusPoster.statusDismiss().post()
//                        pausedByNetwork = true
//                        pausePlay("player_error")
//                    }
//                } else {
                statusDismiss()
//                }
                //播放报错打点
                trackPlayError(errorCode, errorMsg, extra)
                //播放报错：检测是否需要重置备用地址下标 或者 停止播放
                checkSwitchStateAndStop()
            }

            override fun onLoadingBegin(position: Int, vid: String?) {
                LogUtil.d("new_detail_load_tag", "onLoadingBegin   bookname==${mViewModel.mChapterInfoVo?.chapterName}")
                val duration =
                    if (getPlayerDuration() > 0) getPlayerDuration() else currentVideoDuration
                // 打点上报
                mViewModel.sensorPreload(
                    0,
                    duration,
                    getVolume(),
                    getSpeed(),
                    true,
                    getDownloadBitrate()
                )
                if (vid != currentHolder?.mData?.chapterId) {
                    printIntercept(position, vid, "onLoadingBegin")
                    return
                }
                loadingBegin = true
                if (startPlayTime > 0) {
                    playingTime += (System.currentTimeMillis() - startPlayTime)
                    startPlayTime = 0L
                    mViewModel.printLog(
                        TAG_PLAYING_DURATION,
                        "累计播放时长 onLoadingBegin 累加playingTime，startPlayTime清零"
                    )
                }
                stopPlayingTimer("onLoadingBegin")
                statusDismiss()
                mViewBinding.compLoading.show(isLandscape = isLandScape())
                // 卡顿提醒定时任务
                LogUtil.d(TAG_PLAYING_DURATION, "卡顿====开始 ")
                isLayTimeRunning = true
                createLayTimeTask()
                mVideoLifecycle.onVideoLoadingBegin()
            }

            override fun onLoadingEnd(position: Int, vid: String?) {
                LogUtil.d("new_detail_load_tag", "onLoadingEnd   bookname==${mViewModel.mChapterInfoVo?.chapterName}")
                mViewModel.sensorPreload(1, mPlayerController.getLoadingTime(), getVolume(), getSpeed(), true)
                if (vid != currentHolder?.mData?.chapterId) {
                    printIntercept(position, vid, "onLoadingEnd")
                    return
                }
                loadingBegin = false
                isLayTimeRunning = false
                LogUtil.d(TAG_PLAYING_DURATION, "卡顿====结束")
                startPlayTime = System.currentTimeMillis()
                startPlayingTimer()
                mViewModel.printLog(
                    TAG_PLAYING_DURATION, "累计播放时长 onLoadingEnd 记录startPlayTime"
                )
                statusDismiss()
                LogUtil.d(
                    DetailMC.APP_LOADING_TAG,
                    "loading结束，开始打点，触发场景为${mViewModel.loadingScene}"
                )
                mVideoLifecycle.onVideoLoadingEnd()
                mViewModel.loadingScene = ReadingTE.LOADING_SCENE_PLAYING
                LogUtil.d(
                    DetailMC.APP_LOADING_TAG,
                    "loading结束，loadingScene设置为播放过程中"
                )
            }

            override fun onPlaySpeedChanged(speed: Float, position: Int, vid: String?) {
                if (vid != currentHolder?.mData?.chapterId) {
                    printIntercept(position, vid, "onPlaySpeedChanged")
                    return
                }
                // 播放速度发生改变
                mVideoLifecycle.onPlaySpeedChanged(speed)
            }
        })
    }

    /**
     * 初始化播放界面
     */
    private fun initPlayerControllerLayer() {
        isPlaying = false
        // 更新判断window大小
        DetailMS.get()?.updateWindowRatio()
        mViewBinding.layerBack.bindSpeed(mViewModel.playSpeed)
        LogUtil.d(
            ExperimentMC.HTTP_DNS_TAG, "二级播放器初始化播放器，是否打开httpDns==" + BBaseMC.httpDns
        )
        mPlayerController.layer?.run {
            mCurPosition = <EMAIL>
            mIsPause = false
            isLoop(false)
            resetPlayerSize()
            updateViewAlpha(1f)
            enableGesture(false)
            seekBarSeekTo(0)
            setPlayerSpeed(mViewModel.playSpeed)
            setOnGestureListener(object : NewPlayerControllerLayer.OnGestureListener {
                override fun onSingleTapConfirmed() {
                    //播放结束前1秒，无法手动出发暂停a'o'g'n
                    if (getPlayerDuration() - currentDuration < 1000) {
                        return
                    }
                    onSingleTab()
                }

                override fun onDoubleTap() {
                    <EMAIL>()
                }

                override fun onLongClick() {
                    stopScroll()
                    if (mViewModel.playMode.value == PlayMode.NORMAL) {
                        immersiveTimer?.cancel()
                    }
                    currentHolder?.onGestureStart(GestureType.LONG_PRESS)
                    <EMAIL>(GestureType.LONG_PRESS)
                    stopWatch()

                    ElementClickUtils.trackViewAppClick(
                        <EMAIL>,
                        bookID = mViewModel.mVideoInfo?.videoInfo?.bookId,
                        bookName = mViewModel.mVideoInfo?.videoInfo?.bookName,
                        chaptersNum = mViewModel.mChapterInfoVo?.chapterIndex,
                        chaptersId = mViewModel.mChapterInfoVo?.chapterId,
                        content = "2.0X",
                        elementType = "切换倍速",
                        elementPosition = "长按屏幕",
                    )
                    getRatingComp().checkToDismissWithAnim()
                }

                override fun onGestureEnd(gestureType: GestureType) {
                    super.onGestureEnd(gestureType)
                    when (gestureType) {
                        GestureType.LONG_PRESS -> {
                            if (mViewModel.playMode.value == PlayMode.NORMAL) {
                                startImmersiveTimer("长按结束")
                            }
                            currentHolder?.onGestureEnd(GestureType.LONG_PRESS)
                            <EMAIL>(
                                GestureType.LONG_PRESS
                            )
                            startWatch()
                        }

                        else -> {}
                    }
                    noSliding()
                }

                override fun onStartTrackingTouch() {
                    isDragging = true
                    stopScroll()
                    if (mViewModel.playMode.value == PlayMode.NORMAL) {
                        immersiveTimer?.cancel()
                    }
                    currentHolder?.onGestureStart(GestureType.DRAGGING)
                    stopWatch()
                }

                override fun onStopTrackingUpdateSeekbar(currentTime: Long) {
                    mViewModel.clearPause()
                    if (NetWorkUtil.isNetConnected(this@NewPlayDetailActivity)) {
                        seekToPlay(currentTime)
                        mViewModel.loadingScene = ReadingTE.LOADING_SCENE_MANUAL_CHANGE_PROGRESS
                        LogUtil.d(
                            DetailMC.APP_LOADING_TAG,
                            "拖动进度条，loadingScene设置为手动变更进度"
                        )
                    } else if (mCurrentPosition >= 0 && mCurrentPosition < mViewModel.getDataList().size && downLoadUrlCanPlay(
                            mViewModel.getChapterInfoVo(mCurrentPosition)
                        )
                    ) {
                        seekToPlay(currentTime)
                        mViewModel.loadingScene = ReadingTE.LOADING_SCENE_MANUAL_CHANGE_PROGRESS
                        LogUtil.d(
                            DetailMC.APP_LOADING_TAG,
                            "拖动进度条，loadingScene设置为手动变更进度"
                        )
                    } else {
                        loadingBegin = false
                        ToastManager.showToast(context.getString(com.dz.business.base.R.string.bbase_network_error))
                        mViewModel.loadingScene = ReadingTE.LOADING_SCENE_PLAYING
                        LogUtil.d(
                            DetailMC.APP_LOADING_TAG,
                            "拖动进度条，无网络，loadingScene设置为播放过程中"
                        )
                    }
                }

                override fun onStopTrackingTouch(time: Long) {  // 拖动停止
                    stopDrag()  // 结束进度条的交互
                    resumePlay()  // 恢复播放

                }
            })
        }
    }



    private fun getCurBuffer(): Long {
        val holder = ViewPager2Helper.findViewHolderByPosition(
            mViewBinding.vp,
            mViewBinding.vp.currentItem
        )
        if (holder is NewDetailVideoViewHolder) {
            LogUtil.d(
                "player_cur_buffer",
                "NewPlayDetailActivity：当前播放器剩余缓存== ${holder.controller.getCurBuffer()}" +
                        "   播放器tag==${holder.controller.getVid()}" +
                        "   当前bookid==${mPageAdapter.getData(mViewBinding.vp.currentItem)?.chapterId}"
            )
            return holder.controller.getCurBuffer()
        }
        return -1
    }

    private val mPlayerController by lazy { DetailPlayerController() }

    override fun getVideoCurrBufferMs() = getCurBuffer()

    /**
     * 屏幕常亮
     */
    fun openScreenOn() {
        kotlin.runCatching {
            window.setFormat(PixelFormat.TRANSLUCENT)
            window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
            LogUtil.d("screen_on_tag","新二级页屏幕常亮")
        }
    }

    /**
     * 屏幕取消常亮
     */
    fun closeScreenOn() {
        kotlin.runCatching {
            window.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
            LogUtil.d("screen_on_tag", "新二级页屏幕取消常亮")
        }
    }

    private fun onPlayModeChanged(mode: PlayMode) {
        // 通知各个layer以及Holder 播放器当前的模式变了
        if (mode == PlayMode.IMMERSIVE || (mViewModel.previousPlayMode == PlayMode.IMMERSIVE && mode == PlayMode.NORMAL)) {
            val position = mViewBinding.vp.currentItem
            if (position in 0 until mPageAdapter.itemCount) {
                val startPosition = maxOf(0, position - 1)
                val itemCount = minOf(mPageAdapter.itemCount - startPosition, 3) // 更新最多 3 个 item
                LogUtil.d(DetailMC.TAG_PLAY_MODE, "onPlayModeChanged startPosition=$startPosition, itemCount=$itemCount")
                mPageAdapter.notifyItemRangeChanged(
                    startPosition,
                    itemCount,
                    DetailMC.PAYLOAD_PLAY_MODE
                )
            }
        } else {
            mPageAdapter.notifyItemRangeChanged(
                0,
                mPageAdapter.itemCount,
                DetailMC.PAYLOAD_PLAY_MODE
            )
        }
        if (mode == PlayMode.PIP) {
            //小窗尝试关闭福利锚点广告
            tryCloseWelfareAnchorAd(false, "小窗模式")
        }
        mViewBinding.layerBack.onPlayModeChanged(mode)
        mViewBinding.layerGuide.onPlayModeChanged(mode)
        mViewBinding.comVipTips.onPlayModeChanged(mode)
        mViewBinding.bottomLayer.onPlayModeChanged(mode)
//            mViewBinding.topShadow.visibility = if (mode == PlayMode.IMMERSIVE) GONE else VISIBLE
        when (mode) {
            PlayMode.NORMAL, PlayMode.LOCKED -> {  // 正常模式
                // 判断是否可以显示福利挂件
                val show = !isPauseAdShow() && canShowPendantVisible()
                LogUtil.d(
                    OperationMC.TAG_PENDANT, "playMode changed change pendant。visibility:$show"
                )
                welfareWidget?.visibility = if (!show) GONE else VISIBLE
                // 修改通知栏
                if (!isLandScape()) {
                    getImmersionBar().transparentStatusBar()
                        .navigationBarColor(R.color.common_FF0F0F0F)
                        .navigationBarDarkIcon(DeviceInfoUtil.isDarkTheme(this))
                        .statusBarDarkFont(DeviceInfoUtil.isDarkTheme(this))
                        .hideBar(BarHide.FLAG_SHOW_BAR).init()
                    //竖屏沉浸式到正常模式可以弹出卡顿提示弹窗 就弹出
                    if (canShowLayToast && mode == PlayMode.NORMAL && mViewModel.nowPlayMode == PlayMode.IMMERSIVE) {
                        createLayTimeTask()
                    }
                }
                mViewModel.nowPlayMode = mode
            }

            PlayMode.IMMERSIVE -> {  // 沉浸式
                // 剧末推荐、剧末承接显示时隐藏返回按钮
                if (isFinalChapter()) {
                    mViewBinding.layerBack.hideTitle()
                }
                LogUtil.d(DetailMC.TAG_IMMERSIVE, "playMode changed hide welfare widget")
                LogUtil.d(
                    OperationMC.TAG_PENDANT,
                    "进入沉浸式。有挂件：${welfareWidget != null} 挂件是否hide：${mViewModel.playerPendantConfig?.immersiveHide == 0}"
                )
                // 沉浸式时是否显示福利挂件，由挂件配置里的开关控制
                if (mViewModel.playerPendantConfig?.immersiveHide == 0) {
                    welfareWidget?.visibility = GONE
                }
                // 根据当前是横竖屏显示对应的沉浸式操作
                if (isLandScape()) {
//                        getImmersionBar()
//                            .hideBar(BarHide.FLAG_HIDE_BAR)
//                            .init()
                    enterLandImmersive()
                } else {
                    getImmersionBar().hideBar(BarHide.FLAG_HIDE_STATUS_BAR).init()
                    //竖屏正常模式到沉浸式收起弹窗
                    if (mViewModel.nowPlayMode == PlayMode.NORMAL) {
                        mViewBinding.comVipTips.hideMenuTips()
                    }
                }
                mViewModel.nowPlayMode = mode
            }

            else -> {}
        }
    }

    /**
     * 是否允许广告SDK弹出Toast
     */
    override fun canShowToast(msg: String): Boolean {
        val isPreview = mViewModel.mChapterInfoVo?.isPreviewChapter() == true
        return !(isPreview && msg.contains("无法获得奖励"))
    }
}