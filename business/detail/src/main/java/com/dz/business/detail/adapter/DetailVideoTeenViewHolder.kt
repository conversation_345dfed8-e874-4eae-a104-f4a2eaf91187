package com.dz.business.detail.adapter

import android.app.Activity
import android.graphics.Point
import android.view.View
import android.view.View.GONE
import android.view.View.VISIBLE
import android.widget.ImageView
import com.dz.business.base.data.BBaseKV
import com.dz.business.base.data.bean.ChapterInfoVo
import com.dz.business.base.ui.player.adapter.BaseViewHolder
import com.dz.business.base.ui.player.ui.ListPlayerControllerTeenComp
import com.dz.business.detail.R
import com.dz.business.detail.vm.VideoListVM
import com.dz.foundation.imageloader.load

/**
 *@Author: feihk
 *@Date: 2023/2/10
 *@Description:
 *@Version:1.0
 */
class DetailVideoTeenViewHolder(view: View) : BaseViewHolder<ChapterInfoVo, VideoListVM>(view) {


    lateinit var controller: ListPlayerControllerTeenComp//控制层

    private lateinit var coverView: ImageView

    private lateinit var mScreenPoint: Point

    override fun initView() {
        val displayMetrics = mContext.resources.displayMetrics
        mScreenPoint = Point()
        mScreenPoint.x = displayMetrics.widthPixels
        mScreenPoint.y = displayMetrics.heightPixels

        controller = itemView.findViewById(R.id.controller)
        coverView = controller.mViewBinding.imgThumb

        // 修改封面的缩放模式
        val height = BBaseKV.playerDetailHeightWidthRatio * 9
        if (height == 0.0f || (height > 15 && height < 19.56)) {  // 裁剪
            coverView.scaleType = ImageView.ScaleType.CENTER_CROP
        } else {  // 留黑边，不进行裁剪
            coverView.scaleType = ImageView.ScaleType.FIT_CENTER
        }
    }

    override fun initListener() {
    }

    fun onAttachedToWindow() {
        coverView.visibility = VISIBLE
        if (mViewModel?.mVideoInfo?.videoInfo?.isPayVideo() == true) {
            controller.icTagsVisibility(VISIBLE, R.drawable.bbase_ic_pay)
        } else {
            controller.icTagsVisibility(GONE)
        }
        updateVideoIndex()
    }

    override fun setData(data: ChapterInfoVo, vm: VideoListVM, tag: Int) {
        mData = data
        coverView.visibility = VISIBLE
        mViewModel = vm
        if (mContext is Activity) {
            val activity = mContext as Activity
            if (!activity.isFinishing || !activity.isDestroyed) {
                data.chapterImg?.let { loadPicture(it) }
            }
        }
        if (vm.mVideoInfo?.videoInfo?.isPayVideo() == true) {
            controller.icTagsVisibility(VISIBLE, R.drawable.bbase_ic_pay)
        } else {
            controller.icTagsVisibility(GONE)
        }
        updateVideoIndex()

        vm.mVideoInfo?.let { detail ->
            detail.videoInfo.run {
                controller.setVideoName(bookName)
                if (vm.showActor) {
                    controller.setHero(performerInfo?.actorPhoto, performerInfo?.actorVideoNum)
                    controller.setHeroine(
                        performerInfo?.actressPhoto,
                        performerInfo?.actressVideoNum
                    )
                }
            }

        }
    }

    fun updateVideoIndex(){
        mData?.let { data ->
            mViewModel?.mVideoInfo?.videoInfo?.let { info ->
                if (info.finishStatus == 0) {
                    controller.setVideoIndex("第${data.chapterIndex}集·更新至${info.updateNum}集", info.introduction)
                } else {
                    controller.setVideoIndex("第${data.chapterIndex}集·全${info.updateNum}集", info.introduction)
                }
            }
        }
    }
    private fun loadPicture(coverPath: String) {
        coverView.load(coverPath, width = 375, height = 752)
    }
}