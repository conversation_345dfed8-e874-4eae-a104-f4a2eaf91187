package com.dz.business.detail.vm

import BBaseME
import android.app.Activity
import android.os.Handler
import android.os.Looper
import android.text.TextUtils
import androidx.lifecycle.LiveData
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.viewModelScope
import com.alibaba.fastjson.JSONObject
import com.blankj.utilcode.util.GsonUtils
import com.dianzhong.base.data.bean.error.ErrorCode
import com.dianzhong.base.util.AppUtil
import com.dz.business.DzDataRepository
import com.dz.business.base.BBaseMC
import com.dz.business.base.BBaseMC.TAG_RESOLUTION
import com.dz.business.base.BBaseMR
import com.dz.business.base.ad.AdMS
import com.dz.business.base.ad.callback.IAdLoader
import com.dz.business.base.data.BBaseKV
import com.dz.business.base.data.NewDrawAdConfig
import com.dz.business.base.data.PageConstant
import com.dz.business.base.data.bean.AdConfigVo
import com.dz.business.base.data.bean.AscribeBean
import com.dz.business.base.data.bean.BaseBookInfo
import com.dz.business.base.data.bean.BaseEmptyBean
import com.dz.business.base.data.bean.ChapterInfoVo
import com.dz.business.base.data.bean.ContinuousConfigVo
import com.dz.business.base.data.bean.FollowSourceType
import com.dz.business.base.data.bean.GiftVipConf
import com.dz.business.base.data.bean.MultiBtnConf
import com.dz.business.base.data.bean.OperationVo
import com.dz.business.base.data.bean.PopUpConfigVo
import com.dz.business.base.data.bean.PreLoadFunSwitchVo
import com.dz.business.base.data.bean.RecId
import com.dz.business.base.data.bean.RecommendVideoInfo
import com.dz.business.base.data.bean.ResolutionRateVo
import com.dz.business.base.data.bean.StrategyInfo
import com.dz.business.base.data.bean.SwitchState
import com.dz.business.base.data.bean.TierPlaySourceVo
import com.dz.business.base.data.bean.ToastInfo
import com.dz.business.base.data.bean.UnLockConfigVo
import com.dz.business.base.data.bean.VideoDetailBean
import com.dz.business.base.data.bean.VideoEndStyle
import com.dz.business.base.data.bean.VideoInfoVo
import com.dz.business.base.data.bean.WelfarePendantConfigVo
import com.dz.business.base.data.bean.WxShareConfigVo
import com.dz.business.base.data.enums.EnterTypeMode
import com.dz.business.base.detail.DetailMC
import com.dz.business.base.detail.DetailMC.Companion.TAG_PLAYER_CONFIG
import com.dz.business.base.detail.DetailMC.Companion.UNLOCK
import com.dz.business.base.detail.DetailME
import com.dz.business.base.detail.DetailMR
import com.dz.business.base.detail.intent.VideoListIntent
import com.dz.business.base.download.DownloadMS
import com.dz.business.base.experiment.ExperimentMC
import com.dz.business.base.flutter.FlutterMS
import com.dz.business.base.home.AddSubtractLikesCallback
import com.dz.business.base.home.FavoriteCallback
import com.dz.business.base.home.HomeMC
import com.dz.business.base.home.HomeME
import com.dz.business.base.home.HomeMS
import com.dz.business.base.home.RecommendReqCallback
import com.dz.business.base.livedata.CommLiveData
import com.dz.business.base.load.DBHelper
import com.dz.business.base.main.MainMR
import com.dz.business.base.main.intent.MainIntent
import com.dz.business.base.network.AdNetWork
import com.dz.business.base.network.HttpResponseModel
import com.dz.business.base.personal.PersonalME
import com.dz.business.base.priority.PriorityMC
import com.dz.business.base.priority.PriorityTaskManager
import com.dz.business.base.recharge.data.PayListResp
import com.dz.business.base.search.KocAscribeCallback
import com.dz.business.base.search.SearchMS
import com.dz.business.base.splash.SplashMS
import com.dz.business.base.splash.UploadNotificationStatusCallback
import com.dz.business.base.splash.data.PushUploadData
import com.dz.business.base.track.PlayerTrack
import com.dz.business.base.ui.BaseDialogComp
import com.dz.business.base.ui.component.status.StatusPoster
import com.dz.business.base.utils.ActionRecorder
import com.dz.business.base.utils.AdvertisementUtil
import com.dz.business.base.utils.CommInfoUtil
import com.dz.business.base.utils.DeviceInfoHelper
import com.dz.business.base.utils.GsonUtil
import com.dz.business.base.utils.HmHiveSDK
import com.dz.business.base.utils.OCPCManager
import com.dz.business.base.utils.PushDialogManager
import com.dz.business.base.video.Callback
import com.dz.business.base.video.OnRequestCallback
import com.dz.business.base.video.VideoMC
import com.dz.business.base.video.VideoMC.TAG_COMMENT
import com.dz.business.base.video.VideoMS
import com.dz.business.base.video.data.CommentNumBean
import com.dz.business.base.video.data.CommentNumCheckDatabaseBean
import com.dz.business.base.vm.event.RequestEventCallback
import com.dz.business.base.vm.event.VMEventOwner
import com.dz.business.base.welfare.WelfareMC
import com.dz.business.bcommon.utils.PlayingStatisticsMgr
import com.dz.business.detail.data.ChapterInfo
import com.dz.business.detail.data.ChapterUnlockBean
import com.dz.business.detail.data.DetailKV
import com.dz.business.detail.data.LoadResult
import com.dz.business.detail.data.PlayerConfig
import com.dz.business.detail.data.StatusPosterBean
import com.dz.business.detail.delegate.FollowTipManager
import com.dz.business.detail.enums.BottomStyle
import com.dz.business.detail.enums.Orientation
import com.dz.business.detail.enums.PlayMode
import com.dz.business.detail.interfaces.IPlayerDetail
import com.dz.business.detail.network.DetailNetWork
import com.dz.business.detail.ui.component.AdUnlockedDialogComp
import com.dz.business.detail.unlock.VideoUnlockPresenter
import com.dz.business.detail.util.DetailRecommendUtil
import com.dz.business.detail.util.DrawAdManager
import com.dz.business.detail.util.PauseAdManager
import com.dz.business.detail.util.VideoTrackUtils
import com.dz.business.repository.entity.LikesEntity
import com.dz.business.track.base.addParam
import com.dz.business.track.events.DzTrackEvents
import com.dz.business.track.events.hive.HiveTE
import com.dz.business.track.events.sensor.AdTE
import com.dz.business.track.events.sensor.ErrorTE
import com.dz.business.track.events.sensor.OperationClickTE
import com.dz.business.track.events.sensor.OperationExposureTE
import com.dz.business.track.events.sensor.ReadingTE
import com.dz.business.track.monitor.MonitorMC
import com.dz.business.track.monitor.trackApiRequestTime
import com.dz.business.track.monitor.trackPlayerTime
import com.dz.business.track.trace.OmapNode
import com.dz.business.track.trace.QmapNode
import com.dz.business.video.IVideoBehavior
import com.dz.business.video.VideoVM
import com.dz.business.video.danmu.VideoDanMuManager
import com.dz.business.video.data.OperateReportBean
import com.dz.business.video.data.WelfareReward
import com.dz.business.video.db.CommentsDatabase
import com.dz.business.video.network.VideoNetwork
import com.dz.business.video.track.TrackChapterInfo
import com.dz.business.video.unlock.UnlockBean
import com.dz.business.video.unlock.UnlockDelegate
import com.dz.business.video.utils.VideoPlayTimeManager
import com.dz.business.welfare.WelfareMS
import com.dz.business.welfare.data.StageReadAward
import com.dz.business.welfare.data.TaskReportResult
import com.dz.business.welfare.data.WelfareKV
import com.dz.business.welfare.interfaces.ReportCallback
import com.dz.foundation.base.friendly.noOpDelegate
import com.dz.foundation.base.manager.task.Task
import com.dz.foundation.base.manager.task.TaskManager
import com.dz.foundation.base.manager.task.TaskManager.Companion.intervalTask
import com.dz.foundation.base.module.AppModule
import com.dz.foundation.base.utils.DateUtil
import com.dz.foundation.base.utils.LocalActivityMgr
import com.dz.foundation.base.utils.LogUtil
import com.dz.foundation.base.utils.NetWorkUtil
import com.dz.foundation.base.utils.NotificationUtil
import com.dz.foundation.base.utils.SystemTimeUtils
import com.dz.foundation.base.utils.monitor.PlayerMonitorManager
import com.dz.foundation.base.utils.monitor.TimeMonitorManager
import com.dz.foundation.event.EventLiveData
import com.dz.foundation.imageloader.GlideUtils
import com.dz.foundation.network.onError
import com.dz.foundation.network.onResponse
import com.dz.foundation.network.onStart
import com.dz.foundation.network.requester.RequestException
import com.dz.platform.ad.AdManager
import com.dz.platform.ad.ChapterIntervalUtil
import com.dz.platform.ad.CoinsDropIntervalUtil
import com.dz.platform.ad.DrawForceViewTimeUtil
import com.dz.platform.ad.data.AdKV
import com.dz.platform.ad.data.BannerAdKV
import com.dz.platform.ad.data.DrawAdKV
import com.dz.platform.ad.data.DrawDataUtil
import com.dz.platform.ad.lifecycle.PlayDetailLifeCycle
import com.dz.platform.ad.lifecycle.ServerAdDataDispatcher
import com.dz.platform.ad.manager.MineMallAdManager
import com.dz.platform.ad.sky.FeedAd
import com.dz.platform.ad.sky.InterstitialAd
import com.dz.platform.ad.vo.MallAdVo
import com.dz.platform.common.router.onDismiss
import com.dz.platform.common.router.onShow
import com.dz.platform.common.toast.ToastManager
import com.google.gson.reflect.TypeToken
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.lang.ref.WeakReference
import java.util.concurrent.CopyOnWriteArrayList

/**
 * @Author: feihk
 * @Date: 2023/2/10
 * @Description:
 * @Version:1.0
 */
class VideoListVM(private val stateHandle: SavedStateHandle) : VideoVM<VideoListIntent>(),
    VMEventOwner<RequestEventCallback>, IVideoBehavior {
    var adLoader: IAdLoader? = null//去广告激励视频广告对象

    var mOmap: StrategyInfo? = null

    /**
     * 去广告激励视频是否加载中
     */
    var adLoading = false
    /**
     * 商城广告是否加载中
     */
    var adMallLoading = false


    companion object {
        const val ERROR_VIDEO_LOAD_FAILED = 10  // 加载视频信息失败
        const val ERROR_VIDEO_OFF_SHELF = 11  // 视频下架
        const val ERROR_UNLOCK_FAILED = 20  // 解锁失败

        const val SAVE_STATE_KEY_CUR_POSITION = ""
    }

    var pauseAdCommLiveData = CommLiveData<String>()

    var guideTime:Int? = null //展示引导时长
    var isGuideStatus :Int? = null //是否展示引导

    var checkADPic :Int = 0 //检查本次两张图片是否加载完成，为2时可以展示弹窗

    var checkLockPic :Int = 0 //检查本次两张图片是否加载完成，为2时可以展示弹窗

    var immersiveADTimes :Int = 0 //每次进入二级页后遇到的沉浸式广告次数，每个activity独立

    var immersiveADLimit :Int = -1 //每次进入二级页后遇到的沉浸式广告次数，每个activity独立

    var isShowVIPComp : Boolean = false //加锁防止展示两次

    var lockTimes : Int = -1 //解锁上线

    var sceneMap: Map<String, String> = emptyMap()

    var scene: Int? = -1

    val commentNumLiveData = CommLiveData<CommentNumBean?>()


    var loadCommLiveData = CommLiveData<LoadResult>()

    var popUpConfigVo : PopUpConfigVo? = null

    var vipCompIntervalTime : Int = 0


    val videoListLiveData = CommLiveData<MutableList<ChapterInfoVo>>()

    var previewConfigChanged: Boolean = false

    /**
     * 对应项的本地数据
     */
    val commentLocalData = CommLiveData<MutableList<CommentNumCheckDatabaseBean>>()

    //播放器预加载配置liveData
    val preLoadLiveData = CommLiveData<PreLoadFunSwitchVo?>()

    val unlockChapterLiveData = CommLiveData<ChapterUnlockBean>()

    val preLoadChapterList = CommLiveData<List<String>>()

    val favoriteLiveData = CommLiveData<Boolean>()

    //底部banner高度改变LiveData
    val bannerHeightLiveData = CommLiveData<Int>()

    //视频列表包含章节和广告
    private var mVideoAndAdList: CopyOnWriteArrayList<ChapterInfoVo> = CopyOnWriteArrayList()
    private var mAdLifeCycle: PlayDetailLifeCycle? = null

    //当前的视频列表 可能是 mVideoAndAdList 也可能是 mVideoList
    private var videoList: CopyOnWriteArrayList<ChapterInfoVo> = CopyOnWriteArrayList()

    //视频列表只包含章节
    var mVideoListOnly: CopyOnWriteArrayList<ChapterInfoVo> = CopyOnWriteArrayList()

    /**
     * 本地缓存的章节信息
     * 注意：虽然名字包含download，但实际可能没下载完成。
     */
    private var downLoadVideoMap: HashMap<String, ChapterInfoVo> = hashMapOf()

    /**
     * 已经下载完成的剧集
     */
    private var alreadyDownloadChapters = mutableMapOf<String, ChapterInfoVo>()

    //剧集详情
    var mVideoInfo: VideoDetailBean? = null

    //是否在最后一集
    var isInLastPage: Boolean = false

    //是否在第一集
    var isInFirstPage: Boolean = false

    //剧末推荐样式
    var videoEndStyleDetail: VideoEndStyle? = null

    var hasShowToastInStart: Boolean = false
    val videoInfoLiveData = CommLiveData<VideoDetailBean>()

    private val _videoInfoLiveData = CommLiveData<VideoDetailBean>()
    val videoInfoLiveData2: LiveData<VideoDetailBean> = _videoInfoLiveData

    //订阅状态
    val subscribeStatus = CommLiveData<Boolean>()

    /**
     * 二级播放器配置信息
     */
    val playerConfig = CommLiveData<PlayerConfig>()

    //微信分享配置信息
    var mWxShareConfigVo: WxShareConfigVo? = null

    // 当前播放章节信息 可能是广告，也可能是正常章节
    var mChapterInfoVo: ChapterInfoVo? = null
        set(value) {
            field = value
            currentChapterIndex = value?.chapterIndex
        }

    /**
     * 对应项的本地数据
     */
    val commentLocalItemData = CommLiveData<MutableList<CommentNumCheckDatabaseBean>>()

    /**
     * 对应项的本地数据第一条预加载，因为会出现更新太快丢数据
     */
    val commentLocalItemDataFirst = CommLiveData<MutableList<CommentNumCheckDatabaseBean>>()

    /**
     * 下一部未解锁的剧
     */
    private var nexUnlockChapter: ChapterInfoVo? = null

    //最后可播放的章节信息
    var lastPlayChapterInfoVo: ChapterInfoVo? = null

    var mPosition: Int = 0

    private var hasDrawAd = false // 是否有沉浸式广告返回

    /**
     * 解锁章节广告的ecpm
     */
    var unlockAdEcpm: Double? = null

    //后端是否开启预加载
    var mPreloadNum: Int = 0

    //返回上一层是否回到推荐页面；默认为false
    var mBackToRecommend: Boolean? = false

    var showActor: Boolean = true

    /**
     * 底部区域展示的内容类型
     */
    var bottomStyle: BottomStyle? = null

    /**
     * 二级页赠送解锁vip的配置
     */
    var giveVipConf: GiftVipConf? = null

    /**
     * 播放器预加载配置信息
     */
    private var preLoadInfo: PreLoadFunSwitchVo? = null

    /**
     * 触发剧集解锁的开关
     */
    private val unlockChapter = EventLiveData<UnlockBean>()

    /**
     * 当要解锁剧集时，从哪一集开始解锁？
     * 在观看激励视频解锁部分剧集时使用
     */
    private var unlockChaptersStartId = ""

    //视频解锁配置
    private val mAdUnlockConfigVo by lazy { getAdConfig() }

    /**
     * 各种错误码
     * 10-获取剧信息失败
     * 20-解锁失败
     */
    val errorCode = CommLiveData<Int>()

    /**
     * statusPoster状态码：0:取消loading; 1:显示loading; 2:显示无网络提示（1139触发）
     */
    val statusPosterLiveData = CommLiveData<StatusPosterBean>()

    /**
     * 发生错误时的错误消息
     */
    var errorMsg: String? = null

    /**
     * 进入二级页后，广告解锁次数
     */
    var adUnlockedTimes: Int = 0

    val videoTrackInfo = TrackChapterInfo().apply {
        positionName = "二级播放页"
        pageTitle = "二级播放器"
        routeIntent?.let { intent ->
            firstTierPlaySource = intent.firstTierPlaySource
            secondTierPlaySource = intent.secondTierPlaySource
            thirdTierPlaySource = intent.thirdTierPlaySource
            contentSource = intent.fromType
        }
    }

    /**
     * 当前剧集的index
     * 从1开始
     */
    var currentChapterIndex: Int?
        get() {
            return stateHandle[SAVE_STATE_KEY_CUR_POSITION]
        }
        set(value) {
            stateHandle[SAVE_STATE_KEY_CUR_POSITION] = value
        }

    /**
     * 播放模式
     * 私有，禁止外部赋值
     */
    private val _playMode = CommLiveData<PlayMode>()

    /**
     * 上一个播放模式
     */
    var previousPlayMode: PlayMode? = null

    /**
     * 播放模式
     * 对外暴露，用于观察变化
     */
    val playMode: LiveData<PlayMode> = _playMode

    /**
     *  unlock图片
     */
    val unlockPic: String? = null

    /**
     * 当前播放模式：正常
     */
    var nowPlayMode = PlayMode.NORMAL

    /**
     * 屏幕方向
     */
    val orientation = CommLiveData<Orientation>()

    /**
     * 明日解锁文案
     */
    var tomorrowUnlockTipDetail: String? = null
    /**
     * 本地沉浸式开关
     */
    var immersiveLocalEnable: Boolean = true

    /**
     * 播放速度 默认为1倍
     */
    var playSpeed = BBaseKV.multipleSpeed

    val closeEvent = EventLiveData<Boolean>()

    var isSubscribe: Boolean = true

    var mIsShowToast: Boolean = false

    var bottomAdFreeBtnIndex = 0                    // draw底部看激励视频按钮的文案索引
    var multiBtnConf: List<MultiBtnConf>? = null    // draw底部看激励视频按钮的文案列表
    private var waitingRemoveAdsAndPlayVideo: Boolean = false // 是否去广告并播放视频的标记位

    /**
     * 是否支持分辨率切换
     */
    var resolutionEnable = false

    /**
     * 当前使用的清晰度/分辨率
     */
    var currentResolution: String? = null

    /**
     * 切换中的清晰度
     */
    var switchingResolution: ResolutionRateVo? = null

    /**
     * 页面初始化后首次播放
     */
    var isFirstPlay: Boolean = true

    var firstPlayChapterId: String? = null

    /**
     * 清晰度切换状态
     * -1: 清晰度功能不可用
     * 0: 未手动设置清晰度，采用自动选择的清晰度
     * 1：切换中
     * 2：切换完，未播放
     * 3: 用户手动切换了清晰度，并且已经播放
     */
    var resolutionState = DetailMC.RESOLUTION_STATE_AUTO

    //上报实时埋点中添加之前是否播放过这部剧的高光片段，若之前看过这部剧的高光，上报isFocusVideoPlay=1，若未看过这部剧的高光，isFocusVideoPlay=0
    var isFocusVideoPlay: String? = null

    var payListResp: PayListResp? = null // 当前接付费解锁的信息

    /**
     * 当退出到剧场时，剧场弹窗的图片
     */
    var jumpTheaterImg: String? = null

    /**
     * 是否已经自动弹出付费弹窗
     */
    var hasAutoShowPayDialog: Boolean = false

    /**
     * 是否已经弹出过剧末推荐toast，在剧集开始播放时，只显示一次，剧末可以重复展示
     */
    var hasShowRecommendInVideoStart: Boolean = false

    /**
     * 退出小窗后，剧集滑动状态
     * 0:未进入小窗，1:退出小窗后未滑动，2:退出小窗后发生了滑动
     */
    var slideStateAfterPipExit = 0

    /**
     * 播放器挂件的配置信息
     */
    var playerPendantConfig: WelfarePendantConfigVo? = null


    init {
        setPlayMode(PlayMode.NORMAL)
    }

    //draw广告播放完毕后自动跳过相关
    private var autoJumpDrawAdCount = 0

    var keepImmersive = false
        get() {
            return _playMode.value == PlayMode.IMMERSIVE && mVideoInfo?.immersiveSwitchVo?.changeQuit == 1
        }
        set(value) {
            field = value && mVideoInfo?.immersiveSwitchVo?.changeQuit == 1
        }

    fun getAutoJumpDrawAdCount():Int{
        LogUtil.d(DetailMC.AUTO_JUMP_AD_TAG, "已自动滑集次数=${autoJumpDrawAdCount}")
        return autoJumpDrawAdCount
    }

    fun autoJumpDrawAdCountAdd1() {
        autoJumpDrawAdCount += 1
        LogUtil.d(DetailMC.AUTO_JUMP_AD_TAG, "已自动滑集次数加1= ${autoJumpDrawAdCount}")
    }

    fun resetAutoJumpDrawAdCount() {
        LogUtil.d(DetailMC.AUTO_JUMP_AD_TAG, "自动滑集次数 重置 0")
        autoJumpDrawAdCount = 0
    }

    fun canAutoJumpDrawAd(): Boolean {
        if (mChapterInfoVo?.isVideo() == true) {
            LogUtil.d(DetailMC.AUTO_JUMP_AD_TAG, "沉浸式广告播放结束，当前是视频,不能自动跳过")
            return false
        }
        if (mVideoInfo?.getDrawAdConfig()?.getDetailAutoJumpAdNum() == null) {
            LogUtil.d(DetailMC.AUTO_JUMP_AD_TAG, "沉浸式广告播放结束，detailAutoJumpAdNum 配置是空,不能自动跳过")
            return false
        }
        if (CoinsDropIntervalUtil.needInsertCoinsRain()) {
            LogUtil.d(DetailMC.AUTO_JUMP_AD_TAG, "沉浸式广告播放结束，金币掉落广告不自动跳过 coins_drop")
            return false
        }
        LogUtil.d(DetailMC.AUTO_JUMP_AD_TAG, "canAutoJumpDrawAd ${mVideoInfo?.getDrawAdConfig()?.getDetailAutoJumpAdNum()} contPlayNum=${autoJumpDrawAdCount}")
        return (mVideoInfo?.getDrawAdConfig()?.getDetailAutoJumpAdNum() ?: 0) > autoJumpDrawAdCount
    }

    // 播放过的每集播放进度
    val playedVideoPlayTime: MutableMap<String, Long> = mutableMapOf()

    override fun loadData() {
        super.loadData()
        kotlin.runCatching {
            viewModelScope.launch(Dispatchers.IO) {
                val historyEntity = DBHelper.queryBook(routeIntent?.bookId)
                if ((historyEntity?.current_time ?: 0) > 0) {
                    historyEntity?.current_time?.let { time ->
                        withContext(Dispatchers.Main) {
                            playedVideoPlayTime.put(historyEntity.cur_cid ?: "", time)
                        }
                    }
                }
            }
        }.onFailure {
            it.printStackTrace()
        }
    }

    var saveRvAllCells: MutableList<ChapterInfoVo>? = null
    var savePosition: Int = 0
    var saveDuration: Long = 0L
    var saveChapterIndex: Int? = 0
    var saveChapterId: String? = null
    fun restoreSaveCells() {
        videoListLiveData.value = saveRvAllCells
        savePosition = 0
        saveChapterIndex = 0
        saveDuration = 0L
        saveChapterId = null
    }

    var isLandscapeVideo: Boolean = false
    fun updateViewHistory() {
        TaskManager.ioTask {
            mVideoInfo?.videoInfo?.let { info ->
                var firstPlaySource = mOmap?.scene
                info.bookId?.let {
                    val bookEntity = DzDataRepository.bookDao().queryByBid(it)
                    if (!bookEntity?.first_play_source.isNullOrEmpty()) {
                        firstPlaySource = bookEntity?.first_play_source
                    }
                }
                //最近观看剧卡片 发生有效观看 状态标示
                BBaseKV.watchedDrama = true
                DBHelper.insertOrUpdateHistory(
                    info.bookId,
                    info.bookName,
                    info.coverWap,
                    info.author,
                    info.introduction,
                    mChapterInfoVo?.chapterId,
                    mChapterInfoVo?.chapterIndex ?: 1,
                    info.inBookShelf,
                    info.finishStatus,
                    firstPlaySource,
                    routeIntent?.originName,
                    routeIntent?.channelName,
                    GsonUtils.toJson(mVideoInfo?.videoInfo?.omap ?: routeIntent?.cOmap),
                    info.updateNum,
                    info.finishStatusCn,
                    alias = routeIntent?.alias,
                )
                PersonalME.get().historyUpdate().post(null)
            }
        }
    }

    //更新观看进度
    fun updateViewHistoryProgress(
        currentDuration: Long,
        all: Long,
        info: VideoInfoVo?,
        chapter: ChapterInfoVo,
    ) {
        var time = currentDuration
        if ((all - currentDuration) > 0 && (all - currentDuration) <= 3000) {
            time = 0
        } else if ((all - currentDuration) < 0) {
            time = 0
        }
        viewModelScope.launch(Dispatchers.IO) {
            info?.let { item ->
                LogUtil.d(
                    "打印",
                    "onPause    Time=${currentDuration} all=${all} time=${time}  info.bookId=${info.bookId} info.chapterIndex=${info.chapterIndex}"
                )
                DBHelper.insertOrUpdateHistory(item.bookId, item.chapterIndex, currentTime = time)
            }
        }
        playedVideoPlayTime[chapter.chapterId ?: ""] = time
        LogUtil.d(
            "打印",
            "updateViewHistoryProgress==${playedVideoPlayTime[chapter.chapterId ?: ""]} time=${time} name=${chapter.chapterName}  id=${chapter.chapterId}"
        )
    }

    fun deleteViewHistory() {
        TaskManager.ioTask {
            mVideoInfo?.videoInfo?.let { info ->
                info.bookId?.let {
                    DBHelper.deleteBookById(it)
                    PersonalME.get().historyUpdate().post(null)
                }
            }
        }
    }


    /**
     * 页面是否有数据
     */
    fun getHasDataFlag(): Boolean {
        return !videoListLiveData.value.isNullOrEmpty()
    }


    //倒计时task
    private var timeOutTask: Task? = null
    fun kocAscribeRequest(
        channelCode: String, playletId: String,
        searchKocWord: String,
    ) {
        LogUtil.d("KOC", "发起KOC归因")
        timeOutTask = intervalTask(3, 0, 1000) { time ->
            LogUtil.d("KOC", "KOC归因倒计时  time==$time")
            if (time == 2) {//倒计时结束
                cancelTask()
                SearchMS.get()?.cancelKocAscribeRequest()
                initVideoDetailInfo()
            }
        }
        SearchMS.get()
            ?.kocAscribeRequest(channelCode, playletId, searchKocWord, object : KocAscribeCallback {
                override fun onStart() {
                    eventCallback?.onRequestStart(true)
                }

                override fun onSuccess(data: AscribeBean?) {
                    LogUtil.d("KOC", "KOC归因返回")
                    cancelTask()
                    initVideoDetailInfo()
                }

                override fun onFail(e: RequestException) {
                    LogUtil.d("KOC", "KOC归因失败")
                    cancelTask()
                    initVideoDetailInfo()
                }
            })
    }

    private fun cancelTask() {
        timeOutTask?.cancel()
        timeOutTask = null
    }

    private var init = false
    var initBookInfo = false//是否初始化1131成功

    /**
     * 获取剧集详情的一些必须信息
     */
    fun initVideoDetailInfo() {
        if (!init) {
            LogUtil.d("KOC", "开启请求1131")
            TimeMonitorManager.getMonitor(MonitorMC.SCENE_DETAIL)
                .recordTime(MonitorMC.STAGE_NET_START)
            getVideoDetailInfo()
            init = true
        }
    }

    private suspend fun reqBookInfo(bookId: String?) {
        mVideoListOnly.clear()
        kotlin.runCatching {
            bookId?.let {
                DownloadMS.get()?.getCacheChapters(it)?.let { chapters ->
                    if (chapters.isNotEmpty()) {//获取到章节列表
                        var showLoading = false
                        downLoadVideoMap.clear()
                        keepPlayStatus = false
                        videoList = CopyOnWriteArrayList(chapters)
                        chapters.forEachIndexed { index, item ->
                            item.chapterId?.let { chapterId ->
                                val chapter = ChapterInfoVo().apply {
                                    downLoadUrl = item.downLoadUrl
                                    downloadState = item.downloadState
                                    chapterStatus = item.chapterStatus
                                    videoSize = item.videoSize
                                    localFileAvailability = item.localFileAvailability
                                }
                                downLoadVideoMap[chapterId] = chapter
                                if (item.downloadState == BBaseMC.DOWNLOAD_STATE_DONE) {
                                    alreadyDownloadChapters[chapterId] = chapter
                                }
                            }
                            // 查找从哪一集开始播放，记录position
                            if ((currentChapterIndex != null && item.chapterIndex == currentChapterIndex) || (currentChapterIndex == null && item.chapterId == routeIntent?.chapterId)) {
                                mPosition = index
                                showLoading = !downLoadUrlCanPlay(item)
                                LogUtil.d(
                                    DetailMC.PLAYER_DOWNLOAD_TAG,
                                    "数据库读取成功，加载是否需要显示loading  ==$showLoading"
                                )
                            }
                        }
                        DownloadMS.get()?.getBookInfo(it)?.let { info ->
                            viewModelScope.launch(Dispatchers.Main) {
                                mVideoInfo = VideoDetailBean(videoInfo = info)
                                isLandscapeVideo = info.isLandscapeVideo()
                            }
                        }
                        if (!showLoading) {
                            viewModelScope.launch(Dispatchers.Main) {
                                videoListLiveData.value = getDataList()
                            }
                        }
                        reqDetailInfo(showLoading)
                    } else {//未获取到章节列表
                        reqDetailInfo()
                    }
                } ?: run {//数据库获取不到章节信息
                    reqDetailInfo()
                }
            } ?: run {//intent携带的bookid为空，请求接口会报对应的异常
                reqDetailInfo()
            }
        }.onFailure {//代码数据库操作异常或数据回调异常，也需要直接走服务端请求
            reqDetailInfo()
        }
    }

    /**
     * 获取剧集详情
     * 触发1131接口请求
     */
    fun getVideoDetailInfo() {
        videoTrackInfo.apply {
            pageTitle = "二级播放器"
            routeIntent?.let {
                origin = it.originName
                columnName = it.columnName
            }
        }
        // 对于只传入bookId，未传入chapterId的情况，尝试使用本地观看记录的chapterId
        if (!routeIntent?.bookId.isNullOrEmpty() && routeIntent?.chapterId.isNullOrEmpty()) {
            viewModelScope.launch(Dispatchers.IO) {
                DBHelper.queryBook(routeIntent?.bookId!!)?.apply {
                    routeIntent?.chapterId = cur_cid
                }
//                reqDetailInfo()
                reqBookInfo(routeIntent?.bookId)
            }
        } else {
//            reqDetailInfo()
            viewModelScope.launch(Dispatchers.IO) {
                reqBookInfo(routeIntent?.bookId)
            }
        }
    }

    private var is1131Loading = false

    /**
     * 1131接口获取剧集详情信息
     */
    fun reqDetailInfo(showLoading: Boolean = true) {
        if (is1131Loading || initBookInfo) {
            return
        }
        LogUtil.d(DetailMC.PLAYER_DOWNLOAD_TAG, "请求1131")
        is1131Loading = true
        routeIntent?.bookId?.let { bookId ->
            DetailNetWork.get()
                .getVideoDetailInfo()
                .setParams(
                    bookId,
                    routeIntent?.chapterId,
                    routeIntent?.type,
                    routeIntent?.alias,
                    routeIntent?.bookAlias,
                    routeIntent?.firstTierPlaySource,
                    routeIntent?.secondTierPlaySource,
                    routeIntent?.thirdTierPlaySource
                )
                .onStart {
                    if (showLoading) {
                        eventCallback?.onRequestStart(true)
                    }
                }.onResponse { response ->
                    loadCommLiveData.value = LoadResult(LoadResult.CODE_SUCCESS)
                    if (TextUtils.equals(routeIntent?.origin, BBaseMC.origin_tfsj)) {
                        OCPCManager.setPlotOcpcBookId(routeIntent?.bookId)
                    }
                    if (showLoading) {
                        eventCallback?.onResponse()
                    }
                    response.data?.apply {
                        if (status != 1) {  // 请求失败
                            errorMsg = msg ?: "获取视频信息失败"
                            errorCode.value =
                                if (status == 0) ERROR_VIDEO_OFF_SHELF else ERROR_VIDEO_LOAD_FAILED
                            return@onResponse
                        }
                        ServerAdDataDispatcher.onBannerAdFetched(bottomAdVo, onHeightChanged = {
                            bannerHeightLiveData.value = it
                        })
                        giftVipConf?.let { item ->
                            LogUtil.d(TAG_PLAYER_CONFIG, "获取播放器配置giveVipConf = ${item}")
                            giveVipConf = item
                        }
                        tomorrowUnlockTipDetail = this.tomorrowUnlockTip
                        LogUtil.d("interval_chapter_detail", "tomorrowUnlockTipDetail = ${tomorrowUnlockTipDetail } this.tomorrowUnlockTip = ${this.tomorrowUnlockTip}")
                        mVideoInfo = this
                        isGuideStatus = this.immersiveSwitchVo?.secondaryPlayPageImmersiveShowGuide
                        guideTime = this.immersiveSwitchVo?.secondaryPlayPageImmersiveShowGuideTime
                        val isNotifyEnabled = NotificationUtil.isNotifyEnabled(AppModule.getApplication())
                        isSubscribe = isNotifyEnabled && mVideoInfo?.freeNotificationSwitch == 1
                        FollowTipManager.initConfig(mVideoInfo)
                        mVideoInfo?.videoInfo?.isAlias = routeIntent?.alias
                        isLandscapeVideo = videoInfo.isLandscapeVideo() == true
                        videoInfoLiveData.value = this
                        VideoTrackUtils.mVideoInfo = mVideoInfo?.videoInfo
                        if (!getPauseAdConfig()?.adId.isNullOrEmpty()) {
                            pauseAdCommLiveData.value = getPauseAdConfig()?.adId
                        }

                        resolutionEnable = BBaseKV.resolutionSwitch
                        autoSelectResolution()
//                        currentResolution =
//                            this.videoInfo.content?.resolutionRates?.find { it.rate == BBaseKV.selectedResolution }?.rate
//                                ?: this.videoInfo.content?.mp4UrlRate
//                        LogUtil.d(
//                            TAG_RESOLUTION, "清晰度切换Enable:$resolutionEnable, " +
//                                    "开关:${BBaseKV.resolutionSwitch}, " +
//                                    "用户已选:${BBaseKV.selectedResolution}, " +
//                                    "当前使用:$currentResolution, " +
//                                    "下发清晰度:${this.videoInfo.content?.resolutionRates}"
//                        )

                        getPauseAdConfig()?.let { pauseAdConfig ->
                            setPauseAdConfig(pauseAdConfig)
                        }
                        getDrawAdConfig()?.let {
                            ServerAdDataDispatcher.onDetailDrawAdConfigFetched(it)
                        }
                        getWelfareAnchorAdConfig()?.let {
                            ServerAdDataDispatcher.onDetailWelfareAnchorAdConfigFetched(it)
                        }

                        //保存是否评分标记
                        VideoPlayTimeManager.saveVideoRatingFlag(
                            bookId,
                            userVideoVo?.scoreFlag == true
                        )
                        //预加载配置信息同步
                        preLoadInfo = preLoadFunSwitchVo
                        preLoadLiveData.value = preLoadFunSwitchVo
                        //创建列表并开始播放
                        val chapterPos = getCurrChapterPos()
                        LogUtil.d("interval_chapter_detail", "reqDetailInfo() chapterPos:$chapterPos mPosition:$mPosition currentChapterIndex:$currentChapterIndex getUpNoAdsCnt:${chapterPos + 1}")
                        val adInsertIndexSet = ChapterIntervalUtil.getDetailInsertAdsIndexSet(
                            firstIn = true,
                            totalChapter = chapterList?.size ?: 0,
                            upNoAdsCnt = chapterPos + 1,
                            currChapterIndex = chapterPos
                        )
                        mVideoListOnly.clear()
                        mVideoAndAdList.clear()
                        chapterList?.forEachIndexed { index, dramaVo ->
                            if (videoInfo.chapterId == dramaVo.chapterId) {
                                dramaVo.apply {
                                    content = videoInfo.content
                                    m3u8720pUrl = videoInfo.content?.getUrl()
                                    mp4720pSwitchUrl = videoInfo.content?.getUrlList()
                                    bookName = videoInfo.bookName
                                    chapterName = videoInfo.chapterName
                                    chapterIndex = videoInfo.chapterIndex
                                    likesNum = videoInfo.likesNum
                                    likesNumActual = videoInfo.likesNumActual
                                    isLiked = videoInfo.likesChecked
                                    chapterImg = videoInfo.chapterImg
                                }
                            }
                            if (downLoadVideoMap.size > 0 && downLoadVideoMap[dramaVo.chapterId] != null) {
                                dramaVo.apply {
                                    downLoadUrl = downLoadVideoMap[dramaVo.chapterId]?.downLoadUrl
                                    downloadState =
                                        downLoadVideoMap[dramaVo.chapterId]?.downloadState
                                    videoSize = downLoadVideoMap[dramaVo.chapterId]?.videoSize ?: 0
                                    chapterStatus =
                                        downLoadVideoMap[dramaVo.chapterId]?.chapterStatus ?: 2
                                    localFileAvailability =
                                        downLoadVideoMap[dramaVo.chapterId]?.localFileAvailability
                                            ?: false
                                }
                            }
                            dramaVo.bookId = bookId
                            mVideoListOnly.add(dramaVo)
                            initVideoAndAdList(index, dramaVo, adInsertIndexSet)
                            // 查找从哪一集开始播放，记录position
                            if ((currentChapterIndex != null && dramaVo.chapterIndex == currentChapterIndex) || (currentChapterIndex == null && dramaVo.chapterId == videoInfo.chapterId)) {
                                mPosition =
                                    if (noAds()) {
                                        mVideoListOnly.size - 1
                                    } else {
                                        mVideoAndAdList.size - 1
                                    }
                            }
                            // 寻找未解锁的剧集
                            if (nexUnlockChapter == null && (dramaVo.isCharge == DetailMC.CHAPTER_STATUS_PAY || dramaVo.isCharge == DetailMC.CHAPTER_STATUS_TOMORROW )) {
                                nexUnlockChapter = dramaVo
                            }
                        }
                        // 1131回来
                        // 通过这里赋值，来触发后面的播放
                        videoList =
                            if (noAds()) {
                                mVideoListOnly
                            } else {
                                mVideoAndAdList
                            }
                        LogUtil.d(
                            DetailMC.START_PLAY_TAG,
                            "首次创建列表信息 videoList==${videoList.size}"
                        )
                        keepPlayStatus = if (showLoading) {
                            false
                        } else {
                            if (downLoadVideoMap.size > 0 && !downLoadUrlCanPlay(videoList[mPosition])) {
                                false
                            } else {
                                downLoadVideoMap.size > 0
                            }
                        }
                        LogUtil.d(
                            DetailMC.PLAYER_DOWNLOAD_TAG,
                            "1131接口返回，开始创建列表  keepPlayStatus==$keepPlayStatus"
                        )
                        // 判断底部区域显示内容
                        bottomStyle =
                            if (!BBaseKV.playerHasBottomAd || CommInfoUtil.isVip()) {  // 没有底通广告配置
                            if (BBaseKV.playerBottomStyle == 1) {
                                BottomStyle.DRAMA  // 显示选集列表
                            } else {
                                BottomStyle.EMPTY  // 空白，显示河马剧场
                            }
                        } else {
                            BottomStyle.AD  // 显示底通广告
                        }
                        LogUtil.d(
                            "player_bottom",
                            "底通样式:$bottomStyle hasBottomAd:${BBaseKV.playerHasBottomAd} " +
                                    "isVip:${CommInfoUtil.isVip()} " +
                                    "bottomStyle:${BBaseKV.playerBottomStyle}"
                        )
//                        bottomStyle = BottomStyle.EMPTY
                        videoListLiveData.value = getDataList()
                        updateDownLoadChapterStatus()
                        getOperationConfig(bookId, routeIntent?.firstTierPlaySource)
                        initBookInfo = true
                    } ?: let {
                        errorMsg = "获取视频信息失败"
                        errorCode.value = ERROR_VIDEO_LOAD_FAILED
                    }
                }.onError {
                    is1131Loading = false
                    loadCommLiveData.value = LoadResult(LoadResult.CODE_EXCEPTION).apply {
                        setHttpRequestException(it)
                    }
                    if (showLoading) {
                        eventCallback?.onRequestError(it, true)
                    }
                }.doRequest()
        }
    }

    private fun updateDownLoadChapterStatus() {
        viewModelScope.launch(Dispatchers.IO) {
            getDataList().forEach {
                if (!mVideoInfo?.videoInfo?.bookId.isNullOrEmpty() && it.isAd == 0 && !it.chapterId.isNullOrEmpty()) {
                    DownloadMS.get()?.updateChapterChargeStatus(
                        mVideoInfo?.videoInfo?.bookId!!, it.chapterId!!, it.isCharge!!
                    )
                }
            }
        }
    }

    /**
     * 设置暂停广告配置
     */
    private fun setPauseAdConfig(pauseAdConfig: OperationVo) {
        PauseAdManager.setAdConfig(pauseAdConfig)

    }

    /**
     * 刷新章节列表
     */
    fun updateChapterList() {
        //保存当前集
        routeIntent?.chapterId = mChapterInfoVo?.chapterId
        getChapterList()
    }

    /**
     * 1132接口获取剧集目录
     */
    private fun getChapterList() {
        LogUtil.d("VideoListVM", "获取全部剧集")
        mVideoInfo?.videoInfo?.run {
            DetailNetWork.get().getChapterList().setParams(
                bookId = bookId ?: "", if (getPreviewConfig() != null) 1 else 0
            ).onStart {
                eventCallback?.onRequestStart(true)
            }.onResponse {
                it.data?.run {
                    LogUtil.d(UNLOCK, "更新剧集列表")
                    val chapterPos = getCurrChapterPos()
                    LogUtil.d("interval_chapter_detail", "getChapterList() chapterPos:$chapterPos mPosition:$mPosition currentChapterIndex:$currentChapterIndex getUpNoAdsCnt:${getUpNoAdsCnt(mPosition)}")
                    val adInsertIndexSet = ChapterIntervalUtil.getDetailInsertAdsIndexSet(
                        totalChapter = chapterList?.size ?: 0,
                        upNoAdsCnt = (mPosition),
                        currChapterIndex = chapterPos
                    )
                    mVideoAndAdList.clear()
                    mVideoListOnly.clear()

                    chapterList?.forEachIndexed { index, dramaVo ->
                        dramaVo.bookId = bookId
                        mVideoListOnly.add(dramaVo)
                        initVideoAndAdList(index, dramaVo, adInsertIndexSet)
                        // 查找从哪一集开始播放，记录position
                        routeIntent?.let { intent ->
                            //定位播放第几集，点下一集进来的根据chapterIndex定位，否则直接使用intent当前集
//                                if ((intent.type == 1 && dramaVo.chapterIndex == intent.chapterIndex) || dramaVo.chapterId == intent.chapterId) {
                            if ((currentChapterIndex != null && dramaVo.chapterIndex == currentChapterIndex) || (currentChapterIndex == null && dramaVo.chapterId == intent.chapterId)) {
                                mPosition =
                                    if (noAds()) {
                                        mVideoListOnly.size - 1
                                    } else {
                                        mVideoAndAdList.size - 1
                                    }
                            }
                        }

                        // 寻找未解锁的剧集
                        if (nexUnlockChapter == null && (dramaVo.isCharge == DetailMC.CHAPTER_STATUS_PAY || dramaVo.isCharge == DetailMC.CHAPTER_STATUS_TOMORROW)) {
                            LogUtil.d(
                                UNLOCK,
                                "发现未解锁的剧集：${dramaVo.chapterName}, 价格：${dramaVo.price}"
                            )
                            nexUnlockChapter = dramaVo
                        }
                    }
                    // 1132回来
                    // 通过这里赋值，来触发后面的播放
                    videoList =
                        if (noAds()) {
                            mVideoListOnly
                        } else {
                            mVideoAndAdList
                        }
                    keepPlayStatus = true

                    // 判断试看配置是否发生变更
                    if (continuousAd != 1 && mAdUnlockConfigVo?.continuousConfig != null) {
                        mAdUnlockConfigVo?.continuousConfig = null
                        previewConfigChanged = true
                    }

                    videoListLiveData.value = getDataList()
                }

//                    preloadVideoAd()
                eventCallback?.onResponse()
            }.onError {
                eventCallback?.onRequestError(it, true)
            }.doRequest()
        }
    }

    private fun noAds() = !hasDrawAd || isDrawAdFree()

    // 沉浸式广告免广状态
    private fun isDrawAdFree(): Boolean {
        val drawAdConf = mVideoInfo?.getDrawAdConfig()
        LogUtil.i(DetailMC.AD_TAG,"今日观看时长 ${AdKV.todayWatchedDurationSec2} 今日最小观看时长${drawAdConf?.getDetailDrawDailyProtectionTime()} ")
        return (BBaseKV.vipStatus == 1 || BBaseKV.freeDrawAd == 1
                || drawAdConf == null
                || DrawAdKV.detailDrawAdNum >= drawAdConf.getDayMaxShowNum()
                || DrawDataUtil.minWatchTimeNotMetForDetailAd()
                || AdKV.todayWatchedDurationSec2 < drawAdConf.getDetailDrawDailyProtectionTime() //小于当日最短观看时长
                )
            .also { isFree ->
                if (isFree && LogUtil.isDebugMode()) {
                    LogUtil.i(
                        DetailMC.AD_TAG, "二级页沉浸式广告处于免广状态," +
                                " ${if (drawAdConf == null) "drawAdConf=null " else ""}freeDrawAd=${BBaseKV.freeDrawAd} " +
                                "vipStatus=${BBaseKV.vipStatus}" +
                                " maxShowNum=${drawAdConf?.getDayMaxShowNum()} " +
                                "minWatchTime=${DrawAdKV.detailDrawAdMinWatchTimeSec}" +
                                "今日观看时长 ${AdKV.todayWatchedDurationSec2} 今日最小观看时长${drawAdConf?.getDetailDrawDailyProtectionTime()} "
                    )
                }
            }
    }

    fun initUnlockChapter(currentPosition: Int) {
        if (currentPosition < 0 || currentPosition >= getDataList().size) {
            return
        }
        getDataList()[currentPosition].run {
            if (BBaseKV.vipStatus == 1 || (isCharge != DetailMC.CHAPTER_STATUS_PAY && isCharge != DetailMC.CHAPTER_STATUS_TOMORROW)) {
                if (m3u8720pUrl.isNullOrBlank()) {
                    chapterId?.let { unlockChapter(it, false) }
                } else {
                    DetailME.get().playCurrentChapter().post(null)
                }
            }
        }
    }

    private var isRequest1139: Boolean = false
    var hasReport: Boolean = false //阅读上报避免重复上报，或者不报

    /**
     * 获取广告解锁的剧集数量
     */
    fun getAdUnlockChapterSize(startChapterId: String?): Int {
        val startChapterIndex = startChapterId?.takeIf { it.isNotEmpty() }
            ?.let { chapterId -> mVideoListOnly.firstOrNull { it.chapterId == chapterId }?.chapterIndex }
            ?: mChapterInfoVo?.chapterIndex

        return mAdUnlockConfigVo?.continuousConfig?.intervalUnlockVo
            ?.find { startChapterIndex != null && it.chapterBegin <= startChapterIndex && it.chapterEnd >= startChapterIndex }
            ?.unlockVideoNum
            ?: mAdUnlockConfigVo?.continuousConfig?.defaultUnlockVideoNum
            ?: mAdUnlockConfigVo?.videoAdConfigVo?.unlockVideoNum
            ?: 1
    }

    /**
     * 1139章节解锁  处理预加载 解锁 阅读上报 在追更新 有可能会多次调用
     *
     * @param startChapterId 从哪一集开始解锁
     * @param confirmPay
     * @param unlockType 解锁类型
     * @param allowDuplicateRequest 是否允许多个1139同时请求
     */
    fun unlockChapter(
        startChapterId: String?,
        confirmPay: Boolean,
        isPreload: Boolean = false,
        unlockType: String = DetailMC.UNLOCK_LOAD_TYPE,
        ecpm: Double? = null,
        currentChapterId: String? = null,
        mergeData: Boolean? = false,
        isReport: Boolean? = false,
        allowDuplicateRequest: Boolean = false,
    ) {
        if (startChapterId.isNullOrEmpty()) {
            return
        }
        //过滤广告id
        if (AdvertisementUtil.isAdvertisementId(startChapterId)) {
            return
        }
        LogUtil.d(UNLOCK, "$startChapterId 章节解锁,解锁类型：$unlockType ")
        // 预加载章节获取ids
        val chapterIds = getChapterFromPosition(
            isPreload,
            startChapterId,
            when (unlockType) {
                DetailMC.UNLOCK_AD_TYPE -> {
                    if (getPreviewConfig() != null) {
                        getAdUnlockChapterSize(startChapterId)
                    } else {
                        mAdUnlockConfigVo?.videoAdConfigVo?.unlockVideoNum ?: 1
                    }
                }

                else -> 4
            }
        )
        // 阅读上报重复上报 过滤重复请求
        if (isReport == true && hasReport) {
            return
        } else {
            hasReport = true
        }
        LogUtil.d(
            UNLOCK,
            "$startChapterId 章节解锁,解锁类型：$unlockType  chapterIds.size=${chapterIds.size}"
        )
        // 合并数据后重复请求 过滤重复请求
        if (chapterIds.size == 0 && mergeData == true) {
            return
        }
        unlockAdEcpm = ecpm
        mVideoInfo?.run {
            if (videoInfo.bookId.isNullOrEmpty() || startChapterId.isEmpty()) {
                LogUtil.d(
                    UNLOCK, "解锁参数异常bookId/chapterId:${videoInfo.bookId} $startChapterId"
                )
                return
            }
            if (!allowDuplicateRequest && isRequest1139 && unlockType != DetailMC.UNLOCK_AD_TYPE) {
                return
            }
            if (isRequest1139) {
                LogUtil.d(DetailMC.PLAYER_DOWNLOAD_TAG, "1139正在请求，这时候广告解锁触发了")
            }
            isRequest1139 = true
            val mChapterId = if (isPreload) {
                currentChapterId
            } else {
                startChapterId
            } ?: startChapterId
            LogUtil.d(DetailMC.PLAYER_DOWNLOAD_TAG, "请求1139  isPreload===$isPreload")
            DetailNetWork.get().preLoadChapterInfo()
                .setParams(
                    bookId = videoInfo.bookId ?: "",
                    chapterId = mChapterId,
                    chapterIds = chapterIds,
                    unClockType = unlockType,
                    omap = mOmap,
                    ecpm = if (unlockType == DetailMC.UNLOCK_AD_TYPE) ecpm?.toString() else null,
                    tierPlaySource = TierPlaySourceVo(
                        firstTierPlaySource = videoTrackInfo.firstTierPlaySource,
                        secondTierPlaySource = videoTrackInfo.secondTierPlaySource,
                        thirdTierPlaySource = videoTrackInfo.thirdTierPlaySource
                    ),
                    playPercentage = progressUp,
                    continuousPointId = getPreviewConfig()?.continuousPointId
                )
                .onStart {
                    if (!isPreload) {
                        statusPosterLiveData.value =
                            StatusPosterBean(DetailMC.STATUS_NEW_POSTER_SHOW_LOADING)
//                        eventCallback?.onRequestStart(true)
                    }
                }
                .onResponse {
                    isRequest1139 = false
                    TimeMonitorManager.getMonitor(MonitorMC.SCENE_DETAIL)
                        .recordTime(MonitorMC.STAGE_NET_END)
                    statusPosterLiveData.value =
                        StatusPosterBean(DetailMC.STATUS_NEW_POSTER_HIDE_LOADING)
                    eventCallback?.onResponse()
                    LogUtil.d(UNLOCK, "解锁结果返回，状态：${it.data?.status}")
                    it.data?.run {
                        tomorrowUnlockTip = it.data?.tomorrowUnlockTip ?: ""
                        chapterInfo?.let { chapter ->
                            if (chapter.isNotEmpty() && unlockType == DetailMC.UNLOCK_AD_TYPE) {
                                unlockChaptersStartId = chapter.last().chapterId ?: ""
                                LogUtil.d(
                                    "VideoListVM",
                                    "$unlockChaptersStartId -- ${this@VideoListVM}"
                                )
                            }
                        }
                        val chapterIdAndBookIdList: MutableList<CommentNumCheckDatabaseBean> =
                            mutableListOf()
                        val chapterIdList: MutableList<String> = mutableListOf()
                        it.data?.chapterInfo?.forEach { item ->
                            item.chapterId?.let { chapterId ->
                                chapterIdList.add(chapterId)
                                chapterIdAndBookIdList.add(CommentNumCheckDatabaseBean(chapterId = item.chapterId , bookId = it.data?.bookId ))
                            }
                        }
                        commentLocalData
                        commentLocalData.value = chapterIdAndBookIdList
                        preLoadChapterList.value = chapterIdList
                        var vipChange = false  // vip状态是否发生变化
                        // 更新VIP状态
                        val newVipStatus = if (this.isVip == true) 1 else 0
                        if (newVipStatus != BBaseKV.vipStatus) {
                            var delayCallbackTime = 0L  // 延迟回调时间
                            vipChange = true
                            LogUtil.d(UNLOCK, "VIP状态变更，新状态：$newVipStatus")
                            BBaseME.get().onVipStatusChanged().post(newVipStatus)
                            LogUtil.d("recommend_draw_ad_tag", "vip状态：$newVipStatus")
                            BBaseKV.vipStatusCn = if (newVipStatus == 1) "会员" else "过期"
                            BBaseKV.vipStatus = newVipStatus
                            if (newVipStatus == 0) {  // VIP已过期
//                                if (status == 1) {  // 扣费成功
//                                    LogUtil.d(UNLOCK, "VIP过期，使用看点自动解锁")
////                                    delayCallbackTime = 2500L
////                                    ToastManager.showToast(
////                                        "您的VIP会员已过期，使用看点为您解锁视频",
////                                        delayCallbackTime
////                                    )
//                                } else {
//                                    delayCallbackTime = 1500L
//                                    ToastManager.showToast("VIP已过期，开通VIP为您解锁", delayCallbackTime)
//                                }
                                if (status == 2) {//解锁失败
                                    delayCallbackTime = 1500L
                                    ToastManager.showToast(
                                        "VIP已过期，开通VIP为您解锁",
                                        delayCallbackTime
                                    )
                                }
                            }

                            // 更新剧集里的VIP标识
//                            updateChapterList()
                            TaskManager.delayTask(delayCallbackTime) {
                                updateChapterList()
                            }
                        }
//                        if (status == 4) {}//阅读行为上报（自动加书架）
                        mVideoInfo?.videoInfo?.let { item ->
                            LogUtil.d(
                                DetailMC.START_PLAY_TAG,
                                "1139 接口返回回调 替代1701，it.inBookShelf==${item.inBookShelf}   data?.isInBookShelf==${this.isInBookShelf}"
                            )
                            if (item.inBookShelf == false && this.isInBookShelf == true) {
                                favoriteLiveData.value = true
                                HomeME.get().addFavoriteSuccess().post(this.bookId)
                                ActionRecorder.recordAction(ActionRecorder.ACTION_FAVORITE)
                                FlutterMS.get()?.sendEventToFlutter(
                                    "inBookShelf",
                                    mapOf("value" to true, "bookId" to this.bookId)
                                )
                                TaskManager.ioTask {
                                    DBHelper.insertOrUpdateHistory(this.bookId, true)
                                }
                                item.inBookShelf = this.isInBookShelf
                            }
                        }
                        // 回调解锁结果
                        if (!vipChange) {  // VIP状态发生改变的时候不回调
                            this.apply {
                                preload = isPreload
                            }

                            // 处理支付弹窗
                            // 预加载支付弹窗里的权益图片
                            val newVipRightsUrl =
                                payListResp?.payStyleList?.find { payStyle -> payStyle.type == 2 }?.vipRightsUrl
                            val oldVipRightsUrl =
                                <EMAIL>?.payStyleList?.find { payStyle -> payStyle.type == 2 }?.vipRightsUrl
                            if (newVipRightsUrl != oldVipRightsUrl) {
                                GlideUtils.preloadImage(AppModule.getApplication(), newVipRightsUrl)
                            }
                            // 缓存支付信息
                            <EMAIL> = payListResp
                            // 福利页弹窗图片
                            if (jumpTheaterImg != <EMAIL>) {
                                GlideUtils.preloadImage(AppModule.getApplication(), jumpTheaterImg)
                            }
                            <EMAIL> = jumpTheaterImg
                        }
                        unlockChapterLiveData.value = this
                        // 校验分辨率
                        if (this.isVip == false && resolutionEnable) {
                            if ((mVideoInfo?.videoInfo?.content?.resolutionRates?.find { rateVo -> rateVo.rate == currentResolution }?.needVip
                                    ?: 0) == 1
                            ) {
                                LogUtil.d(TAG_RESOLUTION, "1139接口VIP变更，恢复720P分辨率")
                                ToastManager.showToast("会员已到期，切换到720P")
                                BBaseKV.selectedResolution = BBaseMC.RATE_720P
                                currentResolution = BBaseMC.RATE_720P
                                DetailME.get().onResolutionChanged().post(
                                    ResolutionRateVo(
                                        rate = BBaseMC.RATE_720P,
                                        needVip = 1,
                                        scene = "Vip Invalid"
                                    )
                                )
                            }
                        }
                        PlayerMonitorManager.getPlayerMonitor(DetailMC.DETAIL_API_TRACK_TAG)
                            .recordTime(PlayerMonitorManager.TAG_REQUEST_TIME_END)
                        LogUtil.d(DetailMC.PLAYER_START_PLAY_TIME_TAG,"1139接口请求成功")
                        //处理是否继续弹窗
                        startContinueDialog(unlockType)
                    } ?: let {
                        if (!isPreload) {
                            errorMsg = "加载失败，请稍后重试"
                            errorCode.value = ERROR_UNLOCK_FAILED
                        }
                    }
                }.onError {
                    isRequest1139 = false
                    statusPosterLiveData.value =
                        StatusPosterBean(DetailMC.STATUS_NEW_POSTER_HIDE_LOADING)
                    LogUtil.d(UNLOCK, "解锁失败，${it.message}")
                    LogUtil.d(
                        DetailMC.PLAYER_DOWNLOAD_TAG,
                        "请求1139  解锁失败， isPreload===$isPreload"
                    )
                    statusPosterLiveData.value =
                        StatusPosterBean(DetailMC.STATUS_NEW_POSTER_SHOW_REFRESH, startChapterId)
                    if (!isPreload && (mChapterInfoVo == null || !downLoadUrlCanPlay(mChapterInfoVo!!))) {
                        DetailME.get().resetUnlockStatus().post(null)
                    }
                    if (resolutionState == DetailMC.RESOLUTION_STATE_SWITCHING) {
                        if (BBaseKV.resolutionSwitch) {
                            resolutionState = DetailMC.RESOLUTION_STATE_FAILED
                        } else {
                            closeResolution()
                        }
                    }
                }.doRequest()
        }
    }

    /**
     * 继续观看广告以解锁剧集的弹窗
     */
    var continueAdUnlockDialog: WeakReference<BaseDialogComp<*, *>>? = null

    /**
     * 视频解锁继续弹窗
     */
    private fun ChapterUnlockBean.startContinueDialog(unlockType: String) {
        if (LocalActivityMgr.getTopActivity() !is IPlayerDetail || unlockType != DetailMC.UNLOCK_AD_TYPE) { // 非广告解锁的
            return
        }

        LogUtil.d(UNLOCK, "尝试弹出看广告继续解锁弹窗")
        LogUtil.d(
            UNLOCK, "广告配置非IAA单次解锁集数：${mAdUnlockConfigVo?.videoAdConfigVo?.unlockVideoNum}"
        )
        LogUtil.d(
            UNLOCK, "已经解锁次数：${DetailKV.unlockNum}, 再次弹窗次数:${DetailKV.lookAgainNum}"
        )
        var canContinueUnlock = true  // 是否可以继续解锁
        var nextLockChapter: ChapterInfoVo? = null  // 下一个未解锁的剧集
        // 判断是否符合继续解锁规则
        if (mVideoInfo == null || mAdUnlockConfigVo?.videoAdConfigVo == null ||  // 过滤异常情况
            //DetailKV.unlockNum >= DetailKV.unlockLimit ||  // 已经达到最大解锁次数
            DetailKV.lookAgainNum >= mAdUnlockConfigVo?.videoAdConfigVo?.lookAgainNum!!  // 已达到最大再次解锁次数
        ) {
            canContinueUnlock = false
        }

        // 本次要解锁的集数
        var tempUnlockCount = mAdUnlockConfigVo?.videoAdConfigVo?.unlockVideoNum ?: 0
        // 判断后续章节里是否有未解锁章节
        if (canContinueUnlock && chapterInfo != null && chapterInfo!!.isNotEmpty()) {
            if (getPreviewConfig() != null) {
                LogUtil.d(
                    UNLOCK,
                    "本次已解锁的章节：开始章节${startChapterIndex}," + "结束章节${endChapterIndex}"
                )
            } else {
                LogUtil.d(
                    UNLOCK,
                    "本次已解锁的章节：开始章节${chapterInfo!!.first().chapterIndex}," + "结束章节${chapterInfo!!.last().chapterIndex}"
                )
            }
            LogUtil.d(UNLOCK, "开始遍历查找待解锁的章节")
            // 本次已解锁的拒接
            val lastUnlockChapter = if (getPreviewConfig() != null) {  // IAA模式下要通过 chapterIndex 来定位最后一个解锁的chapter
                getDataList().firstOrNull { it.chapterIndex == endChapterIndex }
            } else chapterInfo?.lastOrNull()
            lastUnlockChapter?.let {
                if (getPreviewConfig() != null) {  // IAA模式下解锁的剧集数量是动态的
                    // IAA下查找已解锁集的下一集所在区间，一次解锁几集
                    lastUnlockChapter.chapterIndex?.let { chapterIndex ->
                        getDataList().firstOrNull { it.chapterIndex == chapterIndex + 1 }?.let { nextChapter ->
                            tempUnlockCount = getAdUnlockChapterSize(nextChapter.chapterId)
                            LogUtil.d(UNLOCK, "本次为IAA，可解锁集数: $tempUnlockCount")
                        }
                    }
                }
                if (lastUnlockChapter.chapterIndex == null ||  // 返回数据异常
                    lastUnlockChapter.chapterIndex == mVideoInfo!!.videoInfo.updateNum || // 已经是最后
                    tempUnlockCount == 0  // 单个广告解锁集数
                ) {  // 当前已经是最后一集就不再弹
                    canContinueUnlock = false
                } else {
                    // 再次弹窗解锁剧集范围的规则：在接口规定的解锁剧集范围内，寻找还未解锁的视频
                    // 比如解锁配置要求看一次广告解锁3集，当前已经解锁至第10集。那么从11-13集里寻找未解锁的剧集。
                    // 如果11-13集都解锁，那么不再弹出继续解锁的弹窗，直接给Toast：解锁成功；
                    // 如果11-13集只要有未解锁的剧，那么就需要再次解锁，并且剧集是11-13集。
                    // 比如11集已解锁，12、13集未解锁，那么也会显示再次解锁11-13集。
                    var startListIndex = -1  // 在mVideoList中的index
                    val needUnlockChapterType = listOf(
                        DetailMC.CHAPTER_STATUS_PAY,
                        DetailMC.CHAPTER_STATUS_TOMORROW,
                        DetailMC.CHAPTER_STATUS_PREVIEW
                    )
                    for (index in 0 until getDataList().size) {
                        if (startListIndex >= 0 && getDataList()[index].isAd == 0) { // 开始寻找结束位置index的视频
                            if (tempUnlockCount > 0 && (getDataList()[index].isCharge in needUnlockChapterType)) {  // 存在未解锁的剧集
                                nextLockChapter = getDataList()[index]
                                LogUtil.d(
                                    UNLOCK,
                                    "下一个未解锁的开始剧集, 列表index：$index，剧集${getDataList()[index].chapterIndex}"
                                )
                                break
                            }
                            if (tempUnlockCount == 0) {
                                LogUtil.d(UNLOCK, "解锁范围内的剧已经全部解锁，无需继续解锁")
                                break
                            }
                            tempUnlockCount--
                        } else if (getDataList()[index].chapterIndex != null && getDataList()[index].chapterIndex == lastUnlockChapter.chapterIndex!!) {
                            startListIndex = index
                        }
                    }
                    canContinueUnlock = nextLockChapter != null
                }
            }
        } else {
            canContinueUnlock = false
        }
        LogUtil.d(
            UNLOCK,
            if (canContinueUnlock) "可以继续弹窗，下一次解锁剧集:${nextLockChapter?.chapterIndex}"
            else "不可以继续看广告解锁"
        )
        // 经过层层判断，如果不能继续解锁，那就退出
        val unlockNum = getUnlockVideoNum(chapterInfo)
        val successMsg = mAdUnlockConfigVo?.videoAdConfigVo?.adUnlockSuccDoc ?: "恭喜您解锁成功"
        LogUtil.d(
            UNLOCK,
            "unlockNum=$unlockNum canContinueUnlock=$canContinueUnlock unlockType=$unlockType"
        )
        unlockSuccessTrack(mChapterInfoVo, canContinueUnlock)
        if (!canContinueUnlock) {
            DetailME.get().playing().post(false)  // 暂停播放
            showContinueWatchDialog(successMsg) // 展示继续观看的弹窗
            return
        }
        // 可以继续解锁
        nextLockChapter?.let { lastUnlockChapter ->
            if (lastUnlockChapter.chapterIndex == null) {
                LogUtil.d(
                    UNLOCK,
                    "没有继续解锁剧集 lastUnlockChapter.chapterIndex == null"
                )
                return
            }
            DetailME.get().playing().post(false)  // 暂停播放
//            VideoTrackUtil.trackPause(
//                videoTrackInfo,
//                VideoTrackUtil.PAUSE_SCENE_DIALOG_UNLOCK_AGAIN
//            )
            //是否配置了后续解锁弹窗
            showAgainVideoUnlockedDialog(successMsg, lastUnlockChapter, tempUnlockCount)
        }
    }

    /**
     * 解锁成功继续观看弹窗
     * */
    private fun showContinueWatchDialog(msg: String) {
        if (getPreviewConfig() != null) {
            LogUtil.d(UNLOCK, "IAA模式下不弹出继续观看弹窗")
            continueAdUnlockDialog?.get()?.dismiss()
            clearPause()
            DetailME.get().playing().post(true)
            return
        }
        DetailMR.get().videoUnlockedDialog().apply {
            // 解锁成功文案
            successMsg = msg
            lookAgainDoc = "继续看剧"
            continueWatchIsVisible = false
        }.onSure {
            LogUtil.d(
                UNLOCK,
                "showContinueWatchDialog onSure"
            )
            continueAdUnlockDialog = WeakReference(it)
            continueAdUnlockDialog?.get()?.dismiss()
            sensorOperationClick(OperationClickTE.BUTTON_NAME_CONTINUE_WATCH, false)
        }.onClose { viewType, _ ->
            if (viewType == AdUnlockedDialogComp.CLOSE_BUTTON) {
                sensorOperationClick(OperationClickTE.BUTTON_NAME_CONTINUE_WATCH, false)
            }
        }.onDismiss {
            LogUtil.d(UNLOCK, "showContinueWatchDialog onDismiss")
            clearPause()
            DetailME.get().playing().post(true)
        }.start()
    }

    /**
     * 解锁成功提示用户可以继续解锁弹窗
     * */
    private fun showAgainVideoUnlockedDialog(msg: String, lastUnlockChapter: ChapterInfoVo, unlockNum: Int) {
        //是否配置了后续解锁弹窗
        DetailMR.get().videoUnlockedDialog().apply {
            // 解锁成功文案
            successMsg = msg
            // 提醒用户继续解锁的文案
            lookAgainDoc = mAdUnlockConfigVo?.videoAdConfigVo?.let {
                "${it.lookAgainDoc}${
                    if (getPreviewConfig() == null) {
                        VideoUnlockPresenter.getAdUnlockTip(
                            lastUnlockChapter.chapterIndex!!,
                            mVideoInfo,
                            mAdUnlockConfigVo?.videoAdConfigVo
                        )
                    } else {  // IAA
                        VideoUnlockPresenter.getAdUnlockTip(
                            lastUnlockChapter.chapterIndex!!,
                            mVideoInfo,
                            unlockNum
                        )
                    }
                }"
            } ?: ""
        }.onSure {
            continueAdUnlockDialog = WeakReference(it)
            if (it is AdUnlockedDialogComp) {
                it.showLoading()
            }
            lastUnlockChapter.chapterId?.let { id ->
                DetailKV.lookAgainNum++
                watchVideoAdUnlock(lastUnlockChapter, it)
            }
            sensorOperationClick(OperationClickTE.BUTTON_NAME_AD_AGAIN, true)
        }.onClose { viewType, _ ->
            if (viewType == AdUnlockedDialogComp.CLOSE_BUTTON) {
                //只有点击下方关闭按钮时才上报埋点
                sensorOperationClick(OperationClickTE.BUTTON_NAME_CONTINUE_WATCH, true)
            }
        }.onDismiss {
            clearPause()
            DetailME.get().playing().post(true)
        }.start()
    }

    private fun unlockSuccessTrack(chapterInfo: ChapterInfoVo?, havaAgain: Boolean) {
        DzTrackEvents.get().operationExposureTE()
            .popupName(OperationExposureTE.OPERATION_TYPE_AD_SUCCESS)
            .operationPosition(OperationExposureTE.POSITION_DETAIL_UNLOCK)
            .bookId(chapterInfo?.bookId)
            .bookName(chapterInfo?.bookName)
            .chapterId(chapterInfo?.chapterId)
            .chapterName(chapterInfo?.chapterName)
            .chapterIndex(chapterInfo?.chapterIndex)
            .isAdAgain(if (havaAgain) 1 else 0)
            .track()
    }

    private fun getUnlockVideoNum(chapterInfo: List<ChapterInfo>?): String {
        return chapterInfo?.let { unlockedChapters ->
            if (unlockedChapters.isEmpty()) {  // 异常情况
                "${mAdUnlockConfigVo?.videoAdConfigVo!!.unlockVideoNum}集"
            } else {
                "${unlockedChapters.size}集"
            }
        } ?: run {
            "${mAdUnlockConfigVo?.videoAdConfigVo!!.unlockVideoNum}集"
        }
    }

    override fun dismissUnlockDialogLoading() {
        val continueUnlockDialog = continueAdUnlockDialog?.get()
        if (continueUnlockDialog is AdUnlockedDialogComp) {
            continueUnlockDialog.dismissLoading()
        }
    }

    private fun getAdConfig(): UnLockConfigVo? {
        var unlockBean: UnLockConfigVo? = null
        try {
            mVideoInfo?.unLockConfigs?.forEach {

                if (it.unlockType == DetailMC.UNLOCK_AD_TYPE) {
                    unlockBean = it
                    val day = DateUtil.formatDateToDay()
                    if (day != DetailKV.unlockDate) {
                        DetailKV.unlockDate = day
                        DetailKV.unlockNum = 0
                        DetailKV.lookAgainNum = it.videoAdConfigVo?.lookAgainNum!!
                    }
                }
            }
        } catch (e: Exception) {
            LogUtil.printStackTrace(e)
        }
        return unlockBean
    }

    /**
     * 看视频解锁剧集
     */
    fun watchVideoAdUnlock(chapterInfo: ChapterInfoVo, dialog: BaseDialogComp<*, *>?) {
        if (chapterInfo.chapterId == null) {
            LogUtil.e(UNLOCK, "看广告解锁失败！待解锁剧chapterId为空")
            return
        }
        LogUtil.d(UNLOCK, "看广告进行视频解锁")
        if (!NetWorkUtil.isNetConnected(LocalActivityMgr.getTopActivity())) {
            LogUtil.d(UNLOCK, "当前无网络连接，取消激励视频")
            ToastManager.showToast("无网络连接，请检查网络设置")
            dismissUnlockDialogLoading()
            return
        }
        unlockChaptersStartId = chapterInfo.chapterId!!
        unlockChapter.value = UnlockBean(UnlockDelegate.UNLOCK_TYPE_AD, chapterInfo)
    }

    /**
     * 通知服务端推送的开关状态
     * @param isOpen Boolean
     */
    private fun uploadNotificationStatus(isOpen: Boolean) {
        val status = if (isOpen) 1 else 0
        val params = JSONObject()
        params["payToFree"] = status
        SplashMS
            .get()
            ?.uploadNotificationStatus(params, object : UploadNotificationStatusCallback {
                override fun onResponse(data: HttpResponseModel<PushUploadData>) {
                    if (data.data?.status == 1) {
                        subscribeStatus.value = isOpen
                        mVideoInfo?.freeNotificationSwitch = if (isOpen) 1 else 0
                    } else {
                        ToastManager.showToast("网络请求错误")
                    }
                }

                override fun onError(e: RequestException) {
                    ToastManager.showToast("网络请求错误")
                }
            })
    }

    /**
     * 更新订阅状态
     */
    fun onSubscribeClick() {
        isSubscribe = !isSubscribe
        mIsShowToast = false
        LogUtil.d("Subscribe", "updateSubscribeStatus：$isSubscribe")
        when (isSubscribe) {
            true -> {
                val isNotifyEnabled = NotificationUtil.isNotifyEnabled(AppModule.getApplication())
                if (!isNotifyEnabled) {
                    mIsShowToast = true
                    //要打开订阅开关，但是本地通知没开，需要跳转到设置页
                    NotificationUtil.openPushSetting(getActivity()!!)
                } else {
                    if (PushDialogManager.showPop(2)) {
                        //此时执行的是从关到开，实际上要走弹窗内部处理的逻辑，所以需要先恢复成关，会根据弹窗点击的结果进行恢复
                        isSubscribe = !isSubscribe
                        BBaseMR.get().pushOpenDialog().apply { title = BBaseMC.PUSH_TO_FREE
                            checkType = 2}
                            .onSure {
                                //通知打开的情况下，订阅开关打开，调接口更新状态
                                LogUtil.d("Subscribe", "更新订阅状态：true")
                                uploadNotificationStatus(true)
                                isSubscribe = !isSubscribe
                            }
                            .start()
                    } else {
                        //通知打开的情况下，订阅开关打开，调接口更新状态
                        LogUtil.d("Subscribe", "更新订阅状态：true")
                        uploadNotificationStatus(true)

                    }
                }
            }
            false -> {
                //关闭订阅,调接口更新状态
                LogUtil.d("Subscribe", "更新订阅状态：false")
                uploadNotificationStatus(false)
            }
        }

        val type = if (isSubscribe) "Open" else "Close"
        DzTrackEvents
            .get()
            .subscribe()
            .type(type)
            .buttonName("pay_to_free")
            .buttonPage("会员解锁页")
            .track()
    }


    /**
     * 预加载沉浸式广告
     */
    fun preloadDrawAd(
        position: Int,
        slideDown: Boolean, // true:手指上划看下面的剧集
        activity: Activity,
    ) {
        AppUtil.removeRunnable(preloadDrawRunnable)
        preloadDrawRunnable.prepare(position, slideDown, activity)
        AppUtil.runOnUiThreadDelay(preloadDrawRunnable, 5000)
    }

    private val preloadDrawRunnable = object : Runnable {
        var position: Int = 0
        var slideDown: Boolean = false
        lateinit var activity: Activity

        fun prepare(position: Int, slideDown: Boolean, activity: Activity) {
            this.position = position
            this.slideDown = slideDown
            this.activity = activity
        }

        override fun run() {
//            val cacheFeedAds = DrawAdManager.getCacheDrawAds(isLandMode())
//            if (!activity.isFinishing && !activity.isDestroyed) {
//                if (cacheFeedAds.isNullOrEmpty() || cacheFeedAds.size < cacheFeedAds[0].getDrawCacheNum()) {
//                    LogUtil.d(
//                        "adRequestSeq", "需要预加载，已缓存数:${cacheFeedAds?.size} " +
//                                "配置缓存数:${cacheFeedAds?.getOrNull(0)?.getDrawCacheNum()} cacheFeedAds=${cacheFeedAds}"
//                    )
                    loadDrawAd(activity, "onPageRelease()-> preload()")
//                } else {
//                    LogUtil.d(
//                        "adRequestSeq", " 缓存满，无需预加载，已缓存数:${cacheFeedAds.size} " +
//                                "配置缓存数:${cacheFeedAds.getOrNull(0)?.getDrawCacheNum()} cacheFeedAds=${cacheFeedAds}"
//                    )
//                }
//            } else {
//                LogUtil.d(
//                    "adRequestSeq", "无需预加载，" +
//                            "isDestroyed=${activity.isDestroyed} isFinishing=${activity.isFinishing}"
//                )
//            }
        }
    }

    /**
     * 检查本来应该出沉浸式广告的位置是否展示了广告。如果没展示要上报。
     * @param position Int
     */
    fun checkDrawAdMiss(position: Int) {
        if (isDrawAdFree()) {  // 判断是否是免广告状态
            return
        }
        if (!hasDrawAd) {
            // 本来应该出广告，但是目前还没插入，所以才要上报
//            if (ChapterIntervalUtil.detailInsertAdsIndexSet?.contains(position) == true) {
//                drawAdOperationTrack("no ad")
//            }
        } else {
            // nothing, 已经插入，会走曝光上报
        }
    }

    /**
     * 预加载后面几集
     */
    fun preLoadChapters(position: Int, mergeData: Boolean? = false) {
        val dataList = getDataList()
        if (position >= dataList.size) {
            return
        }
        if (dataList[position].isAd == 0 && TextUtils.isEmpty(dataList[position].m3u8720pUrl)) {
            getPreLoadChapter(position)?.let { chapterInfo ->
                chapterInfo.chapterId?.let { chapterId ->
                    unlock(chapterId, position, downLoadUrlCanPlay(dataList[position]), mergeData)
                }
            }
        } else {
            getPreLoadChapter(position)?.chapterId?.let { chapterId ->
                unlock(chapterId, position, true, mergeData)
            }
        }
    }

    private fun unlock(
        chapterId: String,
        position: Int,
        isPreload: Boolean = false,
        mergeData: Boolean? = false
    ) {
        LogUtil.d(
            DetailMC.START_PLAY_TAG,
            "预加载开始章节chapterId==$chapterId 是否预加载 isPreload==$isPreload"
        )
        val dataList = getDataList()
        var currentChapterId = mChapterInfoVo?.chapterId
        if (position < dataList.size) {
            currentChapterId = dataList[position].chapterId
        }
        if (AdvertisementUtil.isAdvertisementId(currentChapterId ?: "")) {
            currentChapterId = lastPlayChapterInfoVo?.chapterId
        }
        unlockChapter(
            chapterId,
            false,
            isPreload,
            currentChapterId = currentChapterId,
            mergeData = mergeData
        )
    }

    /**
     * 获取预加载开始章节
     * 1、如果当前位置后面第一个剧集章节就无播放链接，则从这里开始
     * 2、如果当前位置后面第一个剧集章节有播放链接，第二个剧集章节无播放链接，则从这里开始
     * 3、其他情况都返回空，表示不需要预加载下一集
     */
    private fun getPreLoadChapter(position: Int): ChapterInfoVo? {
        val dataList = getDataList()
        if (position + 1 >= dataList.size) {
            return null
        }
        for (index in (position + 1) until dataList.size) {
            val chapterInfo = dataList[index]
            if (chapterInfo.isAd == 0) {
                LogUtil.d(
                    "detail_start_play_tag",
                    "获取到预加载第一个章节id==" + chapterInfo.chapterId + "    index==$index"
                )
                return chapterInfo
            }
        }
        return null
    }

    private fun getChapterFromPosition(
        isPreload: Boolean, startChapterId: String, chapterSize: Int
    ): MutableList<String> {
        val chapterList = mutableListOf<String>()
        val dataList = getDataList()
        var position = -1
        var size = 0
        dataList.forEachIndexed { index, item ->
            if (item.chapterId == startChapterId) {
                position = index
                if (item.isCharge == DetailMC.CHAPTER_STATUS_PREVIEW ||
                    (TextUtils.isEmpty(item.m3u8720pUrl) && (item.isCharge != 1 || !isPreload))
                ) {
                    chapterList.add(startChapterId)
                }
                size++
            }
        }
        if (position == -1 || position + 1 >= dataList.size || chapterSize <= 1) {
            return chapterList
        }
        for (index in position + 1 until dataList.size) {
            val chapterInfo = dataList[index]
            if (chapterInfo.isAd == 0) {
                size++
                if (chapterInfo.isCharge == DetailMC.CHAPTER_STATUS_PREVIEW ||
                    (TextUtils.isEmpty(chapterInfo.m3u8720pUrl) && !TextUtils.isEmpty(chapterInfo.chapterId))
                ) {
                    chapterList.add(chapterInfo.chapterId ?: "")
                }
                if (chapterSize <= size) {
                    return chapterList
                }
            }
        }
        return chapterList
    }

    /**
     * 是否加载章节
     */
    private fun isLoadChapter(data: ChapterInfoVo): Boolean {
        val isLoadChapter =
            (data.isAd == 0 && data.m3u8720pUrl.isNullOrEmpty() && (data.isCharge != DetailMC.CHAPTER_STATUS_PAY && data.isCharge != DetailMC.CHAPTER_STATUS_TOMORROW))
        data.run {
            LogUtil.d(
                "VideoListVM",
                "预加载解锁章节 isLoadChapter--$isLoadChapter    chapterIndex--${data.chapterIndex}  chapterId--${data.chapterId}  isVideo--$isAd isCharge--$isCharge m3u8720pUrl--${m3u8720pUrl.isNullOrEmpty()}"
            )

        }
        return isLoadChapter
    }

    /**
     * 加入书架
     */
    fun addShelf(
        bookId: String?,
        chapterId: String?,
        scene: String?,
        omap: StrategyInfo?,
        followSource: FollowSourceType
    ) {
        var mOmap = omap
        if (routeIntent?.origin == "push") {
            if (mOmap == null) {
                mOmap = StrategyInfo(recId = RecId.PUSH_REC.recId)
            } else {
                mOmap.run {
                    recId = RecId.PUSH_REC.recId
                }
            }
        }
        HomeMS.get()?.addFavorite(
            bookId, chapterId, mOmap, scene, "2", object : FavoriteCallback() {
                override fun onStart() {
                    statusPosterLiveData.value =
                        StatusPosterBean(DetailMC.STATUS_POSTER_SHOW_LOADING)
                }

                override fun onSuccess(favorite: BaseEmptyBean) {
                    statusPosterLiveData.value =
                        StatusPosterBean(DetailMC.STATUS_POSTER_HIDE_LOADING)
                    mVideoInfo?.videoInfo?.inBookShelf = true
                    favoriteLiveData.value = true
                }

            override fun onFail(e: RequestException) {
                statusPosterLiveData.value = StatusPosterBean(DetailMC.STATUS_POSTER_HIDE_LOADING)
            }
        },

            TierPlaySourceVo(
                firstTierPlaySource = videoTrackInfo.firstTierPlaySource,
                secondTierPlaySource = videoTrackInfo.secondTierPlaySource,
                thirdTierPlaySource = videoTrackInfo.thirdTierPlaySource
            ),
            followSource,
            pageId = PageConstant.PAGE_ID_PLAYER,
        )
    }

    private fun onFavoriteSuccess() {

    }

    /**
     * 删除书架
     */
    fun deleteShelf(bookId: String?) {
        if (bookId != null) {
            HomeMS.get()?.deleteBooks(listOf(bookId), "2",
                TierPlaySourceVo(
                    videoTrackInfo.firstTierPlaySource,
                    videoTrackInfo.secondTierPlaySource,
                    videoTrackInfo.thirdTierPlaySource
                ),
                object : FavoriteCallback() {
                    override fun onStart() {
                        statusPosterLiveData.value =
                            StatusPosterBean(DetailMC.STATUS_POSTER_SHOW_LOADING)
                    }

                    override fun onSuccess(favorite: BaseEmptyBean) {
                        statusPosterLiveData.value =
                            StatusPosterBean(DetailMC.STATUS_POSTER_HIDE_LOADING)
                        mVideoInfo?.videoInfo?.inBookShelf = false
                        favoriteLiveData.value = false
                    }

                    override fun onFail(e: RequestException) {
                        statusPosterLiveData.value =
                            StatusPosterBean(DetailMC.STATUS_POSTER_HIDE_LOADING)
                    }
                })
        }
    }


    //1、同步播放进度至推荐；2、判断是否需要回到推荐并执行
    fun synPlayProgress(currentDuration: Long) {
        //点击返回时，将当前的播放进度同步到推荐页面的列表数据
        val resultVideoInfo = mVideoInfo?.videoInfo
        LogUtil.d("VideoListVM_back", "videoInfoVo==" + resultVideoInfo.toString())
        if (resultVideoInfo != null) {
            var chapterInfo = mChapterInfoVo
            if (chapterInfo?.m3u8720pUrl.isNullOrEmpty() || chapterInfo?.isCharge == DetailMC.CHAPTER_STATUS_PREVIEW) {
                chapterInfo = lastPlayChapterInfoVo
                LogUtil.d(
                    "VideoListVM_back", "lastPlayChapterInfoVo==" + lastPlayChapterInfoVo.toString()
                )
                LogUtil.d(
                    "VideoListVM_back", "mChapterInfoVo==" + mChapterInfoVo.toString()
                )
            }
            resultVideoInfo.shareTimes = mVideoInfo?.videoInfo?.getShareNum()
            chapterInfo?.run {
                val resolutions = resultVideoInfo.content?.resolutionRates
                resultVideoInfo.chapterId = chapterId
                resultVideoInfo.chapterIndex = chapterIndex
                resultVideoInfo.chapterName = chapterName
                resultVideoInfo.m3u8720pUrl = m3u8720pUrl
                resultVideoInfo.content = content?.apply {
                    resolutionRates = resolutions
                }
                resultVideoInfo.currentDuration = currentDuration
                resultVideoInfo.likesChecked = isLiked
                resultVideoInfo.likesNumActual = likesNumActual
                resultVideoInfo.likesNum = likesNum
                resultVideoInfo.curChapterCommentNum = commentNum
            }
            LogUtil.d("VideoListVM_back", "chapterInfo==" + chapterInfo.toString())
            if (chapterInfo != null) {
                LogUtil.d("VideoListVM_back", "播放链接==${resultVideoInfo.m3u8720pUrl}")
                HomeME.get().updateChapter().post(resultVideoInfo)
            }
        }
        //点击返回时如果需要回到推荐页面则执行
        if (mBackToRecommend == true) {
            MainMR.get().main().apply {
                selectedTab = MainIntent.TAB_HOME
                homeTabPage = HomeMC.TAB_RECOMMEND
            }.start()
        }
    }

    var allowReportInvalidPlay = false//允许上报无效播放（点击左上角返回键、系统返回按键）

    /**
     * hive打点
     */
    fun hiveLog(
        playStatus: Int,
        mFirstPlaySource: String?,
        mLastPlaySource: String?,
        currentDuration: Long,
        duration: Long,
        playingTime: Long,
        isEndPart: Int,
        focusVideoPlay: String,
        canReportInvalidPlay: Boolean,
        intent: VideoListIntent?,
        cOmap: StrategyInfo?,
        chapterInfoVo: ChapterInfoVo?,
        videoInfo: VideoInfoVo?,
        contentSource : Int?,
        triggerScenario: String? = null,  // 触发场景
    ) {
        // 过滤异常情况
        if (playStatus == 1 && currentDuration == 0L) {  // 播放结束 && 播放进度是0
            return
        }
        LogUtil.d("HiveTracker", "  playletSrcType = ${intent?.playletSrcType}")
        videoInfo?.let { vo ->
            val omap = OmapNode().apply {
                origin = intent?.origin ?: ""
                originName = intent?.originName ?: ""
                channelId = intent?.channelId ?: ""
                channelName = intent?.channelName ?: ""
                channelPos = if (intent?.channelPos == null) {
                    ""
                } else {
                    intent.channelPos.toString()
                }
                columnId = intent?.columnId ?: ""
                columnName = intent?.columnName ?: ""
                columnPos = if (intent?.columnPos == null) {
                    ""
                } else {
                    intent.columnPos.toString()
                }
                contentId = vo.bookId ?: ""
                contentPos = intent?.contentPos ?: 0 //%%
                contentType = "2"
                firstPlaySource = mFirstPlaySource ?: ""
                playletSrcType = intent?.playletSrcType
                lastPlaySource = mLastPlaySource ?: ""
                finishStatus = vo.finishStatusCn ?: ""
                firstCanFree = vo.firstCanFree ?: ""
                bookId = vo.bookId ?: ""
                playletId = intent?.bookId ?: ""
                playletName = vo.bookName ?: ""
                tag = vo.getTags()
                tagId = vo.getTagIds()
                rgts = BBaseKV.regTime
                nowChTime = BBaseKV.chTime
                setStrategyInfo(cOmap)
                playletName = vo.bookName ?: ""
                likeNum = vo.likesNumActual
                followNum = vo.videoStarsNumActual
                fromType = contentSource
                intent?.let {
                    channelGroupId = it.channelGroupId
                    channelGroupName = it.channelGroupName
                    channelGroupPos = it.channelGroupPos
                    from_origin = it.fromOrigin
                    post_type = it.postType
                    post_id = it.discussId
                    post_title = it.discussTitle
                }
                isEndpart = isEndPart.toString()
                if (playStatus == 1) {
                    playTime = (playingTime / 1000f).toString() //已经计算好的累计播放时长
                }
                when (intent?.showInfoType) {
                    BaseBookInfo.SHOW_INFO_RANK -> {  // 排行版
                        is_rec_content = 0
                        rec_reason_content = ""
                        is_top_list = 1
                        top_list = intent.rankActionTips
                    }
                    BaseBookInfo.SHOW_INFO_RECOMMEND_REASON -> {  // 推荐理由
                        is_rec_content = 1
                        rec_reason_content = intent.recReason
                        is_top_list = 0
                        top_list = ""
                    }
                    else -> {
                        is_rec_content = 0
                        rec_reason_content = ""
                        is_top_list = 0
                        top_list = ""
                    }
                }
            }
            val qmap = QmapNode().apply {
                playletId = vo.bookId ?: ""
                playletName = vo.bookName ?: ""
                partId = chapterInfoVo?.chapterId ?: ""
                isFocusVideoPlay = focusVideoPlay // 是否播过高光视频
                partNum = if (chapterInfoVo?.chapterIndex == null) {
                    ""
                } else {
                    chapterInfoVo.chapterIndex.toString()
                }
                partName = chapterInfoVo?.chapterName ?: ""
                //0-播放开始，1-播放结束
                eventType = if (playStatus == 0) QmapNode.VIDEO_PLAY else QmapNode.PLAY_END
                if (playStatus == 1) {
                    playTime = (currentDuration / 1000f).toString() //进度条时间
                    partTime = (duration / 1000f).toString() //当前集时长
                }
                endType = triggerScenario
            }

            DzTrackEvents.get().hivePv()
                .pType(if (playStatus == 0) QmapNode.VIDEO_PLAY else QmapNode.PLAY_END)
                .setTopic(if (HmHiveSDK.canUseSDK()) HiveTE.TOPIC_PRO else HiveTE.TOPIC_PRO_OLD)
//                .addParam("is_endpart", isEndPart)
                .withOmapSource(omap)
                .withQmapSource(qmap)
                .track()

            // 无效播放时长上报
            if (canReportInvalidPlay && chapterInfoVo?.chapterIndex == 1 && playStatus == 1 && playingTime < 3000) {//剧的第一集视频开播后，播放时长不足3秒上报
                LogUtil.d("HiveTracker", "二级页无效播放")
                val mQmap = QmapNode().apply {
                    playletId = qmap.playletId
                    playletName = qmap.playletName
                    partId = qmap.partId
                    partNum = qmap.partNum
                    partName = qmap.partName
                    isFocusVideoPlay = focusVideoPlay // 是否播过高光视频
                    //0-播放开始，1-播放结束
                    eventType = QmapNode.VIDEO_INVALID_PLAY
                    playTime = qmap.playTime
                    partTime = qmap.partTime
                }
                DzTrackEvents.get().hivePv()
                    .trackTag(HiveTE.TAG_BI)
                    .setTopic(if (HmHiveSDK.canUseSDK()) HiveTE.TOPIC_PRO else HiveTE.TOPIC_PRO_OLD)
                    .pType(QmapNode.VIDEO_INVALID_PLAY)
                    .withOmapSource(omap)
                    .withQmapSource(mQmap)
                    .track()
            }
        }
    }

    fun getPlayerMonitorVid(chapterId: String? = null): String {
        return DetailMC.DETAIL_TRACK_TAG
    }

    fun sensorPlaying(
        playStatus: Int,
        duration: Long,//总时长
        volume: Float,
        speed: Float,
        currentDuration: Long,//播放时长
        firstPlaySource: String?,
        autoPlay: Boolean?,
        reckonByTime: Long,//累计播放时长
        flipType: String,
        videoStyle: String,  // 横版、竖版
        renderingStartTime: Long,//启播时长
        isPayVideo:Boolean, //是否付费剧
        isDownload:Boolean,//是否播放下载剧
        isMultipleInstances: Boolean = false,
        maxRemainBuffer: Long? = null,
        isPreloadPlayer: Boolean = false,
        isPlayerPrePrepared: Boolean = false,
        contentSource: Int? = null,
        deviceInfoHelper: DeviceInfoHelper,
        triggerScenario: String? = null,  // 触发场景
    ) {
        TaskManager.ioTask {
            if (playStatus == 0) {
                PlayerMonitorManager.getPlayerMonitor(DetailMC.DETAIL_API_TRACK_TAG).api =
                    if (isFirstPlay) {
                        "1131+1139"
                    } else {
                        "1139"
                    }
                DzTrackEvents.get().videoStartPlaying()
                    .trackApiRequestTime(DetailMC.DETAIL_API_TRACK_TAG)
                    .trackPlayerTime(DetailMC.DETAIL_TRACK_TAG)
                    .sensorLog(mChapterInfoVo, duration, volume, speed, firstPlaySource)
                    .addParam("IsVIP", CommInfoUtil.isVip())
                    .isMultipleInstances(isMultipleInstances)
                    .addParam("VideoChargeType", if (isPayVideo) "付费剧" else "免费剧")
                    .addParam("VideoSource", if (isDownload) "下载来源" else "非下载来源")
                    .flipType(flipType).isAutoPlay(autoPlay).isLoad("预加载")
                    .playMode(if (playMode.value == PlayMode.IMMERSIVE) "沉浸式" else "常规")
                    .addParam("IsFocusVideo", "0")
                    .addParam("FocusVideoID", "")
                    .addParam("FocusVideoIndex", "")
                    .addParam("ContentSource", contentSource)
                    .addParam("IsDarkMode", deviceInfoHelper.isNightModeEnabled())
                    .addParam("ScreenBrightness", deviceInfoHelper.getScreenBrightness())
                    .addParam("MediaVolume", deviceInfoHelper.getCurrentVolume())
                    .discussId(routeIntent?.discussId)
                    .topicId(routeIntent?.topicId)
                    .isPreloadPlayer(isPreloadPlayer)
                    .isPlayerPrePrepared(isPlayerPrePrepared)
                    .enterVideoType(routeIntent?.enterVideoType ?: EnterTypeMode.OTHER.value)
                    .definition(
                        if (resolutionEnable) currentResolution
                            ?: mChapterInfoVo?.content?.mp4UrlRate
                            ?: mVideoInfo?.videoInfo?.content?.mp4UrlRate
                        else null ?: BBaseMC.RATE_720P
                    )
                    .pageInitFirstPlay(isFirstPlay)
                    .pipRatio(videoTrackInfo.pipRatio)
                    .videoStyle(videoStyle).track()
            } else {
                if (duration < 0 || reckonByTime > 1_0000_0000 || reckonByTime < 10) {  // 过滤异常数据
                    LogUtil.d(
                        "sensorLog",
                        "EndOfVideoPlayback 屏蔽结束播放事件上报，duration:$duration, reckonByTime:$reckonByTime"
                    )
                    return@ioTask
                }
                DzTrackEvents.get().endOfVideoPlayback()
                    .sensorLog(mChapterInfoVo, duration, volume, speed, firstPlaySource)
                    .addParam("IsVIP", CommInfoUtil.isVip())
                    .isMultipleInstances(isMultipleInstances)
                    .addParam("VideoChargeType", if (isPayVideo) "付费剧" else "免费剧")
                    .addParam("VideoSource", if (isDownload) "下载来源" else "非下载来源")
                    .maxRemainBuffer(maxRemainBuffer)
                    .discussId(routeIntent?.discussId)
                    .topicId(routeIntent?.topicId)
                    .playbackDuration(currentDuration).reckonByTime(reckonByTime).flipType(flipType)
                    .isLoad("预加载")
                    .playMode(if (playMode.value == PlayMode.IMMERSIVE) "沉浸式" else "常规")
                    .enterVideoType(routeIntent?.enterVideoType ?: EnterTypeMode.OTHER.value)
                    .addParam("IsFocusVideo", "0")
                    .addParam("FocusVideoID", "")
                    .addParam("FocusVideoIndex", "")
                    .addParam("ContentSource", contentSource)
                    .addParam("IsDarkMode", deviceInfoHelper.isNightModeEnabled())
                    .addParam("ScreenBrightness", deviceInfoHelper.getScreenBrightness())
                    .addParam("MediaVolume", deviceInfoHelper.getCurrentVolume())
                    .definition(
                        if (resolutionEnable) currentResolution
                            ?: mChapterInfoVo?.content?.mp4UrlRate
                            ?: mVideoInfo?.videoInfo?.content?.mp4UrlRate
                        else null ?: BBaseMC.RATE_720P
                    )
                    .videoStyle(videoStyle)
                    .apply {
                        triggerScenario.takeIf { !it.isNullOrEmpty() }?.let { addParam("Type", it) }
                    }
                    .pipRatio(videoTrackInfo.pipRatio)
                    .track()

            }
        }
    }

    var loadingScene = ReadingTE.LOADING_SCENE_PLAYING//播放器加载的场景

    /**
     * 视频播放中发生缓冲时间
     *
     * @param playStatus
     * @param duration 开始缓冲时传视频的总时长；结束缓冲后传缓冲的时长；
     * @param volume
     * @param speed
     */
    fun sensorPreload(
        playStatus: Int,
        duration: Long,
        volume: Float,
        speed: Float,
        isMultipleInstances: Boolean = false,
        bitrate: Float? = null,
    ) {
        if (playStatus == 0) {
            LogUtil.d(
                DetailMC.APP_LOADING_TAG,
                "开始加载打点，触发加载场景==$loadingScene  场景标识=二级播放页  剧名称=${mChapterInfoVo?.bookName}  第${mChapterInfoVo?.chapterIndex}集  VIP=${CommInfoUtil.isVip()}"
            )
            DzTrackEvents.get().videoPreload()
                .sensorLog(mChapterInfoVo, duration, volume, speed, "")
                .loadingScene(loadingScene)
                .addParam("IsVIP", CommInfoUtil.isVip())
                .downloadBitrate(bitrate)
                .isMultipleInstances(isMultipleInstances)
                .track()
        } else {
            LogUtil.d(
                DetailMC.APP_LOADING_TAG,
                "加载结束打点，缓冲耗时==$duration   场景标识=二级播放页  剧名称=${mChapterInfoVo?.bookName}  第${mChapterInfoVo?.chapterIndex}集  VIP=${CommInfoUtil.isVip()}"
            )
            DzTrackEvents.get().videoPreloadCompleted()
                .sensorLog(mChapterInfoVo, duration, volume, speed, "")
                .addParam("IsVIP", CommInfoUtil.isVip())
                .loadingScene(loadingScene)
                .isMultipleInstances(isMultipleInstances)
                .track()
        }
    }

    private fun ReadingTE.sensorLog(
        chapter: ChapterInfoVo?,
        duration: Long,
        volume: Float,
        speed: Float,
        firstPlaySource: String?,
    ): ReadingTE {
        var unlocktype = "免费"
        mChapterInfoVo?.isCharge?.let { //0免费、1未解锁、2已解锁、3VIP
            when (it) {
                2 -> {
                    unlocktype = "激励解锁"
                }

                3 -> {
                    unlocktype = "VIP"
                }
            }
        }
        LogUtil.d(
            "测试打点",
            "recReason = ${routeIntent?.recReason}   recReasonType = ${routeIntent?.recReasonType}  rankActionTips = ${routeIntent?.rankActionTips}   rankId = ${routeIntent?.rankId}"
        )
        <EMAIL>(unlocktype)
        mVideoInfo?.videoInfo?.run {

            <EMAIL>(bookId).bookName(bookName).chapterId(chapter?.chapterId)
                .chapterName(chapter?.chapterName).chapterIndex(chapter?.chapterIndex)
                .bookFinishStatus(finishStatusCn).bookStatus(finishStatus.toString())
                .chaptersPayType(chapter?.chaptersPayType)
                .setTag(if (bookTags == null) "" else bookTags.toString())
                .origin(routeIntent?.originName).firstPlaySource(firstPlaySource)
                .columnName(routeIntent?.channelName).maxChapter(updateNum).updateTime(utime)
                .duration(duration).payType(chapter?.payType).chaptersCoins(chapter?.price)
                .volume(volume).numberOfComments(0).numberOfLikes(0)
                .numberofCollections(videoStarsNumActual).playbackSpeed(speed)
                .isActorInfor((!performerInfo?.actressVideoNum.isNullOrEmpty() || !performerInfo?.actorVideoNum.isNullOrEmpty()))
                .isFemaleActorInfor(!performerInfo?.actressVideoNum.isNullOrEmpty())
                .isMaleActorInfor(!performerInfo?.actorVideoNum.isNullOrEmpty())
                .scene(routeIntent?.scene ?: "二级播放页")
                .isRecommend(BBaseKV.recommendContent)
                .cpPartnerName(cpPartnerName)
                .cpPartnerId(cpPartnerId)
                .isNewContent(isNewVideo())
            routeIntent?.let {
                this@sensorLog
                    .channelGroupId(it.channelGroupId)
                    .channelGroupName(it.channelGroupName)
                    .channelGroupPos(it.channelGroupPos)
                    .playletPosition(it.playletPosition)
                    .recPageNum(it.recPageNum)
                    .isRecPlaylet(it.isRecPlaylet)
                    .bookIndex(it.bookIndex)
                    .firstTierPlaySource(it.firstTierPlaySource)
                    .secondTierPlaySource(it.secondTierPlaySource)
                    .thirdTierPlaySource(it.thirdTierPlaySource)
                    .isDisplayBarrage(VideoDanMuManager.isEnable() && VideoDanMuManager.isEnableLocal())
                    .recReasonText(it.recReason)
                    .recReasonStyle(it.recReasonType)
                    .rankName(it.rankActionTips)
                    .rankType(it.rankId)
                    .floatingLayerState(it.floatingLayerState)
            }

        }
        return this
    }

    /**
     * @param unlockType -1没有解锁方式 0只有视频 1只有支付 2视频支付都有 3再看
     */
    fun sensorOperationClick(unlockType: Int, buttonName: String) {
        val operationName = when (unlockType) {
            0 -> {
                OperationClickTE.OPERATION_TYPE_AD
            }

            1 -> {
                OperationClickTE.OPERATION_TYPE_PAY
            }

            2 -> {
                OperationClickTE.OPERATION_TYPE_AD_PAY
            }

            else -> {
                OperationClickTE.OPERATION_TYPE_AD_AGAIN
            }
        }
        DzTrackEvents.get().operationClickTE()
            .operationName(operationName).operationType(operationName)
            .operationPosition(OperationClickTE.POSITION_UNLOCK).buttonName(buttonName)
            .bookId(mVideoInfo?.videoInfo?.bookId).bookName(mVideoInfo?.videoInfo?.bookName)
            .chapterId(mChapterInfoVo?.chapterId).chapterName(mChapterInfoVo?.chapterName)
            .chapterIndex(mChapterInfoVo?.chapterIndex).track()
    }

    /**
     * 解锁成功后，提示用户继续观看或者在看一个广告的弹窗 点击事件
     */
    private fun sensorOperationClick(buttonName: String, havaAgain: Boolean) {
        DzTrackEvents.get().operationClickTE()
            .popupName(OperationClickTE.OPERATION_UNLOCK_AD_SUCCESS)
            .operationPosition(OperationClickTE.POSITION_UNLOCK).buttonName(buttonName)
            .isAdAgain(if (havaAgain) 1 else 0).bookId(mVideoInfo?.videoInfo?.bookId)
            .bookName(mVideoInfo?.videoInfo?.bookName).chapterId(mChapterInfoVo?.chapterId)
            .chapterName(mChapterInfoVo?.chapterName).chapterIndex(mChapterInfoVo?.chapterIndex)
            .track()
    }

    fun ecpmToPrice(chapter: ChapterInfoVo?) {
        try {
            if (chapter?.payType?.contains("激励视频") == true) {
                var preEcpm = 0
                if (unlockAdEcpm != null) {
                    preEcpm = (unlockAdEcpm!! * 100).toInt()
                }
                if (preEcpm > 0) {
                    val num = mAdUnlockConfigVo?.videoAdConfigVo?.unlockVideoNum
                    if (num != null && num > 0) {
                        chapter.price = preEcpm / num
                    }
                }
            }
        } catch (e: Exception) {
        }
    }

    var renderFps: Float = 0f
    var downloadBitrate: Float = 0f
    var videoBitrate: Float = 0f
    var audioBitrate: Float = 0f
    var played: Boolean = false
    fun errorReport(
        errorCode: Int,
        errorMsg: String,
        duration: Long,
        extra: String?,
        isMultipleInstances: Boolean = false,
    ) {
        mVideoInfo?.videoInfo?.run {
            PlayerTrack.onError(
                errorCode,
                errorMsg,
                duration,
                bookId,
                bookName,
                mChapterInfoVo?.chapterId,
                mChapterInfoVo?.chapterIndex,
                mChapterInfoVo?.m3u8720pUrl,
                "二级播放页",
                extra,
                renderFps,
                downloadBitrate,
                videoBitrate,
                audioBitrate,
                played, isMultipleInstances, isFirstPlay,
                isPip = isInPip()
            )
        }
    }

    var likesLiveData = CommLiveData<BaseEmptyBean?>()
    fun addSubtractLikes(isLikes: Boolean) {
        mVideoInfo?.videoInfo?.run {
            HomeMS.get()?.addSubtractLikes(playletId = bookId,
                playletName = bookName,
                partId = mChapterInfoVo?.chapterId,
                partNum = mChapterInfoVo?.chapterIndex,
                likesType = if (isLikes) {
                    2
                } else {
                    1
                },
                origin = routeIntent?.origin,
                channelId = routeIntent?.channelId,
                channelName = routeIntent?.channelName,
                channelPos = routeIntent?.channelPos,
                columnId = routeIntent?.columnId,
                columnName = routeIntent?.columnName,
                columnPos = routeIntent?.columnPos,
                omap = omap ?: mOmap,
                contentId = bookId,
                contentType = 2,
                contentPos = routeIntent?.contentPos,
                callback = object : AddSubtractLikesCallback {
                    override fun onStart() {
                        statusPosterLiveData.value =
                            StatusPosterBean(DetailMC.STATUS_POSTER_SHOW_LOADING)
                    }

                    override fun onSuccess(data: BaseEmptyBean?) {
                        statusPosterLiveData.value =
                            StatusPosterBean(DetailMC.STATUS_POSTER_HIDE_LOADING)
                        likesLiveData.value = data
                    }

                    override fun onFail(e: RequestException) {
                        statusPosterLiveData.value =
                            StatusPosterBean(DetailMC.STATUS_POSTER_HIDE_LOADING)
                        ToastManager.showToast(e.message)
                    }

                    override fun onEnd() {
                    }
                },
                pageId = PageConstant.PAGE_ID_PLAYER
            )
        }
    }

    fun updateLikes(isLike: Boolean = true, likesKey: String) {
        if (!BBaseKV.syncLikes) {
            TaskManager.ioTask {
                if (isLike) {
                    DBHelper.checkAndInsertLikes(LikesEntity(likesKey))
                } else {
                    DBHelper.deleteLikes(likesKey)
                }
            }
        }
    }

    override fun getStatus(): StatusPoster = statusPoster

    override fun onAdAwardSuccess(ecpm: Double?) {
        DetailKV.unlockNum++
        adUnlockedTimes++
        if (getPreviewConfig() != null && DetailKV.iaaUnlockDate.isNotEmpty() && DateUtil.formatDateToDay() != DetailKV.iaaUnlockDate) { // 处理跨天的情况
            DzTrackEvents.get().operationExposureTE().apply {
                operationPosition("IAA解锁")
                operationName("IAA解锁")
                addParam("ADDescription", "当日解锁集数")
                addParam("UnlockNum", DetailKV.iaaUnlockNumOneDay)
            }.track()
            DetailKV.iaaUnlockNumOneDay = 0
        }
        if (DetailKV.iaaUnlockDate.isEmpty()) {
            DetailKV.iaaUnlockNumOneDay = 0
        }
        DetailKV.iaaUnlockDate = DateUtil.formatDateToDay()
        DetailKV.iaaUnlockNumOneDay++
        if (unlockChaptersStartId.isBlank()) {
            unlockChaptersStartId = mChapterInfoVo?.chapterId ?: ""
        }
        unlockChapter(unlockChaptersStartId, false, false, DetailMC.UNLOCK_AD_TYPE, ecpm)
    }

    override fun unlockChapter(): EventLiveData<UnlockBean> = unlockChapter

    override fun getVideoInfo(): VideoInfoVo? = mVideoInfo?.videoInfo

    override fun getAdUnlockConfig(): AdConfigVo? = mAdUnlockConfigVo?.videoAdConfigVo

    override fun getPreviewConfig(): ContinuousConfigVo? = mAdUnlockConfigVo?.continuousConfig

    override fun getVideoDetailBean(): VideoDetailBean? = mVideoInfo

    override fun getPageName(): String = "二级播放页"

    override fun getPageId(): String = PageConstant.PAGE_ID_PLAYER

    fun getDataList(): MutableList<ChapterInfoVo> {
//        if (getDrawAd() == null || BBaseKV.vipStatus == 1) {
//            return mVideoList
//        }
//        return mVideoAndAdList
        return videoList
    }

    /**
     * 获取集信息
     */
    fun getChapterInfoVo(position: Int): ChapterInfoVo? {
        if (videoList.size > position && position >= 0) {
            return videoList[position]
        }
        return null
    }
    //****************************************沉浸式广告****************************************
    /**
     * 刷新数据列表
     * 主要是更新数据列表里的沉浸式广告数据
     * 调用后，触发 videoListLiveData 变化
     */
    fun refreshDataList() { // vip变化，draw显示达到最大值，获得了奖励，奖励时间失效, 间隔章节发生改变，
        if (BBaseKV.vipStatus != 1) {//如果非VIP，则重新添加广告
            if (BBaseKV.freeDrawAd == 1) {
                if (videoList != mVideoListOnly) {
                    videoList = mVideoListOnly // 下面调用onDrawAdDestroy，这里为啥不调用？todo lzy
                }
            } else {
                val chapterPos = getCurrChapterPos()
                LogUtil.d("interval_chapter_detail", "refreshDataList() chapterPos:$chapterPos mPosition:$mPosition currentChapterIndex:$currentChapterIndex getUpNoAdsCnt:${getUpNoAdsCnt(mPosition)}")
                val adInsertIndexSet = ChapterIntervalUtil.getDetailInsertAdsIndexSet(
                    totalChapter = mVideoListOnly.size,
                    upNoAdsCnt = getUpNoAdsCnt(mPosition),
                    currChapterIndex = chapterPos
                )
                mVideoAndAdList.clear()
                mVideoListOnly.forEachIndexed { index, dramaVo ->
                    initVideoAndAdList(index, dramaVo,adInsertIndexSet)
                }
                videoList = mVideoAndAdList
            }
        } else {//如果是VIP,回收广告数据
            onDrawAdDestroy(true)
            videoList = mVideoListOnly  // 只保留视频数据
        }
        // 通过这里赋值，来触发后面的播放
        if (mChapterInfoVo?.isAd == 1) {
            mPosition = mChapterInfoVo?.index ?: 0
        } else {
            videoList.forEachIndexed { index, dramaVo ->
                if ((currentChapterIndex != null && dramaVo.chapterIndex == currentChapterIndex) || (currentChapterIndex == null && dramaVo.chapterId == mChapterInfoVo?.chapterId)) {
                    mPosition = index // todo lzy 这里为啥重新计算mPosition，是如何计算的，在哪里会用到，不计算会怎么样；我看onPageSelected也会重新给他赋值
                }
            }
        }
        keepPlayStatus = true
        videoListLiveData.value = getDataList()
    }

    private var hasInsertAd = false

    /**
     * 调用时机：
     * a.1131接口返回后(reqDetailInfo) b.1132获取剧集目录getChapterList() c.refreshDataList()[vip变化；领取奖励成功；adShowRequest1142奖励是否还在免广时间内；达到最大draw的展示次数]
     * 提前把广告列表准备好，但是这个方法本身不会触发adapter改变，要等到广告返回后再替换
     */
    private fun initVideoAndAdList(
        index: Int,
        dramaVo: ChapterInfoVo,
        adInsertIndexSet: Set<Int>
    ) {

        if (!AppUtil.isFastPrint()) {
            LogUtil.d("interval_chapter_detail", "===initVideoAndAdList===")
        }

        //根据广告配置插入沉浸式广告
        mVideoInfo?.getDrawAdConfig()?.run {
            //每天会重置广告曝光次数
            resetDrawConf()
                if (adInsertIndexSet.contains(index)) {
                    ChapterInfoVo().apply {
                        chapterId = "123".plus(index)
                        this.index = index
                        isAd = 1
                        mVideoAndAdList.add(this)
                    }
                }
        }
        mVideoAndAdList.add(dramaVo)
    }

    /**
     * 沉浸式广告运营位曝光打点（当沉浸式广告的位置曝光时或者快速划过沉浸式广告的位置时上报）
     * @param skipAfterPip Boolean 是否从小窗播放返回app后，滑动后的首个广告
     */
    fun drawAdOperationTrack(msg: String, subSlotId: String? = null, skipAfterPip: Boolean? = null) {
        if (isDrawAdFree()) {
            LogUtil.d(DetailMC.AD_TAG, "drawAdOperationTrack isDrawAdFree")
            return
        }
        val adId = DrawAdManager.getAdId(isLandMode())
        val drawReqSeq = DrawAdManager.peekReqSequence(adId)
        val drawReqType = AdManager.getDrawInsertType(DrawForceViewTimeUtil.peekNextForceViewTime("流量上报"))
        LogUtil.d(
            DetailMC.AD_TAG,
            "二级播放页沉浸式-广告流量请求 pos=${AdTE.DRAW_DETAIL} $msg [${adId}-${subSlotId}] reqInsType:$drawReqType "
        )
        LogUtil.d("adRequestSeq", "")
        LogUtil.d("adRequestSeq", "流量请求 $msg [${adId}-${subSlotId}] reqInsType:$drawReqType")
        AdManager.sendTrafficReachLog(
            AdTE.DRAW_DETAIL, adId ?: "", drawReqSeq, drawReqType, subSlotId ?: "", mVideoInfo?.getDrawAdConfig()?.blockConfigId
        )
        skipAfterPip?.let {
            DzTrackEvents.get().littleWindowsADShow()
                .showDone(it)
                .track()
        }
    }

    fun senADTrafficReachEvent(position: Int, sotId: String, blockConfigId: String?) {
        LogUtil.d(
            DetailMC.AD_TAG,
            "senADTrafficReachEvent二级播放页广告上报流量请求事件埋点,pos=$position,adId=$sotId"
        )
        AdManager.sendTrafficReachLog(position, sotId, blockConfigId)
    }

    /**
     * 取消广告运营位打点（成功与失败时触发）
     */
    fun cancelAdOperationTrack(adScene: String?, adFreeTime: Int?, success: Boolean) {
        DzTrackEvents.get().operationExposureTE().operationPosition(
            when (adScene) {
                VideoMC.SCENE_PLAYER_IMMERSIVE -> OperationExposureTE.POSITION_DETAIL_IMMERSIVE_AD
                VideoMC.SCENE_PLAYER_BOTTOM -> OperationExposureTE.POSITION_DETAIL_BOTTOM_AD
                else -> OperationExposureTE.POSITION_DETAIL_BOTTOM_AD
            }
        )
            .operationName(if (success) OperationExposureTE.OPERATION_CANCEL_AD_SUCCESS else OperationExposureTE.OPERATION_CANCEL_AD_FAIL)
            .adFreeTime(adFreeTime).bookId(mVideoInfo?.videoInfo?.bookId)
            .bookName(mVideoInfo?.videoInfo?.bookName).track()
    }

    /**
     * 每天会重置广告曝光次数
     */
    private fun resetDrawConf() {
        val day = DateUtil.formatDateToDay()
        if (day != DrawAdKV.detailDrawAdDate) {
            DrawAdKV.detailDrawAdDate = day
            DrawAdKV.detailDrawAdNum = 0
            DrawAdKV.detailDrawAdDayLoopCount = 0
        }
    }

    fun checkDrawMaxShowAndFreshDataList() {
        val maxLimit = DrawAdKV.detailDrawAdNum >= (mVideoInfo?.getDrawAdConfig()?.getDayMaxShowNum() ?: 0)
//        LogUtil.d(
//            DetailMC.AD_TAG,
//            "checkAndFreshDataList refreshDataList  maxLimit =${maxLimit} hasInsertAd=$hasInsertAd"
//        )
        if (maxLimit && hasInsertAd) {
            LogUtil.i(DetailMC.AD_TAG, "checkDrawMaxShowAndFreshDataList refreshDataList")
//            refreshDataList()
        }
    }

    /**
     * 检查沉浸式广告的间隔位置，并刷新
     */
    fun checkAdIntervalAndFreshDataList() {
        if (hasDrawAd) {  // 有沉浸式广告
            if (ChapterIntervalUtil.isDetailAdIntervalChanged(
                    mVideoListOnly.size,
                    getCurrChapterPos()
                )
            ) {  // 沉浸式广告的间隔发生了变化
                refreshDataList()  // 重新刷新列表，添加广告
            }
        } else {
            LogUtil.d("interval_chapter_detail", "no ads yet")
        }
    }

    fun checkPreloadRewardAd(): Boolean {
        return mVideoInfo?.getDrawAdConfig()?.canPreLoadRewardAd() == true
    }

    /**
     * 预加载免广激励视频
     */
    fun preloadRewardAd(activity: Activity) {
        LogUtil.d(DetailMC.AD_TAG, "页面启动preLoadRewardAd")
        LogUtil.d(
            "ad_load_with_preload", "免广场景 preloadRewardAd firstPreloadRewardAd=$firstPreloadRewardAd isVIP:${CommInfoUtil.isVip()}"
        )
        if (!firstPreloadRewardAd || CommInfoUtil.isVip()) {
            return
        }
        val adId = mVideoInfo?.getDrawAdConfig()?.getRewardRemoveAdId() ?: return
        if (mVideoInfo?.getDrawAdConfig()?.canPreLoadRewardAd() == false) {
            return
        }
        LogUtil.d("ad_load_with_preload", "免广场景 detail_draw_ad_tag preloadRewardAd adId = $adId")
        firstPreloadRewardAd = false
        AdMS.get()?.preloadAd(
            activity,
            AdTE.TYPE_REWARD,
            adId = adId,
            blockConfigId = mVideoInfo?.getDrawAdConfig()?.getRewardAdRemoveInfo()?.blockConfigId(),
            bookId = mVideoInfo?.videoInfo?.bookId,
            chapterId = mVideoInfo?.videoInfo?.chapterId,
            AdTE.EXEMPT_BOTTOM_DETAIL,
            mVideoInfo?.getDrawAdConfig()?.getRewardPreLoadNum()
        )
    }

    /**
     * 商城广告请求成功
     */
    fun sensorMallAdResponse(ad: InterstitialAd, requestTime: Long, sotId: String) {
        DzTrackEvents.get().adResponse()
            .setAdInfo(ad)
            .loadType(AdTE.IMME_LOAD)
            .timeSpent(System.currentTimeMillis() - requestTime)
            .commonTrack(ad.mRequestId, sotId)
    }

    fun sensorMallAdShow(ad: InterstitialAd, sotId: String, mallAdVo: MallAdVo?) {
        DzTrackEvents.get().adShow()
            .setAdInfo(ad)
            .blockConfigId(mallAdVo?.blockConfigId)
            .commonTrack(ad.mRequestId, sotId)
    }

    fun sensorMallAdClick(ad: InterstitialAd, sotId: String, mallAdVo: MallAdVo?) {
        DzTrackEvents.get().adClick()
            .setAdInfo(ad)
            .blockConfigId(mallAdVo?.blockConfigId)
            .commonTrack(ad.mRequestId, sotId)
    }

    private fun AdTE.commonTrack(requestId: String?, sotId: String) {
        track(requestId, sotId)
    }

    fun AdTE.track(
        requestId: String?,
        sotId: String,
    ) {
        requestId(requestId).adPosition(AdTE.MALL_142)
            .adSotId(sotId)
            .adType(AdTE.TYPE_INSERT)
            .userTacticInfo(mVideoInfo?.getDrawAdConfig()?.userTacticsVo)
            .track()
    }

    /**
     * 加载沉浸式广告
     * @param adContainer
     */
    fun loadDrawAd(activity: Activity, where: String) {
        if (isDrawAdFree() || playMode.value == PlayMode.PIP) {
            LogUtil.d(DetailMC.AD_TAG, "loadDrawAd but isDrawAdFree or pip. PlayMode:${playMode.value}")
            return
        }
        DrawAdManager.resetListener()
        DrawAdManager.onFirstLoadSuccess = {
            hasDrawAd = true
            initDrawAd = true
            // 通过这里赋值，来触发后面的播放 todo 这里直接refreshDataList是不是更好
            mVideoAndAdList.forEachIndexed { index, dramaVo ->
                if ((currentChapterIndex != null && dramaVo.chapterIndex == currentChapterIndex) || (currentChapterIndex == null && dramaVo.chapterId == mChapterInfoVo?.chapterId)) {
                    LogUtil.d(DetailMC.AD_TAG, "广告加载成功，mPosition $mPosition  index $index")
                    mPosition = index
                }
            }
            videoList = mVideoAndAdList
            keepPlayStatus = true
            videoListLiveData.value = getDataList()
        }
        DrawAdManager.onLoadError = { code, isRefill ->
            initDrawAd = true
            LogUtil.d(DetailMC.AD_TAG, "广告加载失败，开始轮询广告请求 code = $code")
            // slot 代码位关闭了
            if (code == ErrorCode.SLOT_IS_CLOSED_ERROR.code.toString() &&
                System.currentTimeMillis() - AdKV.requestNewSlotIdTime > AdKV.requestNewSlotSlotRefreshInterval * 1000
            ) {
                reqNewDrawSlotId()
            } else {
                LogUtil.d("adRequestSeq", "onLoadError -> loadDrawAd()")
                loadDrawAd(activity, true, isRefill)
            }
        }
        LogUtil.d("adRequestSeq", "$where-> loadDrawAd()")
        loadDrawAd(activity, false, false)
    }

    private fun reqNewDrawSlotId() {
        val spaceId = if (isLandMode()) NewDrawAdConfig.SPACE_ID_DETAIL_H else NewDrawAdConfig.SPACE_ID_DETAIL_V
        LogUtil.d(DetailMC.RETRY_AD_ID_TAG, "reqNewDrawSlotId 请求新的slotId  spaceId=$spaceId")
        AdNetWork.get().getDrawNewSlotConfig()
            .setParam(spaceId)
            .onResponse { response ->
                response.data?.marketingAdVo?.let { info ->
                    LogUtil.d(DetailMC.RETRY_AD_ID_TAG, "onResponse $info")
                    mVideoInfo?.updateDrawAdConfig(info)
                    ServerAdDataDispatcher.onDetailDrawAdConfigUpdate(info)
                    //横屏的替换横屏代码位
                    info.horizontalAdId?.let { horizontalAdId ->
                        mVideoInfo?.getDrawAdConfig()?.horizontalAdId = horizontalAdId
                    }
                    //接口请求间隔
                    AdKV.requestNewSlotSlotRefreshInterval = info.getApi2150Interval() ?: 60
                }
            }
            .onError {
                LogUtil.d(DetailMC.RETRY_AD_ID_TAG, "获取新的Draw配置失败。${it.message}")
            }
            .doRequest()
        AdKV.requestNewSlotIdTime = System.currentTimeMillis()
    }

    private fun loadDrawAd(activity: Activity, errorRetry: Boolean, isRefill: Boolean) {
        DrawAdManager.loadDrawAd(
            viewModelScope = viewModelScope,
            activity = activity,
            viewModel = this,
            errorRetry = errorRetry,
            isFillUp = isRefill,
            isLandMode = isLandMode()
        )
    }

    /**
     * 使用时需要处理回收情况
     */
    var keepPlayStatus = false

    /**
     * 沉浸式广告回收
     */
    fun onDrawAdDestroy(vipRecycleRes: Boolean = false) {
        DrawAdManager.onDestroy(vipRecycleRes)
    }

    var initDrawAd = false

    @Volatile
    private var firstPreloadRewardAd = true

    /**
     * 获取缓存的沉浸式广告
     * @return
     */
    fun getMaxCacheFeedAd(): FeedAd? {
        return DrawAdManager.getMaxCacheFeedAd(isLandMode())
    }

    /**
     * 是否是横屏模式
     */
    fun isLandMode(): Boolean {
        return (orientation.value ?: Orientation.Port) != Orientation.Port
    }

    ///***************************************************ABTest终章推荐********************************************************
    /**
     *  1、在需要章末推荐的位置发起请求requestTest
     *  2、requestTest请求开始时，显示loading   statusPosterLiveData.value = StatusPosterBean(DetailMC.STATUS_POSTER_SHOW_LOADING)
     *  3、requestTest请求异常，隐藏loading   statusPosterLiveData.value = StatusPosterBean(DetailMC.STATUS_POSTER_HIDE_LOADING)，同时通知页面显示默认章末推荐
     *  4、requestTest请求成功，发起章末推荐接口请求requestRecommended
     *  5、requestRecommended请求成功，隐藏loading   statusPosterLiveData.value = StatusPosterBean(DetailMC.STATUS_POSTER_HIDE_LOADING)，同时通知页面展示章末推荐
     *  6、requestRecommended请求失败，隐藏loading   statusPosterLiveData.value = StatusPosterBean(DetailMC.STATUS_POSTER_HIDE_LOADING)，同时通知页面显示默认章末推荐
     *
     *
     *
     *  章末推荐显示
     *  1、默认状态：倒计时3s，计时结束跳转至首页，同时首页同部剧集数更新，自动切换至下一部剧
     *  2、新的章末推荐：
     *      1.根据数据显示剧集列表
     *      2.倒计时结束后自动跳转至当前选中的集
     *
     *
     *  打点
     *  1、视频开始播放打点，增加参数
     *  2、大数据增加曝光打点
     *  3、
     *
     */

    var recommendLiveData = CommLiveData<RecommendVideoInfo?>()
    //剧末推荐数据是否为空
    var isRecommendDataEmpty = true

    var loading: Boolean = false
    fun requestRecommended(bookId: String, isLandScape: Boolean) {
        if (loading || getDataList().isEmpty()) {
            return
        }
        if (!isRecommendDataEmpty) {
            LogUtil.d(DetailMC.DETAIL_VP_TAG, "剧末推荐数据不为空，请求数据")
            return
        }
        loading = true
//        if (ExperimentMC.recommendVideoSize == 0) {
//            recommendLiveData.value = null
//            loading = false
//        } else {
        HomeMS.get()?.getRecommendInfo(
            bookId,
            ExperimentMC.recommendVideoSize,
            isLandScape,
            object : RecommendReqCallback<RecommendVideoInfo> {
                override fun onStart() {
                    statusPosterLiveData.value =
                        StatusPosterBean(DetailMC.STATUS_POSTER_SHOW_LOADING)
                }

                override fun onSuccess(data: RecommendVideoInfo?) {
                    statusPosterLiveData.value =
                        StatusPosterBean(DetailMC.STATUS_POSTER_HIDE_LOADING)
                    data?.dataList?.firstOrNull()?.let {
                        isRecommendDataEmpty = false
                        LogUtil.d("DetailRecommendUtil", "DetailRecommendUtil_toJson = ${it.toJson()} ")
                        it.bookName?.let { item ->
                            LogUtil.d("DetailRecommendUtil", "DetailRecommendUtil_bookName = $item ")
                            DetailRecommendUtil.setRealToast(item)
                        }
                        it.bookId?.let { bookId ->
                            LogUtil.d("DetailRecommendUtil", "DetailRecommendUtil_bookName = $bookId ")
                            DetailRecommendUtil.setBookId(bookId)
                        }
                        DetailRecommendUtil.firstData = it
                        DetailRecommendUtil.from = mVideoInfo?.videoInfo
                        DetailRecommendUtil.payInfo =
                            if (mVideoInfo?.videoInfo?.isPayVideo() == true) "付费" else "免费"
                    }
                    LogUtil.d("DetailRecommendUtil", "DetailRecommendUtil.firstData = ${DetailRecommendUtil.firstData}  DetailRecommendUtil.from = ${DetailRecommendUtil.from}")
                    recommendLiveData.value = data
                }

                override fun onFail(e: RequestException) {
                    statusPosterLiveData.value =
                        StatusPosterBean(DetailMC.STATUS_POSTER_HIDE_LOADING)
                    recommendLiveData.value = null
                    isRecommendDataEmpty = true
                }

                override fun onEnd() {
                    loading = false
                }
            })
//        }
    }

    var toastCommLiveData = CommLiveData<ToastInfo>()

    // 请求奖励 获取奖励
    fun rewardReport(
        operateId: Int?,
        exemptTime: Int?,
        adScene: String?,
        chapterId: String?,
        currentDuration: Long?,
        rewardScene: String?,
        showOneMoreDialogAction: ((bean: OperateReportBean) -> Unit)
    ) {
        val failToast = "领取奖励失败，请稍后重试"
        operateId?.let {
            VideoNetwork.get().awardReport()
                .setParams(operateId, adScene = adScene ?: "", rewardScene = rewardScene)
                .onResponse { resp ->
                    // 1141接口
                    val operateReportBean: OperateReportBean? = resp.data
                    val respSuccess = operateReportBean?.status == 1
                    if (respSuccess) {
                        waitingRemoveAdsAndPlayVideo = true
                        BBaseKV.freeDrawAd = 1
                        BBaseKV.freeDetailBottomAd = 1
                        BannerAdKV.freeMineBannerAd = 1
                        saveBeforeRefreshPlayInfo(chapterId, currentDuration)
                        operateReportBean?.let {
                            it.printDebugInfo()
                            multiBtnConf = it.getMultiBtnConf()
                            val showLookAgainDialog = it.getLookAgainFlag()
                            LogUtil.i(DetailMC.FREE_DRAW_TAG, "showLookAgainDialog=$showLookAgainDialog")
                            if (showLookAgainDialog) {
                                AppUtil.runOnUiThreadDelay(
                                    { showOneMoreDialogAction.invoke(it) },
                                    100 // 偶现百度的激励视频关闭之后还存在一会，百度激励视频activity不是FragmentActivity导致NP/或者弹窗展示到了百度广告里面，所以延迟一点
                                )
                            } else {
                                tryRemoveAdsAndPlayVideo()
                                toastCommLiveData.value = ToastInfo(it.getWinTitleDoc(), it.getHotWords())
                                LogUtil.i(DetailMC.FREE_DRAW_TAG, "toast() winTitleDoc:${it.getWinTitleDoc()} msg:${it.msg} hotWords:${it.getHotWords()}")
                            }
                        }
                    } else {
                        LogUtil.e(DetailMC.FREE_DRAW_TAG, "领取奖励失败, msg:${resp.msg} status:${operateReportBean?.status}")
                        toastCommLiveData.value = ToastInfo(failToast)
                        tryRemoveAdsAndPlayVideo()
                    }
                    cancelAdOperationTrack(adScene, exemptTime, respSuccess)
                    statusPosterLiveData.value =
                        StatusPosterBean(DetailMC.STATUS_POSTER_HIDE_LOADING)
                }.onError {//广告播放完成，上报失败
                    LogUtil.d(DetailMC.FREE_DRAW_TAG, "领取奖励失败 $it")
                    tryRemoveAdsAndPlayVideo()
                    statusPosterLiveData.value =
                        StatusPosterBean(DetailMC.STATUS_POSTER_HIDE_LOADING)
                    toastCommLiveData.value = ToastInfo(failToast)
                    cancelAdOperationTrack(adScene, exemptTime, false)
                }.doRequest()
        }
    }

    fun tryRemoveAdsAndPlayVideo() {
        if (!waitingRemoveAdsAndPlayVideo) {
            return
        }
        waitingRemoveAdsAndPlayVideo = false
        LogUtil.i(DetailMC.FREE_DRAW_TAG, "领取奖励成功，刷新二级播放器列表")
        refreshDataList()
        refreshBottomBannerAd()
    }

    var adShowRequestIng = false
    fun adShowRequest(
        activity: Activity,
        chapterId: String? = null,
        currentDuration: Long? = null
    ) {
        LogUtil.d(
            DetailMC.FREE_DRAW_TAG,
            "页面被选中，是否请求1142接口==${BBaseKV.freeDrawAd != 0}   freeDrawAd==${BBaseKV.freeDrawAd}"
        )
        if (BBaseKV.freeDrawAd == 0 || adShowRequestIng) {
            return
        }
        adShowRequestIng = true
        LogUtil.d(DetailMC.FREE_DRAW_TAG, "开始请求1142接口")
        VideoMS.get()?.adShowRequest(VideoMC.SCENE_PLAYER_IMMERSIVE, object : Callback {
            override fun onResponse(status: Int?) {
                LogUtil.d(DetailMC.FREE_DRAW_TAG, "1142接口返回  status==$status  1表示奖励有效")
                if (status == 0) {
                    BBaseKV.freeDrawAd = 0
                    saveBeforeRefreshPlayInfo(chapterId, currentDuration)
                    refreshDataList()
                    loadDrawAd(activity, "onResponse()")

                    BBaseKV.freeDetailBottomAd = 0
                    //开始加载底部广告
                    refreshBottomBannerAd()

                }
                adShowRequestIng = false
            }

            override fun onError(e: RequestException) {
                adShowRequestIng = false
            }
        })
    }

    private fun refreshBottomBannerAd() {
        DetailME.get().toLoadBottomAd().post(null)
    }

    fun adLoaderDestroy() {
        try {
            adLoader?.stop( mVideoInfo?.getDrawAdConfig()?.getRewardRemoveAdId())
        } catch (e: Throwable) {
            e.printStackTrace()
            LogUtil.e("adLoaderDestroy", "exception")
        }
        adLoading = false
        adLoader = null
        statusPosterLiveData.value = StatusPosterBean(DetailMC.STATUS_POSTER_HIDE_LOADING)
    }

    fun onDestroy() {
        AppUtil.removeRunnable(preloadDrawRunnable)
    }

    /**
     * 暂停广告回收
     */
    fun onPauseAdDestroy() {
        PauseAdManager.onDestroy()
    }

    /**
     * 商城广告回收
     */
    fun onMallAdDestroy() {
        mVideoInfo?.getMallAdConfig()?.getAdId()?.let {
            MineMallAdManager.onDestroy(it)
        }
    }

    /**
     * 获取缓存的暂停广告
     * @return
     */
    fun getPauseAd(): FeedAd? {
        return PauseAdManager.getPauseAd()
    }

    fun initShareData() {
        mWxShareConfigVo = GsonUtils.fromJson(BBaseKV.shareConfig, WxShareConfigVo::class.java)
    }

    fun isImmersiveEnable(): Boolean {
        return mVideoInfo?.immersiveSwitchVo?.run {
            if (orientation.value == Orientation.Port) secondaryPlayPageImmersiveSwitch == 1
            else horizontalImmersiveSwitch == 1
        } ?: false
    }

    fun getImmersiveWaitTime(): Int {
        return mVideoInfo?.immersiveSwitchVo?.run {
            if (orientation.value == Orientation.Port) secondaryPlayPageImmersiveStartTime
            else horizontalImmersiveStartTime
        } ?: 0
    }

    /**
     * 设置列表中是否插入了广告标识
     */
    fun setHasInsertAd(list: MutableList<ChapterInfoVo>?) {
        var firstAdItem: ChapterInfoVo? = null

        if (!list.isNullOrEmpty()) {
            firstAdItem = list.firstOrNull {
                it.isAd == 1
            }
        }
        hasInsertAd = firstAdItem != null
    }

    // 查找mPosition上方有多少个非广告章节
    private fun getUpNoAdsCnt(currentPos: Int): Int {
        var upAdIndex = -1 // 广告所在位置
        if (getDataList().isNotEmpty()) {
            for (i in currentPos downTo 0) {
                if (i < getDataList().size && getDataList()[i].isAd == 1) {
                    upAdIndex = i
                    break
                }
            }
        }
        if (upAdIndex == -1) {
            return currentPos
        }
        return if (upAdIndex == currentPos) 0 else currentPos - upAdIndex
    }

    // chapterPos代表不包含广告的位置。currentChapterIndex==null 说明当前是广告
    private fun getCurrChapterPos() = currentChapterIndex?.let { it - 1 } ?: (mChapterInfoVo?.index ?: 0)

    val rewardStatus = CommLiveData<Int>()
    val updateRewardProgress = CommLiveData<Boolean>()
    var remotePlayingDuration = 0L  // 服务端播放时长
    private val localPlayingDuration: Float  // 本地播放时长
        get() {
            return PlayingStatisticsMgr.getLocalPlayingDurationFloat()
        }
    var syncingPlayingDuration = 0F  // 正在同步中的播放时长
    var taskProgress = 0.0F  // 进度条挂件进度
    var previousStage: StageReadAward? = null  // 上一个已完成
    var currentStage: StageReadAward? = null  // 当前已完成
    var nextStage: StageReadAward? = null  // 接下来要完成
    var targetDuration = 0L
    var targetCoins = 0
    var lastStageTarget = 0L  // 已完成阶段的时长
    var notClaimedCoins = 0  // 未领取的金币
    var progressUp : Double? = 0.0   // 上报进度

    // 用于2.0版本进度条挂件的动画展示
    var afterSyncTaskStage = -1  // 同步阶段任务后的任务阶段
    var beforeSyncStage = -1  // 同步阶段任务前的任务阶段

    fun syncRewardStatus(reason: String? = null) {
        beforeSyncStage = afterSyncTaskStage
        LogUtil.d(
            WelfareMC.TAG_REPORT,
            "同步任务奖励状态。local duration:$localPlayingDuration reason:$reason"
        )
        val callBack = object : ReportCallback by noOpDelegate() {
            override fun onSuccess(result: TaskReportResult?) {
                result?.let {
                    remotePlayingDuration = it.actualReadTimeSeconds ?: 0L
                    stageReadAwardList = it.stageReadAwardList
                    recStageCoinTip = it.recStageCoinTip
                    updateStage("同步服务端阶段任务")
                }
            }
        }
        mVideoInfo?.videoInfo?.let { video ->
            WelfareMS.get()?.reportPlayingDuration(
                syncingPlayingDuration.toInt(),
                video.bookId,
                video.chapterId,
                callBack,
//                PlayingStatisticsMgr.mLocalPlayingDurationMap,
                emptyMap(),
                1
            )
        }
    }

    /**
     * 播放时长失败时重试上报
     */
    private var hasReportTask = false
    private val reportRetryTime = 2
    private val reportRetryInterval = 5_000L
    private val stageDurations = listOf(10, 20, 30) // 各阶段持续时间
    val circleSecond = 30  // 一圈30秒（对应150次？
    private var stageReadAwardList: List<StageReadAward>? = null
        set(value) {
            field = value
            GsonUtil.toJson(value)?.let {
                WelfareKV.stageReadAwardList = it
            }
        }
        get() {
            if (field != null) {
                return field
            }
            try {
                val type = object : TypeToken<List<StageReadAward>>() {}.type
                field = GsonUtil.gson.fromJson(WelfareKV.stageReadAwardList, type)
            } catch (e: Exception) {
                e.printStackTrace()
            }
            return field
        }

    private val reportCallback by lazy {
        object : ReportCallback {
            override fun onSuccess(result: TaskReportResult?) {
                // 保存阶段任务完成情况
                LogUtil.d(WelfareMC.TAG_REPORT, "更新阶段任务状态")
                clearLocalPlayDuration()
                PlayingStatisticsMgr.mIsReporting = false
                syncingPlayingDuration = 0F
                result?.let {
                    remotePlayingDuration = it.actualReadTimeSeconds ?: 0L
                    stageReadAwardList = it.stageReadAwardList
                    recStageCoinTip = it.recStageCoinTip
                    updateStage("上报看剧时长")
                }
            }

            override fun onFailed(code: Int, msg: String) {
                // 2次5秒间隔的重试
                LogUtil.d(WelfareMC.TAG_REPORT, "播放任务上报失败 $code $msg")
                if (code == 1) {  // 不满足上报条件
                    // 1.5.1 上报失败后直接丢弃，不记录到本地
                    // localPlayingDuration += syncingPlayingDuration  // 累计到本地
                    clearLocalPlayDuration()
                    countProgress()
                    updateRewardProgress.postValue(true)
                } else {  // 网络或接口错误
                    LogUtil.e(WelfareMC.TAG_REPORT, "上报接口请求失败")
                    clearLocalPlayDuration()
                    countProgress()
                    updateRewardProgress.postValue(true)
                    if (hasReportTask) {
                        LogUtil.e(WelfareMC.TAG_REPORT, "有待上报任务，继续上报 3")
                        reportAward()
                    }
                }
                PlayingStatisticsMgr.mIsReporting = false
            }
        }
    }

    var recStageCoinTip: Int? = 0  // 0-不开启引导配置， 1-开启福利钱包引导配置

    // 清空本地播放时长记录
    fun clearLocalPlayDuration() {
        PlayingStatisticsMgr.clearLocalPlayingDurationAndSave()
        syncingPlayingDuration = 0F
    }

    fun reportAward() {
        if (syncingPlayingDuration > 0L) {
            hasReportTask = true
            LogUtil.i(
                WelfareMC.TAG_REPORT,
                "有正在上报的任务，取消本次上报，稍后再试! syncingPlayingDuration:$syncingPlayingDuration"
            )
            return
        }
        hasReportTask = false
        syncingPlayingDuration = PlayingStatisticsMgr.mLocalPlayingDuration
        LogUtil.d(
            WelfareMC.TAG_REPORT,
            "开始进行上报 duration:${syncingPlayingDuration}秒"
        )
        val localPlayingDurationMapLong: Map<String, Long> = PlayingStatisticsMgr.mLocalPlayingDurationMap.mapValues {
            it.value.toLong()
        }
        mVideoInfo?.videoInfo?.let { video ->
            WelfareMS.get()?.reportPlayingDuration(
                syncingPlayingDuration.toInt(),
                video.bookId,
                mChapterInfoVo?.chapterId,
                reportCallback,
                localPlayingDurationMapLong,
                1
            )
        } ?: let {
            LogUtil.e(WelfareMC.TAG_REPORT, "播放任务上报失败！获取剧集信息为空")
            clearLocalPlayDuration()
        }
    }

    fun countProgress(): Float {
        // 计算进度条挂件的进度
        val time =
            (localPlayingDuration + remotePlayingDuration + syncingPlayingDuration - lastStageTarget)
        val alltime = getAllPlayTime()
        // 计算当前阶段的挂件绘制一圈的时长
        val circle = if (playerPendantConfig?.speed == 1) stageDurations[when {
            alltime < 10 -> 0
            alltime < 30 -> 1
            else -> 2
        }] else circleSecond
        // 计算当前阶段的绘制圈的进度
        taskProgress = if (targetDuration == 0L) {
            1F
        } else {
            if (circle == 20) {
                ((time - 10) % circle).toFloat() / circle.toFloat()
            } else {
                (time % circle).toFloat() / circle.toFloat()
            }
        }
        LogUtil.d(
            WelfareMC.TAG_WELFARE,
            "计算后的播放进度 localPlayingDuration:$localPlayingDuration ｜remotePlayingDuration:$remotePlayingDuration｜ syncingPlayingDuration:$syncingPlayingDuration l｜astStageTarget:$lastStageTarget getAllPlayTime()=${getAllPlayTime()} time=${time} circle=${circle}"
        )
        LogUtil.d(
            WelfareMC.TAG_WELFARE,
            "计算后的播放进度 progress:$taskProgress"
        )
        if (taskProgress < 0) {
            onProgressError()
            taskProgress = 0.1F
        }
        return taskProgress
    }

    /**
     * 上次进度异常的时间
     * 做频次控制使用
     */
    private var lastProgressErrorTimeMillis: Long = 0

    /**
     * 福利挂件进度条异常时的处理
     * 增加了频次控制
     */
    private fun onProgressError() {
        val currentTimeMillis = System.currentTimeMillis()
        if (currentTimeMillis - lastProgressErrorTimeMillis < 10_000) {
            return
        }
        lastProgressErrorTimeMillis = currentTimeMillis
        val message = "progress:$taskProgress local:$localPlayingDuration sync:$syncingPlayingDuration " +
                "remote:$remotePlayingDuration lastStage:$lastStageTarget target:$targetDuration"
        LogUtil.e(WelfareMC.TAG_REPORT, "进度异常！$message")
        // 错误上报
        DzTrackEvents.get().error()
            .type(ErrorTE.CODE_WELFARE_PROGRESS_ERROR)
            .pageInitFirstPlay(isFirstPlay)
            .message(message)
            .track()
        // 从服务端同步最新进度
        syncRewardStatus("progress exception")
    }

    //获取二级播放页一共的播放时长
    fun getAllPlayTime(): Float {
        return localPlayingDuration + remotePlayingDuration.toFloat()
    }


    var lastStageUpdateScene: String? = null
    fun updateStage(scene: String? = null) {
        lastStageUpdateScene = scene
        LogUtil.d("task_stage", "更新阶段任务状态 scene:$scene")
        if (stageReadAwardList.isNullOrEmpty()) {
            LogUtil.d("task_stage", "stageReadAwardList为空")
            return
        }
        synchronized(stageReadAwardList!!) {
            previousStage = currentStage
            val alreadyDuration = localPlayingDuration + remotePlayingDuration
            val updatedList = stageReadAwardList!!.map { stage ->
                if (stage.duration * 60 <= alreadyDuration && stage.status == 1) {
                    stage.copy(status = 2)
                } else {
                    stage
                }
            }
            stageReadAwardList = updatedList
            val lastCompletedStageIndex =
                updatedList.indexOfLast { it.status == 2 || it.status == 3 }
            if (lastCompletedStageIndex == -1) {
                this.currentStage = null
                this.nextStage = updatedList[0].apply { index = 0 }
            } else {
                this.currentStage =
                    updatedList[lastCompletedStageIndex].apply { index = lastCompletedStageIndex }
                this.nextStage = updatedList.getOrNull(lastCompletedStageIndex + 1)
                    ?.apply { index = lastCompletedStageIndex + 1 }
            }
            lastStageTarget = ((currentStage?.duration ?: 0) * 60).toLong()
            targetDuration = nextStage?.let {
                it.duration * 60L - lastStageTarget
            } ?: 0L
            targetCoins = nextStage?.award ?: 0
            notClaimedCoins = updatedList.filter { it.status == 2 }.sumOf { it.award }
            rewardStatus.value = when {
                (currentStage?.status == null || currentStage!!.status == 3) && nextStage?.status == 1 ->
                    WelfareReward.STATUS_INCOMPLETE

                currentStage?.status == 2 ->
                    WelfareReward.STATUS_UNCLAIMED

                currentStage?.status == 3 && nextStage == null ->
                    WelfareReward.STATUS_COMPLETED

                else -> {
                    LogUtil.e("task_stage", "计算任务阶段错误")
                    WelfareReward.STATUS_ERROR
                }
            }
            LogUtil.d("task_stage", "previous:$previousStage\ncurrent:$currentStage\nnext:$nextStage")
            LogUtil.d(
                "task_stage",
                "阶段任务状态更新结束.已完成时长:$alreadyDuration rewardStatus:${rewardStatus.value} 阶段任务:$updatedList"
            )
        }
        LogUtil.d(
            "task_stage",
            "阶段任务状态更新结束。未领取金币:$notClaimedCoins 下个阶段时长:$targetDuration 下个阶段金币:$targetCoins"
        )
    }

    //播放列表刷新前的播放进度
    var beforeRefreshPlayDuration: Long? = 0
    private var beforeRefreshChapterId: String? = null
    fun saveBeforeRefreshPlayInfo(chapterId: String?, currentDuration: Long?) {
        beforeRefreshChapterId = chapterId
        beforeRefreshPlayDuration = currentDuration
    }

    /**
     * 是否需要还原 播放列表刷新之前的进度，充值vip 刷新，看激励视频免广告刷新
     */
    fun needResetBeforeRefreshDuration(chapterId: String?): Boolean {
        return beforeRefreshChapterId == chapterId && (beforeRefreshPlayDuration ?: 0) > 0
    }

    fun clearBeforeRefreshPlayInfo() {
        beforeRefreshChapterId = null
        beforeRefreshPlayDuration = null
    }

    /**
     * 播放器配置
     *
     * @param bookId
     * @param firstTierPlaySource
     */
    fun getOperationConfig(bookId: String?, firstTierPlaySource: String?) {
        DetailNetWork.get().playerConfig()
            .setParam(bookId, firstTierPlaySource)
            .onResponse {
                //记录请求接口时间 用来判断是否弹出弹窗
                it.data?.operlocationConf?.quitPlayPagePopVo?.taskAward?.requestTime =
                    SystemTimeUtils.currentTimeMills()
                playerConfig.value = it.data
                //增加一个条件防止在最后一次曝光后切后台回来的时候请求1231刷掉数据
                if(it.data?.operlocationConf?.secondPlayerPop?.advertImg != null || it.data?.operlocationConf?.secondPlayerPop?.lockPageImg != null)
                {
                    immersiveADLimit = it.data?.operlocationConf?.secondPlayerPop?.advertNum ?: -1
                    lockTimes = it.data?.operlocationConf?.secondPlayerPop?.lockPage ?: -1
                    popUpConfigVo = it.data?.operlocationConf?.secondPlayerPop
                    vipCompIntervalTime = popUpConfigVo?.period?:0
                    popUpConfigVo?.advertImg?.let { it1 -> preloadDetailPicture(it1, 1) }
                    popUpConfigVo?.advertReceiveSuccessImg?.let { it1 -> preloadDetailPicture(it1, 1) }
                    popUpConfigVo?.lockPageImg?.let { it1 -> preloadDetailPicture(it1, 2) }
                    popUpConfigVo?.lockPageSuccessImg?.let { it1 -> preloadDetailPicture(it1, 2) }
                }
                if((popUpConfigVo?.popScene?.split(",")?.map { it.trim().toInt() }?.size ?: 0) > 1)
                {
                    scene = 3
                }else{
                    scene = popUpConfigVo?.popScene?.toInt()
                }
            }
            .onError {
                LogUtil.d(TAG_PLAYER_CONFIG, "获取播放器配置失败。${it.message}")
            }
            .doRequest()
    }

    private fun preloadDetailPicture(url : String, scene: Int?){
        LogUtil.d(TAG_PLAYER_CONFIG, "预加载图片 scene = $scene (1 广告 2 lock)")
        GlideUtils.preloadImageListen(AppModule.getApplication(), url) {
            if (it) {
                LogUtil.d(TAG_PLAYER_CONFIG, "预加载图片 scene = $scene 成功")
                if(scene == 1){
                    checkADPic ++
                }else{
                    checkLockPic ++
                }
            }
        }
    }

    fun report1141(id: Int) {
        popUpConfigVo?.id?.let {
            VideoNetwork.get().awardReport()
                .setParams(it, "popExposure" , position = popUpConfigVo?.position)
                .onResponse {
                    //status == 1  广告播放完成，上报成功      广告播放完成，上报失败: ${it.msg}
                    LogUtil.d("report1141", "请求成功 status =：${it.data?.status}")

                }.onError {//广告播放完成，上报失败
                    LogUtil.d("report1141", "请求1141失败")
                }.doRequest()
        }
    }

//    fun report1144() {
//        DetailNetWork.get().requestVip()
//            .setParams(BBaseMC.sendVipToken)
//            .onResponse {
//                LogUtil.d("report1144", "请求成功 status = ${it.data?.status}")
//            }.onError {
//                LogUtil.d("report1144", "请求1144失败")
//            }
//            .doRequest()
//    }

    /**------------------------------------------------------------------------------------------
     * 获取本地某个key下有多少条数据
     * @param
     */
    fun getCommentCountByIdentifier(commentNumCheckDatabaseBeans: List<CommentNumCheckDatabaseBean?>) {
        viewModelScope.launch(Dispatchers.IO) {
            val dao = CommentsDatabase.get().getCommentsDAO()

            // 收集所有 CommentNumCheckDatabaseBean 的结果
            val result = commentNumCheckDatabaseBeans.mapNotNull { commentNumCheckDatabaseBean ->
                commentNumCheckDatabaseBean?.bookId?.let { bookId ->
                    commentNumCheckDatabaseBean.chapterId?.let { chapterId ->
                        val commentNum = dao.getCommentCountByIdentifier(
                            bookId = bookId,
                            chapterId = chapterId,
                            userId = BBaseKV.userId
                        )
                        CommentNumCheckDatabaseBean(
                            commentNum = commentNum,
                            bookId = bookId,
                            chapterId = chapterId
                        )
                    }
                }
            }
            if (result.size == 1) {
                commentLocalItemDataFirst.postValue(result.toMutableList())
            } else {
                commentLocalItemData.postValue(result.toMutableList())
            }
            // 使用 postValue 更新 LiveData，传递所有结果

            LogUtil.d("comment", "本地存储num commentNumLocalData 首页 = ${result.toMutableList()}")
        }
    }

    fun sendVipCompNewComp(scene: String) {
        if (getPreviewConfig() != null) return  // 如果已经有预览配置，则不再请求
        popUpConfigVo?.receiveToken?.let {
            DetailNetWork.get().requestVip()
                .setParams(it,scene)
                .onResponse { response ->
                    if (response.data?.status == 1) {
                        // 更新VIP状态
                        BBaseKV.vipStatus = 1
                        BBaseKV.vipStatusCn = "会员"
                        PriorityTaskManager.removeTask(PriorityMC.TASK_PAY_DIALOG)
                        BBaseME.get().refreshOperation().post(Any())
                        BBaseME.get().onVipStatusChanged().post(1)
                        // 显示 sendVIPRewardDialog
                        DetailME.get().dismissVipComp().postSticky(1)
                        Handler(Looper.getMainLooper()).postDelayed({
                            DetailMR.get().sendVIPComp().apply {
                                if (scene.toInt() == 1) {
                                    compName = "二级免广告页领取会员后弹窗"
                                    imgUrl = popUpConfigVo?.advertReceiveSuccessImg
                                } else {
                                    compName = "解锁页领取会员后弹窗"
                                    imgUrl = popUpConfigVo?.lockPageSuccessImg
                                }

                                type = 4
                            }.onShow {
                                pausePlay(VideoMC.PLAY_END_DIALOG_REWARD_VIP)
                            }.onDismiss {
                                cancelPause(VideoMC.PLAY_END_DIALOG_REWARD_VIP)
                            }.start() // 启动弹窗流程
                        }, 320) // 延迟 300 毫秒
                    } else {
                        // 如果接口返回的状态不是 1，处理错误情况
                        ToastManager.showToast(response.data?.msg)
                        LogUtil.e("sendVIPComp", "VIP状态更新失败，status: ${response.data?.status}")
                    }
                }
                .onError { error ->
                    // 网络请求失败的处理
                    LogUtil.e("sendVIPComp", "1144接口请求失败: ${error.message}")
                }
                .doRequest()
        } // 执行请求
    }
    val sendVipScene = "3"
    fun request1143(scene: String ,token :String?) {
        token?.let {
            DetailNetWork.get().requestVip()
                .setParams(it,scene)
                .onResponse { response ->
                    if (response.data?.status == 1) {
                        // 更新VIP状态
                        BBaseKV.vipStatus = 1
                        BBaseKV.vipStatusCn = "会员"
                        PriorityTaskManager.removeTask(PriorityMC.TASK_PAY_DIALOG)
                        PriorityTaskManager.removeTask(PriorityMC.TASK_SEND_VIP_DIALOG)
                        BBaseME.get().onVipStatusChanged().post(1)
                    } else {
                        // 如果接口返回的状态不是 1，处理错误情况
                        ToastManager.showToast(response.data?.msg)
                        LogUtil.e("sendVIPComp", "VIP状态更新失败，status: ${response.data?.status}")
                    }
                }
                .onError { error ->
                    // 网络请求失败的处理
                    LogUtil.e("sendVIPComp", "1144接口请求失败: ${error.message}")
                }
                .doRequest()
        } // 执行请求
    }

    /**
     * 开始打点被拦截情况
     */
    fun interceptStartPlayTrack(
        currentPosition: Int,
        position: Int,
        vid: String?,
        currentVid: String?
    ) {
        kotlin.runCatching {
            JSONObject().apply {
                put("currentPosition", currentPosition)
                put("position", position)
                vid?.let {
                    put("vid", vid)
                }
                currentVid?.let {
                    put("currentVid", it)
                }
                DzTrackEvents.get().error().pageInitFirstPlay(isFirstPlay)
                    .type(ErrorTE.CODE_INTERCEPT_START_PLAY_ERROR)
                    .message(this.toString()).bookId(mVideoInfo?.videoInfo?.bookId)
                    .chapterId(mChapterInfoVo?.chapterId).track()
            }
        }
    }

    suspend fun chapterHasDownloaded(bookId: String, chapterId: String): Boolean {
        if (bookId.isEmpty() || chapterId.isEmpty()) {
            return false
        }
        return DownloadMS.get()?.chapterHasDownloaded(bookId, chapterId) ?: false
    }

    /**
     * 下载链接是否可以播放 true：可以播放；false：不可以播放
     */
    fun downLoadUrlCanPlay(chapterInfo: ChapterInfoVo): Boolean {
        LogUtil.d(
            DetailMC.PLAYER_DOWNLOAD_CHECK_TAG,
            "chapterInfo.downLoadUrl  ==${chapterInfo.downLoadUrl}" +
                    "\n" +
                    "chapterInfo.localFileAvailability ==${chapterInfo.localFileAvailability} " +
                    "\n" +
                    " chapterInfo.downloadState  ==${chapterInfo.downloadState}" +
                    "\n" +
                    " chapterInfo.chapterIndex  ==${chapterInfo.chapterIndex}" +
                    "\n" +
                    " chapterInfo.chapterStatus  ==${chapterInfo.chapterStatus}" +
                    "\n" +
                    " chapterInfo.isCharge  ==${chapterInfo.isCharge}" +
                    "\n" +
                    " BBaseKV.vipStatus  ==${BBaseKV.vipStatus}" +
                    "\n" +
                    " 下载的链接是否可以播放  ==${
                        !chapterInfo.downLoadUrl.isNullOrEmpty()
                                && chapterInfo.localFileAvailability
                                && chapterInfo.downloadState == BBaseMC.DOWNLOAD_STATE_DONE
                                && chapterInfo.chapterStatus == 1
                                && ((chapterInfo.isCharge == 0 || chapterInfo.isCharge == 2) || (chapterInfo.isCharge == 3 && BBaseKV.vipStatus == 1))
                    }"
        )
        return !chapterInfo.downLoadUrl.isNullOrEmpty()
                && chapterInfo.localFileAvailability
                && chapterInfo.downloadState == BBaseMC.DOWNLOAD_STATE_DONE
                && chapterInfo.chapterStatus == 1
                && ((chapterInfo.isCharge == 0 || chapterInfo.isCharge == 2) || (chapterInfo.isCharge == 3 && BBaseKV.vipStatus == 1))
    }

    fun deleteLoadChapters() {
        viewModelScope.launch(Dispatchers.IO) {
            routeIntent?.bookId?.let { bookId ->
                DownloadMS.get()?.getCompleteChapters(bookId).let { map ->
                    updateChapterStatus(getDataList(), map)
                }
            }
        }
    }

    private fun updateChapterStatus(list: List<ChapterInfoVo>, map: Map<String, ChapterInfoVo>?) {
        list.forEach { item ->
            if (map?.containsKey(item.chapterId) == false) {
                item.apply {
                    localFileAvailability = false
                }
            }
        }
    }

    fun updateDownLoadChapter(chapterInfo: ChapterInfoVo) {
        getDataList().find {
            it.chapterId == chapterInfo.chapterId
        }?.apply {
            downLoadUrl = chapterInfo.downLoadUrl
            downloadState = chapterInfo.downloadState
            videoSize = chapterInfo.videoSize
            chapterStatus = chapterInfo.chapterStatus
            //下载完成重置为切换地址默认状态
            chapterInfo.switchState = SwitchState.NO_SWITCH
            chapterInfo.contentUlrIndex = -1
            localFileAvailability = chapterInfo.localFileAvailability
        }
        if (chapterInfo.bookId != mVideoInfo?.videoInfo?.bookId) {
            return
        }
        chapterInfo.chapterId?.let {
            downLoadVideoMap.put(it, ChapterInfoVo().apply {
                downLoadUrl = chapterInfo.downLoadUrl
                downloadState = chapterInfo.downloadState
                chapterStatus = chapterInfo.chapterStatus
                videoSize = chapterInfo.videoSize
                localFileAvailability = chapterInfo.localFileAvailability
            })
            if (chapterInfo.bookId == mVideoInfo?.videoInfo?.bookId) {
                alreadyDownloadChapters[it] = chapterInfo
            }
        }
    }

    fun setAdLifeCycle(adLifecycle: PlayDetailLifeCycle) {
        this.mAdLifeCycle = adLifecycle
    }

    /**
     * 自动选择分辨率
     * - 如果已经下载了，就用下载的视频。分辨率是720P
     * - 遍历当前剧集支持的分辨率，选中与用户已选择相符合的分辨率；
     * - 如果上面都没有，就用720P分辨率
     */
    fun autoSelectResolution() {
        if (resolutionState == DetailMC.RESOLUTION_STATE_INVALID) {
            LogUtil.d(TAG_RESOLUTION, "清晰度功能关闭，不自动选择清晰度")
            return
        }
        if (resolutionState != DetailMC.RESOLUTION_STATE_AUTO) {
            LogUtil.d(TAG_RESOLUTION, "用户已经设置过清晰度，不自动选择清晰度")
            return
        }
        currentResolution = mChapterInfoVo?.let {
            if (alreadyDownloadChapters.keys.contains(it.chapterId)) {
                BBaseMC.RATE_720P
            } else {
                mVideoInfo?.videoInfo?.content?.resolutionRates?.find { it.rate == BBaseKV.selectedResolution }?.rate
                    ?: it.content?.mp4UrlRate ?: BBaseKV.selectedResolution
            }
        } ?: BBaseMC.RATE_720P
        LogUtil.d(
            TAG_RESOLUTION,
            "自动选择分辨率. 当前集:${mVideoInfo?.videoInfo?.bookName} " +
                    "${mChapterInfoVo?.chapterName} ${mChapterInfoVo?.chapterId} \n" +
                    "url resolution:${mChapterInfoVo?.content?.mp4UrlRate}\n" +
                    "selectedResolution:${BBaseKV.selectedResolution} " +
                    "currentResolution:$currentResolution " +
                    "alreadyDownloadChapters:${alreadyDownloadChapters.keys}"
        )
    }

    /**
     * 关闭清晰度
     */
    fun closeResolution() {
        resolutionEnable = false
        resolutionState = DetailMC.RESOLUTION_STATE_INVALID
        currentResolution = null
        switchingResolution = null
    }

    /**
     * 更新评论数
     * @param position Int
     */
    fun updateVideoCommentNum(position: Int, chapterIds: List<String>? = null) {
        videoListLiveData.value?.let {
            // 加载当前集和下一集
//            val maxLoadIndex = min(position + 1, videoList.size - 1)
//            for (tempPos in position..maxLoadIndex) {
//                val item = videoList[tempPos]
//                if (item.isVideo()) {
//                    reqCommentNum(item)
//                }
//            }
            //1.16.0 修改评论预加载逻辑
            LogUtil.d(TAG_COMMENT, " 请求评论 position = $position  chapterIds = $chapterIds")
            reqCommentNum(getChapterInfoVo(position), chapterIds)
            commentLocalData.value = mutableListOf(CommentNumCheckDatabaseBean(bookId = mVideoInfo?.videoInfo?.bookId , chapterId = getChapterInfoVo(position)?.chapterId))
        }
    }

    /**
     * 检查是否需要查询接口获取评论数据
     * @param position Int
     */
    fun checkVideoCommentNum(position: Int): Boolean {
        return getChapterInfoVo(position)?.commentNum == null
    }

    fun syncCommentNum( num :Long){
        currentChapter.commentNum = num
    }

    /**
     * 初始化本地数据
     */
    fun commentLocalDataInit(info: ChapterInfoVo?){
        mVideoInfo?.videoInfo?.bookId?.let { bid ->
            info?.chapterId?.let { cid ->
                commentLocalData.value =
                    mutableListOf(CommentNumCheckDatabaseBean(bookId = bid, chapterId = cid))
            }
        }
    }
    /**
     * 请求评论数
     */
    fun reqCommentNum(info: ChapterInfoVo? , chapterIds: List<String>? = null) {
        mVideoInfo?.videoInfo?.bookId?.let { bid ->
            info?.chapterId?.let { cid ->
                commentLocalData.value = mutableListOf(CommentNumCheckDatabaseBean(bookId = bid , chapterId = cid))
                VideoMS.get()?.getCommentNum(bid, cid,
                    object : OnRequestCallback<CommentNumBean>  {
                        override fun onStart() {
                        }

                        override fun onEnd() {
                        }

                        override fun onError(e: RequestException) {
                            LogUtil.e(TAG_COMMENT, "获取评论数失败！bid:$bid cid:$cid ${e.message}")
                        }

                        override fun onResponse(data: CommentNumBean?) {
                            if (data?.result == 0) {
                                data.run {
                                    bookId = bid
                                    chapterId = cid
                                    if (bid == currentChapter.bookId && cid == currentChapter.chapterId) {
                                        currentChapter.commentNum = commentNum
                                    }
                                    LogUtil.d(
                                        "commentNumDetail",
                                        "3106请求得到 data.commentNum = ${commentNum} "
                                    )
                                    LogUtil.d(
                                        "commentNumDetail",
                                        "3106请求得到 currentChapter.commentNum = ${currentChapter.commentNum} "
                                    )
                                    LogUtil.d(
                                        "commentNumDetail",
                                        "3106请求得后 =data.commentNum ${commentNum} "
                                    )
                                }
                                LogUtil.d(
                                    "commentNumDetail",
                                    "1111113106请求得后 =data.commentNumLiveData ${data} "
                                )
                                commentNumLiveData.value = data
                            } else {
                                LogUtil.e(TAG_COMMENT, "获取评论数失败！bid:$bid cid:$cid data is null")
                            }
                        }
                    } , chapterIds)
            }
        }
    }

    fun setPlayMode(mode: PlayMode) {
        LogUtil.d(DetailMC.TAG_PLAY_MODE, "setPlayMode: $mode, current:${_playMode.value}")
        if ((playMode.value == PlayMode.PIP && mode != PlayMode.NORMAL)) {
            return
        }
        keepImmersive = mode == PlayMode.IMMERSIVE
        previousPlayMode = _playMode.value
        _playMode.value = mode
    }

    fun isInPip(): Boolean = playMode.value == PlayMode.PIP

    /**
     * 跳过广告，获取下一个视频在列表中的位置
     * @param startPosition Int，遍历的起始位置index
     */
    fun getNextUnlockVideoChapterIndex(startPosition: Int): Int {
        if (videoList.isEmpty() || startPosition !in 0 until videoList.size)
            return startPosition
        // 如果是最后一集直接返回
        if (startPosition == videoList.lastIndex) return startPosition + 1
        for (i in (startPosition + 1) until videoList.size) {
            val item = videoList[i]
            if (item.isVideo()) {  // 找到第一个视频立即返回结果
//                return if (item.isCharge != DetailMC.CHAPTER_STATUS_PAY && item.isCharge != DetailMC.CHAPTER_STATUS_TOMORROW) i else startPosition
                val unsupportedType = listOf(
                    DetailMC.CHAPTER_STATUS_PAY,
                    DetailMC.CHAPTER_STATUS_TOMORROW,
                    DetailMC.CHAPTER_STATUS_PREVIEW
                )
                return if (item.isCharge !in unsupportedType) i else startPosition
            }
        }
        return startPosition
    }

    /**
     * 不重启Activity的情况下，更新当前在播的剧
     * 适用于小窗播放模式下
     * @param newIntent VideoListIntent
     */
    fun changeVideo(newIntent: VideoListIntent) {
        routeIntent = newIntent
        // 恢复初始状态
        init = false
        initBookInfo = false
        is1131Loading = false
        mPosition = 0
        saveRvAllCells = null
        savePosition = 0
        saveDuration = 0L
        saveChapterIndex = 0
        saveChapterId = null
        currentChapterIndex = null
        mChapterInfoVo = null
        mVideoInfo = null
        clearPause()
        // 发通知
        sendEvent(DetailMC.EVENT_CHANGE_VIDEO)
    }

}