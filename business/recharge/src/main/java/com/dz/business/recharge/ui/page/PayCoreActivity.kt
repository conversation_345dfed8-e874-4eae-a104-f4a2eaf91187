package com.dz.business.recharge.ui.page

import android.content.Intent
import androidx.lifecycle.LifecycleOwner
import com.dz.business.base.personal.PersonalMS
import com.dz.business.base.recharge.RechargeMR
import com.dz.business.base.recharge.intent.PayIntent
import com.dz.business.base.ui.BaseActivity
import com.dz.business.recharge.databinding.RechargePayCoreActivityBinding
import com.dz.business.recharge.vm.PayCoreVM
import com.dz.business.recharge.vm.PayCoreVM.Companion.STATUS_CREATE_ORDER_FAILED
import com.dz.business.recharge.vm.PayCoreVM.Companion.STATUS_PAY_FAILED
import com.dz.business.recharge.vm.PayCoreVM.Companion.STATUS_PAY_SUCCESS
import com.dz.business.recharge.vm.PayCoreVM.Companion.STATUS_QUERY_FAILED
import com.dz.business.recharge.vm.PayCoreVM.Companion.STATUS_RESULT_NOT_FOUND
import com.dz.business.recharge.vm.TAG
import com.dz.foundation.base.manager.task.TaskManager
import com.dz.foundation.base.utils.DeviceInfoUtil
import com.dz.foundation.base.utils.LogUtil
import com.dz.platform.pay.base.data.PayResult
import com.dz.platform.pay.paycore.PayCoreME

/**
 *@Author: zhanggy
 *@Date: 2023-03-20
 *@Description: 负责下单、调起三方支付、查询支付结果的 loading 页面
 *@Version:1.0
 */
class PayCoreActivity : BaseActivity<RechargePayCoreActivityBinding, PayCoreVM>() {

    private var lastResumeTime: Long = 0L
    private var lastPauseTime: Long = 0L

    override fun initImmersionBar() {
        getImmersionBar()
            .transparentStatusBar()
            .transparentNavigationBar()
//            .statusBarColor(com.dz.platform.common.R.color.common_card_FFFFFFFF)
            .statusBarDarkFont(!DeviceInfoUtil.isDarkTheme(this))
            .fitsSystemWindows(true)
            .init()
    }

    override fun onResume() {
        super.onResume()
        lastResumeTime = System.currentTimeMillis()
        LogUtil.i(TAG, "PayCoreActivity onResume: $lastResumeTime")
        mViewModel.onPageResume(lastPauseTime)
    }

    override fun onPause() {
        super.onPause()
        lastPauseTime = System.currentTimeMillis()
        LogUtil.i(TAG, "PayCoreActivity onPause: $lastPauseTime")
        mViewModel.onPagePause(lastResumeTime)
    }

    override fun initData() {
        mViewModel.init()
        // 下单，开始走流程
        mViewModel.createOrder()
    }

    override fun initView() {

    }

    override fun initListener() {

    }

    override fun subscribeObserver(lifecycleOwner: LifecycleOwner) {
        super.subscribeObserver(lifecycleOwner)
        mViewModel.status.observe(lifecycleOwner) {
            when (it) {
                STATUS_CREATE_ORDER_FAILED,
                STATUS_QUERY_FAILED,
                STATUS_PAY_FAILED,
                STATUS_RESULT_NOT_FOUND,
                STATUS_PAY_SUCCESS -> {
                    close()
                }
            }
        }
    }

    private fun close() {
        finish()
        TaskManager.delayTask(200) {
            if (mViewModel.payResult != null && getRouteIntent() is PayIntent) {
                (getRouteIntent() as PayIntent).callback?.onResult(
                    mViewModel.payResult!!.resultCode,
                    mViewModel.payResult!!.message ?: "",
                    mViewModel.thirdPay?.platform()?:"",
                    mViewModel.orderInfo?.orderNum
                )
            }
        }
    }

    override fun subscribeEvent(lifecycleOwner: LifecycleOwner, lifecycleTag: String) {
        super.subscribeEvent(lifecycleOwner, lifecycleTag)
        // 监听微信支付的结果
        PayCoreME.get().onWxOnResp().observe(lifecycleOwner) {
            mViewModel.cancelDelayClose()
            it?.run {
                if (isWxPay == true) {
                    // 0成功，-1错误, -2用户取消
                    when (errCode) {
                        0 -> {
                            mViewModel.startQueryResultLoop()
                        }
                        -2 -> {
                            mViewModel.payResult = PayResult(PayResult.RESULT_CANCEL, getMsg())
                            mViewModel.status.value = STATUS_PAY_FAILED
                        }
                        else -> {
                            mViewModel.payResult = PayResult(PayResult.RESULT_PAY_FAIL, getMsg())
                            mViewModel.status.value = STATUS_PAY_FAILED
                        }
                    }
                }
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        mViewModel.cancelQueryPayResult()
    }

    override fun onBackPressAction() {
//        super.onBackPressAction()
    }

    override fun enterAnim(intent: Intent?) {
        overridePendingTransition(0, 0)
    }

    override fun exitAnim() {
        overridePendingTransition(0, 0)
    }
}