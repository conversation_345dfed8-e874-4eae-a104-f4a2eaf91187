package com.dz.business.home

import android.app.Activity
import android.os.Build
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import androidx.fragment.app.Fragment
import com.dz.business.DzDataRepository
import com.dz.business.base.data.BBaseKV
import com.dz.business.base.data.bean.CommonConfigBean
import com.dz.business.base.data.bean.FollowSourceType
import com.dz.business.base.data.bean.RecommendVideoInfo
import com.dz.business.base.data.bean.StrategyInfo
import com.dz.business.base.data.bean.TierPlaySourceVo
import com.dz.business.base.flutter.FlutterMS
import com.dz.business.base.home.AddSubtractLikesCallback
import com.dz.business.base.home.FavoriteCallback
import com.dz.business.base.home.HomeME
import com.dz.business.base.home.HomeMS
import com.dz.business.base.home.PlayEventReportCallback
import com.dz.business.base.home.RecommendReqCallback
import com.dz.business.base.load.DBHelper
import com.dz.business.base.network.BBaseNetWork
import com.dz.business.base.network.HttpResponseModel
import com.dz.business.base.network.RecommendRequest1113
import com.dz.business.base.personal.PersonalMR
import com.dz.business.base.ui.BaseLazyFragment
import com.dz.business.base.utils.ActionRecorder
import com.dz.business.base.utils.CommInfoUtil
import com.dz.business.base.utils.RequestPermissionUtil
import com.dz.business.home.network.HomeNetwork
import com.dz.business.home.repository.HomeRecommendRepository
import com.dz.business.home.repository.PlayerRepository
import com.dz.business.home.tencent.TencentVideoListUtil
import com.dz.business.home.ui.page.HomeFragment
import com.dz.business.home.ui.page.NewRecommendFragment
import com.dz.foundation.base.manager.task.TaskManager
import com.dz.foundation.base.utils.DateUtil
import com.dz.foundation.base.utils.LogUtil
import com.dz.foundation.base.utils.PermissionUtils
import com.dz.foundation.network.onEnd
import com.dz.foundation.network.onError
import com.dz.foundation.network.onResponse
import com.dz.foundation.network.onStart
import com.dz.platform.ad.AdManager
import com.dz.platform.common.toast.ToastManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch


/**
 * @Author: guyh
 * @Date: 2023/1/30 17:05
 * @Description:
 * @Version:1.0
 */
class HomeMSImpl : HomeMS {

    override fun saveConfig(isCold: Boolean, config: CommonConfigBean?) {
        // 腾讯二次剪辑视频配置
        TencentVideoListUtil.saveConfig(config?.funSwitchConf?.scvideo)
    }

    override fun getRecommendInfo(
        bookId: String,
        limit: Int,
        isLandScape: Boolean,
        callback: RecommendReqCallback<RecommendVideoInfo>,
    ) {
        HomeNetwork.get().getRecommendInfo().setParams(bookId, limit, isLandScape).onStart {
            callback.onStart()
        }.onResponse {
            callback.onSuccess(it.data)
        }.onError {
            callback.onFail(it)
        }.onEnd {
            callback.onEnd()
        }.doRequest()
    }

    override fun getRecommendList(apiParams: RecommendRequest1113.RecommendParam, isLoadMore: Boolean, callback: RecommendReqCallback<HttpResponseModel<RecommendVideoInfo>>) {
        HomeRecommendRepository.getRecommendFromNetAsync(apiParams, isLoadMore, callback)
    }


    override fun getHomeFragment(): Fragment {
        return HomeFragment()
    }

    /**
     * source：添加追剧来源，服务端打点用，
     */
    override fun addFavorite(
        bookId: String?,
        chapterId: String?,
        omap: StrategyInfo?,
        scene: String?,
        source: String?,
        callback: FavoriteCallback,
        tierPlaySourceVo: TierPlaySourceVo?,
        followSource: FollowSourceType?,
        pageId: String?,
    ) {
        if (callback.requesting) {
            //为了处理弱网测试，点击多次加书架，发起多个请求的问题
            return
        }
        callback.requesting = true
        bookId?.let {
            CoroutineScope(Dispatchers.IO).launch {
                val bookEntity = DzDataRepository.bookDao().queryByBid(bookId)
                var mOmp = StrategyInfo()
                if (bookEntity != null && !bookEntity.first_play_source.isNullOrEmpty()) {
                    if (omap != null) {
                        omap.scene = bookEntity.first_play_source
                        mOmp = omap
                    } else {
                        mOmp.scene = bookEntity.first_play_source
                    }
                } else {
                    if (omap != null) {
                        omap.scene = scene
                        mOmp = omap
                    } else {
                        mOmp.scene = scene
                    }
                }
                BBaseNetWork.get()
                    .addFavorites()
                    .setParams(bookId, chapterId, source, mOmp, tierPlaySourceVo, followSource)
                    .onStart { callback.onStart() }
                    .onResponse {
                        it.data?.run {
                            HomeME.get().refreshFavorite().post(Any())
                            if (status == 1) {
                                HomeME.get().addFavoriteSuccess()
                                    .post(bookId)
                                FlutterMS.get()?.sendEventToFlutter(
                                    "inBookShelf",
                                    mapOf("value" to true, "bookId" to bookId)
                                )
                                TaskManager.ioTask {
                                    DBHelper.insertOrUpdateHistory(bookId, true)
                                }
                            }
                            callback.onSuccess(this)
                            ActionRecorder.recordAction(ActionRecorder.ACTION_FAVORITE)
                        }
                    }.onError {
                        callback.onFail(it)
                        ToastManager.showToast(it.message)
                    }.onEnd {
                        callback.requesting = false
                    }.doRequest()
            }
        }
    }

    override fun addFavorites(
        bookIds: List<String>,
        scene: String?,
        source: String?,
        callback: FavoriteCallback,
        tierPlaySourceVo: TierPlaySourceVo?,
        followSource: FollowSourceType?,
        pageId: String?
    ) {
        if (callback.requesting) {
            //为了处理弱网测试，点击多次加书架，发起多个请求的问题
            return
        }
        if (bookIds.isEmpty()) {
            return
        }
        callback.requesting = true
        BBaseNetWork.get()
            .addFavorites()
            .massOperation(bookIds, source, tierPlaySourceVo, followSource?.source)
            .onStart { callback.onStart() }
            .onResponse {
                it.data?.run {
                    HomeME.get().refreshFavorite().post(Any())
                    if (status == 1) {
                        bookIds.forEach { bookId ->
                            HomeME.get().addFavoriteSuccess()
                                .post(bookId)
                            FlutterMS.get()?.sendEventToFlutter(
                                "inBookShelf",
                                mapOf("value" to true, "bookId" to bookId)
                            )
                            TaskManager.ioTask {
                                DBHelper.insertOrUpdateHistory(bookId, true)
                            }
                        }
                    }
                    callback.onSuccess(this)
                    ActionRecorder.recordAction(ActionRecorder.ACTION_FAVORITE)
                }
            }.onError {
                callback.onFail(it)
                ToastManager.showToast(it.message)
            }.onEnd {
                callback.requesting = false
            }.doRequest()
    }

    override fun deleteBooks(
        bookIds: List<String>,
        source: String,
        tierPlaySourceVo: TierPlaySourceVo?,
        callback: FavoriteCallback,
    ) {
        if (callback.requesting) {
            //为了处理弱网测试，点击多次加书架，发起多个请求的问题
            return
        }
        callback.requesting = true
        BBaseNetWork.get()
            .deleteFavorites()
            .setParams(bookIds, source, tierPlaySourceVo)
            .onResponse {
                it.data?.run {
                    HomeME.get().refreshFavorite().post(Any())
                    if (status == 1) {
                        HomeME.get().deleteFavoriteSuccess()
                            .post(bookIds)
                        for (bid in bookIds) {
                            FlutterMS.get()?.sendEventToFlutter(
                                "inBookShelf",
                                mapOf("value" to false, "bookId" to bid)
                            )
                            TaskManager.ioTask {
                                DBHelper.insertOrUpdateHistory(bid, false)
                            }
                        }
                    }
                    callback.onSuccess(this)
                    ActionRecorder.recordAction(ActionRecorder.ACTION_FAVORITE_CANCELED)
                }

            }.onError {
                callback.onFail(it)
                ToastManager.showToast(it.message)
            }.onEnd {
                callback.requesting = false
            }.doRequest()
    }

    override fun playEventReport(
        playletId: String?,
        playletName: String?,
        partId: String?,
        partNum: Int?,
        firstPlaySource: String?,
        playStatus: Int?,
        origin: String?,
        channelId: String?,
        channelName: String?,
        channelPos: Int?,
        columnId: String?,
        columnName: String?,
        columnPos: Int?,
        contentId: String?,
        contentPos: Int?,
        contentType: Int?,
        omap: StrategyInfo?,
        playScene: String?,
        playingDuration: String?,  // 播放时长
        tierPlaySource: TierPlaySourceVo?,
        callback: PlayEventReportCallback,
    ) {
        //过滤3秒内视频划走或视频错误自动跳到广告，不上报
        if (!(playletId?.startsWith("123") == true && playletId.length < 8)) {
            HomeNetwork.get()
                .playEventReport()
                .setParams(
                    playletId,
                    playletName,
                    partId,
                    partNum,
                    firstPlaySource,
                    playStatus,
                    origin,
                    channelId,
                    channelName,
                    channelPos,
                    columnId,
                    columnName,
                    columnPos,
                    contentId,
                    contentPos,
                    contentType,
                    omap,
                    playScene,
                    playingDuration,
                    tierPlaySource
                ).onStart {
                    callback.onStart()
                }.onResponse {
                    callback.onSuccess(it.data)
                }.onError { e ->
                    callback.onFail(e)
                }.onEnd {
                    callback.onEnd()
                }.doRequest()
        }
    }

    override fun addSubtractLikes(
        playletId: String?,
        playletName: String?,
        partId: String?,
        partNum: Int?,
        likesType: Int?,
        origin: String?,
        channelId: String?,
        channelName: String?,
        channelPos: Int?,
        columnId: String?,
        columnName: String?,
        columnPos: Int?,
        contentId: String?,
        contentPos: Int?,
        contentType: Int?,
        omap: StrategyInfo?,
        callback: AddSubtractLikesCallback,
        pageId: String?,
    ) {
        HomeNetwork.get()
            .addSubtractLikes()
            .setParams(
                playletId,
                playletName,
                partId,
                partNum,
                likesType,
                origin,
                channelId,
                channelName,
                channelPos,
                columnId,
                columnName,
                columnPos,
                contentId,
                contentPos,
                contentType,
                omap
            ).onStart {
                callback.onStart()
            }.onResponse {
                callback.onSuccess(it.data)
                if (likesType == 1) {
                    ActionRecorder.recordAction(ActionRecorder.ACTION_LIKE)
                }
            }.onError { e ->
                callback.onFail(e)
            }.onEnd {
                callback.onEnd()
            }.doRequest()
    }

    override fun checkPhonePermission(activity: Activity?, position: Int) {
//        val oaid = OaidUtil.getOaid()
        LogUtil.d("ImeiTag", "获取imei权限申请，是否有权限==" + BBaseKV.hasPhonePermission)
//        if (OaidUtil.isOaidEmpty(oaid) && !BBaseKV.hasPhonePermission) {
        if (!BBaseKV.hasPhonePermission) {
            val day = DateUtil.formatDateToDay()
            if (BBaseKV.phonePopDate != day) {
                BBaseKV.phonePopNum = 0
                BBaseKV.phonePopDate = day
            }
            LogUtil.d(
                "ImeiTag",
                "获取imei权限申请，phonePopNum==" + BBaseKV.phonePopNum
                        + "\n attFrequency==" + BBaseKV.attFrequency +
                        "\n position==" + position
                        + "\n attrBeginChapter==" + BBaseKV.attrBeginChapter
                        + "\n attrEndChapter==" + BBaseKV.attrEndChapter
                        + "\n popFrequency==" + BBaseKV.popFrequency
            )
            if (Build.VERSION_CODES.M <= Build.VERSION.SDK_INT
                && Build.VERSION.SDK_INT < Build.VERSION_CODES.Q
                && BBaseKV.phonePopNum < BBaseKV.attFrequency
                && position >= BBaseKV.attrBeginChapter //起始章节
                && position < BBaseKV.attrEndChapter//结束章节
                && (position - BBaseKV.attrBeginChapter).rem(BBaseKV.popFrequency) == 0 //展示频率
            ) {
                LogUtil.d(
                    "ImeiTag",
                    "满足条件开始申请权限"
                )
                BBaseKV.phonePopNum++
                activity?.let {
                    RequestPermissionUtil.checkPermission(
                        it,
                        BBaseKV.authDoc,
                        PermissionUtils.CODE_PHONE_REQUEST,
                        PermissionUtils.loadingPhoneList(),
                        BBaseKV.phonePermission,
                        object : RequestPermissionUtil.OnPermissionRequest {
                            override fun onPermissionGranted() {
                                BBaseKV.hasPhonePermission = true
                                var imei = CommInfoUtil.getImei()
                                LogUtil.d("ImeiTag", "同意权限后，设置imei==$imei")
                                AdManager.setImei(imei)
                            }

                            override fun onPermissionDenied() {
                                LogUtil.d("ImeiTag", "拒绝获取imei")
                                if (RequestPermissionUtil.isShowRequestPermission(
                                        it,
                                        PermissionUtils.loadingPhoneList()
                                    )
                                ) {
                                    BBaseKV.phonePermission = true
                                }
                            }

                            override fun onCustomApply() {
                                LogUtil.d("ImeiTag", "拉起自定义弹窗")
                                PersonalMR.get().permissionDialog()
                                    .apply {
                                        title = BBaseKV.authSettingTitle
                                        content =
                                            it.getString(R.string.bbase_allow_permissions_dec) + BBaseKV.authSettingDoc
                                    }.start()
                            }
                        }
                    )
                }
            }
//        } else if (OaidUtil.isOaidEmpty(oaid) && BBaseKV.hasPhonePermission) {
        } else if (BBaseKV.hasPhonePermission) {
            var imei = CommInfoUtil.getImei()
            LogUtil.d("ImeiTag", "有权限直接设置imei==$imei")
            AdManager.setImei(imei)
        }
    }

    override fun getRecommendFragment(): BaseLazyFragment<*, *> {
        return NewRecommendFragment().apply {
            arguments = Bundle().apply {
                putString("title", "推荐页面")
                //选中页面设置数据
                putSerializable("data", null)
            }
        }
    }

    override fun initPlayerPool() {
        PlayerRepository.initPlayerPool()
    }

    override fun preloadRecommendVideoView(activity: Activity?): View? {
        val inflater = LayoutInflater.from(activity)
        return inflater.inflate(R.layout.home_new_player_list_item, null)
    }

    override fun initTencentVideoSDK() {
        TencentVideoListUtil.initSDK()
    }
}