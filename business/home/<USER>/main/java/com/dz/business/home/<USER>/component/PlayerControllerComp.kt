package com.dz.business.home.ui.component

import BBaseME
import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.res.Configuration
import android.graphics.Rect
import android.graphics.Typeface
import android.os.Handler
import android.os.Looper
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.style.ForegroundColorSpan
import android.text.style.StyleSpan
import android.util.AttributeSet
import android.view.GestureDetector
import android.view.MotionEvent
import android.view.MotionEvent.ACTION_CANCEL
import android.view.MotionEvent.ACTION_DOWN
import android.view.MotionEvent.ACTION_MOVE
import android.view.MotionEvent.ACTION_UP
import android.view.MotionEvent.obtain
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.SeekBar
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.lifecycle.LifecycleOwner
import com.aliyun.thumbnail.ThumbnailBitmapInfo
import com.aliyun.thumbnail.ThumbnailHelper
import com.aliyun.thumbnail.ThumbnailHelper.OnPrepareListener
import com.aliyun.thumbnail.ThumbnailHelper.OnThumbnailGetListener
import com.dz.business.base.BBaseMC
import com.dz.business.base.BBaseMC.PLAY_MODE_NORMAL
import com.dz.business.base.BBaseMC.TAG_APP_LAUNCH
import com.dz.business.base.SpeedUtil
import com.dz.business.base.data.BBaseKV
import com.dz.business.base.data.BBaseKV.dramaListSource
import com.dz.business.base.data.bean.BaseBean
import com.dz.business.base.data.bean.BaseBookInfo
import com.dz.business.base.data.bean.ContinueWatchVo
import com.dz.business.base.data.bean.RecommendVideoInfo
import com.dz.business.base.data.bean.VideoInfoVo
import com.dz.business.base.home.HomeME
import com.dz.business.base.splash.SplashMS
import com.dz.business.base.track.TrackUtil
import com.dz.business.base.ui.player.PlayerRenderView
import com.dz.business.base.ui.player.listener.INewPlayerListener
import com.dz.business.base.ui.player.listener.IPlayerListener
import com.dz.business.base.utils.DescSpanUtil
import com.dz.business.base.utils.OCPCManager
import com.dz.business.base.video.VideoMC
import com.dz.business.base.video.VideoMC.TAG_PLAYER_SEEKBAR
import com.dz.business.base.video.VideoMS
import com.dz.business.base.view.BaseExpandTextView
import com.dz.business.home.R
import com.dz.business.home.databinding.HomePlayerControllerCompBinding
import com.dz.business.home.ui.layer.HomeFunctionPortLayer
import com.dz.business.track.events.DzTrackEvents
import com.dz.business.track.utis.ElementClickUtils
import com.dz.business.video.danmu.VideoDanMuManager
import com.dz.business.video.enums.GestureType
import com.dz.business.video.ui.HorizontalSwipeInterceptView
import com.dz.business.video.utils.FunctionIconManager.getFavoriteNum2
import com.dz.foundation.base.utils.DateUtil
import com.dz.foundation.base.utils.LogUtil
import com.dz.foundation.base.utils.ScreenUtil
import com.dz.foundation.base.utils.ViewUtils
import com.dz.foundation.base.utils.dp
import com.dz.foundation.base.utils.monitor.TimeMonitorManager
import com.dz.foundation.imageloader.load
import com.dz.foundation.imageloader.loadRoundBitmap
import com.dz.foundation.imageloader.loadRoundImg
import com.dz.foundation.ui.utils.AlphaUtil
import com.dz.foundation.ui.utils.click.ClickUtils
import com.dz.platform.common.base.ui.component.UIConstraintComponent
import com.dz.platform.common.router.SchemeRouter
import com.dz.platform.player.player.DzPlayer
import org.json.JSONObject
import kotlin.math.abs


/**
 * @Author: guyh
 * @Date: 2024/8/7
 * @Description: 多播首页推荐视频播放器的view承载布局
 * @Version:1.0
 */
class PlayerControllerComp(
    context: Context, attrs: AttributeSet? = null,
) : UIConstraintComponent<HomePlayerControllerCompBinding, BaseBean>(
    context, attrs
) {
    /**
     * 倍速播放前的播放速度
     */
    private var originalSpeed: Float = 1.0F
    var mCurPosition = -1

    val TAG = "RECOMMEND_VIDEO_VIEW_ITEM_COMP_TAG"

    /**
     * 播放器
     */
    private var mPlayer: PlayerRenderView? = null
    var mOnPlayerListener: INewPlayerListener? = null

    /**
     * 刷新数据listener
     */
    private var onGestureListener: OnGestureListener? = null

    /**
     * 手势监听器
     */
    private lateinit var mGestureDetector: GestureDetector

    /**
     * 是否允许触摸
     */
    private var isGestureEnable = true

    /**
     * 是否正在倍速播放
     */
    private var mIsSpeed = false

    /**
     * 是否水平
     */
    private var isInHorizontalGesture = false

    /**
     * 是否是暂停
     */
    var mIsPause = false

    /**
     * 在SeekBar触发热区下方的View列表
     * 使用setOnTouchListener方案来处理
     */
    private var viewsUnderSeekbarArea: List<View?>? = null

    /**
     * 在SeekBar触发热区下方的View列表
     * 使用HorizontalSwipeInterceptView方案来处理
     */
    private var viewsUnderSeekbarArea2: List<View?>? = null

    /***
     * 正在拖动
     */
    var isDragging: Boolean = false
    var playMode: Int = PLAY_MODE_NORMAL
    var videoInfoVo: BaseBookInfo? = null
    private var videoInfo: VideoInfoVo? = null //当前集的信息

    var introductionLines: Int? = null//简介展示行数
    var showCover: Int? = null//是否展示剧封 0不展示  1展示
    var showPerformer: Int? = null//是否展示演员 0不展示  1展示
    var showFree: Int = 0//是否展示永久免费 0不展示  1展示
    var showTags: Int = 0//是否展示题材标签 0不展示  1展示

    override fun initData() {

    }

    override fun initView() {
        LogUtil.d("SEEKBAR", "设置监听当前类名：" + context.javaClass.name)
        mPlayer = PlayerRenderView(context)
        mViewBinding.seekBar.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            var currentTime: Long = 0
            var duration: Long = 0
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    currentTime = duration * progress / 100
                    mViewBinding.tvCurrent.text = DateUtil.formatSeconds(currentTime / 1000)
                    if (((requestedTime ?: 0) / 1000 != currentTime / 1000)) {
                        requestedTime = currentTime
                        requestedBookId = videoInfo?.bookId
                        requestedChapterId = videoInfo?.chapterId
                        mThumbnailHelper?.requestBitmapAtPosition(currentTime)
                    }
                }
            }

            override fun onStartTrackingTouch(seekBar: SeekBar?) {
                if (!isGestureEnable) {
                    return
                }
                isDragging = true
                mViewBinding.clTime.visibility = VISIBLE
                mViewBinding.layoutInfo.visibility = GONE
                mViewBinding.layerFunction.visibility = GONE
                mViewBinding.rankBottom.setVisible(false)
                mViewBinding.recommendBottom.setVisible(false)
                mViewBinding.bookBottom.setVisible(false)
                duration = mPlayer?.getDuration() ?: 0
                val layoutParams = mViewBinding.seekBar.layoutParams
                layoutParams.height = 16.dp
                mViewBinding.seekBar.layoutParams = layoutParams
                mViewBinding.tvDuration.text = DateUtil.formatSeconds(duration / 1000)
                mViewBinding.seekBar.thumb =
                    ContextCompat.getDrawable(context, R.drawable.bbase_seekbar_btn)
                mViewBinding.seekBar.progressDrawable =
                    ContextCompat.getDrawable(context, R.drawable.bbase_seekbar_style)
                onGestureListener?.onStartTrackingTouch()
            }

            override fun onStopTrackingTouch(seekBar: SeekBar?) {
                if (!isGestureEnable) {
                    return
                }
                isDragging = false
                val layoutParams = mViewBinding.seekBar.layoutParams
                layoutParams.height = 7.dp
                mViewBinding.seekBar.layoutParams = layoutParams
                seekToPlay(currentTime)
                playIconVisibility(GONE)
                mViewBinding.clTime.visibility = GONE
                if (playMode == PLAY_MODE_NORMAL) {
                    mViewBinding.layoutInfo.visibility = VISIBLE
                    mViewBinding.layerFunction.visibility = VISIBLE
                    mViewBinding.rankBottom.setVisible(true)
                    mViewBinding.recommendBottom.setVisible(true)
                    mViewBinding.bookBottom.setVisible(true)
                }
                trackAppClick()
                mViewBinding.seekBar.thumb =
                    ContextCompat.getDrawable(context, R.drawable.bbase_seekbar_normal_btn)
                mViewBinding.seekBar.progressDrawable =
                    ContextCompat.getDrawable(context, R.drawable.bbase_seekbar_normal_style)
                onGestureListener?.onStopTrackingTouch(currentTime)
            }
        })

        setDanMuButton(VideoDanMuManager.isEnable() && VideoDanMuManager.isEnableLocal())
        initCoverView()
        if (VideoMS.get()?.expandSeekBarArea() == true) {
            mViewBinding.viewSpace.visibility = GONE
            mViewBinding.seekBarArea.visibility = VISIBLE
            // 所有SeekBar触发热区下的View要在这里注册，不然无法响应点击事件
            viewsUnderSeekbarArea = listOf(
                findViewById(R.id.tv_next),  // 看全集
                findViewById(R.id.btn_share),  // 分享按钮
                findViewById(R.id.btn_comment),  // 评论按钮
                findViewById(R.id.btn_danmu_right), // 右下角弹幕按钮
                findViewById(R.id.rank_bottom),  // 排行榜
                findViewById(R.id.recommend_bottom),  // 底部推荐
                findViewById(R.id.book_bottom),
            )
            viewsUnderSeekbarArea2 = listOf(
                findViewById(R.id.tv_desc_new),  // 新版本简介
//                findViewById(R.id.nsv_content),  // 新版本简介可滑动组建
                findViewById(R.id.tv_desc2),
                findViewById(R.id.tv_desc2_normal),
                findViewById(R.id.tv_desc3),
                findViewById(R.id.tv_desc_switch),  // 老版本简介的展开收起按钮
            )
        } else {
            mViewBinding.seekBarArea.visibility = GONE
            mViewBinding.viewSpace.visibility = VISIBLE
        }
    }

    private fun initCoverView() {
        // 修改封面的缩放模式
        val height = BBaseKV.recommendHeightWidthRatio * 9
        if (resources.configuration.orientation != Configuration.ORIENTATION_PORTRAIT) {
            // 横屏时候 封面不显示
            hideThumb()
        } else {
            showThumb()
            // 手机竖屏，老逻辑不变
            if (height == 0.0f || (height > 15 && height < 19.56)) {
                mViewBinding.imgThumb.scaleType = ImageView.ScaleType.CENTER_CROP// 裁剪
            } else {  // 留黑边，不进行裁剪
                mViewBinding.imgThumb.scaleType = ImageView.ScaleType.FIT_CENTER
            }
        }
    }

    /**
     * 倒计时提示 0-3秒显示倒计时，其他情况显示按钮名称
     * 开始播放 切换剧集 进度条同步
     */
    fun updateNextText(time: Long) {
    }

    /**
     * 设置除播放器外的view透明度
     */
    fun updateAlpha(alpha: Float) {
        AlphaUtil.setAlpha(mViewBinding.clViewRoot, alpha)
    }

    fun setData(
        context: Context,
        data: VideoInfoVo,
        showShare: Boolean,
        showNormalIcon: Boolean? = null,
        playerHeight: Int,
        isNewStyle: Boolean,
        continueWatchVo: ContinueWatchVo? = null
    ) {
        videoInfo = data
        data.run {
            //1、设置剧名
            setVideoName(this, true)
            //2、更新剧集标签
            setTags(this)
            //3、设置弹幕按钮
            setDanMuButton(VideoDanMuManager.isEnable() && VideoDanMuManager.isEnableLocal())
            //4、设置再追状态
            favoriteStatus(inBookShelf == true, getFavoriteNum2())
            //5、设置分享状态
            shareStatus(showShare, showNormalIcon, getShareNum())
            //6、设置排行榜按钮/推荐理由信息
            setBottomTips(this)
            //7、设置集信息
            setVideoIndex(
                if (finishStatus == 0) {
                    if (data.videoLinkType == 2) {
                        "更新至${updateNum}集"
                    } else {
                        "第${chapterIndex}集·更新至${updateNum}集"
                    }
                } else {
                    if (data.videoLinkType == 2) {
                        "全${updateNum}集"
                    } else {
                        "第${chapterIndex}集·全${updateNum}集"
                    }
                }, introduction,
                data.videoLinkType == 2
            )
            //8、设置封面
            loadCover(context, chapterImg)
            //9、设置视频
            handleLandVideo(this, playerHeight)
            //10、初始化备案号显隐
            initSetRegisterNumber()
            //11、设置备案号
            setRecordNumber(this)
            //12、隐藏暂停按钮
            playIconVisibility(View.GONE)
            //13、设置评论数
            setCommentNum(this)
        }
    }

    private fun initSetRegisterNumber()  //初始化页面的时候进行一次隐藏防止出现页面复用导致的备案号多次出现
    {
        mViewBinding.registerNumber.visibility = GONE
        mViewBinding.registerNumberLand.visibility = GONE
    }

    private fun loadCover(context: Context, coverUrl: String?) {
        if (resources.configuration.orientation != Configuration.ORIENTATION_PORTRAIT) {
            // 横屏时候 封面不显示
            hideThumb()
        } else {
            showThumb()
        }
        if (context is Activity) {
            if (!context.isFinishing || !context.isDestroyed) {
                coverUrl?.let { mViewBinding.imgThumb.load(it) }
            }
        }
    }

    fun setTags(data: VideoInfoVo) {
        if (BBaseKV.isFirstShowPlotOcpcTips && OCPCManager.getPlotOcpcBookId() == data.bookId) {
            tvPlotTagVisibility(VISIBLE, OCPCManager.getPlotOcpcConfig()?.getTip(), data)
        } else if (data.isPayVideo()) {
            icTagsVisibility(VISIBLE, R.drawable.bbase_ic_pay)
        } else if (data.isNewVideo()) {
            icTagsVisibility(VISIBLE, R.drawable.bbase_ic_new_video)
        } else if (data.isPermanentFree()) {
            icFreeVisibility(VISIBLE, R.drawable.bbase_ic_permanent_free)
        } else {
            icTagsVisibility(GONE)
            icFreeVisibility(GONE)
            tvPlotTagVisibility(GONE)
        }
    }

    //打点
    private fun trackAppClick() {
        kotlin.runCatching {
            videoInfoVo?.let {
                val jsonObject = JSONObject().apply {
                    put("\$element_type", "拖动进度条")
                    put("\$element_content", "拖动进度条")
                    put("\$element_position", "首页")
                    put("BookID", videoInfoVo?.bookId ?: "")
                    put("BookName", videoInfoVo?.bookName ?: "")
                    put("ChaptersID", videoInfoVo?.chapterId ?: "")
                    put("ChaptersNum", videoInfoVo?.chapterIndex ?: 0)
                    put("PositionName", "首页")
                    put(
                        "IsPreview",
                        !<EMAIL>?.content?.spriteImg.isNullOrBlank()
                    )
                }
                TrackUtil.track("\$AppClick", jsonObject)
            }
        }.onFailure {
            it.printStackTrace()
        }
    }

    fun setDanMuButton(enable: Boolean) {
        mViewBinding.layerFunction.updateDanmu()
        if (enable && VideoDanMuManager.isEnableLocal()) {
            mViewBinding.ivDanmu.visibility = VISIBLE
        } else {
            mViewBinding.ivDanmu.visibility = GONE
        }
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun initListener() {
        if (VideoMS.get()?.expandSeekBarArea() == true) {
            // 为SeekBar滑动热区的View设置监听
            viewsUnderSeekbarArea?.forEach { it?.let(::interceptTouchEvent) }

            mViewBinding.seekBarArea.callback =
                object : HorizontalSwipeInterceptView.SwipeCallback {
                    override fun onActionMove(event: MotionEvent) {
                        onSeekBarAreaMove(event)
                    }

                    override fun onClick(event: MotionEvent) {
                        if (!onSeekBarAreaClick(event)) {
                            onGestureListener?.onSingleTapConfirmed()
                        }
                    }

                    override fun onDoubleClick(event: MotionEvent) {
                        onGestureListener?.onDoubleTap()
                    }

                    override fun onLongPress(event: MotionEvent) {
                        onGestureListener?.onLongClick()
                    }
                }
        } else {
            seekBarPerformTouchEvent(mViewBinding.viewSpace)
        }
        //手势监听器
        mGestureDetector =
            GestureDetector(context, object : GestureDetector.SimpleOnGestureListener() {
                override fun onSingleTapConfirmed(e: MotionEvent): Boolean {
                    if (!ClickUtils.isCanClick(R.id.bbase_single_tap_confirmed_id)) {
                        return true
                    }
                    onGestureListener?.onSingleTapConfirmed()
                    return true
                }

                override fun onDoubleTap(e: MotionEvent): Boolean {
                    onGestureListener?.onDoubleTap()
                    return true
                }

                override fun onLongPress(e: MotionEvent) {
                    super.onLongPress(e)
                    if (!isGestureEnable || mIsPause || isDragging) {
                        return
                    }
                    mViewBinding.seekBar.isEnabled = false
                    onGestureListener?.onLongClick()
                    mIsSpeed = true
                    originalSpeed = mPlayer?.getSpeed() ?: 1f
                    mPlayer?.setSpeed(2f)
                    mViewBinding.layoutInfo.visibility = GONE
                    mViewBinding.layerFunction.visibility = GONE
                    mViewBinding.rankBottom.setVisible(false)
                    mViewBinding.recommendBottom.setVisible(false)
                    mViewBinding.bookBottom.setVisible(false)
                    mViewBinding.llSpeed.visibility = VISIBLE
                }

                override fun onDown(e: MotionEvent): Boolean {
                    return true
                }

                override fun onScroll(
                    e1: MotionEvent,
                    e2: MotionEvent,
                    distanceX: Float,
                    distanceY: Float,
                ): Boolean {
                    //如果关闭了手势。则不处理。
                    if (!isGestureEnable) {
                        return false
                    }
                    if (abs(distanceX) > abs(distanceY)) {
                        //水平滑动
                        isInHorizontalGesture = true
                    }
                    if (isInHorizontalGesture) {
                        onGestureListener?.onHorizontalDistance(e1.x, e2.x)
                    }
                    return true
                }

            })

        //播放列表界面的touch事件由手势监听器处理
        mViewBinding.root.setOnTouchListener { _, motionEvent ->
            when (motionEvent.action) {
                ACTION_UP, ACTION_CANCEL -> {
                    if (!isDragging) {
                        //对结束事件的监听
                        mViewBinding.seekBar.isEnabled = true
                        onGestureListener?.onGestureEnd(
                            if (mIsSpeed) GestureType.LONG_PRESS else GestureType.UNKNOWN
                        )
                        if (mIsSpeed) {
                            mIsSpeed = false
                            mPlayer?.setSpeed(originalSpeed)
                            if (playMode == PLAY_MODE_NORMAL) {
                                mViewBinding.layoutInfo.visibility = VISIBLE
                                mViewBinding.layerFunction.visibility = VISIBLE
                                mViewBinding.rankBottom.setVisible(true)
                                mViewBinding.recommendBottom.setVisible(true)
                                mViewBinding.bookBottom.setVisible(true)
                            }
                            mViewBinding.llSpeed.visibility = GONE
                        }
                    }
                }

                else -> {}
            }
            mGestureDetector.onTouchEvent(motionEvent)
        }
        //下一集点击
        mViewBinding.tvNext.registerClickAction {
            onGestureListener?.onNextDramaClick(mViewBinding.tvNext.text.toString())
        }
        mViewBinding.viewSpace.registerClickAction {

        }
        ElementClickUtils.ignoreAutoTrack(mViewBinding.tvActorLeft)
        mViewBinding.tvActorLeft.registerClickAction {
            val performerIds = mutableListOf<String>()
            var performerName: String? = null
            var performNum: Int? = null
            if ((videoInfo?.performerList?.size ?: 0) == 1) {
                videoInfo?.performerList?.get(0)?.performerId?.let {
                    performerIds.add(it)
                }
                performerName = videoInfoVo?.performerList?.get(0)?.performerName
                performNum = videoInfoVo?.performerList?.get(0)?.performNum
            } else if ((videoInfo?.performerList?.size ?: 0) > 1) {
                videoInfo?.performerList?.get(0)?.performerId?.let {
                    performerIds.add(it)
                }
                videoInfo?.performerList?.get(1)?.performerId?.let {
                    performerIds.add(it)
                }
                performerName = videoInfoVo?.performerList?.get(0)?.performerName
                performNum = videoInfoVo?.performerList?.get(0)?.performNum
            }
            onGestureListener?.onActorClick(
                mViewBinding.tvActorLeft,
                performerIds,
                performerName,
                performNum
            )
        }
        ElementClickUtils.ignoreAutoTrack(mViewBinding.tvActorRight)
        mViewBinding.tvActorRight.registerClickAction {
            val performerIds = mutableListOf<String>()
            var performerName: String? = null
            var performNum: Int? = null
            if ((videoInfo?.performerList?.size ?: 0) == 1) {
                videoInfo?.performerList?.get(0)?.performerId?.let {
                    performerIds.add(it)
                }
                performerName = videoInfoVo?.performerList?.get(0)?.performerName
                performNum = videoInfoVo?.performerList?.get(0)?.performNum
            } else if ((videoInfo?.performerList?.size ?: 0) > 1) {
                videoInfo?.performerList?.get(1)?.performerId?.let {
                    performerIds.add(it)
                }
                videoInfo?.performerList?.get(0)?.performerId?.let {
                    performerIds.add(it)
                }
                performerName = videoInfoVo?.performerList?.get(1)?.performerName
                performNum = videoInfoVo?.performerList?.get(1)?.performNum
            }
            onGestureListener?.onActorClick(
                mViewBinding.tvActorRight,
                performerIds,
                performerName,
                performNum
            )
        }
        ElementClickUtils.ignoreAutoTrack(mViewBinding.tvTag)
        mViewBinding.tvTag.registerClickAction {
            videoInfo?.showTagDeepLink?.let {
                onGestureListener?.onTagClick(it, mViewBinding.tvTag.text.toString())
            }
        }
        // 剧名点击，去详情
        ElementClickUtils.ignoreAutoTrack(mViewBinding.tvNameOld)
        mViewBinding.tvNameOld.registerClickAction {
            onGestureListener?.onBookNameClick(it, videoInfo?.bookName)
        }
        ElementClickUtils.ignoreAutoTrack(mViewBinding.tvNameNew)
        mViewBinding.tvNameNew.registerClickAction {
            onGestureListener?.onBookNameClick(it, videoInfo?.bookName)
        }
        ElementClickUtils.ignoreAutoTrack(mViewBinding.ivBookCover)
        mViewBinding.ivBookCover.registerClickAction {
            onGestureListener?.onBookNameClick(it, "剧封")
        }
        ElementClickUtils.ignoreAutoTrack(mViewBinding.tvDesc2)
        mViewBinding.tvDesc2.registerClickAction {
            descSwitchClick()
        }
        ElementClickUtils.ignoreAutoTrack(mViewBinding.tvDesc3)
        mViewBinding.tvDesc3.registerClickAction {
            descSwitchClick()
        }
        ElementClickUtils.ignoreAutoTrack(mViewBinding.tvCurrentDrama)
        mViewBinding.tvCurrentDrama.registerClickAction {
            descSwitchClick()
        }
        ElementClickUtils.ignoreAutoTrack(mViewBinding.tvDescSwitch)
        mViewBinding.tvDescSwitch.registerClickAction {
            descSwitchClick()
        }

        mViewBinding.ivDanmu.registerClickAction {
            onGestureListener?.onDanMuClick()
        }


        mViewBinding.tvDescNew.getScrollView().setOnClickListener {
            // 注意：这里不要用registerClickAction。因为registerClickAction有防连点处理，产品认为响应太慢。
            if (videoInfo?.introduction.isNullOrEmpty()) return@setOnClickListener
            mViewBinding.tvDescNew.toggle()  // 切换展开收起状态
            onGestureListener?.onDescSwitchClick()
        }
    }

    private fun descSwitchClick() {
        onGestureListener?.onDescSwitchClick()
        DescSpanUtil.setDescAndSwitch(
            mViewBinding.tvDesc2,
            mViewBinding.tvDesc3,
            mViewBinding.tvDescSwitch,
            descList
        )
    }

    /**
     * 判断View在界面上是否可见
     * @param view View
     * @return Boolean
     */
    private fun isViewVisible(view: View): Boolean {
        val rect = Rect()
        view.getGlobalVisibleRect(rect)
        return rect.width() > 0 && rect.height() > 0
    }


    var isSingleTap: Boolean = true
    private fun seekBarPerformTouchEvent(view: View) {
        view.setOnTouchListener(object : OnTouchListener {
            override fun onTouch(v: View, event: MotionEvent): Boolean {
                when (event.action) {
                    ACTION_DOWN -> {
                        downX = event.x
                        xAxisPosition =
                            (mViewBinding.seekBar.progress * (mViewBinding.seekBar.width - 24.dp) / mViewBinding.seekBar.max)
                        actionDownTime = System.currentTimeMillis()
                        seekBarTakeOver = false
                        isSingleTap = true
                    }

                    ACTION_MOVE -> {
                        return if ((System.currentTimeMillis() - actionDownTime > 0) && (abs(
                                event.x - downX
                            ) > 10) && !mIsSpeed
                        ) {
                            isSingleTap = false
                            seekBarActionMove(event)
                        } else {
                            isSingleTap = true
                            false
                        }
                    }

                    ACTION_UP -> {
                        if (isSingleTap && mViewBinding.rankBottom.visibility == VISIBLE && !mViewBinding.rankBottom.mData?.rankAction.isNullOrEmpty()) {
                            SchemeRouter.doUriJump(mViewBinding.rankBottom.mData?.rankAction)
                        }
                        if (mViewBinding.continueWatchBgg.visibility == VISIBLE) {
                            SchemeRouter.doUriJump(mViewBinding.continueWatchBgg.url)
                        }
                        if (isSingleTap && mViewBinding.bookBottom.visibility == VISIBLE && !mViewBinding.bookBottom.mData?.playListAction.isNullOrEmpty()) {
                            trackAppClick(
                                mViewBinding.bookBottom.mData,
                                "${mViewBinding.bookBottom.mData?.playListTitle}",
                                "剧单",
                                "首页"
                            )
                            LogUtil.d(
                                TAG_APP_LAUNCH,
                                "mViewBinding.bookBottom.mData?.playListAction:${mViewBinding.bookBottom.mData?.playListAction}"
                            )
                            dramaListSource = true
                            SchemeRouter.doUriJump(mViewBinding.bookBottom.mData?.playListAction)
                        } else {
                            return seekBarActionUp(event)
                        }
                    }

                    ACTION_CANCEL -> {
                        seekBarActionCancel()
                    }
                }
                return false
            }
        })
    }

    fun interceptTouchEvent(view: View) {
        view.setOnTouchListener(object : OnTouchListener {
            // 用于判断是否为点击动作
            private var isTap = false

            // 记录按下时的时间戳
            private var touchStartTime = 0L

            // 判断是否移动的阈值
            private val CLICK_ACTION_THRESHOLD = 5
            override fun onTouch(v: View, event: MotionEvent): Boolean {
                when (event.action) {
                    ACTION_DOWN -> {
                        downX = event.x
                        xAxisPosition =
                            (mViewBinding.seekBar.progress * (mViewBinding.seekBar.width - 24.dp) / mViewBinding.seekBar.max)
                        actionDownTime = System.currentTimeMillis()
                        seekBarTakeOver = false
                        isTap = true
                        touchStartTime = System.currentTimeMillis()
                    }

                    ACTION_MOVE -> {
                        // 如果移动距离超过阈值，则不再视为点击
                        if (abs(event.x - downX) > CLICK_ACTION_THRESHOLD) {
                            isTap = false
                        }
                        return if ((abs(event.x - downX) > 10) && !mIsSpeed) {
                            seekBarActionMove(event)
                        } else {
                            false
                        }
                    }

                    ACTION_UP -> {
                        if (!isDragging) {
                            //对结束事件的监听
                            mViewBinding.seekBar.isEnabled = true
                            if (mIsSpeed) {
                                mIsSpeed = false
                                setSpeed(originalSpeed)
                                mViewBinding.llSpeed.visibility = GONE
                            }
                        }

                        // 判断是否为点击操作
                        val touchEndTime = System.currentTimeMillis()
                        val touchDuration = touchEndTime - touchStartTime

                        if (isTap && touchDuration < 200 && abs(event.x - downX) < CLICK_ACTION_THRESHOLD) {
                            // 模拟点击事件
                            v.performClick()
                            return true
                        }

                        return seekBarActionUp(event)
                    }

                    ACTION_CANCEL -> {
                        isTap = false
                        seekBarActionCancel()
                    }
                }
                return true
            }
        })
    }

    private fun trackAppClick(
        data: BaseBookInfo?,
        elementContent: String,
        elementType: String,
        elementPosition: String
    ) {
        kotlin.runCatching {
            LogUtil.d("trackAppClick", "elementContent：“$elementContent")
            val jsonObject = JSONObject().apply {
                put("\$element_content", elementContent)
                put("\$element_type", elementType)
                put("\$element_position", elementPosition)
                put("BookID", data?.bookId)
                put("BookName", data?.bookName)
            }
            TrackUtil.track("\$AppClick", jsonObject)
        }.onFailure {
            it.printStackTrace()
        }
    }

    override fun subscribeEvent(lifecycleOwner: LifecycleOwner, lifecycleTag: String) {
        super.subscribeEvent(lifecycleOwner, lifecycleTag)
        BBaseME.get().onActionEvent().observe(lifecycleOwner, lifecycleTag) { seekBarEvent ->
            LogUtil.d("SEEKBAR", "设置监听当前类名：" + context.javaClass.name)
            if (isGestureEnable && !mIsSpeed && seekBarEvent.contextName == context.javaClass.name) {
                mainBarMoveEvent(seekBarEvent.motionEvent)
            }
        }


        BBaseME.get().onDanMuEnableChanged().observeForever(lifecycleTag) { enable ->
            setDanMuButton(enable)
        }

        BBaseME.get().onDanMuEnableLocalChanged().observeForever(lifecycleTag) { enable ->
            setDanMuButton(enable)
        }
    }

    /**
     * 收藏成功的动画
     */
    fun playFavoriteAnimation() {
        mViewBinding.layerFunction.playFavoriteAnimation()
    }

    private fun seekBarActionMove(event: MotionEvent): Boolean {
        seekBarTakeOver = true
        moveX = xAxisPosition + (event.x - downX)
        if (moveX < -BBaseMC.TOUCH_SLOP) {
            downX = xAxisPosition + event.x
            moveX = 0f
        } else if (moveX > (mViewBinding.seekBar.width - 12.dp)) {
            downX = xAxisPosition + event.x - (mViewBinding.seekBar.width - 12.dp).toFloat()
            moveX = (mViewBinding.seekBar.width - 12.dp).toFloat()
        }
        return mViewBinding.seekBar.onTouchEvent(
            obtain(
                event.downTime,
                event.eventTime, event.action,
                moveX,
                event.y,
                event.metaState
            )
        )
    }

    private fun seekBarActionUp(event: MotionEvent): Boolean {
        if (seekBarTakeOver) {
            actionDownTime = 0L
            seekBarTakeOver = false
            loadX = false
            downX = 0f
            xAxisPosition = 0
            mViewBinding.seekBar.onTouchEvent(
                obtain(
                    event.downTime,
                    event.eventTime, event.action,
                    moveX,
                    event.y,
                    event.metaState
                )
            )
            return true
        } else {
            return false
        }
    }

    private fun seekBarActionCancel() {
        actionDownTime = 0L
        seekBarTakeOver = false
        loadX = false
        downX = 0f
        moveX = 0f
        xAxisPosition = 0
    }


    // 首页的底部滑动，响应为相对滑动
    private var actionDownTime = 0L
    private var seekBarTakeOver = false
    private var downX = 0f //点击位置
    private var moveX = 0f //移动位置
    private var xAxisPosition = 0 //进度条位置
    private var loadX = false //记录不触发 ACTION_DOWN 时候的位置
    private fun mainBarMoveEvent(event: MotionEvent) {
        when (event.action) {
            ACTION_MOVE -> {
                if (!loadX) {
                    downX = event.x
                    xAxisPosition =
                        (mViewBinding.seekBar.progress * (mViewBinding.seekBar.width - 24.dp) / mViewBinding.seekBar.max)
                    actionDownTime = System.currentTimeMillis()
                    loadX = true
                }
                if ((System.currentTimeMillis() - actionDownTime > 0) && (abs(event.x - downX) > 10) && !mIsSpeed) {
                    seekBarActionMove(event)
                }
            }

            ACTION_UP -> {
                seekBarActionUp(event)
            }

            ACTION_CANCEL -> {
                seekBarActionCancel()
            }
        }
    }

    /**
     * 剧图片标签显示隐藏
     */
    private fun tvPlotTagVisibility(
        viewStatus: Int,
        text: String? = null,
        data: VideoInfoVo? = null
    ) {
        if (viewStatus == VISIBLE) {
            icTagsVisibility(GONE)
            icFreeVisibility(GONE)
            if (mViewBinding.tvPlot.visibility == GONE) {
                DzTrackEvents.get().operationExposureTE().apply {
                    operationID("首页描述弹窗曝光")
                    bookName(data?.bookName)
                    bookId(data?.bookId)
                }.track()
            }
        }
        mViewBinding.tvPlot.visibility = viewStatus
        text?.let {
            mViewBinding.tvPlot.text = it
        } ?: run {
            mViewBinding.tvPlot.visibility = GONE
        }
    }

    /**
     * 剧图片标签显示隐藏
     */
    fun icTagsVisibility(viewStatus: Int, resource: Int = 0) {
        if (viewStatus == VISIBLE) {
            icFreeVisibility(GONE)
            tvPlotTagVisibility(GONE)
        }
        mViewBinding.ivTags.visibility = viewStatus
        if (resource != 0) {
            mViewBinding.ivTags.load(resource)
        }
    }

    private var isLandRecordNumber = false  //isLandRecordNumber是否是横版剧  true：横版剧；false：竖版剧

    /**
     * 设置备案号
     */
    fun setRecordNumber(videoInfoVo: VideoInfoVo) {
        videoInfoVo.recordNumber?.let {
            if (videoInfoVo.content?.pressBeianhao == true) {
                mViewBinding.registerNumber.text = it
                mViewBinding.registerNumberLand.text = it
            } else {
                mViewBinding.registerNumber.text = ""
                mViewBinding.registerNumberLand.text = ""
            }
        }
    }

    /**
     * 根据时间显示备案号
     */
    fun showRecordNumberByTime(time: Long) {
        if (isLandRecordNumber) {//横版剧备案号view
            mViewBinding.registerNumberLand
        } else {//竖版剧屏备案号view
            mViewBinding.registerNumber
        }.visibility = if (time > 5000 || time < 0) {
            GONE
        } else {
            VISIBLE
        }
    }

    /**
     * 永久免费图标显示
     */
    fun icFreeVisibility(viewStatus: Int, resource: Int = 0) {
        if (showFree == 1) {
            if (viewStatus == VISIBLE) {
                icTagsVisibility(GONE)
                tvPlotTagVisibility(GONE)
            }
            mViewBinding.ivFree.visibility = viewStatus
            if (resource != 0) {
                mViewBinding.ivFree.load(resource)
            }
        } else {
            icTagsVisibility(GONE)
            tvPlotTagVisibility(GONE)
            mViewBinding.ivFree.visibility = GONE
        }
    }

    /**
     * 开启关闭手势控制。
     * @param enable  开启
     */
    fun enableGesture(enable: Boolean) {
        this.isGestureEnable = enable
    }

    /**
     * 获取TextureView容器
     */
    fun getTextureViewRoot(): FrameLayout {
        return mViewBinding.rootTextureview
    }

    /**
     * 设置进度条进度
     */
    fun seekBarSeekTo(progress: Int) {
        if (!isDragging) {//正在拖动，暂停更新进度条
            mViewBinding.seekBar.progress = progress
        }
    }


    /**
     * 更改播放/暂停图标
     */
    fun playIconVisibility(viewStatus: Int) {
        mViewBinding.ivPlayIcon.visibility = viewStatus
    }

    fun coverVisibility(viewStatus: Int) {
        mViewBinding.imgThumb.visibility = viewStatus
    }

    fun hideThumb() {
        mViewBinding.imgThumb.visibility = View.GONE
    }

    fun showThumb() {
        mViewBinding.imgThumb.visibility = View.VISIBLE
    }

    /**
     * 分享状态
     * @param showNormalIcon 是否显示常规分享图标
     * @param tip 分享的数据
     */
    fun shareStatus(showShare: Boolean, showNormalIcon: Boolean? = null, tip: String? = "分享") {
        mViewBinding.layerFunction.shareStatus(showShare, showNormalIcon, tip)
    }

    /**
     * 收藏状态
     */
    fun favoriteStatus(favorite: Boolean, tip: String?) {
        mViewBinding.layerFunction.favoriteStatus(favorite, tip)
    }

    /**
     * 点赞状态
     */
    fun likesStatus(isLikes: Boolean, tip: String?) {
        mViewBinding.layerFunction.likesStatus(isLikes, tip)
    }

    /**
     * 设置评论数
     */
    fun setCommentNum(videoInfoVo: VideoInfoVo) {
        mViewBinding.layerFunction.setCommentNum(videoInfoVo.curChapterCommentNum)
    }

    /**
     * 当前剧集是否被收藏
     * @return
     */
    fun isLikes(): Boolean {
        return mViewBinding.layerFunction.hasLike
    }

    fun getHeroView(): View {
        return mViewBinding.tvActorLeft
    }

    fun getHeroineView(): View {
        return mViewBinding.tvActorRight
    }

    /**
     * 设置视频名称
     */
    private fun setVideoName(data: VideoInfoVo, isNewStyle: Boolean) {
        if (isNewStyle) {
            mViewBinding.clNameNew.visibility = VISIBLE
            mViewBinding.tvNameOld.visibility = GONE
            data.apply {
                bookName?.let {
                    mViewBinding.tvNameNew.text = it
                }
                //根据配置显隐剧封
                showCover(true)
                //标签
                showTags(true)
                // 演员
                showPerformer()
            }
        } else {
            mViewBinding.clNameNew.visibility = GONE
            mViewBinding.tvNameOld.visibility = VISIBLE
            data.bookName?.let {
                mViewBinding.tvNameOld.text = it
            }
        }
        SplashMS.get()?.updateColdLaunchPageContentDisplayTime(false)
        TimeMonitorManager
            .getMonitor("冷启动")
            .recordTime("rcmd_info")
    }

    private var descList: MutableList<String> = mutableListOf()

    //显示剧封
    private fun showCover(show: Boolean) {
        videoInfo?.run {
            if (show) {
                mViewBinding.ivBookCover.loadRoundImg(
                    coverWap ?: R.drawable.bbase_ic_cover_default,
                    radius = 4.dp,
                    placeholder = R.drawable.bbase_ic_cover_default,
                    error = R.drawable.bbase_ic_cover_default,
                    width = 34,
                    height = 49
                )
                mViewBinding.ivBookCover.visibility = VISIBLE
            } else {
                mViewBinding.ivBookCover.visibility = GONE
            }
        }
    }

    //显示标签
    private fun showTags(show: Boolean) {
        videoInfo?.run {
            if (show) {
                showTag?.let {
                    mViewBinding.tvTag.text = showTag
                    mViewBinding.vTagActor.visibility = VISIBLE
                    mViewBinding.tvTag.visibility = VISIBLE
                } ?: let {
                    mViewBinding.vTagActor.visibility = GONE
                    mViewBinding.tvTag.visibility = GONE
                }
            } else {
                mViewBinding.vTagActor.visibility = GONE
                mViewBinding.tvTag.visibility = GONE
            }
        }
    }

    //显示演员
    private fun showPerformer() {
        videoInfo?.run {
            performerList?.let { list ->
                mViewBinding.tvActorLeft.visibility = GONE
                mViewBinding.tvActorRight.visibility = GONE

                when (list.size) {
                    0 -> { /* 无需额外操作，已默认GONE */
                    }

                    1 -> {
                        val performer = list[0]
                        performer.performerName?.let { name ->
                            if (showPerformer == 1) {
                                mViewBinding.tvActorLeft.text = "演员·$name"
                                mViewBinding.tvActorLeft.visibility = VISIBLE
                            } else {
                                mViewBinding.tvActorLeft.visibility = GONE
                            }
                        }
                    }

                    else -> {
                        // 处理左边的TextView
                        val performerLeft = list[0]
                        performerLeft.performerName?.let { name ->
                            if (showPerformer == 1) {
                                mViewBinding.tvActorLeft.text = "演员·$name"
                                mViewBinding.tvActorLeft.visibility = VISIBLE
                            } else {
                                mViewBinding.tvActorLeft.visibility = GONE
                            }
                        }

                        // 处理右边的TextView
                        val performerRight = list[1]
                        performerRight.performerName?.let { name ->
                            if (showPerformer == 1) {
                                mViewBinding.tvActorRight.text = "演员·$name"
                                mViewBinding.tvActorRight.visibility = VISIBLE
                            } else {
                                mViewBinding.tvActorRight.visibility = GONE
                            }
                        }
                    }
                }
            } ?: run {
                mViewBinding.tvActorLeft.visibility = GONE
                mViewBinding.tvActorRight.visibility = GONE
            }
        }
    }

    /**
     * 设置当前集数及简介
     */
    fun setVideoIndex(chapterIndex: String, introduction: String?, isShowDesc: Boolean) {
        val introStyle = VideoMS.get()?.getIntroductionStyle() ?: 0
        if (introStyle == 1) {
            // 隐藏旧版的简介
            mViewBinding.tvCurrentDrama.visibility = View.VISIBLE
            mViewBinding.tvDesc2.visibility = View.GONE
            mViewBinding.tvDesc2Normal.visibility = View.INVISIBLE
            mViewBinding.tvDesc3.visibility = View.GONE
            mViewBinding.tvDescSwitch.visibility = View.GONE
            // 展示新版本
            val desc = if (chapterIndex.isNotEmpty()) {
                if (introduction.isNullOrEmpty()) {
                    chapterIndex
                } else {
                    "$chapterIndex | $introduction"
                }
            } else {
                "$introduction"
            }
            mViewBinding.tvDescNew.setScrollViewMaxHeight(ScreenUtil.dip2px(context, 36))
            mViewBinding.tvDescNew.setText(desc)
            mViewBinding.tvDescNew.spannableHandler = object : BaseExpandTextView.SpannableHandler {
                override fun getSpannable(
                    textView: TextView,
                    charSequence: CharSequence
                ): SpannableStringBuilder {
                    return SpannableStringBuilder(charSequence).apply {
                        if (chapterIndex.isNotEmpty()) {  // 增加当前剧集信息
                            kotlin.runCatching {
                                // 加粗
                                setSpan(
                                    StyleSpan(Typeface.BOLD),
                                    0,
                                    chapterIndex.length,
                                    Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                                )
                                // 更改字体颜色
                                setSpan(
                                    ForegroundColorSpan(
                                        ContextCompat.getColor(
                                            context,
                                            R.color.common_FFFFFFFF
                                        )
                                    ), 0, chapterIndex.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                                )
                                if (!introduction.isNullOrEmpty()) {
                                    setSpan(
                                        ForegroundColorSpan(
                                            ContextCompat.getColor(
                                                context,
                                                R.color.common_66FFFFFF
                                            )
                                        ),
                                        chapterIndex.length,
                                        chapterIndex.length + 2,
                                        Spanned.SPAN_INCLUSIVE_EXCLUSIVE
                                    )
                                }
                            }
                        }
                    }
                }
            }
            mViewBinding.tvDescNew.visibility = View.VISIBLE

            // 配置简介
            post {
                mViewBinding.tvDescNew.setMaxVisibleLines(10.5F)
            }
        } else {
            mViewBinding.tvDescNew.visibility = View.GONE
            // 设置旧版剧集信息，与xml中保持一致
            mViewBinding.tvCurrentDrama.visibility = View.VISIBLE
            mViewBinding.tvDesc2.visibility = View.GONE
            mViewBinding.tvDesc2Normal.visibility = View.INVISIBLE
            mViewBinding.tvDesc3.visibility = View.GONE
            mViewBinding.tvDescSwitch.visibility = View.GONE
            DescSpanUtil.setDesc(
                context, mViewBinding.tvCurrentDrama,
                mViewBinding.tvDesc2, mViewBinding.tvDesc2Normal,
                mViewBinding.tvDesc3, mViewBinding.tvDescSwitch,
                chapterIndex, introduction, if (isShowDesc) 84 else 112
            ) {
                descList.clear()
                descList.addAll(it)
            }
        }
    }

    fun seekToPlay(position: Long) {
        mPlayer?.seekTo(position)
        mIsPause = false
    }


    /**
     * 暂停播放
     */
    fun pausePlay() {
        onlyPausePlay()
        mViewBinding.ivPlayIcon.setImageResource(R.drawable.player_resume)
        playIconVisibility(View.VISIBLE)
    }

    private fun onlyPausePlay() {
        mIsPause = true
        mPlayer?.pause()
    }

    /**
     * 恢复播放
     */
    fun resumePlay() {
        mIsPause = false
        start()
        playIconVisibility(View.GONE)
    }

    fun getFullButton() = mViewBinding.btnFullScreen

    /**
     * 设置排行榜按钮/推荐理由信息
     */
    private fun setBottomTips(data: BaseBookInfo?) {
        LogUtil.d(TAG, "data bottomTips = ${data}")
        //设置排行榜按钮
        mViewBinding.rankBottom.bindData(data)
        //设置推荐理由信息
        mViewBinding.recommendBottom.bindData(data)
        mViewBinding.bookBottom.bindData(data)
        mViewBinding.bottomCover.visibility =
            if (mViewBinding.rankBottom.visibility == GONE && mViewBinding.recommendBottom.visibility == GONE && mViewBinding.bookBottom.visibility == GONE) GONE else VISIBLE
        mViewBinding.areaRank.visibility =
            if (mViewBinding.bottomCover.visibility == GONE) VISIBLE else GONE
    }


    fun setWatchingVisibility(visibility: Boolean, data: ContinueWatchVo?) {
        LogUtil.d(
            "continueWatchBgg",
            "当前video：= ${videoInfoVo?.bookId} showBook = ${data?.continueWatch?.showBook}"
        )
        Handler(Looper.getMainLooper()).post {
            if (visibility && data?.continueWatch?.littleCardSwitch == 1) {
                if (videoInfoVo?.bookId == data?.continueWatch?.showBook) {
                    mViewBinding.continueWatchBgg.visibility = VISIBLE
                    HomeME.get().setVisibility().post(visibility)
                    mViewBinding.continueWatchBgg.bindData(data)
                } else {
                    HomeME.get().setVisibility().post(visibility)
                    mViewBinding.continueWatchBgg.visibility = GONE
                }
            } else {
                HomeME.get().setVisibility().post(false)
                mViewBinding.continueWatchBgg.visibility = GONE
            }
        }


    }

    /**
     * 手势监听
     */
    interface OnGestureListener : HomeFunctionPortLayer.Listener {

        fun onSingleTapConfirmed()

        fun onDoubleTap()

        fun onLongClick() {}

        fun onSelectDramaClick()

        fun onNextDramaClick(text: String)

        fun onGestureEnd(gestureType: GestureType) {}

        fun onHorizontalDistance(startF: Float, endF: Float) {}

        fun onStartTrackingTouch()

        fun onStopTrackingTouch(time: Long)

        fun onActorClick(
            view: View,
            performerIds: MutableList<String>,
            performerName: String?,
            worksNum: Int?
        )

        fun onBookNameClick(view: View, contentName: String?)

        fun onTagClick(deepLink: String, tag: String)

        fun ignoreAutoTrack(view: View)

        fun onDescSwitchClick(): Boolean
    }

    fun setOnGestureListener(onListener: OnGestureListener?) {
        this.onGestureListener = onListener
        onGestureListener?.ignoreAutoTrack(mViewBinding.tvNameNew)
        onGestureListener?.ignoreAutoTrack(mViewBinding.ivBookCover)
        onGestureListener?.ignoreAutoTrack(mViewBinding.tvNameOld)
        mViewBinding.layerFunction.listener = onListener
    }

    fun handleLandVideo(videoInfo: BaseBookInfo?, playerHeight: Int) {
        var bottomDistance = 0
        var topOffset = 0  // 横屏剧整体向上移动，以保证Y轴居中。topOffset就是整体的偏移量
        var videoY = 0  // 视频Y轴的坐标
        videoInfoVo = videoInfo
        if (videoInfo != null && videoInfo.isLandscapeVideo() && playerHeight > 0
            && (videoInfo.height ?: 0) > 0 && (videoInfo.width ?: 0) > 0
        ) {
            val screenWidth = ScreenUtil.getScreenWidth()
            val videoHeight = screenWidth * videoInfo.height!! / videoInfo.width!!
            bottomDistance =
                (playerHeight - videoHeight - ScreenUtil.dip2px(context, 42)) / 2
            topOffset = (playerHeight - videoHeight) / 2 - bottomDistance
            videoY = (playerHeight - videoHeight) / 2 - topOffset
        }
        getTextureViewRoot().y = -topOffset.toFloat()

        mViewBinding.playIconAnchor.visibility = if (topOffset > 0) View.VISIBLE else View.GONE

        mViewBinding.btnFullScreen.apply {
            if (bottomDistance > 0) {
                val lp = layoutParams
                if (lp is MarginLayoutParams) {
                    lp.bottomMargin = bottomDistance
                }
                layoutParams = lp
                visibility = View.VISIBLE
            } else {
                visibility = View.GONE
            }
        }
        mViewBinding.registerNumberLand.apply {
            if (bottomDistance > 0) {
                val lp = layoutParams
                if (lp is MarginLayoutParams) {
                    lp.topMargin = videoY + ScreenUtil.dip2px(context, 12)
                }
                isLandRecordNumber = true
                layoutParams = lp
            } else {
                isLandRecordNumber = false
            }
        }
    }

    fun updateScaleMode(heightWidthRatio: Float, isLandVideo: Boolean? = null) {
        mPlayer?.updateScaleMode(heightWidthRatio, isLandVideo)
    }

    fun setStartTime(position: Long) {
        mPlayer?.setStartTime(position)
    }

    fun enableHttpDns(openHttpDns: Boolean) {
        mPlayer?.enableHttpDns(openHttpDns)
    }

    fun setSpeed(speed: Float) {
        mPlayer?.setSpeed(speed)
    }

    fun start() {
        mPlayer?.start()
        maxBufferDurationMax()
    }

    /**
     * 更新最大缓冲区时长为最大值
     */
    private fun maxBufferDurationMax() {
        LogUtil.d(TAG, "${mPlayer?.getPlayer().hashCode()}     maxBufferDurationMax   ===60000")
        mPlayer?.maxBufferDurationMax()
    }

    /**
     * 更新最大缓冲区时长为初始化值
     */
    private fun maxBufferDurationInit() {
        LogUtil.d(TAG, "${mPlayer?.getPlayer().hashCode()}     maxBufferDurationInit   ===3000")
        mPlayer?.maxBufferDurationInit()
    }

    fun stop() {
        mPlayer?.stop()
    }

    fun switchVideo(forceStop: Boolean) {
        if (mPlayer?.isPrepared() == true && !forceStop) {
            mPlayer?.replenishLoadingEnd()
            seekTo(0, false)
            onlyPausePlay()
            seekBarSeekTo(0)
            maxBufferDurationInit()
        } else {
            stop()
//            seekBarSeekTo(0)
//            showThumb()
            maxBufferDurationInit()
        }
    }

    fun getDuration(): Long {
        return mPlayer?.getDuration() ?: 0L
    }

    fun getLoadingTime(): Long {
        return mPlayer?.getLoadingTime() ?: 0
    }

    fun getRenderFPS(): Float {
        return mPlayer?.getRenderFPS() ?: 0F
    }

    fun getVideoBitrate(): Float {
        return mPlayer?.getVideoBitrate() ?: 0F
    }

    fun getDownloadBitrate(): Float {
        return mPlayer?.getDownloadBitrate() ?: 0F
    }

    fun getAudioBitrate(): Float {
        return mPlayer?.getAudioBitrate() ?: 0F
    }

    fun changeUrlTo(url: String?) {
        mPlayer?.moveTo(url)
    }

    fun seekTo(progress: Long, startPlay: Boolean) {
        mPlayer?.seekTo(progress, startPlay)
        updateNextText(-1)
    }

    fun destroy() {
        mPlayer?.destroy()
    }

    fun getVolume(): Float {
        return mPlayer?.getVolume() ?: 1F
    }

    fun getSpeed(): Float {
        return mPlayer?.getSpeed() ?: 1F
    }

    fun isPrepared(): Boolean {
        return mPlayer?.isPrepared() ?: false
    }

    fun hasRenderingStart(): Boolean {
        return mPlayer?.hasRenderingStart() ?: false
    }

    fun prepareAndStart(currentPosition: Int, onPageSelected: Boolean) {
        LogUtil.d(TAG, "prepareAndStart")
        if (isPrepared()) {
            LogUtil.d(
                TAG,
                "${
                    mPlayer?.getPlayer().hashCode()
                }   当前播放器已经prepared，直接开始播放即可   ${videoInfoVo?.bookName}"
            )
            if (onPageSelected) {
                mOnPlayerListener?.onPrepared(currentPosition, mPlayer?.getVid())
            }
            if (hasRenderingStart()) {
                mOnPlayerListener?.onRenderingStart(currentPosition, mPlayer?.getVid())
                hideThumb()
            }
            start()
        } else {
            mPlayer?.prepare()
            LogUtil.d(
                TAG,
                "${
                    mPlayer?.getPlayer().hashCode()
                }   当前播放器未prepared，先进行prepare    ${videoInfoVo?.bookName}"
            )
        }
    }


    fun bindVideo(player: DzPlayer?, url: String?, position: Int) {
        mPlayer?.bindTextViewAndVideo(getTextureViewRoot(), player, url, position) {
            showThumb()
        }
    }

    //预渲染播放器
    fun preRenderVideo(player: DzPlayer?, url: String?, position: Int) {
        mPlayer?.bindTextureView(getTextureViewRoot(), player, url, position) {
            showThumb()
        }
    }

    fun prePrepared(position: Int) {
        if (mPlayer?.getAliPlayer()?.getPlayerState() != -1) {
            mPlayer?.prepare()
            LogUtil.d(BBaseMC.TAG_PLAYER, "播放器预准备  position==$position")
        } else {
            LogUtil.d(BBaseMC.TAG_PLAYER, "播放器对象已经被回收了   position==$position")
        }
    }

    fun bindListener(itemPosition: Int) {
        mCurPosition = itemPosition
        mPlayer?.mOnPlayerListener = object : IPlayerListener {
            override fun onPrepared(position: Int) {
                mOnPlayerListener?.onPrepared(mCurPosition, mPlayer?.getVid())
            }

            override fun onInfo(code: Int, msg: String?, value: Long) {
                mOnPlayerListener?.onInfo(code, msg, value, mCurPosition, mPlayer?.getVid()) {}
            }

            override fun onSeekComplete() {
                mOnPlayerListener?.onSeekComplete(mCurPosition, mPlayer?.getVid())
            }

            override fun onCompletion() {
                mOnPlayerListener?.onCompletion(mCurPosition, mPlayer?.getVid())
            }

            override fun onPlayStateChanged(status: Int) {
                mOnPlayerListener?.onPlayStateChanged(status, mCurPosition, mPlayer?.getVid())
            }

            override fun onRenderingStart() {
                if (!hasRenderingStart()) {
                    mOnPlayerListener?.onRenderingStart(mCurPosition, mPlayer?.getVid())
                } else {
                    LogUtil.e(BBaseMC.TAG_PLAYER, "首帧二次回调")
                }
                hideThumb()
            }

            override fun onError(errorCode: Int, errorMsg: String, extra: String?) {
                mOnPlayerListener?.let {
                    it.onError(errorCode, errorMsg, extra, mCurPosition, mPlayer?.getVid())
                } ?: stop()
            }

            override fun onLoadingBegin() {
                mOnPlayerListener?.onLoadingBegin(mCurPosition, mPlayer?.getVid())
            }

            override fun onLoadingEnd() {
                mOnPlayerListener?.onLoadingEnd(mCurPosition, mPlayer?.getVid())
            }
        }
    }

    fun unbind(tagIndex: Int) {
        showThumb()
        mPlayer?.unbind(tagIndex)
    }

    fun getPlayerRenderView(): PlayerRenderView? {
        return mPlayer
    }

    fun isLoop(loop: Boolean) {
        mPlayer?.isLoop(loop)
    }

    fun updatePlayerSize(height: Int, width: Int, top: Int) {
        LogUtil.d("updatePlayerSize", "height ==$height    width==$width    top=$top")
        if (height > 0 && width > 0) {
            val layoutParams = getTextureViewRoot().layoutParams
            if (layoutParams is ViewGroup.LayoutParams) {
                layoutParams.height = height
                layoutParams.width = width
            }
            getTextureViewRoot().layoutParams = layoutParams
            val marginLayoutParams = getTextureViewRoot().layoutParams
            if (marginLayoutParams is MarginLayoutParams) {
                marginLayoutParams.topMargin = top
            }
            getTextureViewRoot().layoutParams = marginLayoutParams
        }
    }

    fun resetPlayerSize() {
        val layoutParams = getTextureViewRoot().layoutParams
        if (layoutParams is ViewGroup.LayoutParams) {
            layoutParams.height = ViewGroup.LayoutParams.MATCH_PARENT
            layoutParams.width = ViewGroup.LayoutParams.MATCH_PARENT
        }
        getTextureViewRoot().layoutParams = layoutParams
        val marginLayoutParams = getTextureViewRoot().layoutParams
        if (marginLayoutParams is MarginLayoutParams) {
            marginLayoutParams.topMargin = 0
        }
        getTextureViewRoot().layoutParams = marginLayoutParams
    }

    fun isPreRenderPlayer(): Boolean {
        return mPlayer?.getAliPlayer()?.isPreload ?: false
    }

    fun isPreRendered(): Boolean {
        return mPlayer?.hasRenderingStart() ?: false
    }

    fun getCurBuffer(): Long {
        mPlayer?.getAliPlayer()?.let {
            return it.getCurBuffer()
        } ?: run {
            return -1
        }
    }

    fun getVid(): String {
        return mPlayer?.getPlayer()?.mVid ?: ""
    }

    /**
     * 发送行为埋点事件
     * @param event String
     */
    fun sendEvent(event: String) {
        mViewBinding.layerFunction.onEvent(event)
        if (event == VideoMC.EVENT_PAGE_RELEASE) {  // 页面被划走
            // 划走后自动收起简介
            if (VideoMS.get()?.getIntroductionStyle() == 1) {
                mViewBinding.tvDescNew.setCollapsedText()
            } else {
                DescSpanUtil.collapsedText(
                    mViewBinding.tvDesc2,
                    mViewBinding.tvDesc3,
                    mViewBinding.tvDescSwitch,
                    descList
                )
            }
        }
    }

    fun updateSeekBarTouchArea() {
        val location = IntArray(2)
        mViewBinding.clName.getLocationOnScreen(location)

        val screenY = location[1] // Y坐标
        val height = mViewBinding.clName.height // View的高度
        mViewBinding.seekBarArea.also {
            val lp = it.layoutParams
            lp.height =
                (this.height - screenY - height).coerceAtMost(ScreenUtil.dip2px(context, 48))
            it.layoutParams = lp
        }
    }

    /**
     * 接收MainActivity透传的Move事件
     */
    fun onSeekBarAreaMove(event: MotionEvent) {
        when (event.action) {
            ACTION_DOWN -> {
                downX = event.x
                xAxisPosition =
                    (mViewBinding.seekBar.progress * (mViewBinding.seekBar.width - 24.dp) / mViewBinding.seekBar.max)
                actionDownTime = System.currentTimeMillis()
                seekBarTakeOver = false
                isSingleTap = true
            }

            ACTION_MOVE -> {
                if (!mIsSpeed) {
                    isSingleTap = false
                    seekBarActionMove(event)
                } else {
                    isSingleTap = true
                }
            }

            ACTION_UP -> {
                seekBarActionUp(event)
            }

            ACTION_CANCEL -> {
                seekBarActionCancel()
            }
        }
    }

    /**
     * 遍历 SeekBar 热区下方的所有视图View，透传点击事件到对应位置的View
     * @param event 点击事件
     */
    fun onSeekBarAreaClick(event: MotionEvent): Boolean {
        viewsUnderSeekbarArea2?.forEach { view ->
            if (ViewUtils.isEventInView(view, event)) {
                LogUtil.d(
                    TAG_PLAYER_SEEKBAR, "SeekBar触发热区点击事件," +
                            "落在${context.resources.getResourceEntryName(view?.id ?: 0)}上"
                )
                view?.performClick()
                return true
            }
        }
        return false
    }

    /**
     * 请求缩略图时的剧集ID
     */
    private var requestedBookId: String? = null
    private var requestedChapterId: String? = null
    private var requestedTime: Long? = null

    /**
     * 雪碧图缩略图重试次数
     */
    var retryThumbnailListenerCount = 0

    /**
     * 雪碧图帮助类
     */
    var mThumbnailHelper: ThumbnailHelper? = null

    /**
     * 雪碧图缩略图监听
     */
    fun setThumbnailListener() {
        retryThumbnailListenerCount = 0
        val spriteImg = this.videoInfo?.content?.spriteImg
        LogUtil.d("ThumbnailHelper", "雪碧图URL: $spriteImg") // 添加调试日志
        if (!spriteImg.isNullOrBlank()) {
            mViewBinding.ivTimeH.setImageDrawable(getDrawable(com.dz.business.base.R.drawable.bbase_ic_cover_default_h))
            mViewBinding.ivTimeV.setImageDrawable(getDrawable(com.dz.business.base.R.drawable.bbase_ic_cover_default))
            mViewBinding.ivTimeH.visibility = GONE
            mViewBinding.ivTimeV.visibility = GONE
            mThumbnailHelper = ThumbnailHelper(spriteImg)
            //2.设置监听。
            mThumbnailHelper?.setOnPrepareListener(object : OnPrepareListener {
                override fun onPrepareSuccess() {
                    retryThumbnailListenerCount = 0
                    LogUtil.d("ThumbnailHelper", "onPrepareSuccess 1000")
                }

                override fun onPrepareFail() {
                    LogUtil.e("ThumbnailHelper", "onPrepareFail")
                    if (retryThumbnailListenerCount < 4 && mViewBinding.clTime.context != null) {
                        retryThumbnailListenerCount++
                        setThumbnailListener()
                    }
                }
            })

            mThumbnailHelper?.setOnThumbnailGetListener(object : OnThumbnailGetListener {
                override fun onThumbnailGetSuccess(
                    positionMs: Long,
                    thumbnailBitmapInfo: ThumbnailBitmapInfo
                ) {
                    if (isDragging) {
                        try {
                            if (requestedBookId == <EMAIL>?.bookId &&
                                requestedChapterId == <EMAIL>?.chapterId
                                && ((requestedTime ?: 0) / 1000 == positionMs / 1000)
                                && mViewBinding.clTime.context != null &&
                                thumbnailBitmapInfo.thumbnailUrl == spriteImg
                            ) {
                                val roundedBitmap = thumbnailBitmapInfo.thumbnailBitmap
                                mViewBinding.clImage.visibility = VISIBLE
                                if (videoInfo?.isLandscapeVideo() == true) {
                                    mViewBinding.ivTimeH.visibility = VISIBLE
                                    mViewBinding.ivTimeV.visibility = GONE
                                    mViewBinding.ivTimeH.loadRoundBitmap(
                                        img = roundedBitmap,
                                        radius = ScreenUtil.dip2px(context, 4f),
                                    )
                                } else {
                                    mViewBinding.ivTimeH.visibility = GONE
                                    mViewBinding.ivTimeV.visibility = VISIBLE
                                    mViewBinding.ivTimeV.loadRoundBitmap(
                                        img = roundedBitmap,
                                        radius = ScreenUtil.dip2px(context, 4f),
                                    )
                                }
                            }
                        } catch (e: Exception) {
                            LogUtil.e("ThumbnailHelper", "图片加载失败: ${e.message}")
                        }
                    }
                }

                override fun onThumbnailGetFail(positionMs: Long, errorMsg: String?) {
                    LogUtil.e(
                        "ThumbnailHelper",
                        "onThumbnailGetFail positionMs=${positionMs} errorMsg=${errorMsg}"
                    )
                }
            })
            //3.加载缩略图。
            mThumbnailHelper?.prepare()
        }
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        mThumbnailHelper?.setOnThumbnailGetListener(null)
        mThumbnailHelper?.setOnPrepareListener(null)
        mThumbnailHelper = null
    }

    //描述
    private fun updateDes(data: VideoInfoVo) {
        mViewBinding.tvDescNew.updateCollapsedLine(if (introductionLines == 1) 1 else 2)
        data.run {
            val chapterIndex = if (finishStatus == 0) {
                if (videoLinkType == 2) {
                    "更新至${updateNum}集"
                } else {
                    "第${chapterIndex}集·更新至${updateNum}集"
                }
            } else {
                if (videoLinkType == 2) {
                    "全${updateNum}集"
                } else {
                    "第${chapterIndex}集·全${updateNum}集"
                }
            }
            val desc = if (chapterIndex.isNotEmpty()) {
                if (introduction.isNullOrEmpty()) {
                    chapterIndex
                } else {
                    "$chapterIndex | $introduction"
                }
            } else {
                "$introduction"
            }
            mViewBinding.tvDescNew.updateText(desc)
        }
    }

    // 2.9.0 UI可配置
    fun setCompUI(recommendVideoInfo: RecommendVideoInfo?) {
        recommendVideoInfo?.let { item ->
            showFree = item.showFree ?: 0
            showTags = item.showTags ?: 0
            showPerformer = item.showPerformer ?: 0
            showCover = item.showCover ?: 0
            introductionLines = if ((item.introductionLines ?: 0) > 0) item.introductionLines else 2
            videoInfo?.let {
                showCover(showCover == 1)
                showTags(showTags == 1)
                setTags(it)
                showPerformer()
                updateDes(it)
            }
        }
    }
}