package com.dz.business.home.ui.component


import BBaseME
import android.annotation.SuppressLint
import android.content.Context
import androidx.lifecycle.LifecycleOwner
import com.dz.business.base.BBaseMC
import com.dz.business.base.R
import com.dz.business.base.data.BBaseKV
import com.dz.business.base.data.bean.ContinueWatchVo
import com.dz.business.base.data.bean.StrategyInfo
import com.dz.business.base.detail.DetailMR
import com.dz.business.base.track.TrackUtil
import com.dz.business.base.ui.BaseDialogComp
import com.dz.business.base.utils.GsonUtil
import com.dz.business.home.databinding.HomeCompContinueFloatBinding
import com.dz.business.home.vm.ContinueWatchVM
import com.dz.business.track.events.DzTrackEvents
import com.dz.business.track.trace.OmapNode
import com.dz.business.track.trace.SourceNode
import com.dz.business.track.trackProperties
import com.dz.business.track.utis.ElementClickUtils
import com.dz.foundation.base.utils.LogUtil
import com.dz.foundation.base.utils.dp
import com.dz.foundation.imageloader.loadRoundImg
import com.google.gson.Gson
import org.json.JSONObject


/**
 * @Author: boqw
 * @Date: 2024/10/28
 * @Description:
 * @Version:1.0
 */
class ContinueWatchFloatComp(context: Context) :
    BaseDialogComp<HomeCompContinueFloatBinding, ContinueWatchVM>(context) {
    var homeTag: String? = "推荐"
    override fun initData() {
        trackProperties(ignoreAutoTrack = true)
        dialogSetting.cancelable = true
        dialogSetting.backgroundColor = getColor(R.color.transparent)
        dialogSetting.canpropagation = true
    }

    override fun initView() {
        mViewModel.routeIntent?.continueWatchVo.let {
            loadImg(it)
            mViewBinding.continueTitle.text = it?.continueWatch?.bookName
            mViewBinding.continueRecord.text = it?.continueWatch?.content
            mViewBinding.continueTitle.text = it?.continueWatch?.bookName
        }
        trackBookExposure()
        trackHiveExposure()
    }


    private fun loadImg(data: ContinueWatchVo?) {
        mViewBinding.continuePic.loadRoundImg(
            data?.continueWatch?.coverWap,
            error = R.drawable.bbase_ic_cover_default,
            placeholder = R.drawable.bbase_ic_cover_default,
            radius = 8.dp
        )
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun initListener() {
        ElementClickUtils.ignoreAutoTrack(mViewBinding.continueBg)
        ElementClickUtils.ignoreAutoTrack(mViewBinding.continueClose)
        mViewBinding.continueClose.registerClickAction {
            BBaseKV.lastTimeShowContinueCompInOneLife = System.currentTimeMillis()
            mViewModel.routeIntent?.let {
                it.doClose(this)
            }
            trackAppClickClose()
            dismiss()
        }
        mViewBinding.continueBg.registerClickAction {
            trackAppClick()
            BBaseKV.lastTimeShowContinueCompInOneLife = System.currentTimeMillis()
            mViewModel.routeIntent?.let {
                it.doClose(this)
            }
            DetailMR.get().videoList().apply {
                bookId = mViewModel.routeIntent?.continueWatchVo?.continueWatch?.bookId
                chapterIndex = mViewModel.routeIntent?.continueWatchVo?.continueWatch?.chapterIndex
                chapterId = mViewModel.routeIntent?.continueWatchVo?.continueWatch?.chapterId
                columnName = "继续观看-首页$homeTag"
                origin = SourceNode.origin_sy
                firstTierPlaySource = "首页"
                secondTierPlaySource = "首页-$homeTag"
                thirdTierPlaySource = "首页-继续观看浮层"
                floatingLayerState = "展开"
                autoPlay = true
                playletSrcType = 7
                originName = SourceNode.origin_name_sy
                columnId = if(homeTag == "推荐 ") "continue_wch_tj" else "continue_wch_jd"
                backToRecommend = false
                omap = mViewModel.routeIntent?.continueWatchVo?.continueWatch?.omap
                channelId = "continue_play"
                channelName = "继续观看浮层"
                channelPos = 0
                contentPos = 0

            }.start()
            dismiss()
        }
    }

    override fun onDismiss() {
        super.onDismiss()
    }

    override fun subscribeEvent(lifecycleOwner: LifecycleOwner, lifecycleTag: String) {
        super.subscribeEvent(lifecycleOwner, lifecycleTag)
        BBaseME.get().getHomeTag().observeForever(lifecycleTag) {
            LogUtil.d("getHomeTag", "传来的homeTag:$it")
            homeTag = it?:"推荐"
        }
    }

    private fun trackBookExposure() {
        DzTrackEvents.get().bookViewShow()
            .bookId(mViewModel.routeIntent?.continueWatchVo?.continueWatch?.bookId)
            .chapterName("第${mViewModel.routeIntent?.continueWatchVo?.continueWatch?.chapterIndex}集")
            .bookName(mViewModel.routeIntent?.continueWatchVo?.continueWatch?.bookName)
            .chapterId(mViewModel.routeIntent?.continueWatchVo?.continueWatch?.chapterId)
            .chapterIndex(mViewModel.routeIntent?.continueWatchVo?.continueWatch?.chapterIndex)
            .isNewContent(mViewModel.routeIntent?.continueWatchVo?.continueWatch?.newContent)
            .contentSource("首页继续观看")
            .videoChargeType(mViewModel.routeIntent?.continueWatchVo?.continueWatch?.videoPayType)
            .origin("首页").columnName(if(homeTag == "剧单") "首页-剧单" else homeTag).firstTierPlaySource("首页")
            .secondTierPlaySource("首页-$homeTag").thirdTierPlaySource("首页-继续观看浮层").floatingLayerState("展开").track()
    }




    private fun trackAppClick() {
        DzTrackEvents.get().buttonClick()
            .buttonContent(mViewModel.routeIntent?.continueWatchVo?.continueWatch?.chapterIndex.toString())
            .buttonName("首页推荐继续观看")
            .bookId(mViewModel.routeIntent?.continueWatchVo?.continueWatch?.bookId)
            .chapterName("第${mViewModel.routeIntent?.continueWatchVo?.continueWatch?.chapterIndex}集")
            .bookName(mViewModel.routeIntent?.continueWatchVo?.continueWatch?.bookName)
            .chapterId(mViewModel.routeIntent?.continueWatchVo?.continueWatch?.chapterId)
            .chapterIndex(mViewModel.routeIntent?.continueWatchVo?.continueWatch?.chapterIndex)
            .videoChargeType((mViewModel.routeIntent?.continueWatchVo?.continueWatch?.videoPayType))
            .isNewContent(mViewModel.routeIntent?.continueWatchVo?.continueWatch?.newContent)
            .origin("首页").columnName(if(homeTag == "剧单") "首页-剧单" else homeTag).firstTierPlaySource("首页")
            .secondTierPlaySource("首页-$homeTag").thirdTierPlaySource("首页-继续观看浮层").floatingLayerState("展开").track()
    }

    private fun trackAppClickClose() {
        kotlin.runCatching {
            val jsonObject = JSONObject().apply {
                put("PositionName", "首页推荐继续观看")
                put("\$element_content", "关闭")
                put("BookID", mViewModel.routeIntent?.continueWatchVo?.continueWatch?.bookId)
                put("BookName", mViewModel.routeIntent?.continueWatchVo?.continueWatch?.bookName)
                put("ChaptersID", mViewModel.routeIntent?.continueWatchVo?.continueWatch?.chapterId)
                put(
                    "ChaptersName",
                    "第${mViewModel.routeIntent?.continueWatchVo?.continueWatch?.chapterIndex}集"
                )
                put(
                    "ChaptersNum",
                    mViewModel.routeIntent?.continueWatchVo?.continueWatch?.chapterIndex
                )
                put("VideoChargeType", mViewModel.routeIntent?.continueWatchVo?.continueWatch?.videoPayType)
                put(
                    "IsNewContent",
                    mViewModel.routeIntent?.continueWatchVo?.continueWatch?.newContent
                )
                put("Origin", "首页")
                put("ColumnName", if(homeTag == "剧单") "首页-剧单" else homeTag)
                put("firstTierPlaySource", "首页")
                put("secondTierPlaySource", "首页-$homeTag")
                put("thirdTierPlaySource", "首页-继续观看浮层")
                put("FloatingLayerState", "展开")


            }
            TrackUtil.track("\$AppClick", jsonObject)
        }.onFailure {
            it.printStackTrace()
        }
    }

    private fun trackHiveExposure() {

        // 将 oMap 转换为 StrategyInfo
        val jsonString = Gson().toJson(mViewModel.routeIntent?.continueWatchVo?.continueWatch?.omap )
        val momap = GsonUtil.fromJson(jsonString, StrategyInfo::class.java)
        if (momap == null) {
            LogUtil.d("继续观看浮层", "hiveExploreTracking: omap 解析失败")
        }

        DzTrackEvents.get().hiveExposure().show(OmapNode().apply {
            origin = BBaseMC.origin_sy
            originName = SourceNode.origin_name_sy
            channelId = "continue_play"
            channelName = "继续观看浮层"
            channelPos = "0"
            columnId = if(homeTag == "推荐") "continue_wch_tj" else "continue_wch_jd"
            columnName = if(homeTag == "推荐") "继续观看-首页推荐" else "继续观看-首页剧单"
            contentPos = 0
            playletSrcType = 7
            contentType = "2"
            partnerId = ""
            playletId = mViewModel.routeIntent?.continueWatchVo?.continueWatch?.bookId ?: ""
            playletName = mViewModel.routeIntent?.continueWatchVo?.continueWatch?.bookName ?: ""
            contentId = mViewModel.routeIntent?.continueWatchVo?.continueWatch?.bookId ?: ""
            setStrategyInfo(momap)
        }).track()
    }

    override fun getEnterAnim(): Int {
        return R.anim.common_top_in
    }

    override fun getExitAnim(): Int {
        return R.anim.common_top_out
    }

    fun setCompVisibility(visibility: Boolean) {
        if (visibility) {
            mViewBinding.root.visibility = VISIBLE
        } else {
            mViewBinding.root.visibility = GONE
        }
    }


}