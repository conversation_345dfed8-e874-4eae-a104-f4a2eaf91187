package com.dz.business.home.ui.page

import BBaseME
import CoroutineUtils
import android.content.res.Configuration
import android.graphics.PixelFormat
import android.os.Bundle
import android.view.MotionEvent
import android.view.View
import android.view.WindowManager
import androidx.core.content.ContextCompat
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.OnItemTouchListener
import androidx.viewpager2.widget.ViewPager2
import com.blankj.utilcode.util.GsonUtils
import com.dz.business.DzDataRepository
import com.dz.business.base.BBaseMC
import com.dz.business.base.BBaseMC.TAG_APP_LAUNCH
import com.dz.business.base.BBaseMC.TAG_RESOLUTION
import com.dz.business.base.BBaseMR
import com.dz.business.base.SpeedUtil
import com.dz.business.base.SpeedUtil.coldLaunchTacked
import com.dz.business.base.SpeedUtil.pageName
import com.dz.business.base.bcommon.BCommonMC
import com.dz.business.base.bcommon.BCommonME
import com.dz.business.base.bcommon.BCommonMS
import com.dz.business.base.bcommon.ShareListener
import com.dz.business.base.data.BBaseKV
import com.dz.business.base.data.BBaseKV.needFollowBubble
import com.dz.business.base.data.BBaseKV.newUserGuide
import com.dz.business.base.data.BBaseKV.recommendGuideStatus
import com.dz.business.base.data.FragmentStatus
import com.dz.business.base.data.PageConstant
import com.dz.business.base.data.bean.CardType
import com.dz.business.base.data.bean.ContinueWatchVo
import com.dz.business.base.data.bean.PreLoadFunSwitchVo
import com.dz.business.base.data.bean.ShareItemBean
import com.dz.business.base.data.bean.ShareResultBean
import com.dz.business.base.data.bean.SwitchState
import com.dz.business.base.data.bean.TierPlaySourceVo
import com.dz.business.base.data.bean.VideoInfoVo
import com.dz.business.base.data.bean.WxShareConfigVo
import com.dz.business.base.data.enums.EnterTypeMode
import com.dz.business.base.detail.DetailMC
import com.dz.business.base.detail.DetailMC.Companion.TAG_PLAYER
import com.dz.business.base.detail.DetailMR
import com.dz.business.base.detail.DetailMS
import com.dz.business.base.dialog.DialogME
import com.dz.business.base.experiment.ExperimentMC
import com.dz.business.base.flutter.FlutterMS
import com.dz.business.base.helper.FloatWindowManage
import com.dz.business.base.helper.FloatWindowManage.Companion.MAIN_SOURCE
import com.dz.business.base.home.HomeMC
import com.dz.business.base.home.HomeME
import com.dz.business.base.home.HomeMR
import com.dz.business.base.home.HomeMS
import com.dz.business.base.home.data.LikesInfo
import com.dz.business.base.home.intent.ActorDialogIntent.Callback
import com.dz.business.base.livedata.AppEvent
import com.dz.business.base.load.DBHelper
import com.dz.business.base.main.MainMC
import com.dz.business.base.main.MainME
import com.dz.business.base.main.priority.MainPriorityDialogTask
import com.dz.business.base.operation.OperationMS
import com.dz.business.base.personal.PersonalMC
import com.dz.business.base.personal.PersonalME
import com.dz.business.base.priority.PriorityMC
import com.dz.business.base.priority.PriorityTaskManager
import com.dz.business.base.priority.tasks.PriorityDialogTask
import com.dz.business.base.reader.ReaderMS
import com.dz.business.base.splash.SplashMC
import com.dz.business.base.splash.SplashME
import com.dz.business.base.splash.SplashMS
import com.dz.business.base.track.ThirdSDKTrack
import com.dz.business.base.ui.player.PrerenderConfig
import com.dz.business.base.ui.player.listener.INewPlayerListener
import com.dz.business.base.ui.refresh.RefreshStatePage
import com.dz.business.base.ui.viewpager2.OnPageChangeCallbackCompat
import com.dz.business.base.ui.viewpager2.ViewPager2Helper
import com.dz.business.base.utils.ActionRecorder
import com.dz.business.base.utils.CommInfoUtil
import com.dz.business.base.utils.NotificationStyleLikeDialogUtils
import com.dz.business.base.utils.OCPCManager
import com.dz.business.base.utils.ViewExposeUtil
import com.dz.business.base.video.VideoMC
import com.dz.business.base.video.VideoMC.TAG_PLAYING_DURATION
import com.dz.business.base.video.VideoME
import com.dz.business.base.video.VideoMS
import com.dz.business.base.video.data.CommentNumBean
import com.dz.business.base.video.data.CommentNumCheckDatabaseBean
import com.dz.business.base.video.intent.CommentIntent
import com.dz.business.base.vm.event.RequestEventCallback
import com.dz.business.base.web.WebMS
import com.dz.business.base.welfare.WelfareMC
import com.dz.business.base.welfare.WelfareME
import com.dz.business.base.welfare.widget.IProgressDragPendantComp
import com.dz.business.base.welfare.widget.IProgressPendantComp
import com.dz.business.bcommon.utils.PlayingStatisticsMgr
import com.dz.business.home.R
import com.dz.business.home.adapter.AggregationCardListener
import com.dz.business.home.adapter.AggregationCardViewHolder
import com.dz.business.home.adapter.HomeAdVideoViewHolder
import com.dz.business.home.adapter.NewRecommendPageAdapter
import com.dz.business.home.adapter.NewRecommendVideoViewHolder
import com.dz.business.home.databinding.HomeFragmentRecommendNewBinding
import com.dz.business.home.ui.component.ContinueWatchFloatComp
import com.dz.business.home.ui.component.PlayerControllerComp
import com.dz.business.home.ui.component.RecommendPlayerController
import com.dz.business.home.ui.component.RecommendRefreshHeader
import com.dz.business.home.utils.DrawAdManager
import com.dz.business.home.vm.RecommendVM
import com.dz.business.home.vm.TrackUtil
import com.dz.business.repository.entity.BookEntity
import com.dz.business.repository.entity.HistoryEntity
import com.dz.business.track.events.DzTrackEvents
import com.dz.business.track.events.sensor.ErrorTE
import com.dz.business.track.events.sensor.ReadingTE
import com.dz.business.track.events.sensor.ShareTE
import com.dz.business.track.monitor.MonitorMC
import com.dz.business.track.monitor.printLaunchDuration
import com.dz.business.track.monitor.trackToSensor
import com.dz.business.track.trace.OmapNode
import com.dz.business.track.trace.QmapNode
import com.dz.business.track.trace.SourceNode
import com.dz.business.track.tracker.SensorTracker
import com.dz.business.track.utis.ElementClickUtils
import com.dz.business.video.comment.CommentDelegate
import com.dz.business.video.danmu.VideoDanMuManager
import com.dz.business.video.data.VideoKV
import com.dz.business.video.enums.GestureType
import com.dz.business.video.enums.PlayState
import com.dz.business.video.interfaces.VideoHolderListener
import com.dz.business.video.interfaces.VideoLifecycle
import com.dz.business.video.track.VideoTrackUtil
import com.dz.business.video.utils.FunctionIconManager.getFavoriteNum2
import com.dz.business.video.utils.VideoPlayTimeManager
import com.dz.business.welfare.WelfareMS
import com.dz.foundation.base.component.splash.SplashDisplayStateManager
import com.dz.foundation.base.manager.task.TaskManager
import com.dz.foundation.base.manager.task.TaskManager.Companion.delayTask
import com.dz.foundation.base.utils.LocalActivityMgr
import com.dz.foundation.base.utils.LogUtil
import com.dz.foundation.base.utils.NetWorkUtil
import com.dz.foundation.base.utils.ScreenUtil
import com.dz.foundation.base.utils.dp
import com.dz.foundation.base.utils.monitor.PlayerMonitorManager
import com.dz.foundation.base.utils.monitor.TimeMonitorManager
import com.dz.foundation.event2.observeEvent
import com.dz.foundation.network.requester.RequestException
import com.dz.platform.ad.lifecycle.PlayScene
import com.dz.platform.common.base.ui.dialog.PDialogComponent
import com.dz.platform.common.router.DialogRouteIntent
import com.dz.platform.common.router.SchemeRouter
import com.dz.platform.common.router.addDismissListener
import com.dz.platform.common.router.addShowListener
import com.dz.platform.common.router.onDismiss
import com.dz.platform.common.router.onShow
import com.dz.platform.common.toast.ToastManager
import com.dz.platform.player.listener.ConvertURLCallback
import com.dz.platform.player.listener.OnInfoListener
import com.dz.platform.player.listener.OnStateChangedListener
import com.dz.platform.player.player.BasePlayerManager
import com.dz.platform.player.player.PlayerInfo
import com.scwang.smart.refresh.layout.constant.RefreshState
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.json.JSONObject
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * @Author: guyh
 * @Date: 2024/8/7
 * @Description:
 * @Version:1.0
 */
class NewRecommendFragment : BaseRecommendFragment<HomeFragmentRecommendNewBinding, RecommendVM>(),
    RefreshStatePage {

    //刷新类型，默认是下拉刷新类型
    private var mFreshType = BBaseMC.REFRESH_TYPE_PULL
    private var firstCreateVp = true//列表初次创建

    companion object {
        const val TAG = "player_recommend"
    }

    /**
     * 由于网络原因导致的暂停
     */
    private var pausedByNetwork = false
    private var currentDuration: Long = 0L
    private var isRecommendedRefresh: Boolean = false//个性化推荐刷新，默认false，当个性化推荐开关切换后变为true
    private var isUserLikeRefresh: Boolean = false//看剧喜好设置刷新，默认false，当个性化推荐开关切换后变为true
    private var isUserChangeRefresh: Boolean = false//账号信息变更，触发页面刷新。默认false，当账号信息变更后变为true
    private var isPlayerPrePrepared: Boolean = false//预加载的播放器是否已prepared

    private lateinit var mPageAdapter: NewRecommendPageAdapter

    private val mPlayerController by lazy { RecommendPlayerController() }
    private var pageRendered = false//页面是否已经渲染

    /**
     * 是否在后台
     */
    private var mIsOnBackground = false

    /**
     * 当前选中位置
     */
    private var mCurrentPosition = 0

    /**
     * 正常滑动，上一个被暂停的位置
     */
    private var mLastStopPosition = -1

    /**
     * 当前列表的ViewHolder
     */
    private var currentHolder: RecyclerView.ViewHolder? = null

    /**
     * 是否正在刷新
     */
    private var mIsLoadingData = false

    /**
     * 是否有更多剧集
     */
    private var mHasMore = false

    /**
     * 是否长按
     */
    private var mIsLongPress = false

    /**
     * 处于播放中的时长
     */
    private var playingTime = 0L
    private var startPlayTime = 0L

    // 解bug万能方法：加变量
    private var currentVideoDuration = 0L

    //跳转场景标识
    private var sceneSign = ""

    private var refreshState: RefreshState = RefreshState.None
    private val mVideoLifecycle: VideoLifecycle = VideoLifecycle()
    private var videoDanMuManger: VideoDanMuManager? = null
    private var introDialog: PDialogComponent<*>? = null
    //视频是否已经可以起播了
    private var isVideoReady = false

    /**
     * 负责视频评论列表弹窗
     */
    private val commentDelegate: CommentDelegate by lazy {
        CommentDelegate(mViewModel).apply {
            bind(null, this@NewRecommendFragment)
        }
    }

    override fun getPageLazyTag(): PageLazyTag {
        return PageLazyTag.FRAGMENT_RECOMMEND_NEW
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        SpeedUtil.recommendOnCreateTime = System.currentTimeMillis()
        super.onCreate(savedInstanceState)
    }

    override fun getViewModel(): RecommendVM = mViewModel

    override fun initData() {
        super.initData()
        LogUtil.d(TAG_PLAYER, "推荐页打开:NewRecommendFragment")
        mVideoLifecycle.addObserver(VideoPlayTimeManager.getVideoPlayTimeObserver())
        BBaseKV.lastTimeShowContinueCompInOneLife = 0
        initDanMuManager()
        //判断重建后报错
        if (mViewModel.saveRvAllCells != null) {
            mViewModel.restoreSaveCells()
            mViewModel.cancelPause("welfare")
            mViewModel.cancelPause("push")
        }
        mViewModel.initShareData()
        getUILifecycleOwner()?.lifecycle?.removeObserver(lifecycleObserver)
        getUILifecycleOwner()?.lifecycle?.addObserver(lifecycleObserver)
    }

    private var inputBoxShowing = false

    private fun initDanMuManager() {
        val danMuManager = VideoDanMuManager()
        danMuManager.init(getActivityPageId())
        mVideoLifecycle.addObserver(danMuManager.videoLifecycleObserver)
        danMuManager.setPlaySpeed(1f)
        danMuManager.setActionCallback(object : VideoDanMuManager.DanMuActionCallback {
            override fun onInputBoxDismiss() {
                inputBoxShowing = false
                mViewModel.cancelPause(VideoMC.PLAY_END_SEND_DANMU)
            }

            override fun onInputBoxShow() {
                inputBoxShowing = true
                if (!getPlayerPauseStatus()) {
                    mViewModel.pausePlay(VideoMC.PLAY_END_SEND_DANMU)
                }
            }

        })
        videoDanMuManger = danMuManager
    }

    private val lifecycleObserver = LifecycleEventObserver { _, event ->
        when (event) {
            Lifecycle.Event.ON_RESUME -> {
                openPushDialog()
            }

            Lifecycle.Event.ON_DESTROY -> {
                unregister()
            }

            else -> {}
        }
    }

    /**
     * 首次加载推荐页数据
     */
    private fun firstLoadRecommendData() {
        LogUtil.d("HomeDataRepository", "${this::class.java.simpleName} firstLoadRecommendData -->")
        var type = BBaseMC.REFRESH_TYPE_STARTUP
        isRecommendedRefresh = false
        isUserLikeRefresh = false
        isUserChangeRefresh = false
        var appointedContents: MutableList<String>? = null
        if ((BBaseKV.ocpcJumpPosition == OCPCManager.OCPC_JUMP_TYPE1 || BBaseKV.ocpcJumpPosition == OCPCManager.OCPC_JUMP_TYPE2) && !SplashMC.isHotSplash) {//首页启动并且不是热启动，则刷新首页的数据
            LogUtil.d(
                "OCPC",
                "RecommendFragment归因，show==" + BBaseKV.ocpcJumpPosition + "，是否热启动==" + SplashMC.isHotSplash
            )
            OCPCManager.setPlotOcpcBookId(null)
            if (BBaseKV.ocpcJumpPosition == OCPCManager.OCPC_JUMP_TYPE1) {
                OCPCManager.getOcpcResult().let { ocpcResult ->
                    //跳转到阅读器
                    ocpcResult?.bookInfo?.bookId?.let { bid ->
                        appointedContents = mutableListOf()
                        val ocpcCid: String =
                            OCPCManager.getOcpcResult()?.chapterId ?: ""
                        appointedContents?.add(
                            bid + "_" + ocpcCid.ifEmpty {
                                "0"
                            }
                        )
                        OCPCManager.setPlotOcpcBookId(bid)
                    }
                }
            }
            type = BBaseMC.REFRESH_TYPE_OCPC
            OCPCManager.onOpenBookSuccess()
        }
        val excludeBookIds: MutableList<String> = mutableListOf()
        OCPCManager.getOcpcBookId()?.let {
            if (appointedContents == null || appointedContents?.size == 0) {
                excludeBookIds.add(it)
            }
        }
        mViewModel.getDataList(
            type,
            appointedContents,
            excludeBookIds,
            isFirstQuery = true,
        )
    }


    private fun unregister() {
        getUILifecycleOwner()?.lifecycle?.removeObserver(lifecycleObserver)
    }

    override fun initView() {
        mViewBinding.refreshView.apply {
            setEnableHeaderTranslationContent(false)
            setEnableOverScrollBounce(false)  // 关闭越界回弹。
            setHeaderTriggerRate(0.5f)  // 触发下拉刷新的比例，以Header的高度为基数
            setRefreshHeader(RecommendRefreshHeader(context).apply {
                mListener = object : RecommendRefreshHeader.StateListener {
                    override fun onStateChanged(oldState: RefreshState, newState: RefreshState) {
                        refreshState = newState
                        HomeME.get().rcmdRefreshStateChanged().post(newState)
                    }

                    override fun onDragging(percent: Float, offset: Int) {
                        HomeME.get().rcmdRefreshDraggingPercent().post(percent)
                    }
                }
            })
        }
        mViewBinding.vp.post {
            val ratio =
                mViewBinding.vp.measuredHeight.toFloat() / mViewBinding.vp.measuredWidth.toFloat()
            BBaseKV.recommendHeightWidthRatio = ratio
            LogUtil.d(
                TAG_PLAYER,
                "推荐页播放器高度：${mViewBinding.vp.measuredHeight}，宽度：${mViewBinding.vp.measuredWidth}"
            )
            LogUtil.d(TAG_PLAYER, "推荐页播放器宽高比：${ratio * 9} : 9")
        }
        mPlayerController.initPlayerConfig(
            resources.configuration.orientation == Configuration.ORIENTATION_PORTRAIT,
            PrerenderConfig( CommInfoUtil.isPlayerPreload(), BBaseMC.PLAYER_PRELOAD_BUFFER_B, getUiId()),
            this.lifecycle
        )
        //初始化ViewPager2
        initViewPager2()
//        initLPlayerListener()
        //初始化沉浸式广告
        initDrawAd()

        if (mPageAdapter.itemCount <= 0) {
            firstLoadRecommendData()
            if (mViewModel.isFirstPlay) {
                recordUserSenseTime(0)
            }
        }
        logPlayerConfig()
    }

    private fun logPlayerConfig() {
        kotlin.runCatching {
            LogUtil.d(
                "player_config_recommend",
                "当前为：${CommInfoUtil.multicastDesc()} " +
                        "\n使用的：${if (BBaseKV.multipleInstancesTypeTest == -1) "神策配置" else "本地配置"}" +
                        "\n设备评分开关为：${if (CommInfoUtil.scoreSwitch()) "开启" else "关闭"}" +
                        "\n设备评分为：${BBaseKV.equipmentScore}" +
                        "\n预加载开关为：${if (CommInfoUtil.isPlayerPreload()) "开启" else "关闭"}"
            )
        }
    }

    private val convertURLCallback = object : ConvertURLCallback {
        override fun convertURL(srcURL: String, srcFormat: String): String? {
            LogUtil.d("convertURL", "首页convertURL回调  srcURL==$srcURL")
            mViewModel.currentVideInfo?.let {
                var url: String?
                //线上地址报错切换
                //判断备用地址下标有改变，需要切换播放地址 防止获取下标越界
                it.content?.apply {
                    if (this.contentUlrIndex > -1 &&
                        (!this.getUrlList().isNullOrEmpty()) &&
                        (this.getUrlList()?.contains(srcURL) == true || this.getUrl() == srcURL) &&
                        (this.getUrlList()?.size ?: 0) > this.contentUlrIndex
                    ) {
                        //取出当前要播放的备用地址
                        url = this.getUrlList()?.get(this.contentUlrIndex)
                        if (this.switchState == SwitchState.NEED_SWITCH) {
                            //修改状态为切换完成
                            this.switchState = SwitchState.SWITCH_ED
                        }
                        return url
                    }
                }
            }
            LogUtil.d(
                DetailMC.PLAYER_DOWNLOAD_TAG,
                "原始的url ==$srcURL    不需要切换地址修改当前的播放链接"
            )
            return null
        }
    }

    private fun initDrawAd() {
        //每天会重置广告曝光次数
        mViewModel.resetDrawConf()
        LogUtil.d(HomeMC.AD_TAG, "应用启动loadDrawAd")
        mViewModel.loadDrawAd(mViewBinding.container, activity)
    }

    var downX = 0f//当前item按下的X轴坐标
    var downY = 0f//当前item按下的Y轴坐标
    private val itemOnTouchListener = object : OnItemTouchListener {
        override fun onInterceptTouchEvent(rv: RecyclerView, event: MotionEvent): Boolean {
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    downX = event.x
                    downY = event.y
                }
            }
            return false
        }

        override fun onTouchEvent(rv: RecyclerView, e: MotionEvent) {

        }

        override fun onRequestDisallowInterceptTouchEvent(disallowIntercept: Boolean) {
        }

    }

    override fun initListener() {
        mViewBinding.refreshView.setDzRefreshListener {
            onRefresh(mFreshType)
        }
        mViewModel.setEventCallback(this, object : RequestEventCallback {
            override fun onRequestStart(hasData: Boolean) {
                SpeedUtil.recommendRequestStart = System.currentTimeMillis()
                if (!hasData) {
                    mViewModel.statusPoster.statusLoading().post()
                    MainScope().launch(Dispatchers.IO) {
                        mPlayerController.getPlayer(0, preload = true, playerVid = null)
                    }
                }
            }

            override fun onResponse() {
                SpeedUtil.recommendRequestEnd = System.currentTimeMillis()
                SpeedUtil.recommendRequestResult = 1
                if (mViewModel.mVideoAndAdList.isEmpty()) {
                    SpeedUtil.recommendRequestResult = 2
                    mViewModel.statusPoster.statusDataEmpty().iconRes(R.drawable.bbase_ic_net_error_dark)
                        .marginTop(230).des(getString(R.string.bbase_not_network))
                        .desColor(ContextCompat.getColor(requireContext(), R.color.common_FFFFFFFF))
                        .actionText(getString(R.string.bbase_refresh))
                        .actionTextColor(ContextCompat.getColor(mViewBinding.refreshView.context, R.color.common_white))
                        .actionBgResource(R.drawable.common_refresh_btn_bg_dark)
                        .actionListener {
                            onRefresh(BBaseMC.REFRESH_TYPE_ERROR)
//                            if (mViewModel.isFirstPlay) {
//                                recordUserSenseTime(0)
//                            }
                        }.post()
                } else {
                    mViewModel.statusPoster.statusDismiss().post()
                }
            }

            override fun onRequestError(e: RequestException, hasData: Boolean) {
                SpeedUtil.recommendRequestEnd = System.currentTimeMillis()
                SpeedUtil.recommendRequestResult = 0
                mIsLoadingData = false
                mViewModel.statusPoster.statusDismiss().post()
                if (hasData) {
                    ToastManager.showToast(e.message)
                } else {
                    LogUtil.d(
                        TAG_APP_LAUNCH,
                        "App启动 -> 网络异常UI显示的耗时:${System.currentTimeMillis() - SpeedUtil.appAttachTime}"
                    )
                    SplashMS.get()?.updateColdLaunchPageContentDisplayTime(false)
                    SplashMS.get()?.updateColdPlayerPrepareDisplayTime(false)
                    mViewModel.statusPoster.statusDataEmpty()
                        .marginTop(230).des(getString(R.string.bbase_not_network))
                        .iconRes(R.drawable.bbase_ic_net_error_dark)
                        .desColor(ContextCompat.getColor(requireContext(), R.color.common_FF5E6267))
                        .actionText(getString(R.string.bbase_refresh))
                        .actionTextColor(ContextCompat.getColor(mViewBinding.refreshView.context, R.color.common_white))
                        .actionBgResource(R.drawable.common_refresh_btn_bg_dark)
                        .actionListener {
                            onRefresh(BBaseMC.REFRESH_TYPE_ERROR)
//                            if (mViewModel.isFirstPlay) {
//                                recordUserSenseTime(0)
//                            }
                        }.post()
                }
                if (mViewBinding.refreshView.isRefreshing) {
                    mViewBinding.refreshView.finishDzRefresh()
                }
                if (mViewBinding.refreshView.isLoading) {
                    mViewBinding.refreshView.finishDzLoadMoreFail()
                }
            }

        })
        val view = mViewBinding.vp.getChildAt(0)
        if (view is RecyclerView) {
            view.addOnItemTouchListener(itemOnTouchListener)
        }
        mViewBinding.vp.viewTreeObserver.addOnGlobalLayoutListener {
            kotlin.runCatching {
                if (BBaseKV.recommendHeightWidthRatio == 0F || playerWidth == 0 || playerHeight == 0 ) {
                    val ratio =
                        mViewBinding.vp.measuredHeight.toFloat() / mViewBinding.vp.measuredWidth.toFloat()
                    BBaseKV.recommendHeightWidthRatio = ratio
                    LogUtil.d(
                        TAG_PLAYER,
                        "推荐页播放器高度：${mViewBinding.vp.measuredHeight}，宽度：${mViewBinding.vp.measuredWidth}"
                    )
                    LogUtil.d(TAG_PLAYER, "推荐页播放器宽高比：${ratio * 9} : 9")
                    updateScaleMode(
                        BBaseKV.recommendHeightWidthRatio,
                        mViewModel.currentVideInfo?.isLandscapeVideo()
                    )
                    playerWidth = mViewBinding.vp.measuredWidth
                    playerHeight = mViewBinding.vp.measuredHeight
                }
            }

        }
    }

    /**
     *  当页面可见时，包含父fragment、自己fragment生命周期走onResume时，视频自动播放或从暂停切换为继续播放
     *  页面恢复可见状态，如果是个性化推荐开关更新了，则向下翻章
     */
    private fun onPageResumed() {
        attemptToInit(true)
        if (isOnResumed()) {
            if (!BBaseKV.testRecording) {
                ScreenUtil.prohibitScreenRecording(requireActivity().window)
            }
            val holder =
                ViewPager2Helper.findViewHolderByPosition(mViewBinding.vp, mCurrentPosition)
            if (holder is NewRecommendVideoViewHolder) {
                holder.onAttachedToWindow()
            }
            updateHistory = true
            setOnBackground(false)
            if (isRecommendedRefresh || isUserLikeRefresh || isUserChangeRefresh) {//如果同时改了个性化设置、看剧喜好，则按看剧喜好类型刷新
                mFreshType = if (isUserChangeRefresh) {
                    if (CommInfoUtil.hasLogin()) BBaseMC.REFRESH_TYPE_LOGIN else BBaseMC.REFRESH_TYPE_LOGOUT
                } else if (isRecommendedRefresh) {
                    BBaseMC.REFRESH_TYPE_RECOMMEND_FRESH
                } else {
                    BBaseMC.REFRESH_TYPE_UPDATE_LABEL
                }
                mViewBinding.refreshView.autoRefresh()
                mIsOnBackground = false
            }
        }
    }

    override fun subscribeEvent(lifecycleOwner: LifecycleOwner, lifecycleTag: String) {
        observeEvent<AppEvent.TouchEvent> {
            if (it.context == context ) {
                (currentHolder as? NewRecommendVideoViewHolder)?.controller?.onSeekBarAreaMove(it.motionEvent)
            }
        }
        HomeME.get().onFragmentStatus().observe(lifecycleOwner) {
            mViewModel.parentPaused = it == FragmentStatus.PAUSE
            if (mViewModel.parentPaused) {
                ScreenUtil.allowScreenRecording(requireActivity().window)
                setOnBackground(true)
            } else {
                if (isOnResumed()) {
                    if (mViewModel.initDrawAd) {
                        LogUtil.d(HomeMC.AD_TAG, "页面切换loadDrawAd")
                        mViewModel.loadDrawAd(mViewBinding.container, activity)
                    }
                }
                onPageResumed()
            }
        }
        BBaseME.get().getHomeTag().observeForever(lifecycleTag) {
            LogUtil.d("getHomeTag", "传来的homeTag:$it")
            mViewModel.homeTag = it?:"推荐"
        }
        HomeME.get().getWatchVo().observe(lifecycleOwner){
            HomeME.get().postWatchVo().postSticky(mViewModel.continueWatchVoRecommend)
            HomeME.get().setVisibility().postSticky(mViewModel.isShowComp)
        }

        HomeME.get().dramaListPause().observeForever(lifecycleTag) {
            coroutineScope?.cancel()
            coroutineScope = null
        }
        HomeME.get().continueResume().observeForever(lifecycleTag) {
            coroutineScope?.cancel()
            coroutineScope = null
            if (recommendContinueWatchingDialog != null) {
                coroutineScope = CoroutineUtils.getCoroutineScope()
                LogUtil.d(TAG_PLAYER, "剧单恢复单播准备二次计时")
                MainScope().launch(Dispatchers.Main) {
                    recommendContinueWatchingDialog?.setCompVisibility(true)
                }
                coroutineScope?.launch {
                    while (hasShowTime <= if (mViewModel?.continueWatchVoRecommend?.continueWatch?.littleCardSwitch == 1) (mViewModel?.continueWatchVoRecommend?.continueWatch?.littleCardTime
                            ?: 0) else (mViewModel?.continueWatchVoRecommend?.continueWatch?.longCardVanishTime
                            ?: 0)
                    ) {
                        delay(100)
                        hasShowTime += 0.1
                        LogUtil.d(TAG_PLAYER, "播放器继续播放弹窗展示时间：${hasShowTime}s")
                    }
                    if (mViewModel.continueWatchVoRecommend?.continueWatch?.littleCardSwitch == 0) {
                        BBaseKV.lastTimeShowContinueCompInOneLife = System.currentTimeMillis()
                    }
                    MainScope().launch(Dispatchers.Main) {
                        recommendContinueWatchingDialog?.dismiss()
                    }

                    mViewModel.trackPopExposure()
                    mPlayerController?.setWatchingVisibility(
                        true,
                        mViewModel.continueWatchVoRecommend
                    )
                    mViewModel.isShowComp = true
                    coroutineScope?.cancel()
                }
            }
        }
        HomeME.get().invisibleComp().observeForever(lifecycleTag) {
            LogUtil.d("favorite", "切换到在追暂停1")
            recommendContinueWatchingDialog?.setCompVisibility(false)
            coroutineScope?.cancel()
            coroutineScope = null
        }
        BBaseME.get().jumpToDetail().observe(lifecycleOwner){
            mPlayerController.setWatchingVisibility(false, mViewModel.continueWatchVoRecommend)
            mViewModel.trackAppClick()
            mViewModel.jumpToDetail(it)
            HomeME.get().setVisibility().post(false)
        }
        BBaseME.get().closeContinueComp().observe(lifecycleOwner){
            mPlayerController?.setWatchingVisibility(false, mViewModel.continueWatchVoRecommend)
            mViewModel.trackAppClickClose()
        }
        //监听章末推荐
        HomeME.get().finalChapterJump().observe(lifecycleOwner) {
            sceneSign = "_章末"
            if (it == mViewModel.mVideoInfo?.bookId) {
                scrollToNextVideo()
            } else {
                if (mCurrentPosition < mPageAdapter.itemCount) {
                    startPlay(mCurrentPosition)
                }
            }
        }
        //个性化开关设置修改后触发推荐刷新
        PersonalME.get().recommendContentChanged().observe(lifecycleOwner) {
            isRecommendedRefresh = true
        }
        //看剧喜好设置修改后触发推荐刷新
        BBaseME.get().userLikeUpdateNotice().observe(lifecycleOwner) {
            isUserLikeRefresh = true
        }
        //首页选中时，推荐可见。再次点击首页tab刷新推荐列表
        HomeME.get().onHomeTabReselected().observe(lifecycleOwner) { tabName ->
            tabName?.let { tab ->
                if (tab == HomeMC.TAB_RECOMMEND) {
                    mFreshType = BBaseMC.REFRESH_TYPE_CLICK_TAB
                    mViewBinding.refreshView.autoRefresh()
                }
            }
        }
        //监听删除收藏成功
        HomeME.get().deleteFavoriteSuccess().observeForever(lifecycleTag) { bookIds ->
            mPageAdapter.getData().forEachIndexed { index, videoInfoVo ->
                if (videoInfoVo.bookId in bookIds) {
                    videoInfoVo.inBookShelf = false
                    mPageAdapter.setItem(index, videoInfoVo)
                }
            }
            mViewModel.mVideoInfo?.let { infoVo ->
                if (infoVo.bookId in bookIds) {
                    favoriteStatus(false, infoVo.getFavoriteNum2())
                }
            }
        }
        //监听增加收藏成功
        HomeME.get().addFavoriteSuccess().observeForever(lifecycleTag) {
            mPageAdapter.getData().forEachIndexed { index, videoInfoVo ->
                if (videoInfoVo.bookId == it) {
                    videoInfoVo.inBookShelf = true
                    mPageAdapter.setItem(index, videoInfoVo)
                }
            }
            mViewModel.mVideoInfo?.let { infoVo ->
                if (infoVo.bookId == it) {
                    playFavoriteAnimation()
                    favoriteStatus(true, infoVo.getFavoriteNum2())
                }
            }
        }
        //监听刷新分享数
        BCommonME.get().refreshShareNum().observeForever(lifecycleTag) {
            updateShares(it)
        }
        //监听增加点赞成功
        HomeME.get().addLikesSuccess().observeForever(lifecycleTag) { likeInfo ->
            mPageAdapter.getData().forEachIndexed { index, videoInfoVo ->
                if (videoInfoVo.bookId + "_" + videoInfoVo.chapterId == likeInfo.likesKey) {//变更推荐列表数据
                    videoInfoVo.likesChecked = likeInfo.isLiked
                    videoInfoVo.likesNumActual = likeInfo.likesNumActual
                    videoInfoVo.likesNum = likeInfo.likesNum
                    mPageAdapter.setItem(index, videoInfoVo)
                }
            }
            updateLikes(likeInfo)
        }
        //监听删除点赞成功
        HomeME.get().deleteLikesSuccess().observeForever(lifecycleTag) { likeInfo ->
            mPageAdapter.getData().forEachIndexed { index, videoInfoVo ->
                if (videoInfoVo.bookId + "_" + videoInfoVo.chapterId == likeInfo.likesKey) {
                    videoInfoVo.likesChecked = likeInfo.isLiked
                    videoInfoVo.likesNum = likeInfo.likesNum
                    videoInfoVo.likesNumActual = likeInfo.likesNumActual
                    mPageAdapter.setItem(index, videoInfoVo)
                }
            }
            updateLikes(likeInfo)
        }
        BBaseME.get().updateLikes().observe(lifecycleOwner) { likeList ->
            LogUtil.d(BBaseMC.TAG_LIKES, "推荐页面刷新")
            mPageAdapter.getData().forEachIndexed { index, videoInfoVo ->
                if (likeList.contains(videoInfoVo.bookId + "_" + videoInfoVo.chapterId)) {
                    videoInfoVo.likesChecked = true
                    mPageAdapter.setItem(index, videoInfoVo)
                }
            }
        }
        //用户发生了变化
        PersonalME.get().onUserAccountChanged().observeForever(lifecycleTag) {
            val excludedLoginTypes = setOf(
                PersonalMC.LOGIN_JUMP_TYPE_DAN_MU,
                PersonalMC.LOGIN_JUMP_TYPE_COMMENT
            )
            if (it.loginJumpType !in excludedLoginTypes) {
                isUserChangeRefresh = true
            }
        }
        //监听详情页面返回数据，刷新推荐列表数据
        HomeME.get().updateChapter().observeForever(lifecycleTag) {
            it?.let {
                clickPause = false
                mViewModel.clearPause()
                updateItemData(it)
            }
        }
        MainME.get().showDialogEvent().observe(lifecycleOwner) {
            if (it) {
                setOnBackground(true)
            } else {
                onPageResumed()
            }
        }
        MainME.get().appExit().observe(lifecycleOwner) {
            if (isPlaying) {
                track(1)
            }
        }
        MainME.get().onTabSelected().observe(lifecycleOwner, lifecycleTag) {
            introDialog?.dismiss()
        }
        MainME.get().onTabChange().observe(lifecycleOwner, lifecycleTag) {
            if (recommendContinueWatchingDialog != null) {
                recommendContinueWatchingDialog?.setCompVisibility(false)
                coroutineScope?.cancel()
                coroutineScope = null
            }
        }
        WelfareME.get().awardDialog().observe(lifecycleOwner) { showing ->
            if (!isOnResumed()) {
                return@observe
            }
            if (showing) {
                mViewModel.pausePlay("welfare")
            } else {
                mViewModel.cancelPause("welfare")
                if (needOpenPushDialog) {
                    openPushDialog()
                }
            }
        }

        MainME.get().showKocComp().observe(lifecycleOwner) { showing ->
            if (showing) {
                mViewModel.pausePlay("KocComp")
            } else {
                mViewModel.cancelPause("KocComp")
            }
        }

        BCommonME.get().pusDialog().observe(lifecycleOwner) { showing ->
            if (showing) {
                mViewModel.pausePlay("push")
            } else {
                mViewModel.cancelPause("push")
                if (needOpenWelfDialog) {
                    openWelfDialog()
                }
            }
        }
        //微信分享配置信息
        BCommonME.get().refreshWxShare().observeSticky(lifecycleOwner) { data ->
            var config: WxShareConfigVo? = null
            if (data != null) {
                config = GsonUtils.fromJson(data, WxShareConfigVo::class.java)
            }
            mViewModel.mWxShareConfigVo = config
            config?.shareCompleteTime?.let { time ->
                BBaseMC.shareCompleteTime = time
            }
            updateShare(
                mViewModel.mWxShareConfigVo?.isWxSharedInPlayUI() == true,
                mViewModel.mWxShareConfigVo?.showNormalIcon(), mViewModel.mVideoInfo?.getShareNum()
            )
        }
        //网络恢复监听
        MainME.get().onNetworkChanged().observe(lifecycleOwner) {
            if (it == MainMC.NETWORK_CONNECTED) {  // 网络恢复连接
                // 当前是暂停状态 && 由于网络原因导致的暂停 && 处于前台
                if (getPlayerPauseStatus() && pausedByNetwork && !mIsOnBackground) {
                    playerStop()
                    LogUtil.d(
                        "player_start_time",
                        "网络状态恢复，切换进度并开始播放currentDuration ===$currentDuration"
                    )
                    mViewBinding.vp.post {
                        setStartTime(currentDuration)
                        startPlay(mCurrentPosition)
                    }
                }
            }
        }
        //vip发生变更，则刷新数据
        BBaseME.get().onVipStatusChanged().observe(lifecycleOwner) {
            LogUtil.d(HomeMC.AD_TAG, "VIP状态发生变化  ，刷新列表")
            mViewModel.refreshDataList()
        }
        BBaseME.get().httpDnsResponse().observeSticky(lifecycleOwner) {
            if (it) {
                LogUtil.d(
                    ExperimentMC.HTTP_DNS_TAG, "推荐页面收到abtest数据获取成功消息，打开httpDns"
                )
                enableHttpDns(it)
            }
        }
        MainME.get().pauseAdLoadError().observe(lifecycleOwner) {
            MainME.get().errorLoadPauseAd().post(mViewModel.mVideoInfo)
        }
        MainME.get().closePauseAd().observe(lifecycleOwner) {
            if (it) {
                VideoTrackUtil.trackPauseCancel(
                    mViewModel.videoTrackInfo, VideoTrackUtil.PAUSE_SCENE_USER, true
                )
                onPauseClick()
            }
        }
        BBaseME.get().updateMultipleSpeed().observe(lifecycleOwner) {
            setSpeed(BBaseKV.multipleSpeed)
        }
        SplashME.get().forceLaunch().observe(lifecycleOwner) {
            onRefresh(BBaseMC.REFRESH_TYPE_FORCE_LAUNCH)
        }
        DialogME.get().onDialogShow().observe(lifecycleOwner) {
            mViewModel.printLog("dialog", "弹窗展示:$it")
            mViewModel.pausePlay(it)
        }
        DialogME.get().onDialogDismiss().observe(lifecycleOwner) {
            mViewModel.printLog("dialog", "弹窗关闭:$it")
            mViewModel.cancelPause(it)
        }
        DialogME.get().onAppstoreGuideShow().observe(lifecycleOwner) {
            if (it) {
                mViewModel.pausePlay("AppStore")
            } else {
                mViewModel.cancelPause("AppStore")
            }
        }
        VideoME.get().configChanged().observe(lifecycleOwner) {
            when (it) {
                VideoMC.FUNC_INTRO -> {
                    if (!VideoKV.introRecSwitch) {
                        introDialog?.dismiss()
                    }
                }
            }
        }
        //播放器预渲染下一集
        BBaseME.get().preloadPlayer().observe(lifecycleOwner) {//需要区分是否是二级页，暂时不用
            if (it == getUiId() && mViewBinding.vp.currentItem + 1 < mPageAdapter.itemCount) {
                mPageAdapter.notifyItemRangeChanged(
                    mViewBinding.vp.currentItem + 1, 1, HomeMC.PAYLOAD_UPDATE_PLAYER_BIND
                )
            }
        }
        //开屏页结束后视频起播
        SplashDisplayStateManager.splashDisplayStatus.observe(lifecycleOwner) { visible ->
            LogUtil.d(TAG,"splashDisplayStatus 改变,visible=$visible,getPlayerPauseStatus=${getPlayerPauseStatus()},isFragmentVisible=${isFragmentVisible()}")
            if (!visible && getPlayerPauseStatus() && isFragmentVisible()) {
                LogUtil.d(TAG,"开屏页结束，视频准备播放-->,isVideoReady=${isVideoReady}")
                if (isVideoReady) {
                    SplashMS.get()?.updateColdLaunchPageContentDisplayTime(true)
                    SplashMS.get()?.updateColdPlayerPrepareDisplayTime(true)

                    //视频起播后延迟2秒开始预加载福利页激励视频
                    delayTask(2000) {
                        LogUtil.d("XXX","视频起播，开屏页关闭2秒后，触发预加载-->")
                        WebMS.get()?.preloadWelfareRewardAd(0)
                    }
                }
                resumePlay()
            }
        }

        // 显示福利挂件
        MainME.get().showWelfareWidget().observeForeverSticky(lifecycleTag) {
            welfareWidget = it
            if (welfareWidget is IProgressDragPendantComp) {
                (welfareWidget as? IProgressDragPendantComp)?.run {
                    if (BBaseKV.playFirstShow != SimpleDateFormat(
                            "yyyy-MM-dd",
                            Locale.CHINA
                        ).format(Date())
                    ) {
//                  每日首次冷启动显示侧滑引导
                        showIconsPlay()
                        lifecycleScope.delayTask(2000) {
                            setIsDragEnable(true)
                        }
                    } else {
                        setIsDragEnable(true)
                    }
                }
            }
        }
        // 挂件曝光的时候判断阶段任务才曝光
        MainME.get().pendantExposure().observeForeverSticky(lifecycleTag) {
            if (mViewModel.rewardStatus.value == null) {
                waitRewardStatusToReport = true
            } else {
                reportPendantExposure(OperationMS.get()?.getPendantConfig(WelfareMC.POSITION_HOME))
            }
        }

        //是否有悬浮窗正在显示
        MainME.get().floatCompDisplayStatus().observe(lifecycleOwner) { status ->
            if (status == 3) {
                LogUtil.d(TAG,"floatCompDisplayStatus，其它悬浮窗已经显示完了，重新检测要不要显示最近观看浮窗")
                checkIsShowContinueWatchComp()
            }
        }

    }


    private fun checkIsShowContinueWatchComp(){
        MainScope().launch(Dispatchers.Main) {
            if (mViewModel?.continueWatchVoRecommend?.continueWatch?.showBook == mViewModel.mVideoInfo?.bookId && mViewModel?.continueWatchVoRecommend?.continueWatch?.isShow == 0) {
                recommendContinueWatching(data = mViewModel?.continueWatchVoRecommend)
            }
        }
    }


    private fun scrollToNextVideo() {
        if (mCurrentPosition + 1 < mPageAdapter.itemCount) {
            if (mViewModel.mVideoAndAdList.isNotEmpty() && mViewModel.mVideoAndAdList.size > mCurrentPosition + 1 && mViewModel.mVideoAndAdList[mCurrentPosition + 1].cardType == CardType.VIDEO) {
                selectVideo(mCurrentPosition + 1, VideoMC.PLAY_END_AUTO_SWAP)
            } else if (mViewModel.mVideoAndAdList.isNotEmpty() && mViewModel.mVideoAndAdList.size > mCurrentPosition + 2 && mCurrentPosition + 2 < mPageAdapter.itemCount) {
                selectVideo(mCurrentPosition + 2, VideoMC.PLAY_END_AUTO_SWAP)
            } else {
                loadMore(true)
            }
        } else {
            loadMore(true)
        }
    }

    private fun updateLikes(likeInfo: LikesInfo) {
        mViewModel.mVideoInfo?.run {
            if (bookId + "_" + chapterId == likeInfo.likesKey) {//刷新当前页面UI
                likesChecked = likeInfo.isLiked
                likesNumActual = likeInfo.likesNumActual
                likesNum = likeInfo.likesNum
                LogUtil.d(BBaseMC.TAG_LIKES, "updateLikes,显示点赞==${likesChecked}")
                likesStatus(likeInfo.isLiked == true, getRealLikesNum())
            }
        }
    }

    //更新分享数据
    private fun updateShares(shareResultInfo: ShareResultBean) {
        mPageAdapter.getData().forEachIndexed { index, videoInfoVo ->
            if (videoInfoVo.bookId == shareResultInfo.bookId) {
                if (!shareResultInfo.shareTimes.isNullOrEmpty()) {
                    videoInfoVo.setShareNum(shareResultInfo.shareTimes)
                }
                mPageAdapter.setItem(index, videoInfoVo)
            }
        }
        mViewModel.mVideoInfo?.run {
            if (bookId == shareResultInfo.bookId) {//刷新当前页面UI
                if (!shareResultInfo.shareTimes.isNullOrEmpty()) {
                    updateShare(
                        mViewModel.mWxShareConfigVo?.isWxSharedInPlayUI() == true,
                        mViewModel.mWxShareConfigVo?.showNormalIcon(), shareResultInfo.shareTimes
                    )
                }
            }
        }
    }

    override fun subscribeObserver(lifecycleOwner: LifecycleOwner) {
        mViewModel.preLoadLiveData.observe(lifecycleOwner) {
            updatePlayerPreLoad(it)
        }
        mViewModel.videoListLiveData.observe(lifecycleOwner) {
            val videoListData = it.dataList
            LogUtil.d("videoListLiveData", "首页数据返回刷新列表")
            TimeMonitorManager.getMonitor(MonitorMC.SCENE_RCMD)
                .recordTime(MonitorMC.STAGE_RENDER_START)  // 渲染开始
            if (!mViewModel.keepPlayStatus) {
                track(1, triggerScenario = VideoMC.PLAY_END_LIST_REFRESH_IN_RCMD)
                clickPause = false
                playerStop()
                mPlayerController.cancelAllPreload()
                mHasMore = it.hasMore
                //遍历资源,添加到列表播放器当中,刷新先移除原来的地址
                videoListData?.forEach { videoData ->
                    LogUtil.d(TAG_RESOLUTION, "推荐页 视频地址：${videoData.m3u8720pUrl}")
                    videoData.content?.getUrl()?.let { url ->
                        mPlayerController.setPlayerInfo(
                            PlayerInfo(
                                videoData.bookId,
                                videoData.bookId + videoData.chapterId,
                                url,
                                videoData.index,
                                videoData.bookId
                            )
                        )
                    }
                }
                setData(videoListData)
                updateShare(
                    mViewModel.mWxShareConfigVo?.isWxSharedInPlayUI() == true,
                    mViewModel.mWxShareConfigVo?.showNormalIcon(),
                    mViewModel.mVideoInfo?.getShareNum()
                )
                LogUtil.d("videoListLiveData", "首页数据返回刷新列表   updateShare")
            } else {
                LogUtil.d(HomeMC.AD_TAG, "增加或删除数据")
                mViewModel.keepPlayStatus = false
                mCurrentPosition = mViewModel.currentPosition
                videoListData?.let {
                    mPageAdapter.updateItemsAsync(videoListData)
                    LogUtil.d("videoListLiveData", "首页数据返回刷新列表   updateItemsAsync")
                }
            }
            when (it.freshType) {
                BBaseMC.REFRESH_TYPE_PULL,
                BBaseMC.REFRESH_TYPE_RECOMMEND_FRESH,
                BBaseMC.REFRESH_TYPE_LOGIN,
                BBaseMC.REFRESH_TYPE_LOGOUT,
                BBaseMC.REFRESH_TYPE_CLICK_TAB,
                BBaseMC.REFRESH_TYPE_FORCE_LAUNCH,
                BBaseMC.REFRESH_TYPE_UPDATE_LABEL -> {
                    LogUtil.d("videoListLiveData", "滑动到顶部")
                    mViewModel.clearPause()
                    mViewBinding.vp.setCurrentItem(0, false)
                    mViewBinding.vp.post {
                        videoListData?.firstOrNull()?.let { data ->
//                            mViewModel.reqCommentNum(data)
//                            mViewModel.updateVideoCommentNum(0)
                            data.syncVideoInfo(mViewModel.currentChapter)
                        }
                    }
                }

                else -> {
                    LogUtil.d("videoListLiveData", "保持当前位置不动")
                }
            }
            MainME.get().loadPauseAd().post(mViewModel.mVideoInfo)
        }
        mViewModel.continueWatchVo.observe(lifecycleOwner) {
            mViewModel.continueWatchVoRecommend = it
            LogUtil.d("continueWatchVo", "多播收到continueWatchVo")
            HomeME.get().postWatchVo().postSticky(it)
            hasShowTime = 0.0
            mPlayerController?.setWatchingVisibility(false, mViewModel.continueWatchVoRecommend)
            if (it?.continueWatch?.showBook == mViewModel.mVideoInfo?.bookId ) {
                recommendContinueWatching(data = it)
            }
        }
        mViewModel.moreVideoListLiveData.observe(lifecycleOwner) {
            val videoListData = it.dataList
            mHasMore = it.hasMore
            //遍历资源,添加到列表播放器当中
            videoListData?.forEach { videoData ->
                videoData.content?.getUrl()?.let { url ->
                    mPlayerController.setPlayerInfo(
                        PlayerInfo(
                            videoData.bookId,
                            videoData.bookId + videoData.chapterId,
                            url,
                            videoData.index,
                            videoData.bookId
                        )
                    )
                }
            }
            addMoreData(videoListData)
            if (it.needFlipPage == true) {
                if (mCurrentPosition + 1 < mPageAdapter.itemCount && mViewModel.mVideoAndAdList.isNotEmpty() && mViewModel.mVideoAndAdList.size > mCurrentPosition + 1 && (mViewModel.mVideoAndAdList[mCurrentPosition + 1].cardType == CardType.VIDEO || mViewModel.getDrawAd() != null)) {
                    selectVideo(mCurrentPosition + 1, VideoMC.PLAY_END_AUTO_SWAP)
                } else if (mViewModel.mVideoAndAdList.isNotEmpty() && mViewModel.mVideoAndAdList.size > mCurrentPosition + 2 && mCurrentPosition + 2 < mPageAdapter.itemCount) {
                    selectVideo(mCurrentPosition + 2, VideoMC.PLAY_END_AUTO_SWAP)
                }
            } else {
                mViewModel.mVideoInfo?.let { videoInfo ->
                    moveTo(videoInfo.bookId + videoInfo.chapterId)
                }
            }
            updateShare(
                mViewModel.mWxShareConfigVo?.isWxSharedInPlayUI() == true,
                mViewModel.mWxShareConfigVo?.showNormalIcon(), mViewModel.mVideoInfo?.getShareNum()
            )
        }

        mViewModel.favoriteStatusLiveData.observe(lifecycleOwner) {
            mViewModel.mVideoInfo?.run {
                inBookShelf = it
                if (inBookShelf == true) {
                    ToastManager.showToast(R.string.bbase_add_favorite_hint)
                    mViewBinding.vp.postDelayed({
                        BCommonMS.get()?.openPush(
                            BCommonMC.PUSH_SHOW_LOCATION_HOME,
                            BCommonMC.PUSH_SHOW_TYPE_COLLECT,
                            true
                        )
                    }, 1500)
                }
                HomeME.get().refreshFavorite().post(null)
                setFavoriteNum(inBookShelf == true)
                favoriteStatus(inBookShelf == true, getFavoriteNum2())
                mPageAdapter.setItem(mCurrentPosition, this)
            }
        }
        //点赞与取消点赞接口请求成功，数据回调
        mViewModel.likesLiveData.observe(lifecycleOwner) {
            if (it?.status == 1) {//请求成功
                // 当前点赞状态取反。==> 已点赞，变为取消点赞。取消点赞，变为点赞状态
                val likesKey =
                    mViewModel.mVideoInfo?.bookId + "_" + mViewModel.mVideoInfo?.chapterId
                val isLikeTo = !isLikes()
                mViewModel.mVideoInfo?.likesChecked = isLikeTo
                LogUtil.d("likes_Status", "推荐页面，移除点赞：key==$likesKey,,,isLiked==$isLikeTo")
                LogUtil.d(BBaseMC.TAG_LIKES, "点赞与取消点赞接口请求成功,显示点赞==${isLikeTo}")
                likesStatus(
                    isLikeTo,
                    mViewModel.mVideoInfo?.getRealLikesNum(if (isLikeTo) 1 else -1)
                )
                mViewModel.updateLikes(isLikeTo, likesKey)

                if (isLikeTo) {//当前是已点赞状态，则表示本次操作取消点赞成功
                    BCommonMS.get()?.openPush(
                        BCommonMC.PUSH_SHOW_LOCATION_HOME, BCommonMC.PUSH_SHOW_TYPE_LIKE, true
                    )
                }
                FlutterMS.get()?.sendEventToFlutter(
                    "theaterLikeAction",
                    mapOf(
                        "value" to isLikeTo,
                        "bookId" to mViewModel.mVideoInfo?.bookId,
                        "chapterId" to mViewModel.mVideoInfo?.chapterId
                    )
                )
            } else {//请求失败

            }
        }
        mViewModel.playing.observe(lifecycleOwner) { playing ->
            if (playing) {
                resumePlay()
            } else {
                playerPause()
            }
        }
        //评论接口返回
        mViewModel.commentNumLiveData.observe(lifecycleOwner) { data ->
            updateCommentNum(data)
            data?.commentNumMap.let {
                it?.forEach { item ->
                    updateCommentNum(
                        CommentNumBean(
                            commentNum = item.value,
                            chapterId = item.key,
                            bookId = data?.bookId
                        )
                    )
                }
            }
        }

        //刷新预加载配置
        mViewModel.preLoadChapterList.observe(lifecycleOwner) { data ->
            LogUtil.d(HomeMC.START_PLAY_TAG, "动态预加载 chapterlist = $data")
            mViewModel.reqCommentNum(mViewModel.mVideoInfo, data)
        }


        //本地查询数据库
        mViewModel.commentLocalData.observe(lifecycleOwner) { data ->
            LogUtil.d(HomeMC.START_PLAY_TAG, "动态预加载 chapterlistlocal = $data")
            mViewModel.getCommentCountByIdentifier(data)

        }

        //刷新本地数据预加载配置
        mViewModel.commentLocalItemData.observe(lifecycleOwner) { data ->
            LogUtil.d(HomeMC.START_PLAY_TAG, "动态预加载 commentLocalItemData = $data")
            updateLocalCommentLocalNum(data)
        }

        mViewModel.updateRewardProgress.observe(lifecycleOwner) {
            LogUtil.d(
                WelfareMC.TAG_REPORT, "updateRewardProgress progress:${getViewModel().taskProgress}"
            )
            (welfareWidget as? IProgressPendantComp)?.setProgress(getViewModel().taskProgress)
        }
        mViewModel.rewardStatus.observe(lifecycleOwner) {
            LogUtil.d(WelfareMC.TAG_REPORT, "福利奖励状态变更：$it")
            // 这里重新计算当前的阶段任务阶段，以及任务的状态
            updatePendantState(getViewModel().lastStageUpdateScene)
            // 上面计算出最新的状态后，刷新进度图挂件的UI
            refreshProgressPendant()
            // 打点上报
            if (it != null && waitRewardStatusToReport) {
                // 标记下次不用再曝光了
                waitRewardStatusToReport = false
                // 上报挂件曝光打点
                reportPendantExposure(
                    // 获取进度条挂件的配置信息
                    OperationMS.get()?.getPendantConfig(WelfareMC.POSITION_HOME)
                )
            }
        }

        // 2.9.0需求 1113接口下发：首页播放器UI布局配置
        mViewModel.recommendUIData.observe(lifecycleOwner) {
            mPlayerController.playerComp?.setCompUI(it)
        }
    }
    fun getCurrentNum()
    {
        LogUtil.d(HomeMC.START_PLAY_TAG, "动态预加载 mViewModel.currentVideInfo?.chapterId= ${mViewModel.currentVideInfo?.chapterId} ")
        mPageAdapter.getData().forEachIndexed { index, videoInfoVo ->
            if(mViewModel.currentChapter?.chapterId ==  videoInfoVo.chapterId){
                LogUtil.d(HomeMC.START_PLAY_TAG, "动态预加载 curChapterCommentNum = ${videoInfoVo.curChapterCommentNum} ")
                videoInfoVo.curChapterCommentNum?.let { mViewModel.syncCommentNum(it) }
            }
        }
    }
    /**
     * 更新评论数
     */
    private fun updateCommentNum(data: CommentNumBean? , isAddLocal : Boolean? = false) {
        LogUtil.d(HomeMC.START_PLAY_TAG, "动态预加载 评论数  data 多播= $data")
        mPageAdapter.getData().forEachIndexed { index, videoInfoVo ->
            if (data?.chapterId == videoInfoVo.chapterId) {
                videoInfoVo.serverCommentNum = data?.commentNum ?: 0
                if (videoInfoVo.localCommentNum != -1L && isAddLocal == false) {
                    videoInfoVo.curChapterCommentNum =
                        videoInfoVo.localCommentNum + videoInfoVo.serverCommentNum
                    getCurrentNum()
                }
                if(isAddLocal == true)
                {
                    videoInfoVo.curChapterCommentNum = videoInfoVo.serverCommentNum
                }
                mPageAdapter.setItem(index, videoInfoVo)
                mPageAdapter.notifyItemRangeChanged(
                    index, 1, HomeMC.PAYLOAD_UPDATE_ITEM_DATA
                )
            }
        }
    }

    /**
     * 更新本地评论数
     */
    private fun updateLocalCommentLocalNum(data: List<CommentNumCheckDatabaseBean?>) {
        LogUtil.d(HomeMC.START_PLAY_TAG, "动态预加载 本地评论数 data = $data")
        // 遍历每一条数据
        data.forEach { commentNumCheckDatabaseBean ->
            // 如果 commentNumCheckDatabaseBean 非空且存在 chapterId
            commentNumCheckDatabaseBean?.let {
                mPageAdapter.getData().forEachIndexed { index, videoInfoVo ->
                    if (it.chapterId == videoInfoVo.chapterId) {
                        // 更新本地评论数
                        videoInfoVo.localCommentNum = it.commentNum ?: 0L

                        // 如果服务器评论数不为 -1，则更新当前章节评论数
                        if (videoInfoVo.serverCommentNum != -1L) {
                            videoInfoVo.curChapterCommentNum =
                                videoInfoVo.localCommentNum + videoInfoVo.serverCommentNum
                            getCurrentNum()
                        }

                        // 更新 adapter 中的数据
                        mPageAdapter.setItem(index, videoInfoVo)
                        mPageAdapter.notifyItemRangeChanged(
                            index,
                            1,
                            HomeMC.PAYLOAD_UPDATE_ITEM_DATA
                        )
                    }
                }
            }
        }
    }

    fun startPlayingTimer(reason: String? = null) {
        if (!isFragmentVisible()) {
            return
        }
        mViewModel.mVideoInfo?.bookId?.let {
            PlayingStatisticsMgr.startPlayingTimer(PlayScene.HOME, reason, it,
                reportAward = {
                    mViewModel.reportAward()
                }, conditionsNoMet = {
                    playingTimer()
                })
        }
    }

    //显示福利引导金币
    override fun showEarnedGoldCoins() {
        mViewBinding.refreshView
        mViewBinding.clEarnedGoldCoins.visibility = View.VISIBLE
        mViewBinding.tvEarnedGoldCoins.text =
            "已赚${mViewModel.nextStage?.award ?: 0}金币"
        TaskManager.delayTask(1000) {
            mViewBinding.clEarnedGoldCoins.visibility =
                View.GONE
        }
    }

    fun stopPlayingTimer(reason: String? = null) {
        PlayingStatisticsMgr.stopPlayingTimer(playScene = PlayScene.HOME)
    }

    /**
     * 更新播放器预加载策略
     */
    private fun updatePlayerPreLoad(preLoadFunInfoVo: PreLoadFunSwitchVo?) {
        preLoadFunInfoVo?.let { info ->
            mPlayerController.setPreloadCount(info.preLoadNum)
        }
    }
    private var hasShowTime: Double = 0.0//计时
    private var coroutineScope: CoroutineScope? = CoroutineUtils.getCoroutineScope()
    private var recommendContinueWatchingDialog: ContinueWatchFloatComp? = null
    private fun recommendContinueWatching(data: ContinueWatchVo?) {
        LogUtil.d("continueWatchVo", "多播进入方法")

        if (NotificationStyleLikeDialogUtils.isShowingFloatComp) {
            LogUtil.d(TAG,"checkCanShowNotificationLikeDialog，有悬浮窗正在显示，最近观看浮窗不显示-->")
            return
        }

        data.let {
            coroutineScope?.cancel()
            coroutineScope = null
            if (isOnResumed()) {
                coroutineScope = if (coroutineScope == null) {
                    CoroutineUtils.getCoroutineScope()
                } else {
                    coroutineScope?.cancel()
                    coroutineScope = null
                    CoroutineUtils.getCoroutineScope()
                }
                val continueWatchIntent = HomeMR.get().continueWatchComp().apply {
                    continueWatchVo = data
                    activityPageId = <EMAIL>()
                }.onShow {
                    NotificationStyleLikeDialogUtils.isShowingFloatComp = true
                    recommendContinueWatchingDialog = it as ContinueWatchFloatComp
                }.onDismiss {
                    NotificationStyleLikeDialogUtils.isShowingFloatComp = false
                    MainME.get().floatCompDisplayStatus().post(2)
                    recommendContinueWatchingDialog = null
                }.onClose {
                    coroutineScope?.cancel()
                }
                PriorityTaskManager.addTask(
                    PriorityDialogTask(
                        PriorityMC.TASK_CONTINUE_WATCH,
                        PageConstant.HOME_ID,
                        PriorityMC.PRIORITY_MAIN_CONTINUE_WATCH,
                        continueWatchIntent
                    )
                )
                PriorityTaskManager.executeTask()

                mViewModel.continueWatchVoRecommend?.continueWatch?.isShow = 1
            }
            if (isOnResumed()) {
                coroutineScope?.launch {
                    while (hasShowTime <= if (data?.continueWatch?.littleCardSwitch == 1) (data?.continueWatch?.littleCardTime
                            ?: 0) else (data?.continueWatch?.longCardVanishTime ?: 0)
                    ) {
                        delay(100)
                        hasShowTime += 0.1
                        LogUtil.d(TAG_PLAYER, "播放器继续播放弹窗展示时间：${hasShowTime}s")
                    }
                    if (data?.continueWatch?.littleCardSwitch == 0) {
                        BBaseKV.lastTimeShowContinueCompInOneLife = System.currentTimeMillis()
                    }
                    MainScope().launch(Dispatchers.Main) {
                        recommendContinueWatchingDialog?.dismiss()
                    }
                    mViewModel.trackPopExposure()

                    mPlayerController?.setWatchingVisibility(
                        true,
                        mViewModel.continueWatchVoRecommend
                    )
                    mViewModel.isShowComp = true
                    coroutineScope?.cancel()
                }
            }

        }
    }

    private fun checkPositionAndUpdateUI(position: Int) {
        if (position == 0) {
            HomeME.get().continueResume().post(Any())
            if (mViewModel.isShowComp) {
                mPlayerController?.setWatchingVisibility(
                    true,
                    mViewModel.continueWatchVoRecommend
                )
            }
        } else {
            mPlayerController?.setWatchingVisibility(
                false,
                mViewModel.continueWatchVoRecommend
            )
            HomeME.get().invisibleComp().post(Any())
        }
    }

    fun isOnResumed(): Boolean {
        return isResumed && !mViewModel.parentPaused && !LocalActivityMgr.isBackground()
    }

    //第一帧是否已经渲染
    private var firstRendering = false
    private var playCount = 0
    var updateHistory = false

    private fun getCurrentVideoInfoVo(): VideoInfoVo? {
        if (mViewBinding.vp.currentItem < 0 || mViewBinding.vp.currentItem >= mPageAdapter.itemCount) {
            return null
        }
        return mPageAdapter.getData(mViewBinding.vp.currentItem)
    }

    /**
     * 初始化列表播放器
     */
    private fun initLPlayerListener() {
        mPlayerController.initPlayerListener(object : INewPlayerListener {
            override fun onPrepared(position: Int, vid: String?) {
                LogUtil.d(TAG,"推荐页视频准备起播 isSplashShowing=${SplashDisplayStateManager.isSplashShowing()}")
                if (vid != getCurrentVideoInfoVo()?.bookId) {
                    printIntercept(position,vid,"onPrepared")
                    return
                }
                MainMC.isVideoStartPlay = true
                isVideoReady = true
                if (!SplashDisplayStateManager.isSplashShowing()){
                    playerPrepared()
                }else{
                    playerPause()
                    enableGesture(true)
                }
            }

            override fun onInfo(
                code: Int,
                msg: String?,
                value: Long,
                position: Int,
                vid: String?,
                prohibitPlay: ((isCurrPlayer: Boolean) -> Unit)
            ) {
                playerOnInfo(code, position, vid, value, prohibitPlay)
            }

            override fun onSeekComplete(position: Int, vid: String?) {
                playerSeekComplete()
            }

            override fun onCompletion(position: Int, vid: String?) {
                if (vid != getCurrentVideoInfoVo()?.bookId) {
                    printIntercept(position,vid,"onCompletion")
                    kotlin.runCatching {
                        track(1, triggerScenario = VideoMC.PLAY_END_COMPLETE_SPECIAL)
                    }
                    return
                }
                mViewModel.updatePlayerState(PlayState.COMPLETION)
                playerCompletion()
            }

            override fun onPlayStateChanged(status: Int, position: Int, vid: String?) {
                if (vid != getCurrentVideoInfoVo()?.bookId) {
                    printIntercept(position,vid,"onPlayStateChanged")
                    return
                }
                playerPlayStateChanged(status)
            }

            override fun onRenderingStart(position: Int, vid: String?) {
                if (vid != getCurrentVideoInfoVo()?.bookId) {
                    printIntercept(position,vid,"onRenderingStart")
                    mViewModel.interceptStartPlayTrack(
                        mViewBinding.vp.currentItem,
                        position,
                        vid,
                        getCurrentVideoInfoVo()?.bookId
                    )

                    //开屏页显示的时候，先暂停播放
                    if (SplashDisplayStateManager.isSplashShowing()){
                        LogUtil.d(TAG,"11视频首帧渲染，但是开屏页在显示，暂停播放~~~")
                        playerPause()
                    }
                    return
                }
                playerRenderingStart()
            }

            override fun onError(errorCode: Int, errorMsg: String, extra: String?, position: Int, vid: String?) {
                if (vid != getCurrentVideoInfoVo()?.bookId) {
                    printIntercept(position,vid,"onError")
                    return
                }
                playerOnError(errorCode, errorMsg, extra)
            }

            override fun onLoadingBegin(position: Int, vid: String?) {
                LogUtil.d("new_recommend_load_tag", "onLoadingBegin   bookname==${mViewModel.mVideoInfo?.bookName}")
                val duration = if (getPlayerDuration() > 0) getPlayerDuration() else currentVideoDuration
                mViewModel.sensorPreload(
                    mViewModel.mVideoInfo,
                    0,
                    duration,
                    mCurrentPosition,
                    true,
                    getDownloadBitrate()
                )
                if (vid != getCurrentVideoInfoVo()?.bookId) {
                    printIntercept(position,vid,"onLoadingBegin")
                    return
                }
                playerLoadingBegin()
            }

            override fun onLoadingEnd(position: Int, vid: String?) {
                LogUtil.d("new_recommend_load_tag", "onLoadingEnd   bookname==${mViewModel.mVideoInfo?.bookName}")
                mViewModel.sensorPreload(mViewModel.mVideoInfo, 1, mPlayerController.getLoadingTime(), mCurrentPosition, true)
                if (vid != getCurrentVideoInfoVo()?.bookId) {
                    printIntercept(position,vid,"onLoadingEnd")
                    return
                }
                playerLoadingEnd()
            }

            override fun onPlaySpeedChanged(speed: Float, position: Int, vid: String?) {
                if (vid != getCurrentVideoInfoVo()?.bookId) {
                    printIntercept(position,vid,"onPlaySpeedChanged")
                    return
                }
                mVideoLifecycle.onPlaySpeedChanged(speed)
            }
        }
        )
    }

    private fun printIntercept(position: Int, vid: String?, method: String) {
        LogUtil.d(
            "recommend_player_intercept", "播放器回调被拦截，当前position==$position   " +
                    "currentItem==${mViewBinding.vp.currentItem}" +
                    "vid==$vid   " +
                    "chapterId==${getCurrentVideoInfoVo()?.bookId}  " +
                    "method==${method}"
        )
    }

    private var loadingBegin = false

    /**
     * 播放器onSeekComplete
     */
    private fun playerSeekComplete() {
        if (!loadingBegin) {
            mViewModel.loadingScene = ReadingTE.LOADING_SCENE_PLAYING
            LogUtil.d(HomeMC.APP_LOADING_TAG, "onSeekComplete结束，loadingScene设置为播放过程中")
        }
    }

    private var maxRemainBuffer = 0L

    /**
     * 播放器onInfo
     */
    private fun playerOnInfo(
        code: Int,
        position: Int,
        vid: String?,
        value: Long,
        prohibitPlay: ((isCurrPlayer: Boolean) -> Unit)
    ) {
        prohibitPlay(vid == getCurrentVideoInfoVo()?.bookId)
        when (code) {
            OnInfoListener.AutoPlayStart -> {
                if (vid != getCurrentVideoInfoVo()?.bookId) {
                    printIntercept(position,vid,"OnInfoListener.AutoPlayStart")
                    return
                }
                // 视频已经开始播放，记录下当前视频的时长
                currentVideoDuration = getPlayerDuration()
            }

            OnInfoListener.CurrentPosition -> {
                prohibitPlay(position == mViewBinding.vp.currentItem)
                if (vid != getCurrentVideoInfoVo()?.bookId) {
                    printIntercept(position,vid,"OnInfoListener.CurrentPosition")
                    return
                }
                mVideoLifecycle.onPlayProgressChanged(value, getPlayerDuration())
                currentDuration = value
                mViewModel.played = true
                LogUtil.d(
                    HomeMC.APP_ERROR_TAG,
                    "播放器进度回调，played ==true  currentDuration ==$currentDuration"
                )
                LogUtil.d(
                    HomeMC.APP_ERROR_TAG,
                    "播放器进度回调，value ==$value 总时间=${getPlayerDuration()} 显示时间=${(getPlayerDuration() - value) / 1000}"
                )
                if (value >= getPlayerDuration() - 4 * 1000) {
                    updateNextText((getPlayerDuration() - value) / 1000)
                }
                var current = 0L
                getPlayerDuration().let { duration ->
                    if (duration > 0) {
                        current = (value * 100 / duration)
                    }
                }
                seekBarSeekTo(current.toInt())
                showRecordNumberByTime(value)
                //上报播放事件
                if (startReportDuration == -1L && value < getPlayerDuration() - 6 * 1000) {
                    startReportDuration = value
                }
                if (!isOnResumed() || !mViewModel.canPlay()) {
                    playerPause()
                }
                if (firstRendering || playCount <= 1) {
                    //                            changeHolderCover(mCurrentPosition, View.GONE)
                    firstRendering = false
                    if (ViewPager2Helper.findViewHolderByPosition(
                            mViewBinding.vp,
                            mCurrentPosition
                        ) !is NewRecommendVideoViewHolder
                    ) {
                        playerPause()
                    }
                }
                if (updateHistory) {
                    mViewModel.updateViewHistory()
                    updateHistory = false
                }
                playCount++
                if (BBaseMC.isShowMainDialog == true) {
                    setOnBackground(true)
                }
                reqBitrate()
            }

            OnInfoListener.BufferedPosition -> {
                if (vid != getCurrentVideoInfoVo()?.bookId) {
                    printIntercept(position,vid,"OnInfoListener.BufferedPosition")
                    return
                }
                val bufferDuration = value - currentDuration
                if (maxRemainBuffer < bufferDuration) {
                    maxRemainBuffer = bufferDuration
                }
            }

            109 -> {
                //边播边缓存成功
            }

            110 -> {
                //边播边缓存失败
            }
        }
    }

    /**
     * 播放器onError
     */
    private fun playerOnError(errorCode: Int, errorMsg: String, extra: String?) {
        loadingBegin = false
        SplashMS.get()?.updateColdPlayerPrepareDisplayTime(false)
        mViewBinding.compLoading.dismiss()
        //播放报错打点
        trackPlayError(errorCode, errorMsg, extra)
        //播放报错：检测是否需要重置备用地址下标 或者 停止播放
        checkSwitchStateAndStop()

        if (BBaseKV.platformConfig == 1) {
            if (BBaseMC.hasWelfareMall) {
                BBaseME.get().onVideoFirstFrame().post(PageLazyTag.FRAGMENT_WELFARE_TAB)
            } else {
                BBaseME.get().onVideoFirstFrame().post(PageLazyTag.FRAGMENT_WELFARE)
            }
        } else {
            BBaseME.get().onVideoFirstFrame().post(PageLazyTag.FRAGMENT_THEATRE_CONTAINER)
        }
    }

    /**
     * 播放器onLoadingEnd
     */
    private fun playerLoadingEnd() {
        loadingBegin = false
        startPlayTime = System.currentTimeMillis()
        mViewModel.printLog(
            TAG_PLAYING_DURATION, "累计播放时长 onLoadingEnd 记录startPlayTime"
        )
        mViewBinding.compLoading.dismiss()
        LogUtil.d(
            HomeMC.APP_LOADING_TAG,
            "loading结束，开始打点，触发场景为${mViewModel.loadingScene}"
        )
        mViewModel.loadingScene = ReadingTE.LOADING_SCENE_PLAYING
        LogUtil.d(
            HomeMC.APP_LOADING_TAG,
            "loading结束，loadingScene设置为播放过程中"
        )
        mVideoLifecycle.onVideoLoadingEnd()
        startPlayingTimer()
    }

    /**
     * 播放器onLoadingBegin
     */
    private fun playerLoadingBegin() {
        loadingBegin = true
        if (startPlayTime > 0) {
            playingTime += (System.currentTimeMillis() - startPlayTime)
            startPlayTime = 0L
            mViewModel.printLog(
                TAG_PLAYING_DURATION,
                "累计播放时长 onLoadingBegin 累加playingTime，startPlayTime清零"
            )
        }
        if (isFragmentVisible()) {
            stopPlayingTimer("onLoadingBegin")
        }
        mViewBinding.compLoading.show()
        mVideoLifecycle.onVideoLoadingBegin()
    }

    /**
     * 播放器onPlayStateChanged
     */
    private fun playerPlayStateChanged(status: Int) {
        LogUtil.d(TAG_PLAYER, "一级播放页 onPlayStateChanged: $status")
        isPlaying = status == OnStateChangedListener.started
        when (status) {
            OnStateChangedListener.initalized -> {  // 有时候 onRenderingStart 不回调
                TimeMonitorManager.getMonitor(MonitorMC.SCENE_RCMD).let { timeMonitor ->
                    if (!timeMonitor.isEnd) {
                        timeMonitor.recordTime(MonitorMC.STAGE_VIDEO_PREPARE_END)
                        timeMonitor.recordTime(MonitorMC.STAGE_END)
                        timeMonitor.trackToSensor()
                        timeMonitor.end()
                    }
                }
            }

            OnStateChangedListener.started -> {  // 播放中
                startPlayTime = System.currentTimeMillis()
                mViewModel.printLog(
                    TAG_PLAYING_DURATION,
                    "累计播放时长 onPlayStateChanged started 记录startPlayTime"
                )
                mVideoLifecycle.onPlayStart(
                    mViewModel.currentVideInfo?.bookId ?: "",
                    mViewModel.currentVideInfo?.chapterId ?: ""
                )
                startPlayingTimer()
                mViewModel.updatePlayerState(PlayState.PLAYING)
                if ((mViewModel.currentVideInfo?.content?.contentUlrIndex ?: -1) > -1 &&
                    (!mViewModel.currentVideInfo?.content?.getUrlList().isNullOrEmpty()) &&
                    (mViewModel.currentVideInfo?.content?.getUrlList()?.size
                        ?: 0) > (mViewModel.currentVideInfo?.content?.contentUlrIndex ?: 0)
                ) {

                    //播放成功 重置为切换地址默认状态
                    mViewModel.currentVideInfo?.content?.switchState = SwitchState.NO_SWITCH
                }
            }

            OnStateChangedListener.paused -> {  // 暂停
                playCount = 0
                LogUtil.d(TAG, "暂停播放")
                track(1)
                mVideoLifecycle.onPlayPause(
                    mViewModel.currentVideInfo?.bookId ?: "",
                    mViewModel.currentVideInfo?.chapterId ?: ""
                )
                mViewModel.updatePlayerState(PlayState.PAUSING)
            }

            OnStateChangedListener.stopped -> {
                showRecordNumberByTime(-1)
                playCount = 0
                mViewBinding.compLoading.dismiss()
                if (!NetWorkUtil.isNetConnected(context)) {
                    ToastManager.showToast(getString(R.string.bbase_not_network))
                }
                mVideoLifecycle.onPlayStop()
                mViewModel.updatePlayerState(PlayState.STOP)
            }

            OnStateChangedListener.error -> {  // 错误
                playCount = 0
                mVideoLifecycle.onPlayError(
                    mViewModel.currentVideInfo?.bookId ?: "",
                    mViewModel.currentVideInfo?.chapterId ?: ""
                )
                mViewModel.updatePlayerState(PlayState.ERROR)
            }
        }
    }

    /**
     * 播放器onCompletion
     */
    private fun playerCompletion() {
        LogUtil.d(TAG, "onCompletion 当前集播放结束")
        mVideoLifecycle.onPlayCompletion(
            mViewModel.currentVideInfo?.bookId ?: "",
            mViewModel.currentVideInfo?.chapterId ?: ""
        )
//        DialogMS.get()?.let {
//            if (it.isInCommentInterval() == false) {  // 为null、true不处理
//                ActionRecorder.recordAction(ActionRecorder.ACTION_CHAPTER_COMPLETED)
//            }
//        }
        ActionRecorder.recordAction(ActionRecorder.ACTION_CHAPTER_COMPLETED)
        //进度条拖动不做任何自动跳转
        if (isDragging()) {
            track(1, triggerScenario = VideoMC.PLAY_END_COMPLETE)
        } else if (mViewModel.mVideoInfo?.chapterId == mViewModel.mVideoInfo?.maxChapterId) {
            LogUtil.d(TAG, "onCompletion scrollToNextVideo")
            introDialog?.dismiss()
            scrollToNextVideo()
        } else {
            track(1, triggerScenario = VideoMC.PLAY_END_COMPLETE)
            seekTo(0, false)
            startVideoList(DetailMC.LAUNCH_TYPE_NEXT, EnterTypeMode.HOME)
        }
    }

    /**
     * 播放器onPrepared成功
     */
    private fun playerPrepared() {
        mPlayerController.playerComp?.setThumbnailListener()
        LogUtil.d(TAG,"视频开始播放")
        startPlayTime = 0L
        playingTime = 0L
        mViewModel.printLog(
            TAG_PLAYING_DURATION,
            "累计播放时长 onPlayStateChanged prepared 恢复初始值"
        )
        SplashMS.get()?.updateColdPlayerPrepareDisplayTime(true)

        LogUtil.d(
            TAG_APP_LAUNCH,
            "App启动 -> 视频起播耗时:${System.currentTimeMillis() - SpeedUtil.appAttachTime}"
        )
        firstRendering = false
        playCount = 0
        recordPrepared(1)
        recordRenderingStart(0)
        LogUtil.d(HomeMC.PLAYER_START_PLAY_TIME_TAG, "onPrepared回调")
        playerStart()
        if (!getPlayerPauseStatus() && !mIsOnBackground && isOnResumed()) {
            resumePlay()
            isWatching = true
        }
        enableGesture(true)
        pausedByNetwork = false

        //视频起播后延迟2秒开始预加载福利页激励视频
        delayTask(2000) {
            LogUtil.d("XXX","视频起播2秒后,没显示开屏页，触发预加载-->")
            WebMS.get()?.preloadWelfareRewardAd(0)
        }
    }

    /**
     * 播放器RenderingStart
     */
    private fun playerRenderingStart(isZeroFirstFrame: Boolean = false) {
        LogUtil.d(TAG_PLAYER, "一级播放页 onRenderingStart")
        mViewModel.played = true
        maxRemainBuffer = 0
        LogUtil.d(HomeMC.APP_ERROR_TAG, "首帧渲染回调，played ==true")
        TimeMonitorManager.getMonitor(MonitorMC.SCENE_RCMD).let { timeMonitor ->
            if (!timeMonitor.isEnd) {
                timeMonitor.recordTime(MonitorMC.STAGE_VIDEO_PREPARE_END)
                timeMonitor.recordTime(MonitorMC.STAGE_END)
                timeMonitor.trackToSensor()
                timeMonitor.end()
            }
        }
        recordRenderingStart(1)
        recordUserSenseTime(1)
        LogUtil.d(HomeMC.PLAYER_START_PLAY_TIME_TAG, "首帧播放回调")
        track(0, mPlayerController.isPreRenderPlayer(), isPlayerPrePrepared)
        isPlayerPrePrepared = false
        isWatching = true
        mViewBinding.compLoading.dismiss()
        //                changeHolderCover(mCurrentPosition, View.GONE)
        startReportDuration = -1L
        cReportBookId = null
        firstRendering = true
        if (getPlayerPauseStatus() || mIsOnBackground || !isOnResumed()) {
            playerPause()
        } else {
            mViewModel.updateViewHistory()
        }

        //视频起播以后其它页面开始初始化
        TimeMonitorManager.getMonitor(MonitorMC.SCENE_LAUNCH).apply {
            recordTime(MonitorMC.STAGE_RCMD_FIRST_VIDEO_FRAME)
            printLaunchDuration()
            end()
        }
        if (mViewBinding.vp.offscreenPageLimit == ViewPager2.OFFSCREEN_PAGE_LIMIT_DEFAULT) {
            mViewBinding.vp.offscreenPageLimit = 1
        }
        if (BBaseKV.platformConfig == 1) {
            if (BBaseMC.hasWelfareMall) {
                BBaseME.get().onVideoFirstFrame().post(PageLazyTag.FRAGMENT_WELFARE_TAB)
            } else {
                BBaseME.get().onVideoFirstFrame().post(PageLazyTag.FRAGMENT_WELFARE)
            }
        } else {
            BBaseME.get().onVideoFirstFrame().post(PageLazyTag.FRAGMENT_THEATRE_CONTAINER)
        }
    }

    private fun reqBitrate() {
        getRenderFPS().let {
            LogUtil.d("Bitrate_Tag", "推荐页renderFps=$it")
            if (it != 0f) {
                mViewModel.renderFps = it
            }
        }
        getAudioBitrate().let {
            LogUtil.d("Bitrate_Tag", "推荐页audioBitrate=$it")
            if (it != 0f) {
                mViewModel.audioBitrate = it
            }
        }
        getVideoBitrate().let {
            LogUtil.d("Bitrate_Tag", "推荐页videoBitrate=$it")
            if (it != 0f) {
                mViewModel.videoBitrate = it
            }
        }
        getDownloadBitrate().let {
            LogUtil.d("Bitrate_Tag", "推荐页downloadBitrate=$it")
            if (it != 0f) {
                mViewModel.downloadBitrate = it ?: 0f
            }
        }
    }

    /**
     * 上报切换地址事件
     */
    fun switchStatusTrack(type: String, url: String?) {
        val msgObj = JSONObject()
        kotlin.runCatching {
            msgObj.put("url", url)
            msgObj.put("errorMessage", type)
        }
        DzTrackEvents.get().error().pageInitFirstPlay(mViewModel.isFirstPlay).type(type)
            .message(msgObj.toString()).track()
    }

    /**
     * 播放报错打点
     */
    private fun trackPlayError(errorCode: Int, errorMsg: String, extra: String?) {
        //播放报错上报过滤：切换地址的报错 和 默认播放报错上报
        if (mViewModel.currentVideInfo?.content?.switchState == SwitchState.SWITCH_ED) {// 切换完成播放报错打点
            //获取 当前报错的url
            var url = mViewModel.currentVideInfo?.content?.getUrl()
            if ((mViewModel.currentVideInfo?.content?.contentUlrIndex ?: -1) > -1) {
                url = mViewModel.currentVideInfo?.content?.getUrlList()?.get(
                    mViewModel.currentVideInfo?.content?.contentUlrIndex ?: 0
                )
            }
            //报错上报
            switchStatusTrack(ErrorTE.CODE_REPLACE_FAILED, url)
        } else {
            //正常报错上报：默认地址报错
            mViewModel.errorReport(errorCode, errorMsg, currentDuration, extra, true)
        }
    }

    /**
     * 播放报错：检测是否需要重置备用地址下标 或者 停止播放
     */
    private fun checkSwitchStateAndStop() {
        //判断是否可以切换播放地址
        if (
            mViewModel.currentVideInfo != null &&
            !mViewModel.currentVideInfo?.content?.getUrlList().isNullOrEmpty() &&
            //备用地址数量 是否大于 要切换的备用地址下标加一
            (mViewModel.currentVideInfo?.content?.getUrlList()?.size
                ?: 0) > ((mViewModel.currentVideInfo?.content?.contentUlrIndex ?: 0) + 1) &&
            NetWorkUtil.isNetConnected(context)
        ) {
            //停止播放，不移除Surface
            playerStop()
            mViewModel.currentVideInfo?.let { info ->
                //重置备用地址下标（下标+1）
                info.content?.contentUlrIndex = (info.content?.contentUlrIndex ?: -1) + 1
                //设置需要切换地址的状态
                mViewModel.currentVideInfo?.content?.switchState = SwitchState.NEED_SWITCH
                //控制进度
                setStartTime(currentDuration)
                LogUtil.d(
                    "player_start_time",
                    "播放器报错，切换播放链接，进度切换为currentDuration ==$currentDuration"
                )
                //重新播放
                mViewModel.played = false
                LogUtil.d(HomeMC.APP_ERROR_TAG, "播放器报错，播放器切换备用地址，played ==false")
                mViewModel.loadingScene = ReadingTE.LOADING_SCENE_PLAYING
                loadingBegin = false
                LogUtil.d(HomeMC.APP_LOADING_TAG, "切换剧集开始播放，loadingScene设置为播放过程中")
                changeUrlTo(info.content?.getUrl())
                moveTo(info.bookId + info.chapterId)
//                recordUserSenseTime(0)
                recordPrepared(0)
                LogUtil.d(HomeMC.PLAYER_START_PLAY_TIME_TAG, "切换剧集开始播放,调用moveTo")
            }
        } else {
            //设置需要切换地址的状态
            mViewModel.currentVideInfo?.content?.switchState = SwitchState.NO_SWITCH
            // 网络问题 用该值拦截用户点击请求等操作
            pausedByNetwork = true
            mViewModel.pausePlay(VideoMC.PLAY_END_ERROR)
        }
    }

    /**
     * 当前在播的剧集id
     */
    private var cReportBookId: String? =
        null//上报打点bookid，如果为空且上报开始，可以上报，不为空且上报为结束，可以上报；1、开始播放第一帧时或者上报结束时，初始化为空；2、上报开始播放时，设置为当前bookid
    private var startReportDuration: Long = -1L//开始播放的进度，开始播放后初始化，当播放结束恢复-1
    private fun stopScroll() {
        mViewBinding.vp.setUserInputEnabled(false)
    }

    private fun startScroll() {
        mViewBinding.vp.setUserInputEnabled(true)
    }

    private var playerHeight = 0
    private var playerWidth = 0
    private var isAllowShowFloatComp = true//是否允许展示浮窗

    /**
     * 初始化播放界面
     */
    private fun initPlayerController(videoInfo: VideoInfoVo) {
        isPlaying = false
        // 根据播放器的显示区域，设置缩放模式
        mViewBinding.vp.post {
            kotlin.runCatching {
                updateScaleMode(
                    BBaseKV.recommendHeightWidthRatio,
                    mViewModel.currentVideInfo?.isLandscapeVideo()
                )
                playerWidth = mViewBinding.vp.measuredWidth
                playerHeight = mViewBinding.vp.measuredHeight
            }.onFailure {
                LogUtil.e(TAG_PLAYER, "推荐页更新播放器适配方式出现异常：${it.message}")
            }
        }
        // 更新判断window大小
        DetailMS.get()?.updateWindowRatio()
        mPlayerController.playerComp?.run {
            mIsPause = false
            isLoop(false)
            resetPlayerSize()
            updateViewAlpha(1f)
            mCurPosition = <EMAIL>
            enableGesture(isPrepared())
            seekBarSeekTo(0)
            LogUtil.d(
                "打印",
                "推荐页面，开始播放 播放速度：BBaseKV.multipleSpeed==" + BBaseKV.multipleSpeed
            )
            setSpeed(BBaseKV.multipleSpeed)
            favoriteStatus(videoInfo.inBookShelf == true, videoInfo.getFavoriteNum2())
            if (!BBaseKV.syncLikes) {
                lifecycleScope.launch(Dispatchers.IO) {
                    videoInfo.likesChecked =
                        DBHelper.hasLikes(videoInfo.bookId + "_" + videoInfo.chapterId)
                    LogUtil.d(
                        "likes_Status",
                        "推荐页面，初始化播放器：key==" + mViewModel.mVideoInfo?.bookId + "_" + mViewModel.mVideoInfo?.chapterId + ",,,isLiked==" + mViewModel.mVideoInfo?.likesChecked
                    )
                    withContext(Dispatchers.Main) {
                        likesStatus(videoInfo.likesChecked == true, videoInfo.getRealLikesNum())
                    }
                }
            } else {
                LogUtil.d(
                    "likes_Status",
                    "推荐页面，未初始化播放器：key==" + mViewModel.mVideoInfo?.bookId + "_" + mViewModel.mVideoInfo?.chapterId + ",,,isLiked==" + mViewModel.mVideoInfo?.likesChecked
                )
                likesStatus(videoInfo.likesChecked == true, videoInfo.getRealLikesNum())
            }
            updateShare(
                mViewModel.mWxShareConfigVo?.isWxSharedInPlayUI() == true,
                mViewModel.mWxShareConfigVo?.showNormalIcon(), videoInfo.getShareNum()
            )
            playIconVisibility(View.GONE)
            ElementClickUtils.ignoreAutoTrack(getHeroView())
            ElementClickUtils.ignoreAutoTrack(getHeroineView())
            setOnGestureListener(object : PlayerControllerComp.OnGestureListener {
                override fun onSingleTapConfirmed() {
                    BBaseMC.isShowMainDialog = false
                    if (!getPlayerPauseStatus()) {
                        VideoTrackUtil.trackPause(
                            mViewModel.videoTrackInfo,
                            VideoTrackUtil.PAUSE_SCENE_USER,
                            if (mViewModel.mVideoInfo?.isLandscapeVideo() == true) "竖屏" else "",
                            firstTierPlaySource = "首页",
                            secondTierPlaySource = "首页-推荐",
                            thirdTierPlaySource = "首页-推荐",
                            isMultipleInstances = true,
                        )
                    } else {
                        VideoTrackUtil.trackPauseCancel(
                            mViewModel.videoTrackInfo, VideoTrackUtil.PAUSE_SCENE_USER, true
                        )
                    }

                    if (getPlayerPauseStatus()) {
                        mViewModel.clearPause()
                    }
                    mViewModel.allowReportInvalidPlay = false
                    onPauseClick {
                        if (getPlayerPauseStatus()) {
                            MainME.get().showPauseAd().post(mViewModel.mVideoInfo)
                        }
                    }
                }

                override fun onDoubleTap() {
                    if (!isLikes()) {
                        likesClick(false)
                    }
                }

                override fun onShareClick() {
                    shareClick()
                }

                override fun onFavoriteClick() {
                    favoriteClick()
                }

                override fun onLikeClick(isLike: Boolean) {
                    likesClick(isLike)
                }

                override fun onCommentClick(view: View, guideContent: String?) {
                    isAllowShowFloatComp = false
                    HomeME.get().invisibleComp().post(Any())
                    // 神策打点
                    kotlin.runCatching {
                        val jsonObj = JSONObject().apply {
                            put("\$element_content", "评论入口")
                            put("PositionName", "首页")
                            put("BookID", mViewModel.currentVideInfo?.bookId)
                            put("BookName", mViewModel.currentVideInfo?.bookName)
                            put("ChaptersID", mViewModel.currentVideInfo?.chapterId)
                            put("ChaptersNum", mViewModel.currentVideInfo?.chapterIndex)
                            if (guideContent.isNullOrEmpty()) {
                                put("IsCommentGuide", "无")
                            } else {
                                put("IsCommentGuide", "有")
                                put("GuideContent", guideContent)
                            }
                        }
                        SensorTracker.trackViewAppClick(view, jsonObj)
                    }
                    // 缩放视频
                    // 弹窗高度用这个：commentDelegate.dialogHeight
                    val statusHeight = ScreenUtil.getStatusHeight(requireContext()).takeIf { it > 0 } ?: 44.dp
                    commentDelegate.showDialog()?.apply {
                        callback = object : CommentIntent.Callback {
                            override fun onDialogHeightChanged(current: Int, total: Int) {
                                val top = current * statusHeight / total
                                mPlayerController.updatePlayerSize(
                                    (playerHeight - BBaseMC.PLAYER_SCALE_RATIO * current - top).toInt(),
                                    if (mViewModel.mVideoInfo?.isLandscapeVideo() == true) {
                                        playerWidth
                                    } else {
                                        playerWidth - (BBaseMC.PLAYER_SCALE_RATIO * current + top) / BBaseKV.recommendHeightWidthRatio
                                    }.toInt(),
                                    top
                                )
                                updateViewAlpha(1 - current.toFloat() / total)
                            }

                            override fun onCommentNumChanged(num: Long) {
                                LogUtil.d(VideoMC.TAG_COMMENT, "NewRecommendFragment 评论数量变化：$num")
                                mViewModel.currentChapter.commentNum = num
                                updateCommentNum(
                                    CommentNumBean(
                                        commentNum = num,
                                        bookId = mViewModel.currentVideInfo?.bookId,
                                        chapterId = mViewModel.currentVideInfo?.chapterId
                                    ), true
                                )
                            }

                            override fun onDialogShow(currentHeight: Int, totalHeight: Int) {//弹窗展示时
                            }

                            override fun onInputClick(view: View) {
                                SensorTracker.customTrackViewAppClick(
                                    view,
                                    content = "输入评论",
                                    position = "首页",
                                    bookID = mViewModel.currentVideInfo?.bookId,
                                    bookName = mViewModel.currentVideInfo?.bookName,
                                    chaptersID = mViewModel.currentVideInfo?.chapterId,
                                    chaptersNum = mViewModel.currentVideInfo?.chapterIndex
                                )
                            }

                        }
                        addShowListener {
                            // 循环播放
                            mPlayerController.isLoop(true)
                        }
                        addDismissListener {
                            mPlayerController.isLoop(false)
                            mPlayerController.resetPlayerSize()
                            updateViewAlpha(1f)
                            isAllowShowFloatComp = true
                            HomeME.get().continueResume().post(Any())
//                            mViewBinding.clViewRoot.postDelayed({
//                                cancelTask()
//                                mPlayerController.updatePlayerSize(playerHeight, playerWidth, 0)
//                                updateViewAlpha(1f)
//                            }, 300)
                        }
                    }?.start()
                }

                override fun onSelectDramaClick() {
                    startVideoList(DetailMC.LAUNCH_TYPE_SHOW_DRAMA)
                }

                override fun onNextDramaClick(text: String) {
                    mViewModel.trackNextDrama(text)
                    startVideoList(DetailMC.LAUNCH_TYPE_CURRENT, EnterTypeMode.CLICK)
                }

                override fun onHorizontalDistance(startF: Float, endF: Float) {
                    //左滑距离大于30进入二级播放页面
                    if (startF - endF > 20) {
                        startVideoList(DetailMC.LAUNCH_TYPE_CURRENT, EnterTypeMode.SLIDE)
                    }
                }

                override fun onStartTrackingTouch() {
                    stopScroll()
                }

                override fun onStopTrackingTouch(time: Long) {
                    mViewModel.clearPause()
                    currentDuration = time
                    startScroll()
                    mViewModel.loadingScene = ReadingTE.LOADING_SCENE_MANUAL_CHANGE_PROGRESS
                    resumePlay()
                    updateNextText(-1)
                    LogUtil.d(HomeMC.APP_LOADING_TAG, "拖动进度条，loadingScene设置为手动变更进度")
                }

                override fun onLongClick() {
                    stopScroll()
                    longPress(true)
                }

                override fun onGestureEnd(gestureType: GestureType) {
                    super.onGestureEnd(gestureType)
                    startScroll()
                    longPress(false)
                }

                override fun onActorClick(
                    view: View,
                    performerIds: MutableList<String>,
                    performerName: String?, worksNum: Int?
                ) {
                    ElementClickUtils.trackViewAppClick2(
                        elementPosition = "演员",
                        content = performerName,
                        positionName = "首页播放器",
                        bookName = mViewModel.currentVideInfo?.bookName,
                        bookID = mViewModel.currentVideInfo?.bookId,
                        chaptersNum = mViewModel.currentVideInfo?.chapterIndex,
                        chaptersId = mViewModel.currentVideInfo?.chapterId
                    )
                    actorDialog(performerIds)
                }

                override fun onBookNameClick(view: View, contentName: String?) {
                    ElementClickUtils.trackViewAppClick2(
                        elementPosition = if (contentName == "剧封") {
                            "剧封"
                        } else {
                            "剧名"
                        },
                        positionName = "首页播放器",
                        content = contentName,
                        bookName = mViewModel.currentVideInfo?.bookName,
                        bookID = mViewModel.currentVideInfo?.bookId,
                        chaptersNum = mViewModel.currentVideInfo?.chapterIndex,
                        chaptersId = mViewModel.currentVideInfo?.chapterId
                    )
                    HomeME.get().invisibleComp().post(Any())
                    mViewModel.currentVideInfo?.apply {
                        omap?.run {
                            scene = BBaseMC.origin_sy
                            originName = BBaseMC.origin_name_sy
                            channelName = if (BBaseKV.recommendContent) "推荐" else "精选"
                        }
                        // 打点相关
                        val params = mutableMapOf<String, Any>()
                        params["firstPlaySource"] = SourceNode.PLAY_SOURCE_SYTJ
                        params["origin"] = BBaseMC.origin_sy
                        params["originName"] = BBaseMC.origin_name_sy
                        params["channelId"] = if (BBaseKV.recommendContent) "tj" else "jx"
                        params["channelName"] = if (BBaseKV.recommendContent) "推荐" else "精选"
//                        omap?.let { params["cOmap"] = it }
                        omap?.let { params["omap"] = it.toJson() }
                        params["playletPosition"] = playletPosition
                        params["recPageNum"] = recPageNum
                        params["isRecPlaylet"] = isRecPlaylet
                        params["bookIndex"] = mCurrentPosition
                        mViewModel.allowReportInvalidPlay = false
                        VideoMS.get()?.goToVideoDetail(
                            bookId,
                            bookName,
                            chapterId,
                            chapterIndex,
                            progress = <EMAIL>,
                            coverBgColor,
                            coverWap,
                            other = params
                        )
                    }
                }

                override fun onTagClick(deepLink: String, tag: String) {
                    ElementClickUtils.trackViewAppClick2(
                        elementPosition = "标签",
                        content = tag,
                        positionName = "首页播放器",
                        bookName = mViewModel.currentVideInfo?.bookName,
                        bookID = mViewModel.currentVideInfo?.bookId,
                        chaptersNum = mViewModel.currentVideInfo?.chapterIndex,
                        chaptersId = mViewModel.currentVideInfo?.chapterId
                    )
                    HomeME.get().invisibleComp().post(Any())
                    SchemeRouter.doUriJump(deepLink)
                }

                override fun ignoreAutoTrack(view: View) {
                    ElementClickUtils.ignoreAutoTrack(view)
                }

                override fun onDanMuClick() {
                    mViewModel.pausePlay(VideoMC.PLAY_END_SEND_DANMU)
                    videoDanMuManger?.let {
//                        inputBoxShowing = true
                        it.showDanMuInput(lightVideo = true)
                        VideoDanMuManager.trackDanMuButtonClick("首页")
                    }
                }

                override fun onDescSwitchClick(): Boolean {
                    ElementClickUtils.trackViewAppClick2(
                        elementPosition = "简介",
                        content = "简介",
                        positionName = "首页播放器",
                        bookName = mViewModel.currentVideInfo?.bookName,
                        bookID = mViewModel.currentVideInfo?.bookId,
                        chaptersNum = mViewModel.currentVideInfo?.chapterIndex,
                        chaptersId = mViewModel.currentVideInfo?.chapterId
                    )
//                    val isEnabled = VideoKV.introRecSwitch
//                    val isEnabled = false
//                    if (isEnabled) {
//                        DetailMR.get().videoIntro().apply {
//                            scene = PageConstant.PAGE_ID_RECOMMEND
//                            showTab = PageConstant.PAGE_INTRO_RCMD_ID
//                            bookInfoVo = mViewModel.mVideoInfo
//                            goToVideoDetail = {
//                                onBookNameClick(mViewBinding.tvName)
//                            }
//                            goToVideoDetail = {
////                                ElementClickUtils.trackViewAppClick2(
////                                    elementPosition = "首页",
////                                    content = mViewModel.currentVideInfo?.bookName,
////                                    elementType = "查看详情",
////                                    bookName = mViewModel.currentVideInfo?.bookName,
////                                    bookID = mViewModel.currentVideInfo?.bookId,
////                                    chaptersNum = mViewModel.currentVideInfo?.chapterIndex
////                                )
//                                mViewModel.currentVideInfo?.apply {
//                                    omap?.run {
//                                        scene = "简介页"
//                                        originName = "简介页"
//                                        channelName = "简介页"
//                                    }
//                                    // 打点相关
//                                    val params = mutableMapOf<String, Any>()
//                                    params["firstPlaySource"] = "简介页"
//                                    params["origin"] = "简介页"
//                                    params["originName"] = "简介页"
//                                    params["channelId"] = "简介页"
//                                    params["channelName"] = "简介页"
//                                    omap?.let { params["cOmap"] = it }
//                                    params["playletPosition"] = playletPosition
//                                    params["recPageNum"] = recPageNum
//                                    params["isRecPlaylet"] = isRecPlaylet
//                                    params["bookIndex"] = mCurrentPosition
//                                    mViewModel.allowReportInvalidPlay = false
//                                    VideoMS.get()?.goToVideoDetail(
//                                        bookId,
//                                        bookName,
//                                        chapterId,
//                                        chapterIndex,
//                                        progress = <EMAIL>,
//                                        coverBgColor,
//                                        coverWap,
//                                        other = params
//                                    )
//                                }
//                            }
//                            onStateChanged = { newState ->
//                                when (newState) {
//                                    BottomSheetBehavior.STATE_EXPANDED -> {
//                                        mViewModel.pausePlay("dialog_intro")
//                                    }
//
//                                    else -> {
//                                        mViewModel.cancelPause("dialog_intro")
//                                    }
//                                }
//                            }
//                            addShowListener {
//                                introDialog = it
//                            }
//                            addDismissListener {
//                                introDialog = null
//                                mViewModel.cancelPause("dialog_intro")
//                            }
//                        }.start()
//                    }
                    return false
                }
            })
        }
    }

    private fun actorDialog(ids: MutableList<String>) {
        HomeMR.get().actorDialog().apply {
            bookId = mViewModel.mVideoInfo?.bookId
            bookName = mViewModel.mVideoInfo?.bookName
            chapterId = mViewModel.mVideoInfo?.chapterId
            chapterNum = mViewModel.mVideoInfo?.chapterIndex.toString()
            chapterIndex = mViewModel.mVideoInfo?.chapterIndex ?: 0
            performerIds = ids
            callback = object : Callback {
                val statusHeight = ScreenUtil.getStatusHeight(requireContext())
                override fun onDialogHeightChanged(current: Int, total: Int) {
                    val top = current * statusHeight / total
                    mPlayerController.updatePlayerSize(
                        (playerHeight - BBaseMC.PLAYER_SCALE_RATIO * current - top).toInt(),
                        if (mViewModel.mVideoInfo?.isLandscapeVideo() == true) {
                            playerWidth
                        } else {
                            playerWidth - (BBaseMC.PLAYER_SCALE_RATIO * current + top) / BBaseKV.recommendHeightWidthRatio
                        }.toInt(),
                        top
                    )
                    updateViewAlpha(1 - current.toFloat() / total)
                }

                override fun onDialogShow(currentHeight: Int, totalHeight: Int) {
                }
            }
            addShowListener {
                isAllowShowFloatComp = false
                HomeME.get().invisibleComp().post(Any())
                // 循环播放
                mPlayerController.isLoop(true)
            }
            addDismissListener {
                isAllowShowFloatComp = true
                HomeME.get().continueResume().post(Any())
                mPlayerController.isLoop(false)
                mPlayerController.resetPlayerSize()
                updateViewAlpha(1f)
            }
        }.start()
    }

    private fun updateViewAlpha(alpha: Float) {
        if (alpha > 0.95f) {
            HomeME.get().updateAlpha().post(1f)
            mPlayerController.updateAlpha(1f)
        } else {
            HomeME.get().updateAlpha().post(alpha)
            mPlayerController.updateAlpha(alpha)
        }
    }

    override fun currentIsVideo(): Boolean {
        val holder = ViewPager2Helper.findViewHolderByPosition(mViewBinding.vp, mCurrentPosition)
        return holder == null || holder is NewRecommendVideoViewHolder
    }

    /**
     * 处理长按手势
     */
    private fun longPress(isPress: Boolean) {
        if (isPress) {
            HomeME.get().tabBarStatus().post(false)
        } else if (mIsLongPress) {
            HomeME.get().tabBarStatus().post(true)
        }
        mIsLongPress = isPress
    }

    /**
     * 分享剧
     */
    private fun shareClick() {
        mViewModel.mWxShareConfigVo?.let { wxShareConfigVo ->
            wxShareConfigVo.firstPlaySource = SourceNode.PLAY_SOURCE_SYTJ
            wxShareConfigVo.wxShareAppId = BBaseKV.wxShareAppId
            mViewModel.pausePlay(VideoMC.PLAY_END_WECHAT_SHARE)
            wxShareConfigVo.shareVoList?.forEachIndexed { _, shareItemBean ->
                shareItemBean?.needToastResult = false
                shareItemBean?.dismissShareDialogOnFail = true
                shareItemBean?.bookId = mViewModel.mVideoInfo?.bookId
                shareItemBean?.bookName = mViewModel.mVideoInfo?.bookName
                shareItemBean?.coverUrl = mViewModel.mVideoInfo?.coverWap
                shareItemBean?.chapterId = mViewModel.mVideoInfo?.chapterId
            }
            DzTrackEvents.get().shareTE().type().channelCode(CommInfoUtil.getAppChannel())
                .site(ShareTE.SITE_SHARE_BTN).bookId(mViewModel.mVideoInfo?.bookId)
                .bookName(mViewModel.mVideoInfo?.bookName).track()
            isSharing = true
            if (wxShareConfigVo.shareVoList?.size == 1) {
                isShareDirect = true
            }
            BCommonMS.get()?.share(wxShareConfigVo, object : ShareListener {
                override fun onShareStart(shareItemBean: ShareItemBean) {

                }

                override fun onFail(
                    shareItemBean: ShareItemBean,
                    errorMessage: String?,
                    shareResultBean: ShareResultBean?,
                ) {
                    isSharing = false
                    isShareDirect = false
                    ToastManager.showToast(errorMessage ?: "分享失败")
                    mViewModel.cancelPause(VideoMC.PLAY_END_WECHAT_SHARE)
                    ThirdSDKTrack.onError("首页推荐", errorMessage, "分享")
                }

                override fun onSuccess(
                    shareItemBean: ShareItemBean,
                    shareResultBean: ShareResultBean,
                ) {
                    isSharing = false
                    isShareDirect = false
                    ToastManager.showToast("分享成功")
                    mViewModel.cancelPause(VideoMC.PLAY_END_WECHAT_SHARE)
                    updateShares(shareResultBean)
                }

                override fun onCancel(
                    shareItemBean: WxShareConfigVo,
                    clickCancelBtn: Boolean,
                ) {
                    isSharing = false
                    isShareDirect = false
                    if (clickCancelBtn) {
                        ToastManager.showToast("取消分享")
                    }
                    mViewModel.cancelPause(VideoMC.PLAY_END_WECHAT_SHARE)
                }

                override fun prohibitShare(shareItemBean: ShareItemBean) {
                    isSharing = false
                    isShareDirect = false
                    mViewModel.cancelPause(VideoMC.PLAY_END_WECHAT_SHARE)
                }
            })
        }
    }

    /**
     * 点击追剧按钮
     */
    private fun favoriteClick() {
        mPageAdapter.getData(mCurrentPosition)?.apply {
            if (inBookShelf == true) {
                HomeMR.get().favoriteDialog().apply {
                    cancelText = "再想想"
                    sureText = "确认"
                    title = "确认取消追剧吗？"
                    content = "取消后可能找不到本剧哦~"
                }.onSure {
                    bookId?.let { bookId ->
                        val list = mutableListOf<String>()
                        list.add(bookId)
                        mViewModel.deleteBooks(
                            list,
                            TierPlaySourceVo(
                                "首页",
                                "首页-" + getPageName(),
                                "首页-" + getPageName()
                            )
                        )
                    }
                }.start()

            } else {
                bookId?.let { bookId ->
                    mViewModel.addFavorite(
                        bookId,
                        chapterId ?: "-1",
                        BBaseMC.origin_name_sy,
                        "1",
                        omap,
                        TierPlaySourceVo(
                            firstTierPlaySource = "首页",
                            secondTierPlaySource = "首页-" + getPageName(),
                            thirdTierPlaySource = "首页-" + getPageName()
                        ),
                        videoInfoVo = this,
                    )
                }
            }
        }
    }

    /**
     *  点击点赞
     */
    private fun likesClick(isLikes: Boolean) {
        mViewModel.addSubtractLikes(isLikes, mCurrentPosition)
    }

    private var lastClickTime: Long = 0

    /**
     * 跳转二级播放页
     */
    private fun startVideoList(startType: Int, enterTypeMode: EnterTypeMode? = null) {
        coroutineScope?.cancel()
        coroutineScope = null
        MainME.get().bubbleDismiss().post(1)
        mViewModel.guideDialog?.dismiss()
        mViewModel.allowReportInvalidPlay = false
        val currentClickTime = System.currentTimeMillis()
        if (currentClickTime - lastClickTime < 600) {
            return
        }
        lastClickTime = currentClickTime
        LogUtil.d(TAG, "startVideoList 去二级页")
        mViewModel.mVideoInfo?.let {
            it.omap?.run {
                scene = BBaseMC.origin_sy
                originName = BBaseMC.origin_name_sy
                channelName = if (BBaseKV.recommendContent) "推荐" else "精选"
            }
            if (it.fromType == 1) {
                //强插运营位点击
                DzTrackEvents.get().operationClickTE()
                    .operationName("首页推荐手动运营位")
                    .bookName(it.bookName)
                    .bookId(it.bookId)
                    .track()
            }
            lifecycleScope.launch(Dispatchers.IO) {
                val historyEntity = DBHelper.queryBook(it.bookId)
                withContext(Dispatchers.Main) {
                    DetailMR.get().videoList().apply {
                        type = startType
                        bookId = it.bookId
                        if (it.videoLinkType == 2) {
                            type = if (type == DetailMC.LAUNCH_TYPE_NEXT) {
                                DetailMC.LAUNCH_TYPE_CURRENT
                            } else {
                                startType
                            }
                            if (historyEntity?.cur_cid.isNullOrEmpty()) {
                                chapterIndex = it.chapterIndex
                                chapterId = it.chapterId
                            }
                        } else {
                            type = startType
                            chapterIndex = it.chapterIndex
                            chapterId = it.chapterId
                            playPosition = currentDuration
                        }
                        updateNum = it.updateNum
                        videoStarsNum = it.videoStarsNum
                        firstPlaySource = SourceNode.PLAY_SOURCE_SYTJ
                        enterVideoType = enterTypeMode?.value
                        cOmap = it.omap
                        origin = BBaseMC.origin_sy
                        originName = BBaseMC.origin_name_sy
                        channelId = if (BBaseKV.recommendContent) "tj" else "jx"
                        channelName = if (BBaseKV.recommendContent) "推荐" else "精选"
                        backToRecommend = false
                        playletPosition = it.playletPosition
                        recPageNum = it.recPageNum
                        isRecPlaylet = it.isRecPlaylet
                        bookIndex = mCurrentPosition
                        firstTierPlaySource = "首页"
                        secondTierPlaySource = "首页-推荐"
                        thirdTierPlaySource = "首页-推荐"
                        recReasonType = it.recReasonType
                        recReason = it.recReason
                        rankId = it.rankId
                        rankActionTips = it.rankActionTips
                        showInfoType = it.showInfoType
                    }.start()
                }
            }
        }
    }

    private fun updateItemData(it: VideoInfoVo) {
        if (it.cardType == CardType.VIDEO && it.content?.getUrl()?.isNotBlank() == true) {
            mPageAdapter.getData().forEachIndexed { index, videoInfoVo ->
                if (videoInfoVo.bookId == it.bookId) {
                    val changeChapter = (videoInfoVo.chapterIndex != it.chapterIndex)
                    videoInfoVo.chapterId = it.chapterId
                    videoInfoVo.chapterIndex = it.chapterIndex
                    videoInfoVo.chapterName = it.chapterName
                    videoInfoVo.likesChecked = it.likesChecked
                    videoInfoVo.likesNumActual = it.likesNumActual
                    videoInfoVo.likesNum = it.likesNum
                    videoInfoVo.shareTimes = it.getShareNum()
                    videoInfoVo.episodeTags = it.episodeTags
                    videoInfoVo.curChapterCommentNum = it.curChapterCommentNum
                    if (mViewModel.mVideoInfo?.videoLinkType == 1) {
                        videoInfoVo.currentDuration = it.currentDuration
                        videoInfoVo.content = it.content
                    }
                    it.content?.getUrl()?.let { url ->
                        mPlayerController.updatePlayerInfo(
                            PlayerInfo(it.bookId, it.bookId + it.chapterId, url, it.index, it.bookId)
                        )
                    }
                    if (mViewModel.mVideoInfo?.bookId == it.bookId) {
                        mViewModel.currentVideInfo?.content?.switchState = SwitchState.NO_SWITCH
                        if (mViewModel.mVideoInfo?.videoLinkType == 1) {
                            if (changeChapter) {
                                setStartTime(it.currentDuration)
                                LogUtil.d("player_start_time", "章节发生变化，调用setStartTime")
                                mViewModel.loadingScene = ReadingTE.LOADING_SCENE_PLAYING
                                loadingBegin = false
                                LogUtil.d(
                                    HomeMC.APP_LOADING_TAG,
                                    "同步播放进度，章节发生变化，调用setStartTime,loadingScene设置为播放过程中"
                                )
                                mViewModel.played = false
                                LogUtil.d(HomeMC.APP_ERROR_TAG, "同步播放进度，played ==false")
                                changeUrlTo(it.content?.getUrl())
                                mViewModel.isFirstPlay = false
                                moveTo(it.bookId + it.chapterId)
                                recordUserSenseTime(0)
                                recordPrepared(0)
                                LogUtil.d(
                                    HomeMC.PLAYER_START_PLAY_TIME_TAG,
                                    "同步播放进度,调用moveTo"
                                )
                            }
                            if (!changeChapter) {
                                mViewModel.loadingScene =
                                    ReadingTE.LOADING_SCENE_AUTO_CHANGE_PROGRESS
                                LogUtil.d(
                                    HomeMC.APP_LOADING_TAG,
                                    "同步播放进度，章节未发生变化，调用seekTo,loadingScene设置为自动变更进度"
                                )
                                seekTo(it.currentDuration, false)
                                LogUtil.d("player_start_time", "章节未发生变化，调用seekTo")
                            }
                        } else {
                            LogUtil.d(TAG, "继续播放高光视频")
                        }
                    }
                    mPageAdapter.setItem(index, videoInfoVo)
                    videoInfoVo.syncVideoInfo(mViewModel.currentChapter)
                    mPageAdapter.notifyItemRangeChanged(
                        index, 1, HomeMC.PAYLOAD_UPDATE_ITEM_DATA
                    )
                }
            }
        }
    }


    /**
     * 视频暂停/恢复的时候使用，
     */
    fun onPauseClick(pauseBlock: (() -> Unit)? = null) {
        if (getPlayerPauseStatus()) {
            if (pausedByNetwork) {
                ToastManager.showToast("网络异常，请稍后重试")
                return
            }
            clickPause = false
//            mViewModel.cancelPause(VideoMC.PLAY_END_PAUSE)
            mViewModel.clearPause()
            resumePlay()
        } else {
            clickPause = true
            mViewModel.pausePlay(VideoMC.PLAY_END_PAUSE)
            pauseBlock?.invoke()
        }
    }


    /**
     * 初始化滑动RecyclerView
     */
    private fun initViewPager2() {
        mPageAdapter = NewRecommendPageAdapter(requireContext(), mPlayerController).apply {
            holderListener = object : VideoHolderListener {
                override fun onFullscreenClick(position: Int) {
                    startVideoList(DetailMC.LAUNCH_TYPE_FULLSCREEN)

                    mViewModel.mVideoInfo?.let {
                        DzTrackEvents.get().buttonClick().buttonName("全屏观看")
                            .positionName("首页").bookId(it.bookId).bookName(it.bookName)
                            .chapterId(it.chapterId).chapterIndex(it.chapterIndex)
                            .chapterName(it.chapterName).track()
                    }
                }
            }
            cardListener = object : AggregationCardListener {
                override fun scrollToNextPage() {
                    mViewBinding.vp.setCurrentItem(mCurrentPosition + 1, false)
                }
            }
        }
        mPageAdapter.setViewModel(mViewModel)
        ViewPager2Helper.setup(mViewBinding.vp)
        mViewBinding.vp.offscreenPageLimit = ViewPager2.OFFSCREEN_PAGE_LIMIT_DEFAULT
        mViewBinding.vp.setOrientation(ViewPager2.ORIENTATION_VERTICAL)
        mViewBinding.vp.setAdapter(mPageAdapter)
        mViewBinding.vp.registerOnPageChangeCallback(object :
            OnPageChangeCallbackCompat(mViewBinding.vp) {

            override fun onPageLoadMore(pager: ViewPager2, position: Int) {
                super.onPageLoadMore(pager, position)
                if (mPageAdapter.itemCount <= position + HomeMC.DEFAULT_PRELOAD_NUMBER && !mIsLoadingData) {
                    loadMore()
                }
            }

            override fun onPageSelected(pager: ViewPager2, position: Int, forceSelected: Boolean) {
                super.onPageSelected(pager, position, forceSelected)
                mViewModel.clearPause()
                updateNextText(-1)
                if (pageRendered && position == mCurrentPosition) {
                    updateCurPosition(mCurrentPosition)
                    return
                }
                pageRendered = true
                val lastHolder =
                    ViewPager2Helper.findViewHolderByPosition(mViewBinding.vp, mCurrentPosition)
                ViewPager2Helper.findItemViewByPosition(mViewBinding.vp, mCurrentPosition)
                    ?.let { holderView ->
                        if (lastHolder is HomeAdVideoViewHolder && lastHolder.isVideo()) {
                            DrawAdManager.interactiveTrack(
                                downX,
                                downY,
                                holderView.width,
                                holderView.height,
                                lastHolder.adShowTime(),
                                lastHolder.adFinishStatus()
                            )
                        }
                    }
                mViewModel.preloadDrawAd(
                    position,
                    mCurrentPosition < position,
                    mViewBinding.container,
                    activity
                )
                //3、NEW_VIEW_PAGER2预渲染打点结束
                TimeMonitorManager.getMonitor(MonitorMC.SCENE_RCMD)
                    .recordTime(MonitorMC.STAGE_RENDER_END)  // 渲染结束
                if (mCurrentPosition != position) {
                    LogUtil.d(TAG, "页面被划走")
                    mLastStopPosition = mCurrentPosition
                    clearPlotInfo()
                    val holder = ViewPager2Helper.findViewHolderByPosition(mViewBinding.vp, mLastStopPosition)
                    if (holder is NewRecommendVideoViewHolder) {
                        holder.controller.sendEvent(VideoMC.EVENT_PAGE_RELEASE)
                    }
                }
                //5、NEW_VIEW_PAGER2刷新弹幕显示
                val holder =
                    ViewPager2Helper.findViewHolderByPosition(mViewBinding.vp, position)
                currentHolder = holder
                if (holder is HomeAdVideoViewHolder) {
                    //11、NEW_VIEW_PAGER2 广告显示
                    holder.onPageShow(mViewModel)
                    LogUtil.d(
                        HomeMC.AD_TAG,
                        "有广告缓存，滑动到当前广告占位，沉浸式广告运营位曝光，广告曝光"
                    )
                    //11、NEW_VIEW_PAGER2 沉浸式广告运营位曝光
                    mViewModel.drawAdOperationTrack("ad show")
                    LogUtil.d(HomeMC.AD_TAG, "广告占位曝光，开始预加载新的广告")
                    mViewModel.loadDrawAd(mViewBinding.container, activity)
                    MainME.get().setWelfareWidgetVisible().post(false)
                } else {
                    mViewModel.checkAndFreshDataList()
                    openPushDialog()
                    if (holder is NewRecommendVideoViewHolder) {
//                        mViewModel.reqCommentNum(holder.mData)
//                        mViewModel.updateVideoCommentNum(position)
                        if(mViewModel.mVideoInfo?.curChapterCommentNum == null)
                        {
                            mViewModel.updateVideoCommentNum(position)
                        }
                        videoDanMuManger?.onDanMuContainerShow(holder.controller.mViewBinding.danmuContainer)
                        MainME.get().setWelfareWidgetVisible().post(true)
                    }
                    //处理聚合卡片嵌套滑动冲突
                    if (holder is AggregationCardViewHolder) {
                        val view = mViewBinding.vp.getChildAt(0)
                        if (view is RecyclerView) {
                            holder.setRecycleViewParent(view)
                        }
                    }
                }
                //8、NEW_VIEW_PAGER2申请权限IMEI
                checkPhonePermission(position)
                //重新选中视频不播放，如果该位置被stop过则会重新播放视频
                //10、NEW_VIEW_PAGER2当page被选中的时候，检测当前是否应该触发预加载，是则触发预加载
                if (mPageAdapter.itemCount <= position + HomeMC.DEFAULT_PRELOAD_NUMBER && !mIsLoadingData) {
                    loadMore()
                }
                if (lastHolder is NewRecommendVideoViewHolder) {
                    if (isPlaying) {
                        track(1, triggerScenario = VideoMC.PLAY_END_CHANGE_CHAPTER_BY_MANUAL_IN_RCMD)
                    }
                    mPlayerController.unbindComp(pausedByNetwork)
                }
                mCurrentPosition = position
                mViewModel.currentPosition = position
                //6、NEW_VIEW_PAGER2开始播放
                startPlay(position, true)
                LogUtil.d("continue", "进入多播判断1 position = $position")
                checkPositionAndUpdateUI(position)

                //4、NEW_VIEW_PAGER2重置点击暂停的booblean值
                clickPause = false
                //9、NEW_VIEW_PAGER2视频界面展示
                onVideoPageShow()
                mViewModel.checkDrawAdMiss(position)
                if (!isAd(mCurrentPosition)) { // 要放到最后一行
                    mViewModel.checkAdIntervalAndFreshDataList()
                }
            }

            override fun onPagePeekStart(pager: ViewPager2, position: Int, peekPosition: Int) {
                super.onPagePeekStart(pager, position, peekPosition)
                onPagePeekStart(peekPosition)
            }

            override fun onPagePeekEnd(pager: ViewPager2, position: Int, peekPosition: Int) {
                // 滑动结束
                super.onPagePeekEnd(pager, position, peekPosition)
                updateSeekBarArea()
            }
        })
    }

    /**
     * 滑动开始
     */
    private fun onPagePeekStart(peekPosition: Int) {
//        if (BBaseKV.realTimeRender && BBaseKV.playerPrerender && BBaseKV.playerPrerenderTest) {//开启播放器实时预渲染
//            val holder =
//                ViewPager2Helper.findViewHolderByPosition(mViewBinding.vp, peekPosition)
//            if (holder is NewRecommendVideoViewHolder) {
//                holder.preRenderVideo(
//                    mPlayerController.getPlayer(
//                        holder.mData?.index ?: 0,
//                        false,
//                        playerVid = holder.mData?.bookId
//                    ),
//                    peekPosition
//                )
//            }
//        }
    }

    /**
     * 更新SeekBar滑动热区的高度
     */
    private fun updateSeekBarArea() {
        (currentHolder as? NewRecommendVideoViewHolder)?.controller?.updateSeekBarTouchArea()
    }

    private fun isAd(position: Int): Boolean {
        val holder = ViewPager2Helper.findViewHolderByPosition(mViewBinding.vp, position)
        return (holder is HomeAdVideoViewHolder).also { isAd ->
            if (isAd) LogUtil.d(HomeMC.AD_INTERVAL_CHAPTER_TAG, "position:$position 是广告")
        }
    }

    /**
     * 页面展现，视频，广告
     */
    private fun onVideoPageShow() {
        val bookId = mViewModel.mVideoInfo?.bookId ?: ""
        val chapterId = mViewModel.mVideoInfo?.chapterId
        val isAd = mViewModel.mVideoInfo?.cardType == CardType.AD
        val isUnlock = false
        val isLightVideo =
            if (mViewModel.mVideoInfo?.videoLinkType == 2) 1 else 0 //是否是在高光片段发送的弹幕(0-否,1-是)
        val lightVideoId = mViewModel.mVideoInfo?.highlightId ?: ""
        val lightVideoIndex = mViewModel.mVideoInfo?.highlightSort ?: -1
        mVideoLifecycle.onPageShow(
            bookId,
            chapterId,
            isAd,
            isUnlock,
            isLightVideo = isLightVideo,
            lightVideoId = lightVideoId,
            lightVideoIndex = lightVideoIndex,
        )
    }


    /**
     * 跳转章节
     */
    private fun selectVideo(position: Int, triggerScenario: String) {
        if (isPlaying) track(1, triggerScenario = triggerScenario)
        if (position >= 0 && position < mPageAdapter.itemCount) {
            mViewBinding.vp.setCurrentItem(position, false)
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        // 取消 VideoDanMuManager 的回调 防止内存泄露
        videoDanMuManger?.unregisterCallbacks()
//        DzEventManager.removeObservers(getActivityPageId())
    }

    /**
     * 显隐封面
     */
    private fun changeHolderCover(position: Int, visibility: Int) {
        val holder = ViewPager2Helper.findViewHolderByPosition(mViewBinding.vp, position)
        if (holder is NewRecommendVideoViewHolder) {
            holder.controller.coverVisibility(visibility)
        }
    }

    private fun onRefresh(refreshType: Int) {
        mFreshType = BBaseMC.REFRESH_TYPE_PULL
        mIsLoadingData = true
        isRecommendedRefresh = false
        isUserLikeRefresh = false
        mPlayerController?.setWatchingVisibility(false, mViewModel.continueWatchVoRecommend)
        mViewModel.isShowComp = false
        isUserChangeRefresh = false
        mViewBinding.refreshView.setEnableRefresh(true)
        mViewModel.getDataList(refreshType)
    }

    /**
     * 加载更多
     */
    private fun loadMore(needFlipPage: Boolean = false) {
        mIsLoadingData = true
        mViewModel.getMoreDataList(needFlipPage)
    }
    private fun getGuideDialogIntent(position: Int): DialogRouteIntent {
        LogUtil.d(HomeMC.PLAYER_START_PLAY_TIME_TAG, "执行首页引导弹窗获取路由")
        return BBaseMR.get().recommendGuide()
            .addShowListener {
                mViewModel.guideDialog = it
            }
            .addDismissListener {
                mViewModel.guideDialog = null
            }.onUp {
                LogUtil.d(HomeMC.PLAYER_START_PLAY_TIME_TAG, "执行首页引导弹窗上滑")
                mViewBinding.vp.setCurrentItem(position + 1, true)
            }
    }

    private fun tryToShowGuideDialog(position: Int) {
        LogUtil.d(com.dz.business.welfare.signin.TAG, "执行首页引导弹窗展示")
        getGuideDialogIntent(position).let { intent ->
            LogUtil.d(com.dz.business.welfare.signin.TAG, "展示首页引导弹窗")
//            PriorityDialogManager.listApply[PriorityDialogManager.GUIDE] = 1
//            PriorityDialogManager.guideApply(intent)
            PriorityTaskManager.apply {
                addTask(
                    MainPriorityDialogTask(
                        PriorityMC.TASK_RCMD_SLIDE_GUIDE,
                        PageConstant.RECOMMEND_ID,
                        PriorityMC.PRIORITY_RCMD_SLIDE_GUIDE,
                        intent
                    )
                )
                executeTask()
            }
        }
    }

    /**
     * 播放视频
     */
    private fun startPlay(position: Int, onPageSelected: Boolean = false) {
        //首次播放的时候判断条件是否展示新人引导弹窗
        LogUtil.d(
            HomeMC.PLAYER_START_PLAY_TIME_TAG,
            "单播执行首页引导弹窗条件判断 recommendGuideStatus = ¥$recommendGuideStatus needFollowBubble = $needFollowBubble"
        )
        if (recommendGuideStatus == 0 && newUserGuide == 1 && isFragmentVisible()) {
            // 使用 Handler 添加100ms延迟
            tryToShowGuideDialog(position)
        }
        playCount = 0
        currentDuration = 0
        updateNextText(-1)
        ToastManager.dismissToast()
        mViewBinding.compLoading.dismiss()
        if (position < 0 || position >= mPageAdapter.itemCount) {
            return
        }

        mViewModel.mVideoInfo = mPageAdapter.getData(position)
        if (position != 0 || (mViewModel.firstPlayBook != null && mViewModel.firstPlayBook != "${mViewModel.mVideoInfo?.bookId}_${mViewModel.mVideoInfo?.chapterId}")) {
            mViewModel.isFirstPlay = false
        }
        mViewModel.mVideoInfo?.let { videoInfo ->
            mViewModel.videoTrackInfo.apply {
                bookId = videoInfo.bookId
                bookName = videoInfo.bookName
                chapterId = videoInfo.chapterId
                chapterName = videoInfo.chapterName
                chapterNum = videoInfo.chapterIndex.toString()
                chapterIndex = videoInfo.chapterIndex
                cpPartnerName = videoInfo.cpPartnerName
                cpPartnerId = videoInfo.cpPartnerId
            }
            if (sceneSign.isNotBlank()) {
                videoInfo.sceneSign = sceneSign
                sceneSign = ""
            }
            val holder = ViewPager2Helper.findViewHolderByPosition(mViewBinding.vp, position)
            if (holder is NewRecommendVideoViewHolder) {
                isPlayerPrePrepared = false
                mViewModel.currentVideInfo = videoInfo
                //恢复界面状态
                changePlayerController(holder)
                initPlayerController(videoInfo)
                //初始化列表播放器
                initLPlayerListener()
                //防止退出后台之后，再次调用start方法，导致视频播放
                mViewModel.currentVideInfo?.content?.switchState = SwitchState.NO_SWITCH
                mViewModel.played = false
                LogUtil.d(HomeMC.APP_ERROR_TAG, "切换剧集开始播放，played ==false")
                mViewModel.loadingScene = ReadingTE.LOADING_SCENE_PLAYING
                loadingBegin = false
                LogUtil.d(
                    HomeMC.APP_LOADING_TAG,
                    "切换剧集开始播放，loadingScene设置为播放过程中"
                )
                if (mPlayerController.isPreRenderPlayer()) {
                    isPlayerPrePrepared = mPlayerController.isPlayerPrepared()
                }
                mPlayerController.getPlayer(
                    holder.mData?.index ?: 0,
                    playerVid = holder.mData?.bookId
                )?.let {
                    holder.bindVideo(it, position)
                }
                //开始准备
                LogUtil.d(HomeMC.PLAYER_START_PLAY_TIME_TAG, "切换剧集开始播放,调用moveTo")
                if (onPageSelected) {
                    //NEW_VIEW_PAGER2 用户感知的计时、开始准备的计时开始记录点
                    if (!mViewModel.isFirstPlay) {
                        recordUserSenseTime(0)
                    }
                    recordPrepared(0)
                }
                lifecycleScope.launch(Dispatchers.IO) {
//                    var cEntity: ChapterEntity? = null
//                    if (onPageSelected) {
//                        cEntity = DBHelper.queryChapterTime(
//                            mViewModel.mVideoInfo?.bookId,
//                            mViewModel.mVideoInfo?.chapterId,
//                            mViewModel.mVideoInfo?.chapterIndex,
//                        )
//                    }
                    var cEntity: HistoryEntity? = null
                    if (onPageSelected) {
                        cEntity = DBHelper.queryBook(mViewModel.mVideoInfo?.bookId)
                    }
                    LogUtil.d(
                        "打印",
                        "获取数据库：lifecycleScope.launch ：${mViewModel.mVideoInfo?.bookId}  videoInfo.chapterId=${videoInfo.chapterId} cEntity?.cid=${cEntity?.cur_cid}  videoInfo.chapterIndex=${videoInfo.chapterIndex} cEntity?.chapter_num=${cEntity?.cur_index}  book_name=${cEntity?.book_name} ${cEntity?.current_time}"
                    )
                    withContext(Dispatchers.Main) {
                        if ((cEntity?.current_time ?: 0) > 0 &&
                            (videoInfo.chapterId == cEntity?.cur_cid || videoInfo.chapterIndex == cEntity?.cur_index)
                        ) {
                            LogUtil.d(
                                "打印",
                                "获取数据库：开始播放 ${cEntity?.current_time}"
                            )
                            setStartTime(cEntity?.current_time ?: 0)
                            seekTo(cEntity?.current_time ?: 0,false)
                        } else {
                            LogUtil.d(
                                "打印",
                                "获取数据库：开始播放 =0"
                            )
                            setStartTime(0)
                        }
                        prepareAndStart(mViewBinding.vp.currentItem, onPageSelected)
                        moveTo(videoInfo.bookId + videoInfo.chapterId)
                        if (SplashDisplayStateManager.isSplashShowing()) {
                            LogUtil.d(TAG, "开屏页正在显示，暂停播放111")
                            playerPause()
                        }
                        mViewBinding.compLoading.dismiss()
                        // 同步当前在播剧集信息
                        videoInfo.syncVideoInfo(mViewModel.currentChapter)
                        holder.controller.sendEvent(VideoMC.EVENT_START_PLAY)
                        mViewModel.onChapterChanged.value = 1
                        // 埋点
                        videoStartPlayTrack(holder, position)
                    }
                }
            } else if (holder is HomeAdVideoViewHolder) {
                if (!holder.hasAdView()) {
                    //当广告为空时，默认跳转下一章
                    LogUtil.d(HomeMC.AD_TAG, "广告为空，默认跳转下一章 $position")
                    autoSelectVideo(position)
                } else {
                    LogUtil.d(HomeMC.AD_TAG, "广告不为空，展示广告 $position")
                }
            } else {

            }
        }
    }

    private fun videoStartPlayTrack(holder: NewRecommendVideoViewHolder, position: Int) {
        //起播
        TimeMonitorManager.getMonitor(MonitorMC.SCENE_RCMD)
            .recordTime(MonitorMC.STAGE_VIDEO_PREPARE_START)  // 准备起播
        //曝光
        onExpose(holder, position)
    }

    private fun onExpose(holder: NewRecommendVideoViewHolder, position: Int) {
        if (ViewExposeUtil.checkIsFirstExpose(holder.itemView, holder.getItemDataId())) {
            mViewModel.onExpose(position, true)
        }
    }

    private fun changePlayerController(holder: NewRecommendVideoViewHolder) {
        mPlayerController.playerComp = holder.controller
        mViewModel.recommendUIData.value?.let {
            mPlayerController.playerComp?.setCompUI(it)
        }
    }

    /**
     * 默认跳转下一章
     */
    private fun autoSelectVideo(position: Int) {
        if (position > mLastStopPosition) {
            selectVideo(position + 1, VideoMC.PLAY_END_AUTO_SWAP)
        } else {
            selectVideo(position - 1, VideoMC.PLAY_END_AUTO_SWAP)
        }
    }


    /**
     * 设置播放源
     */
    private fun setData(videoInfoList: MutableList<VideoInfoVo>?) {
        mIsLoadingData = false
        if (mViewBinding.refreshView.isRefreshing) {
            mViewBinding.refreshView.finishDzRefresh()
        }
        mPageAdapter.setData(videoInfoList)
        if (mViewModel.currentPosition != 0) {
            LogUtil.d(
                HomeMC.AD_TAG, "滑动至mViewModel.currentPosition==" + mViewModel.currentPosition
            )
            mViewBinding.vp.setCurrentItem(0, false)
            mViewModel.currentPosition = 0
            mViewModel.mVideoInfo = mPageAdapter.getData(0)
        } else {
            mViewBinding.vp.post {
                mViewModel.mVideoInfo = mPageAdapter.getData(0)
                mViewModel.mVideoInfo?.let {
                    val holder = ViewPager2Helper.findViewHolderByPosition(mViewBinding.vp, 0)
                    if (holder is NewRecommendVideoViewHolder) {
                        changePlayerController(holder)
                        initPlayerController(it)
                        //初始化列表播放器
                        initLPlayerListener()
                        if (!firstCreateVp) {
                            mViewModel.reqCommentNum(it)
                        }
                    }
                }
            }
        }
        mViewModel.currentVideInfo = mViewModel.mVideoInfo
    }


    /**
     * 加载更多数据
     */
    private fun addMoreData(videoInfoList: MutableList<VideoInfoVo>?) {
        mIsLoadingData = false
        mViewBinding.refreshView.finishDzRefresh(false)
        mPageAdapter.addMoreData(videoInfoList)
    }


    /**
     * activity不可见或者播放页面不可见时调用该方法
     */
    private fun setOnBackground(isOnBackground: Boolean) {
        if (mIsOnBackground == isOnBackground) return
        mIsOnBackground = isOnBackground
        if (isOnBackground) {
            if (LocalActivityMgr.getForegroundActivityCount() == 1) {
                TaskManager.delayTask(1000) {
                    if (LocalActivityMgr.isBackground()) {
                        VideoTrackUtil.trackPause(
                            mViewModel.videoTrackInfo,
                            VideoTrackUtil.PAUSE_SCENE_BACKGROUND,
                            if (mViewModel.mVideoInfo?.isLandscapeVideo() == true) "竖屏" else "",
                            firstTierPlaySource = "首页",
                            secondTierPlaySource = "首页-推荐",
                            thirdTierPlaySource = "首页-推荐",
                            isMultipleInstances = true,
                        )
                    }
                }
            }
            mViewModel.pausePlay(VideoMC.PLAY_END_BACKGROUND)
            isWatching = false
        } else {
            mViewModel.cancelPause(VideoMC.PLAY_END_BACKGROUND)
            if (NetWorkUtil.isNetConnected(context)) {
                if (pausedByNetwork) {
                    setStartTime(currentDuration)
                    startPlay(mCurrentPosition)
                    pausedByNetwork = false
                } else {
                    resumePlay()
                }
                if (hasPlayed) isWatching = true
            }
        }
    }

    /**
     * 恢复播放
     */
    private fun resumePlay() {
        if (isSharing || clickPause || !mViewModel.canPlay() || isPauseAdShow() || MainMC.allowPlay == false || !isOnResumed()|| SplashDisplayStateManager.isSplashShowing()) {
            MainMC.allowPlay = null
            return
        }
        playerResume()
        pausedByNetwork = false
    }

    var isPlaying = false
    private var isFirstPause = true
    override fun onPause() {
        super.onPause()
        LogUtil.d(
            "continueWatchVo", "pause cancel"
        )
        BasePlayerManager.unRegisterConvertURLCallback()
        PlayingStatisticsMgr.saveLocalPlayingDurationToKV()
        ScreenUtil.allowScreenRecording(requireActivity().window)
        if (!isOnResumed()) {
            setOnBackground(true)
        }
        mVideoLifecycle.onUIPause()
        mViewModel.onDrawAdPause()
        if (!isFirstPause && SplashMS.get()?.getIsStartHotSplash() != true) {
            clearPlotInfo()
        }
        isFirstPause = false
    }

    private fun clearPlotInfo() {
        if (BBaseKV.isFirstShowPlotOcpcTips && OCPCManager.getPlotOcpcBookId() != null) {
            BBaseKV.isFirstShowPlotOcpcTips = false
            OCPCManager.setPlotOcpcBookId(null)
        }
    }

    override fun onStop() {
        super.onStop()
        mVideoLifecycle.onUIStop()
    }

    var isSharing = false
    private var isShareDirect = false
    override fun onResume() {
        SpeedUtil.recommendOnResumeTime = System.currentTimeMillis()
        LogUtil.d(TAG_PLAYER, "多播首页恢复")
        LogUtil.d(TAG_PLAYER, "coroutineScope == $coroutineScope")
        if (coroutineScope == null) {
            LogUtil.d(TAG_PLAYER, "coroutineScope == null")
            if (recommendContinueWatchingDialog != null && isAllowShowFloatComp) {
                LogUtil.d(TAG_PLAYER, "多播准备二次计时")
                coroutineScope = CoroutineUtils.getCoroutineScope()
                MainScope().launch(Dispatchers.Main) {
                    recommendContinueWatchingDialog?.setCompVisibility(true)
                }
                coroutineScope?.launch {
                    while (hasShowTime <= if (mViewModel?.continueWatchVoRecommend?.continueWatch?.littleCardSwitch == 1) (mViewModel?.continueWatchVoRecommend?.continueWatch?.littleCardTime
                            ?: 0) else (mViewModel?.continueWatchVoRecommend?.continueWatch?.longCardVanishTime
                            ?: 0)
                    ) {
                        delay(100)
                        hasShowTime += 0.1
                        LogUtil.d(TAG_PLAYER, "播放器继续播放弹窗展示时间：${hasShowTime}s")
                    }
                    if (mViewModel.continueWatchVoRecommend?.continueWatch?.littleCardSwitch == 0) {
                        BBaseKV.lastTimeShowContinueCompInOneLife = System.currentTimeMillis()
                    }
                    MainScope().launch(Dispatchers.Main) {
                        recommendContinueWatchingDialog?.dismiss()
                    }
                    mViewModel.trackPopExposure()

                    mPlayerController?.setWatchingVisibility(
                        true,
                        mViewModel.continueWatchVoRecommend
                    )
                    mViewModel.isShowComp = true
                    coroutineScope?.cancel()
                }
            } else {
                LogUtil.d(
                    "continueWatchVoRecommend",
                    "showBook：${mViewModel?.continueWatchVoRecommend?.continueWatch?.showBook} bookId = ${mViewModel.mVideoInfo?.bookId}"
                )
               checkIsShowContinueWatchComp()
            }
        }


        if (recommendGuideStatus == 0 && newUserGuide == 1 ) {
            // 使用 Handler 添加100ms延迟
            tryToShowGuideDialog(mCurrentPosition)
        }
        TimeMonitorManager.getMonitor(MonitorMC.SCENE_LAUNCH)
            .recordTime(MonitorMC.STAGE_RCMD_RESUME)
        super.onResume()
        if (isShareDirect) {
            isSharing = false
            isShareDirect = false
        }

        //如果pageName不为空,说明是其它页面切过来的，不上报
        if (pageName != null) {
            coldLaunchTacked = true
        } else {
            pageName = SpeedUtil.PageType.HOME
        }
        BasePlayerManager.registerConvertURLCallback(convertURLCallback)
        onPageResumed()
        if (isOnResumed() && mViewModel.initDrawAd) {
            LogUtil.d(HomeMC.AD_TAG, "页面切换loadDrawAd")
            mViewModel.loadDrawAd(mViewBinding.container, activity)
        }
        mVideoLifecycle.onUIResume()
        ReaderMS.get()?.dismissAudioComp(MAIN_SOURCE)
        FloatWindowManage.instance.allDismiss = true
    }

    override fun onDestroy() {
        super.onDestroy()
        LogUtil.d(TAG, "onDestroy")
        PlayingStatisticsMgr.saveLocalPlayingDurationToKV()
        mPlayerController.destroy()
        mViewModel.onDrawAdDestroy()
        mVideoLifecycle.onUIDestroy()
//        clearPlotInfo()
    }

    fun checkPhonePermission(position: Int) {
        //权限申请处理
        if (isOnResumed()) {
            HomeMS.get()?.checkPhonePermission(requireActivity(), position)
        }
    }


    /**
     * 屏幕常亮
     */
    private fun openScreenOn() {
        kotlin.runCatching {
            activity?.window?.setFormat(PixelFormat.TRANSLUCENT)
            activity?.window?.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
            LogUtil.d("screen_on_tag", "新首页推荐屏幕常亮")
        }
    }

    /**
     * 屏幕取消常亮
     */
    private fun closeScreenOn() {
        kotlin.runCatching {
            activity?.window?.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
            LogUtil.d("screen_on_tag", "新首页推荐屏幕取消常亮")
        }
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        try {
            PlayingStatisticsMgr.saveLocalPlayingDurationToKV()
            mViewModel.saveRvAllCells = mViewModel.videoListLiveData.value
            ///1、NEW_VIEW_PAGER2获取当前位置
            mViewModel.currentPosition = mViewBinding.vp.currentItem
        }catch (e: Exception){
            e.printStackTrace()
            LogUtil.d(TAG, "onSaveInstanceState error:" + e.message)
        }
    }

    private fun track(
        status: Int,
        isPreloadPlayer: Boolean = false,
        isPlayerPrePrepared: Boolean = false,
        triggerScenario: String? = null,
    ) {
        if (status == 1 && getPlayerDuration() <= 0) {
            // 当本次播放时长是0时，不执行上报。此时的上报是无意义的。
            return
        }
        var playEndScene: String? = null
        if (status == 1 && startPlayTime > 0) {
            playingTime += (System.currentTimeMillis() - startPlayTime)
            startPlayTime = 0L
            LogUtil.d(TAG, "track 开始统计累计播放时长：$playingTime")
            playEndScene = triggerScenario ?: mViewModel.getPauseModels().firstOrNull()
        }
        val info = TrackUtil.copyVideoInfoVo(mViewModel.mVideoInfo, mViewModel.currentVideInfo)
        val duration =
            if (getPlayerDuration() > 0) getPlayerDuration() else currentVideoDuration
        val mCurrentDuration = currentDuration
        val playerPrePrepared = isPlayerPrePrepared
        val reckonByTime = playingTime//播放时长
        val currentPosition = mCurrentPosition
        val isEndPart =
            if (mViewModel.currentVideInfo?.chapterId == mViewModel.mVideoInfo?.maxChapterId) 1 else 0
        val volume = getVolume()
        val speed = getSpeed()
        val mMaxRemainBuffer = maxRemainBuffer
        val reportInvalidPlay = mViewModel.allowReportInvalidPlay
        val mIsFirstPlay = mViewModel.isFirstPlay
        lifecycleScope.launch(Dispatchers.IO) {
            val bookEntity = info.bookId?.let { DzDataRepository.bookDao().queryByBid(it) }
            var mFirstPlaySource = SourceNode.PLAY_SOURCE_SYTJ
            if (!bookEntity?.first_play_source.isNullOrEmpty()) {
                mFirstPlaySource = bookEntity?.first_play_source!!
            }
            TrackUtil.hiveLog(
                playStatus = status,
                position = currentPosition,
                currentDuration = mCurrentDuration,
                mFirstPlaySource = mFirstPlaySource,
                duration = duration,
                videInfo = info,
                playingTime = reckonByTime,
                isEndPart = isEndPart,
                allowReportInvalidPlay = reportInvalidPlay,
                triggerScenario = playEndScene,
            )
            if (status == 1) {
                mViewModel.allowReportInvalidPlay = true
            }
            TrackUtil.sensorPlaying(
                playStatus = status,
                duration = duration,
                volume = volume,
                speed = speed,
                currentDuration = mCurrentDuration,
                firstPlaySource = mFirstPlaySource,
                reckonByTime = reckonByTime,
                videInfo = info,
                position = currentPosition,
                isMultipleInstances = true,
                isPayVideo = info.isPayVideo(),
                maxRemainBuffer = mMaxRemainBuffer,
                isPreloadPlayer = isPreloadPlayer,
                isPlayerPrePrepared = playerPrePrepared,
                isFirstPlay = mIsFirstPlay,
                triggerScenario = playEndScene,
            )
            if (status == 1) {
                stopPlayingTimer()
                // 清空
                mViewModel.printLog(
                    TAG_PLAYING_DURATION, "累计播放时长 上报结束全部清零 在播时长：$playingTime"
                )
                startPlayTime = 0L
                playingTime = 0L
                //保存播放进度到数据库 不处理高光视频
                if (info.videoLinkType == 1) {
                    mViewModel.updateViewHistoryProgress(mCurrentDuration, duration, info)
                }
            }
            if (bookEntity?.first_play_source.isNullOrEmpty()) {
                info.bookId?.let {
                    DzDataRepository.bookDao().insertOrUpdateBooks(BookEntity(it).apply {
                        first_play_source = mFirstPlaySource
                    })
                }
            }
        }
    }

    private var needOpenWelfDialog = false

    private fun openWelfDialog() {
        if (!isPauseAdShow()) {
            val holder =
                ViewPager2Helper.findViewHolderByPosition(mViewBinding.vp, mCurrentPosition)
            if (holder is NewRecommendVideoViewHolder) {
                needOpenWelfDialog = if (mViewModel.canPlay()) {
                    WelfareMS.get()?.openWelfDialog()
                    false
                } else {
                    true
                }
            }
        }
    }

    private var needOpenPushDialog = false

    private fun openPushDialog() {
        if (isResumed) {
            val holder =
                ViewPager2Helper.findViewHolderByPosition(mViewBinding.vp, mCurrentPosition)
            needOpenPushDialog = if (mViewModel.canPlay() && holder !is HomeAdVideoViewHolder) {
                BCommonMS.get()?.openPush(
                    BCommonMC.PUSH_SHOW_LOCATION_HOME, BCommonMC.PUSH_SHOW_TYPE_PLAY, true
                )
                false
            } else {
                true
            }
        }
    }

    /**
     *  暂停广告是否正在显示  true：正在显示，false：未显示
     * @return
     */
    private fun isPauseAdShow(): Boolean {
        return MainMC.pauseAdShown
    }

    /**
     * 已经播放过
     */
    private var hasPlayed = false

    /**
     * 用户是否正在观看推荐页视频
     */
    private var isWatching = false
        set(value) {
            if (value) hasPlayed = true
            if (field == value) return
            field = value
            HomeME.get().recommendIsWatching().post(value)
        }

    override fun getRefreshState() = refreshState

    override fun getPageName(): String {
        return if (BBaseKV.recommendContent) "推荐" else "精选"
    }

    override fun getPageId(): String? {
        return PageConstant.RECOMMEND_ID
    }

    override fun onVisibilityChanged(visible: Boolean) {
        super.onVisibilityChanged(visible)
        if (visible) {
            TimeMonitorManager.getMonitor(MonitorMC.SCENE_RCMD).recordTime(MonitorMC.STAGE_START)
            DzTrackEvents.get().hivePv()
                .pType("page_view")
                .withOmapSource(OmapNode().apply {
                    rgts = BBaseKV.regTime
                    nowChTime = BBaseKV.chTime
                    is_login = if (CommInfoUtil.hasLogin()) 1 else 0
                    pageName = "首页-${getPageName()}"
                })
                .withQmapSource(QmapNode().apply {
                    eventType = "page_view"
                })
                .track()
            openScreenOn()
        } else {
            TimeMonitorManager.getMonitor(MonitorMC.SCENE_RCMD).end()  // 结束计时
            val holder = ViewPager2Helper.findViewHolderByPosition(mViewBinding.vp, mCurrentPosition)
            if (holder is NewRecommendVideoViewHolder) {
                holder.controller.sendEvent(VideoMC.EVENT_PAGE_INVISIBLE)
            }
            closeScreenOn()
        }
    }

    /**
     * onPrepared开始结束时间记录
     */
    private fun recordPrepared(status: Int) {
        if (status == 0) {//onPrepared开始
            PlayerMonitorManager.getPlayerMonitor(HomeMC.RECOMMEND_TRACK_TAG)
                .recordTime(PlayerMonitorManager.TAG_START_PLAY_TIME_START)
        } else {//onPrepared结束
            PlayerMonitorManager.getPlayerMonitor(HomeMC.RECOMMEND_TRACK_TAG)
                .recordTime(PlayerMonitorManager.TAG_START_PLAY_TIME_END)
        }
    }

    /**
     * 首帧渲染开始结束时间记录
     */
    private fun recordRenderingStart(status: Int) {
        if (status == 0) {//首帧渲染开始
            PlayerMonitorManager.getPlayerMonitor(HomeMC.RECOMMEND_TRACK_TAG)
                .recordTime(PlayerMonitorManager.TAG_FIRST_RENDERED_TIME_START)
        } else {//首帧渲染结束
            PlayerMonitorManager.getPlayerMonitor(HomeMC.RECOMMEND_TRACK_TAG)
                .recordTime(PlayerMonitorManager.TAG_FIRST_RENDERED_TIME_END)
        }
    }

    /**
     * 用户感知的耗时开始结束时间记录
     */
    private fun recordUserSenseTime(status: Int) {
        if (status == 0) {//用户感知的耗时开始
            LogUtil.d("player_splash_tag", "首页推荐记录用户感知的耗时开始")
            PlayerMonitorManager.getPlayerMonitor(HomeMC.RECOMMEND_TRACK_TAG)
                .recordTime(PlayerMonitorManager.TAG_USER_SENSE_TIME_START)
        } else {//用户感知的耗时结束
            if (!SplashDisplayStateManager.isSplashShowing()) {
                LogUtil.d("player_splash_tag", "首页推荐页用户感知的耗时结束")
                PlayerMonitorManager.getPlayerMonitor(HomeMC.RECOMMEND_TRACK_TAG)
                    .recordTime(PlayerMonitorManager.TAG_USER_SENSE_TIME_END)
            } else {
                LogUtil.d("player_splash_tag", "有广告显示，首页推荐页用户感知的耗时结束不记录")
                PlayerMonitorManager.getPlayerMonitor(HomeMC.RECOMMEND_TRACK_TAG)
                    .recordTime(PlayerMonitorManager.TAG_USER_SENSE_TIME_START)
                PlayerMonitorManager.getPlayerMonitor(HomeMC.RECOMMEND_TRACK_TAG)
                    .recordTime(PlayerMonitorManager.TAG_USER_SENSE_TIME_END)
            }
        }
    }


    /**
     * 获取视频的总长度
     */
    private fun getPlayerDuration(): Long {
        return mPlayerController.getPlayerDuration()
    }

    /**
     * 获取播放器的暂停状态
     */
    private fun getPlayerPauseStatus(): Boolean {
        return mPlayerController.getPlayerPauseStatus()
    }

    /**
     * 设置开始播放的时间（精准时刻）
     */
    private fun setStartTime(position: Long) {
        mPlayerController.setStartTime(position)
    }

    /**
     * 开启关闭httpdns
     */
    private fun enableHttpDns(openHttpDns: Boolean) {
        mPlayerController.enableHttpDns(openHttpDns)
    }

    /**
     *  倍速播放
     */
    private fun setSpeed(speed: Float) {
        mPlayerController.setSpeed(speed)
    }

    /**
     * 设置进度条进度
     */
    private fun seekBarSeekTo(progress: Int) {
        mPlayerController.seekBarSeekTo(progress)
    }

    /**
     * 切换进度
     */
    private fun seekTo(progress: Long, startPlay: Boolean) {
        mPlayerController.seekTo(progress, startPlay)
    }

    /**
     * 开始播放
     */
    private fun playerStart() {
        mPlayerController.playerStart()
    }

    /**
     * 停止播放
     */
    private fun playerStop() {
        mPlayerController.playerStop()
    }

    /**
     * 暂停播放
     */
    private fun playerPause() {
        mPlayerController.playerPause()
    }

    /**
     * 恢复播放器播放
     */
    private fun playerResume() {
        mPlayerController.playerResume()
    }

    /**
     * 开启关闭手势控制。
     */
    private fun enableGesture(enable: Boolean) {
        mPlayerController.enableGesture(enable)
    }

    /**
     * 根据时间显示备案号
     */
    private fun showRecordNumberByTime(time: Long) {
        mPlayerController.showRecordNumberByTime(time)
    }

    private fun changeUrlTo(url: String?) {
        mPlayerController.changeUrlTo(url)
    }

    private fun updateScaleMode(heightWidthRatio: Float, isLandVideo: Boolean? = null) {
        mPlayerController.updateScaleMode(heightWidthRatio, isLandVideo)
    }

    private fun updateCurPosition(position: Int) {
        mPlayerController.updateCurPosition(position)
    }

    private fun getRenderFPS(): Float {
        return mPlayerController.getRenderFPS()
    }

    private fun getAudioBitrate(): Float {
        return mPlayerController.getAudioBitrate()
    }

    private fun getVideoBitrate(): Float {
        return mPlayerController.getVideoBitrate()
    }

    private fun getDownloadBitrate(): Float {
        return mPlayerController.getDownloadBitrate()
    }

    private fun getVolume(): Float {
        return mPlayerController.getVolume()
    }

    private fun getSpeed(): Float {
        return mPlayerController.getSpeed()
    }

    private fun prepareAndStart(currentPosition: Int, onPageSelected: Boolean) {
        mPlayerController.prepareAndStart(currentPosition, onPageSelected)
    }

    /***
     * 正在拖动
     */
    private fun isDragging(): Boolean {
        return mPlayerController.isDragging()
    }

    /**
     * 更新收藏状态
     */
    private fun favoriteStatus(favorite: Boolean, tip: String?) {
        mPlayerController.favoriteStatus(favorite, tip)
    }

    /**
     * 播放收藏动画
     */
    private fun playFavoriteAnimation() {
        mPlayerController.playFavoriteAnimation()
    }

    /**
     * 点赞状态
     */
    private fun likesStatus(isLikes: Boolean, tip: String?) {
        mPlayerController.likesStatus(isLikes, tip)
    }

    /**
     * 当前剧集是否被收藏
     */
    private fun isLikes(): Boolean {
        return mPlayerController.isLikes()
    }

    /**
     * 更新分享状态
     */
    private fun updateShare(showShare: Boolean, showNormalIcon: Boolean? = null, tip: String?) {
        mPlayerController.updateShare(showShare, showNormalIcon, tip)
    }

    /**
     * 更新倒计时描述
     */
    private fun updateNextText(time: Long) {
        mPlayerController.updateNextText(time)
    }

    private fun moveTo(vid: String) {
        mPlayerController.moveTo(vid)
    }

    private fun getCurBuffer(): Long {
        val holder = ViewPager2Helper.findViewHolderByPosition(
            mViewBinding.vp,
            mViewBinding.vp.currentItem
        )
        if (holder is NewRecommendVideoViewHolder) {
            LogUtil.d(
                "player_cur_buffer",
                "NewRecommendFragment：当前播放器剩余缓存== ${holder.controller.getCurBuffer()}" +
                        "   播放器tag==${holder.controller.getVid()}" +
                        "   当前bookid==${mPageAdapter.getData(mViewBinding.vp.currentItem)?.bookId}"
            )
            return holder.controller.getCurBuffer()
        }
        return -1
    }

    //显示福利金币动画
    override fun showIconsPlay() {
        mViewBinding.tcIcons.startPlay()
    }
}


