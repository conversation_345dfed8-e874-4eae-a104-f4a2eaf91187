package com.dz.business.track.trace

import com.blankj.utilcode.util.GsonUtils
import com.dz.business.base.data.bean.BaseBean

/**
 * @Author: guyh
 * @Date: 2023/4/28 17:22
 * @Description:
 * @Version:1.0
 */
class SourceNode : BaseBean() {
    companion object {
        fun fromJson(routeSource: String?): SourceNode? {
            try {
                routeSource?.let {
                    if (it.isNotEmpty()) {
                        return GsonUtils.fromJson(it, SourceNode::class.java)
                    }
                }

            } catch (e: Throwable) {

            }
            return null
        }

        //----------------------------origin----------------------------
        //听书
        const val origin_ts = "ts"

        //投放书籍
        const val origin_tfsj = "tfsj"

        //首页
        const val origin_sy = "sy"

        //追剧
        const val origin_zj = "zj"

        //书城(剧场)
        const val origin_nsc = "nsc"

        //书城(小说)
        const val origin_xs_nsc = "xs_nsc"

        //推送
        const val origin_push = "push"

        //个人中心
        const val origin_grzx = "grzx"

        //搜索页面
        const val origin_ssym = "ssym"

        //阅读器
        const val origin_ydq = "ydq"

        //达人推荐
        const val origin_drtj = "drtj"

        //演员信息
        const val origin_yyxx = "yyxx"

        // 简介页
        const val origin_jjy = "jjy"

        //最近观看
        const val origin_zjgk = "zjgk"

        //社区页面
        const val origin_sqym = "sqym"

        //福利中心
        const val origin_flzx = "flzx"

        //----------------------------origin_name----------------------------
        const val origin_name_tfsj = "投放书籍"
        const val origin_name_sy = "首页"
        const val origin_name_zj = "追剧"
        const val origin_name_jc = "剧场"
        const val origin_name_zz = "在追"
        const val origin_name_nsc = "书城"
        const val origin_name_xs_nsc = "小说书城"
        const val origin_name_push = "推送"
        const val origin_name_zjgk = "最近观看"
        const val origin_name_grzx = "个人中心"
        const val origin_name_ssym = "搜索页面"
        const val origin_name_sqym = "社区页面"
        const val origin_name_ydq = "阅读器"
        const val origin_name_drtj = "达人推荐"
        const val origin_name_yyxx = "演员信息"
        const val origin_name_flzx = "福利中心"
        const val ORIGIN_NAME_JJY = "简介页"


        //----------------------------channel_id----------------------------
        //投放书籍
        const val channel_id_tfsj = "tfsj"

        //在追
        const val channel_id_zz = "zz"

        //精选
        const val channel_id_jx = "jx"

        //推荐
        const val channel_id_tj = "tj"

        //剧单
        const val channel_id_jd = "jd"

        //新剧
        const val channel_id_xj = "xj"

        //本地通知
        const val channel_id_bdtz = "bdtz"

        //厂商推送
        const val channel_id_csts = "csts"

        //观看历史
        const val channel_id_gkls = "gkls"

        //我的消息
        const val channel_id_wdxx = "wdxx"

        //我的在追
        const val channel_id_follow = "follow"

        //我的点赞
        const val channel_id_like = "like"

        //搜索结果
        const val channel_id_ssjg = "ssjg"

        //搜索结果达人推荐
        const val channel_id_ssjg_drtj = "ssjg_drtj"

        //联想结果
        const val channel_id_lxjg = "lxjg"

        //联想结果达人推荐
        const val channel_id_lxjg_drtj = "lxjg_drtj"

        //热剧推荐
        const val channel_id_rjtj = "rjtj"

        //猜你喜欢
        const val channel_id_cnxh = "cnxh"

        //大家都在搜
        const val channel_id_djdzs = "djdzs"

        //剧末推荐
        const val channel_id_jmtj = "jmtj"

        //剪切板读取
        const val channel_id_jqbdq = "jqbdq"

        const val channel_id_tctj = "tctj"

        const val channel_id_tctj_rec = "tctj_rec"

        //演员信息
        const val channel_id_yyxx = "yyxx"

        //相关推荐
        const val channel_id_xgtj = "xgtj"

        //话题列表
        const val channel_id_htlb = "htlb"

        //帖子发布页
        const val channel_id_tzfb = "tzfb"

        //话题详情页
        const val channel_id_htxq = "htxq"

        //帖子详情页
        const val channel_id_tzxq = "tzxq"

        //个人主页
        const val channel_id_grzy = "grzy"


        //章末推荐
        const val MODULE_YDQ_ZMTJ = "zmtj"

        //终章推荐
        const val MODULE_YDQ_ZZTJ = "zztj"

        //章末文字链
        const val MODULE_YDQ_ZMWZL = "zmwzl"

        //----------------------------channel_id----------------------------
        //投放书籍
        const val channel_name_tfsj = "投放书籍"

        //在追
        const val channel_name_zz = "在追"

        //精选
        const val channel_name_jx = "精选"

        //推荐
        const val channel_name_tj = "推荐"

        const val channel_name_jd = "剧单"

        //本地通知
        const val channel_name_bdtz = "本地通知"

        //厂商推送
        const val channel_name_csts = "厂商推送"

        //观看历史
        const val channel_name_gkls = "观看历史"

        //我的消息
        const val channel_name_wdxx = "我的消息"

        //我的在追
        const val channel_name_follow = "我的在追"

        //我的点赞
        const val channel_name_like = "我的点赞"

        //搜索结果
        const val channel_name_ssjg = "搜索结果"

        //搜索结果
        const val channel_name_ssjg_drtj = "搜索结果达人推荐"

        //联想结果
        const val channel_name_lxjg = "联想结果"

        //联想结果
        const val channel_name_lxjg_drtj = "联想结果达人推荐"

        //猜你喜欢
        const val channel_name_cnxh = "猜你喜欢"

        //大家都在搜
        const val channel_name_djdzs = "大家都在搜"

        //热剧推荐
        const val channel_name_rjtj = "热剧推荐"

        //剧末推荐
        const val channel_name_jmtj = "剧末推荐"

        //剧末推荐
        const val channel_name_jqbdq = "剪切板读取"

        const val channel_name_tctj = "退出推荐"

        const val channel_name_tctj_rcmd = "退出挽留推荐"

        //演员信息
        const val channel_name_yyxx = "演员信息"

        //相关推荐
        const val channel_name_xgtj = "相关推荐"

        //话题列表页
        const val channel_name_htlb = "话题列表页"

        //帖子发布页
        const val channel_name_tzfb = "帖子发布页"

        //帖子详情页
        const val channel_name_tzxq = "帖子详情页"

        //个人主页
        const val channel_name_grzy = "个人主页"

        //帖子详情页
        const val channel_name_htxq = "话题详情页"

        //----------------------------channel_id----------------------------
        //追剧
        const val column_id_zj = "zj"

        //聚合卡片
        const val column_id_jhkp = "jhkp"

        //在看
        const val column_id_zk = "zk"

        //搜索关键词
        const val column_id_gjc = "gjc"

        //相关推荐
        const val column_id_xgtj = "xgtj"

        //大家都在看
        const val column_id_dzk = "dzk"

        //帖子流_全部
        const val column_id_tzl_qb = "tzl_qb"

        //帖子流_最新
        const val column_id_tzl_zx = "tzl_zx"

        //帖子流_最热
        const val column_id_tzl_zr = "tzl_zr"

        //评论
        const val column_id_pl = "pl"

        //点赞
        const val column_id_dz = "dz"

        //----------------------------channel_name----------------------------
        //追剧
        const val column_name_zj = "追剧"

        //最近在追剧
        const val column_name_zjgk = "最近在追剧"

        //在看
        const val column_name_zk = "在看"

        //搜索关键词
        const val column_name_gjc = "搜索关键词"

        //聚合卡片
        const val column_name_jhkp = "聚合卡片"

        //大家都在看
        const val column_name_dzk = "大家都在看"

        //帖子流_全部
        const val column_name_tzl_qb = "帖子流_全部"

        //帖子流_最新
        const val column_name_tzl_zx = "帖子流_最新"

        //帖子流_最热
        const val column_name_tzl_zr = "帖子流_最热"

        //评论
        const val column_name_pl = "评论"

        //点赞
        const val column_name_dz = "点赞"

        //-------------------------firstPlaySource----------------------
        const val PLAY_SOURCE_SYTJ = "sy_tj"  // 播放来源：首页推荐
        const val PLAY_SOURCE_SYTJ_JHKP = "sy_tj_jhkp"  // 播放来源：首页推荐_聚合卡片
        const val PLAY_SOURCE_SYJD = "sy_jd"  // 播放来源：首页推荐
        const val PLAY_SOURCE_SSYM_SSJG = "ssym_ssjg"  // 播放来源：搜索结果
        const val PLAY_SOURCE_SSYM_SSGJ_DRTJ = "ssym_ssgj_drtj"  // 播放来源：搜索结果达人推荐
        const val PLAY_SOURCE_SSYM_LXJG = "ssym_lxjg"  // 播放来源：联想结果
        const val PLAY_SOURCE_SSYM_LXJG_DRTJ = "ssym_lxjg_drtj"  // 播放来源：联想结果达人推荐
        const val PLAY_SOURCE_SSYM_RJTJ = "ssym_rjtj"  // 播放来源：热剧推荐
        const val PLAY_SOURCE_SSYM_CNXH = "ssym_cnxh"  // 播放来源：猜你喜欢
        const val PLAY_SOURCE_SSYM_DJDZS = "ssym_djdzs"  // 播放来源：大家都在搜
        const val PLAY_SOURCE_SSYM_QB = "ssym_xj_qb"  // 播放来源：搜索新剧全部
        const val PLAY_SOURCE_SSYM_NS = "ssym_xj_ns"  // 播放来源：搜索新剧男生
        const val PLAY_SOURCE_SSYM_VS = "ssym_xj_vs"  // 播放来源：搜索新剧女生
        const val PLAY_SOURCE_PUSH = "push"  // 播放来源：PUSH
        const val PLAY_SOURCE_TFSJ = "tfsj"  // 播放来源：PUSH
        const val PLAY_SOURCE_DRTJ_JQBDQ = "drtj_jqbdq"  // 播放来源：剪切板读取
        const val PLAY_SOURCE_YDQ = "ydq_jmtj"   // 播放来源：剧末推荐
        const val PLAY_SOURCE_YYXX = "yyxx"//演员信息
        const val PLAY_SOURCE_YYTC = "yytc"//演员弹窗  产品未给出，暂时仿照演员信息填写
        const val PLAY_SOURCE_SSYM_PHB = "ssym_phb"//搜索页面排行榜单
        const val PLAY_SOURCE_SSYM_XGTJ = "ssym_xgtj"//搜索页面相关推荐
        const val PLAY_SOURCE_WDXX_XX = "grzx_wdxx_xx"//我的消息-消息
        const val PLAY_SOURCE_WDXX_DZ = "grzx_wdxx_dz"//我的消息-点按
        const val PLAY_SOURCE_WDXX_PL = "grzx_wdxx_pl"//我的消息-评论
        const val PLAY_SOURCE_SQYM_TZFB = "sqym_tzfb"//社区页面-帖子发布页
        const val PLAY_SOURCE_SQYM_TZXQ = "sqym_tzxq"//社区页面-帖子详情页
        const val PLAY_SOURCE_SQYM_DZK = "sqym_dzk"//社区页面-话题详情页-大家都在看
        const val PLAY_SOURCE_SQYM_TZL_QB = "sqym_tzl_qb"//社区页面-话题详情页-帖子流_全部
        const val PLAY_SOURCE_SQYM_TZL_ZX = "sqym_tzl_zx"//社区页面-话题详情页-帖子流_最新
        const val PLAY_SOURCE_SQYM_TZL_ZR = "sqym_tzl_zr"//社区页面-话题详情页-帖子流_最热

        //-------------------------book_type----------------------
        const val BOOK_TYPE_XS = "xs"   // 类型：小说
        const val BOOK_TYPE_VIDEO = "video"//类型：短剧
        const val BOOK_COLUMN_ID_QB = "qb"   // 类型：新剧全部
        const val BOOK_COLUMN_ID_NS = "ns"   // 类型：新剧男生
        const val BOOK_COLUMN_ID_VS = "vs"   // 类型：新剧女生

    }

    var origin = ""
        //来源
        set(value) {
            field = value
            originName = getOriginName(value)
        }

    /**
     * 通过origin 获取来源名称
     *
     * @param origin
     * @return
     */
    private fun getOriginName(origin: String): String {
        return when (origin) {
            origin_tfsj -> "投放书籍"
            origin_sy -> "首页"
            origin_nsc -> "书城"
            origin_push -> "推送"
            origin_grzx -> "个人中心"
            origin_ssym -> "搜索页面"
            origin_ts -> "听书"
            else -> ""
        }
    }

    var originName = ""
    var channelId = ""//频道id
    var channelName = ""//频道名称
    var columnId = "" //栏目Id
    var columnName = "" //栏目名称
    var contentId = "" //item  详情id
    var contentName = ""//item 详情名称
    internal var triggerTime = 0L//时间戳毫秒值
    var logId = ""//请求ID
    var expId = ""//大数据场景id
    var strategyId = ""//大数据分组ID
    var strategyName = ""//大数据分组名称

    var contentType = ""//条目跳转类型 路由 action

    var channelPos = "" //对应第几个频道，从0开始
    var columnPos = "" //对应第几个栏目，从0开始
    var contentPos = "" //对应第几条内容，从0开始

    var itemDataId: String? = "" //时间


}