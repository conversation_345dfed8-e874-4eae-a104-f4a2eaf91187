package com.dz.business.track.monitor

import com.dz.business.track.base.addParam
import com.dz.business.track.events.sensor.VideoStartPlayingTE
import com.dz.foundation.base.utils.LogUtil
import com.dz.foundation.base.utils.monitor.PlayerMonitor
import com.dz.foundation.base.utils.monitor.PlayerMonitorManager
import com.dz.foundation.base.utils.monitor.PlayerMonitorManager.TAG_FIRST_RENDERED_TIME_END
import com.dz.foundation.base.utils.monitor.PlayerMonitorManager.TAG_FIRST_RENDERED_TIME_START
import com.dz.foundation.base.utils.monitor.PlayerMonitorManager.TAG_REQUEST_TIME_END
import com.dz.foundation.base.utils.monitor.PlayerMonitorManager.TAG_REQUEST_TIME_START
import com.dz.foundation.base.utils.monitor.PlayerMonitorManager.TAG_START_PLAY_TIME_END
import com.dz.foundation.base.utils.monitor.PlayerMonitorManager.TAG_START_PLAY_TIME_START
import com.dz.foundation.base.utils.monitor.PlayerMonitorManager.TAG_USER_SENSE_TIME_END
import com.dz.foundation.base.utils.monitor.PlayerMonitorManager.TAG_USER_SENSE_TIME_START
import com.dz.foundation.base.utils.monitor.PlayerMonitorManager.getPlayerMonitor

/**
 * @Author: guyh
 * @Date: 2024/8/1
 * @Description:
 * @Version:1.0
 */

fun VideoStartPlayingTE.trackApiRequestTime(vid: String): VideoStartPlayingTE {
    getPlayerMonitor(vid).apply {
        computeTime(TAG_REQUEST_TIME_START, TAG_REQUEST_TIME_END).let {
            addParam("RequestTime", it ?: 0)
            addParam("ApiPort", api)
            LogUtil.d("PlayerMonitorUtilsTag", "RequestTime==${it ?: 0}   ApiPort==$api")
        }
    }
    PlayerMonitorManager.clearMonitor(vid)
    return this
}

fun VideoStartPlayingTE.trackPlayerTime(vid: String): VideoStartPlayingTE {
    getPlayerMonitor(vid).apply {
        computeTime(TAG_START_PLAY_TIME_START, TAG_START_PLAY_TIME_END)?.let {
            addParam("StartPlayTime", it)
            LogUtil.d("PlayerMonitorUtilsTag", "StartPlayTime==$it")
        }

        computeTime(TAG_FIRST_RENDERED_TIME_START, TAG_FIRST_RENDERED_TIME_END)?.let {
            addParam("FirstRenderedTime", it)
            LogUtil.d("PlayerMonitorUtilsTag", "FirstRenderedTime==$it")
        }

        computeTime(TAG_USER_SENSE_TIME_START, TAG_USER_SENSE_TIME_END)?.let {
            addParam("UserSenseTime", it)
            LogUtil.d("PlayerMonitorUtilsTag", "userSenseTime==$it")
        }
        PlayerMonitorManager.clearMonitor(vid)
    }
    return this
}

fun PlayerMonitor.computeTime(startTag: String, endTag: String): Long? {
    playTimes[endTag]?.let { endTime ->
        playTimes[startTag]?.let { startTime ->
            if (startTime > 0 && endTime > 0) {
                val result = endTime - startTime
                if (result > 120_000) {
                    return null
                }
                return result
            }
        }
    }
    return null
}