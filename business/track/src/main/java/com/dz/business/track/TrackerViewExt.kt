package com.dz.business.track

import android.view.View
import com.dz.business.track.tracker.SensorTracker
import org.json.JSONObject
import java.io.Serializable

class TrackProperties : Serializable {
    var ignoreAutoTrack = false//忽略自动采集
    var elementParam: Any? = null
    var elementContent: String? = null
    var elementId: String? = null

    fun toMap(): MutableMap<String, Any> {
        val propertiesMap = mutableMapOf<String, Any>()
        elementParam?.let {
            propertiesMap["ele_param_value"] = it
        }
        elementContent?.let {
            propertiesMap["\$element_content"] = it
        }
        elementId?.let {
            propertiesMap["\$element_id"] = it
        }
        return propertiesMap
    }
}

fun <T : View> T.trackProperties(
    elementId: String? = null,
    elementContent: String? = null,
    elementParam: Any? = null,
    ignoreAutoTrack: Boolean? = null
): TrackProperties {
    val viewElementProperties = getViewElementProperties(this)
    elementParam?.let {
        viewElementProperties.elementParam = it
    }
    elementContent?.let {
        viewElementProperties.elementContent = it
    }
    elementId?.let {
        viewElementProperties.elementId = it
    }

    if (ignoreAutoTrack == true) {
        //忽略自动采集，改为手动调用
        SensorTracker.ignoreViewAutoTrack(this)
        viewElementProperties.ignoreAutoTrack = true
    }
    SensorTracker.setViewProperties(this, viewElementProperties.toMap())
    return viewElementProperties
}

private fun getViewElementProperties(view: View): TrackProperties {

    val tag = view.getTag(R.id.tracker_view_element_properties_tag)
    return if (tag != null && tag is TrackProperties) {
        tag
    } else {
        val extendParams = TrackProperties()
        view.setTag(R.id.tracker_view_element_properties_tag, extendParams)
        extendParams
    }

}