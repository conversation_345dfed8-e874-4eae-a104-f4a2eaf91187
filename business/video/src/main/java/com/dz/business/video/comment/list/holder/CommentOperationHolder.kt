package com.dz.business.video.comment.list.holder

import android.graphics.Rect
import android.view.TouchDelegate
import android.view.View
import com.dz.business.base.utils.MultiTouchDelegate
import com.dz.business.video.data.CommentInfoVo
import com.dz.business.video.data.CommentOperationBean
import com.dz.business.video.databinding.VideoCommentOperationHolderBinding
import com.dz.foundation.base.utils.ScreenUtil
import com.dz.foundation.ui.utils.click.setOnAntiFrequentClickListener

/**
 *@Author: zhanggy
 *@Date: 2024-10-21
 *@Description:展开、收起、loading holder
 *@Version:1.0
 */
class CommentOperationHolder(
    private val viewBinding: VideoCommentOperationHolderBinding, private val listener: Listener?
) : CommentBaseHolder(viewBinding.root) {

    private var operationBean: CommentOperationBean? = null

    init {
        viewBinding.tvBtnName.setOnAntiFrequentClickListener {
            when (operationBean?.state) {
                CommentOperationBean.COMMENT_OPERATION_EXPAND_NUM, CommentOperationBean.COMMENT_OPERATION_EXPAND -> {
                    operationBean?.state = CommentOperationBean.COMMENT_OPERATION_LOADING
                    viewBinding.groupLoading.visibility = View.VISIBLE
                    viewBinding.groupDesc.visibility = View.GONE
                    listener?.onExpandClick(operationBean?.rootComment)
                }

                CommentOperationBean.COMMENT_OPERATION_COLLAPSE -> {
                    listener?.onCollapse(operationBean?.rootComment)
                }
            }
        }
        viewBinding.tvBtnName.post {
            // 获取点击区域
            val btnRect = Rect()
            viewBinding.tvBtnName.getHitRect(btnRect)

            // 扩大点击区域
            val ext = ScreenUtil.dip2px(viewBinding.root.context, 40)
            btnRect.inset(-ext, -ext)

            // 创建MultiTouchDelegate并添加到父View
            val multiTouchDelegate = MultiTouchDelegate(Rect(), viewBinding.root)
            multiTouchDelegate.addDelegate(TouchDelegate(btnRect, viewBinding.tvBtnName))
            viewBinding.root.touchDelegate = multiTouchDelegate
        }
    }

    fun bindData(operationBean: CommentOperationBean) {
        this.operationBean = operationBean
        viewBinding.apply {
            when (operationBean.state) {
                CommentOperationBean.COMMENT_OPERATION_EXPAND_NUM -> {
                    if (operationBean.remainReplyNum > 0) {
                        tvBtnName.text = "展开${operationBean.remainReplyNum}条回复"
                    } else {
                        tvBtnName.text = "展开更多"
                    }
                    ivArrow.rotation = 0f
                    viewBinding.groupDesc.visibility = View.VISIBLE
                    viewBinding.groupLoading.visibility = View.GONE
                }

                CommentOperationBean.COMMENT_OPERATION_EXPAND -> {
                    tvBtnName.text = "展开更多"
                    ivArrow.rotation = 0f
                    viewBinding.groupDesc.visibility = View.VISIBLE
                    viewBinding.groupLoading.visibility = View.GONE
                }

                CommentOperationBean.COMMENT_OPERATION_COLLAPSE -> {
                    tvBtnName.text = "收起"
                    ivArrow.rotation = 180f
                    viewBinding.groupDesc.visibility = View.VISIBLE
                    viewBinding.groupLoading.visibility = View.GONE
                }

                CommentOperationBean.COMMENT_OPERATION_LOADING -> {
                    viewBinding.groupDesc.visibility = View.GONE
                    viewBinding.groupLoading.visibility = View.VISIBLE
                }
            }
        }
    }

    interface Listener {
        fun onExpandClick(rootComment: CommentInfoVo?)
        fun onCollapse(rootComment: CommentInfoVo?)
    }
}
