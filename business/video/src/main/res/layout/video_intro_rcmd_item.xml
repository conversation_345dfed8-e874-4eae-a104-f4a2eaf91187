<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:ignore="MissingDefaultResource">

    <merge tools:parentTag="com.dz.foundation.ui.widget.DzConstraintLayout">

        <com.dz.foundation.ui.widget.DzImageView
            android:id="@+id/iv_book_cover"
            android:layout_width="@dimen/common_dp109"
            android:layout_height="@dimen/common_dp156"
            android:scaleType="centerCrop"
            android:src="@drawable/bbase_ic_cover_default_new"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.dz.business.base.ui.component.PlayLetLabelComp
            android:id="@+id/label_keyword"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/common_dp16"
            app:layout_constraintEnd_toEndOf="@+id/iv_book_cover"
            app:layout_constraintTop_toTopOf="@+id/iv_book_cover" />

        <com.dz.foundation.ui.widget.DzView
            android:id="@+id/cover_shadow"
            android:layout_width="0dp"
            android:layout_height="@dimen/common_dp22"
            android:background="@drawable/video_intro_item_gradient"
            app:layout_constraintBottom_toBottomOf="@+id/iv_book_cover"
            app:layout_constraintEnd_toEndOf="@+id/iv_book_cover"
            app:layout_constraintStart_toStartOf="@+id/iv_book_cover" />

        <com.dz.foundation.ui.widget.DzTextView
            android:id="@+id/tv_favorite_num"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/common_dp4"
            android:ellipsize="end"
            android:gravity="end|center_horizontal"
            android:includeFontPadding="false"
            android:maxLines="1"
            android:paddingEnd="@dimen/common_dp4"
            android:textColor="@color/common_FFFFFFFF"
            android:textSize="@dimen/common_dp11"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toBottomOf="@+id/iv_book_cover"
            app:layout_constraintEnd_toEndOf="@+id/iv_book_cover"
            app:layout_constraintStart_toStartOf="@+id/iv_book_cover"
            tools:ignore="RtlSymmetry,SpUsage"
            tools:text="57.2万人在追" />

        <com.dz.foundation.ui.widget.DzTextView
            android:id="@+id/tv_book_name"
            android:layout_width="@dimen/common_dp0"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/common_dp9"
            android:ellipsize="end"
            android:maxLines="1"
            android:paddingEnd="@dimen/common_dp2"
            android:textColor="@color/common_FF191919"
            android:textSize="@dimen/common_dp14"
            app:layout_constraintEnd_toEndOf="@+id/iv_book_cover"
            app:layout_constraintStart_toStartOf="@+id/iv_book_cover"
            app:layout_constraintTop_toBottomOf="@+id/iv_book_cover"
            tools:text="最强战神最强战神最强战神" />

        <com.dz.foundation.ui.widget.DzTextView
            android:id="@+id/tv_tags"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/common_dp3.5"
            android:ellipsize="end"
            android:gravity="start"
            android:maxLines="1"
            android:textColor="@color/common_FF959595"
            android:textSize="@dimen/common_dp12"
            android:visibility="gone"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="@+id/tv_book_name"
            app:layout_constraintStart_toStartOf="@+id/tv_book_name"
            app:layout_constraintTop_toBottomOf="@+id/tv_book_name"
            tools:text="逆袭 古装 逆袭 古装 逆袭 古装 "
            tools:visibility="visible" />

        <com.dz.foundation.ui.widget.DzTextView
            android:id="@+id/tv_rank"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/common_dp18"
            android:layout_marginTop="@dimen/common_dp3.5"
            android:drawableEnd="@drawable/video_ic_intro_rcmd_arrow_right"
            android:drawablePadding="@dimen/common_dp2"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:paddingStart="@dimen/common_dp5"
            android:paddingEnd="@dimen/common_dp5"
            android:textColor="@color/common_FFB37D44"
            android:textSize="@dimen/common_dp12"
            android:visibility="gone"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="@+id/tv_book_name"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toStartOf="@+id/tv_book_name"
            app:layout_constraintTop_toBottomOf="@+id/tv_book_name"
            app:shape_radius="@dimen/common_dp2"
            app:shape_solid_color="@color/common_FFFFF2D4"
            tools:text="热搜榜NO.1"
            tools:visibility="visible" />

    </merge>
</layout>