<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:ignore="MissingDefaultResource">

    <com.dz.foundation.ui.widget.DzConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/common_dp48">

        <com.dz.foundation.ui.widget.DzTextView
            android:id="@+id/tvName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:includeFontPadding="false"
            android:textColor="@color/reader_E6000000_DBFFFFFF"
            android:textSize="@dimen/common_dp16"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="哈哈哈哈哈"
            tools:ignore="SpUsage" />

        <com.dz.foundation.ui.widget.DzImageView
            android:id="@+id/iv_check"
            android:layout_width="@dimen/common_dp24"
            android:layout_height="@dimen/common_dp24"
            android:src="@drawable/reader_ic_check"
            android:layout_marginEnd="@dimen/common_dp2"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>
    </com.dz.foundation.ui.widget.DzConstraintLayout>
</layout>