<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:ignore="MissingDefaultResource">

    <com.dz.foundation.ui.widget.DzRelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <View
            android:id="@+id/leftPaddingView"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_alignParentStart="true"
            android:background="@color/common_FF171717" />

        <View
            android:id="@+id/rightPaddingView"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_alignParentEnd="true"
            android:background="@color/common_FF171717" />

        <View
            android:id="@+id/bottomPaddingView"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_alignParentBottom="true"
            android:layout_toStartOf="@+id/rightPaddingView"
            android:layout_toEndOf="@+id/leftPaddingView"
            android:background="@color/common_FF171717" />

        <com.dz.foundation.ui.widget.DzConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_above="@+id/bottomPaddingView"
            android:layout_alignStart="@+id/bottomPaddingView"
            android:layout_alignEnd="@+id/bottomPaddingView">

            <com.dz.business.reader.ui.component.menu.MenuTitleComp
                android:id="@+id/comp_menu_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:clickable="true"
                android:focusable="true"
                app:layout_constraintTop_toTopOf="parent" />

            <com.dz.foundation.ui.widget.DzImageView
                android:id="@+id/iv_tts"
                android:layout_width="@dimen/common_dp0"
                android:layout_height="@dimen/common_dp0"
                android:layout_marginEnd="@dimen/common_dp16"
                android:layout_marginBottom="@dimen/common_dp16"
                app:layout_constraintBottom_toTopOf="@id/menu_bottom"
                app:layout_constraintRight_toRightOf="parent" />

            <com.dz.foundation.ui.widget.DzConstraintLayout
                android:id="@+id/menu_bottom"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/common_FF171717"
                android:clickable="true"
                android:focusable="true"
                app:layout_constraintBottom_toBottomOf="parent">

                <com.dz.foundation.ui.widget.DzView
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/common_dp1"
                    android:background="@color/reader_08000000"
                    android:layout_marginBottom="@dimen/common_dp23"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toTopOf="@id/iv_switch"/>

                <com.dz.foundation.ui.widget.DzImageView
                    android:id="@+id/iv_pre_chapter"
                    android:layout_width="@dimen/common_dp24"
                    android:layout_height="@dimen/common_dp24"
                    android:layout_marginEnd="@dimen/common_dp32"
                    android:src="@drawable/reader_ic_arrow_left4"
                    app:layout_constraintBottom_toBottomOf="@+id/iv_switch"
                    app:layout_constraintEnd_toEndOf="@+id/layout_pre_chapter"
                    app:layout_constraintTop_toTopOf="@+id/iv_switch" />

                <View
                    android:id="@+id/layout_pre_chapter"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    app:layout_constraintBottom_toBottomOf="@+id/iv_switch"
                    app:layout_constraintEnd_toStartOf="@+id/layout_play"
                    app:layout_constraintHorizontal_chainStyle="spread_inside"
                    app:layout_constraintHorizontal_weight="1"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="@id/iv_switch" />

                <com.dz.foundation.ui.widget.DzImageView
                    android:id="@+id/iv_switch"
                    android:layout_width="@dimen/common_dp54"
                    android:layout_height="@dimen/common_dp54"
                    android:layout_marginBottom="@dimen/common_dp16"
                    android:src="@drawable/reader_ic_pause"
                    app:layout_constraintBottom_toTopOf="@+id/comp_section_progress"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent" />

                <com.airbnb.lottie.LottieAnimationView
                    android:id="@+id/loading"
                    android:layout_width="@dimen/common_dp54"
                    android:layout_height="@dimen/common_dp54"
                    android:visibility="visible"
                    app:lottie_autoPlay="false"
                    app:lottie_loop="true"
                    android:padding="@dimen/common_dp7"
                    android:background="@drawable/reader_tts_loading_bkg"
                    app:lottie_imageAssetsFolder="images"
                    app:lottie_rawRes="@raw/tts_loading"
                    app:layout_constraintStart_toStartOf="@+id/iv_switch"
                    app:layout_constraintEnd_toEndOf="@+id/iv_switch"
                    app:layout_constraintTop_toTopOf="@+id/iv_switch"
                    app:layout_constraintBottom_toBottomOf="@+id/iv_switch"/>

                <View
                    android:id="@+id/layout_play"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    app:layout_constraintBottom_toBottomOf="@+id/layout_pre_chapter"
                    app:layout_constraintEnd_toStartOf="@+id/layout_next_chapter"
                    app:layout_constraintHorizontal_weight="1"
                    app:layout_constraintStart_toEndOf="@+id/layout_pre_chapter"
                    app:layout_constraintTop_toTopOf="@+id/layout_pre_chapter" />

                <com.dz.foundation.ui.widget.DzImageView
                    android:id="@+id/iv_next_chapter"
                    android:layout_width="@dimen/common_dp24"
                    android:layout_height="@dimen/common_dp24"
                    android:layout_marginStart="@dimen/common_dp32"
                    android:src="@drawable/reader_ic_arrow_right4"
                    app:layout_constraintBottom_toBottomOf="@+id/iv_switch"
                    app:layout_constraintStart_toStartOf="@+id/layout_next_chapter"
                    app:layout_constraintTop_toTopOf="@+id/iv_switch" />

                <View
                    android:id="@+id/layout_next_chapter"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    app:layout_constraintBottom_toBottomOf="@+id/layout_pre_chapter"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_weight="1"
                    app:layout_constraintStart_toEndOf="@+id/layout_play"
                    app:layout_constraintTop_toTopOf="@+id/layout_pre_chapter" />

                <com.dz.business.reader.ui.component.menu.MenuSectionProgress
                    android:id="@+id/comp_section_progress"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/common_dp28"
                    android:layout_marginBottom="@dimen/common_dp16"
                    android:layout_marginStart="@dimen/common_dp12"
                    android:layout_marginEnd="@dimen/common_dp12"
                    app:layout_constraintBottom_toTopOf="@+id/layout_catalog" />

                <com.dz.foundation.ui.widget.DzLinearLayout
                    android:id="@+id/layout_catalog"
                    android:layout_width="@dimen/common_dp0"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:paddingTop="@dimen/common_dp8"
                    android:paddingBottom="@dimen/common_dp8"
                    android:orientation="vertical"
                    app:layout_constraintHorizontal_weight="1"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/layout_timer"
                    app:layout_constraintBottom_toBottomOf="parent">

                    <com.dz.foundation.ui.widget.DzImageView
                        android:id="@+id/iv_catalog"
                        android:layout_width="@dimen/common_dp24"
                        android:layout_height="@dimen/common_dp24"
                        android:src="@drawable/reader_ic_catalog"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent" />

                    <com.dz.foundation.ui.widget.DzTextView
                        android:id="@+id/tv_catalog"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/common_dp2"
                        android:includeFontPadding="false"
                        android:textColor="@color/reader_E6000000"
                        android:textSize="@dimen/common_dp10"
                        android:text="@string/reader_catalog"
                        android:maxLines="1"
                        android:ellipsize="end"
                        app:layout_constraintTop_toTopOf="@id/iv_catalog"
                        app:layout_constraintBottom_toBottomOf="@id/iv_catalog"
                        app:layout_constraintStart_toEndOf="@+id/iv_catalog"
                        tools:ignore="SpUsage" />
                </com.dz.foundation.ui.widget.DzLinearLayout>

                <com.dz.foundation.ui.widget.DzLinearLayout
                    android:id="@+id/layout_timer"
                    android:layout_width="@dimen/common_dp0"
                    android:layout_height="@dimen/common_dp0"
                    android:paddingTop="@dimen/common_dp8"
                    android:paddingBottom="@dimen/common_dp8"
                    android:gravity="center"
                    app:layout_constraintHorizontal_weight="1"
                    android:orientation="vertical"
                    app:layout_constraintStart_toEndOf="@id/layout_catalog"
                    app:layout_constraintEnd_toStartOf="@id/layout_timbre"
                    app:layout_constraintTop_toTopOf="@id/layout_catalog"
                    app:layout_constraintBottom_toBottomOf="@id/layout_catalog">

                    <com.dz.foundation.ui.widget.DzImageView
                        android:id="@+id/iv_time"
                        android:layout_width="@dimen/common_dp24"
                        android:layout_height="@dimen/common_dp24"
                        android:src="@drawable/reader_ic_timing" />

                    <com.dz.foundation.ui.widget.DzTextView
                        android:id="@+id/tv_time"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:includeFontPadding="false"
                        android:text="@string/reader_timing"
                        android:textColor="@color/reader_E6000000"
                        android:textSize="@dimen/common_dp10"
                        android:maxLines="1"
                        android:ellipsize="end"
                       android:layout_marginTop="@dimen/common_dp2"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintStart_toEndOf="@+id/iv_time"
                        app:layout_constraintEnd_toEndOf="parent"
                        tools:text="20:18"
                        tools:ignore="SpUsage"/>
                </com.dz.foundation.ui.widget.DzLinearLayout>

                <com.dz.foundation.ui.widget.DzLinearLayout
                    android:id="@+id/layout_timbre"
                    android:layout_width="@dimen/common_dp0"
                    android:layout_height="@dimen/common_dp0"
                    android:paddingTop="@dimen/common_dp8"
                    android:paddingBottom="@dimen/common_dp8"
                    android:gravity="center"
                    app:layout_constraintHorizontal_weight="1"
                    android:orientation="vertical"
                    app:layout_constraintStart_toEndOf="@id/layout_timer"
                    app:layout_constraintEnd_toStartOf="@id/layout_speech_rate"
                    app:layout_constraintTop_toTopOf="@id/layout_catalog"
                    app:layout_constraintBottom_toBottomOf="@id/layout_catalog">

                    <com.dz.foundation.ui.widget.DzImageView
                        android:id="@+id/iv_timbre"
                        android:layout_width="@dimen/common_dp24"
                        android:layout_height="@dimen/common_dp24"
                        android:src="@drawable/reader_ic_timbre" />

                    <com.dz.foundation.ui.widget.DzTextView
                        android:id="@+id/tv_timbre"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:includeFontPadding="false"
                        android:text="@string/reader_toggle_sound"
                        android:textColor="@color/reader_E6000000"
                        android:textSize="@dimen/common_dp10"
                        android:maxLength="4"
                        android:maxLines="1"
                        android:ellipsize="end"
                        android:layout_marginTop="@dimen/common_dp2"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintStart_toEndOf="@+id/iv_catalog"
                        app:layout_constraintEnd_toEndOf="parent"
                        tools:ignore="SpUsage"/>
                </com.dz.foundation.ui.widget.DzLinearLayout>

                <com.dz.foundation.ui.widget.DzLinearLayout
                    android:id="@+id/layout_speech_rate"
                    android:layout_width="@dimen/common_dp0"
                    android:layout_height="@dimen/common_dp0"
                    android:paddingTop="@dimen/common_dp8"
                    android:paddingBottom="@dimen/common_dp8"
                    android:gravity="center"
                    android:orientation="vertical"
                    app:layout_constraintTop_toTopOf="@id/layout_catalog"
                    app:layout_constraintBottom_toBottomOf="@id/layout_catalog"
                    app:layout_constraintStart_toEndOf="@id/layout_timbre"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_weight="1">

                    <com.dz.foundation.ui.widget.DzImageView
                        android:id="@+id/iv_speech_rate"
                        android:layout_width="@dimen/common_dp24"
                        android:layout_height="@dimen/common_dp24"
                        android:src="@drawable/reader_ic_speed_1d0" />

                    <com.dz.foundation.ui.widget.DzTextView
                        android:id="@+id/tv_speech_rate"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:includeFontPadding="false"
                        android:text="@string/reader_speech_rate"
                        android:textColor="@color/reader_E6000000"
                        android:textSize="@dimen/common_dp10"
                        android:maxLines="1"
                        android:layout_marginTop="@dimen/common_dp2"
                        android:ellipsize="end"
                        tools:ignore="SpUsage" />
                </com.dz.foundation.ui.widget.DzLinearLayout>

            </com.dz.foundation.ui.widget.DzConstraintLayout>

        </com.dz.foundation.ui.widget.DzConstraintLayout>
    </com.dz.foundation.ui.widget.DzRelativeLayout>
</layout>