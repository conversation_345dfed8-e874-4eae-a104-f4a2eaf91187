<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:ignore="MissingDefaultResource">

    <merge
        android:layout_width="match_parent"
        android:layout_height="@dimen/common_dp52"
        tools:background="@color/reader_color_FF171717"
        tools:parentTag="com.dz.foundation.ui.widget.DzConstraintLayout">

        <!--上一章-->
        <com.dz.foundation.ui.widget.DzConstraintLayout
            android:id="@+id/cl_pre_chapter"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            app:layout_constraintHorizontal_weight="1"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@id/cl_catalog">

            <com.dz.foundation.ui.widget.DzImageView
                android:id="@+id/iv_pre_ic"
                android:layout_width="@dimen/common_dp18"
                android:layout_height="@dimen/common_dp18"
                android:src="@drawable/reader_ic_arrow_left"
                android:layout_marginBottom="@dimen/common_dp20"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toStartOf="@id/tv_pre"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintHorizontal_chainStyle="packed"/>

            <com.dz.foundation.ui.widget.DzTextView
                android:id="@+id/tv_pre"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:includeFontPadding="false"
                android:text="@string/reader_pre_chapter"
                android:textColor="@color/reader_color_FFFFFFFF"
                android:textSize="@dimen/common_dp15"
                android:layout_marginStart="@dimen/common_dp2"
                app:layout_constraintStart_toEndOf="@id/iv_pre_ic"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@id/iv_pre_ic"
                app:layout_constraintBottom_toBottomOf="@id/iv_pre_ic"
                app:layout_constraintHorizontal_chainStyle="packed"
                tools:ignore="SpUsage" />
        </com.dz.foundation.ui.widget.DzConstraintLayout>

        <!--目录-->
        <com.dz.foundation.ui.widget.DzConstraintLayout
            android:id="@+id/cl_catalog"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            app:layout_constraintHorizontal_weight="1"
            app:layout_constraintStart_toEndOf="@id/cl_pre_chapter"
            app:layout_constraintEnd_toStartOf="@id/cl_next_chapter">

            <com.dz.foundation.ui.widget.DzImageView
                android:id="@+id/iv_catalog_ic"
                android:layout_width="@dimen/common_dp18"
                android:layout_height="@dimen/common_dp18"
                android:src="@drawable/reader_ic_catalog"
                android:layout_marginBottom="@dimen/common_dp20"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toStartOf="@id/tv_catalog"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintHorizontal_chainStyle="packed"/>

            <com.dz.foundation.ui.widget.DzTextView
                android:id="@+id/tv_catalog"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:includeFontPadding="false"
                android:text="@string/reader_catalog"
                android:textColor="@color/reader_color_FFFFFFFF"
                android:textSize="@dimen/common_dp15"
                android:layout_marginStart="@dimen/common_dp4"
                app:layout_constraintStart_toEndOf="@id/iv_catalog_ic"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@id/iv_catalog_ic"
                app:layout_constraintBottom_toBottomOf="@id/iv_catalog_ic"
                app:layout_constraintHorizontal_chainStyle="packed"
                tools:ignore="SpUsage" />
        </com.dz.foundation.ui.widget.DzConstraintLayout>

        <!--下一章-->
        <com.dz.foundation.ui.widget.DzConstraintLayout
            android:id="@+id/cl_next_chapter"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            app:layout_constraintHorizontal_weight="1"
            app:layout_constraintStart_toEndOf="@id/cl_catalog"
            app:layout_constraintEnd_toEndOf="parent">

            <com.dz.foundation.ui.widget.DzTextView
                android:id="@+id/tv_next"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/reader_next_chapter"
                android:textColor="@color/reader_color_FFFFFFFF"
                android:textSize="@dimen/common_dp15"
                android:includeFontPadding="false"
                android:layout_marginEnd="@dimen/common_dp2"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toStartOf="@id/iv_next_ic"
                app:layout_constraintTop_toTopOf="@id/iv_next_ic"
                app:layout_constraintBottom_toBottomOf="@id/iv_next_ic"
                app:layout_constraintHorizontal_chainStyle="packed"
                tools:ignore="SpUsage" />

            <com.dz.foundation.ui.widget.DzImageView
                android:id="@+id/iv_next_ic"
                android:layout_width="@dimen/common_dp18"
                android:layout_height="@dimen/common_dp18"
                android:src="@drawable/reader_ic_arrow_right"
                android:layout_marginBottom="@dimen/common_dp20"
                app:layout_constraintStart_toEndOf="@id/tv_next"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintHorizontal_chainStyle="packed"/>
        </com.dz.foundation.ui.widget.DzConstraintLayout>
    </merge>
</layout>