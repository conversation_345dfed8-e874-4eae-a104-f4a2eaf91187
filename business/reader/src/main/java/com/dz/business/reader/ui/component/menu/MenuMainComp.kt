package com.dz.business.reader.ui.component.menu

import android.animation.Animator
import android.app.Activity
import android.content.Context
import android.content.pm.ActivityInfo
import android.database.ContentObserver
import android.net.Uri
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.text.TextUtils
import android.util.AttributeSet
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.isVisible
import androidx.lifecycle.LifecycleOwner
import com.dz.business.base.helper.FloatWindowManage.Companion.READER_MENU
import com.dz.business.base.helper.FloatWindowManage.Companion.READ_SOURCE
import com.dz.business.base.reader.ReaderME
import com.dz.business.base.reader.ReaderMS
import com.dz.business.reader.R
import com.dz.business.reader.ReaderInsideEvents
import com.dz.business.reader.audio.TtsPlayer
import com.dz.business.reader.data.NoAdConfig
import com.dz.business.reader.databinding.ReaderMainMenuCompBinding
import com.dz.business.reader.repository.entity.NovelBookEntity
import com.dz.business.reader.repository.entity.NovelChapterEntity
import com.dz.business.reader.ui.page.AudioOpener
import com.dz.business.reader.ui.page.ReaderActivity
import com.dz.business.reader.utils.HwUtils
import com.dz.business.reader.utils.ReaderConfigUtil
import com.dz.business.reader.utils.ReaderSettingUtils
import com.dz.business.track.trace.OmapNode
import com.dz.business.track.trackProperties
import com.dz.foundation.base.utils.LogUtil
import com.dz.foundation.base.utils.ScreenUtil
import com.dz.foundation.ui.view.custom.ActionListener
import com.dz.foundation.ui.view.custom.ActionListenerOwner
import com.dz.foundation.ui.widget.getContainerActivity
import com.dz.platform.common.base.ui.component.UIConstraintComponent
import com.dz.platform.common.toast.ToastManager
import com.dz.platform.uplog.SensorLog
import com.gyf.immersionbar.ImmersionBar
import org.json.JSONObject
import reader.xo.config.AnimType

/**
 *@Author: shidz
 *@Date: 2022/9/29 15:26
 *@Description: 阅读器主菜单
 *@Version:1.0
 */
class MenuMainComp @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : UIConstraintComponent<ReaderMainMenuCompBinding, Any>(context, attrs, defStyleAttr),
    ActionListenerOwner<MenuMainComp.ViewActionListener> {

    override var mActionListener: ViewActionListener? = null

    /**
     * 听书是否可用
     */
    var ttsEnable = false
//        set(value) {
//            field = value
//            mViewBinding.ivTts.isVisible = value
//        }

    interface ViewActionListener : ActionListener, MenuBgComp.MenuBgCompActionListener,
        MenuTurnPageComp.ViewActionListener, MenuFontSizeComp.ViewActionListener,
        MenuSwitchChapterComp.ViewActionListener, MenuTitleCompActionListener {

        fun onTtsClick(): Boolean

    }

    private var navigationBarSize = 0
    private var navigationBarUri: Uri? = null

    private val mNavigationStatusObserver: ContentObserver =
        object : ContentObserver(
            Looper.myLooper()
                ?.let { Handler(it) }) {
            override fun onChange(selfChange: Boolean) {
                resetPadding()
            }
        }

    override fun initData() {
        navigationBarUri = HwUtils.getNavigationBarUri()
        navigationBarSize = HwUtils.getNavigationBarHeight(context)
    }

    override fun initView() {
        mViewBinding.compFontSize.bindData(MenuFontSizeComp.FontSizeBean(ReaderConfigUtil.getCurrentFontSize()))
        mViewBinding.compFontSize.setActionListener(object : MenuFontSizeComp.ViewActionListener {
            override fun setFontSize(fontSize: Int) {
                mActionListener?.setFontSize(fontSize)
            }
        })
        mViewBinding.compMenuBg.setActionListener(object : MenuBgComp.MenuBgCompActionListener {
            override fun onCheckColorStyle(colorStyleIndex: Int) {
                mActionListener?.onCheckColorStyle(colorStyleIndex)
            }

            override fun switchNightMode() {
                mActionListener?.switchNightMode()
            }
        })

        mViewBinding.compTurnPage.setActionListener(object : MenuTurnPageComp.ViewActionListener {
            override fun onCheckTurnPageStyle(animType: AnimType) {
                mActionListener?.onCheckTurnPageStyle(animType)
            }
        })

        mViewBinding.compSwitchChapter.setActionListener(object :
            MenuSwitchChapterComp.ViewActionListener {

            override fun onPreChapterClick() {
                onPreChapter()
            }

            override fun onNextChapterClick() {
                onNextChapter()
            }

            override fun onCatalogClick() {
                onCatalog()
            }
        })

        mViewBinding.compMenuTitle.setActionListener(object : MenuTitleCompActionListener {
            override fun onBackClick() {
                mActionListener?.onBackClick()
            }

            override fun addToShelf() {
                mActionListener?.addToShelf()
            }

            override fun batchOrder() {
                mActionListener?.batchOrder()
            }

            override fun doShare() {
                hide()
            }
        })

//        mViewBinding.compBottomAction.setActionListener(object :
//            MenuBottomActionComp.ViewActionListener {
//            override fun onFontSizeClick(selected: Boolean) {
//                //显示字号设置面板
//                if (mViewBinding.compFontSize.visibility == GONE) {
//                    showFontSizePanel()
//                } else {
//                    showSwitchChapterPanel()
//                }
//            }
//
//            override fun onBgClick(selected: Boolean) {
//                //显示背景设置面板
//                if (mViewBinding.compMenuBg.visibility == GONE) {
//                    showBgPanel()
//                } else {
//                    showSwitchChapterPanel()
//                }
//            }
//
//            override fun onCatalogClick() {
//                //显示目录
//                onCatalog()
//            }
//
//            override fun onTurnPageClick(selected: Boolean) {
//                //显示 翻页设置面板
//                if (mViewBinding.compTurnPage.visibility == GONE) {
//                    showTurnPagePanel()
//                } else {
//                    showSwitchChapterPanel()
//                }
//            }
//
//        })
//
//        mViewBinding.menuTtsFloatComp.setActionListener(object :
//            MenuTtsFloatComp.ViewActionListener {
//
//            override fun toggleTTS() {
//                TtsPlayer.instance.toggleTTS()
//            }
//
//            override fun onCloseClick() {
//                TtsPlayer.instance.exit()
//                hide()
//            }
//
//            override fun onBookCoverClick() {
//                //弹出TTS菜单
//                getContainerActivity()?.let {
//                    if (it is ReaderActivity) {
//                        hide()
//                        it.showTTSMenu()
//                    }
//                }
//            }
//
//        })
        onMode()


        trackConfig()


//        mViewBinding.ttsFloatComp.setActionListener(object :
//            MenuTtsFloatComp.ViewActionListener {
//
//            override fun toggleTTS() {
//                TtsPlayer.instance.toggleTTS()
//            }
//
//            override fun onCloseClick() {
//                TtsPlayer.instance.exit()
//                mViewBinding.ttsFloatComp.visibility = GONE
//                mViewBinding.ivTts.visibility = VISIBLE
//            }
//
//            override fun onBookCoverClick() {
//                startTts()
//            }
//
//        })

        setTtsFloatComp()
    }

    private fun trackConfig() {

        mViewBinding.ivTts.trackProperties(elementContent = "开始听书", ignoreAutoTrack = true)
        this.trackProperties(elementContent = "关闭菜单", ignoreAutoTrack = true)
    }

    override fun initListener() {
        registerClickAction { hide() }
        mViewBinding.apply {
            menuBottom.registerClickAction { hide() }
            menuBkg.registerClickAction { }
            ivTts.registerClickAction { startTts() }
        }
    }

    private fun onMode() {
        mActionListener?.switchNightMode()
        if (ReaderConfigUtil.isNightMode())
            setNightMode()
        else
            setDayMode()
    }

    private fun setDayMode() {
        mViewBinding.run {
            val menuBgColor =
                getColor(
                    when (ReaderConfigUtil.getCurrentColorStyleIndex()) {
                        0 -> R.color.reader_config_color_style_menu_0
                        1 -> R.color.reader_config_color_style_menu_1
                        2 -> R.color.reader_config_color_style_menu_2
                        3 -> R.color.reader_config_color_style_menu_3
                        else -> R.color.reader_config_color_style_menu_0
                    }
                )
            mViewBinding.compMenuTitle.setBackgroundColor(menuBgColor)
            menuBkg.setBackgroundColor(menuBgColor)
            leftPaddingView.setBackgroundColor(menuBgColor)
            rightPaddingView.setBackgroundColor(menuBgColor)
            bottomPaddingView.setBackgroundColor(menuBgColor)

            when (ReaderConfigUtil.getCurrentColorStyleIndex()) {
                0 -> ivTts.setImageResource(R.drawable.reader_ic_tts_button_yellow)
                1 -> ivTts.setImageResource(R.drawable.reader_ic_tts_button_purple)
                2 -> ivTts.setImageResource(R.drawable.reader_ic_tts_button_orange)
                3 -> ivTts.setImageResource(R.drawable.reader_ic_tts_button_blue)
                else -> ivTts.setImageResource(R.drawable.reader_ic_tts_button_gray)
            }


//            tvTips.setBackgroundResource(R.drawable.reader_menu_tips_bg)
        }
    }

    private fun setNightMode() {
        mViewBinding.run {
            val menuBgColor = getColor(R.color.reader_config_color_style_menu_night)
            mViewBinding.compMenuTitle.setBackgroundColor(menuBgColor)
            menuBkg.setBackgroundColor(menuBgColor)
            leftPaddingView.setBackgroundColor(menuBgColor)
            rightPaddingView.setBackgroundColor(menuBgColor)
            bottomPaddingView.setBackgroundColor(menuBgColor)
            ivTts.setImageResource(R.drawable.reader_ic_tts_button_gray)
//            tvTips.setBackgroundResource(R.drawable.reader_menu_tips_bg_night)
        }
    }

    /**
     * 开启听书
     */
    private fun startTts() {
        mActionListener?.onTtsClick()
    }

    private fun onNextChapter() {
        mActionListener?.onNextChapterClick()
        hide()
    }

    private fun onPreChapter() {
        mActionListener?.onPreChapterClick()
        hide()
    }

    /**
     * 去目录页面
     */
    private fun onCatalog() {
        ReaderMS.get()?.dismissAudioComp(READER_MENU)
//        hide()
        ReaderSettingUtils.applyDecorUi(
            this@MenuMainComp,
            ReaderSettingUtils.HIDE_BAR_FULL_SCREEN,
            false
        )
        mViewBinding.compMenuTitle.translationY =
            (-mViewBinding.compMenuTitle.measuredHeight).toFloat()
        mViewBinding.menuBottom.translationY = mViewBinding.menuBottom.measuredHeight.toFloat()
        visibility = INVISIBLE
        mActionListener?.onCatalogClick()
    }

    override fun subscribeEvent(lifecycleOwner: LifecycleOwner, lifecycleTag: String) {
        ReaderInsideEvents.get().colorStyleChanged().observe(lifecycleOwner, lifecycleTag) {
            if (ReaderConfigUtil.isNightMode())
                setNightMode()
            else
                setDayMode()
            if (context is ReaderActivity) {
                (context as ReaderActivity)
                    .getImmersionBar()
                    .transparentBar()
                    .navigationBarDarkIcon(!ReaderConfigUtil.isNightMode())
                    .statusBarDarkFont(!ReaderConfigUtil.isNightMode())
                    .init()
            }
        }

        // 播放状态发生变化
        ReaderME.get().ttsStatusChanged().observe(lifecycleOwner) {
            LogUtil.d("XXX", "ttsStatusChanged,status=$it")
            when (it) {
                TtsPlayer.STATUS_PLAYING,TtsPlayer.STATUS_PAUSE,TtsPlayer.STATUS_LOADING -> {
                    setTtsFloatComp()
                }


                TtsPlayer.STATUS_CLOSE -> {
                    ReaderMS.get()?.dismissAudioComp(READ_SOURCE)
                    setTtsFloatComp()
                }


            }
        }
    }

    private var bookInfo: NovelBookEntity? = null
    fun bindBookInfoData(bookInfo: NovelBookEntity) {
        this.bookInfo = bookInfo
        mViewBinding.compMenuTitle.bindData(bookInfo)
//        mViewBinding.ttsFloatComp.bindData(bookInfo.)

    }

    fun setTtsFloatComp() {
        LogUtil.d("XXX", "setTtsFloatComp ttsEnable=$ttsEnable ${TtsPlayer.instance.status}")
        if (!ttsEnable) {
            mViewBinding.ivTts.visibility = GONE
            return
        }
        if (TtsPlayer.instance.ttsConfig?.showListen != 1) {
            mViewBinding.ivTts.visibility = GONE
            return
        }
        if (TtsPlayer.instance.status == TtsPlayer.STATUS_CLOSE) {
            //还没有播放
            ReaderMS.get()?.dismissAudioComp(READ_SOURCE)
            mViewBinding.ivTts.visibility = VISIBLE
        } else if (!TextUtils.equals(AudioOpener.instance.getCurrentBookId(), bookInfo?.bid)) {
            //播放的不是同一本书
            mViewBinding.ivTts.visibility = VISIBLE
        } else {
            //同一本书播放中
            mViewBinding.ivTts.visibility = GONE
        }
    }


    private var isRegisterObserver = false

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        if (isRegisterObserver) {
            isRegisterObserver = false
            context.contentResolver.unregisterContentObserver(mNavigationStatusObserver)
        }
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        if (navigationBarUri != null) {
            isRegisterObserver = true
            context.contentResolver.registerContentObserver(
                navigationBarUri!!,
                true,
                mNavigationStatusObserver
            )
        }
    }

    fun show() {
        LogUtil.d("XXX", "菜单显隐：menu show, ttsEnable=$ttsEnable")
        ToastManager.dismissToast()
        ReaderMS.get()?.showAudioComp(READER_MENU)
        visibility = VISIBLE
        trackShow()
        ReaderSettingUtils.applyDecorUi(this, ReaderSettingUtils.SHOW_BAR_FULL_SCREEN, false)
        resetPadding()
        mViewBinding.compMenuTitle.translationY =
            (-mViewBinding.compMenuTitle.measuredHeight).toFloat()
        mViewBinding.compMenuTitle.animate().translationY(0F)
        mViewBinding.menuBottom.translationY = mViewBinding.menuBottom.measuredHeight.toFloat()
        mViewBinding.menuBottom.animate().translationY(0F).setListener(null)
        mViewBinding.menuBottom.bringToFront()

    }


    private fun trackShow() {

        // DzTrackEvents.get().popupShow().title(PopupShowTE.TITLE_MAIN_MENU).track()

        try {
            var jsonObject = JSONObject()
            jsonObject.put("\$screen_name", "阅读器菜单")
            jsonObject.put("\$title", "阅读器菜单")
            jsonObject.put("PositionName", "阅读器")

            getContainerActivity()?.let { activity ->
                if (activity is ReaderActivity) {
                    val currentDocInfo = activity.getCurrentDocInfo()
                    var bookName = currentDocInfo.bookName
                    var bookId = activity.getBookId()
                    val bookSource = activity.getBookSource()
                    if (!bookSource.isNullOrEmpty()) {
                        OmapNode.fromJson(bookSource)?.let {
                            jsonObject.put("Origin", it.origin)
                            jsonObject.put("ColumnName", it.columnName)

                            if (bookName.isNullOrEmpty()) {
                                bookName = it.contentName
                            }
                            if (bookId.isNullOrEmpty()) {
                                bookId = it.contentId
                            }
                        }
                    }


                    var chapterNum: Int? = null
                    var chapterId: String? = null
                    var chapterName: String? = null


                    activity.getCurrentXoFile()?.let { xoFile ->
                        if (xoFile.tag is NovelChapterEntity) {
                            val novelChapterEntity = xoFile.tag as NovelChapterEntity

                            chapterId = novelChapterEntity.cid

                            novelChapterEntity.chapter_num?.let { num ->
                                chapterNum = num + 1
                            }

                            chapterName = novelChapterEntity.chapter_name

                        }
                    }

                    jsonObject.put("ChaptersID", chapterId)
                    jsonObject.put("ChaptersNum", chapterNum)
                    jsonObject.put("ChaptersName", chapterName)
                    jsonObject.put("BookID", bookId)
                    jsonObject.put("BookName", bookName)
                }
            }

            SensorLog.trackViewScreen(this, jsonObject)
        } catch (e: Throwable) {

        }
    }

    fun hide(onHideBlock: (() -> Unit)? = null) {
        LogUtil.d("XXX", "菜单显隐：menu hide, ttsEnable=$ttsEnable")
        ReaderMS.get()?.dismissAudioComp(READER_MENU)
        ReaderSettingUtils.applyDecorUi(
            this@MenuMainComp,
            ReaderSettingUtils.HIDE_BAR_FULL_SCREEN,
            false
        )
        mViewBinding.compMenuTitle.translationY = 0F
        mViewBinding.compMenuTitle.animate()
            .translationY((-mViewBinding.compMenuTitle.measuredHeight).toFloat())
        mViewBinding.menuBottom.translationY = 0F
        mViewBinding.menuBottom.animate()
            .translationY(mViewBinding.menuBottom.measuredHeight.toFloat())
            .setListener(object : Animator.AnimatorListener {
                override fun onAnimationStart(animator: Animator) {}
                override fun onAnimationEnd(animator: Animator) {
                    visibility = INVISIBLE
                    onHideBlock?.invoke()
                }

                override fun onAnimationCancel(animator: Animator) {}
                override fun onAnimationRepeat(animator: Animator) {}
            })
    }

    /**
     * 重置Padding
     */
    private fun resetPadding() {
        val hasNavigationBar = HwUtils.hasNavigationBar(context)
        val isNavigationBarHide = HwUtils.isNavigationBarHide(context)
        val statusBarHeight = ImmersionBar.getStatusBarHeight((context as Activity))
        val navigationBarHeight = ScreenUtil.getNavigationHeight(context as Activity)
        var isInMultiWindow = false
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            isInMultiWindow = (context as Activity).isInMultiWindowMode
        }
        mViewBinding.run {
            when {
                isInMultiWindow -> {
                    leftPaddingView.layoutParams.width = 0
                    rightPaddingView.layoutParams.width = 0
                    bottomPaddingView.layoutParams.height = 0
                }

                (context as Activity).requestedOrientation == ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE -> {
                    bottomPaddingView.layoutParams.height = 0
                    leftPaddingView.layoutParams.width = getNotchSize()[1]
                    rightPaddingView.layoutParams.width =
                        if (hasNavigationBar && !isNavigationBarHide)
                            navigationBarSize
                        else
                            0
                }

                else -> {
                    if (Build.VERSION.SDK_INT < Build.VERSION_CODES.Q) {//低于安卓10
                        leftPaddingView.layoutParams.width = 0
                        rightPaddingView.layoutParams.width = 0
                        bottomPaddingView.layoutParams.height = navigationBarHeight
                        compMenuTitle.setPadding(0, statusBarHeight, 0, 0)
                    } else {
                        setOnApplyWindowInsetsListener { view, insets ->
                            WindowInsetsCompat.toWindowInsetsCompat(insets, view)
                                .getInsets(WindowInsetsCompat.Type.systemBars())
                                .apply {
                                    mViewBinding.run {
                                        leftPaddingView.layoutParams.width = 0
                                        rightPaddingView.layoutParams.width = 0
                                        bottomPaddingView.layoutParams.height = bottom
                                        compMenuTitle.setPadding(0, top, 0, 0)
                                    }
                                }
                            insets
                        }
                    }
                }
            }
        }
    }

    private var mNotchSize: IntArray? = null
    private fun getNotchSize(): IntArray {
        if (mNotchSize == null) {
            mNotchSize = HwUtils.getNotchSize()
        }
        if (mNotchSize == null) {
            mNotchSize = IntArray(2)
            mNotchSize!![0] = 0
            mNotchSize!![1] = 0
        }
        return mNotchSize as IntArray
    }


    private var lastBackPressMills = 0L

    /**
     * 处理物理返回按键
     *
     * @return true消费此事件，false不消费
     */
    fun onBackPress(): Boolean {

        //显示菜单时，计算两次点击的时间差
        val currentMills = System.currentTimeMillis()
        val diffValue = currentMills - lastBackPressMills
        if (diffValue < 10_000) {  // 10秒
            hide()
            lastBackPressMills = 0
            return false
        } else {
            ToastManager.showToast(context.getString(R.string.reader_click_double_exit))
            lastBackPressMills = currentMills
            return true
        }
    }

    fun bindNoAdConfig(removeAdVo: NoAdConfig?) {
        mViewBinding.compMenuTitle.bindNoAdConfig(removeAdVo)
    }

}