package com.dz.business.reader.ui.component.menu

import android.animation.Animator
import android.app.Activity
import android.content.Context
import android.content.pm.ActivityInfo
import android.database.ContentObserver
import android.net.Uri
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.util.AttributeSet
import android.view.View
import android.widget.SeekBar
import androidx.core.view.WindowInsetsCompat
import androidx.lifecycle.LifecycleOwner
import com.dz.business.base.reader.ReaderME
import com.dz.business.base.reader.ReaderMR
import com.dz.business.reader.R
import com.dz.business.reader.ReaderInsideEvents
import com.dz.business.reader.audio.TtsPlayer
import com.dz.business.reader.audio.presenter.TtsLoaderPresenter
import com.dz.business.reader.data.NoAdConfig
import com.dz.business.reader.data.TtsConfigDataVo
import com.dz.business.reader.databinding.ReaderTtsMainMenuCompBinding
import com.dz.business.reader.utils.HwUtils
import com.dz.business.reader.utils.MenuTtsConfig
import com.dz.business.reader.utils.ReaderConfigUtil
import com.dz.business.reader.utils.ReaderSettingUtils
import com.dz.business.reader.repository.entity.NovelBookEntity
import com.dz.business.track.events.DzTrackEvents
import com.dz.business.track.events.sensor.PopupShowTE
import com.dz.business.track.trackProperties
import com.dz.foundation.base.utils.LogUtil
import com.dz.foundation.base.utils.ScreenUtil
import com.dz.foundation.ui.view.custom.ActionListener
import com.dz.foundation.ui.view.custom.ActionListenerOwner
import com.dz.platform.common.base.ui.component.UIConstraintComponent
import com.dz.platform.common.toast.ToastManager
import com.gyf.immersionbar.ImmersionBar

/**
 * @Author: guyh
 * @Date: 2022/11/2 20:40
 * @Description: 听书TTS主菜单
 * @Version:1.0
 */
class MenuTtsMainComp @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : UIConstraintComponent<ReaderTtsMainMenuCompBinding, TtsConfigDataVo>(
    context,
    attrs,
    defStyleAttr
),
    ActionListenerOwner<MenuTtsMainComp.ViewActionListener> {

    interface ViewActionListener : ActionListener, MenuTitleCompActionListener {
        fun onCatalogClick()
//        fun onNextChapterClick()
//        fun onPreChapterClick()
    }

    override var mActionListener: ViewActionListener? = null

    private val sectionProgress = MenuSectionProgress.SectionProgressBean(0)
    private var navigationBarSize = 0
    private var navigationBarUri: Uri? = null
    private var playStatusText: String = ""

    /**
     * 进度条是否在拖动中
     */
    var progressDragging = false

    private val mNavigationStatusObserver: ContentObserver =
        object : ContentObserver(
            Looper.myLooper()
                ?.let { Handler(it) }) {
            override fun onChange(selfChange: Boolean) {
                resetPadding()
            }
        }

    override fun initData() {
        navigationBarUri = HwUtils.getNavigationBarUri()
        navigationBarSize = HwUtils.getNavigationBarHeight(context)
    }

    override fun initView() {
        mViewBinding.apply {
            compSectionProgress.bindData(
                MenuSectionProgress.SectionProgressBean(
                    TtsPlayer.instance.progressPresenter.progress
                )
            )
            compSectionProgress.setActionListener(object :
                MenuSectionProgress.ViewActionListener {
                override fun onProgressChanged(
                    seekBar: SeekBar?,
                    progress: Int,
                    fromUser: Boolean
                ) {
//                LogUtil.d("seekbar", "onProgressChanged, progress:$progress, fromUser:$fromUser")
                }

                override fun onStartTrackingTouch(seekBar: SeekBar?) {
//                LogUtil.d("seekbar", "onStartTrackingTouch")
                    progressDragging = true
//                mViewBinding.compSpeechRate.bindData(false)
                }

                override fun onStopTrackingTouch(seekBar: SeekBar?) {
//                LogUtil.d("seekbar", "onStopTrackingTouch")
                    progressDragging = false
//                mViewBinding.compSpeechRate.bindData(true)
                    seekBar?.apply {
                        LogUtil.d(TtsPlayer.TAG, "用户拖动语音播放的进度，progress：$progress")
                        TtsPlayer.instance.progressPresenter.seekToProgress(progress)
                    }
                }
            })

            // 顶部菜单的回调：返回、批量订购、加书架
            compMenuTitle.setActionListener(object : MenuTitleCompActionListener {
                override fun onBackClick() {
//                    hide()
                    mActionListener?.onBackClick()
                }

                override fun addToShelf() {
                    mActionListener?.addToShelf()
                }

                override fun batchOrder() {
                    mActionListener?.batchOrder()
                }

                override fun doShare() {
                    hide()
                }
            })
            layoutPreChapter.trackProperties(elementContent = "TTS上一章")
            mViewBinding.layoutPlay.trackProperties(elementContent = "TTS${playStatusText}")
            layoutNextChapter.trackProperties(elementContent = "TTS下一章")
            layoutCatalog.trackProperties(elementContent = "TTS目录")
            layoutTimer.trackProperties(elementContent = "TTS定时")
        }
    }

    override fun initListener() {
        registerClickAction {
            if (clickIntercept()) {
                return@registerClickAction
            }
            hide()
        }
        mViewBinding.apply {
            getClickEventHandler().addInterceptor{ view ->  // 对点击事件添加拦截器
                        view != this@MenuTtsMainComp &&
                        TtsPlayer.instance.isLoading()
            }

            layoutCatalog.registerClickAction {  // 目录
                hide()
                mActionListener?.onCatalogClick()
            }

            layoutTimer.registerTtsClickAction {  // 定时菜单
                ReaderMR.get().timerDialog().apply {
                    currentTime = TtsPlayer.instance.timerPresenter.timerFullLength
                }.start()
            }

            // 下一章
            layoutNextChapter.registerTtsClickAction {
                if (clickIntercept()) {
                    return@registerTtsClickAction
                }
                TtsPlayer.instance.chapterPresenter.loadNextChapter()
            }

            // 上一章
            layoutPreChapter.registerTtsClickAction {
                if (clickIntercept()) {
                    return@registerTtsClickAction
                }
                TtsPlayer.instance.chapterPresenter.loadPreviousChapter()
            }

            // 播放暂停按钮开关点击
            layoutPlay.registerTtsClickAction {
                TtsPlayer.instance.toggleTTS()
            }

            layoutTimbre.registerClickAction {
                if (!TtsPlayer.instance.isRunning()||TtsPlayer.instance.isLoading()) return@registerClickAction
                ReaderMR.get().timbreDialog().apply {

                }.start()
            }

            layoutSpeechRate.registerTtsClickAction {
                ReaderMR.get().speedDialog().apply {
                    currentSpeed = TtsPlayer.instance.speedPresenter.speed
                    changeSpeedBlock = { speed ->
                        speed?.let {
                            TtsPlayer.instance.speedPresenter.onSpeedChanged(speed)
                            ivSpeechRate.setImageResource(MenuTtsConfig.getIvSpeed(TtsPlayer.instance.speedPresenter.speed))
                        }
                    }
                }.start()
            }
        }
    }

    override fun subscribeEvent(lifecycleOwner: LifecycleOwner, lifecycleTag: String) {
        ReaderME.get().apply {
            ttsTimbreChanged().observe(lifecycleOwner) {  // 音色发生变化
                LogUtil.d(TtsPlayer.TAG, "音色变化：${it.name}")
                mViewBinding.tvTimbre.text = it?.name?: ""
            }

            ttsPlayingChanged().observe(lifecycleOwner) { isPlaying ->
                if (isPlaying) {
                    playStatusText = context.getString(R.string.reader_pause)
                    mViewBinding.ivSwitch.setImageResource(MenuTtsConfig.getIvSwitchPause())
                } else {
                    playStatusText = context.getString(R.string.reader_play)
                    mViewBinding.ivSwitch.setImageResource(MenuTtsConfig.getIvSwitchPlay())
                }
                mViewBinding.layoutPlay.trackProperties(elementContent = "TTS${playStatusText}")
            }

            // 播放状态发生变化
            ttsStatusChanged().observe(lifecycleOwner) {
                when (it) {
                    TtsPlayer.STATUS_PLAYING -> {
                        mViewBinding.compSectionProgress.setMaxCount(TtsPlayer.instance.chapterParagraphCount)
                    }
                    TtsPlayer.STATUS_CLOSE -> {
                        hide()
                    }
                }
            }

            // 播放进度发生变化
            ttsProgressChanged().observe(lifecycleOwner) {
                sectionProgress.sectionProgress = it
                mViewBinding.compSectionProgress.bindData(sectionProgress)
            }

            // 加载进度改变
            ttsLoadingChanged().observe(lifecycleOwner) {
                when(it) {
//                    TtsLoaderPresenter.LOAD_NEXT_CHAPTER, TtsLoaderPresenter.LOAD_PREVIOUS_CHAPTER -> {
//                        hide()
//                    }
                    TtsLoaderPresenter.LOAD_NOTHING, TtsLoaderPresenter.LOAD_OWN_AUDIO, TtsLoaderPresenter.LOAD_THIRD_AUDIO -> {
                        mViewBinding.ivSwitch.visibility = View.VISIBLE
                        mViewBinding.loading.visibility = View.GONE
                        mViewBinding.loading.cancelAnimation()
                    }
                    else -> {
                        mViewBinding.ivSwitch.visibility = View.INVISIBLE
                        mViewBinding.loading.visibility = View.VISIBLE
                        mViewBinding.loading.playAnimation()
                    }
                }
            }

            // 当开启倒计时时，剩余的时间
            ttsCloseLeftTime().observe(lifecycleOwner) {
                mViewBinding.tvTime.text = it
            }
        }
        ReaderInsideEvents.get().colorStyleChanged()
            .observe(lifecycleOwner, lifecycleTag) {  // 白天/夜间模式切换
                updateTheme()
            }
    }

    private fun updateTheme() {
        mViewBinding.apply {
            //标题颜色
            compMenuTitle.setBackgroundColor(MenuTtsConfig.getMenuBg(context))
            //主菜单背景色
            menuBottom.setBackgroundColor(MenuTtsConfig.getMenuBg(context))
            //上一张、暂停、下一章按钮
            ivPreChapter.setImageResource(MenuTtsConfig.getIvPreChapter())
            if (playStatusText == context.getString(R.string.reader_pause)) {
                ivSwitch.setImageResource(MenuTtsConfig.getIvSwitchPause())
            } else {
                ivSwitch.setImageResource(MenuTtsConfig.getIvSwitchPlay())
            }
            ivNextChapter.setImageResource(MenuTtsConfig.getIvNextChapter())
            loading.setBackgroundResource(MenuTtsConfig.getLoadingBkg())
            //倒计时
            ivTime.setImageResource(MenuTtsConfig.getIvTime())
            tvTime.setTextColor(getColor(MenuTtsConfig.getTextColor()))
            mViewBinding.tvTime.setTextColor(getColor(MenuTtsConfig.getTimeColor()))
            // 音色
          //  compTimbre.initView()
            //目录
            ivCatalog.setImageResource(MenuTtsConfig.getIvCatalog())
            tvCatalog.setTextColor(getColor(MenuTtsConfig.getTextColor()))
            //语速
            ivSpeechRate.setImageResource(MenuTtsConfig.getIvSpeed(TtsPlayer.instance.speedPresenter.speed))
            tvSpeechRate.setTextColor(getColor(MenuTtsConfig.getTextColor()))

            //切换音色
            ivTimbre.setImageResource(MenuTtsConfig.getIvTimbre())
            tvTimbre.setTextColor(getColor(MenuTtsConfig.getTextColor()))

            //进度条
            compSectionProgress.initView()
//            } else {
//                compMenuTitle.setBackgroundColor(getColor(MenuTtsConfig.dayModeBackGround))
//                menuBottom.setBackgroundColor(getColor(MenuTtsConfig.dayModeBackGround))
//                ivPreChapter.setImageResource(R.mipmap.reader_ic_arrow_left4)
//                ivSwitch.setImageResource(R.mipmap.reader_ic_play)
//                ivNextChapter.setImageResource(R.mipmap.reader_ic_arrow_right4)
//            }
            // 音速控制器
       //     compSpeechRate.updateTheme()
            //底部导航栏
            bottomPaddingView.setBackgroundColor(MenuTtsConfig.getMenuBg(context))
        }
    }

    fun bindBookInfoData(bookInfo: NovelBookEntity) {
        mViewBinding.compMenuTitle.bindData(bookInfo)
    }

    private var isRegisterObserver = false

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        if (isRegisterObserver) {
            isRegisterObserver = false
            context.contentResolver.unregisterContentObserver(mNavigationStatusObserver)
        }
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        if (navigationBarUri != null) {
            isRegisterObserver = true
            context.contentResolver.registerContentObserver(
                navigationBarUri!!,
                true,
                mNavigationStatusObserver
            )
        }
    }

    fun show() {
        updateTheme()
        ToastManager.dismissToast()
        visibility = VISIBLE
        trackShow()
        ReaderSettingUtils.applyDecorUi(this, ReaderSettingUtils.SHOW_BAR_FULL_SCREEN, !ReaderConfigUtil.isNightMode())
        resetPadding()
        mViewBinding.compMenuTitle.translationY =
            (-mViewBinding.compMenuTitle.measuredHeight).toFloat()
        mViewBinding.compMenuTitle.animate().translationY(0F)

        mViewBinding.menuBottom.translationY = mViewBinding.menuBottom.measuredHeight.toFloat()
        mViewBinding.menuBottom.animate().translationY(0F).setListener(null)

        mViewBinding.menuBottom.bringToFront()
        startViewScaleAnim(mViewBinding.ivTts, true)
    }

    private fun trackShow() {
        DzTrackEvents.get().popupShow().title(PopupShowTE.TITLE_TTS_MENU).track()
    }

    fun hide(onEnd: (()->Unit)? = null) {
        ReaderSettingUtils.applyDecorUi(
            this@MenuTtsMainComp,
            ReaderSettingUtils.HIDE_BAR_FULL_SCREEN,
            !ReaderConfigUtil.isNightMode()
        )

        mViewBinding.compMenuTitle.translationY = 0F
        mViewBinding.compMenuTitle.animate()
            .translationY((-mViewBinding.compMenuTitle.measuredHeight).toFloat())
        mViewBinding.menuBottom.translationY = 0F
        mViewBinding.menuBottom.animate()
            .translationY(mViewBinding.menuBottom.measuredHeight.toFloat())
            .setListener(object : Animator.AnimatorListener {
                override fun onAnimationStart(animator: Animator) {}
                override fun onAnimationEnd(animator: Animator) {
                    visibility = INVISIBLE

                    onEnd?.invoke()
                }

                override fun onAnimationCancel(animator: Animator) {}
                override fun onAnimationRepeat(animator: Animator) {}
            })
        startViewScaleAnim(mViewBinding.ivTts, false)
    }

    /**
     * 开始视图尺寸动画
     *
     * @param view
     * @param show
     */
    private fun startViewScaleAnim(view: View, show: Boolean) {
        if (show) {
            view.scaleX = 0f
            view.scaleY = 0f
            view.animate().scaleX(1f).scaleY(1f)
        } else {
            view.scaleX = 1f
            view.scaleY = 1f
            view.animate().scaleX(0f).scaleY(0f)
        }
    }

    /**
     * 重置Padding
     */
    private fun resetPadding() {
        val hasNavigationBar = HwUtils.hasNavigationBar(context)
        val isNavigationBarHide = HwUtils.isNavigationBarHide(context)
        val statusBarHeight = ImmersionBar.getStatusBarHeight((context as Activity))
        val navigationBarHeight = ScreenUtil.getNavigationHeight(context as Activity)
        var isInMultiWindow = false
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            isInMultiWindow = (context as Activity).isInMultiWindowMode
        }
        mViewBinding.run {
            when {
                isInMultiWindow -> {
                    leftPaddingView.layoutParams.width = 0
                    rightPaddingView.layoutParams.width = 0
                    bottomPaddingView.layoutParams.height = 0
                }
                (context as Activity).requestedOrientation == ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE -> {
                    bottomPaddingView.layoutParams.height = 0
                    leftPaddingView.layoutParams.width = getNotchSize()[1]
                    rightPaddingView.layoutParams.width =
                        if (hasNavigationBar && !isNavigationBarHide)
                            navigationBarSize
                        else
                            0
                }
                else -> {
                    if (Build.VERSION.SDK_INT < Build.VERSION_CODES.Q) {//低于安卓10
                        leftPaddingView.layoutParams.width = 0
                        rightPaddingView.layoutParams.width = 0
                        bottomPaddingView.layoutParams.height = navigationBarHeight
                        compMenuTitle.setPadding(0, statusBarHeight, 0, 0)
                    } else {
                        setOnApplyWindowInsetsListener { view, insets ->
                            WindowInsetsCompat.toWindowInsetsCompat(insets, view)
                                .getInsets(WindowInsetsCompat.Type.systemBars())
                                .apply {
                                    leftPaddingView.layoutParams.width = 0
                                    rightPaddingView.layoutParams.width = 0
                                    bottomPaddingView.layoutParams.height = bottom
                                    compMenuTitle.setPadding(0, top, 0, 0)
                                }
                            insets
                        }
                    }
                }
            }
        }
    }

    private var mNotchSize: IntArray? = null
    private fun getNotchSize(): IntArray {
        if (mNotchSize == null) {
            mNotchSize = HwUtils.getNotchSize()
        }
        if (mNotchSize == null) {
            mNotchSize = IntArray(2)
            mNotchSize!![0] = 0
            mNotchSize!![1] = 0
        }
        return mNotchSize as IntArray
    }

    private var lastBackPressMills = 0L

    /**
     * 处理物理返回按键
     *
     * @return true消费此事件，false不消费
     */
    fun onBackPress(): Boolean {
        if (TtsPlayer.instance.isRunning()) {
            val currentMills = System.currentTimeMillis()
            val diffValue = currentMills - lastBackPressMills
            lastBackPressMills = if (diffValue < 10_000) {  // 10秒
                TtsPlayer.instance.exit()
                0
            } else {
                ToastManager.showToast(context.getString(R.string.reader_click_double_exit))
                currentMills
            }
            return true
        }
        return false
    }

    /**
     * 点击事件的拦截处理
     *
     * @return true进行拦截，false不拦截
     */
    fun clickIntercept(): Boolean {
        return progressDragging
    }

    private fun <T : View> T.registerTtsClickAction(clickAction: (view: View) -> Unit) {
        registerClickAction {
            if (!TtsPlayer.instance.isRunning()) return@registerClickAction
            clickAction.invoke(this)
        }
    }

    fun bindNoAdConfig(removeAdVo: NoAdConfig?) {
        mViewBinding.compMenuTitle.bindNoAdConfig(removeAdVo)
    }
}