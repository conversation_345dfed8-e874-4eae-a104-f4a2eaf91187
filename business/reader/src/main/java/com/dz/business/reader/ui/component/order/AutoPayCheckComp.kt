package com.dz.business.reader.ui.component.order

import android.content.Context
import android.text.TextUtils
import android.util.AttributeSet
import com.dz.business.base.reader.ReaderMR
import com.dz.business.reader.R
import com.dz.business.reader.databinding.ReaderAutoPayCheckCompBinding
import com.dz.business.reader.utils.ReaderConfigUtil
import com.dz.platform.common.base.ui.component.UIConstraintComponent

/**
 *@Author: shidz
 *@Date: 2022/11/10 16:51
 *@Description: 自动订购勾选 组件
 *@Version:1.0
 */
class AutoPayCheckComp @JvmOverloads constructor(
        context: Context,
        attrs: AttributeSet? = null,
        defStyleAttr: Int = 0
) : UIConstraintComponent<ReaderAutoPayCheckCompBinding, Boolean>(
        context,
        attrs,
        defStyleAttr
) {

    //是否坚持深色主题（这个控件有点特殊，有的页面需要坚持有的页面不需要支持）
    private var mIsSupportDark = false
    private var isClickable: Boolean? = true
    private var isValidChecked: Boolean = true

    override fun initAttrs(context: Context?, attrs: AttributeSet?, defStyleAtt: Int) {
        val array = context?.obtainStyledAttributes(attrs, R.styleable.readerAutoPayCheck, 0, 0)
        array?.run {
            mIsSupportDark = getBoolean(R.styleable.readerAutoPayCheck_isSupportDark, false)
            recycle()
        }
    }

    override fun initData() {
    }

    override fun initView() {
    }

    override fun initListener() {
        mViewBinding.llCheck.registerClickAction { doCheck() }
    }

    private fun doCheck() {
        if (isClickable == true) {
            mViewBinding.cbAutoPay.changeChecked(!mViewBinding.cbAutoPay.checked)
            isValidChecked = mViewBinding.cbAutoPay.checked
        }
    }

    fun setAgreementClickable(isClickable: Boolean) {
        this.isClickable = isClickable
        mViewBinding.cbAutoPay.visibility = if (isClickable) VISIBLE else GONE
        mViewBinding.tvTitle.text = getTitle(isClickable, bookType)

    }

    private var bookType: String = "1" //1文本书架 2 音频书籍
    fun setBookType(bookType: String?) {
        bookType?.let {
            this.bookType = it
        }

    }

    private fun getTitle(isClickable: Boolean, bookType: String): String {
        return if (isClickable) {
            if (bookType == "1") {
//                context.getString(R.string.reader_auto_pay_next_chapter)
                "自动购买下一章"
            } else {
//                context.getString(R.string.order_auto_pay_next_chapter)
                "自动购买"
            }
        } else {
//            context.getString(
//                R.string.reader_auto_pay_next_all_free
//            )
            "购买后全部章节免费看"
        }
    }

    override fun bindData(data: Boolean?) {
        super.bindData(data)
        mViewBinding.cbAutoPay.checked = (data == true && isClickable == true)
        isValidChecked = mViewBinding.cbAutoPay.checked
        mViewBinding.tvTitle.text = getTitle(isClickable == true, bookType)
        setNightMode(ReaderConfigUtil.isNightMode())
    }

    fun isAutoPayChecked(): Boolean {
        return mViewBinding.cbAutoPay.checked
    }

    fun setNightMode(nightMode: Boolean) {
        if (mIsSupportDark) {
            mViewBinding.tvTitle.setTextColor(getColor(R.color.reader_E6000000_DBFFFFFF))
            return
        }
        if (nightMode ) {
            mViewBinding.tvTitle.setTextColor(getColor(R.color.reader_DBFFFFFF))
        } else {
            mViewBinding.tvTitle.setTextColor(getColor(R.color.reader_E6000000))
        }
        setDark(nightMode)
    }

    private var containerType = ""
    fun setContainerType(containerType: String) {
        this.containerType = containerType
    }

    fun setDark(isDark:Boolean){
        mViewBinding.cbAutoPay.setDartTheme(isDark)
    }

}