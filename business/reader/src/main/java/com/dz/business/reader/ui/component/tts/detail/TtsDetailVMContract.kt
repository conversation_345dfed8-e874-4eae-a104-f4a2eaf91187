package com.dz.business.reader.ui.component.tts.detail

import androidx.lifecycle.LiveData
import com.dz.business.base.reader.data.MenuInfoVO
import com.dz.business.base.reader.data.NovelTimbreVo
import com.dz.business.base.theatre.data.BookInfoVo
import com.dz.business.reader.data.NovelSpeedVo
import com.dz.business.reader.repository.entity.NovelChapterEntity

interface TtsDetailVMContract {


    //推荐列表
    val recommendNovelList: LiveData<List<BookInfoVo>?>

    //推荐结果状态
    val recommendStatus: LiveData<Int>

    //"是否在书架上：0-否；1-是"
    val isShelf: LiveData<Int>

    //页面渲染需要的数据
    val novelChapterPageData: LiveData<NovelChapterPageData>

    //章节数据
    val chapterData: LiveData<NovelChapterEntity>


    /**
     * 点击定时按钮
     */
    fun onClockMenuClick()

    /**
     * 点击音色按钮
     */
    fun onVoiceMenuClick()

    /**
     * 点击语速按钮
     */
    fun onSpeedMenuClick()

    /**
     * 点击目录按钮
     */
    fun onChapterListMenuClick()

    /**
     * 加入书架
     */
    fun onShelfMenuClick()



    /**
     * 章节内容变化
     */
    fun onChapterChanged(novelInfo: NovelChapterEntity?)

    /**
     * 拖动进度条
     */
    fun onSeekBarProgressChange(progress: Int, uiId: String)

    /**
     * 重新加载推荐数据
     */
    fun reloadRecommendData(bookId: String)


    fun getSelectedVoice():NovelTimbreVo?

    fun getSelectedSpeed(): NovelSpeedVo?
}