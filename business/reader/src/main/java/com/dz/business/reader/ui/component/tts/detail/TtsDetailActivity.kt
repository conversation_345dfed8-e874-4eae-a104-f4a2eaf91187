package com.dz.business.reader.ui.component.tts.detail

import android.graphics.drawable.GradientDrawable
import android.view.View
import android.view.View.GONE
import android.view.View.INVISIBLE
import android.view.View.VISIBLE
import android.widget.SeekBar
import androidx.annotation.ColorInt
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.core.graphics.ColorUtils
import androidx.lifecycle.LifecycleOwner
import androidx.palette.graphics.Palette
import com.dz.business.base.helper.FloatWindowManage.Companion.TTS_TYPE
import com.dz.business.base.reader.ReaderME
import com.dz.business.base.reader.ReaderMR
import com.dz.business.base.reader.intent.ReaderIntent
import com.dz.business.base.theatre.data.BookInfoVo
import com.dz.business.base.ui.BaseActivity
import com.dz.business.base.ui.BaseVisibilityFragment
import com.dz.business.base.ui.component.status.Status
import com.dz.business.base.utils.ImageUtil
import com.dz.business.reader.R
import com.dz.business.reader.audio.AudioListener
import com.dz.business.reader.audio.TtsPlayer
import com.dz.business.reader.audio.presenter.TtsLoaderPresenter
import com.dz.business.reader.databinding.ReaderTtsDetailActivityBinding
import com.dz.business.reader.load.LoadResult
import com.dz.business.reader.repository.entity.AudioChapterEntity
import com.dz.business.reader.repository.entity.NovelChapterEntity
import com.dz.business.reader.ui.component.emptyDataComp.EmptyDataComp
import com.dz.business.reader.ui.component.menu.MenuSectionProgress
import com.dz.business.reader.ui.component.tts.recommend.TtsRecommendItemCardComp
import com.dz.business.reader.ui.page.AudioOpener
import com.dz.business.reader.ui.page.ReaderActivity
import com.dz.business.track.trace.OmapNode
import com.dz.foundation.base.manager.task.TaskManager
import com.dz.foundation.base.module.AppModule
import com.dz.foundation.base.utils.LocalActivityMgr
import com.dz.foundation.base.utils.LogUtil
import com.dz.foundation.base.utils.ScreenUtil
import com.dz.foundation.base.utils.dp
import com.dz.foundation.imageloader.loadRoundedCornersImage
import com.dz.foundation.ui.view.recycler.DzRecyclerViewCell
import com.dz.platform.common.toast.ToastManager
import com.gyf.immersionbar.BarHide
import com.gyf.immersionbar.ImmersionBar
import com.sensorsdata.analytics.android.sdk.ScreenAutoTracker
import reader.xo.base.TextSection

/**
 * 听书详情页
 */
class TtsDetailActivity : BaseActivity<ReaderTtsDetailActivityBinding, TtsDetailVM>(),
    ScreenAutoTracker {
    companion object {
        const val TAG: String = "TtsDetailActivity"
        private val DEFAULT_COLOR = R.color.common_FF3C3C3C
    }

    private var immersionBar: ImmersionBar? = null

    private var contract: TtsDetailVMContract? = null
    private val sectionProgress = MenuSectionProgress.SectionProgressBean(0)

    private var isSelectedVoice = false

    private val audioOpener = AudioOpener.instance
    private var readerVM = audioOpener.getReaderVM()


    /**
     * 进度条是否在拖动中
     */
    var progressDragging = false
    private var mAudioListener: AudioListener? = null

    private fun getAudioObserver(): AudioListener {
        return mAudioListener ?: object : AudioListener {
            override fun onPlayStatusChanged(status: Int) {

            }

            override fun onChapterOpen(
                bookId: String, currentChapterEntity: NovelChapterEntity?,
                audioChapterEntity: AudioChapterEntity?,
            ) {
                mViewBinding.tvBookTitle.text =
                    currentChapterEntity?.chapter_name ?: audioChapterEntity?.chapter_name
//                setTextSection(null)
//                updateActionBtnStatus()
//                mViewBinding.compMenuTitle.setChapterInfo(currentChapterEntity)
            }

            override fun onTextSectionSelected(
                bookId: String,
                chapterId: String,
                section: TextSection?
            ) {
//                setTextSection(section)
            }

            override fun onStartTtsMode(bookId: String, fid: String) {

            }

            override fun onStopTtsMode(bookId: String) {

            }

            override fun onCheckTextSectionInCurrentPage(
                bookId: String,
                currentTextSection: TextSection
            ) {

            }

            override fun onSeekToParagraph(bookId: String, index: Int) {

            }

            override fun onParagraphProgress(position: Long, duration: Long, section: TextSection) {
//                mViewBinding.tvContent.highlightLineAt(position, duration / section.text.length)
            }
        }.apply {
            mAudioListener = this
        }
    }


    override fun initData() {
        contract = mViewModel
        audioOpener.readyTTS(mViewModel.routeIntent?.audioType)

        //进入页面立即播放
        LogUtil.d(
            TAG,
            "TtsPlayer.instance.isRunning()=${TtsPlayer.instance.status}  mViewModel?.routeIntent?.audioType=${mViewModel?.routeIntent?.audioType}"
        )
        if (!TtsPlayer.instance.isRunning()) {
            playAudio()
        }

        mViewBinding.progressBar.setMaxCount(TtsPlayer.instance.chapterParagraphCount)

        setBgShadow(mViewBinding.viewRootMask, null)
    }


    override fun onStart() {
        super.onStart()
        AudioOpener.instance.dismissFloatWindow()
    }

    override fun onStop() {
        super.onStop()
        if (LocalActivityMgr.getTopActivity() !is ReaderActivity) {
            AudioOpener.instance.showFloatWindow()
        }
    }


    override fun recycleRes() {
        super.recycleRes()
        AudioOpener.instance.unregisterAudioListener(getAudioObserver())

//        audioOpener.unbindAudioActivity()
    }


    override fun initImmersionBar() {
        super.initImmersionBar()
        // 设置完全透明的状态栏，让背景渐变延伸到状态栏区域
        getImmersionBar()
            .transparentStatusBar()
            .navigationBarColor(R.color.common_card_FFFFFFFF)
            .statusBarDarkFont(false)  // 状态栏文字使用白色
            .navigationBarDarkIcon(false)
            .hideBar(BarHide.FLAG_SHOW_BAR)
            .fitsSystemWindows(false)  // 允许内容延伸到系统窗口区域
            .init()
    }

    /**
     * 设置状态栏高度，防止内容被状态栏遮挡
     */
    private fun setupStatusBarHeight() {
        // 获取状态栏高度
        val statusBarHeight = ScreenUtil.getStatusHeight(this)

        val closeLayoutParams = mViewBinding.tvTitle.layoutParams as ConstraintLayout.LayoutParams
        closeLayoutParams.topMargin += statusBarHeight
        mViewBinding.tvTitle.layoutParams = closeLayoutParams
    }


    /**
     * 点击事件的拦截处理
     *
     * @return true进行拦截，false不拦截
     */
    private fun clickIntercept(): Boolean {
        return progressDragging
    }


    override fun initView() {
        // 设置状态栏高度，让内容不被状态栏遮挡
        setupStatusBarHeight()

        TaskManager.delayTask(300) {
            readerVM.updateBookShelfIndex()
        }

        mViewBinding.ivPlay.setImageResource(if (TtsPlayer.instance.isPlaying()) R.drawable.reader_icon_tts_pause else R.drawable.reader_ic_tts_play)


        mViewBinding.progressBar.initView()
        mViewBinding.progressBar.bindData(
            MenuSectionProgress.SectionProgressBean(
                TtsPlayer.instance.progressPresenter.progress
            )
        )
        mViewBinding.progressBar.setActionListener(object : MenuSectionProgress.ViewActionListener {
            override fun onProgressChanged(
                seekBar: SeekBar?, progress: Int, fromUser: Boolean
            ) {
                LogUtil.d("seekbar", "onProgressChanged, progress:$progress, fromUser:$fromUser")
            }

            override fun onStartTrackingTouch(seekBar: SeekBar?) {
                LogUtil.d("seekbar", "onStartTrackingTouch")
                progressDragging = true
            }

            override fun onStopTrackingTouch(seekBar: SeekBar?) {
                LogUtil.d("seekbar", "onStopTrackingTouch")
                progressDragging = false
                seekBar?.apply {
                    LogUtil.d(TtsPlayer.TAG, "用户拖动语音播放的进度，progress：$progress")
                    TtsPlayer.instance.progressPresenter.seekToProgress(progress)
                }
            }
        })

        mViewBinding.emptyDataLayout.registerCompListener(object : EmptyDataComp.ViewListener {
            override fun onRefreshClick() {
                mViewModel.routeIntent?.bookId?.let {
                    contract?.reloadRecommendData(it)
                }
            }

        })

        contract?.getSelectedSpeed()?.let {
            mViewBinding.tvSpeed.text = if (it.speed == 1f) "语速" else it.speedName
        }

        contract?.getSelectedVoice()?.let {
            mViewBinding.tvVoice.text = it.desc
        }
    }

    override fun initListener() {
        mViewBinding.layoutClock.registerClickAction {
            contract?.onClockMenuClick()
        }

        mViewBinding.layoutVoiceType.registerClickAction {
            isSelectedVoice = true
            contract?.onVoiceMenuClick()
        }

        mViewBinding.layoutVoiceSpeed.registerClickAction {
            contract?.onSpeedMenuClick()
        }
        mViewBinding.layoutChapterList.registerClickAction {
            contract?.onChapterListMenuClick()
        }

        mViewBinding.ivPlay.registerClickAction {
            if (!TtsPlayer.instance.isRunning()) {
                playAudio()
            } else {
                audioOpener.toggleTTS(mViewModel.routeIntent?.bookSource)
            }

        }
        mViewBinding.ivClose.registerClickAction {
            finish()
        }

        // 下一章
        mViewBinding.ivNext.registerClickAction {
            if (clickIntercept()) {
                return@registerClickAction
            }
            if (AudioOpener.instance.getCurrentChapterInfo()?.next_cid.isNullOrEmpty()) {
                ToastManager.showToast(
                    AppModule.getResources().getString(R.string.reader_is_last_chapter)
                )
            } else {
                TtsPlayer.instance.chapterPresenter.loadNextChapter()
            }
        }

        // 上一章
        mViewBinding.ivPrevious.registerClickAction {
            if (clickIntercept()) {
                return@registerClickAction
            }
            if (AudioOpener.instance.getCurrentChapterInfo()?.pre_cid.isNullOrEmpty()) {
                ToastManager.showToast(
                    AppModule.getResources().getString(R.string.reader_is_first_chapter)
                )
            } else {
                TtsPlayer.instance.chapterPresenter.loadPreviousChapter()
            }
        }

        mViewBinding.viewRead.registerClickAction {
            ReaderMR.get().reader().apply {
                bookId = mViewModel.routeIntent?.bookId!!
                chapterId =  mViewModel.routeIntent?.bookId
                routeSource = mViewModel.routeIntent?.routeSource
                referrer = mViewModel.routeIntent?.referrer
            }.start()
        }

        mViewBinding.layoutFavorite.registerClickAction {
            contract?.onShelfMenuClick()
        }
        AudioOpener.instance.registerAudioListener(getAudioObserver())
    }

    override fun subscribeEvent(lifecycleOwner: LifecycleOwner, lifecycleTag: String) {
        super.subscribeEvent(lifecycleOwner, lifecycleTag)
        ReaderME.get().goToPreChapter().observeForever(getUiId()) {
            LogUtil.d("XXX", "ReaderActivity goToPreChapter observeForever")
            sectionProgress.sectionProgress = 0
            mViewBinding.progressBar.bindData(sectionProgress)
        }
        ReaderME.get().goToNextChapter().observeForever(getUiId()) {
            LogUtil.d("XXX", "ReaderActivity goToNextChapter observeForever")
            sectionProgress.sectionProgress = 0
            mViewBinding.progressBar.bindData(sectionProgress)
        }

        // 加载进度改变
        ReaderME.get().ttsLoadingChanged().observe(lifecycleOwner, lifecycleTag) {
            LogUtil.d("XXX", "ttsLoadingChanged,status=${it}")
            when (it) {
                TtsLoaderPresenter.LOAD_NOTHING, TtsLoaderPresenter.LOAD_OWN_AUDIO, TtsLoaderPresenter.LOAD_THIRD_AUDIO -> {
                    mViewBinding.ivPlay.visibility = View.VISIBLE
                    mViewBinding.loading.visibility = View.GONE
                    mViewBinding.loading.cancelAnimation()
                }

                TtsLoaderPresenter.LOAD_BUFFER_START -> {
                    LogUtil.d(
                        TAG,
                        "onChapterOpen,chapterParagraphCount=${TtsPlayer.instance.chapterParagraphCount}"
                    )
                    mViewBinding.progressBar.setMaxCount(TtsPlayer.instance.chapterParagraphCount)
                }


                else -> {
                    mViewBinding.ivPlay.visibility = View.INVISIBLE
                    mViewBinding.loading.visibility = View.VISIBLE
                    mViewBinding.loading.playAnimation()
                }
            }
        }

        // 播放进度发生变化
        ReaderME.get().ttsProgressChanged().observe(lifecycleOwner, lifecycleTag) {
            LogUtil.d(TAG, "ttsProgressChanged,status=${it}")
            if (it > 0) {
                sectionProgress.sectionProgress = it
                mViewBinding.progressBar.bindData(sectionProgress)
            }
        }

        //  TTS听书播放状态变化
        ReaderME.get().ttsStatusChanged().observe(lifecycleOwner, lifecycleTag) {
            LogUtil.d(TAG, "ttsStatusChanged newStatus=${it}")
            when (it) {
                TtsPlayer.STATUS_PLAYING -> {
                    mViewBinding.ivPlay.setImageResource(R.drawable.reader_icon_tts_pause)
                }

                TtsPlayer.STATUS_PAUSE -> {
                    mViewBinding.ivPlay.setImageResource(R.drawable.reader_ic_tts_play)
                }

                TtsPlayer.STATUS_CLOSE, TtsPlayer.STATUS_STOP -> {
                    mViewBinding.loading.cancelAnimation()
                    mViewBinding.loading.visibility = GONE
                    mViewBinding.ivPlay.visibility = VISIBLE
                    mViewBinding.ivPlay.setImageResource(R.drawable.reader_ic_tts_play)
                }

                TtsPlayer.STATUS_LOADING -> {
                    mViewBinding.ivPlay.visibility = INVISIBLE
                    mViewBinding.loading.visibility = VISIBLE
                    mViewBinding.loading.playAnimation()
                }
            }
        }

        //章节加载完毕
        ReaderME.get().onChapterLoadFinish().observe(lifecycleOwner, lifecycleTag) { loadResult ->
            if (loadResult is LoadResult) {
                LogUtil.d(TAG, "onChapterLoadFinish result=${loadResult.getResultCode()}")
                if (loadResult.isSuccess()) {
                    contract?.onChapterChanged(loadResult.getResult()?.getChapterInfo())
                } else {
                    mViewBinding.loading.visibility = GONE
                    mViewBinding.loading.cancelAnimation()
                    mViewBinding.ivPlay.visibility = VISIBLE
                    mViewBinding.ivPlay.setImageResource(R.drawable.reader_ic_tts_play)

                    ToastManager.showToast(loadResult.getMsg())

                    // 如果TTS正在进行，那么退出TTS
                    if (TtsPlayer.instance.isRunning()) {
                        TtsPlayer.instance.chapterPresenter.handleChapterLoadFailed(
                            loadResult.getResultCode(),
                            loadResult.getResult()?.getResultChapterName() ?: "",
                            loadResult.getResult()?.getResultChapterId() ?: ""
                        )
                    }
                }
            }
        }

        // 当开启倒计时时，剩余的时间
        ReaderME.get().ttsCloseLeftTime().observe(lifecycleOwner, lifecycleTag) {
            LogUtil.d("XXX","tts ttsCloseLeftTime ,time=$it")
            mViewBinding.tvClockLeftTime.text = it
        }

        // 音色发生变化
        ReaderME.get().ttsTimbreChanged().observe(lifecycleOwner, lifecycleTag) {
            LogUtil.d(TtsPlayer.TAG, "音色变化：${it.name}")
            mViewBinding.tvVoice.text = it?.desc
            if (isSelectedVoice) {
                ToastManager.showToast("已切换为${it?.desc}")
                isSelectedVoice = false
            }
        }

        //倍速发生变化
        ReaderME.get().ttsSpeedChanged().observe(lifecycleOwner, lifecycleTag) {
            TtsPlayer.instance.ttsConfig?.speedList?.find { item -> item.speed == it }
                ?.let { target ->
                    mViewBinding.tvSpeed.text = if (target.speed == 1f) "语速" else target.speedName
                    ToastManager.showToast("已切换至${target.speedName}倍速")
                }
        }


        // tts详情页选择完章节，重新加载章节信息
        ReaderME.get().onChapterChange().observe(lifecycleOwner, lifecycleTag) { chapterId ->
            LogUtil.d(BaseVisibilityFragment.TAG, "听书页选择章节-->chapterId=${chapterId}")
            readerVM.routeIntent?.chapterId = chapterId
            readerVM.initChapterContent(true)
        }


        ReaderME.get().onAudioBookPlayEnd().observe(lifecycleOwner, lifecycleTag) {
            finish()
        }
    }


    private fun setBgShadow(view: View, palette: Palette?) {
        val domainColor = getColorFromPalette(view, palette)
        // 创建颜色数组，用于定义渐变色
        val colors = intArrayOf(
            (domainColor and 0x00ffffff) or 0xD9000000.toInt(), // 顶部颜色，透明度设置为85%
            domainColor,
            domainColor,
        )
        val gradientDrawable =
            GradientDrawable(GradientDrawable.Orientation.TOP_BOTTOM, colors).apply {
                shape = GradientDrawable.RECTANGLE

                gradientType = GradientDrawable.LINEAR_GRADIENT
            }
        // 将创建的GradientDrawable设置为View的背景
        view.background = gradientDrawable
    }


    private fun playAudio() {
        val readerIntent: ReaderIntent = ReaderIntent().apply {
            bookId = mViewModel.routeIntent?.bookId ?: ""
            chapterId = mViewModel.routeIntent?.chapterId ?: ""
            routeSource = mViewModel.routeIntent?.bookSource ?: ""
            audioType = mViewModel.routeIntent?.audioType ?: TTS_TYPE
//            currentPos = getXoReader().getCurrentDocInfo().charIndex
//                bookCover = mViewModel.novelBookEntityLd.value?.coverurl
        }
        audioOpener.onNewIntent(readerIntent)
    }

    private fun setMaskShadow(view: View, palette: Palette?) {
        val domainColor = getColorFromPalette(view, palette)
        // 创建颜色数组，用于定义渐变色
        val colors = intArrayOf(
            domainColor and 0x00ffffff, // 顶部颜色，透明度设置为0
            domainColor
        )

        val cornerRadius = ScreenUtil.dip2px(this, 12).toFloat()
        val gradientDrawable =
            GradientDrawable(GradientDrawable.Orientation.TOP_BOTTOM, colors).apply {
                shape = GradientDrawable.RECTANGLE
                cornerRadii = floatArrayOf(
                    0f,
                    0f,
                    0f,
                    0f,
                    cornerRadius,
                    cornerRadius,
                    cornerRadius,
                    cornerRadius
                )
                gradientType = GradientDrawable.LINEAR_GRADIENT
            }
        // 将创建的GradientDrawable设置为View的背景
        view.background = gradientDrawable
    }


    /**
     * 从 Palette 中获取颜色
     */
    @ColorInt
    private fun getColorFromPalette(view: View, palette: Palette?): Int {
//        LogUtil.d("ImageUtil", "${mData?.bookName} " +
//                "dominantSwatch:{${String.format("#%06X", 0xFFFFFF and (palette?.dominantSwatch?.rgb ?: Color.WHITE))} ${palette?.dominantSwatch?.hsl?.contentToString()}} " +
//                "darkVibrantSwatch:{${String.format("#%06X", 0xFFFFFF and (palette?.darkVibrantSwatch?.rgb ?: Color.WHITE))} ${palette?.darkVibrantSwatch?.hsl?.contentToString()}}")
        return palette?.dominantSwatch?.hsl?.let {
            it[2] = 0.3F  // 统一亮度
            ColorUtils.HSLToColor(it)
        } ?: ContextCompat.getColor(view.context, DEFAULT_COLOR)
    }


    override fun subscribeObserver(lifecycleOwner: LifecycleOwner) {
        super.subscribeObserver(lifecycleOwner)
        contract?.novelChapterPageData?.observe(lifecycleOwner) { pageData ->
            LogUtil.d(
                "打印",
                "书籍信息：：contract?.novelChapterPageData:pageData?.bookName=${pageData?.bookName} ${pageData.isShelf}"
            )
            //封面
            mViewBinding.ivCover.loadRoundedCornersImage(
                img = pageData.bookCover,
                radius = 12f.dp,
                placeholder = R.drawable.bbase_ic_recommend_fillet_default,
                error = R.drawable.bbase_ic_recommend_fillet_default,
                width = 192,
                height = 252
            )

            mViewBinding.tvBookTitle.text = pageData?.chapterName
            mViewBinding.tvBookDesc.text = pageData?.bookName

            if (pageData.isShelf) {
                mViewBinding.ivShelf.setImageResource(R.drawable.reader_ic_tts_shelf)
                mViewBinding.tvShelf.text = "已加书架"
            } else {
                mViewBinding.ivShelf.setImageResource(R.drawable.reader_ic_tts_not_shelf)
                mViewBinding.tvShelf.text = "加入书架"
            }

            kotlin.runCatching {
                ImageUtil.getDominantSwatch(
                    mViewBinding.viewRootMask, pageData.bookCover
                ) { palette ->
                    setBgShadow(mViewBinding.viewRootMask, palette)
                }
            }

            kotlin.runCatching {
                ImageUtil.getDominantSwatch(
                    mViewBinding.viewMask, pageData.bookCover
                ) { palette ->
                    setMaskShadow(mViewBinding.viewMask, palette)
                }
            }


        }

        contract?.recommendNovelList?.observe(lifecycleOwner) { dataList ->
            if (dataList?.isEmpty() == true) {
                mViewBinding.emptyDataLayout.visibility = VISIBLE
                return@observe
            }
            mViewBinding.emptyDataLayout.visibility = GONE
            mViewBinding.tvRecommendTitle.visibility = VISIBLE
            val allCell = mutableListOf<DzRecyclerViewCell<BookInfoVo>>()
            dataList?.forEach { dec ->
                DzRecyclerViewCell<BookInfoVo>().apply {
                    viewClass = TtsRecommendItemCardComp::class.java
                    viewData = dec
                    allCell.add(this)
                    this.setActionListener(object : TtsRecommendItemCardComp.ViewActionListener {
                        override fun onClick(data: BookInfoVo?) {
                            val routeSource: String = mViewModel.routeIntent?.routeSource ?: ""
                            val routeSourceJson: OmapNode =
                                OmapNode.fromJson(routeSource) ?: OmapNode()
                            routeSourceJson.reader_third_reading_source = "播放页"
                            ReaderMR.get().reader().apply {
                                bookId = data?.bookId ?: ""
                                chapterId = data?.chapterId ?: ""
                                this.routeSource = routeSourceJson.toString()
                            }.start()
                        }

                        override fun onExpose(data: BookInfoVo?) {

                        }
                    })
                }
                mViewBinding.rvComponents.removeAllCells()
                mViewBinding.rvComponents.addCells(allCell)
            }
        }

        contract?.recommendStatus?.observe(lifecycleOwner) { status ->
            if (status == Status.NORMAL) {
                mViewBinding.rvComponents.visibility = VISIBLE
                mViewBinding.emptyDataLayout.visibility = GONE
            } else {
                mViewBinding.rvComponents.visibility = GONE
                mViewBinding.emptyDataLayout.visibility = VISIBLE
            }
            mViewBinding.emptyDataLayout.updateCompStatus(status)
        }

        contract?.isShelf?.observe(this) { isShelf ->
            if (isShelf == 1) {
                mViewBinding.ivShelf.setImageResource(R.drawable.reader_ic_tts_shelf)
                mViewBinding.tvShelf.text = "已加书架"
            } else {
                mViewBinding.ivShelf.setImageResource(R.drawable.reader_ic_tts_not_shelf)
                mViewBinding.tvShelf.text = "加入书架"
            }
        }
    }
}