package com.dz.business.reader.ui.component.menu

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Color
import android.util.AttributeSet
import android.widget.SeekBar
import androidx.lifecycle.LifecycleOwner
import com.dz.business.base.reader.ReaderME
import com.dz.business.reader.audio.TtsPlayer
import com.dz.business.reader.audio.TtsPlayer.Companion.STATUS_PAUSE
import com.dz.business.reader.databinding.ReaderAudioSeekbarCompBinding
import com.dz.business.reader.ui.view.Down
import com.dz.business.reader.ui.view.Event
import com.dz.business.reader.ui.view.Move
import com.dz.business.reader.ui.view.SeekBarViewOnChangeListener
import com.dz.business.reader.ui.view.Up
import com.dz.business.reader.utils.MenuTtsConfig
import com.dz.business.reader.utils.ReaderConfigUtil
import com.dz.foundation.base.utils.LogUtil
import com.dz.foundation.ui.view.custom.ActionListener
import com.dz.foundation.ui.view.custom.ActionListenerOwner
import com.dz.platform.common.base.ui.component.UIConstraintComponent

/**
 * @Description: 有声书章节进度条
 * @Version:1.0
 */
class MenuAudioSectionProgress @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : UIConstraintComponent<ReaderAudioSeekbarCompBinding, MenuAudioSectionProgress.SectionProgressBean>(
    context,
    attrs,
    defStyleAttr
), ActionListenerOwner<MenuAudioSectionProgress.ViewActionListener> {

    data class SectionProgressBean(var sectionProgress: Int = 0)

    private var isTouching: Boolean = false

    override var mActionListener: ViewActionListener? = null

    interface ViewActionListener : ActionListener, SeekBar.OnSeekBarChangeListener {
    }

    override fun initData() {
    }

    override fun initView() {
//        mViewBinding.seekbarSectionProgress.backgroundProgressBarColor =
//            if (ReaderConfigUtil.isNightMode()) Color.parseColor("#1AFFFFFF") else Color.parseColor(
//                "#1AFFFFFF"
//            )
//        mViewBinding.seekbarSectionProgress.thumb =
//            if (ReaderConfigUtil.isNightMode()) Color.parseColor("#1FFFFFFF") else Color.parseColor("#FFFFFFFF")
//            getDrawable(MenuTtsConfig.getProgressThumbNormal())
    }

    override fun initListener() {
        mViewBinding.ivRewind.setOnClickListener {
            TtsPlayer.instance.seekBackward(10000) // 后退10秒
        }
        mViewBinding.ivForward.setOnClickListener {
            TtsPlayer.instance.seekForward(10000) // 快进10秒
        }
        mViewBinding.seekbarSectionProgress.addOnChangeListener(object :
            SeekBarViewOnChangeListener {
            override fun touch(percent: Float, eventType: Event) {
                when (eventType) {
                    Up -> { // 手指触发抬起
                        isTouching = false
                        if (TtsPlayer.instance.chapterParagraphCount > 0 && (TtsPlayer.instance.isPlaying() || TtsPlayer.instance.status == STATUS_PAUSE) && totalTime > 0) {
                            val s =
                                "${formatTime((totalTime * percent).toInt())}/${formatTime(totalTime)}"
                            mViewBinding.seekbarSectionProgress.setPercent(percent, s)
                            TtsPlayer.instance.progressPresenter.seekToProgress((totalTime * percent * 1000).toInt())
                        }
                        LogUtil.d(
                            "打印",
                            "SeekBarViewOnChangeListener ：Up percent = ${percent} ${(totalTime * percent).toInt()} max=${TtsPlayer.instance.chapterParagraphCount} isPlaying=${TtsPlayer.instance.isPlaying()} STATUS_PAUSE=${TtsPlayer.instance.status == STATUS_PAUSE}"
                        )
                    }

                    Move -> { // 进度变更 or 手指滑动
                        if (TtsPlayer.instance.chapterParagraphCount > 0 && (TtsPlayer.instance.isPlaying() || TtsPlayer.instance.status == STATUS_PAUSE) && totalTime > 0) {
                            val s =
                                "${formatTime((totalTime * percent).toInt())}/${formatTime(totalTime)}"
                            mViewBinding.seekbarSectionProgress.setPercent(percent, s, true)
                        }
                        LogUtil.d(
                            "打印",
                            "SeekBarViewOnChangeListener ：Move percent = ${percent} ${(totalTime * percent).toInt()}  max=${TtsPlayer.instance.chapterParagraphCount} isPlaying=${TtsPlayer.instance.isPlaying()} STATUS_PAUSE=${TtsPlayer.instance.status == STATUS_PAUSE}"
                        )
                    }

                    Down -> { // 手指按下
                        LogUtil.d(
                            "打印",
                            "SeekBarViewOnChangeListener ：Down percent = ${percent} "
                        )
                        isTouching = true
                    }
                }
            }
        })
    }

    override fun setActionListener(actionListener: ViewActionListener?) {
        super.setActionListener(actionListener)
    }

    fun setMaxCount(second: Int) {
        LogUtil.d("XXX", "setMaxCount,count=$second")
//        mViewBinding.seekbarSectionProgress.max = count
        totalTime = second
        if (totalTime > 0) {
            mViewBinding.seekbarSectionProgress.isEnable = false
        }
    }

    var totalTime: Int = 0

    override fun bindData(data: SectionProgressBean?) {
        super.bindData(data)
        mData = data
        data?.let {
            if (!isTouching) {
                val s = "${formatTime(it.sectionProgress)}/${formatTime(totalTime)}"
                val f = if (totalTime > 0) {
                    it.sectionProgress.toFloat() / totalTime.toFloat()
                } else {
                    0.0f
                }
                mViewBinding.seekbarSectionProgress.setPercent(f, s)
            }
            LogUtil.d(
                "打印",
                "AudioSeekBar ：bindData: mViewBinding.tvProgressTime = ${it.sectionProgress}  ${totalTime}  ${isTouching}"
            )
        }
    }

    @SuppressLint("DefaultLocale")
    private fun formatTime(time: Int): String {
        val minutes = time / 60
        val seconds = time % 60
        return String.format("%02d:%02d", minutes, seconds)
    }

    override fun subscribeEvent(lifecycleOwner: LifecycleOwner, lifecycleTag: String) {
        super.subscribeEvent(lifecycleOwner, lifecycleTag)
        ReaderME.get().ttsStatusChanged().observe(lifecycleOwner) {
            // 当暂停状态且不可恢复时，禁用 seekbar
            mViewBinding.seekbarSectionProgress.isEnabled = (it != TtsPlayer.STATUS_LOCKED &&
                    it != TtsPlayer.STATUS_LOADING)
        }
    }
}