package com.dz.business.reader.ui.component.block

import android.app.Activity
import android.content.Context
import android.util.AttributeSet
import com.dz.business.reader.R
import com.dz.business.reader.databinding.ReaderTopStatusCompBinding
import com.dz.business.reader.ui.page.ReaderActivity
import com.dz.business.reader.utils.ReaderConfigUtil
import com.dz.foundation.base.utils.dp
import com.dz.foundation.ui.widget.getContainerActivity
import com.dz.platform.common.base.ui.component.UIConstraintComponent
import reader.xo.config.ColorStyle

/**
 * 沉浸式顶部状态栏的UI。
 * 包括：书名、菜单按钮
 * @constructor
 */
class ReaderTopStatusComp @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : UIConstraintComponent<ReaderTopStatusCompBinding, ReaderTopStatusComp.TopStatus>(
    context,
    attrs,
    defStyleAttr
) {
    override fun initData() {
    }

    override fun initView() {

        setColorStyle()
    }

    override fun initListener() {
        mViewBinding.llMenu.registerClickAction {
            getContainerActivity()?.let {
                showMenu(it)
            }
        }
    }

    private fun showMenu(activity: Activity) {
        if (activity is ReaderActivity) {
            activity.showMenu()
        }
    }

    override fun bindData(data: TopStatus?) {
        super.bindData(data)
        data?.bookName?.let { setBookName(it) }
        setColorStyle()
    }

    fun setColorStyle() {
        if (ReaderConfigUtil.isNightMode()) {
            mViewBinding.tvBookName.setTextColor(getColor(R.color.reader_color_61FFFFFF))
            mViewBinding.llMenu.setShapeBackground(
                radius = 14f.dp,
                solidColor = getColor(R.color.reader_1AFFFFFF)
            )
            mViewBinding.ivIcon.setImageResource(R.drawable.reader_ic_menu_night)
            mViewBinding.tvMenu.setTextColor(getColor(R.color.reader_color_61FFFFFF))
        } else {
            mViewBinding.tvBookName.setTextColor(getColor(R.color.reader_61000000))
            mViewBinding.llMenu.setShapeBackground(
                radius = 14f.dp,
                solidColor = getColor(R.color.reader_0D000000)
            )
            mViewBinding.ivIcon.setImageResource(R.drawable.reader_ic_menu_day)
            mViewBinding.tvMenu.setTextColor(getColor(R.color.reader_61000000))
        }
    }

    fun setBookName(bookName: String) {
        mViewBinding.tvBookName.text = bookName
    }

    data class TopStatus(var bookName: String, var colorStyle: ColorStyle) {

    }
}