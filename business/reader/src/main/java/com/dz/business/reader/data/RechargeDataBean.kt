package com.dz.business.reader.data

import com.dz.business.base.data.bean.BaseBean

/**
 * <AUTHOR>
 * @description:
 * @date :2022/10/27 14:53
 */
data class RechargeDataBean(
    var allZcList: ArrayList<RechargePayWayBean>? = null, //所有支持的支付方式【命名规避苹果审核】
    var rechargeGearList: ArrayList<RechargeMoneyBean>? = null,//充值档位列表
    var checkAgreement: Int = 0,//是否默认勾选协议，0-否，1-是
    var showAgreement: Int? = 0,//是否显示协议，0-否，1-是
    var hasYhq: Int? = 0,//是否有有效期的优惠券，0-否，1-是【命名规避苹果审核】
    var showYhq: Int? = 0,//是否显示优惠券，苹果内购不显示，0-否，1-是【命名规避苹果审核】
    var showZffs: Int? = 0,//是否显示支付方式，0-否，1-是【命名规避苹果审核】
    var title: String? = null,//充值列表标题，如：充值看点（充值中心用）、充值档位（订购页用）
    var subtitle: String? = null,//充值列表副标题，如：（1元=100看点）），如果在订购页则存放'更多档位'字样，在订购页时，有更多档位字样，则说明可点击，为空则不显示
    var title2: String? = null,//对应支付方式的标题，如：支付方式
    var pop: Int? = 0,//如果未勾选协议点击充值，是否弹窗引导勾选协议，0-否，1-是
    var msg: String? = null //优惠券点击立即使用但没有匹配的档位时提示语，p>=2时新增
) : BaseBean() {
    fun isValid(): Boolean {
        return !rechargeGearList.isNullOrEmpty() && !allZcList.isNullOrEmpty()
    }
    /**
     * 获取打点挡位信息
     *
     * @return
     */
    fun getTrackGearInfo(): String {
        var sb = StringBuilder()
        rechargeGearList?.forEach { item ->
            if (sb.isNotEmpty()) {
                sb.append("/")
            }
            sb.append(item.money ?: "")
            sb.append(item.moneyUnit ?: "")
            sb.append(item.desc ?: "")
        }
        return sb.toString()
    }


    /**
     * 获取打点重置方式信息
     *
     * @return
     */
    fun getTrackPayWayInfo(): String {

        var sb = StringBuilder()
        allZcList?.forEach { item ->
            if (sb.isNotEmpty()) {
                sb.append("/")
            }
            sb.append(item.title)
        }
        return sb.toString()
    }


}