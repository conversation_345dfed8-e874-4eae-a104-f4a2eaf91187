package com.dz.business.reader.ui.component.order

import android.content.Context
import android.util.AttributeSet
import com.dz.business.reader.data.BatchUnlockAct
import com.dz.business.reader.data.BatchUnlockGear
import com.dz.business.reader.data.RechargePayWayBean
import com.dz.business.reader.data.PayWayBean
import com.dz.business.reader.databinding.ReaderBatchUnlockRechargeCompBinding
import com.dz.foundation.ui.view.custom.ActionListener
import com.dz.foundation.ui.view.custom.ActionListenerOwner
import com.dz.foundation.ui.view.recycler.DzRecyclerViewCell
import com.dz.platform.common.base.ui.component.UIConstraintComponent

/**
 *@Author: shidz
 *@Date: 2022/11/10 16:51
 *@Description: 批量解锁充值信息组件
 *@Version:1.0
 */
class BatchUnlockRechargeComp @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : UIConstraintComponent<ReaderBatchUnlockRechargeCompBinding, BatchUnlockAct>(
    context,
    attrs,
    defStyleAttr
), ActionListenerOwner<BatchUnlockRechargeComp.BatchUnlockRechargeActionListener> {
    override fun initData() {
    }

    override fun initView() {
    }

    override fun initListener() {

    }

    override fun bindData(data: BatchUnlockAct?) {
        super.bindData(data)
        data?.let { setViewData(data) }

    }

    private fun setViewData(data: BatchUnlockAct) {
        data.batchUnlockGear?.let { setGearInfo(it) }
        data.allZcList?.let {
            val payWayBean = PayWayBean().apply {
                title = data.title2
                isValid = true
                payWayItemList = it
            }
            mViewBinding.compPayWay.setActionListener(object : RechargePayWayCompActionListener {
                override fun onPayWayChecked(bean: RechargePayWayBean) {
                    onPayWayItemChecked(bean)
                }
            })
            mViewBinding.compPayWay.bindData(payWayBean)
        }
    }


    private fun setGearInfo(gearList: List<BatchUnlockGear?>) {
        mViewBinding.rvMoney.addCells(createGearCells(gearList))
    }

    private fun createGearCells(gearList: List<BatchUnlockGear?>): MutableList<DzRecyclerViewCell<BatchUnlockGear>>? {
        val cellList = mutableListOf<DzRecyclerViewCell<BatchUnlockGear>>()
        gearList.forEachIndexed { index, batchUnlockGear ->
            batchUnlockGear?.run {
                if (index == 0) {
                    isSelected = true
                    onGearItemChecked(this)
                }
                val cellItem = createGearItemCell(this)
                if (cellItem != null) {
                    cellList.add(cellItem)
                }
            }

        }
        return cellList
    }

    private fun onGearItemChecked(batchUnlockGear: BatchUnlockGear) {
        mActionListener?.onGearChecked(batchUnlockGear)
    }

    private fun onPayWayItemChecked(bean: RechargePayWayBean) {
        mActionListener?.onPayWayItemChecked(bean)
    }

    private fun createGearItemCell(
        item: BatchUnlockGear
    ): DzRecyclerViewCell<BatchUnlockGear> {
        return DzRecyclerViewCell<BatchUnlockGear>().apply {
            viewClass = BatchUnlockGearItemComp::class.java
            viewData = item
            setActionListener(gearItemActionListener)
        }
    }

    private val gearItemActionListener =
        object : BatchUnlockGearItemComp.BatchUnlockGearActionListener {
            override fun onGearChecked(gear: BatchUnlockGear) {
                val allCells = mViewBinding.rvMoney.allCells
                for (cellItem in allCells) {
                    val batchUnlockGear = cellItem.viewData as BatchUnlockGear
                    if (batchUnlockGear == gear) {
                        if (!batchUnlockGear.isSelected) {
                            batchUnlockGear.isSelected = true
                            mViewBinding.rvMoney.updateCell(cellItem, batchUnlockGear)
                            onGearItemChecked(batchUnlockGear)
                        }
                    } else if (batchUnlockGear.isSelected) {
                        batchUnlockGear.isSelected = false
                        mViewBinding.rvMoney.updateCell(cellItem, batchUnlockGear)
                    }
                }

            }

        }


    override var mActionListener: BatchUnlockRechargeActionListener? = null

    interface BatchUnlockRechargeActionListener : ActionListener {
        fun onGearChecked(gear: BatchUnlockGear)
        fun onPayWayItemChecked(bean: RechargePayWayBean)
    }

}