package com.dz.business.reader.ui.component.block

import android.content.Context
import android.text.TextUtils
import android.util.AttributeSet
import android.view.View
import android.view.ViewGroup
import androidx.constraintlayout.widget.ConstraintLayout
import reader.xo.base.XoFile
import reader.xo.block.Block
import reader.xo.block.ExtContentBlockView
import reader.xo.config.ColorStyle
import reader.xo.config.LayoutStyle


class ReaderPageBlockView @JvmOverloads constructor(
    context: Context,
    type: Int,
    attrs: AttributeSet? = null,
) : ExtContentBlockView(context, type, attrs) {

    companion object {
        const val TYPE_BLOCk_COVER = 0
        const val TYPE_BOOK_END = 1
        const val TYPE_PREVIEW_PAY = 2

    }

    override fun setFontSize(fontSize: Int) {
        super.setFontSize(fontSize)
        mBlockComp?.setFontSize(fontSize)
    }

    override fun setColorStyle(colorStyle: ColorStyle) {
        super.setColorStyle(colorStyle)
        mBlockComp?.setColorStyle(colorStyle)
    }

    override fun setLayoutStyle(layoutStyle: LayoutStyle) {
        super.setLayoutStyle(layoutStyle)
        mBlockComp?.setLayoutStyle(layoutStyle)
    }

    private var currentTag: String? = null
    override fun bindData(file: XoFile, block: Block) {
        block.tag?.let { blockTag ->
            val dataTag = blockTag.javaClass.name + "@" + Integer.toHexString(blockTag.hashCode())
            if (TextUtils.equals(dataTag, currentTag)) {
                return
            }
            currentTag = dataTag
            super.bindData(file, block)
            mBlockComp?.bindViewData(file.fid, block)
        }

    }

    private var mBlockComp: ReaderBlockComp? = null
    fun addBlock(blockComp: ReaderBlockComp): ReaderPageBlockView {
        mBlockComp = blockComp
        if (blockComp is View) {
            if (blockComp.parent != null) {
                (blockComp.parent as ViewGroup).removeView(blockComp)
            }
            val layoutParams = ConstraintLayout.LayoutParams(
                ConstraintLayout.LayoutParams.MATCH_PARENT,
                ConstraintLayout.LayoutParams.MATCH_PARENT
            )
            addView(blockComp, layoutParams)
        }
        return this

    }

}