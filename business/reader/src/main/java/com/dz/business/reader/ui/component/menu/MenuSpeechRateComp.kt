package com.dz.business.reader.ui.component.menu

import android.content.Context
import android.util.AttributeSet
import androidx.lifecycle.LifecycleOwner
import com.dz.business.base.reader.ReaderME
import com.dz.business.reader.R
import com.dz.business.reader.audio.TtsPlayer
import com.dz.business.reader.databinding.ReaderMenuSpeechRateCompBinding
import com.dz.business.reader.utils.MenuTtsConfig
import com.dz.business.reader.utils.ReaderConfigUtil
import com.dz.business.track.trackProperties
import com.dz.foundation.base.utils.dp
import com.dz.platform.common.base.ui.component.UIConstraintComponent

/**
 * @Author: guyh
 * @Date: 2022/11/2 22:04
 * @Description:  调整语音速率
 * @Version:1.0
 */
class MenuSpeechRateComp @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : UIConstraintComponent<ReaderMenuSpeechRateCompBinding, Boolean>(
    context,
    attrs,
    defStyleAttr
) {

    override fun initData() {
    }

    override fun initView() {
        trackConfig()
        mViewBinding.tvSpeechRate.text = String.format(
            context.getString(R.string.reader_magnification),
            TtsPlayer.instance.speedPresenter.speed
        )
        updateTheme()
    }

    private fun trackConfig() {
        mViewBinding.tvAccelerate.trackProperties(ignoreAutoTrack = true)
        mViewBinding.tvSlowDown.trackProperties(ignoreAutoTrack = true)
    }

    override fun initListener() {
        mViewBinding.apply {
            getClickEventHandler().addInterceptor{
                TtsPlayer.instance.isLoading()
            }
            tvAccelerate.registerClickAction {
                if (!TtsPlayer.instance.isRunning()) return@registerClickAction
                // 加速
                TtsPlayer.instance.speedPresenter.speedFaster()
                mViewBinding.tvAccelerate.trackProperties(elementParam = TtsPlayer.instance.speedPresenter.speed)

            }
            tvSlowDown.registerClickAction {
                if (!TtsPlayer.instance.isRunning()) return@registerClickAction
                // 减速
                TtsPlayer.instance.speedPresenter.speedSlower()
                mViewBinding.tvSlowDown.trackProperties(elementParam = TtsPlayer.instance.speedPresenter.speed)
            }
        }
    }

    override fun bindData(data: Boolean?) {
        super.bindData(data)
        (data ?: true).apply {
            mViewBinding.tvAccelerate.isEnabled = this
            mViewBinding.tvSlowDown.isEnabled = this
        }
    }

    override fun subscribeEvent(lifecycleOwner: LifecycleOwner, lifecycleTag: String) {
        super.subscribeEvent(lifecycleOwner, lifecycleTag)
        ReaderME.get().ttsSpeedChanged().observe(lifecycleOwner) {
            mViewBinding.tvSpeechRate.text =
                String.format(context.getString(R.string.reader_magnification), it)
        }
    }

    fun updateTheme() {
        mViewBinding.tvSpeechRateTitle.setTextColor(getColor(MenuTtsConfig.getSpeechRateColorTitle()))
        mViewBinding.tvSpeechRate.setTextColor(getColor(MenuTtsConfig.getSpeechRateColor()))
        if (ReaderConfigUtil.isNightMode())
            setNightMode()
        else
            setDayMode()
    }

    private fun setDayMode() {
        mViewBinding.run {
            tvAccelerate.setShapeBackground(
                radius = 18f.dp,
                solidColor = getColor(R.color.reader_0A000000),
            )
            tvAccelerate.setTextColor(getColor(R.color.reader_99000000))
            tvSlowDown.setShapeBackground(
                radius = 18f.dp,
                solidColor = getColor(R.color.reader_0A000000),
            )
            tvSlowDown.setTextColor(getColor(R.color.reader_99000000))
        }
    }

    private fun setNightMode() {
        mViewBinding.run {
            tvAccelerate.setShapeBackground(
                radius = 18f.dp,
                solidColor = getColor(R.color.reader_1AFFFFFF),
            )
            tvAccelerate.setTextColor(getColor(R.color.reader_color_99FFFFFF))
            tvSlowDown.setShapeBackground(
                radius = 18f.dp,
                solidColor = getColor(R.color.reader_1AFFFFFF),
            )
            tvSlowDown.setTextColor(getColor(R.color.reader_color_99FFFFFF))
        }
    }
}