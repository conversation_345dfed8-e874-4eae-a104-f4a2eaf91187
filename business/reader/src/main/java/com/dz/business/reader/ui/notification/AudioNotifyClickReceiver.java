package com.dz.business.reader.ui.notification;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

import com.dz.business.base.splash.SplashMR;
import com.dz.business.reader.audio.TtsPlayer;
import com.dz.business.reader.audio.presenter.TtsChapterPresenter;
import com.dz.business.reader.ui.page.ReaderActivity;
import com.dz.foundation.base.utils.LocalActivityMgr;
import com.dz.foundation.base.utils.LogUtil;

/**
 * 音频播放通知，按钮点击监听
 */
public class AudioNotifyClickReceiver extends BroadcastReceiver {

    @Override
    public void onReceive(Context context, Intent intent) {
        String action = intent.getAction();
        LogUtil.i("AudioPlayUtilService", "ButtonBroadcastReceiver-----onReceiveMsg()---action" + action);
        if (AudioPlayUtilService.Companion.getAction().equals(action)) {
            //通过传递过来的ID判断按钮点击属性或者通过getResultCode()获得相应点击事件
            int buttonId = intent.getIntExtra(AudioPlayUtilService.INTENT_BUTTON_ID_TAG, 0);
            switch (buttonId) {
                case AudioPlayUtilService.BUTTON_ID_PRE_CHAPTER:
                    TtsPlayer.Companion.getInstance().getChapterPresenter().loadPreviousChapter();
                    break;
                case AudioPlayUtilService.BUTTON_ID_NEXT_CHAPTER:
                    TtsPlayer.Companion.getInstance().getChapterPresenter().loadNextChapter(TtsChapterPresenter.LOAD_NEXT_CHAPTER_BY_USER);
                    break;
                case AudioPlayUtilService.BUTTON_ID_PLAY:  // 播放暂停
                case AudioPlayUtilService.BUTTON_ID_PAUSE:  // 播放暂停
                    if (!canPlayOrPause()) return;
                    TtsPlayer.Companion.getInstance().toggleTTS();
                    break;
                case AudioPlayUtilService.BUTTON_ID_CANCEL:
                    TtsPlayer.Companion.getInstance().cancelNotification();
                    break;
                case AudioPlayUtilService.BUTTON_ID_ROOT:  // 调起阅读页
//                    String bookId = intent.getStringExtra(AudioPlayUtilService.INTENT_BOOK_ID);
//                    String chapterId = intent.getStringExtra(AudioPlayUtilService.INTENT_CHAPTER_ID);
//                    ReaderIntent readerIntent = ReaderMR.Companion.get().reader();
//                    readerIntent.setBookId(bookId);
//                    readerIntent.setChapterId(chapterId);
//                    readerIntent.setIntentFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_SINGLE_TOP);
//                    readerIntent.start();

                    SplashMR.Companion.get().splash().start();
                    break;
                default:
                    break;
            }
        }
    }

    /**
     * 唤醒APP
     *
     * @param context
     */

    public void rouseApp(Context context) {

        Activity currentActivity = LocalActivityMgr.INSTANCE.getTopActivity();

        if (currentActivity != null) {

            if (currentActivity instanceof ReaderActivity) {
//                ReaderUtils.continueReadBook(Const.getInstance().getCurActivity());
//                if (DzTtsManager.getInstance().ttsReady()) {
//                    EventBusUtils.sendMessage(EventConstant.CODE_NOTIFICATION_CONTROLLER_PLAY);
//                }
            } else {
                Intent intent = new Intent(context, currentActivity.getClass());
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_SINGLE_TOP);
                context.startActivity(intent);
            }
//            SensorTrackManager.trackAppStart("本地" + LogConstants.LAUNCH_SCENE_PUSH, currentActivity.getClass().getName());
        } else {
            Intent intent = context.getPackageManager().getLaunchIntentForPackage(context.getPackageName());
            context.startActivity(intent);
        }

    }

    private boolean canPlayOrPause() {
        int ttsStatus = TtsPlayer.Companion.getInstance().getStatus();
        return TtsPlayer.Companion.getInstance().isRunning() &&
                !TtsPlayer.Companion.getInstance().isLoading() &&
                ttsStatus != TtsPlayer.STATUS_ERROR;

    }
}