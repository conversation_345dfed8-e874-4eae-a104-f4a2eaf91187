package com.dz.business.reader.vm

import com.dz.business.base.livedata.CommLiveData
import com.dz.business.base.vm.ComponentVM
import com.dz.business.reader.data.PushMoreBean
import com.dz.business.reader.network.ReaderNetwork
import com.dz.foundation.network.*
import com.dz.platform.common.toast.ToastManager

/**
 * <AUTHOR>
 * @description:
 * @date :2022/11/16 18:48
 */
class BookEndStatusCompVM : ComponentVM() {
    var mPushMoreLd = CommLiveData<PushMoreBean>()
    fun doPushMoreRequest(bookId: String?) {
        if (bookId == null) {
            return
        }
        ReaderNetwork.get()
            .pushMore()
            .setTag(uiId)
            .setParams(bookId)
            .onStart {
            }
            .onResponse {
                if (it.isSuccess() && it.data != null) {
                    mPushMoreLd.value = it.data
                }
            }
            .onError {
                ToastManager.showToast(it.message)
            }
            .onEnd {
            }
            .doRequest()
    }
}