package com.dz.business.reader.ui.component.menu

import CoroutineUtils
import android.animation.ObjectAnimator
import android.content.Context
import android.graphics.drawable.GradientDrawable
import android.util.AttributeSet
import android.view.View
import android.view.animation.LinearInterpolator
import androidx.annotation.ColorInt
import androidx.core.content.ContextCompat
import androidx.core.graphics.ColorUtils
import androidx.lifecycle.LifecycleOwner
import androidx.palette.graphics.Palette
import com.dz.business.base.helper.FloatWindowManage
import com.dz.business.base.helper.FloatWindowManage.Companion.AUDIO_SOURCE
import com.dz.business.base.helper.FloatWindowManage.Companion.AUDIO_TYPE
import com.dz.business.base.helper.FloatWindowManage.Companion.TTS_TYPE
import com.dz.business.base.home.HomeME
import com.dz.business.base.reader.ReaderME
import com.dz.business.base.reader.ReaderMR
import com.dz.business.base.reader.ReaderMS
import com.dz.business.base.utils.ImageUtil
import com.dz.business.reader.DataRepository
import com.dz.business.reader.R
import com.dz.business.reader.audio.AudioListener
import com.dz.business.reader.audio.TtsPlayer
import com.dz.business.reader.audio.TtsPlayer.Companion.STATUS_PLAYING
import com.dz.business.reader.audio.presenter.TtsLoaderPresenter
import com.dz.business.reader.databinding.ReaderMenuTtsFloatCompBinding
import com.dz.business.reader.repository.entity.AudioBookEntity
import com.dz.business.reader.repository.entity.NovelBookEntity
import com.dz.business.reader.ui.page.AudioOpener
import com.dz.business.track.trace.OmapNode
import com.dz.foundation.base.utils.LogUtil
import com.dz.foundation.base.utils.ScreenUtil
import com.dz.foundation.imageloader.loadCircleImage
import com.dz.platform.common.base.ui.component.UIConstraintComponent

/**
 *@Author: shidz
 *@Date: 2022/10/9 23:22
 *@Description: tts 悬浮控制view
 *@Version:1.0
 */
class MenuTtsFloatComp @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : UIConstraintComponent<ReaderMenuTtsFloatCompBinding, String>(
    context,
    attrs,
    defStyleAttr
), AudioListener {
    companion object {
        private val DEFAULT_COLOR = R.color.common_FF3C3C3C
    }

    private lateinit var rotateAnimation: ObjectAnimator

    private var mAudioListener: AudioListener? = null


    override fun initData() {

        rotateAnimation =
            ObjectAnimator.ofFloat(mViewBinding.ivBookCover, "rotation", 0f, 360f).apply {
                duration = 4000
                interpolator = LinearInterpolator()
                repeatCount = ObjectAnimator.INFINITE
            }

        mAudioListener = object : AudioListener {
            override fun onBookOpen(bookInfo: NovelBookEntity?, bookEntity: AudioBookEntity?) {
                super.onBookOpen(bookInfo, bookEntity)
                LogUtil.d(
                    "XXX",
                    "悬浮窗 onBookOpen,bookInfo=${bookInfo?.coverurl},bookEntity=${bookEntity?.coverurl}"
                )
                mViewBinding.ivBookCover.loadCircleImage(
                    AudioOpener.instance.getCurrentBookCoverUrl(),
                    width = 40,
                    height = 40
                )
            }

        }.apply {
            AudioOpener.instance.registerAudioListener(this)
        }

    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        rotateAnimation.cancel()

        mAudioListener?.let {
            AudioOpener.instance.unregisterAudioListener(it)
        }

        HomeME.get().audioCompVisibilityChanged().post(false)
    }


    override fun initView() {
        updateBookCover()

        val playerStatus = TtsPlayer.instance.status
        onPlayerStatusChanged(playerStatus)


    }

    override fun initListener() {
        //点击热区
        mViewBinding.rootLayout.registerClickAction {
            startTts()
        }
        //暂停，播放按钮
        mViewBinding.ivPlay.registerClickAction {
            TtsPlayer.instance.toggleTTS()
        }
        //关闭按钮
        mViewBinding.ivClose.registerClickAction {
            closeComp()
        }
        mViewBinding.ivBookCover.registerClickAction {
            startTts()
        }

        mViewBinding.viewClose.registerClickAction {
            closeComp()
        }

    }

    private fun closeComp() {
        //退出播放系统
        AudioOpener.instance.exit(true)
        LogUtil.d("打印", "您已退出语音朗读模式：closeComp")
        <EMAIL> = GONE
        //后续其他页面不需要自动展示悬浮bar
        FloatWindowManage.instance.allDismiss = true
        //隐藏现在的悬浮bar
        ReaderMS.get()?.dismissAudioComp(AUDIO_SOURCE)
    }

    private fun startTts() {
        val routeSourceStr = AudioOpener.instance.getBookSource()
        val routeSourceJson: OmapNode =
            OmapNode.fromJson(routeSourceStr) ?: OmapNode()
        routeSourceJson.reader_first_reading_source = "悬浮bar"
        routeSourceJson.reader_second_reading_source = "悬浮bar"
        routeSourceJson.reader_third_reading_source = "悬浮bar"

        when (AudioOpener.instance.audioType) {
            TTS_TYPE -> AudioOpener.instance.getCurrentChapterInfo()?.let { chapterInfo ->
                ReaderMR.get().ttsDetail().apply {
                    bookId = chapterInfo.bid
                    chapterId = chapterInfo.cid
                    bookSource = routeSourceJson.toJson()
//                bookOpenBean =AudioOpener.instance.getCurrentBookInfo()
//            currentPos = getXoReader().getCurrentDocInfo().charIndex
                    bookCover = AudioOpener.instance.getCurrentBookCoverUrl()
                    audioType = TTS_TYPE
                }.start()
            }

            AUDIO_TYPE -> AudioOpener.instance.getCurrentAudioChapterInfo()?.let { chapterInfo ->
                ReaderMS.get()?.openAudioPage(
                    bookId = chapterInfo.bid,
                    chapterId = chapterInfo.cid,
                    bookSource =routeSourceJson.toJson(),
                    bookCover = AudioOpener.instance.getCurrentBookCoverUrl(),
                )
            }

            else -> {}
        }

    }

    override fun subscribeEvent(lifecycleOwner: LifecycleOwner, lifecycleTag: String) {
        super.subscribeEvent(lifecycleOwner, lifecycleTag)
        // 播放进度发生变化
        ReaderME.get().ttsProgressChanged().observe(lifecycleOwner, lifecycleTag) {
            //这里的 progress 是段落index，真正的进度=progress * 100 / 总段落
            val realProgress = TtsPlayer.instance.getCurrentPlayProgress()
            refreshChapterTime(it)
            LogUtil.d(
                "XXX",
                "悬浮窗ttsProgressChanged 当前播放的段落=${it},真实进度=${realProgress}"
            )
            mViewBinding.ivPlay.setProgress(realProgress, true)

            showPlayStatus()
            mViewBinding.ivPlay.setCenterImg(R.drawable.reader_ic_tts_float_pause)
        }

        // 播放状态发生变化
        ReaderME.get().ttsStatusChanged().observe(lifecycleOwner, lifecycleTag) {
            LogUtil.d(
                "打印",
                "显示播放的问题:ttsStatusChanged it=${it}"
            )
            onPlayerStatusChanged(it)
        }

        // 加载进度改变
        ReaderME.get().ttsLoadingChanged().observe(lifecycleOwner) {
            LogUtil.d("XXX", "悬浮窗 ttsLoadingChanged,status=${it}")
            when (it) {
                TtsLoaderPresenter.LOAD_CURRENT_CHAPTER,
                TtsLoaderPresenter.LOAD_PREVIOUS_CHAPTER,
                TtsLoaderPresenter.LOAD_NEXT_CHAPTER,
                TtsLoaderPresenter.LOAD_BUFFER_START,
                TtsLoaderPresenter.LOAD_OWN_AUDIO,
                TtsLoaderPresenter.LOAD_THIRD_AUDIO -> {
                    showLoadingStatus()
                }

                else -> {
                    showPlayStatus()
                }
            }
        }

    }

    private fun onPlayerStatusChanged(status: Int) {
        LogUtil.d(
            "打印",
            "显示播放的问题:ttsStatusChanged it=${status}"
        )
        val realProgress = TtsPlayer.instance.getCurrentPlayProgress()
        mViewBinding.ivPlay.setProgress(realProgress, false)
        when (status) {
            TtsPlayer.STATUS_PLAYING -> {
                showPlayStatus()
                mViewBinding.ivPlay.setCenterImg(R.drawable.reader_ic_tts_float_pause)
            }

            TtsPlayer.STATUS_PAUSE, TtsPlayer.STATUS_LOCKED, TtsPlayer.STATUS_CLOSE, TtsPlayer.STATUS_STOP -> {
                showPlayStatus()
                mViewBinding.ivPlay.setCenterImg(R.drawable.reader_ic_tts_float_play)

            }

            TtsPlayer.STATUS_LOADING -> {
                showLoadingStatus()
            }
        }
    }


    fun showPlayStatus() {
        mViewBinding.loading.cancelAnimation()
        mViewBinding.loading.visibility = GONE
        mViewBinding.ivPlay.visibility = VISIBLE
        if (TtsPlayer.instance.status == STATUS_PLAYING) {
            if (!rotateAnimation.isRunning) {
                rotateAnimation.start()
            } else {
                rotateAnimation.resume()
            }
        } else {
            rotateAnimation.pause()
        }
    }

    private fun showLoadingStatus() {
        mViewBinding.ivPlay.visibility = INVISIBLE
        mViewBinding.loading.visibility = VISIBLE
        mViewBinding.loading.playAnimation()

        rotateAnimation.cancel()
    }


    private fun updateBookCover() {
        LogUtil.d("XXX", "updateBookCover,coverUrl=AudioOpener.instance.getCurrentBookCoverUrl()")
        mViewBinding.ivBookCover.loadCircleImage(
            AudioOpener.instance.getCurrentBookCoverUrl(),
            width = 40,
            height = 40
        )
        kotlin.runCatching {
            ImageUtil.getDominantSwatch(
                mViewBinding.rootLayout, AudioOpener.instance.getCurrentBookCoverUrl()
            ) { palette ->
                setShadow(mViewBinding.rootLayout, palette)
            }
        }
    }

    override fun bindData(data: String?) {
        super.bindData(data)
        LogUtil.d(
            "XXX",
            "悬浮窗bindData data=${data}，url=${AudioOpener.instance.getCurrentBookCoverUrl()}"
        )
        data?.run {
            LogUtil.d("打印", "封面动画：rotateAnimation.start() ${TtsPlayer.instance.isPlaying()}")
            if (TtsPlayer.instance.status == STATUS_PLAYING) {
                if (!rotateAnimation.isRunning) {
                    rotateAnimation.start()
                } else {
                    rotateAnimation.resume()
                }
            }
            updateBookCover()

        }
    }

    private fun setShadow(view: View, palette: Palette?) {
        val domainColor = getColorFromPalette(view, palette)
        // 创建颜色数组，用于定义渐变色
        val colors = intArrayOf(
            domainColor,
            domainColor
        )

        val cornerRadius = ScreenUtil.dip2px(context, 24).toFloat()
        val gradientDrawable =
            GradientDrawable(GradientDrawable.Orientation.TOP_BOTTOM, colors).apply {
                shape = GradientDrawable.RECTANGLE
                cornerRadii = floatArrayOf(
                    cornerRadius,
                    cornerRadius,
                    cornerRadius,
                    cornerRadius,
                    cornerRadius,
                    cornerRadius,
                    cornerRadius,
                    cornerRadius
                )
                gradientType = GradientDrawable.LINEAR_GRADIENT
            }
        // 将创建的GradientDrawable设置为View的背景
        view.background = gradientDrawable
    }

    /**
     * 从 Palette 中获取颜色
     */
    @ColorInt
    private fun getColorFromPalette(view: View, palette: Palette?): Int {
//        LogUtil.d("ImageUtil", "${mData?.bookName} " +
//                "dominantSwatch:{${String.format("#%06X", 0xFFFFFF and (palette?.dominantSwatch?.rgb ?: Color.WHITE))} ${palette?.dominantSwatch?.hsl?.contentToString()}} " +
//                "darkVibrantSwatch:{${String.format("#%06X", 0xFFFFFF and (palette?.darkVibrantSwatch?.rgb ?: Color.WHITE))} ${palette?.darkVibrantSwatch?.hsl?.contentToString()}}")
        return palette?.dominantSwatch?.hsl?.let {
            it[2] = 0.3F  // 统一亮度
            ColorUtils.HSLToColor(it)
        } ?: ContextCompat.getColor(view.context, DEFAULT_COLOR)
    }


    /**
     * 刷新播放进度
     */
    private fun refreshChapterTime(time: Int) {
        if (time > 0 && AudioOpener.instance.audioType == AUDIO_TYPE && TtsPlayer.instance.isPlaying()) {
            var progress = time
            if ((time + 3) >= TtsPlayer.instance.chapterParagraphCount) {
                progress = TtsPlayer.instance.chapterParagraphCount - 3
            }
            CoroutineUtils.launch {
                AudioOpener.instance.getCurrentAudioChapterInfo()?.let { e ->
                    e.ptime = (progress * 1000).toLong()
                    LogUtil.d(
                        "打印",
                        "当前章节:refreshChapterTime e?.ptime=${e?.ptime}  time=${time} book_name=${e.book_name}  chapterParagraphCount=${TtsPlayer.instance.chapterParagraphCount}"
                    )
                    DataRepository.audioChapterDao().insertOrUpdate(e)
                }
            }
        }
    }

}