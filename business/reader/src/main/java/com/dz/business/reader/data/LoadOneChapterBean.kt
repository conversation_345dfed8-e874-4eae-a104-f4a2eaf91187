package com.dz.business.reader.data

import android.os.SystemClock
import com.dz.business.base.data.bean.BaseBean
import com.dz.business.base.data.bean.BaseOperationBean
import com.dz.foundation.base.utils.LogUtil

/**
 *@Author: shidz
 *@Date: 2022/10/19 0:31
 *@Description:章节加载信息
 *@Version:1.0
 */
data class LoadOneChapterBean(
    var bookId: String? = "",
    var bookName: String? = "",
    var author: String? = null,
    var bookStatus: Int? = 0,
    var chapterInfo: NovelChapterInfo? = null,
    var coverWap: String? = "",
    var currentChapterInfo: NovelChapterInfo? = null,
    var msg: String? = "",
    var orderPageVo: OrderPageVo? = null,
    var readEndResponse: ReadEndResponse? = null,

    //状态：1-扣费成功；2-不需要付费，免费章节；3-不需要付费，已购章节；4-购买确认的弹窗；5-余额不足的弹窗；6-终章了,7,订购加载失败(服务异常，参数异常)，8预加载失败，9，书架被删除，10书籍下架,11章节内容丢失,服务端会返回一段描述章节缺失的文案,12,余额不足但下发充值挡位失败
    var status: Int? = 0,
    var bookType: String? = null,//书籍类型 1 文本书籍  2音频书籍
    var bookTypeOne: String? = null,//书籍一级分类  1出版  2 男频  3 女频
    var bookTypeThree: String? = null,//书籍三级分类  1出版  2 男频  3 女频
    var unit: Int? = 0,// 0单章购买  1 全本购买
    var consumeAmountFree: String? = null,// 消费赠送看点数
    var consumeAmountPay: String? = null,//消费充值看点数
    var updateTime: Long? = null,

    var isInBookShelf: Boolean? = false, //书籍是否已经在书架上

    //有声书小说标签
    var mark: String? = null,
) : BaseBean() {
    companion object {
        const val STATUS1 = 1 //扣费成功
        const val STATUS2 = 2 //不需要付费，免费章节
        const val STATUS3 = 3 //不需要付费，已购章节
        const val STATUS4 = 4 //购买确认的弹窗
        const val STATUS5 = 5 //余额不足的弹窗
        const val STATUS6 = 6 //终章了
        const val STATUS7 = 7 //订购加载失败(服务异常，参数异常)
        const val STATUS8 = 8 //预加载失败
        const val STATUS9 = 9 //书架被删除
        const val STATUS10 = 10 //书籍下架
        const val STATUS11 = 11 //章节内容丢失,服务端会返回一段描述章节缺失的文案
        const val STATUS12 = 12  //余额不足但下发充值挡位失败
    }

    //书籍来源 本地流转，非服务下发
    var source: String? = null

    fun isPaySuccessStatus(): Boolean {
        return status == STATUS1
    }

    //加载内容成功
    fun loadContentSuccess(): Boolean {
        return when (status) {
            STATUS1, STATUS2, STATUS3, STATUS11 -> true
            else -> false
        }
    }

    /**
     * 需要显示订购弹窗
     */
    fun needShowOrder(): Boolean {
        return when (status) {
            STATUS4, STATUS5 -> true
            else -> false
        }
    }

    /**
     * 终章
     */
    fun bookEnd(): Boolean {
        return status == STATUS6
    }

    /**
     *
     *
     * @return
     */
    fun unshelve(): Boolean {
        return status == STATUS10
    }

    /**
     * 删除
     *
     * @return
     */
    fun removed(): Boolean {
        return status == STATUS9
    }

    /**
     * 删除
     *
     * @return
     */
    fun rechargeGearEmpty(): Boolean {
        return status == STATUS12
    }
}

/**
 *@Author: shidz
 *@Date: 2022/11/9 10:58
 *@Description: 订单信息
 *@Version:1.0
 */
data class OrderPageVo(
    var batchUnlockAct: BatchUnlockAct? = null,
    var bookAmount: Int? = 0,
    var bookAmountTitle: String? = "",
    var bookAmountUnit: String? = "",
    var buttonTips: String? = "",
    var buttonText: String? = "",
    var guideWords: String? = "",
    var chapterId: String? = "",
    var index: Int? = null,
    var bookId: String? = null,
    var chapterName: String? = "",
    var rechargeInfo: RechargeDataBean? = null,
    var selectAutoPay: Int? = 0,
    var showAutoPay: Int? = 0,
    var title: String? = "",
    var totalAmount: Int? = 0,
    var totalAmountTitle: String?,
    var totalAmountUnit: String? = "",
    var unit: Int? = 0,
    var isSssVvvFreeBook: Int? = 0,//是否是超V免费书，0-否，1-是
    var previewContent: String? = null,
    val vipTipVo: BaseOperationBean?, //底部运营位
//    val equityInfo: EquityInfoBean?, //权益列表信息
    val style: Int?, //订购页样式，默认0-原来样式；1-会员订购页样式
    val iapText: String?,
    val unlockOperate: BaseOperationBean? = null//广告解锁运营位
) : BaseBean() {

    // refresh 从阅读器跳转到别的页面后返回刷新
    var refreshRequest: Boolean = false

    //章节未解锁，余额不足时是否需要主动弹出订购弹窗,本地流转未服务端下发
    var needAutoShowPayDialog: Boolean = false

    var batchUnLockDialogShowing = false
        // 批量解锁弹窗是否正在显示
        //批量解锁弹窗开始显示时间
        set(value) {
            field = value
            batchUnLockDialogShowingStartTime = SystemClock.elapsedRealtime()
        }
    private var batchUnLockDialogShowingStartTime = 0L

    var singleOrderDialogShowing = false
        // 单章订购弹窗是否正在显示
        set(value) {
            field = value
            singleOrderDialogShowingStartTime = SystemClock.elapsedRealtime()
        }
    private var singleOrderDialogShowingStartTime = 0L //单章订购弹窗开始显示时间


    /**
     *是否需要阻断用户翻页操作，解决快速翻页导致的连续跳两章订购页的问题
     * @return
     */
    fun blockTurnPage(): Boolean {
        val elapsedRealtime = SystemClock.elapsedRealtime()
        val unlockDistanceTime = elapsedRealtime - batchUnLockDialogShowingStartTime
        val singleOrderDistanceTime = elapsedRealtime - singleOrderDialogShowingStartTime
        LogUtil.d(
            "ReaderListener", chapterName +
                    "blockTurnPage batchUnLockDialogShowing=$batchUnLockDialogShowing unlockDistanceTime=$unlockDistanceTime singleOrderDialogShowing=$singleOrderDialogShowing singleOrderDistanceTime =  $singleOrderDistanceTime"
        )
        return (batchUnLockDialogShowing && unlockDistanceTime < 300) || (singleOrderDialogShowing && singleOrderDistanceTime < 300)
    }

    //充值来源场景
    var sourceScene: String? = null //reader/audio
}







