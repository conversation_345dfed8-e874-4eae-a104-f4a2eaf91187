package com.dz.business.reader.data

import com.dz.business.base.data.bean.BaseBean

/**
 * <AUTHOR>
 * @description:
 * @date :2022/10/27 15:16
 */
data class RechargeMoneyBean(
    var id: String = "",//充值金额ID
    var money: Double?,//充值金额，如：50
    var moneyText: String? = "",//充值列表标题，如：充值看点（充值中心用）、充值档位（订购页用）
    var moneyUnit: String? = "",//充值金额单位，如：元
    var topDesc: String? = "",//最顶部的优惠描述信息(主要用于VIP)
    var desc: String? = "",//充值金额下方描述信息，如：100送300看点
    var bottomDesc: String? = "",//最底部的优惠描述信息
    var cornerMark: String? = "",//角标
    var verifyParam: String? = "",//透传校验参数
    var check: Int? = 0,//是否默认选中，0-否，1-是
    var gearLx: Int? = 0,//档位类型，1-购买看点，2-购买超级VIP，3-购买超级VIP并自动续费【命名规避苹果审核】
    var giveAmount: Int? = 0,//赠送看点
    var middleDesc: String? = "",//VIP档位下方优惠说明，p>=2时新增，如：折合1元/天
    /**
     * 支持的支付方式列表，
     * dd100-WECHAT_MOBILE_PAY（微信SDK），
     * dd200-WECHAT_WAP_PAY（现在支付），
     * dd300-ALIPAY_MOBILE_PAY（支付宝SDK），
     * dd400-ALIPAY_WEB_PAY（支付宝WEB支付），
     * dd500-IOS_IAP_PAY（苹果内购），
     * 为空时表示都支持【命名规避苹果审核】
     */
    var zCList: ArrayList<String>,
    var gearStyle:Int?=0,//档位显示样式 0，普通  1，长款，p>=3时增加
    var firstPayPrice:String?//原价格
) : BaseBean() {

    /**
     * 该充值档位是否选中
     */
    var isSelected: Boolean = false

    /**
     * 该充值档位是否支持选择的支付方式
     */
    var isSupportSelectedPay: Boolean = false


    fun isCheckSupportPayWay(selectedPay: RechargePayWayBean?): Boolean {
        zCList.run {
            if (zCList.contains(selectedPay?.descId)) {
                return true
            }
        }
        return false
    }
}