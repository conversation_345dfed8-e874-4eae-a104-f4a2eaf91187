package com.dz.business.reader.ui.page

import android.animation.Animator
import android.annotation.SuppressLint
import android.content.Intent
import android.text.TextUtils
import android.view.View
import android.view.ViewGroup.MarginLayoutParams
import androidx.core.content.ContextCompat
import androidx.lifecycle.LifecycleOwner
import androidx.recyclerview.widget.LinearLayoutManager
import com.dz.business.base.reader.ReaderME
import com.dz.business.base.reader.ReaderMR
import com.dz.business.base.ui.BaseActivity
import com.dz.business.base.ui.component.status.Status
import com.dz.business.base.ui.component.status.StatusComponent
import com.dz.business.reader.R
import com.dz.business.reader.databinding.ReaderCatalogActivityBinding
import com.dz.business.reader.initReader
import com.dz.business.reader.ui.component.CatalogItemComp
import com.dz.business.reader.ui.component.CatalogTopBean
import com.dz.business.reader.ui.component.ChapterItemBean
import com.dz.business.reader.utils.CatalogColorConfig
import com.dz.business.reader.utils.ReaderConfigUtil
import com.dz.business.reader.utils.ReaderSettingUtils
import com.dz.business.reader.vm.CatalogVMEventCallback
import com.dz.business.reader.vm.ReaderCatalogVM
import com.dz.foundation.base.module.AppModule
import com.dz.foundation.base.utils.LogUtil
import com.dz.foundation.base.utils.NetWorkUtil
import com.dz.foundation.base.utils.ScreenUtil
import com.dz.foundation.base.utils.dp
import com.dz.foundation.network.requester.RequestException
import com.dz.foundation.ui.utils.ShapeDrawableUtil
import com.dz.foundation.ui.view.recycler.DzRecyclerViewCell
import com.dz.platform.common.toast.ToastManager
import com.dz.support.PiKaChu
import com.gyf.immersionbar.BarHide
import com.sensorsdata.analytics.android.sdk.ScreenAutoTracker
import org.json.JSONObject
import kotlin.math.abs

/**
 *@Author: wanxin
 *@Date: 2022/10/19 17:13
 *@Description: 阅读器目录页面
 *@Version: 1.0
 */
class ReaderCatalogActivity : BaseActivity<ReaderCatalogActivityBinding, ReaderCatalogVM>(),
    ScreenAutoTracker {
    init {
        initReader(PiKaChu.getApplication())
    }

    private var chapterReadingIndex: Int? = 0
    private var chapterReadingId: String? = ""
    private var isFromBookDetail = false

    override fun initImmersionBar() {
        if (!TextUtils.equals(mViewModel.routeIntent?.referrer, ReaderMR.READER) &&
            !TextUtils.equals(mViewModel.routeIntent?.referrer, ReaderMR.TTS_DETAIL)
        ) {
            isFromBookDetail = true
        }
        refreshBarStatus()
    }

    override fun onResume() {
        super.onResume()
        refreshBarStatus()
    }

    private fun refreshBarStatus() {
        //从听书详情进入
        if (TextUtils.equals(
                mViewModel.routeIntent?.referrer,
                ReaderMR.TTS_DETAIL
            ) || TextUtils.equals(mViewModel.routeIntent?.referrer, ReaderMR.AUDIO_BOOK)
        ) {
            getImmersionBar()
                .transparentBar()
                .hideBar(BarHide.FLAG_SHOW_BAR)
                .init()
        } else if (isFromBookDetail) {
            //从详情进入
            getImmersionBar()
                .navigationBarColor(R.color.reader_FFF2F3F6_FF000000)
                .navigationBarDarkIcon(false)
                .statusBarDarkFont(true, 0f)
                .hideBar(BarHide.FLAG_SHOW_BAR)
                .init()
        } else {
            //阅读器进入
            getImmersionBar()
                .transparentBar()
                .init()
            ReaderSettingUtils.applyDecorUi(this, ReaderSettingUtils.HIDE_BAR_FULL_SCREEN, false)
        }
    }

    override fun initStatusComponent(): StatusComponent {
        return mViewBinding.statusCom
    }

    override fun subscribeStatus() {
        mViewModel.setEventCallback(this, object : CatalogVMEventCallback {

            override fun onRequestStart(hasData: Boolean) {
                if (!hasData) {
                    //目录第一次请求，状态组件有背景
                    mViewBinding.statusCom.background = ShapeDrawableUtil.createGradientDrawable(
                        leftTopRadius = 12f.dp,
                        rightTopRadius = 12f.dp,
                        solidColor = ContextCompat.getColor(
                            AppModule.getApplication(),
                            CatalogColorConfig.catalogRootBg
                        )
                    )
                    mViewBinding.ivTopBar.background =
                        ContextCompat.getDrawable(
                            this@ReaderCatalogActivity,
                            CatalogColorConfig.topBarIcon
                        )
                    mViewBinding.statusCom.bindData(Status().setStatus(Status.LOADING))
                }
            }

            override fun onRequestDownloadStart() {
                //点击下载，状态组件无背景
                mViewBinding.ivTopBar.background =
                    ContextCompat.getDrawable(
                        this@ReaderCatalogActivity,
                        CatalogColorConfig.topBarIcon
                    )
                mViewBinding.statusCom.setBackgroundColor(
                    ContextCompat.getColor(
                        AppModule.getApplication(),
                        R.color.common_transparent
                    )
                )
                mViewBinding.statusCom.bindData(Status().setStatus(Status.LOADING))
            }

            override fun onResponse() {
                //关闭状态组件，下载请求章节列表失败也走这里
                mViewBinding.statusCom.bindData(Status().setStatus(Status.DISMISS))
                mViewBinding.ivTopBar.background =
                    ContextCompat.getDrawable(
                        this@ReaderCatalogActivity,
                        CatalogColorConfig.topBarIcon
                    )
            }

            override fun onRequestError(e: RequestException, hasData: Boolean) {
                if (hasData) {
                    ToastManager.showToast(getString(R.string.reader_catalog_net_error))
                } else {
                    mViewBinding.statusCom.background = ShapeDrawableUtil.createGradientDrawable(
                        leftTopRadius = 12f.dp,
                        rightTopRadius = 12f.dp,
                        solidColor = ContextCompat.getColor(
                            AppModule.getApplication(),
                            CatalogColorConfig.catalogRootBg
                        )
                    )
                    mViewBinding.statusCom.bindData(
                        Status().setStatus(Status.NET_ERROR)
                            .setActionTextColor(
                                ContextCompat.getColor(
                                    mViewBinding.statusCom.context,
                                    R.color.common_E1442E
                                )
                            )
                            .setActionBgResource(R.drawable.common_refresh_btn_bg)
                            .setException(e)
                    )
                    mViewBinding.ivTopBar.background =
                        ContextCompat.getDrawable(
                            this@ReaderCatalogActivity,
                            CatalogColorConfig.topBarIcon
                        )
                }
            }

        })
    }

    override fun initData() {
        mViewBinding.viewBg.alpha = 0f
        mViewBinding.rootLayout.translationY = ScreenUtil.getScreenHeight().toFloat()
        mViewBinding.rootLayout.visibility = View.VISIBLE
        mViewBinding.viewBg.animate().alpha(1f).setDuration(400L).start()
        mViewBinding.rootLayout.animate().translationY(0f).setDuration(400L).start()

        //设置颜色风格
        resetColorMode()
        chapterReadingIndex = mViewModel.routeIntent?.chapterIndex
        chapterReadingId = mViewModel.routeIntent?.chapterId
        //隐藏view
        setShowBookInfo(View.INVISIBLE)
        //使用chapterIndex请求
        mViewModel.getRealCatalogInfo(chapterReadingIndex, chapterReadingId)
    }

    override fun initView() {
        mViewBinding.rv.itemAnimator = null
        mViewBinding.scrollBar.setRecyclerView(mViewBinding.rv)
        setActivityTitle("目录")

        //tts详情页进入的，留出底部导航栏的距离
        if (TextUtils.equals(mViewModel.routeIntent?.referrer, ReaderMR.TTS_DETAIL)|| TextUtils.equals(mViewModel.routeIntent?.referrer, ReaderMR.AUDIO_BOOK)) {
            (mViewBinding.llBg.layoutParams as? MarginLayoutParams)?.also {
                it.bottomMargin = ScreenUtil.getNavigationHeight(this)
            }
        }
    }

    override fun subscribeEvent(lifecycleOwner: LifecycleOwner, lifecycleTag: String) {
        super.subscribeEvent(lifecycleOwner, lifecycleTag)
        ReaderME.get().onAudioBookPlayEnd().observe(lifecycleOwner, lifecycleTag) {
            finish()
        }
    }


    @SuppressLint("SetTextI18n")
    override fun subscribeObserver(lifecycleOwner: LifecycleOwner) {
        //书籍信息
        mViewModel.bookInfoData.observe(lifecycleOwner) {
            //判断是否下架
            if (it.canShow()) {
                mViewBinding.bookRemovedCom.visibility = View.GONE
                mViewBinding.ivTopBar.background =
                    ContextCompat.getDrawable(this, CatalogColorConfig.topBarIcon)
            } else {
                mViewBinding.bookRemovedCom.bindData(
                    Status().setStatus(Status.EMPTY)
                        .setDes(getString(R.string.reader_book_remove_des))
                )
                mViewBinding.ivTopBar.background =
                    ContextCompat.getDrawable(this, CatalogColorConfig.topBarIcon)
            }
            mViewBinding.compTop.bindData(
                CatalogTopBean(
                    it.coverWap,
                    it.bookName,
                    it.author,
                    it.bookId,
                    isFromBookDetail
                )
            )
            //总章节数
            mViewBinding.tvTotalChapter.text = "${it.totalChapterNum ?: 0}"
            setShowBookInfo(View.VISIBLE)
        }

        //批量下载更新数据
        mViewModel.updateChapterData.observe(lifecycleOwner) {
            //更新章节下载状态
            if (it.bean != null && it.updatePosition != null) {
                mViewBinding.rv.updateCell(it.updatePosition, it.bean)
            }
            //更新下载进度条
            mViewBinding.loadProgress.setDownloadProgress(it.totalCount, it.downLoadSize)
            if (it.totalCount <= it.downLoadSize) {
                //下载完成
                mViewBinding.llBatchLoad.visibility = View.VISIBLE
                mViewBinding.loadProgress.visibility = View.GONE
                ToastManager.showToast(getString(R.string.reader_download_finish))
            } else {
                //下载中
                mViewBinding.llBatchLoad.visibility = View.GONE
                mViewBinding.loadProgress.visibility = View.VISIBLE
            }
        }

        //目录数据
        mViewModel.catalogLiveData.observe(lifecycleOwner) {
            val currentCells = mViewBinding.rv.allCells
            //排序改变，或者当前章节条数与最新数据不同，重新设置
            if (mViewModel.sortTypeIsChanged || currentCells.size != it.size) {
                mViewBinding.rv.removeAllCells()
                val cellList = mutableListOf<DzRecyclerViewCell<*>>()
                it.forEach { chapterBean ->
                    val cellItem = DzRecyclerViewCell<ChapterItemBean>()
                    cellItem.viewClass = CatalogItemComp::class.java
                    cellItem.viewData = chapterBean
                    cellItem.setActionListener(infoActionListener)
                    cellList.add(cellItem)
                }
                mViewBinding.rv.addCells(cellList)
                if (mViewModel.sortTypeIsChanged) {
                    //排序方式改变直接定位到顶部
                    mViewBinding.rv.scrollToPosition(0)
                } else {
                    (mViewBinding.rv.layoutManager as LinearLayoutManager).scrollToPositionWithOffset(
                        mViewModel.currentPosition ?: 0,
                        0
                    )
                }
                //重新设置总章节数
                mViewBinding.tvTotalChapter.text = "${currentCells.size}"
                mViewModel.sortTypeIsChanged = false
            } else {
                //更新局部
                val maxIndex = mViewBinding.rv.allCells.size - 1
                mViewModel.run {
                    if (sortIsPositiveType) {
                        //正序
                        if (startRefreshIndex < endRefreshIndex) {
                            for (index in startRefreshIndex..endRefreshIndex) {
                                val cell = currentCells[index]
                                cell?.viewData = it[index]
                            }
                            mViewBinding.rv.notifyDataSetChanged()
                        }
                    } else {
                        //倒序
                        var startIndex = maxIndex - endRefreshIndex
                        var endIndex = maxIndex - startRefreshIndex
                        //越界校验
                        startIndex = if (startIndex < 0) 0 else startIndex
                        endIndex = if (endIndex > maxIndex) maxIndex else endIndex

                        if (startIndex < endIndex) {
                            (startIndex..endIndex).forEach { index ->
                                val cell = currentCells[index]
                                cell?.viewData = it[index]
                            }
                            mViewBinding.rv.notifyDataSetChanged()
                        }
                    }
                }
            }
        }

        mViewModel.supportBatchLiveData.observe(lifecycleOwner) {
            if (mViewBinding.loadProgress.isLoading()) {
                //下载中
                return@observe
            }

//            if (it.supportBatch && !it.btnText.isNullOrEmpty()) {
//                //有可下载章节
//                mViewBinding.llBottomBatchLoad.visibility = View.VISIBLE
//                mViewBinding.llBatchLoad.visibility = View.VISIBLE
//                mViewBinding.tvBatchLoad.text = it.btnText
//                mViewBinding.loadProgress.visibility = View.GONE
//            } else {
//                mViewBinding.llBottomBatchLoad.visibility = View.GONE
//            }

        }
    }

    @SuppressLint("SetTextI18n")
    override fun initListener() {
        //退出目录
        mViewBinding.llBg.registerClickAction {
            finish()
        }
        mViewBinding.llTopBar.registerClickAction {
            finish()
        }

        mViewBinding.scrollBar.setStopScrollListener {
            //滚动停止后发起请求
            whenScrollEndRequestData()
        }

        //点击批量下载
        mViewBinding.llBatchLoad.registerClickAction {
//            refreshBarStatus()
            if (mViewBinding.loadProgress.isLoading()) {
                //下载中
                return@registerClickAction
            }

            //开始下载
            val requestChapter = chapterReadingId ?: mViewModel.getFirstChapterId()
            if (!requestChapter.isNullOrEmpty()) {
                mViewModel.getLoadCatalogInfo(
                    chapterReadingId ?: mViewModel.getFirstChapterId() ?: ""
                )
            }
            //触发自动加书架
            mViewModel.checkAutoAddShelf()
        }

        //点击排序
        mViewBinding.llSort.registerClickAction {
            if (!NetWorkUtil.isNetConnected(this)) {
                ToastManager.showToast("当前网络欠佳，请检查网络设置")
                return@registerClickAction
            }
            mViewModel.changeSortType()
            mViewBinding.tvSortName.text = getSortName()
            mViewBinding.ivSort.background = ContextCompat.getDrawable(this, getSortIcon())
//            mViewBinding.tvSort.text = if (mViewModel.sortIsPositiveType) "倒序" else "正序"
//            mViewBinding.tvSort.setCompoundDrawablesWithIntrinsicBounds(
//                null,
//                ContextCompat.getDrawable(this, getSortIcon()),
//                null,
//                null
//            )

            //根据请求最顶端
            val chapterIndex = if (mViewModel.sortIsPositiveType) {
                0
            } else {
                mViewBinding.rv.adapter!!.itemCount - 1
            }
            mViewModel.getRealCatalogInfo(chapterIndex, null)
        }


    }

    private fun setShowBookInfo(visibility: Int) {
        mViewBinding.run {
            compTop.visibility = visibility
            llTotalChapter.visibility = visibility
            ivSort.visibility = visibility
        }
    }

    /**
     * 请求当前位置目录数据
     */
    private fun whenScrollEndRequestData() {
        val startIndex = mViewBinding.rv.firstVisibleItemPosition
        val endIndex = mViewBinding.rv.lastVisibleItemPosition
        var chapterIndex = startIndex + (endIndex - startIndex) / 2
        //倒序时改变请求位置
        if (!mViewModel.sortIsPositiveType) {
            chapterIndex = mViewBinding.rv.adapter!!.itemCount - chapterIndex
        }
        //滚动偏移20条才发起请求
        if (abs(mViewModel.requestChapterLastIndex - chapterIndex) > 20) {
            //滑动停止使用index请求
            mViewModel.getRealCatalogInfo(chapterIndex, null)
        }
    }

    private var canFinish = false
    override fun finish() {
        mViewModel.needCancelDownLoad = true
        if (!canFinish) {
            exitCatalogAnimator {
                canFinish = true
                finish()
            }
        } else {
            super.finish()
        }

    }

//    override fun enterAnim(intent: Intent?) {
//        overridePendingTransition(R.anim.common_ac_none, R.anim.common_ac_none)
//    }

    override fun exitAnim() {
        overridePendingTransition(R.anim.common_ac_none, R.anim.common_ac_none)
    }

    private val infoActionListener = object : CatalogItemComp.ViewActionListener {
        override fun onClickChapter(readChapterId: String) {
            if (TextUtils.equals(mViewModel.routeIntent?.referrer, ReaderMR.TTS_DETAIL)||
                TextUtils.equals(mViewModel.routeIntent?.referrer, ReaderMR.AUDIO_BOOK)
                ) {
                exitCatalogAnimator {
                    ReaderME.get().onChapterChange().post(readChapterId)
                    canFinish = true
                    finish()
                }
            } else if (isFromBookDetail) {
//                toReader(readChapterId)
                exitCatalogAnimator {
                    toReader(readChapterId)
                    canFinish = true
                    finish()
                }

            } else {
                exitCatalogAnimator {
                    toReader(readChapterId)
                }
            }
        }
    }

    private fun toReader(readChapterId: String) {
        LogUtil.d("打印", "暂停播放：TtsPlayer.instance.isPlaying()  toReader")
        ReaderMR.get().reader().apply {
            bookId = mViewModel.routeIntent?.bookId!!
            chapterId = readChapterId
            routeSource = mViewModel.routeIntent?.routeSource
            referrer = mViewModel.routeIntent?.referrer
            if (TextUtils.equals(mViewModel.routeIntent?.referrer, ReaderMR.READER)) {
                intentFlags =
                    Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP
            }
        }.start()
    }

    /**
     * 设置颜色风格
     */
    private fun resetColorMode() {
        if (isFromBookDetail) {
            setColorMode()
        } else {
            if (ReaderConfigUtil.isNightMode()) {
                setNightMode()
            } else {
                setColorMode()
            }
        }

        setViewColor()
    }

    private fun setViewColor() {
        if (isFromBookDetail) {
            mViewBinding.scrollBar.setBarIsNightStyle(false)
        } else {
            mViewBinding.scrollBar.setBarIsNightStyle(ReaderConfigUtil.isNightMode())
        }
        //目录背景
        mViewBinding.rootLayout.background = ShapeDrawableUtil.createGradientDrawable(
            solidColor = ContextCompat.getColor(this, CatalogColorConfig.catalogRootBg),
            leftTopRadius = 12f.dp,
            rightTopRadius = 12f.dp,
        )
        mViewBinding.statusCom.background = ShapeDrawableUtil.createGradientDrawable(
            solidColor = ContextCompat.getColor(this, CatalogColorConfig.catalogRootBg),
            leftTopRadius = 12f.dp,
            rightTopRadius = 12f.dp,
        )
        mViewBinding.bookRemovedCom.background = ShapeDrawableUtil.createGradientDrawable(
            solidColor = ContextCompat.getColor(this, CatalogColorConfig.catalogRootBg),
            leftTopRadius = 12f.dp,
            rightTopRadius = 12f.dp,
        )
        //分割线
        mViewBinding.divider.setBackgroundColor(
            ContextCompat.getColor(
                this,
                CatalogColorConfig.dividerColor
            )
        )
        //章节列表背景
        mViewBinding.rv.setBackgroundColor(
            ContextCompat.getColor(
                this,
                CatalogColorConfig.chapterListBg
            )
        )
        //目录通用文字文字颜色
        mViewBinding.compTop.resetColor()

        mViewBinding.tvGong.setTextColor(
            ContextCompat.getColor(
                this,
                CatalogColorConfig.authorTextColor
            )
        )
        mViewBinding.tvTotalChapter.setTextColor(
            ContextCompat.getColor(
                this,
                CatalogColorConfig.authorTextColor
            )
        )
        mViewBinding.tvZhang.setTextColor(
            ContextCompat.getColor(
                this,
                CatalogColorConfig.authorTextColor
            )
        )


        mViewBinding.tvBatchLoad.setTextColor(
            ContextCompat.getColor(
                this,
                CatalogColorConfig.batchLoadTextColor
            )
        )

        //图片
        mViewBinding.ivSort.background = ContextCompat.getDrawable(this, getSortIcon())

        mViewBinding.tvSortName.setTextColor(
            ContextCompat.getColor(
                this,
                CatalogColorConfig.chapterUnloadTextColor
            )
        )


        mViewBinding.ivTopBar.background =
            ContextCompat.getDrawable(this, CatalogColorConfig.topBarIcon)

        mViewBinding.llBatchLoad.background = ShapeDrawableUtil.createGradientDrawable(
            solidColor = ContextCompat.getColor(this, CatalogColorConfig.batchLoadBtnBg),
            radius = 18f.dp
        )
        mViewBinding.tvBatchLoad.setCompoundDrawablesWithIntrinsicBounds(
            ContextCompat.getDrawable(this, CatalogColorConfig.batchLoadIcon),
            null,
            null,
            null
        )
    }

    private fun setNightMode() {
        CatalogColorConfig.run {
            //目录背景
            catalogRootBg = R.color.reader_catalog_night_bg_root
            //章节列表背景
            chapterListBg = R.color.reader_catalog_night_bg_list
            //书名
            bookNameTextColor = R.color.reader_catalog_night_book_name_text
            //作者
            authorTextColor = R.color.reader_catalog_night_author_text
            //分割线
            dividerColor = R.color.reader_catalog_night_divider_color

            //正在阅读的章节文字颜色
            chapterReadingTextColor = R.color.reader_catalog_night_chapter_reading_text
            //已经加载的章节文字颜色
            chapterLoadedTextColor = R.color.reader_catalog_night_chapter_loaded_text
            //未加载的章节文字颜色
            chapterUnloadTextColor = R.color.reader_catalog_night_chapter_unload_text
            //下载按钮背景
            batchLoadBtnBg = R.color.reader_catalog_night_batch_btn_bg
            //下载按钮文字颜色
            batchLoadTextColor = R.color.reader_catalog_download_text_color

            goDetailArrow = R.drawable.reader_ic_arrow_right2_night
            lockIcon = R.drawable.reader_ic_lock_night
            topBarIcon = R.drawable.reader_ic_catalog_bar_night
            batchLoadIcon = R.drawable.reader_ic_download_night
        }
    }

    private fun setColorMode() {
        CatalogColorConfig.run {
            //目录背景
            catalogRootBg = R.color.reader_catalog_bg_root_detail
            //章节列表背景
            chapterListBg = R.color.reader_catalog_bg_list_detail
            //书名
            bookNameTextColor = R.color.reader_catalog_book_name_text
            //作者
            authorTextColor = R.color.reader_catalog_author_text
            //分割线
            dividerColor = R.color.reader_catalog_divider_color
            //正在阅读的章节文字颜色
            chapterReadingTextColor = R.color.reader_catalog_chapter_reading_text
            //已经加载的章节文字颜色
            chapterLoadedTextColor = R.color.reader_catalog_chapter_loaded_text
            //未加载的章节文字颜色
            chapterUnloadTextColor = R.color.reader_catalog_chapter_unload_text
            //下载按钮背景
            batchLoadBtnBg = R.color.reader_catalog_batch_btn_bg
            //下载按钮文字颜色
            batchLoadTextColor = R.color.reader_catalog_download_text_color
            goDetailArrow = R.drawable.reader_ic_arrow_right2
            lockIcon = R.drawable.reader_ic_lock
            topBarIcon = R.drawable.reader_ic_catalog_bar
            batchLoadIcon = R.drawable.reader_ic_download
        }


    }

    private fun getSortIcon(): Int {
        return if (mViewModel.sortIsPositiveType) {
            //是正序情况
            if (isFromBookDetail) {
                getReverseIcon(false)
            } else {
                getReverseIcon(ReaderConfigUtil.isNightMode())
            }
        } else {
            //是倒序情况
            if (isFromBookDetail) {
                getPositiveSequenceIcon(false)
            } else {
                getPositiveSequenceIcon(ReaderConfigUtil.isNightMode())
            }
        }
    }

    private fun getSortName(): String {
        return if (mViewModel.sortIsPositiveType) {
            //是正序情况
            return getString(R.string.reader_sort_reverse)
        } else {
            //是倒序情况
            return getString(R.string.reader_sort_positive)
        }
    }

    //根据亮暗获取倒序icon
    private fun getReverseIcon(isDark: Boolean): Int {
        return if (isDark) {
            R.drawable.reader_ic_sort_d_night
        } else {
            R.drawable.reader_ic_sort_d
        }
    }

    //根据亮暗获取正序icon
    private fun getPositiveSequenceIcon(isDark: Boolean): Int {
        return if (isDark) {
            R.drawable.reader_ic_sort_z_night
        } else {
            R.drawable.reader_ic_sort_z
        }
    }


    private fun exitCatalogAnimator(endBlock: () -> Unit) {
        mViewBinding.viewBg.animate().alpha(0f).setDuration(400L).start()
        mViewBinding.rootLayout.animate()
            .translationY(
                ScreenUtil.getScreenHeight().toFloat() + ScreenUtil.getNavigationHeight(
                    this
                )
            ).setDuration(400L)
            .setListener(object : Animator.AnimatorListener {
                override fun onAnimationStart(animation: Animator) {

                }

                override fun onAnimationEnd(animation: Animator) {
                    endBlock()
                }

                override fun onAnimationCancel(animation: Animator) {
                }

                override fun onAnimationRepeat(animation: Animator) {
                }

            }).start()
    }

    override fun onDestroy() {
        super.onDestroy()
        if (mViewBinding.loadProgress.isLoading()) {
            if (mViewModel.downLoadSuccessSize > 0) {
                ToastManager.showToast(
                    getString(
                        R.string.reader_download_num,
                        mViewModel.downLoadSuccessSize.toString()
                    )
                )
            } else {
                ToastManager.showToast(getString(R.string.reader_download_stop))
            }
        }
    }

    override fun getPageName(): String {
        return "目录"
    }

    override fun getTrackProperties(): JSONObject {
        val jsonObject = super.getTrackProperties()
        jsonObject.put("PositionName", if (isFromBookDetail) "书籍详情" else "阅读器")

        try {

            jsonObject.put("ChaptersID", mViewModel.routeIntent?.chapterId)
            mViewModel.routeIntent?.chapterIndex?.let { index ->
                jsonObject.put("ChaptersNum", index + 1)
            }

            jsonObject.put("ChaptersName", mViewModel.routeIntent?.chapterName)

            var bookId = mViewModel.routeIntent?.bookId

            var bookName = mViewModel.routeIntent?.bookName

            mViewModel.getSourceNode()?.let {

                jsonObject.put("Origin", it.origin)
                jsonObject.put("ColumnName", it.columnName)

                if (bookName.isNullOrEmpty()) {
                    bookName = it.contentName
                }

                if (bookId.isNullOrEmpty()) {
                    bookId = it.contentId
                }


            }

            jsonObject.put("BookID", bookId)
            jsonObject.put("BookName", bookName)

        } catch (e: Throwable) {

        }

        return jsonObject
    }
}