package com.dz.business.reader.data

import com.dz.business.base.`data`.bean.BaseBean
import com.dz.business.base.data.bean.BaseOperationBean
import com.dz.business.base.data.bean.UserTacticInfoBean

/**
 *@Author: shidz
 *@Date: 2022/11/15 0:52
 *@Description: 批量订购模型
 *@Version:1.0
 */
data class BatchOrderBean(
    var status: Int? = null,
    var msg: String? = null,
    var allZcList: List<RechargePayWayBean?>? = null,
    var batchOrderGearInfo: List<BatchOrderGear?>? = null,
    var remainAmount: String? = null,
    var remainAmountText: String? = null,
    var showAgreement: Int? = null,
    var checkAgreement: Int? = null,
    var showZffs: Int? = null,
    var chapterIndex: String? = null,
    var startChapterId: String? = null,//购买起始章节id
    var subtitle: String? = null,
    var title: String? = null,
    var title2: String? = null,
    var pop: Int? = null,
    var buttonTxt: String? = null,//按钮显示方案，当所有档位都置灰或超V非免费书等
    var explainTxt: String? = null,//档位上方的说明方案，当所有档位都置灰或超V非免费书等，显示方案
    var operateId: String? = null,//运营位id
    var titleTips: String? = null,//标题下提示语
    var remainTxt: String? = null,//账户余额文案
    var payAmountTxt: String? = null,//支付金额文案
    var amountUnit: String? = null,//金额单位
    var userTacticInfo: UserTacticInfoBean? = null,
    var chapterUnit: String? = null,//章或者集
    var bookType:String?=null,//书籍类型  1 文本  2 音频书籍
    var bookTypeOne: String?,//书籍一级分类   //书籍一级分类  1出版  2 男频  3 女频
    var vipTipVo:BaseOperationBean?=null

) : BaseBean() {

    //书籍来源 本地流转，非服务下发
    var source: String? = null
    var bookId: String? = null
    var bookName: String? = null
    var chapterId: String? = null
    fun isSuccess(): Boolean {
        return status == 1
    }

    fun isValidBatchOrderGear(): Boolean {
        if (batchOrderGearInfo.isNullOrEmpty()) {
            return false
        }
        val mValidList = batchOrderGearInfo!!.filter {
            it?.valid == 1
        }
        return mValidList.isNotEmpty()
    }
}

data class BatchOrderGear(
    var amount: Int? = null,
    var amountText: String? = null,
    var batchNum: Int? = null,
    var buttonText: String? = null,
    var cornerMark: String? = null,
    var price: String? = null,
    var id: String? = null,
    var isEnough: Int? = null,
    var valid: Int? = 1,//档位是否有效，0-否，1-是
    var verifyParam: String? = null,
    var userTacticInfo: UserTacticInfoBean? = null
) : BaseBean() {
    var isSelected: Boolean = false
    var chapterUnit:String?=null//章或者集
}