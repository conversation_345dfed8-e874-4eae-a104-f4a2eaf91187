package com.dz.business.reader.data

import com.dz.business.base.data.bean.BaseBean

/**
 * <AUTHOR>
 * @description:
 * @date :2022/10/27 15:18
 */
data class RechargePayWayBean(
    var check: Int? = 0,//是否勾选，0-否，1-是
    /**
     * 支付类型，
     * dd100-WECHAT_MOBILE_PAY（微信SDK），
     * dd200-WECHAT_WAP_PAY（现在支付），
     * dd300-ALIPAY_MOBILE_PAY（支付宝SDK），
     * dd400-ALIPAY_WEB_PAY（支付宝WEB支付），
     * dd500-IOS_IAP_PAY（苹果内购）【命名规避苹果审核】
     */
    var descId: String,
    var icon: String,//支付方式图标地址
    var title: String//充值类型标题，如：微信支付、支付宝、苹果内购
) : BaseBean() {
    /**
     * 该支付方式是否选中
     */
    var isSelected: Boolean = false

    /**
     * 主要用于批量下载、批量解锁
     */
    var isValid: Boolean = true

    /**
     * 该支付方式是否支持已选择的充值档位
     */
    var isSupportSelectedMoney: Boolean = false

    var mPosition: Int = 0

}
