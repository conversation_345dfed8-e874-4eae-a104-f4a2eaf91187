package com.dz.business.reader.ui.component.order

import android.content.Context
import android.text.TextUtils
import android.util.AttributeSet
import com.dz.business.base.recharge.RechargeMS
import com.dz.business.reader.data.RechargePayWayBean
import com.dz.business.reader.data.PayWayBean
import com.dz.business.reader.databinding.ReaderPayWayCompBinding
import com.dz.foundation.ui.view.custom.ActionListener
import com.dz.foundation.ui.view.custom.ActionListenerOwner
import com.dz.foundation.ui.view.recycler.DzRecyclerViewCell
import com.dz.platform.common.base.ui.component.UIConstraintComponent

/**
 *@Author: shidz
 *@Date: 2022/11/11 17:10
 *@Description: 支付方式列表组件
 *@Version:1.0
 */
class RechargePayWayComp :
    UIConstraintComponent<ReaderPayWayCompBinding, PayWayBean>,
    ActionListenerOwner<RechargePayWayCompActionListener> {

    @JvmOverloads
    constructor(
        context: Context,
        attrs: AttributeSet? = null,
        defStyleAttr: Int = 0
    ) : super(context, attrs, defStyleAttr)

    override var mActionListener: RechargePayWayCompActionListener? = null

    override fun initData() {

    }

    override fun initView() {

    }

    override fun initListener() {

    }

    override fun bindData(data: PayWayBean?) {
        super.bindData(data)
        data?.let { setViewData(it) }

    }

    private fun setViewData(payWayBean: PayWayBean) {

        payWayBean.payWayItemList?.let { setPayWayList(it) }


    }

    private fun setPayWayList(list: List<RechargePayWayBean?>) {
        val cellList = mutableListOf<DzRecyclerViewCell<RechargePayWayBean>>()

        //获取上次支付方式
        val lastPayWay = RechargeMS.get()?.getLastPaySuccessPayWay()
        var defaultSelectIndex = 0
        list.forEachIndexed { index, rechargePayWayBean ->

            val cellItem = rechargePayWayBean?.run {
                if ((TextUtils.equals(lastPayWay, descId))) {
                    defaultSelectIndex = index
                }
                rechargePayWayBean.isValid = mData?.isValid ==true
                createPayWayItemCell(rechargePayWayBean)
            }
            if (cellItem != null) {
                cellList.add(cellItem)
            }
        }
        list[defaultSelectIndex]?.let { defaultSelectItem ->
            if (!defaultSelectItem.isSelected) {
                defaultSelectItem.isSelected = true
                onPayWayItemChecked(defaultSelectItem)
            }
        }
        mViewBinding.rvPayWay.addCells(cellList)

    }

    private fun onPayWayItemChecked(rechargePayWayBean: RechargePayWayBean) {
        mActionListener?.onPayWayChecked(rechargePayWayBean)
    }

    private fun createPayWayItemCell(
        item: RechargePayWayBean
    ): DzRecyclerViewCell<RechargePayWayBean> {
        return DzRecyclerViewCell<RechargePayWayBean>().apply {
            viewClass = RechargePayWayItemComp::class.java
            viewData = item
            setActionListener(payWayItemActionListener)
        }
    }

    private val payWayItemActionListener =
        object : RechargePayWayItemComp.PayWayItemActionListener {
            override fun onPayWayClick(position: Int, bean: RechargePayWayBean) {
                setPayItemSelected(position, bean)
            }


        }

    private fun setPayItemSelected(position: Int, bean: RechargePayWayBean) {
        if (bean.isSelected) {
            return
        }
        val range = mViewBinding.rvPayWay.allCells.indices
        for (i in range) {
            (mViewBinding.rvPayWay.getCell(i).viewData as RechargePayWayBean).isSelected =
                i == position
        }
        mViewBinding.rvPayWay.notifyDataSetChanged()
        onPayWayItemChecked(bean)
    }
}

interface RechargePayWayCompActionListener : ActionListener {
    fun onPayWayChecked(bean: RechargePayWayBean)
}