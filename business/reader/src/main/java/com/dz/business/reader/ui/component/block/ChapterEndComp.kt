package com.dz.business.reader.ui.component.block

import android.content.Context
import android.text.TextUtils
import android.util.AttributeSet
import androidx.lifecycle.LifecycleOwner
import com.dz.business.reader.ReaderInsideEvents
import com.dz.business.reader.data.ChapterOpenBean
import com.dz.business.reader.data.RecommendBookInfo
import com.dz.business.reader.data.ScoreBannerInfo
import com.dz.business.reader.data.TextActionAct
import com.dz.business.reader.databinding.ReaderChapterEndCompBinding
import com.dz.business.reader.ui.page.ReaderActivity
import com.dz.foundation.base.utils.LogUtil
import com.dz.foundation.ui.widget.getContainerActivity
import com.dz.platform.common.base.ui.component.UIConstraintComponent
import reader.xo.block.Block
import reader.xo.config.ColorStyle
import reader.xo.config.LayoutStyle


/**
 *@Author: shidz
 *@Date: 2022/11/15 12:36
 *@Description:章末推荐 页面
 *@Version:1.0
 */
class ChapterEndComp @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : UIConstraintComponent<ReaderChapterEndCompBinding, ChapterOpenBean>(
    context,
    attrs,
    defStyleAttr
), ReaderBlockComp {
    companion object {
        private val keySet = mutableSetOf<String>()
        fun hasMarketingShow(key: String): Boolean {
            LogUtil.d("ChapterEndComp", "hasMarketingShow key=" + key)
            return keySet.contains(key)
        }

        fun addKey(key: String) {
            LogUtil.d("ChapterEndComp", "addKey key=" + key)
            keySet.add(key)
        }
    }

    override fun initData() {

    }

    override fun initView() {

    }

    override fun initListener() {

    }

    private var blockHeight = 0f
    override fun bindViewData(fid: String, block: Block) {
        block.tag?.run {
            if (this is ChapterOpenBean) {
                blockHeight = block.height + 0F
                bindData(this)
                checkRealShow()
            }

        }

    }

    /**
     * 触发是否触达到了章末，解决翻到章末后，运营位才返回的情况
     */
    private fun checkRealShow() {
        val containerActivity = getContainerActivity()
        if (containerActivity is ReaderActivity) {
            containerActivity.checkChapterEndShow()
        }

    }

    override fun subscribeEvent(lifecycleOwner: LifecycleOwner, lifecycleTag: String) {
        ReaderInsideEvents.get().onChapterEndShow()
            .observe(lifecycleOwner, lifecycleTag) { chapterId ->
                //章末显示
                if (TextUtils.equals(mData?.currentChatperId, chapterId)) {
                    val dataId = mData?.dataId.toString()
                    if (!hasMarketingShow(dataId)) {
                        addKey(dataId)
                        onChapterEndShow()
                        LogUtil.d("ChapterEndComp", "onChapterEndShow key=" + dataId)
                    }
                }
            }
    }

    private fun onChapterEndShow() {

        if (mViewBinding.compBook.visibility == VISIBLE) {
            mViewBinding.compBook.onBlockShow()
        }

        if (mViewBinding.compTextLink.visibility == VISIBLE) {
            mViewBinding.compTextLink.onBlockShow()
        }

        if (mViewBinding.compScore.visibility == VISIBLE) {
            mViewBinding.compScore.onBlockShow()
        }
    }

    override fun setFontSize(fontSize: Int) {
    }

    override fun setColorStyle(colorStyle: ColorStyle) {
    }

    override fun setLayoutStyle(layoutStyle: LayoutStyle) {
    }

    override fun bindData(data: ChapterOpenBean?) {
        super.bindData(data)
        data?.let { setViewData(it) }
    }

    private fun setViewData(readerEndData: ChapterOpenBean) {
        mViewBinding.compBook.visibility = GONE
        mViewBinding.compScore.visibility = GONE
        mViewBinding.compTextLink.visibility = GONE
        var remainHeight = blockHeight


        //根据高度判断能展示几个组件
        if (readerEndData.scoreBannerInfo != null) {
            val viewHeight = readerEndData.scoreBannerInfo!!.viewHeight
            if (remainHeight >= viewHeight) {
                val scoreBean = readerEndData.scoreBannerInfo!!.apply {
                    bookId = readerEndData.bookId
                    chapterId = readerEndData.currentChatperId
                    bookName = readerEndData.bookName
                }
                showScoreComp(scoreBean)
                return
            }

        }

        if (readerEndData.recommendBookInfo != null) {
            val viewHeight = readerEndData.recommendBookInfo!!.viewHeight
            if (remainHeight >= viewHeight) {
                showBookComp(readerEndData.recommendBookInfo!!.apply {
                    currentBookId = readerEndData.bookId
                    currentBookName = readerEndData.bookName
                })
                return
            }

        }
        if (readerEndData.textActionAct != null) {
            val viewHeight = readerEndData.textActionAct!!.viewHeight
            if (remainHeight >= viewHeight) {
                showTextLinkComp(readerEndData.textActionAct!!.apply {
                    bookId = readerEndData.bookId
                    bookName = readerEndData.bookName
                })
                return
            }

        }
    }

    private fun showBookComp(recommendBookInfo: RecommendBookInfo) {
        mViewBinding.compBook.visibility = VISIBLE
        mViewBinding.compBook.bindData(recommendBookInfo)

    }

    private fun showTextLinkComp(textActionAct: TextActionAct) {
        mViewBinding.compTextLink.visibility = VISIBLE
        mViewBinding.compTextLink.bindData(textActionAct)

    }

    private fun showScoreComp(scoreBannerInfo: ScoreBannerInfo) {
        mViewBinding.compScore.visibility = VISIBLE
        mViewBinding.compScore.bindData(scoreBannerInfo)
    }

    interface ChapterEndItem {
        fun onBlockShow()
    }
}