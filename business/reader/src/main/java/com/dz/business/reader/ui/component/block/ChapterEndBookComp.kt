package com.dz.business.reader.ui.component.block

import android.content.Context
import android.util.AttributeSet
import androidx.lifecycle.LifecycleOwner
import com.dz.business.base.bookdetail.BookDetailMR
import com.dz.business.base.main.MainMS
import com.dz.business.base.reader.ReaderMR
import com.dz.business.reader.R
import com.dz.business.reader.ReaderInsideEvents
import com.dz.business.reader.data.RecommendBookInfo
import com.dz.business.reader.databinding.ReaderChapterEndBookCompBinding
import com.dz.business.reader.utils.BookNameTitleUtil
import com.dz.business.reader.utils.ReaderConfigUtil
import com.dz.business.track.events.DzTrackEvents
import com.dz.business.track.events.sensor.PositionActionTE
import com.dz.foundation.base.utils.dp
import com.dz.foundation.imageloader.loadRoundImg
import com.dz.platform.common.base.ui.component.UIConstraintComponent


/**
 *@Author: shidz
 *@Date: 2022/11/17 2:21
 *@Description: 章末推荐书籍
 *@Version:1.0
 */
class ChapterEndBookComp @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : UIConstraintComponent<ReaderChapterEndBookCompBinding, RecommendBookInfo>(
    context,
    attrs,
    defStyleAttr
), ChapterEndComp.ChapterEndItem {
    override fun initData() {

    }

    override fun initView() {

    }

    override fun initListener() {
        mViewBinding.clRoot.registerClickAction {
            if (mData != null) {
                doLog(PositionActionTE.ACTION_CLICK)
                BookDetailMR.get().bookDetail().apply {
                    bookId = mData!!.bookId
                }.start()

            }
        }
    }

    override fun bindData(data: RecommendBookInfo?) {
        super.bindData(data)
        data?.let { setViewData(it) }
    }

    override fun subscribeEvent(lifecycleOwner: LifecycleOwner, lifecycleTag: String) {
        ReaderInsideEvents.get().colorStyleChanged().observe(lifecycleOwner, lifecycleTag) {
            setViewColor()
        }
    }

    private fun setViewData(info: RecommendBookInfo) {

        BookNameTitleUtil.setTitleStr(info.currentBookName?:"",info.titlePlaceHolder,info.title?:"",mViewBinding.tvTitle)
        mViewBinding.ivBookCover.loadRoundImg(info.coverWap, 4.dp, width = 65, height = 86)
        mViewBinding.tvBookName.text = info.bookName
        mViewBinding.tvDes.text = info.introduction
        mViewBinding.tfTag.bindData(info.tags)
        setViewColor()
    }

    private fun setViewColor() {
        if (ReaderConfigUtil.isNightMode()) {
            mViewBinding.clRoot.setShapeBackground(
                solidColor = getColor(R.color.reader_color_242424),
                radius = 8f.dp
            )
            mViewBinding.tvTitle.setTextColor(getColor(R.color.reader_color_60_ffffff))
            mViewBinding.tvBookName.setTextColor(getColor(R.color.reader_color_FFD0D0D0))
            mViewBinding.tvDes.setTextColor(getColor(R.color.reader_color_8A8A8A))
        } else {
            mViewBinding.clRoot.setShapeBackground(
                solidColor = getColor(R.color.reader_color_30_ffffff),
                radius = 8f.dp
            )
            mViewBinding.tvTitle.setTextColor(getColor(R.color.reader_color_60_000000))
            mViewBinding.tvBookName.setTextColor(getColor(R.color.reader_color_FF222222))
            mViewBinding.tvDes.setTextColor(getColor(R.color.reader_color_60_000000))
        }
    }

    private fun doLog(action: Int) {
        //神策打点
        doSensorTrack(action)
        //服务端点击上报点
        doServerUpload(action)
        //HIVE打点
        doHiveTrack(action)
    }

    /**
     * Hive
     */
    private fun doHiveTrack(actionType: Int) {
//        mData?.run {
//
//            val sourceNode = SourceNode().apply {
//                origin = SourceNode.origin_ydq
//                channelId = SourceNode.MODULE_YDQ_ZMTJ
//                channelName = "章末推荐"
//                columnId = currentBookId ?: ""
//                columnName = currentBookName ?: ""
//                contentId = bookId ?: ""
//                contentName = bookName ?: ""
//                contentType = BookDetailMR.BOOK_DETAIL//条目跳转类型 路由 action
//                logId = bigDataDotInfoVo?.logId ?: ""//请求ID
//                strategyId = bigDataDotInfoVo?.strategyId ?: ""//大数据分组ID
//                strategyName = bigDataDotInfoVo?.strategyName ?: ""//大数据分组名称
//            }
//
//            val trackEvents = DzTrackEvents.get()
//                .hiveExposure()
//            if (actionType == PositionActionTE.ACTION_CLICK) {
//                trackEvents.click(sourceNode)
//            } else {
//                trackEvents.show(sourceNode)
//            }
//            trackEvents.track()
//        }
    }

    private fun doServerUpload(action: Int) {
        mData?.run {
            if (action == PositionActionTE.ACTION_CLICK) {
                MainMS.get()?.activityReportEvent(id ?: "", "", 0)
            } else {
                MainMS.get()?.activityReportEvent(id ?: "", "", 1)
            }
        }
    }

    private fun doSensorTrack(action: Int) {
        mData?.run {
            DzTrackEvents.get()
                .positionAction()
                .action(action)
                .bookId(currentBookId)
                .bookName(currentBookName)
                .activityId("")
                .oTypeId(id)
                .userTacticInfo(userTacticInfo)
                .title(title)
                .contentId(bookId)
                .contentName(bookName)
                .contentType(ReaderMR.READER)
                .track()
        }

    }

    override fun onBlockShow() {
        doLog(PositionActionTE.ACTION_SHOW)
    }
}