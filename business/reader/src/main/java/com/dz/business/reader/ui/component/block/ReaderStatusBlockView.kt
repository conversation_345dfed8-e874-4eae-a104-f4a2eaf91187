package com.dz.business.reader.ui.component.block

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.view.ViewGroup
import androidx.constraintlayout.widget.ConstraintLayout
import reader.xo.base.PageInfo
import reader.xo.block.StatusBlockView
import reader.xo.config.ColorStyle

class ReaderStatusBlockView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null
) : StatusBlockView(context, attrs) {

    override fun setColorStyle(colorStyle: ColorStyle) {
        mBlockComp?.setColorStyle(colorStyle)
    }


    override fun setStatusInfo(page: PageInfo?, isVertical: Boolean) {
        super.setStatusInfo(page, isVertical)
        mBlockComp?.setStatusInfo(page, isVertical)
    }

    private var mBlockComp: IReaderStatusBlock? = null
    fun addBlock(blockComp: IReaderStatusBlock): ReaderStatusBlockView {
        mBlockComp = blockComp
        if (blockComp is View) {
            if (blockComp.parent != null) {
                (blockComp.parent as ViewGroup).removeView(blockComp)
            }
            val layoutParams = ConstraintLayout.LayoutParams(
                ConstraintLayout.LayoutParams.MATCH_PARENT,
                ConstraintLayout.LayoutParams.WRAP_CONTENT
            )
            addView(blockComp, layoutParams)
        }
        return this
    }
}