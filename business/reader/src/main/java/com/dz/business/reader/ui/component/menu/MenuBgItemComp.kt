package com.dz.business.reader.ui.component.menu

import android.content.Context
import android.util.AttributeSet
import android.view.View
import androidx.recyclerview.widget.RecyclerView
import com.dz.business.reader.R
import com.dz.business.reader.databinding.ReaderMenuBgItemCompBinding
import com.dz.business.reader.utils.ReaderConfigUtil
import com.dz.business.track.trackProperties
import com.dz.foundation.base.utils.ScreenUtil
import com.dz.foundation.base.utils.dp
import com.dz.foundation.ui.view.custom.ActionListener
import com.dz.foundation.ui.view.custom.ActionListenerOwner
import com.dz.foundation.ui.view.recycler.DzRecyclerView
import com.dz.platform.common.base.ui.component.UIFrameComponent

/**
 *@Author: shidz
 *@Date: 2022/10/9 23:22
 *@Description: 菜单选项组件
 *@Version:1.0
 */
class MenuBgItemComp @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : UIFrameComponent<ReaderMenuBgItemCompBinding, BgItemBean>(context, attrs, defStyleAttr),
    ActionListenerOwner<MenuBgItemComp.ViewActionListener> {
    override var mActionListener: ViewActionListener? = null

    override fun initData() {

    }

    override fun initView() {

    }

    override fun onCreateRecyclerViewItem(
        parent: DzRecyclerView?,
        itemView: View?
    ): RecyclerView.LayoutParams {
        return RecyclerView.LayoutParams(
            ScreenUtil.dip2px(context, 58), ScreenUtil.dip2px(context, 33)
        )
    }

    override fun initListener() {
        registerClickAction {
            mData?.let {
                if (it.checked) {
                    return@registerClickAction
                }
                mActionListener?.onItemChecked(it)
                it.checked = true
                setCheckedStatus(it)
            }
        }
    }

    override fun bindData(data: BgItemBean?) {
        super.bindData(data)
        data?.let {
            mViewBinding.vBgColor.setShapeBackground(
                solidColor = it.bgColor,
                radius = 16.dp.toFloat()
            )
            when (it.index) {
                4 -> {
                    if (ReaderConfigUtil.isNightMode())
                        mViewBinding.ivCheckMark.setImageResource(R.drawable.reader_ic_mode_night)
                    else
                        mViewBinding.ivCheckMark.setImageResource(R.drawable.reader_ic_mode_day)
                    this.trackProperties(elementContent = "阅读背景:夜间")
                }

                else -> {
                    mViewBinding.ivCheckMark.setImageResource(R.drawable.reader_ic_selected)
                    this.trackProperties(elementContent = "阅读背景:#${Integer.toHexString(it.bgColor)}")
                }
            }
            setCheckedStatus(it)
        }
    }

    private fun setCheckedStatus(data: BgItemBean) {
        if (data.checked) {
            when (data.index) {
                4 -> mViewBinding.ivCheckMark.setImageResource(R.drawable.reader_ic_mode_night)
                else -> mViewBinding.ivCheckMark.setImageResource(R.drawable.reader_ic_selected)
            }
            mViewBinding.ivCheckMark.visibility = VISIBLE
            setShapeBackground(
                solidColor = getColor(R.color.reader_menu_item_stroke_selected),
                radius = 16.5F.dp,
            )
        } else {
            when (data.index) {
                4 -> {
                    mViewBinding.ivCheckMark.setImageResource(R.drawable.reader_ic_mode_day)
                    mViewBinding.ivCheckMark.visibility = VISIBLE
                }

                else -> mViewBinding.ivCheckMark.visibility = GONE
            }
            setShapeBackground(
                solidColor = getColor(R.color.reader_menu_item_stroke_unselected),
                radius = 16.5F.dp,
            )
        }
    }

    interface ViewActionListener : ActionListener {
        fun onItemChecked(data: BgItemBean)
    }
}

/**
 * 菜单背景数据bean
 */
class BgItemBean {
    var bgColor = 0
    var checked = false
    var index = 0
}