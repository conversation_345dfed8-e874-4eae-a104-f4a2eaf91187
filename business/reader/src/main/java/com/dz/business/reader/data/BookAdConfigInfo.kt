package com.dz.business.reader.data

import com.dz.business.reader.ad.data.ReaderAdLoadParam
import com.dz.business.reader.utils.ReaderAdUtil
import com.dz.business.reader.utils.ReaderConfigUtil
import com.dz.business.track.events.sensor.AdTE
import com.dz.platform.ad.vo.basic.AdSharedInfo


open class BookAdConfigInfo : AdSharedInfo() {
    var adId: String? = null//广告位ID
    var adPosition: Int? = null

    fun isValidAd(): Boolean {
        return !adId.isNullOrEmpty()
    }

    fun getLoadAdParam(
        imgW: Int,
        imgH: Int,
        temW: Int,
        temH: Int,
        forward: String?,
        mIsCache: Boolean
    ): ReaderAdLoadParam {

        val loadParam = ReaderAdLoadParam().apply {

            adPosition = <EMAIL>
            adId = <EMAIL>
            blockConfigId = <EMAIL>
            adType = AdTE.TYPE_FEED
            loadType = AdTE.PRE_LOAD
            adCount = 1
            actionPage = forward
            isUseCache = mIsCache
            imageW = imgW
            imageH = imgH
            templateW = temW
            templateH = temH
            sceneType = getSceneType()
            userTacticsVo = <EMAIL>
            nightMode = ReaderConfigUtil.isNightMode()

            /**
             *加载类型  信息流 ，激励视频，开屏,banner等
            interface AdLoaderType {
            companion object {
            const val SPLASH = "open_screen" //开屏
            const val REWARD_VIDEO = "video" //激励视频
            const val BANNER = "banner" //banner
            const val FEED = "information_flow" //信息流
            const val REWARD_FEED = "reward_feed"
            }
            }
             */
            loaderType = "information_flow"
            bookId = ReaderAdUtil.getInstance().bookId
            bookName = ReaderAdUtil.getInstance().bookName
            chapterId = ReaderAdUtil.getInstance().chapterId
            chapterIndex = ReaderAdUtil.getInstance().chapterIndex
            chapterName = ReaderAdUtil.getInstance().chapterName

        }

        return loadParam
    }

    /**
     * 使用场景（插页、底通、公告等）
     * AdBaseMC.AdSceneStyle
     */
    private fun getSceneType(): String {
        //广告位类型 1底通 2公告 3插页
        return when (adPosition) {
            AdTE.READER_BOTTOM_BANNER -> "reader_bottom"
            AdTE.READER_INSERT_PAGE -> "reader_insert_page"
            else -> {
                "reader_notice"
            }
        }
    }
}
