package com.dz.business.reader.ui.component

import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Path
import android.util.AttributeSet
import android.view.View
import androidx.core.content.ContextCompat
import com.dz.business.reader.R
import com.dz.business.reader.utils.ReaderConfigUtil
import com.dz.foundation.base.utils.dp

/**
 *@Author: wanxin
 *@Date: 2022/11/18 16:17
 *@Description: 批量下载进度条
 *@Version: 1.0
 */
class BatchLoadProgressView(context: Context?, attrs: AttributeSet?) : View(context, attrs) {

    private var mWidth = 0
    private var mHeight = 0

    private var radius = 18f.dp

    private var isLoading = false

    private val paintBase = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        style = Paint.Style.STROKE
        strokeWidth = 1f.dp
    }

    private val paintProgressRect = Paint(Paint.ANTI_ALIAS_FLAG)
    private val paintText = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        textSize = 16f.dp
        textAlign = Paint.Align.CENTER
    }
    private val fontMetrics = Paint.FontMetrics()

    private var totalSize = 1
    private var downloadSize = 0
    private var currentProgress = 0
    val path = Path()

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        mWidth = width
        mHeight = height
        path.addRoundRect(
            0f,
            0f,
            mWidth.toFloat(),
            mHeight.toFloat(),
            radius,
            radius, Path.Direction.CW
        )
        setColorStyle()
    }


    override fun onDraw(canvas: Canvas) {
        canvas.save()
        canvas.clipPath(path)
        canvas.drawRoundRect(
            0f,
            0f,
            mWidth.toFloat(),
            mHeight.toFloat(),
            radius,
            radius,
            paintBase
        )

        //绘制进度
        canvas.drawRect(
            0f,
            0f,
            mWidth.toFloat() / 100 * currentProgress,
            mHeight.toFloat(),
            paintProgressRect
        )

        //绘制文字
        paintText.getFontMetrics(fontMetrics)
        val baseLine = mHeight / 2 - (fontMetrics.ascent + fontMetrics.descent) / 2
        canvas.drawText(
            "${context.getString(R.string.reader_downloading)}（${currentProgress}%）",
            mWidth / 2f,
            baseLine,
            paintText
        )

        canvas.restore()
    }

    private fun setColorStyle() {
        if (ReaderConfigUtil.isNightMode()) {
            //夜间模式：
            paintBase.color = ContextCompat.getColor(
                context,
                R.color.reader_night_download_border
            )

            paintProgressRect.color = ContextCompat.getColor(
                context, R.color.reader_night_download_bg
            )
            paintText.color = ContextCompat.getColor(
                context, R.color.reader_night_download_text
            )
        } else {
            paintBase.color = ContextCompat.getColor(
                context,
                R.color.reader_download_border
            )

            paintProgressRect.color =
                ContextCompat.getColor(
                    context,
                    R.color.reader_download_bg
                )
            paintText.color =
                ContextCompat.getColor(
                    context,
                    R.color.reader_download_text
                )
        }
    }

    fun setDownloadProgress(totalSize: Int, downloadSize: Int) {
        this.totalSize = totalSize
        this.downloadSize = downloadSize
        currentProgress = (downloadSize.toFloat() / totalSize.toFloat() * 100).toInt()
        if (currentProgress > 100) {
            currentProgress = 100
        }
        isLoading = currentProgress != 100
        invalidate()
    }

    fun isLoading(): Boolean {
        return isLoading
    }


}