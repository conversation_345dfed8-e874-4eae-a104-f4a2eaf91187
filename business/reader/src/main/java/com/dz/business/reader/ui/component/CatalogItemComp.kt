package com.dz.business.reader.ui.component

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import androidx.core.content.ContextCompat
import com.dz.business.reader.R
import com.dz.business.reader.databinding.ReaderCatalogItemCompBinding
import com.dz.business.reader.utils.CatalogColorConfig
import com.dz.foundation.ui.view.custom.ActionListener
import com.dz.foundation.ui.view.custom.ActionListenerOwner
import com.dz.platform.common.base.ui.component.UIConstraintComponent

/**
 *@Author: wanxin
 *@Date: 2022/10/19 13:55
 *@Description: 目录item View
 *@Version: 1.0
 */
class CatalogItemComp @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : UIConstraintComponent<ReaderCatalogItemCompBinding, ChapterItemBean>(
    context,
    attrs,
    defStyleAttr
), ActionListenerOwner<CatalogItemComp.ViewActionListener> {
    override var mActionListener: ViewActionListener? = null

    override fun initData() {
//        setShapeBackground(
//            solidColor = getColor(R.color.common_transparent),
//            statePressedCoverColor = getColor(R.color.bbase_0D000000_1AFFFFFF)
//        )
    }

    override fun initView() {
        mViewBinding.ivLock.setBackgroundResource(CatalogColorConfig.lockIcon)
//        mViewBinding.vLine.setBackgroundColor(
//            ContextCompat.getColor(
//                context,
//                CatalogColorConfig.dividerColor
//            )
//        )

    }

    override fun initListener() {
        registerClickAction {
            mData?.run {
                chapterId?.let {
                    mActionListener?.onClickChapter(it)
                }
            }
        }
    }


    @SuppressLint("SetTextI18n")
    override fun bindData(data: ChapterItemBean?) {
        super.bindData(data)
        data?.run {
            //有章节id
            if (!chapterId.isNullOrEmpty()) {
                mViewBinding.tvChapterName.text = chapterName
                mViewBinding.tvChapterName.run {
                    when (readType) {
                        CHAPTER_TYPE_READING -> {
                            //正在阅读
                            setTextColor(
                                ContextCompat.getColor(
                                    context,
                                    CatalogColorConfig.chapterReadingTextColor
                                )
                            )
                        }
                        CHAPTER_TYPE_LOAD -> {
                            //已经下载
                            setTextColor(
                                ContextCompat.getColor(
                                    context,
                                    CatalogColorConfig.chapterLoadedTextColor
                                )
                            )
                        }
                        else -> {
                            //未下载
                            setTextColor(
                                ContextCompat.getColor(
                                    context,
                                    CatalogColorConfig.chapterUnloadTextColor
                                )
                            )
                        }
                    }
                }
                if (hasLock == 1) {
                    mViewBinding.ivLock.visibility = VISIBLE
                } else {
                    mViewBinding.ivLock.visibility = GONE
                }

            } else {
                mViewBinding.tvChapterName.text = context.getString(R.string.reader_catalog_loading)
                mViewBinding.tvChapterName.setTextColor(
                    ContextCompat.getColor(
                        context,
                        CatalogColorConfig.chapterUnloadTextColor
                    )
                )
                mViewBinding.ivLock.visibility = GONE
            }

        }
    }

    interface ViewActionListener : ActionListener {
        fun onClickChapter(readChapterId: String)
    }

}

/**
 *@Author: wanxin
 *@Date: 2022/10/19 21:40
 *@Description:
 *@Version: 1.0
 */
data class ChapterItemBean(
    //章节名
    var chapterName: String?,
    //章节id
    var chapterId: String?,
    //章节位置
    var chapterIndex: Int?,
    //章节阅读状态
    var readType: Int?,
    //是否有锁
    var hasLock: Int?
)

/**
 * 章节正在阅读
 */
const val CHAPTER_TYPE_READING = 1

/**
 * 章节已下载
 */
const val CHAPTER_TYPE_LOAD = 2

/**
 * 章节未下载
 */
const val CHAPTER_TYPE_UNLOAD = 3