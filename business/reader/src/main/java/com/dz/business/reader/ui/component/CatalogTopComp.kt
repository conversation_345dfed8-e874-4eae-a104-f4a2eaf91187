package com.dz.business.reader.ui.component

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import androidx.core.content.ContextCompat
import com.dz.business.base.bookdetail.BookDetailMR
import com.dz.business.base.data.BBaseKV
import com.dz.business.reader.R
import com.dz.business.reader.databinding.ReaderCatalogTopCompBinding
import com.dz.business.reader.repository.entity.NovelBookEntity
import com.dz.business.reader.utils.CatalogColorConfig
import com.dz.foundation.base.utils.dp
import com.dz.foundation.imageloader.loadRoundImg
import com.dz.foundation.ui.widget.getContainerActivity
import com.dz.platform.common.base.ui.component.UIConstraintComponent
import com.dz.platform.common.toast.ToastManager

/**
 *@Author: wanxin
 *@Date: 2022/10/19 17:12
 *@Description: 目录页面顶部view
 *@Version: 1.0
 */
class CatalogTopComp @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : UIConstraintComponent<ReaderCatalogTopCompBinding, CatalogTopBean>(
    context,
    attrs,
    defStyleAttr
) {

    override fun initData() {
    }

    override fun initView() {

    }

    override fun initListener() {
        this.registerClickAction {
            //青少年模式、从书籍详情进入 屏蔽点击
            if (BBaseKV.isYoungMode == 1 || mData?.isFromBookDetail == true) {
                return@registerClickAction
            }
            if (NovelBookEntity.isLocalBook(mData?.bookId)) {
                return@registerClickAction
            }
            mData?.run {
                bookId?.let {
                    ToastManager.dismissToast()
                    BookDetailMR.get().bookDetail().apply {
                        bookId = it
                    }.start()
                    getContainerActivity()?.run { finish() }
                }
            }
        }
    }


    @SuppressLint("SetTextI18n")
    override fun bindData(data: CatalogTopBean?) {
        super.bindData(data)
        data?.run {
            mViewBinding.run {
                //封面

                ivBookCover.loadRoundImg(
                        bookCoverUrl,
                        4.dp,
                        R.drawable.dz_default_book_shelf,
                        R.drawable.dz_default_book_shelf,
                        width = 58, height = 77
                    )

                //书名
                tvBookName.text = bookName
                //作者
                if (!author.isNullOrEmpty()) {
                    tvAuthor.text = context.getString(R.string.reader_author, author)
                }

                //箭头(青少年模式或者从详情进入隐藏)
                if (BBaseKV.isYoungMode == 1 || isFromBookDetail == true ) {
                    ivArrow.visibility = INVISIBLE
                } else {
                    ivArrow.visibility = VISIBLE
                }
            }
        }

        resetColor()
    }

    fun resetColor() {
        mViewBinding.run {
            tvBookName.setTextColor(
                ContextCompat.getColor(
                    context,
                    CatalogColorConfig.bookNameTextColor
                )
            )
            tvAuthor.setTextColor(
                ContextCompat.getColor(
                    context,
                    CatalogColorConfig.authorTextColor
                )
            )
            ivArrow.setBackgroundResource(CatalogColorConfig.goDetailArrow)
        }
    }


}

data class CatalogTopBean(
    val bookCoverUrl: String?,
    val bookName: String?,
    val author: String?,
    val bookId: String?,
    val isFromBookDetail: Boolean?
)