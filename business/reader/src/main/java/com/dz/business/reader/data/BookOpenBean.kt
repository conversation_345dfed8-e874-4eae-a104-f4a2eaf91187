package com.dz.business.reader.data

import com.dz.business.base.data.bean.BaseBean

import com.dz.business.base.data.bean.BaseOperationBean
import com.dz.business.base.helper.FloatWindowManage.Companion.TTS_TYPE
import com.dz.business.base.reader.data.NovelTimbreVo


data class BookOpenBean(
    var addBookshelfChapterNum: Int? = null,
    var addBookshelfTime: Int? = null,
    var bookInfo: NovelBookInfo? = null,
    var preloadNum: Int? = null, //预加载的章节数
    var preloadPrevChapter: Int? = null,//是否开启预加载上一章：0-不开启，1-开启
    var onTheShelf: Int? = null,//"是否在书架上：0-否；1-是"
    var bookIsFirst: Int? = null,//书籍是否首次阅读：1-是，其他-不是
    var isFirstTimeRead: Int? = null,//用户是否首次阅读：1-是，其他-不是
    val operating: BaseOperationBean?,

    var novelAdVo: ReaderAdConfigInfo? = null, //广告配置

    var removeAdVo: NoAdConfig? = null, //去广告配置


    //tts的配置
    var ttsConfigData: TtsConfigDataVo? = null, //tts 配置

    //有声书的配置
    var speedList: List<NovelSpeedVo>? = null, //音速列表
    var timeList: List<TimerListItem>? = null //定时列表

) : BaseBean()


data class TtsConfigDataVo(
    var showListen: Int? = null, //是否显示听书入口，1--显示，0--不显示
    var reason: String? = null, //不支持听书的原因
    var ttsEnable: Int? = null,  // 当前书籍是否支持听书 1-支持；0-不支持；
    val firm: String? = null, // 听书三方：1-百度；
    val token: String? = null,// 听书三方toke
    var serverUrl: String? = null,// https://tsn.baidu.com/text2audio
    var toneList: List<NovelTimbreVo>? = null, //音色列表
    var speedList: List<NovelSpeedVo>? = null, //音速列表
    var timeList: List<TimerListItem>? = null //定时列表
) : BaseBean()

/**
 * 定时配置
 */
data class TimerListItem(
    val time: Int,//时间
    val desc: String,//描述
    var isDefault: Int, //是否默认选中
) : BaseBean()

/**
 * 音速配置
 */
data class NovelSpeedVo(
    var speed: Float = 0f, //三方音速
    var speedName: String? = null, //预转音速:对应内容中台预转音速
    var isDefault: Int? = null,  // 是否默认选中
) : BaseBean()

/**
 * 去广告配置
 */
data class NoAdConfig(
    var removeAdBtn: Int? = null,
    var removeAdUrl: String? = null,
    var removeAdTip: String? = null
) : BaseBean()


data class PreTtsInfo(

    var audioUrl: String?, //音频说明地址 有效期 3天,可以短期存储
    var mrcUrl: String?, //音频段落地址 有效期 3天,可以短期存储

) : BaseBean()