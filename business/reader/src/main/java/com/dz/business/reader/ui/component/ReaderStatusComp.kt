package com.dz.business.reader.ui.component

import android.content.Context
import android.util.AttributeSet
import androidx.annotation.AttrRes
import com.dz.business.base.ui.component.status.LoadingComponent
import com.dz.business.base.ui.component.status.Status
import com.dz.business.reader.R
import com.dz.business.reader.databinding.ReaderStatusCompBinding
import com.dz.business.reader.ui.page.ReaderActivity
import com.dz.business.reader.utils.ReaderConfigKV
import com.dz.foundation.base.utils.ScreenUtil
import com.dz.foundation.base.utils.dp2px
import com.dz.foundation.ui.widget.getContainerActivity
import com.dz.platform.common.base.ui.component.UIConstraintComponent
import reader.xo.config.ReaderConfigs

/**
 *@Author: shidz
 *@Date: 2023/1/8 16:30
 *@Description: 阅读器状态组件
 *@Version:1.0
 */
class ReaderStatusComp : UIConstraintComponent<ReaderStatusCompBinding, Status> {
    companion object{

        fun getNetErrorIconRes():Int{
            if (ReaderConfigKV.nightEnable) {
                return R.drawable.reader_ic_net_error_night
            }
            return R.drawable.reader_ic_net_error_day
        }

        fun getUnShelveIconRes():Int{
            if (ReaderConfigKV.nightEnable) {
                return R.drawable.reader_ic_status_empty_night
            }
            return R.drawable.reader_ic_status_empty_day
        }
    }

    @JvmOverloads
    constructor(
        context: Context,
        attrs: AttributeSet? = null,
        @AttrRes defStyleAttr: Int = 0
    ) : super(context, attrs, defStyleAttr)

    override fun initData() {

    }

    override fun initView() {

        val statusHeight = ScreenUtil.getStatusHeight(context)
        mViewBinding.statusTitle.layoutParams = mViewBinding.statusTitle.layoutParams.apply {
            height = statusHeight + 48.dp2px.toInt()
        }
        mViewBinding.statusTitle.setPadding(
            mViewBinding.statusTitle.paddingLeft,
            statusHeight,
            mViewBinding.statusTitle.paddingRight,
            mViewBinding.statusTitle.paddingBottom
        )
    }

    override fun initListener() {

    }

    override fun bindData(data: Status?) {
        super.bindData(data)
        data?.let {
            visibility = VISIBLE
            mViewBinding.statusTitle.visibility = VISIBLE
            if (ReaderConfigKV.nightEnable) {
                mViewBinding.compStatus.setBackgroundResource(R.color.reader_config_color_style_bg_night)
                mViewBinding.statusTitle.setBackgroundResource(R.color.reader_config_color_style_bg_night)
                mViewBinding.statusTitle.setBackArrowImageResource(R.drawable.reader_arrow_back_night_mode)
                mViewBinding.compStatus.setDesTextColor(R.color.reader_99FFFFFF)
                mViewBinding.compStatus.setActionBtnBg(solidColor =getColor(R.color.reader_1AFFFFFF),radius = 18.dp2px )
            } else {
                mViewBinding.statusTitle.setBackgroundResource(R.color.reader_FFF2F3F6)
                mViewBinding.statusTitle.setBackArrowImageResource(R.drawable.reader_arrow_back_day_mode)
                mViewBinding.compStatus.setBackgroundResource(R.color.reader_FFF2F3F6)
                mViewBinding.compStatus.setDesTextColor(R.color.reader_99000000)
                mViewBinding.compStatus.setActionBtnBg(solidColor =getColor(R.color.reader_0D000000),radius = 18.dp2px)
            }
        }

        when (data?.getStatus()) {
            Status.LOADING -> {
                getContainerActivity().let {
                    if (it is ReaderActivity) {
                        if (it.getXoReader().getCurrentDocInfo().fid.isNotEmpty()) {
                            data.setLoadStyle(LoadingComponent.STYLE_READER_TOAST)
                        } else {
                            data.setLoadStyle(LoadingComponent.STYLE_COMMON)
                        }
                    }
                }
                mViewBinding.compStatus.setBackgroundResource(R.color.reader_00000000_00000000)
                mViewBinding.statusTitle.visibility = GONE
            }
            Status.DISMISS -> {
                visibility = GONE
            }
        }

        mViewBinding.compStatus.bindData(data)
    }
}