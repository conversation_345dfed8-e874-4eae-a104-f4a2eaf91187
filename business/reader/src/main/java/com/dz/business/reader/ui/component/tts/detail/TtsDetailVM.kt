package com.dz.business.reader.ui.component.tts.detail

import CoroutineUtils
import com.dz.business.base.data.bean.StrategyInfo
import com.dz.business.base.livedata.CommLiveData
import com.dz.business.base.network.BBaseNetWork
import com.dz.business.base.reader.ReaderMR
import com.dz.business.base.reader.data.MenuInfoVO
import com.dz.business.base.reader.data.MenuItemInfoVO
import com.dz.business.base.reader.data.NovelTimbreVo
import com.dz.business.base.reader.intent.ReaderTtsDetailIntent
import com.dz.business.base.theatre.data.BookInfoVo
import com.dz.business.base.ui.component.status.Status
import com.dz.business.base.vm.PageVM
import com.dz.business.reader.DataRepository
import com.dz.business.reader.R
import com.dz.business.reader.audio.TtsPlayer
import com.dz.business.reader.data.NovelSpeedVo
import com.dz.business.reader.data.TimerListItem
import com.dz.business.reader.data.TtsConfigDataVo
import com.dz.business.reader.network.ReaderNetwork
import com.dz.business.reader.repository.entity.NovelBookEntity
import com.dz.business.reader.repository.entity.NovelChapterEntity
import com.dz.foundation.base.manager.task.TaskManager
import com.dz.foundation.base.module.AppModule
import com.dz.foundation.base.utils.LogUtil
import com.dz.foundation.network.onError
import com.dz.foundation.network.onResponse
import com.dz.platform.common.toast.ToastManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class TtsDetailVM : PageVM<ReaderTtsDetailIntent>(), TtsDetailVMContract {

    companion object {
        const val TAG: String = "TtsDetailVM"
    }

    //章节数据
    private var bookSource: String? = null

    //进入阅读时的数据
    override var novelChapterPageData = CommLiveData<NovelChapterPageData>()
    override var chapterData = CommLiveData<NovelChapterEntity>()
    override var recommendNovelList = CommLiveData<List<BookInfoVo>?>()
    override var recommendStatus = CommLiveData<Int>()

    //"是否在书架上：0-否；1-是"
    override var isShelf = CommLiveData<Int>()

    var novelBookEntity: NovelBookEntity? = null
    private var ttsConfig: TtsConfigDataVo? = null

    override fun loadData() {
        val bookId: String = routeIntent?.bookId ?: ""
        val chapterId: String? = routeIntent?.chapterId
        val bookSource: String? = routeIntent?.bookSource
        LogUtil.d(TAG, "initData bookId=${bookId}, chapterId=${chapterId}")
        this.bookSource = bookSource
        CoroutineUtils.getCoroutineScope().launch(Dispatchers.IO) {
            chapterId?.let {
                val chapterInfo = DataRepository.chapterDao().queryByCid(bookId, it)
                var novelBookInfo = DataRepository.bookDao().queryByBid(bookId)
                withContext(Dispatchers.Main) {
                    LogUtil.d(
                        TAG,
                        "getBookData chapterInfo=${chapterInfo} ,novelBookInfo isShelf=${isShelf.value}"
                    )
                    chapterInfo?.let {
                        chapterData.value = it
                    }

                    novelBookInfo?.let {
                        novelBookEntity = it
                        novelChapterPageData.value = NovelChapterPageData(
                            bookCover = it.coverurl ?: "",
                            chapterName = chapterData.value?.chapter_name ?: "",
                            bookName = it.book_name ?: "",
                            isShelf = isShelf.value == 1,
                        )
                    }
                }
            }
        }
        loadBookInfo()

        //更新 tts 配置
        ttsConfig = TtsPlayer.instance.ttsConfig
        updateTtsUserConfig()


        reloadRecommendData(bookId)

    }


    private fun updateTtsUserConfig() {
        //倒计时
        val timeConfig = TtsPlayer.instance.timerPresenter.timerFullLength
        ttsConfig?.timeList?.forEach { item ->
            item.isDefault = if (item.time == timeConfig) 1 else 0
        }

        //音色
        val toneId = TtsPlayer.instance.voicePresenter.voice?.toneId
        if (!toneId.isNullOrEmpty()) {
            // 根据 toneId 匹配设置选中状态
            ttsConfig?.toneList?.forEach { item ->
                item.isDefault = if (item.toneId == toneId) 1 else 0
            }
        } else {
            // 找到第一个 isDefault=1 的项设为选中,其他设为非选中
            var hasSelected = false
            ttsConfig?.toneList?.forEach { item ->
                if (!hasSelected && item.isDefault == 1) {
                    item.isDefault = 1
                    hasSelected = true
                } else {
                    item.isDefault = 0
                }
            }
        }


        //语速
        val speed = TtsPlayer.instance.speedPresenter.speed
        ttsConfig?.speedList?.forEach { item ->
            item.isDefault = if (item.speed == speed) 1 else 0
        }
    }


    override fun onClockMenuClick() {
        val menuList = ttsConfig?.timeList?.map { item ->
            MenuItemInfoVO(
                name = item.desc, value = item.time.toString(), isChecked = item.isDefault == 1
            )
        } ?: mutableListOf()
        if (menuList.isEmpty()) {
            ToastManager.showToast("暂无可用配置")
            return
        }
        //显示弹窗
        ReaderMR.get().ttsMenuDialog().apply {
            menuData = MenuInfoVO(
                title = "定时设置", menuInfo = menuList
            )
        }.onSure { menuInfo ->
            var target: TimerListItem? = null
            ttsConfig?.timeList?.forEach { item ->
                if (item.time.toString() == menuInfo?.value) {
                    item.isDefault = 1
                    target = item
                } else {
                    item.isDefault = 0
                }
            }

            target?.let {
                // 立即生效，开始倒计时
                if (it.time != 0) {
                    ToastManager.showToast(
                        "听书模式将在${it.time}分钟后关闭"
                    )
                    TtsPlayer.instance.timerPresenter.startTimerToClose(it.time)
                } else {
                    ToastManager.showToast(AppModule.getApplication().resources.getString(R.string.reader_closed_timer))
                    TtsPlayer.instance.timerPresenter.cancelTimerToClose(true)
                }

            }

        }.start()
    }

    override fun onVoiceMenuClick() {
        val menuList = ttsConfig?.toneList?.map { item ->
            MenuItemInfoVO(
                name = "${item.desc}·${item.name}",
                value = item.toneId,
                isChecked = item.isDefault == 1
            )
        } ?: mutableListOf()
        if (menuList.isEmpty()) {
            ToastManager.showToast("暂无可用配置")
            return
        }
        //显示弹窗
        ReaderMR.get().ttsMenuDialog().apply {
            menuData = MenuInfoVO(
                title = "切换声音", menuInfo = menuList
            )
        }.onSure { menuInfo ->
            //更改选中状态
            var targetVoice: NovelTimbreVo? = null
            ttsConfig?.toneList?.forEach { item ->
                if (item.toneId == menuInfo?.value) {
                    item.isDefault = 1
                    targetVoice = item
                } else {
                    item.isDefault = 0
                }
            }

            //切换音色
            targetVoice?.let { it ->
                TtsPlayer.instance.voicePresenter.onVoiceChanged(it)
            }


        }.start()
    }

    override fun onSpeedMenuClick() {
        val menuList = ttsConfig?.speedList?.map { item ->
            MenuItemInfoVO(
                name = item.speedName,
                value = item.speed.toString(),
                isChecked = item.isDefault == 1
            )
        } ?: mutableListOf()
        if (menuList.isEmpty()) {
            ToastManager.showToast("暂无可用配置")
            return
        }
        //显示弹窗
        ReaderMR.get().ttsMenuDialog().apply {
            menuData = MenuInfoVO(
                title = "语速", menuInfo = menuList
            )
        }.onSure { menuInfo ->
            //更新选中态
            var targetSpeed: NovelSpeedVo? = null
            ttsConfig?.speedList?.forEach { item ->
                if (item.speed.toString() == menuInfo?.value) {
                    item.isDefault = 1
                    targetSpeed = item
                } else {
                    item.isDefault = 0
                }
            }

            //切换倍速
            targetSpeed?.let {
                TtsPlayer.instance.speedPresenter.onSpeedChanged(it.speed)
            }

        }.start()

    }

    override fun onChapterListMenuClick() {
        ReaderMR.get().readerCatalog().apply {
            bookId = novelBookEntity?.bid.toString()
            bookName = novelBookEntity?.book_name.toString()
            chapterId = chapterData.value?.cid.toString()
            chapterName = chapterData.value?.chapter_name
            chapterIndex = chapterData.value?.chapter_num
            isAddShelf = isShelf.value == 1
            routeSource = bookSource
            referrer = ReaderMR.TTS_DETAIL
            audioType = routeIntent?.audioType ?: 0
        }.overridePendingTransition(
            R.anim.common_ac_none, R.anim.common_ac_none
        ).start()
    }

    override fun onShelfMenuClick() {
        if (novelBookEntity == null) {
            ToastManager.showToast("小说信息获取失败")
            return
        }
        if (isShelf.value != 1) {
            addFavorites()
        } else {
            deleteFavorites()
        }
    }

    /**
     * 取消加入书架
     */
    private fun deleteFavorites() {
        val list = mutableListOf<String>()
        list.add(novelBookEntity?.bid ?: "")
        BBaseNetWork.get().deleteFavorites().setParams(
            bookIds = list,
            source = "12",
            tierPlaySource = null,
        ).onResponse {
            it.data?.run {
                if (status == 1) {
                    ToastManager.showToast(R.string.reader_delete_shelf_success)
                    novelBookEntity?.let { bookData ->
                        isShelf.value = 0
                        TaskManager.ioTask {
                            novelBookEntity?.let {
                                DataRepository.bookDao().insertOrUpdateBooks(bookData)
                            }
                        }
                        novelChapterPageData.value = NovelChapterPageData(
                            bookCover = bookData.coverurl ?: "",
                            chapterName = chapterData.value?.chapter_name ?: "",
                            bookName = bookData.book_name ?: "",
                            isShelf = false,
                        )
                    }
                }
            }
        }.onError {
            ToastManager.showToast("加入书架失败")
        }.doRequest()


    }

    /**
     * 加入书架
     */
    private fun addFavorites() {
        BBaseNetWork.get().addFavorites().setParams(
            bookId = novelBookEntity?.bid ?: "",
            chapterId = chapterData.value?.cid,
            source = "12",
            omap = StrategyInfo(),
            tierPlaySource = null,
            followSource = null
        ).onResponse {
            it.data?.run {
                if (status == 1) {
                    ToastManager.showToast(R.string.reader_add_shelf_success)
                    novelBookEntity?.let { bookData ->
                        isShelf.value = 1
                        TaskManager.ioTask {
                            DataRepository.bookDao().insertOrUpdateBooks(bookData)
                        }
                        novelChapterPageData.value = NovelChapterPageData(
                            bookCover = bookData.coverurl ?: "",
                            chapterName = chapterData.value?.chapter_name ?: "",
                            bookName = bookData.book_name ?: "",
                            isShelf = true,
                        )
                    }
                }
            }
        }.onError {
            ToastManager.showToast("加入书架失败")
        }.doRequest()
    }


    override fun onChapterChanged(novelInfo: NovelChapterEntity?) {
        novelInfo?.let { chapterInfo ->
            chapterData.value = chapterInfo
            //刷新界面章节内容
            novelChapterPageData.value = NovelChapterPageData(
                bookCover = novelBookEntity?.coverurl ?: "",
                chapterName = chapterData.value?.chapter_name ?: "",
                bookName = novelBookEntity?.book_name ?: "",
                isShelf = isShelf.value == 1,
            )
        }

    }

    override fun onSeekBarProgressChange(progress: Int, uiId: String) {
        if (TtsPlayer.instance.isRunning()) {
            TtsPlayer.instance.progressPresenter.seekToProgress(progress)
        } else {
            TtsPlayer.instance.startTTS(
                uiId,
                novelBookEntity?.bid,
                chapterData.value?.cid,
                novelBookEntity?.coverurl ?: "",
                progress,
                routeIntent?.audioType,
            )
        }

    }

    override fun reloadRecommendData(bookId: String) {
        //加载推荐数据
        ReaderNetwork.get().ttsRecommendData().setParams(bookId).onResponse {
            if (it.data?.novelList?.isEmpty() == true) {
                recommendStatus.value = Status.EMPTY
            } else {
                recommendStatus.value = Status.NORMAL
                recommendNovelList.value = it.data?.novelList
            }
        }.onError {
            ToastManager.showToast("网络异常，请稍后重试")
            recommendStatus.value = Status.NET_ERROR
        }.doRequest()
    }

    //请求书籍信息
    fun loadBookInfo() {
        ReaderNetwork.get().bookOpen().setParams(
            routeIntent?.bookId ?: "",
            routeIntent?.bookSource ?: "",
            false,
        ).onResponse {
            it.data?.run {
                isShelf.value = this.onTheShelf
            }
        }.doRequest()
    }

    override fun getSelectedVoice(): NovelTimbreVo? {
        return ttsConfig?.toneList?.find { it.isDefault == 1 }
    }

    override fun getSelectedSpeed(): NovelSpeedVo? {
        return ttsConfig?.speedList?.find { it.isDefault == 1 }
    }
}