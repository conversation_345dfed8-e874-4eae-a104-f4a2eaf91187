package com.dz.business.reader.vm

import com.dz.business.base.reader.intent.ReaderIntent
import com.dz.business.base.vm.PageVM

class AudioVM : PageVM<ReaderIntent>() {

    private var currentCid: String? = null
    private var currentBid: String? = null
    fun getBookSource(): String {
        return routeIntent?.getBookRouteSource() ?: ""
    }

    fun getBookId(): String {
        return currentBid?:routeIntent?.bookId ?: ""
    }

    fun getCurrentChapterId(): String? {
        return currentCid?:routeIntent?.chapterId
    }
    /**
     * 是否是短篇
     */
    fun isShortBook(): Bo<PERSON>an {
//        return routeIntent?.shortTag == "1"
        return false
    }
}