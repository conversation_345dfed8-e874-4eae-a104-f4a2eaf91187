package com.dz.business.reader.data

import com.dz.business.base.data.bean.BaseBean
import com.dz.business.reader.repository.entity.NovelBookEntity

data class CatalogInfoBaseBean(
    val bookInfo: CatalogBookInfo?,
    val chapterList: List<Chapter>?,
    val supportBatch: Int?,
    val title: String?,
) : BaseBean()

data class CatalogBookInfo(
    val alias: String? = null,
    val author: String? = null,
    val bookId: String? = null,
    val bookName: String? = null,
    val bookType: Int? = null,
    val clickNum: Int? = null,
    val comCount: Int? = null,
    val comScore: String? = null,
    val coverWap: String? = null,
    val introduction: String? = null,
    val isAddBookShelf: Int? = null,
    val isPreTts: Int? = null,
    val isSvip: Int? = null,
    val isTts: Int? = null,
    val lastChapterId: Int? = null,
    val lastChapterName: String? = null,
    val lastChapterUtime: String? = null,
    val limitCountTime: Int? = null,
    val marketStatus: Int? = null,
    val protagonist: List<String?>? = null,
    val status: Int? = null,
    val statusTips: String? = null,
    val tags: List<Any?>? = null,
    val totalChapterNum: Int? = null,
    val totalWordSize: String? = null,
    val unit: Int? = null
) : BaseBean() {
    fun isLocalBook(): Boolean {
        return NovelBookEntity.isLocalBook(bookId)
    }

    fun canShow(): Boolean {
        //下架、删除
        if (marketStatus == 2 || marketStatus == 10) {
            return false
        }
        return true
    }
}

data class Chapter(
    val chapterId: String,
    val chapterName: String,
    val hasLock: Int?,
    val index: Int
) : BaseBean()