package com.dz.business.reader.ui.component.block


import com.dz.business.reader.data.BookOpenBean
import com.dz.business.reader.presenter.ReaderCallbackPresenter.Companion.BLOCK_TYPE_STORY_PAGER_AD
import com.dz.business.reader.ui.component.ad.AdReaderPageComp
import com.dz.business.reader.ui.component.ad.AdReaderPageContainerComp
import com.dz.business.reader.ui.component.order.ChapterPreviewOrderComp
import com.dz.business.reader.ui.page.ReaderActivity
import com.dz.business.reader.utils.ChapterEndMarketingCacheUtil
import com.dz.foundation.base.utils.LogUtil
import reader.xo.block.AppendBlockView
import reader.xo.block.BlockViewProvider
import reader.xo.block.EmptyBlockView
import reader.xo.block.ExtContentBlockView
import reader.xo.block.ExtPageBlockView
import reader.xo.block.LoadingBlockView
import reader.xo.block.StatusBlockView
import reader.xo.widgets.StoryTopStatusView


class ReaderBlockProvider(private val readerActivity: ReaderActivity) : BlockViewProvider {

    private var bookOpenBean: BookOpenBean? = null

    init {
        ChapterEndMarketingCacheUtil.clear()
    }


    private fun createChapterEndComp(): ChapterEndComp {
        return ChapterEndComp(readerActivity)
    }



    private fun createBookEndComp(): ReaderBlockComp? {
        return BookEndComp(readerActivity)
    }

    private fun createChapterPreviewComp(): ReaderBlockComp? {
        return ChapterPreviewOrderComp(readerActivity)
    }

//    private fun createBottomStatusComp(): IReaderStatusBlock {
//        return ReaderBottomStatusComp(readerActivity).apply {
//            bindData(bookOpenBean)
//        }
//    }



    override fun createAppendView(type: Int): AppendBlockView? {
        LogUtil.d("ReaderBlockProvider", "getEmptyView type=0")
        var blockView = when (type) {
            ReaderAppendBlockView.TYPE_CHAPTER_END -> {
                createChapterEndComp()
            }
            else -> null
        }
        return blockView?.run { ReaderAppendBlockView(readerActivity, type).addBlock(this) }
    }

    override fun createBottomStatusView(): StatusBlockView? {
//        return createBottomStatusComp().run { ReaderStatusBlockView(readerActivity).addBlock(this) }
        return null
    }

    override fun createEmptyView(type: Int): EmptyBlockView? {
        return null
    }

    override fun createExtContentView(type: Int): ExtContentBlockView? {
        LogUtil.d("ReaderBlockProvider", "getEmptyView type=0")
        var blockView = when (type) {
            ReaderPageBlockView.TYPE_BOOK_END -> {
                createBookEndComp()
            }
            ReaderPageBlockView.TYPE_PREVIEW_PAY -> {
                createChapterPreviewComp()
            }
//            ReaderPageBlockView.TYPE_BOOK_COVER -> {
//                createBookCoverComp()
//            }
            else -> null
        }
        return blockView?.run { ReaderPageBlockView(readerActivity, type).addBlock(this) }
    }

    override fun createExtPageView(type: Int): ExtPageBlockView? {
//        if (type == BLOCK_TYPE_STORY_INTRODUCE_HEADER){
//            return StoryIntroduceBlockView(readerActivity,type)
//        }
        if (type == BLOCK_TYPE_STORY_PAGER_AD){
            val adPage = AdReaderPageContainerComp(readerActivity)
            adPage.addAdPageComp(
                AdReaderPageComp(
                    readerActivity
                )
            )
            return adPage
        }
        return null
    }

    override fun createLoadingView(type: Int): LoadingBlockView? {
        return null
    }

    override fun createTopStatusView(): StatusBlockView? {
//        val topStatusBlockView = ReaderStatusBlockView(readerActivity)
//        topStatusBlockView.addTopStatusComp(ReaderTopStatusComp(readerActivity))
//        return topStatusBlockView
        return StoryTopStatusView(readerActivity)
    }

    fun onBookOpenConfig(bookOpenBean: BookOpenBean) {
        this.bookOpenBean = bookOpenBean
        //fix:底部文字链创建View与1301接口无先后顺序，需要双向确保接口数据绑定View中
//        ReaderInsideEvents.get().onBottomOperaData().post(bookOpenBean)
    }

}