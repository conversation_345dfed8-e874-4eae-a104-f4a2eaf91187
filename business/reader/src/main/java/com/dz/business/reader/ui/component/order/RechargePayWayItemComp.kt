package com.dz.business.reader.ui.component.order

import android.content.Context
import android.util.AttributeSet
import com.dz.business.reader.data.RechargePayWayBean
import com.dz.business.reader.databinding.ReaderPayWayItemCompBinding
import com.dz.foundation.imageloader.load

import com.dz.foundation.ui.view.custom.ActionListener
import com.dz.foundation.ui.view.custom.ActionListenerOwner
import com.dz.platform.common.base.ui.component.UIConstraintComponent

/**
 *@Author: shidz
 *@Date: 2022/11/15 9:50
 *@Description:支付方式item组件
 *@Version:1.0
 */
class RechargePayWayItemComp :
    UIConstraintComponent<ReaderPayWayItemCompBinding, RechargePayWayBean>,
    ActionListenerOwner<RechargePayWayItemComp.PayWayItemActionListener> {

    @JvmOverloads
    constructor(
        context: Context,
        attrs: AttributeSet? = null,
        defStyleAttr: Int = 0
    ) : super(context, attrs, defStyleAttr)

    override var mActionListener: PayWayItemActionListener? = null

    override fun initData() {
    }

    override fun initView() {
    }

    override fun initListener() {
        this.registerClickAction {
            mData?.run {
                if (!isValid) {
                    return@registerClickAction
                }
                mActionListener?.onPayWayClick(recyclerViewItemPosition, this)
            }
        }
    }

    override fun bindData(data: RechargePayWayBean?) {
        super.bindData(data)
        data?.run {
            mViewBinding.tvPayWayName.text = title
            mViewBinding.imgIcon.load(icon, width = 24, height = 24)
            if (isValid) {
                mViewBinding.rlRoot.alpha = 1.0F
                mViewBinding.imgPaySelectTag.isSelected = isSelected
            } else {
                mViewBinding.rlRoot.alpha = 0.3F
                mViewBinding.imgPaySelectTag.isSelected = false
            }

        }
    }

    interface PayWayItemActionListener : ActionListener {
        fun onPayWayClick(position: Int, bean: RechargePayWayBean)
    }
}