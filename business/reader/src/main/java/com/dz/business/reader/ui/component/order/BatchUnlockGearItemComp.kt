package com.dz.business.reader.ui.component.order

import android.content.Context
import android.graphics.Paint
import android.util.AttributeSet
import com.dz.business.reader.data.BatchUnlockGear
import com.dz.business.reader.databinding.ReaderBatchUnlockGearItemCompBinding
import com.dz.foundation.ui.view.custom.ActionListener

import com.dz.foundation.ui.view.custom.ActionListenerOwner
import com.dz.platform.common.base.ui.component.UIConstraintComponent

/**
 *@Author: shidz
 *@Date: 2022/11/15 9:49
 *@Description: 批量解锁挡位
 *@Version:1.0
 */

class BatchUnlockGearItemComp :
    UIConstraintComponent<ReaderBatchUnlockGearItemCompBinding, BatchUnlockGear>,
    ActionListenerOwner<BatchUnlockGearItemComp.BatchUnlockGearActionListener> {
    @JvmOverloads
    constructor(
        context: Context,
        attrs: AttributeSet? = null,
        defStyleAttr: Int = 0
    ) : super(context, attrs, defStyleAttr)

    override var mActionListener: BatchUnlockGearActionListener? = null

    override fun initData() {
    }

    override fun initView() {
    }

    override fun initListener() {
        this.registerClickAction {
            mData?.let {
                mActionListener?.onGearChecked(it)
            }
        }
    }

    override fun bindData(data: BatchUnlockGear?) {
        super.bindData(data)
        data?.run {
            setViewData(this)

        }
    }

    private fun setViewData(batchUnlockGear: BatchUnlockGear) {
        mViewBinding.tvMoney.text = batchUnlockGear.priceText
        mViewBinding.tvOriginPriceValue.text = batchUnlockGear.srcPriceText
        mViewBinding.tvOriginPriceValue.paintFlags =
            mViewBinding.tvOriginPriceValue.paintFlags or Paint.STRIKE_THRU_TEXT_FLAG
        mViewBinding.clRoot.isSelected = batchUnlockGear.isSelected
        mViewBinding.tvChapterNum.text = batchUnlockGear.unlockNum.toString()
    }

    interface BatchUnlockGearActionListener : ActionListener {
        fun onGearChecked(gear: BatchUnlockGear)
    }
}