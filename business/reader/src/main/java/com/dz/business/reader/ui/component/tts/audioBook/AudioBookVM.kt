package com.dz.business.reader.ui.component.tts.audioBook

import CoroutineUtils
import androidx.lifecycle.viewModelScope
import com.dz.business.base.data.bean.StrategyInfo
import com.dz.business.base.helper.FloatWindowManage.Companion.AUDIO_TYPE
import com.dz.business.base.livedata.CommLiveData
import com.dz.business.base.network.BBaseNetWork
import com.dz.business.base.reader.ReaderMR
import com.dz.business.base.reader.data.MenuInfoVO
import com.dz.business.base.reader.data.MenuItemInfoVO
import com.dz.business.base.reader.data.NovelTimbreVo
import com.dz.business.base.reader.intent.AudioBookIntent
import com.dz.business.base.shelf.ShelfME
import com.dz.business.base.theatre.data.BookInfoVo
import com.dz.business.base.ui.component.status.Status
import com.dz.business.base.vm.PageVM
import com.dz.business.reader.DataRepository
import com.dz.business.reader.R
import com.dz.business.reader.audio.TtsPlayer
import com.dz.business.reader.data.BookOpenBean
import com.dz.business.reader.data.NovelSpeedVo
import com.dz.business.reader.data.TimerListItem
import com.dz.business.reader.data.TtsConfigDataVo
import com.dz.business.reader.network.ReaderNetwork
import com.dz.business.reader.repository.entity.AudioBookEntity
import com.dz.business.reader.repository.entity.AudioChapterEntity
import com.dz.business.reader.ui.page.AudioOpener
import com.dz.foundation.base.manager.task.TaskManager
import com.dz.foundation.base.module.AppModule
import com.dz.foundation.base.utils.LogUtil
import com.dz.foundation.network.onError
import com.dz.foundation.network.onResponse
import com.dz.platform.common.toast.ToastManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class AudioBookVM : PageVM<AudioBookIntent>(), AudioBookVMContract {

    companion object {
        const val TAG: String = "TtsDetailVM"
    }

    //章节数据
    private var bookSource: String? = null

    //进入阅读时的数据
    override var bookData = CommLiveData<AudioBookData>()
    override var chapterData = CommLiveData<AudioChapterEntity>()
    override var recommendNovelList = CommLiveData<List<BookInfoVo>?>()
    override var recommendStatus = CommLiveData<Int>()

    var novelBookEntity: AudioBookEntity? = null
    private var ttsConfig: TtsConfigDataVo? = null

    //"是否在书架上：0-否；1-是"
    override var isShelf = CommLiveData<Int>()

    override fun loadData() {
        super.loadData()
        val bookId = routeIntent?.bookId ?: ""

        LogUtil.d(TAG, "initData bookId=${routeIntent?.bookId}")
        this.bookSource = routeIntent?.bookSource
        CoroutineUtils.getCoroutineScope().launch(Dispatchers.IO) {
            var novelBookInfo = DataRepository.audioDao().queryByBid(bookId)
            withContext(Dispatchers.Main) {
                LogUtil.d(
                    TAG,
                    "getBookData novelBookInfo isShelf=${isShelf.value}"
                )

                novelBookInfo?.let {
                    novelBookEntity = it
                    LogUtil.d("打印", "刷新数据库书架：loadData=${isShelf.value}")
                    bookData.value = AudioBookData(
                        bookId = it.bid ?: "",
                        bookCover = it.coverurl ?: "",
                        bookName = it.book_name ?: "",
                        isShelf = isShelf.value == 1,
                    )
                }
            }
        }

        loadBookInfo()
        //更新 tts 配置
        ttsConfig = TtsPlayer.instance.ttsConfig
        updateTtsUserConfig()


        reloadRecommendData(bookId)
    }


    override fun onBookOpen(bookOpenBean: BookOpenBean) {
        LogUtil.d(
            TAG,
            "onBookOpen bookOpenBean=${bookOpenBean},bookInfo=${bookOpenBean.bookInfo?.toJson()} TtsPlayer.instance.ttsConfig=${TtsPlayer.instance.ttsConfig?.speedList} ${TtsPlayer.instance.ttsConfig?.timeList}"
        )
        bookData.value = AudioBookData(
            bookId = bookOpenBean.bookInfo?.bookId ?: "",
            bookCover = bookOpenBean.bookInfo?.coverWap ?: "",
            bookName = bookOpenBean.bookInfo?.bookName ?: "",
            isShelf = bookOpenBean.onTheShelf == 1,
        )
        //更新 tts 配置
        ttsConfig = TtsPlayer.instance?.ttsConfig
        updateTtsUserConfig()
    }


    private fun updateTtsUserConfig() {
        //倒计时
        val timeConfig = TtsPlayer.instance.timerPresenter.timerFullLength
        ttsConfig?.timeList?.forEach { item ->
            item.isDefault = if (item.time == timeConfig) 1 else 0
        }

        //语速
        val speed = TtsPlayer.instance.speedPresenter.speed
        ttsConfig?.speedList?.forEach { item ->
            item.isDefault = if (item.speed == speed) 1 else 0
        }
    }


    override fun onClockMenuClick() {
        val menuList = ttsConfig?.timeList?.map { item ->
            MenuItemInfoVO(
                name = item.desc, value = item.time.toString(), isChecked = item.isDefault == 1
            )
        } ?: mutableListOf()
        if (menuList.isEmpty()) {
            ToastManager.showToast("暂无可用配置")
            return
        }
        //显示弹窗
        ReaderMR.get().ttsMenuDialog().apply {
            menuData = MenuInfoVO(
                title = "定时设置", menuInfo = menuList
            )
        }.onSure { menuInfo ->
            var target: TimerListItem? = null
            ttsConfig?.timeList?.forEach { item ->
                if (item.time.toString() == menuInfo?.value) {
                    item.isDefault = 1
                    target = item
                } else {
                    item.isDefault = 0
                }
            }

            target?.let {
                // 立即生效，开始倒计时
                if (it.time != 0) {
                    ToastManager.showToast(
                        "听书模式将在${it.time}分钟后关闭"
                    )
                    TtsPlayer.instance.timerPresenter.startTimerToClose(it.time)
                } else {
                    ToastManager.showToast(AppModule.getApplication().resources.getString(R.string.reader_closed_timer))
                    TtsPlayer.instance.timerPresenter.cancelTimerToClose(true)
                }

            }

        }.start()
    }

    override fun onSpeedMenuClick() {
        val menuList = ttsConfig?.speedList?.map { item ->
            MenuItemInfoVO(
                name = item.speedName,
                value = item.speed.toString(),
                isChecked = item.isDefault == 1
            )
        } ?: mutableListOf()
        if (menuList.isEmpty()) {
            ToastManager.showToast("暂无可用配置")
            return
        }
        //显示弹窗
        ReaderMR.get().ttsMenuDialog().apply {
            menuData = MenuInfoVO(
                title = "语速", menuInfo = menuList
            )
        }.onSure { menuInfo ->
            //更新选中态
            var targetSpeed: NovelSpeedVo? = null
            ttsConfig?.speedList?.forEach { item ->
                if (item.speed.toString() == menuInfo?.value) {
                    item.isDefault = 1
                    targetSpeed = item
                } else {
                    item.isDefault = 0
                }
            }

            //切换倍速
            targetSpeed?.let {
                TtsPlayer.instance.speedPresenter.onSpeedChanged(it.speed)
            }

        }.start()

    }

    override fun onChapterListMenuClick() {
        ReaderMR.get().readerCatalog().apply {
            bookId = novelBookEntity?.bid.toString()
            bookName = novelBookEntity?.book_name.toString()
            chapterId = chapterData.value?.cid.toString()
            chapterName = chapterData.value?.chapter_name
            chapterIndex = chapterData.value?.chapter_num
            isAddShelf = isShelf.value == 1
            routeSource = bookSource
            referrer = ReaderMR.AUDIO_BOOK
            audioType = AUDIO_TYPE
        }.overridePendingTransition(
            R.anim.common_ac_none, R.anim.common_ac_none
        ).start()
    }

    override fun onShelfMenuClick() {
        if (novelBookEntity == null) {
            ToastManager.showToast("小说信息获取失败")
            return
        }
        LogUtil.d("打印", "刷新数据库书架：onShelfMenuClick=${isShelf.value}")
        if (isShelf.value != 1) {
            addFavorites()
        } else {
            deleteFavorites()
        }
    }

    /**
     * 取消加入书架
     */
    private fun deleteFavorites() {
        val list = mutableListOf<String>()
        list.add(novelBookEntity?.bid ?: "")
        BBaseNetWork.get().deleteFavorites().setParams(
            bookIds = list,
            source = "12",
            tierPlaySource = null,
        ).onResponse {
            it.data?.run {
                if (status == 1) {
                    ToastManager.showToast(R.string.reader_delete_shelf_success)
                    novelBookEntity?.let { bookEntity ->
                        isShelf.value = 0
                        LogUtil.d(
                            "打印",
                            "刷新数据库书架：deleteFavorites=${isShelf.value}"
                        )
                        TaskManager.ioTask {
                            novelBookEntity?.let {
                                DataRepository.audioDao().insertOrUpdateBooks(bookEntity)
                            }
                        }
                        bookData.value = AudioBookData(
                            bookId = bookEntity.bid ?: "",
                            bookCover = bookEntity.coverurl ?: "",
                            bookName = bookEntity.book_name ?: "",
                            isShelf = false,
                        )
                    }
                }
            }
        }.onError {
            ToastManager.showToast("加入书架失败")
        }.doRequest()


    }

    /**
     * 加入书架
     */
    private fun addFavorites() {
        BBaseNetWork.get().addFavorites().setParams(
            bookId = novelBookEntity?.bid ?: "",
            chapterId = chapterData.value?.cid,
            source = "12",
            omap = StrategyInfo(),
            tierPlaySource = null,
            followSource = null
        ).onResponse {
            it.data?.run {
                if (status == 1) {
                    ToastManager.showToast(R.string.reader_add_shelf_success)

                    novelBookEntity?.let { bookEntity ->
                        isShelf.value = 1
                        TaskManager.ioTask {
                            DataRepository.audioDao().insertOrUpdateBooks(bookEntity)
                        }
                        bookData.value = AudioBookData(
                            bookId = bookEntity.bid ?: "",
                            bookCover = bookEntity.coverurl ?: "",
                            bookName = bookEntity.book_name ?: "",
                            isShelf = true,
                        )
                    }
                }
            }
        }.onError {
            ToastManager.showToast("加入书架失败")
        }.doRequest()


    }


    override fun onChapterChanged(novelInfo: AudioChapterEntity?) {
        novelInfo?.let { chapterInfo ->
            chapterData.value = chapterInfo
        }

    }

    override fun onSeekBarProgressChange(progress: Int, uiId: String) {
        if (TtsPlayer.instance.isRunning()) {
            TtsPlayer.instance.progressPresenter.seekToProgress(progress)
        } else {
            TtsPlayer.instance.startTTS(
                uiId,
                novelBookEntity?.bid,
                chapterData.value?.cid,
                novelBookEntity?.coverurl ?: "",
                progress,
                AUDIO_TYPE,
            )
        }

    }

    override fun reloadRecommendData(bookId: String) {
        //加载推荐数据
        ReaderNetwork.get().ttsRecommendData().setParams(bookId).onResponse {
            if (it.data?.novelList?.isEmpty() == true) {
                recommendStatus.value = Status.EMPTY
            } else {
                recommendStatus.value = Status.NORMAL
                recommendNovelList.value = it.data?.novelList
            }
        }.onError {
            ToastManager.showToast("网络异常，请稍后重试")
            recommendStatus.value = Status.NET_ERROR
        }.doRequest()

    }

    override fun getSelectedSpeed(): NovelSpeedVo? {
        return ttsConfig?.speedList?.find { it.isDefault == 1 }
    }

    /**
     * 刷新是否在书架状态 ，用户发生变更时调用
     */
    override fun refreshAddShelfStatus() {
        ReaderNetwork.get()
            .getAddShelfStatus()
            .paramBookId(routeIntent?.bookId ?: "")
            .onResponse {
                it.data?.let { addShelfStatus ->
                    ShelfME.get().addAudioToFavorite()
                        .post(AudioBookEntity(routeIntent?.bookId ?: ""))
                }
            }.doRequest()
    }

    /**
     * 刷新播放进度
     */
    override fun refreshChapterTime(it: Int) {
        chapterData?.value?.let { e ->
            if (it > 0 && TtsPlayer.instance.isPlaying()) {
                var progress = it
                if ((it + 3) >= TtsPlayer.instance.chapterParagraphCount) {
                    progress = TtsPlayer.instance.chapterParagraphCount - 3
                }
                viewModelScope.launch(Dispatchers.IO) {
                    e?.ptime = (progress * 1000).toLong()
                    LogUtil.d(
                        "打印",
                        "当前章节:refreshChapterTime e?.ptime=${e?.ptime}   ${e.book_name}"
                    )
                    DataRepository.audioChapterDao().insertOrUpdate(e)
                    AudioOpener.instance.getCurrentAudioChapterInfo()?.ptime = e.ptime
                }
            }
        }
    }

    //请求书籍信息
    fun loadBookInfo() {
        ReaderNetwork.get().getAudioInfo().setParams(
            routeIntent?.bookId ?: "",
            routeIntent?.bookSource ?: "",
            false,
        ).onResponse {
            it.data?.run {
                isShelf.value = this.onTheShelf
            }
        }.doRequest()
    }
}