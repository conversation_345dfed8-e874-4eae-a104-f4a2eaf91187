package com.dz.business.reader.ui.component.menu

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import androidx.lifecycle.LifecycleOwner
import com.dz.business.reader.R
import com.dz.business.reader.ReaderInsideEvents
import com.dz.business.reader.databinding.ReaderMenuSwitchChapterCompBinding
import com.dz.business.reader.utils.ReaderConfigUtil
import com.dz.foundation.ui.view.custom.ActionListener
import com.dz.foundation.ui.view.custom.ActionListenerOwner
import com.dz.platform.common.base.ui.component.UIConstraintComponent

/**
 *@Author: shidz
 *@Date: 2022/10/26 19:47
 *@Description: 切换章节
 *@Version:1.0
 */
class MenuSwitchChapterComp @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : UIConstraintComponent<ReaderMenuSwitchChapterCompBinding, Any>(
    context,
    attrs,
    defStyleAttr
),
    ActionListenerOwner<MenuSwitchChapterComp.ViewActionListener> {

    private var isPreChapterTouchDown = false//上一章按钮是否按下
    private var isNextChapterTouchDown = false//下一章按钮是否按下
    private var isCatalogTouchDown = false//目录按钮是否按下

    override fun initData() {

    }

    override fun initView() {
        setMode()
    }

    override fun initListener() {
        mViewBinding.run {
            clPreChapter.registerClickAction {
                mActionListener?.onPreChapterClick()
            }
            clPreChapter.setOnTouchListener { v, event ->
                when (event?.action) {
                    MotionEvent.ACTION_DOWN -> {
                        if (!isPreChapterTouchDown) {
                            isPreChapterTouchDown = true
                            ivPreIc.alpha = 0.5F
                            tvPre.alpha = 0.5F
                        }
                    }
                    MotionEvent.ACTION_UP -> {
                        if (isPreChapterTouchDown) {
                            isPreChapterTouchDown = false
                            ivPreIc.alpha = 1F
                            tvPre.alpha = 1F
                        }
                    }
                }
                false
            }
            clNextChapter.registerClickAction {
                mActionListener?.onNextChapterClick()
            }
            clNextChapter.setOnTouchListener { v, event ->
                when (event?.action) {
                    MotionEvent.ACTION_DOWN -> {
                        if (!isNextChapterTouchDown) {
                            isNextChapterTouchDown = true
                            tvNext.alpha = 0.5F
                            ivNextIc.alpha = 0.5F
                        }
                    }
                    MotionEvent.ACTION_UP -> {
                        if (isNextChapterTouchDown) {
                            isNextChapterTouchDown = false
                            tvNext.alpha = 1F
                            ivNextIc.alpha = 1F
                        }
                    }
                }
                false
            }
            clCatalog.registerClickAction {
                mActionListener?.onCatalogClick()
            }
            clCatalog.setOnTouchListener { v, event ->
                when (event?.action) {
                    MotionEvent.ACTION_DOWN -> {
                        if (!isCatalogTouchDown) {
                            isCatalogTouchDown = true
                            tvCatalog.alpha = 0.5F
                            ivCatalogIc.alpha = 0.5F
                        }
                    }
                    MotionEvent.ACTION_UP -> {
                        if (isCatalogTouchDown) {
                            isCatalogTouchDown = false
                            tvCatalog.alpha = 1F
                            ivCatalogIc.alpha = 1F
                        }
                    }
                }
                false
            }
        }
    }

    override fun subscribeEvent(lifecycleOwner: LifecycleOwner, lifecycleTag: String) {
        ReaderInsideEvents.get().colorStyleChanged().observe(lifecycleOwner, lifecycleTag) {
            setMode()
        }
    }

    private fun setMode() {
        if (ReaderConfigUtil.isNightMode()) setNightMode() else setDayMode()
    }

    private fun setDayMode() {
        mViewBinding.run {
            ivPreIc.setImageResource(R.drawable.reader_ic_previous_chapter_day)
            tvPre.setTextColor(getColor(R.color.reader_title_text_color_day))
            ivNextIc.setImageResource(R.drawable.reader_ic_next_chapter_day)
            tvNext.setTextColor(getColor(R.color.reader_title_text_color_day))
            ivCatalogIc.setImageResource(R.drawable.reader_ic_catalog)
            tvCatalog.setTextColor(getColor(R.color.reader_title_text_color_day))
        }
    }

    private fun setNightMode() {
        mViewBinding.run {
            ivPreIc.setImageResource(R.drawable.reader_ic_previous_chapter_night)
            tvPre.setTextColor(getColor(R.color.reader_title_text_color_night))
            ivNextIc.setImageResource(R.drawable.reader_ic_next_chapter_night)
            tvNext.setTextColor(getColor(R.color.reader_title_text_color_night))
            ivCatalogIc.setImageResource(R.drawable.reader_ic_catalog_night)
            tvCatalog.setTextColor(getColor(R.color.reader_title_text_color_night))
        }
    }

    override var mActionListener: ViewActionListener? = null

    interface ViewActionListener : ActionListener {
        fun onPreChapterClick()
        fun onNextChapterClick()
        fun onCatalogClick()
    }
}