package com.dz.business.reader.ui.component.block

import android.content.Context
import android.util.AttributeSet
import android.view.View
import androidx.lifecycle.LifecycleOwner
import androidx.recyclerview.widget.RecyclerView
import com.dz.business.reader.R
import com.dz.business.reader.ReaderInsideEvents
import com.dz.business.reader.databinding.ReaderChapterEndBookTagCompBinding
import com.dz.business.reader.utils.ReaderConfigUtil
import com.dz.foundation.base.utils.dp
import com.dz.foundation.ui.view.recycler.DzRecyclerView
import com.dz.platform.common.base.ui.component.UIConstraintComponent


/**
 * <AUTHOR>
 * @description: 书籍标签
 * @date :2022/11/16 14:28
 */
class ChapterEndBookTagComp : UIConstraintComponent<ReaderChapterEndBookTagCompBinding, String> {
    @JvmOverloads
    constructor(
        context: Context,
        attrs: AttributeSet? = null,
        defStyleAttr: Int = 0
    ) : super(context, attrs, defStyleAttr)


    override fun initData() {
    }

    override fun initView() {
    }

    override fun initListener() {
    }

    override fun bindData(data: String?) {
        super.bindData(data)
        data?.run {
            mViewBinding.tvContent.text = this
            refreshViewColor()
        }
    }

    override fun subscribeEvent(lifecycleOwner: LifecycleOwner, lifecycleTag: String) {
        ReaderInsideEvents.get().colorStyleChanged().observe(lifecycleOwner, lifecycleTag) {
            refreshViewColor()
        }
    }

   private fun refreshViewColor(){
        if (ReaderConfigUtil.isNightMode()) {
            mViewBinding.tvContent.setShapeBackground(
                solidColor = getColor(R.color.reader_color_2E2E2E),
                radius = 4f.dp
            )
            mViewBinding.tvContent.setTextColor(getColor(R.color.reader_color_8A8A8A))
        } else {
            mViewBinding.tvContent.setShapeBackground(
                solidColor = getColor(R.color.reader_color_30_ffffff),
                radius = 4f.dp
            )
            mViewBinding.tvContent.setTextColor(getColor(R.color.reader_color_40_000000))
        }
    }

    override fun onCreateRecyclerViewItem(
        parent: DzRecyclerView?,
        itemView: View?
    ): RecyclerView.LayoutParams {
        return RecyclerView.LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT)
    }

}