package com.dz.business.reader.ui.component.menu

import android.content.Context
import android.util.AttributeSet
import androidx.lifecycle.LifecycleOwner
import com.dz.business.reader.R
import com.dz.business.reader.ReaderInsideEvents
import com.dz.business.reader.databinding.ReaderMenuBottomActionCompBinding
import com.dz.business.reader.utils.ReaderConfigUtil
import com.dz.foundation.ui.view.custom.ActionListener
import com.dz.foundation.ui.view.custom.ActionListenerOwner
import com.dz.platform.common.base.ui.component.UIConstraintComponent

/**
 *作者: shidz
 *创建时间 :  2023/9/26 15:53
 *功能描述: 阅读器菜单底部 功能按钮
 * 包括：目录、字号、背景、翻页
 *
 */
class MenuBottomActionComp @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : UIConstraintComponent<ReaderMenuBottomActionCompBinding, Any>(
    context,
    attrs,
    defStyleAttr
),
    ActionListenerOwner<MenuBottomActionComp.ViewActionListener> {

    override fun initData() {

    }

    override fun initView() {
        setMode()
    }

    override fun initListener() {
        mViewBinding.run {
            clCatalog.registerClickAction {
                resetSelect()
                mActionListener?.onCatalogClick()
            }
            clFontSize.registerClickAction {
                clFontSize.isSelected = true
                clBg.isSelected = false
                clTurnPage.isSelected = false
                mActionListener?.onFontSizeClick( clFontSize.isSelected )
                setMode()
            }
            clBg.registerClickAction {
                clBg.isSelected = true
                clFontSize.isSelected = false
                clTurnPage.isSelected = false
                mActionListener?.onBgClick(clBg.isSelected)
                setMode()
            }
            clTurnPage.registerClickAction {
                clTurnPage.isSelected = true
                clFontSize.isSelected = false
                clBg.isSelected = false
                mActionListener?.onTurnPageClick( clTurnPage.isSelected)
                setMode()
            }
        }
    }

    override fun subscribeEvent(lifecycleOwner: LifecycleOwner, lifecycleTag: String) {
        ReaderInsideEvents.get().colorStyleChanged().observe(lifecycleOwner, lifecycleTag) {
            setMode()
        }
    }

    private fun setMode() {
        if (ReaderConfigUtil.isNightMode())
            setNightMode()
        else
            setDayMode()
    }

    private fun setDayMode() {
        mViewBinding.run {
            ivCatalogIc.setImageResource(R.drawable.reader_ic_catalog)
            tvCatalog.setTextColor(getColor(R.color.reader_E6000000))
            if (clFontSize.isSelected) {
                ivFontSizeIc.setImageResource(R.drawable.reader_ic_font_select)
                tvFontSize.setTextColor(getColor(R.color.reader_FFA74E41))
            } else {
                ivFontSizeIc.setImageResource(R.drawable.reader_ic_font)
                tvFontSize.setTextColor(getColor(R.color.reader_E6000000))
            }
            if (clBg.isSelected) {
                ivBgIc.setImageResource(R.drawable.reader_ic_bg_select)
                tvBg.setTextColor(getColor(R.color.reader_FFA74E41))
            } else {
                ivBgIc.setImageResource(R.drawable.reader_ic_bg)
                tvBg.setTextColor(getColor(R.color.reader_E6000000))
            }
            if (clTurnPage.isSelected) {
                ivTurnPageIc.setImageResource(R.drawable.reader_ic_turn_page_select)
                tvTurnPage.setTextColor(getColor(R.color.reader_FFA74E41))
            } else {
                ivTurnPageIc.setImageResource(R.drawable.reader_ic_turn_page)
                tvTurnPage.setTextColor(getColor(R.color.reader_E6000000))
            }
        }
    }

    private fun setNightMode() {
        mViewBinding.run {
            ivCatalogIc.setImageResource(R.drawable.reader_ic_catalog_night)
            tvCatalog.setTextColor(getColor(R.color.reader_DBFFFFFF))
            if (clFontSize.isSelected) {
                ivFontSizeIc.setImageResource(R.drawable.reader_ic_font_select_night)
                tvFontSize.setTextColor(getColor(R.color.reader_FFA74E41))
            } else {
                ivFontSizeIc.setImageResource(R.drawable.reader_ic_font_night)
                tvFontSize.setTextColor(getColor(R.color.reader_DBFFFFFF))
            }
            if (clBg.isSelected) {
                ivBgIc.setImageResource(R.drawable.reader_ic_bg_select_night)
                tvBg.setTextColor(getColor(R.color.reader_FFA74E41))
            } else {
                ivBgIc.setImageResource(R.drawable.reader_ic_bg_night)
                tvBg.setTextColor(getColor(R.color.reader_DBFFFFFF))
            }
            if (clTurnPage.isSelected) {
                ivTurnPageIc.setImageResource(R.drawable.reader_ic_turn_page_select_night)
                tvTurnPage.setTextColor(getColor(R.color.reader_FFA74E41))
            } else {
                ivTurnPageIc.setImageResource(R.drawable.reader_ic_turn_page_night)
                tvTurnPage.setTextColor(getColor(R.color.reader_DBFFFFFF))
            }
        }
    }

    /**
     * 重置选中状态
     */
    fun resetSelect() {
        mViewBinding.run {
            clFontSize.isSelected = false
            clBg.isSelected = false
            clTurnPage.isSelected = false
            setMode()
        }
    }

    override var mActionListener: ViewActionListener? = null

    interface ViewActionListener : ActionListener {
        fun onFontSizeClick(selected:Boolean)
        fun onBgClick(selected:Boolean)
        fun onCatalogClick()
        fun onTurnPageClick(selected: Boolean)
    }
}