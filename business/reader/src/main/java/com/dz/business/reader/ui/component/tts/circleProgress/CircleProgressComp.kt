package com.dz.business.reader.ui.component.tts.circleProgress

import android.animation.ValueAnimator
import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.Rect
import android.graphics.RectF
import android.util.AttributeSet
import android.view.View
import androidx.core.content.withStyledAttributes
import androidx.core.graphics.toColorInt
import com.dz.business.reader.R

/**
 * 圆形进度条
 */

class CircleProgressComp @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    private var progress = 0
    private var maxProgress = 100
    private var progressColor = Color.WHITE
    private var backgroundColor = Color.parseColor("#80FFFFFF")
    private var progressDuration = 400
    private var centerIconRes: Int = R.drawable.reader_ic_tts_float_play // 默认图标
    private var centerIconWidth: Int = 0 // px
    private var centerIconHeight: Int = 0 // px

    private val paint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val rectF = RectF()
    private var iconBitmap: Bitmap? = null

    init {
        attrs?.let {
            context.withStyledAttributes(it, R.styleable.readerCircleProgressComp) {
                progress = getInt(R.styleable.readerCircleProgressComp_progress, 0)
                maxProgress = getInt(R.styleable.readerCircleProgressComp_maxProgress, 100)
                progressColor =
                    getColor(R.styleable.readerCircleProgressComp_progressColor, Color.WHITE)
                backgroundColor = getColor(
                    R.styleable.readerCircleProgressComp_backgroundColor,
                    "#80FFFFFF".toColorInt()
                )
                centerIconRes =
                    getResourceId(
                        R.styleable.readerCircleProgressComp_centerIcon,
                        R.drawable.reader_ic_tts_float_play
                    )
                progressDuration =
                    getInt(R.styleable.readerCircleProgressComp_progressDuration, 400)

                centerIconWidth = getDimensionPixelSize(
                    R.styleable.readerCircleProgressComp_centerIconWidth, 0
                )
                centerIconHeight = getDimensionPixelSize(
                    R.styleable.readerCircleProgressComp_centerIconHeight, 0
                )
            }
        }
        iconBitmap = BitmapFactory.decodeResource(resources, centerIconRes)
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        val strokeWidth = width / 12f
        paint.strokeWidth = strokeWidth
        paint.style = Paint.Style.STROKE

        val radius = (width.coerceAtMost(height) - strokeWidth) / 2f
        val cx = width / 2f
        val cy = height / 2f
        rectF.set(cx - radius, cy - radius, cx + radius, cy + radius)

        // 画未完成背景
        paint.color = backgroundColor
        canvas.drawArc(rectF, 0f, 360f, false, paint)

        // 画已完成进度
        paint.color = progressColor
        val sweepAngle = 360f * progress / maxProgress
        canvas.drawArc(rectF, -90f, sweepAngle, false, paint)

        // 画中间图片
        iconBitmap?.let {
            val iconW = if (centerIconWidth > 0) centerIconWidth else width / 3
            val iconH = if (centerIconHeight > 0) centerIconHeight else height / 3
            val left = (width - iconW) / 2
            val top = (height - iconH) / 2
            val dstRect = Rect(left, top, left + iconW, top + iconH)
            canvas.drawBitmap(it, null, dstRect, null)
        }
    }

    fun setProgress(newProgress: Int, animate: Boolean = true) {
        val validProgress = newProgress.coerceIn(0, maxProgress)
        if (animate) {
            val animator = ValueAnimator.ofInt(progress, validProgress)
            animator.duration = progressDuration.toLong()
            animator.addUpdateListener {
                progress = it.animatedValue as Int
                invalidate()
            }
            animator.start()
        } else {
            progress = validProgress
            invalidate()
        }
    }

    fun setProgressColor(color: Int) {
        progressColor = color
        invalidate()
    }

    fun setCenterImg(resId: Int) {
        iconBitmap = BitmapFactory.decodeResource(resources, resId)
        invalidate()
    }

    override fun setBackgroundColor(color: Int) {
        backgroundColor = color
        invalidate()
    }

    fun setCenterIconSize(widthPx: Int, heightPx: Int) {
        centerIconWidth = widthPx
        centerIconHeight = heightPx
        invalidate()
    }

    fun setCenterIcon(resId: Int) {
        centerIconRes = resId
        iconBitmap = BitmapFactory.decodeResource(resources, resId)
        invalidate()
    }
}