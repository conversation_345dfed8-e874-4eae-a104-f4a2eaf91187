package com.dz.business.reader.ui.component.ad


import android.content.Context
import android.util.AttributeSet
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.LifecycleOwner
import com.dz.business.reader.R
import com.dz.business.reader.ReaderInsideEvents
import com.dz.business.reader.ad.ReaderAdManager
import com.dz.business.reader.ad.ReaderFeedAd
import com.dz.business.reader.ad.callback.ReaderAdActionCallback
import com.dz.business.reader.ad.callback.ReaderAdLoadCallback
import com.dz.business.reader.data.ReaderAdConfigInfo
import com.dz.business.reader.databinding.ReaderBookAdPageCompBinding
import com.dz.business.reader.ui.page.ReaderActivity
import com.dz.business.reader.utils.ReaderAdUtil
import com.dz.business.reader.utils.ReaderConfigUtil
import com.dz.business.track.events.sensor.AdTE
import com.dz.foundation.base.utils.LogUtil
import com.dz.foundation.base.utils.ScreenUtil
import com.dz.foundation.base.utils.UIIdentify
import com.dz.foundation.base.utils.dp
import com.dz.foundation.base.utils.dp2px
import com.dz.foundation.ui.utils.WindowAdapter
import com.dz.foundation.ui.widget.getContainerActivity
import com.dz.platform.ad.AdManager
import com.dz.platform.common.base.ui.component.UIConstraintComponent
import reader.xo.block.Block

/**
 * <AUTHOR>
 * @description:书籍插页广告
 * @date :2023/9/21
 */
class AdReaderPageComp @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : UIConstraintComponent<ReaderBookAdPageCompBinding, ReaderAdConfigInfo.InsertPageAdConfig>(
    context,
    attrs,
    defStyleAttr
) {


    private var adPageTag:String?=null
    fun getAdPageTag(): String {
        if (!adPageTag.isNullOrEmpty()) {
            return adPageTag!!
        }
        val temp =   UIIdentify.getObjId(this)
        adPageTag =temp
        return temp

    }

    private var blockData: Block? = null
    private var mViewWidth = 0
    private var blockHeight: Float = ScreenUtil.getScreenHeight() - 72.dp2px - 32.dp2px

    private var currentFeedAd: ReaderFeedAd? = null

    override fun initData() {
//        setBackgroundColor(getColor(R.color.bbase_FFFF7500_FFBA5500))
    }

    override fun initView() {

        var padding = 0
        var margin = 15.dp
//        var paddingBottom = 35.dp
//        if (WindowAdapter.getWindowSizeType() == WindowAdapter.WindowSizeType.EXPANDED) {
//            padding =
//                (WindowAdapter.getWindowWidth() / 4 + WindowAdapter.getWindowSizeInfo().columnWidth).toInt()
//            paddingBottom = 0
//            margin = 0
//        } else if (WindowAdapter.getWindowSizeType() == WindowAdapter.WindowSizeType.MEDIUM) {
//            padding =
//                2 * WindowAdapter.getWindowSizeInfo().columnWidth.toInt()
//            paddingBottom = 0
//            margin = 0
//        }
        mViewWidth =
            (WindowAdapter.getWindowWidth().toInt() - 2 * padding - 2 * margin)

//        setPadding(padding, 0, padding, 0)
//        mViewBinding.clBottomRoot.setPadding(0, 0, 0, paddingBottom)
    }

    private var lifecycleObserver: LifecycleObserver? = null
    private fun addActivityLifeListener() {
        getContainerActivity()?.let { activity ->
            if (activity is LifecycleOwner) {
                lifecycleObserver = object : DefaultLifecycleObserver {

                    override fun onDestroy(owner: LifecycleOwner) {
                        super.onDestroy(owner)
                        currentFeedAd?.destroy()
                    }

                }
                lifecycleObserver?.let {
                    activity.lifecycle.addObserver(it)
                }

            }
        }
    }

    private fun removeActivityLifeListener() {
        getContainerActivity()?.let { activity ->
            if (activity is LifecycleOwner) {
                lifecycleObserver?.let {
                    activity.lifecycle.removeObserver(it)
                }

            }
        }
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        addActivityLifeListener()
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        removeActivityLifeListener()
    }

    override fun initListener() {

    }

    fun setBlock(block: Block) {
        this.blockData = block
        blockHeight = block.height + 0F
    }

    override fun subscribeEvent(lifecycleOwner: LifecycleOwner, lifecycleTag: String) {
        ReaderInsideEvents.get().colorStyleChanged().observe(lifecycleOwner, lifecycleTag) {
            refreshBackgroundColor()
        }

        ReaderInsideEvents.get().onPageShow().observe(lifecycleOwner, lifecycleTag) { pageInfo ->
            var visibilityStatus = 0
            if (pageInfo.isBlockPage() && (blockData?.id == pageInfo.blockId)) {
                //插页block显示
                visibilityStatus = 1
            }
            onShowStatusChanged(visibilityStatus)
        }
    }


    private var visibilityStatus = 0 //0不显示 1，显示
    private fun onShowStatusChanged(visibilityStatus: Int) {
        if (this.visibilityStatus == visibilityStatus) {
            return
        }
        this.visibilityStatus = visibilityStatus
        if (visibilityStatus == 1) {
            onExposure()
        } else {
            onHidden()
        }
    }

    private fun onHidden() {
        currentFeedAd?.pause()
        LogUtil.e(
            "king-AdReader",
            "插页广告-- ${blockData?.id} 隐藏"
        )
    }

    /**
     * 插页view 曝光
     */
    private fun onExposure() {
        currentFeedAd?.resume()
        LogUtil.e(
            "king-AdReader",
            "插页广告-- ${blockData?.id} 曝光"
        )
        blockData?.let { block ->

            ReaderAdUtil.getInstance().clearOtherAdPageBlock(getAdPageTag(),block)
        }
        val adId = mData?.adId ?: ""
        val blockConfigId = mData?.blockConfigId ?: ""
        LogUtil.d(
            "king-AdReader",
            "senADTrafficReachEvent 解锁广告触发上报流量请求事件埋点 pos=${AdTE.READER_INSERT_PAGE} adId=$adId"
        )
        AdManager.sendTrafficReachLog(AdTE.READER_INSERT_PAGE, adId, blockConfigId)
//        LogUtil.e(
//                "king-AdReader_exposure",
//                "插页广告-- ${blockData?.id} 曝光"
//        )
//        if (!CommInfoUtil.isVip()) {
//            DzTrackEvents.get().operationExposureTE()
//                    .operationPosition(OperationExposureTE.POSITION_READER_INSERT_PAGE_AD)
//                    .operationName(OperationExposureTE.OPERATION_TYPE_AD_READER_INSERT_PAGE)
//                    .track()
//        }
    }


    private fun refreshBackgroundColor() {
        currentFeedAd?.setNightMode(ReaderConfigUtil.isNightMode())
        if (ReaderConfigUtil.isNightMode()) {
            mViewBinding.tvNoticeClick.setTextColor(getColor(R.color.reader_8CFFFFFF))
            mViewBinding.imgRightArrow.setImageResource(R.drawable.reader_ic_ad_tip_arrow_right_night)
        } else {
            mViewBinding.tvNoticeClick.setTextColor(getColor(R.color.reader_8C000000))
            mViewBinding.imgRightArrow.setImageResource(R.drawable.reader_ic_ad_tip_arrow_right)


        }
    }

    private fun getAdImageViewHeight(): Int {
        val ratio: Double = 9.0 / 16.0
        return (mViewWidth * ratio).toInt()
    }


    override fun bindData(data: ReaderAdConfigInfo.InsertPageAdConfig?) {
        LogUtil.d(
            "king-AdReader",
            "bindData blockId = ${blockData?.id} "
        )
        super.bindData(data)
        refreshBackgroundColor()
        loadAd()
    }

    private fun loadAd() {
        currentFeedAd?.let {
            if (!it.hasShown) {
                //
                LogUtil.e("king-AdReader", "未曝光过 不再触发请求新广告")

                return
            }
        }
        mData?.let {
            if (it.isValidAd()) {
                LogUtil.e("king-AdReader", "插页广告--bindData")

                ReaderAdManager.loadInsertPageAdView(
                    mViewBinding.flAdContent, it.getLoadAdParam(
                        mViewWidth, getAdImageViewHeight(),
                        mViewWidth, getAdTemplateHeight(),
                        if (ReaderAdUtil.getInstance().forward == true) " forth" else " back",
                        false
                    ),
                    object : ReaderAdLoadCallback {
                        override fun onLoaded(readerFeedAd: ReaderFeedAd) {
                            LogUtil.e(
                                "king-AdReader",
                                "插页广告--onLoaded tag= ${blockData?.id}"
                            )
                            renderAd(readerFeedAd)


                        }

                        override fun onAdFailed() {
                            LogUtil.e(
                                "king_ad-adListener",
                                "adReaderPageComp onAdFailed:失败去刷新------ "
                            )
                        }
                    }
                )
            } else {
                refreshViewVisible(isAdContentVis = false)
            }

        }
    }

    /**
     * 渲染广告
     */
    private fun renderAd(readerFeedAd: ReaderFeedAd) {
        currentFeedAd?.destroy()
        currentFeedAd = readerFeedAd
        LogUtil.e(
            "king-AdReader",
            "插页广告--renderAd tag= ${blockData?.id}"
        )
        val actionCallback = object : ReaderAdActionCallback {

            override fun onRenderSuccess() {
                LogUtil.e(
                    "king_ad-adListener",
                    "adReaderPageComp onRenderSuccess: "
                )
                refreshViewVisible(isAdContentVis = true)
            }

            override fun onShow() {
                LogUtil.e("king_ad-adListener", "adReaderPageComp onShow: ")
            }

            override fun onClick() {
                LogUtil.e("king_ad-adListener", "adReaderPageComp onShow: ")
            }

            override fun onClose() {
                LogUtil.e("king_ad-adListener", "adReaderPageComp onShow: ")
                refreshViewVisible(isAdContentVis = false)
                turnNextPage()

                currentFeedAd?.destroy()
                currentFeedAd = null
            }

            override fun onRenderFail() {
                refreshViewVisible(isAdContentVis = false)
                LogUtil.e("king_ad-adListener", "adReaderPageComp onRenderFail: ")

            }

        }
        readerFeedAd.render(mViewBinding.flAdContent, actionCallback)
    }

    private fun getAdTemplateHeight(): Int {
        return (blockHeight - 65.dp2px).toInt()
    }

    /**
     * 翻到下一页
     */
    private fun turnNextPage() {
        getContainerActivity()?.let {
            if (it is ReaderActivity) {
                it.turnNextPage()
            }
        }
    }



    private fun refreshViewVisible(isAdContentVis: Boolean) {
        mViewBinding.flAdContent.visibility = if (isAdContentVis) VISIBLE else GONE
    }

}