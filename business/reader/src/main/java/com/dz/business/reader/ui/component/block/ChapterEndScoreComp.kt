package com.dz.business.reader.ui.component.block

import android.content.Context
import android.util.AttributeSet
import android.util.TypedValue
import androidx.lifecycle.LifecycleOwner
import com.dz.business.base.data.bean.UserTacticInfoBean
import com.dz.business.base.vm.getViewModel
import com.dz.business.reader.data.ScoreBannerInfo
import com.dz.business.reader.R
import com.dz.business.reader.ReaderInsideEvents
import com.dz.business.reader.databinding.ReaderChapterEndScoreCompBinding
import com.dz.business.reader.ui.page.ReaderActivity
import com.dz.business.reader.utils.ReaderConfigUtil
import com.dz.business.reader.vm.ChapterEndScoreCompVM
import com.dz.business.track.events.DzTrackEvents
import com.dz.business.track.events.sensor.PositionActionTE
import com.dz.business.track.trackProperties
import com.dz.foundation.base.module.AppModule
import com.dz.foundation.base.utils.LogUtil
import com.dz.foundation.base.utils.NetWorkUtil
import com.dz.foundation.base.utils.UIIdentify
import com.dz.foundation.base.utils.dp
import com.dz.foundation.ui.widget.getContainerActivity
import com.dz.platform.common.base.ui.component.UIConstraintComponent
import com.dz.platform.common.toast.ToastManager


/**
 *@Author: shidz
 *@Date: 2022/11/15 12:36
 *@Description:章末评分组件
 *@Version:1.0
 */
class ChapterEndScoreComp @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : UIConstraintComponent<ReaderChapterEndScoreCompBinding, ScoreBannerInfo>(
    context,
    attrs,
    defStyleAttr
), ChapterEndComp.ChapterEndItem {
    private var mViewModel: ChapterEndScoreCompVM? = null
    override fun initAttrs(context: Context?, attrs: AttributeSet?, defStyleAtt: Int) {
        mViewModel = getViewModel(ChapterEndScoreCompVM::class.java)
    }

    override fun initData() {

    }

    override fun initView() {
        trackConfig()
    }

    private fun trackConfig() {
        mViewBinding.rlSubmitRoot.trackProperties(ignoreAutoTrack = true)
    }


    private fun onScoreShow() {
        LogUtil.d("chapterEnd", "onScoreShow")
        getContainerActivity()?.let { activity ->
            if (activity is ReaderActivity) {
                activity.onScoreShow()
            }
        }
    }

    private fun onScore(score: Int) {
        LogUtil.d("chapterEnd", "onScore=$score")
        getContainerActivity()?.let { activity ->
            if (activity is ReaderActivity) {
                activity.onScore(score)
            }
        }
    }


    private var ratingCount = 0
    override fun initListener() {
        mViewBinding.ratingbar.setOnRatingChangeListener {
            ratingCount = it.toInt() * 2
            mViewBinding.tvScore.text = "${ratingCount}.0分"
            mViewBinding.rlSubmitRoot.visibility = if (mData?.hasScored == true) GONE else VISIBLE
        }

        mViewBinding.rlSubmitRoot.registerClickAction {
            if (mViewBinding.loading.visibility == VISIBLE) {
                return@registerClickAction
            }
            if (!NetWorkUtil.isNetConnected(AppModule.getApplication())) {
                ToastManager.showToast("网络异常，请稍后重试")
                return@registerClickAction
            }
            mViewBinding.loading.visibility = VISIBLE
            mViewBinding.loading.playAnimation()
            mViewBinding.ratingbar.setmClickable(false)
            mViewModel?.doScoreRequest(mData?.bookId, ratingCount)
            mViewBinding.rlSubmitRoot.trackProperties(elementParam = ratingCount)
            onScore(ratingCount)
            doTrack(PositionActionTE.ACTION_CLICK)
        }
    }

    override fun subscribeObserver(lifecycleOwner: LifecycleOwner) {
        mViewModel?.mScoreLd?.observe(lifecycleOwner) {
            if (it != null) {
                if (it.status == 1) {
                    mData?.let { info ->

                        info.hasScored = true
                        info.score = ratingCount
                        setViewData(info)

                        LogUtil.d(
                            "chapterEnd",
                            "submit ${UIIdentify.getObjId(info)} +hasScored=" + info.hasScored
                        )
                    }
                }
                mViewBinding.loading.visibility = GONE
                mViewBinding.loading.cancelAnimation()
                ToastManager.showToast(it.tips)
            } else {
                mViewBinding.loading.visibility = GONE
                mViewBinding.loading.cancelAnimation()
            }

        }
    }

    override fun subscribeEvent(lifecycleOwner: LifecycleOwner, lifecycleTag: String) {
        ReaderInsideEvents.get().colorStyleChanged().observe(lifecycleOwner, lifecycleTag) {
            setViewColor()
        }
    }

    override fun bindData(data: ScoreBannerInfo?) {
        super.bindData(data)
        data?.let {
            LogUtil.d(
                "chapterEnd",
                "bindData ${UIIdentify.getObjId(data)} +hasScored=" + data.hasScored
            )
            setViewData(it)
        }
    }

    private fun setViewData(info: ScoreBannerInfo) {
        ratingCount = info.totalScore ?: 0
        var score = if (info.hasScored) info.score else info.totalScore
        mViewBinding.tvTitle.text = info.title
        if (score == 0){
            mViewBinding.tvScore.text = "轻点星星进行评分"
            mViewBinding.tvScore.setTextSize(TypedValue.COMPLEX_UNIT_PX, 14f.dp)
        }else{
            mViewBinding.tvScore.text = "${score}分"
            mViewBinding.tvScore.setTextSize(TypedValue.COMPLEX_UNIT_PX, 18f.dp)
        }
        val meg: Int? = score?.toFloat()?.div(2)?.let { Math.round(it) }
        mViewBinding.ratingbar.setStar(meg?.toFloat() ?: 0.0f)
        mViewBinding.ratingbar.setmClickable(!info.hasScored)
        mViewBinding.rlSubmitRoot.visibility = if (info.hasScored || score == 0) GONE else VISIBLE
        setViewColor()
    }

    private fun setViewColor() {
        if (ReaderConfigUtil.isNightMode()) {
            mViewBinding.clRoot.setShapeBackground(
                solidColor = getColor(R.color.reader_color_FF262626),
                radius = 12f.dp
            )
            mViewBinding.rlSubmitRoot.setShapeBackground(
                solidColor = getColor(R.color.reader_FF007AAC),
                radius = 14f.dp
            )
            mViewBinding.tvTitle.setTextColor(getColor(R.color.reader_DBFFFFFF))
            mViewBinding.tvScore.setTextColor(getColor(R.color.reader_color_99FFFFFF))
            mViewBinding.ratingbar.setStarEmptyDrawable(getDrawable(R.drawable.reader_ic_chapter_end_star_empty_night))
            mViewBinding.ratingbar.setStarFillDrawable(getDrawable(R.drawable.reader_ic_chapter_end_star_full_night))

        } else {
            mViewBinding.clRoot.setShapeBackground(
                solidColor = getColor(R.color.reader_color_61FFFFFF),
                radius = 12f.dp
            )
            mViewBinding.rlSubmitRoot.setShapeBackground(
                solidColor = getColor(R.color.reader_FF00AAEE),
                radius = 14f.dp
            )
            mViewBinding.tvTitle.setTextColor(getColor(R.color.reader_E6000000))
            mViewBinding.tvScore.setTextColor(getColor(R.color.reader_99000000))
            mViewBinding.ratingbar.setStarEmptyDrawable(getDrawable(R.drawable.reader_ic_chapter_end_star_empty))
            mViewBinding.ratingbar.setStarFillDrawable(getDrawable(R.drawable.reader_ic_chapter_end_star_full))
        }
    }

    private fun doTrack(action: Int) {

        mData?.run {
            DzTrackEvents.get()
                .positionAction()
                .action(action)
                .bookId(bookId)
                .bookName(bookName)
                .activityId("")
                .oTypeId("score")
                .eleContent(mViewBinding.tvSubmit.text.toString())
                .eleParamType("score")
                .eleParamValue(ratingCount.toString())
                .userTacticInfo(
                    UserTacticInfoBean(
                        null,
                        null,
                        "章末评分",
                        null,
                        null
                    )
                )
                .title(title)
                .track()
        }

    }

    override fun onBlockShow() {
        onScoreShow()
        doTrack(PositionActionTE.ACTION_SHOW)
    }

}