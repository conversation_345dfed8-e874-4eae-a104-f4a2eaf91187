package com.dz.business.reader.ui.component.block

import android.content.Context
import android.util.AttributeSet
import com.dz.business.reader.R
import com.dz.business.reader.data.ReadEndResponse
import com.dz.business.reader.databinding.ReaderBookEndContinueReadCompBinding
import com.dz.business.reader.ui.page.ReaderActivity
import com.dz.business.reader.utils.ReaderConfigUtil
import com.dz.foundation.base.utils.dp2px
import com.dz.foundation.ui.widget.getContainerActivity
import com.dz.platform.common.base.ui.component.UIConstraintComponent
import reader.xo.config.ColorStyle


/**
 * 终章推荐书籍底部继续阅读功能组件
 */
class BookEndContinueReadComp :
    UIConstraintComponent<ReaderBookEndContinueReadCompBinding, ReadEndResponse> {
    @JvmOverloads
    constructor(
        context: Context,
        attrs: AttributeSet? = null,
        defStyleAttr: Int = 0
    ) : super(context, attrs, defStyleAttr)

    override fun initAttrs(context: Context?, attrs: AttributeSet?, defStyleAtt: Int) {

    }

    override fun initData() {

    }

    override fun initView() {

    }

    override fun initListener() {
        mViewBinding.dlBottomBtn.registerClickAction {
            getContainerActivity()?.let {
                if (it is ReaderActivity) {
                    mData?.let { readerEndBean -> it.loadBookEndNextChapter(readerEndBean) }
                }
            }
        }
    }

    fun setColorStyle(colorStyle: ColorStyle) {
        resetColorMode()

    }

    private fun resetColorMode() {
        if (ReaderConfigUtil.isNightMode()) {
            setNightMode()
        } else {
            setColorMode()
        }

    }

    private fun setNightMode() {
        val gradientStartColor: Int = R.color.reader_config_color_style_gradient_start_night
        val gradientEndColor: Int = R.color.reader_config_color_style_gradient_end_night
        val solid: Int = R.color.reader_00000000_00000000
        val stokeColor: Int = R.color.reader_4DFFFFFF
        mViewBinding.flTopCover.setShapeBackground(
            gradientOrientation = 1,
            gradientStartColor = getColor(gradientStartColor),
            gradientEndColor = getColor(gradientEndColor)
        )
        mViewBinding.flBottomBg.setBackgroundResource(gradientEndColor)
        mViewBinding.dlBottomBtn.setShapeBackground(
            solidColor = getColor(solid),
            radius = 20.dp2px,
            stokeWidth = (0.5f).dp2px,
            stokeColor = getColor(stokeColor)
        )

        mViewBinding.tvText.setTextColor(getColor(R.color.reader_80FFFFFF))

        mViewBinding.ivRightArrow.setImageResource(R.drawable.reader_ic_arrow_right_white)
    }

    private fun setColorMode() {
        val currentColorStyleIndex = ReaderConfigUtil.getCurrentColorStyleIndex()

        val gradientStartColor: Int
        val gradientEndColor: Int
        val solid: Int = R.color.reader_00000000_00000000
        val stokeColor: Int = R.color.reader_4D000000

        when (currentColorStyleIndex) {
            0 -> {
                gradientStartColor = R.color.reader_config_color_style_gradient_start_0
                gradientEndColor = R.color.reader_config_color_style_bg_0
            }
            1 -> {
                gradientStartColor = R.color.reader_config_color_style_gradient_start_1
                gradientEndColor = R.color.reader_config_color_style_bg_1

            }
            2 -> {
                gradientStartColor = R.color.reader_config_color_style_gradient_start_2
                gradientEndColor = R.color.reader_config_color_style_bg_2

            }
            3 -> {
                gradientStartColor = R.color.reader_config_color_style_gradient_start_3
                gradientEndColor = R.color.reader_config_color_style_bg_3

            }
            else -> {
                gradientStartColor = R.color.reader_config_color_style_gradient_start_0
                gradientEndColor = R.color.reader_config_color_style_bg_0

            }
        }
        mViewBinding.flTopCover.setShapeBackground(
            gradientOrientation = 1,
            gradientStartColor = getColor(gradientStartColor),
            gradientEndColor = getColor(gradientEndColor)
        )

        mViewBinding.flBottomBg.setBackgroundResource(gradientEndColor)
        mViewBinding.dlBottomBtn.setShapeBackground(
            solidColor = getColor(solid),
            radius = 20.dp2px,
            stokeWidth = (0.5f).dp2px,
            stokeColor = getColor(stokeColor)
        )

        mViewBinding.tvText.setTextColor(getColor(R.color.reader_80000000))

        mViewBinding.ivRightArrow.setImageResource(R.drawable.reader_ic_arrow_right_gray)

    }

    override fun bindData(data: ReadEndResponse?) {
        super.bindData(data)
        //触发继续阅读

    }
}