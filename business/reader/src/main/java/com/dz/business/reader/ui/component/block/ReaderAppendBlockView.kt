package com.dz.business.reader.ui.component.block

import android.content.Context
import android.text.TextUtils
import android.util.AttributeSet
import android.view.View
import android.view.ViewGroup
import androidx.constraintlayout.widget.ConstraintLayout
import com.dz.business.reader.data.ChapterOpenBean
import com.dz.business.reader.utils.ReaderConfigUtil
import com.dz.foundation.base.utils.LogUtil
import com.dz.foundation.base.utils.UIIdentify
import reader.xo.base.XoFile
import reader.xo.block.AppendBlockView
import reader.xo.block.Block

/**
 *@Author: shidz
 *@Date: 2022/11/15 15:38
 *@Description: 阅读器章末运营位等block
 *@Version:1.0
 */
class ReaderAppendBlockView @JvmOverloads constructor(
    context: Context,
    type: Int,
    attrs: AttributeSet? = null,
) : AppendBlockView(context, type, attrs) {

    companion object {
        const val TYPE_CHAPTER_END = 1
        var currentFid: String? = null
        var currentTag: String? = null

    }


    private var mFid: String? = null
    override fun bindData(file: XoFile, block: Block) {
        super.bindData(file, block)

        if (block.tag is ChapterOpenBean) {
            val chapterOpenBean = block.tag as ChapterOpenBean
            LogUtil.e(
                "chapterEnd",
                "${UIIdentify.getObjId(this)} bindData= ${chapterOpenBean.dataId + " chapterId" + chapterOpenBean.currentChatperId}"
            )

            val turnPageType = ReaderConfigUtil.getCurrentTurnPageStyle().name
            val tag = chapterOpenBean.dataId + "_" + turnPageType
            if (!TextUtils.isEmpty(mFid) && TextUtils.equals(tag, currentTag)) {
                LogUtil.e(
                    "chapterEnd",
                    "tag equal return =$tag currentTag=$currentTag"
                )
                return
            }
            chapterOpenBean.setHasShownMarketingItem()
            currentTag = tag
            currentFid = file.fid
            mFid = file.fid
            mBlockComp?.bindViewData(file.fid, block)

        }
    }

    private var mBlockComp: ReaderBlockComp? = null
    fun addBlock(blockComp: ReaderBlockComp): ReaderAppendBlockView {
        mBlockComp = blockComp
        if (blockComp is View) {
            if (blockComp.parent != null) {
                (blockComp.parent as ViewGroup).removeView(blockComp)
            }
            val layoutParams = ConstraintLayout.LayoutParams(
                ConstraintLayout.LayoutParams.MATCH_PARENT,
                ConstraintLayout.LayoutParams.MATCH_PARENT
            )
            addView(blockComp, layoutParams)
        }
        return this

    }
}