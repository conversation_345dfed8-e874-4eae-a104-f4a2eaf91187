package com.dz.business.reader.data

import com.dz.business.base.data.bean.BaseBean
import com.dz.business.base.data.bean.UserTacticInfoBean

/**
 *@Author: shidz
 *@Date: 2022/11/9 10:59
 *@Description: 终章推荐
 *@Version:1.0
 */
data class ReadEndResponse(
    var chapterInfo: NovelChapterInfo? = null,
    var isPushMore: Int? = 0,//是否催更过：1-是，0-否
    var pushMoreNum: Int? = 0,
    var pushMoreText: String? = "",
    var recommendBookInfo: NovelBookInfo? = null,
    var recommendBookTitle: String? = "",
    var serialText: String? = "",
    var userTacticInfo: UserTacticInfoBean? = null,//用户分层信息
    var operateId: String? = null,//运营位id
    var titlePlaceHolder:String?=null
) : BaseBean(){
    var bookStatus: Int? = 0//书籍完本状态，0-连载，1-完本
    var bookId: String? = ""
    var bookName: String? = ""
}
