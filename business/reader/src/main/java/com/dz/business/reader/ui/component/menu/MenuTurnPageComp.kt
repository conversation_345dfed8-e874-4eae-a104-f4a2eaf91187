package com.dz.business.reader.ui.component.menu

import android.content.Context
import android.util.AttributeSet
import androidx.lifecycle.LifecycleOwner
import com.dz.business.reader.R
import com.dz.business.reader.ReaderInsideEvents
import com.dz.business.reader.databinding.ReaderMenuTurnPageCompBinding
import com.dz.business.reader.utils.ReaderConfigUtil
import com.dz.foundation.ui.view.custom.ActionListener
import com.dz.foundation.ui.view.custom.ActionListenerOwner
import com.dz.foundation.ui.view.recycler.DzRecyclerViewCell
import com.dz.platform.common.base.ui.component.UIConstraintComponent
import reader.xo.config.AnimType

/**
 *@Author: shidz
 *@Date: 2022/9/29 15:26
 *@Description: 阅读器主菜单翻页设置组件
 *@Version:1.0
 */
class MenuTurnPageComp @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : UIConstraintComponent<ReaderMenuTurnPageCompBinding, Any>(context, attrs, defStyleAttr),
    ActionListenerOwner<MenuTurnPageComp.ViewActionListener> {

    override fun initData() {

    }

    override fun initView() {
        setMode()
        mViewBinding.drv.addCells(createCells())
    }

    private fun createCells(): MutableList<DzRecyclerViewCell<TurnPageItemBean>> {
        val cells = mutableListOf<DzRecyclerViewCell<TurnPageItemBean>>()
        val nameArray = ReaderConfigUtil.getTurnPageStyleNameArr()
        val turnPageStyles = ReaderConfigUtil.getTurnPageStyles()
        repeat(nameArray.size) {
            val itemBean = TurnPageItemBean()
            itemBean.name = nameArray[it]
            itemBean.animType = turnPageStyles[it]
            if (itemBean.animType == ReaderConfigUtil.getCurrentTurnPageStyle()) {
                itemBean.checked = true
            }
            cells.add(createCellItem(itemBean))
        }
        return cells
    }

    private fun createCellItem(data: TurnPageItemBean): DzRecyclerViewCell<TurnPageItemBean> {
        val itemCell = DzRecyclerViewCell<TurnPageItemBean>()
        itemCell.viewClass = MenuTurePageItemComp::class.java
        itemCell.viewData = data
        itemCell.setActionListener(getListener())
        itemCell.spanSize = 1
        return itemCell
    }

    override fun initListener() {

    }

    private var mListener: MenuTurePageItemComp.ViewActionListener? = null
    private fun getListener(): MenuTurePageItemComp.ViewActionListener {
        if (mListener == null) {
            mListener = object : MenuTurePageItemComp.ViewActionListener {
                override fun onItemChecked(data: TurnPageItemBean) {
                    unCheckedLastItem()
                    //切换翻页时tag重置下，要不广告不刷新
                    mActionListener?.onCheckTurnPageStyle(data.animType)
                }
            }
        }
        return mListener!!
    }

    private fun unCheckedLastItem() {
        for (itemCell in mViewBinding.drv.allCells) {
            val itemBean = itemCell.viewData as TurnPageItemBean
            if (itemBean.checked) {
                itemBean.checked = false
                mViewBinding.drv.updateCell(itemCell, itemBean)
                break
            }
        }
    }

    interface ViewActionListener : ActionListener {
        fun onCheckTurnPageStyle(animType: AnimType)
    }

    override var mActionListener: ViewActionListener? = null

    override fun subscribeEvent(lifecycleOwner: LifecycleOwner, lifecycleTag: String) {
        ReaderInsideEvents.get().colorStyleChanged().observe(lifecycleOwner, lifecycleTag) {
            setMode()
        }
    }

    private fun setMode() {
        if (ReaderConfigUtil.isNightMode()) setNightMode() else setDayMode()
    }

    private fun setDayMode() {
        mViewBinding.tvTurnPageTitle.setTextColor(getColor(R.color.reader_title_text_color_day))
    }

    private fun setNightMode() {
        mViewBinding.tvTurnPageTitle.setTextColor(getColor(R.color.reader_title_text_color_night))
    }
}