package com.dz.business.reader.ui.component.menu

import android.content.Context
import android.graphics.Color
import android.util.AttributeSet
import androidx.lifecycle.LifecycleOwner
import com.dz.business.base.data.BBaseKV
import com.dz.business.reader.R
import com.dz.business.reader.ReaderInsideEvents
import com.dz.business.reader.data.NoAdConfig
import com.dz.business.reader.databinding.ReaderMenuTitleCompBinding
import com.dz.business.reader.utils.ReaderConfigUtil
import com.dz.business.reader.repository.entity.NovelBookEntity
import com.dz.foundation.base.utils.NetWorkUtil
import com.dz.foundation.base.utils.dp
import com.dz.foundation.base.utils.dp2px
import com.dz.foundation.ui.view.custom.ActionListener
import com.dz.foundation.ui.view.custom.ActionListenerOwner
import com.dz.platform.common.base.ui.component.UIConstraintComponent
import com.dz.platform.common.router.SchemeRouter
import com.dz.platform.common.toast.ToastManager

/**
 *@Author: shidz
 *@Date: 2022/10/27 10:38
 *@Description: 阅读器菜单标题组件抽取
 * 顶部：返回、去广告按钮
 *@Version:1.0
 */
class MenuTitleComp @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : UIConstraintComponent<ReaderMenuTitleCompBinding, NovelBookEntity>(
    context,
    attrs,
    defStyleAttr
),
    ActionListenerOwner<MenuTitleCompActionListener> {

    private var addToShelf: Int = 0

    override fun initData() {

    }

    override fun initView() {
        setMode()
    }

    override fun initListener() {
        mViewBinding.apply {
            llBack.registerClickAction {
                mActionListener?.onBackClick()
            }
            tvBackTitle.registerClickAction {
                mActionListener?.onBackClick()
            }

        }
    }

    private fun toAddShelf() {
        if (mData?.add_to_shelf == 1) {
            ToastManager.showToast(context.getString(R.string.reader_added_shelf))
        } else {
            //如果是本地书未加入书架中说明已达到上限
            if (NovelBookEntity.isLocalBook(mData?.bid)) {
                ToastManager.showToast(resources.getString(R.string.reader_local_limit))
                return
            }
            ToastManager.showToast("")
            mActionListener?.addToShelf()
        }
    }

    private fun toBatchOrder() {
        mActionListener?.batchOrder()
    }

    override fun bindData(data: NovelBookEntity?) {
        super.bindData(data)
        data?.let {
            addToShelf = it.add_to_shelf ?: 0
            setAddShelfIcon()
        }
    }

    override var mActionListener: MenuTitleCompActionListener? = null

    override fun subscribeEvent(lifecycleOwner: LifecycleOwner, lifecycleTag: String) {
        ReaderInsideEvents.get().colorStyleChanged().observe(lifecycleOwner, lifecycleTag) {
            setMode()
        }
    }

    private fun setMode() {
        if (!ReaderConfigUtil.isNightMode())
            setDayMode()
        else
            setNightMode()
    }

    private fun setDayMode() {
        mViewBinding.run {
            ivBack.setImageResource(R.drawable.reader_arrow_back_day_mode)
            tvBackTitle.setTextColor(getColor(R.color.reader_E6000000))
            llNoAd.setShapeBackground(
                radius = 14.dp2px,
                stokeWidth = 1.dp2px,
                stokeColor = getColor(R.color.reader_4D000000)
            )

            ivNoAd.setImageResource(R.drawable.reader_ic_no_ad)
            tvNoAd.setTextColor(getColor(R.color.reader_E6000000))
            setAddShelfIcon()
        }
    }

    private fun setNightMode() {
        mViewBinding.run {
            ivBack.setImageResource(R.drawable.reader_arrow_back_night_mode)
            tvBackTitle.setTextColor(getColor(R.color.reader_E6FFFFFF))
            llNoAd.setShapeBackground(
                radius = 14.dp2px,
                stokeWidth = 1.dp2px, stokeColor = getColor(R.color.reader_4DFFFFFF)
            )
            tvNoAd.setTextColor(getColor(R.color.reader_E6FFFFFF))
            ivNoAd.setImageResource(R.drawable.reader_ic_no_ad_night)
            setAddShelfIcon()
        }
    }

    private fun setAddShelfIcon() {
    }

    fun bindNoAdConfig(removeAdVo: NoAdConfig?) {
        if (removeAdVo == null) {
            mViewBinding.llNoAd.visibility = GONE
        } else {
            removeAdVo?.let { noAdConfig ->
                setNoAd(noAdConfig)

            }
        }

    }

    private fun setNoAd(noAdConfig: NoAdConfig) {
        if (noAdConfig.removeAdBtn != 1) {
            mViewBinding.llNoAd.visibility = GONE
        }
        mViewBinding.tvNoAd.text = noAdConfig.removeAdTip
        mViewBinding.llNoAd.registerClickAction {
            SchemeRouter.doUriJump(noAdConfig.removeAdUrl)
        }
    }
}

interface MenuTitleCompActionListener : ActionListener {
    fun onBackClick()
    fun addToShelf()
    fun batchOrder()
    fun doShare()
}
