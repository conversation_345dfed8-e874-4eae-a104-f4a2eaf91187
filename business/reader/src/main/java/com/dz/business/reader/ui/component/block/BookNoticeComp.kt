package com.dz.business.reader.ui.component.block

import android.animation.Animator
import android.animation.Animator.AnimatorListener
import android.content.Context
import android.util.AttributeSet
import androidx.lifecycle.LifecycleOwner

import com.dz.business.reader.R
import com.dz.business.reader.ReaderInsideEvents
import com.dz.business.reader.ad.ReaderAdManager
import com.dz.business.reader.ad.ReaderFeedAd
import com.dz.business.reader.ad.callback.ReaderAdActionCallback
import com.dz.business.reader.ad.callback.ReaderAdLoadCallback
import com.dz.business.reader.data.ReaderAdConfigInfo
import com.dz.business.reader.databinding.ReaderBookNoticeCompBinding

import com.dz.business.reader.utils.ReaderAdUtil
import com.dz.business.reader.utils.ReaderConfigUtil

import com.dz.foundation.base.utils.dp
import com.dz.platform.common.base.ui.component.UIConstraintComponent

/**
 * <AUTHOR>
 * @description:书籍公告
 * @date :2023/9/21
 */
class BookNoticeComp @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : UIConstraintComponent<ReaderBookNoticeCompBinding, ReaderAdConfigInfo.NoticeAdConfig>(
    context,
    attrs,
    defStyleAttr
) {
    private var mViewWidth = 0

    private var currentFeedAd: ReaderFeedAd? = null
    override fun initData() {
    }

    override fun initView() {
        var padding = 0
        var margin = 24.dp
        var paddingBottom = 80.dp
        setPadding(padding, 0, padding, 0)
        mViewBinding.tvNoticeClick.setPadding(0, 0, 0, paddingBottom)
    }

    override fun initListener() {
        registerClickAction {
            startViewAnimate()
        }
    }

    private fun startViewAnimate() {
        translationX = 0f
        animate().translationX(-measuredWidth.toFloat()).setListener(object : AnimatorListener {
            override fun onAnimationStart(animation: Animator) {
            }

            override fun onAnimationEnd(animation: Animator) {
                visibility = GONE
                ReaderAdUtil.getInstance().isAdShowNotice = false
                ReaderAdUtil.getInstance().isAdNoticeShow = true
                onRecycleRes()
            }

            override fun onAnimationCancel(animation: Animator) {
            }

            override fun onAnimationRepeat(animation: Animator) {
            }

        })
    }


    fun onRecycleRes() {

    }

    private fun getViewHeight(): Int {
        val ratio: Double = 9.0 / 16.0
        return (mViewWidth * ratio).toInt()
    }

    override fun subscribeEvent(lifecycleOwner: LifecycleOwner, lifecycleTag: String) {
        ReaderInsideEvents.get().colorStyleChanged().observe(lifecycleOwner, lifecycleTag) {
            refreshBackgroundColor()
        }
    }

    override fun bindData(data: ReaderAdConfigInfo.NoticeAdConfig?) {
        super.bindData(data)
        if (ReaderAdUtil.getInstance().isAdNoticeShow) {
            visibility = GONE
            return
        }
        refreshBackgroundColor()
        data?.let {
            if (it.isValidAd()) {
                ReaderAdUtil.getInstance().isAdShowNotice = true
                visibility = VISIBLE
                mViewBinding.tvNoticeTitle.text = it.noticeTitle
                mViewBinding.tvNoticeDes.text = it.noticeDesc
              ReaderAdManager.loadNoticeAdView(
                        mViewBinding.flAdNoticeContent, it.getLoadAdParam(
                            mViewWidth, getViewHeight(),
                            mViewWidth, 336.dp,
                            "",
                            false
                        ),
                        object : ReaderAdLoadCallback {
                            override fun onLoaded(readerFeedAd: ReaderFeedAd) {
                                renderAd(readerFeedAd)
                            }

                            override fun onAdFailed() {
                                titleViewVisibility()
                            }

                        }
                    )
            }

        }

    }

    private fun renderAd(readerFeedAd: ReaderFeedAd){

        currentFeedAd?.destroy()
        currentFeedAd = readerFeedAd

     val actionCallback =    object : ReaderAdActionCallback {
            override fun onRenderSuccess() {

                titleViewVisibility()
            }

            override fun onShow() {
            }

            override fun onClick() {
            }

            override fun onClose() {
                mViewBinding.flAdNoticeContent.visibility = INVISIBLE
            }

            override fun onRenderFail() {
//                                ReaderAdUtil.getInstance().isAdShowNotice = false
                titleViewVisibility()
            }

        }
        readerFeedAd.render( mViewBinding.flAdNoticeContent,actionCallback)
    }
    /**
     * 标题上半部分居中显示，广告未加载出来显示可能会上下浮动
     */
    private fun titleViewVisibility() {
        mViewBinding.tvNoticeTitle.visibility = VISIBLE
        mViewBinding.tvNoticeDes.visibility = VISIBLE
    }

    private fun refreshBackgroundColor() {
        if (ReaderConfigUtil.isNightMode()) {
            setBackgroundColor(getColor(R.color.reader_config_color_style_bg_night))
            mViewBinding.tvNoticeTitle.setTextColor(getColor(R.color.reader_DBFFFFFF))
            mViewBinding.tvNoticeDes.setTextColor(getColor(R.color.reader_99FFFFFF))
            mViewBinding.tvNoticeClick.setTextColor(getColor(R.color.reader_99FFFFFF))
            mViewBinding.flAdNoticeContent.setShapeBackground(
                solidColor = getColor(R.color.reader_1AFFFFFF),
                radius = 12f.dp
            )
        } else {
            val currentColorStyle = ReaderConfigUtil.getCurrentColorStyle()
            currentColorStyle?.run {
                setBackgroundColor(bgColor)
            }
            mViewBinding.tvNoticeTitle.setTextColor(getColor(R.color.reader_E6000000))
            mViewBinding.tvNoticeDes.setTextColor(getColor(R.color.reader_99000000))
            mViewBinding.tvNoticeClick.setTextColor(getColor(R.color.reader_99000000))
            mViewBinding.flAdNoticeContent.setShapeBackground(
                solidColor = getColor(R.color.reader_0D000000),
                radius = 12f.dp
            )
        }
    }
}