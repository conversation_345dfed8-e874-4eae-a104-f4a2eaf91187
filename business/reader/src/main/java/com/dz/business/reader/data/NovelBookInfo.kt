package com.dz.business.reader.data

import com.dz.business.base.data.bean.BaseBean
import com.dz.business.base.data.bean.StrategyInfo

/**
 *@Author: shidz
 *@Date: 2022/10/19 0:39
 *@Description: 书籍基础信息 ,别的地方如有需要可继承扩展
 *@Version:1.0
 */
open class NovelBookInfo(
    var author: String? = null,//作者
    var bookId: String? = null,//id
    var bookName: String? = null,//书名
    var coverWap: String? = null,//书封
    var introduction: String? = null,//简介
    var roleName: String? = null,//角色
    var bookAlias: String? = null,//书籍别名
    var add_to_shelf: Int? = null,
    var status: Int? = null,//完本状态:0-连载,1-完本
    var statusTips: String? = null,//完本状态：连载中或完本
    var totalChapterNum: Int? = null,
    var unit: Int? = null,
    var wordSizeText: String? = null,
    var chapterId: String? = null,//章节id 书架接口下发，进度用
    var bigDataDotInfoVo: StrategyInfo? = null,//大数据推荐策略id
    var source: String? = null,//书籍来源
    var bookType: Int? = null,//书籍类型 文本书籍还是音频书籍
    //有声书小说标签
    var mark:String? = null,
) : BaseBean() {

}
