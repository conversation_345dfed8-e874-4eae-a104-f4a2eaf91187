package com.dz.business.reader.ui.component.ad

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Canvas
import android.util.AttributeSet
import android.view.Surface
import android.view.SurfaceView
import android.view.ViewGroup
import androidx.annotation.AttrRes
import com.dz.foundation.base.utils.LogUtil
import com.dz.foundation.ui.widget.DzFrameLayout

class AdContainer : DzFrameLayout {

    @JvmOverloads
    constructor(
        context: Context,
        attrs: AttributeSet? = null,
        @AttrRes defStyleAttr: Int = 0
    ) : super(context, attrs, defStyleAttr) {
         setWillNotDraw(false)
    }

   fun setVideoPause(viewGroup: ViewGroup){
       val count = viewGroup.childCount
       for (i in 0 until count) {
           val childAt = viewGroup.getChildAt(i)
           LogUtil.d("AdContainer","setVideoPause childAd=${childAt}")
           if (childAt is SurfaceView) {
               childAt.visibility = GONE
           }else if(childAt is ViewGroup){
               setVideoPause(childAt)
           }
       }
   }

    fun setVideoResume(viewGroup: ViewGroup){
        val count = viewGroup.childCount
        for (i in 0 until count) {
            val childAt = viewGroup.getChildAt(i)
            LogUtil.d("AdContainer","setVideoResume childAd=${childAt}")
            if (childAt is SurfaceView) {
                childAt.visibility = VISIBLE
            }else if(childAt is ViewGroup){
                setVideoResume(childAt)
            }
        }
    }


}