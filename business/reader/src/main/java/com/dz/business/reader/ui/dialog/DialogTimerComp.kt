package com.dz.business.reader.ui.dialog

import android.app.Activity
import android.content.Context
import android.view.Gravity
import android.view.ViewGroup
import com.dz.business.base.ui.BaseDialogComp
import com.dz.business.reader.R
import com.dz.business.reader.audio.TtsPlayer
import com.dz.business.reader.data.TtsTimerGear
import com.dz.business.reader.databinding.ReaderDialogTimerBinding
import com.dz.business.reader.ui.component.menu.MenuTtsTimerItemComp
import com.dz.business.reader.utils.ReaderConfigUtil
import com.dz.business.reader.vm.ReaderDialogTimerVM
import com.dz.foundation.base.utils.ScreenUtil
import com.dz.foundation.base.utils.dp
import com.dz.foundation.ui.view.recycler.DzRecyclerViewCell
import com.dz.platform.common.toast.ToastManager

/**
 *@Author: wanxin
 *@Date: 2023/8/22 21:00
 *@Description:
 *@Version: 1.0
 */
class DialogTimerComp(context: Context) : BaseDialogComp<ReaderDialogTimerBinding, ReaderDialogTimerVM>(context) {

    /**
     * 定时的档位，单位：分钟
     * -1 表示不开启
     */
    private var gears: List<Int> = listOf(15, 30, 60, 90, -1)
    private var currentGear: TtsTimerGear? = null

    override fun initData() {
        dialogSetting.cancelable = true

        val time = mViewModel.routeIntent?.currentTime?: -1
        var index = gears.size - 1
        for (i in gears.indices) {
            if (time == gears[i]) {
                index = i
            }
        }
        currentGear = TtsTimerGear(
            index = index,
            timer = time,
            name =
            if (time == -1)
                context.getString(R.string.reader_not_open)
            else
                "${  context.getString(R.string.reader_minute, time.toString())}",
            selected = true
        )
    }

    override fun initView() {
        mViewBinding.run {
            if (ReaderConfigUtil.isNightMode()) {
                menuBottom.setShapeBackground(
                    leftTopRadius = 22f.dp,
                    rightTopRadius = 22f.dp,
                    solidColor = getColor(R.color.reader_color_FF262626)
                )
                ivClose.setImageResource(R.drawable.reader_timer_dialog_top_arrow_night)
                tvTitle.setTextColor(getColor(R.color.reader_DBFFFFFF))
                rv.showDivider(R.color.reader_color_33FFFFFF)
            } else {
                menuBottom.setShapeBackground(
                    leftTopRadius = 22f.dp,
                    rightTopRadius = 22f.dp,
                    solidColor = getColor(R.color.reader_FFFFFFFF)
                )
                ivClose.setImageResource(R.drawable.reader_timer_dialog_top_arrow)
                tvTitle.setTextColor(getColor(R.color.reader_E6000000))
                rv.showDivider(R.color.reader_color_33000000)
            }
            menuBottom.setPadding(0, 0, 0, ScreenUtil.getNavigationHeight(context as Activity))
            rv.addCells(createCells())
        }
    }

    override fun initListener() {
        mViewBinding.ivClose.registerClickAction {
            dismiss()
        }
    }

    private fun createCells(): MutableList<DzRecyclerViewCell<*>> {
        val cellList = mutableListOf<DzRecyclerViewCell<*>>()
        for (i in gears.indices) {
            val item = TtsTimerGear(
                index = i,
                timer = gears[i],
                name =
                if (gears[i] == -1)
                    context.getString(R.string.reader_not_open)
                else
                    "${  context.getString(R.string.reader_minute, gears[i].toString())}",
                selected = i == currentGear?.index,
            )
            cellList.add(createItemCell(item))
        }
        return cellList
    }

    private fun createItemCell(item: TtsTimerGear): DzRecyclerViewCell<*> {
        val cellItem = DzRecyclerViewCell<TtsTimerGear>()
        cellItem.viewClass = MenuTtsTimerItemComp::class.java
        cellItem.viewData = item
        cellItem.setActionListener(object : MenuTtsTimerItemComp.TimerChangeListener {

            override fun onChange(timer: TtsTimerGear?) {
                if (currentGear?.timer == timer?.timer) {
                    return
                }
                // 取消上一个选中的档位
                currentGear?.apply {
                    selected = false
                    mViewBinding.rv.updateCell(index, this)
                }
                // 选中当前档位
                timer?.selected = true
                mViewBinding.rv.updateCell(timer?.index?: 0, timer)
                currentGear = timer!!
                // 立即生效，开始倒计时
                if (gears[timer.index] > 0) {
                    ToastManager.showToast("${gears[timer.index]}${resources.getString(R.string.reader_some_minutes_exit_tts)}")
                    TtsPlayer.instance.timerPresenter.startTimerToClose(gears[timer.index])
                } else {
                    ToastManager.showToast(resources.getString(R.string.reader_closed_timer))
                    TtsPlayer.instance.timerPresenter.cancelTimerToClose(true)
                }
            }
        })
        return cellItem
    }

    override fun getEnterAnim(): Int = R.anim.common_bottom_in

    override fun getExitAnim(): Int = R.anim.common_bottom_out

}