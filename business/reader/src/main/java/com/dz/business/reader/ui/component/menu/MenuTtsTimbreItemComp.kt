package com.dz.business.reader.ui.component.menu

import android.content.Context
import android.util.AttributeSet
import com.dz.business.base.reader.data.NovelTimbreVo
import com.dz.business.reader.R
import com.dz.business.reader.databinding.ReaderMenuTtsTimbreItemCompBinding
import com.dz.business.reader.utils.ReaderConfigUtil
import com.dz.foundation.ui.view.custom.ActionListener
import com.dz.foundation.ui.view.custom.ActionListenerOwner
import com.dz.platform.common.base.ui.component.UIConstraintComponent

/**
 *@Author: zhanggy
 *@Date: 2022-11-17
 *@Description:听书选择音色列表 - 单个Item
 *@Version:1.0
 */
class MenuTtsTimbreItemComp @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : UIConstraintComponent<ReaderMenuTtsTimbreItemCompBinding, NovelTimbreVo>(
    context,
    attrs,
    defStyleAttr
), ActionListenerOwner<MenuTtsTimbreItemComp.TimbreChangeListener> {

    override var mActionListener: TimbreChangeListener? = null

    override fun initData() {
    }

    override fun initView() {
        mViewBinding.run {
            tvName.setTextColor(
                getColor(
                    if (ReaderConfigUtil.isNightMode())
                        R.color.reader_DBFFFFFF
                    else
                        R.color.reader_E6000000
                )
            )
        }
    }

    override fun initListener() {
        registerClickAction {
            if (mData?.selected == true) {
                return@registerClickAction
            }
            mData?.apply {
                selected = true
                mActionListener?.onChange(this)
            }
        }
    }

    override fun bindData(data: NovelTimbreVo?) {
        super.bindData(data)
        data?.apply {
            mViewBinding.apply {
                tvName.text = name
                ivCheck.setImageResource(
                    if (selected) {
                        if (ReaderConfigUtil.isNightMode())
                            R.drawable.reader_ic_night_check
                        else
                            R.drawable.reader_ic_check
                    } else {
                        if (ReaderConfigUtil.isNightMode())
                            R.drawable.reader_ic_night_check_normal
                        else
                            R.drawable.reader_ic_check_normal
                    }
                )
            }
        }
    }

    interface TimbreChangeListener : ActionListener {
        fun onChange(data: NovelTimbreVo)
    }
}