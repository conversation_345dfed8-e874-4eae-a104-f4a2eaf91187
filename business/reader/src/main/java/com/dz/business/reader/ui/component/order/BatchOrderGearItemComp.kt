package com.dz.business.reader.ui.component.order

import android.content.Context
import android.util.AttributeSet
import com.dz.business.reader.data.BatchOrderGear
import com.dz.business.reader.databinding.ReaderBatchOrderGearItemCompBinding
import com.dz.foundation.ui.view.custom.ActionListener

import com.dz.foundation.ui.view.custom.ActionListenerOwner
import com.dz.platform.common.base.ui.component.UIConstraintComponent

/**
 *@Author: shidz
 *@Date: 2022/11/15 9:49
 *@Description:批量订购挡位
 *@Version:1.0
 */
class BatchOrderGearItemComp :
    UIConstraintComponent<ReaderBatchOrderGearItemCompBinding, BatchOrderGear>,
    ActionListenerOwner<BatchOrderGearActionListener> {
    @JvmOverloads
    constructor(
        context: Context,
        attrs: AttributeSet? = null,
        defStyleAttr: Int = 0
    ) : super(context, attrs, defStyleAttr)

    override var mActionListener: BatchOrderGearActionListener? = null

    override fun initData() {
    }

    override fun initView() {
    }

    override fun initListener() {
        this.registerClickAction {
            mData?.let {
                mActionListener?.onGearChecked(it)
            }
        }
    }

    override fun bindData(data: BatchOrderGear?) {
        super.bindData(data)
        data?.run {
            setViewData(this)

        }
    }

    private fun setViewData(gear: BatchOrderGear) {
        mViewBinding.tvChapterNum.text = gear.batchNum.toString()
        mViewBinding.tvAmount.text = gear.amountText
        mViewBinding.tvChapterUnit.text = gear.chapterUnit
        mViewBinding.clRoot.isSelected = gear.isSelected
        if (gear.cornerMark.isNullOrEmpty()) {
            mViewBinding.tvCorner.visibility = GONE
        } else {
            mViewBinding.tvCorner.visibility = VISIBLE
            mViewBinding.tvCorner.text = gear.cornerMark
        }

    }

}

interface BatchOrderGearActionListener : ActionListener {
    fun onGearChecked(gear: BatchOrderGear)
}