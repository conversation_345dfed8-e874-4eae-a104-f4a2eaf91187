package com.dz.business.reader.ui.component.block

import android.content.Context
import android.content.res.ColorStateList
import android.util.AttributeSet
import androidx.lifecycle.LifecycleOwner
import com.dz.business.base.main.MainMS
import com.dz.business.reader.R
import com.dz.business.reader.ReaderInsideEvents
import com.dz.business.reader.data.TextActionAct
import com.dz.business.reader.databinding.ReaderChapterEndTextLinkCompBinding
import com.dz.business.reader.utils.ReaderConfigUtil
import com.dz.business.track.events.DzTrackEvents
import com.dz.business.track.events.sensor.PositionActionTE
import com.dz.business.track.trace.OmapNode
import com.dz.business.track.trace.SourceNode
import com.dz.business.track.trace.SourceTrace
import com.dz.foundation.base.utils.dp
import com.dz.platform.common.base.ui.component.UIConstraintComponent
import com.dz.platform.common.router.SchemeRouter


/**
 *@Author: shidz
 *@Date: 2022/11/15 12:36
 *@Description:章末文字链组件
 *@Version:1.0
 */
class ChapterEndTextLinkComp @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : UIConstraintComponent<ReaderChapterEndTextLinkCompBinding, TextActionAct>(
    context,
    attrs,
    defStyleAttr
), ChapterEndComp.ChapterEndItem {
    override fun initData() {

    }

    override fun initView() {

    }

    override fun initListener() {
        registerClickAction {
            if (mData != null) {
                doLog(PositionActionTE.ACTION_CLICK)
                SchemeRouter.doUriJump(mData!!.action)
            }
        }
    }

    override fun subscribeEvent(lifecycleOwner: LifecycleOwner, lifecycleTag: String) {
        ReaderInsideEvents.get().colorStyleChanged().observe(lifecycleOwner, lifecycleTag) {
            setViewColor()
        }
    }

    override fun bindData(data: TextActionAct?) {
        super.bindData(data)
        data?.let { setViewData(it) }
    }

    private fun setViewData(info: TextActionAct) {
        mViewBinding.tvContent.text = info.title
        mViewBinding.tvOk.text = info.buttonTxt
        setViewColor()
    }

    private fun setViewColor() {
        if (ReaderConfigUtil.isNightMode()) {
            mViewBinding.clRoot.setShapeBackground(
                stokeColor = getColor(R.color.reader_1AFFFFFF),
                stokeWidth = 1f.dp,
                radius = 22f.dp
            )
            mViewBinding.ivIcon.imageTintList =
                ColorStateList.valueOf(getColor(R.color.reader_B3E55749))
            mViewBinding.tvContent.setTextColor(getColor(R.color.reader_B3E55749))
            mViewBinding.tvOk.setTextColor(getColor(R.color.reader_CCE55749))
        } else {
            //0 黄色, 1 绿色, 2 蓝色, 3 粉色
            when (ReaderConfigUtil.getCurrentColorStyleIndex()) {
                0 -> {
                    mViewBinding.clRoot.setShapeBackground(
                        stokeColor = getColor(R.color.reader_FFE4D0A5),
                        stokeWidth = 1f.dp,
                        radius = 22f.dp
                    )
                    mViewBinding.ivIcon.imageTintList =
                        ColorStateList.valueOf(getColor(R.color.reader_FFA7853D))
                    mViewBinding.tvContent.setTextColor(getColor(R.color.reader_FFA7853D))
                    mViewBinding.tvOk.setTextColor(getColor(R.color.reader_color_FFE55749))
                }
                1 -> {
                    mViewBinding.clRoot.setShapeBackground(
                        stokeColor = getColor(R.color.reader_FFD6E2CC),
                        stokeWidth = 1f.dp,
                        radius = 22f.dp
                    )
                    mViewBinding.ivIcon.imageTintList =
                        ColorStateList.valueOf(getColor(R.color.reader_FF76955D))
                    mViewBinding.tvContent.setTextColor(getColor(R.color.reader_FF76955D))
                    mViewBinding.tvOk.setTextColor(getColor(R.color.reader_color_FFE55749))
                }
                2 -> {
                    mViewBinding.clRoot.setShapeBackground(
                        stokeColor = getColor(R.color.reader_FFC0CEE1),
                        stokeWidth = 1f.dp,
                        radius = 22f.dp
                    )
                    mViewBinding.ivIcon.imageTintList =
                        ColorStateList.valueOf(getColor(R.color.reader_FF697A92))
                    mViewBinding.tvContent.setTextColor(getColor(R.color.reader_FF697A92))
                    mViewBinding.tvOk.setTextColor(getColor(R.color.reader_color_FFE55749))
                }
                3 -> {
                    mViewBinding.clRoot.setShapeBackground(
                        stokeColor = getColor(R.color.reader_FFDCB8C0),
                        stokeWidth = 1f.dp,
                        radius = 22f.dp
                    )
                    mViewBinding.ivIcon.imageTintList =
                        ColorStateList.valueOf(getColor(R.color.reader_FFB86678))
                    mViewBinding.tvContent.setTextColor(getColor(R.color.reader_FFB86678))
                    mViewBinding.tvOk.setTextColor(getColor(R.color.reader_color_FFE55749))
                }
            }
        }
    }

    private fun doLog(action: Int) {
        //神策打点
        doSensorTrack(action)
        //服务端点击上报点
        doServerUpload(action)
        //HIVE打点
        doHiveTrack(action)
    }

    /**
     * 固化来源
     */
    private fun doHiveTrack(actionType: Int) {
        mData?.run {
            if (actionType == PositionActionTE.ACTION_CLICK) {
                SourceTrace.putSourceNode(OmapNode().apply {
                    origin = SourceNode.origin_ydq
                    channelId = SourceNode.MODULE_YDQ_ZMWZL
                    channelName = "章末文字链"
                    contentType = SchemeRouter.getActionFromDeepLink(action)
                })
            }
        }
    }

    private fun doServerUpload(action: Int) {
        mData?.run {
             if (action == PositionActionTE.ACTION_CLICK) {
                 MainMS.get()?.activityReportEvent(id ?: "", activityId, 0)
             } else {
                 MainMS.get()?.activityReportEvent(id ?: "", activityId, 1)
             }
        }
    }

    private fun doSensorTrack(action: Int) {

        mData?.run {
            DzTrackEvents.get()
                .positionAction()
                .action(action)
                .bookId(bookId)
                .bookName(bookName)
                .activityId(activityId)
                .oTypeId(id)
                .userTacticInfo(userTacticInfo)
                .title(title)
                .track()
        }

    }

    override fun onBlockShow() {
        doLog(PositionActionTE.ACTION_SHOW)
    }
}