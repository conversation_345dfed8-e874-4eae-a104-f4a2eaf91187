package com.dz.business.reader.ui.dialog

import android.app.Activity
import android.content.Context
import android.view.Gravity
import android.view.ViewGroup
import com.dz.business.base.ui.BaseDialogComp
import com.dz.business.reader.R
import com.dz.business.reader.databinding.ReaderDialogSpeedBinding
import com.dz.business.reader.ui.component.menu.AudioBookSpeedBean
import com.dz.business.reader.ui.component.menu.MenuSpeedItemComp
import com.dz.business.reader.utils.ReaderConfigUtil
import com.dz.business.reader.vm.ReaderDialogVM
import com.dz.foundation.base.utils.ScreenUtil
import com.dz.foundation.base.utils.dp

import com.dz.foundation.ui.view.recycler.DzRecyclerViewCell

/**
 *@Author: wanxin
 *@Date: 2023/8/22 21:00
 *@Description:
 *@Version: 1.0
 */
class DialogAudioSpeedComp(context: Context) :
    BaseDialogComp<ReaderDialogSpeedBinding, ReaderDialogVM>(context),
    MenuSpeedItemComp.SpeedChangeListener {

    private var userChangeSpeed: Float? = null

    override fun initData() {
        dialogSetting.cancelable = true
    }

    override fun initView() {
        mViewBinding.run {
            if (ReaderConfigUtil.isNightMode()) {
                menuBottom.setShapeBackground(
                    leftTopRadius = 22f.dp,
                    rightTopRadius = 22f.dp,
                    solidColor = getColor(R.color.reader_color_FF262626)
                )
                ivClose.setImageResource(R.drawable.reader_timer_dialog_top_arrow_night)
                tvTitle.setTextColor(getColor(R.color.reader_DBFFFFFF))
                rv.showDivider(R.color.reader_color_33FFFFFF)
            } else {
                menuBottom.setShapeBackground(
                    leftTopRadius = 22f.dp,
                    rightTopRadius = 22f.dp,
                    solidColor = getColor(R.color.reader_FFFFFFFF)
                )
                ivClose.setImageResource(R.drawable.reader_timer_dialog_top_arrow)
                tvTitle.setTextColor(getColor(R.color.reader_E6000000))
                rv.showDivider(R.color.reader_color_33000000)
            }
            menuBottom.setPadding(0, 0, 0, ScreenUtil.getNavigationHeight(context as Activity))
            rv.addCells(createCells())
        }
    }

    override fun initListener() {
        mViewBinding.ivClose.registerClickAction {
            dismiss()
        }
    }

    private fun createCells(): MutableList<DzRecyclerViewCell<*>> {
        val nameArray = arrayOf(0.75f, 1.0f, 1.25f, 1.5f, 1.75f, 2.0f)
        val cells = mutableListOf<DzRecyclerViewCell<*>>()
        nameArray.forEachIndexed { index, speed ->
            val itemCell = DzRecyclerViewCell<AudioBookSpeedBean>().apply {
                viewClass = MenuSpeedItemComp::class.java
                viewData = AudioBookSpeedBean(index, speed).apply {
                    check = mViewModel.routeIntent?.currentSpeed == speed
                }
                setActionListener(this@DialogAudioSpeedComp)
            }
            cells.add(itemCell)
        }
        return cells
    }

    override fun getEnterAnim(): Int = R.anim.common_bottom_in

    override fun getExitAnim(): Int = R.anim.common_bottom_out

    override fun onChange(checkSpeed: Float?) {
        mViewBinding.rv.allCells.forEach {
            val cellData = it.viewData as AudioBookSpeedBean
            cellData.check = checkSpeed == cellData.speed
        }
        mViewBinding.rv.notifyDataSetChanged()
        userChangeSpeed = checkSpeed
    }


    override fun onDismiss() {
        userChangeSpeed?.let {
            mViewModel.routeIntent?.changeSpeedBlock?.invoke(it)
        }
        super.onDismiss()
    }
}