package com.dz.business.reader.data

import com.blankj.utilcode.util.GsonUtils
import com.dz.business.base.data.bean.BaseBean
import com.google.gson.Gson
import java.util.*

/**
 *@Author: shidz
 *@Date: 2022/12/5 0:05
 *@Description:终章推荐 fid 模型
 *@Version:1.0
 */
class BookEndFid : BaseBean() {
    companion object {
        const val PREFIX = "end_"
        fun parseJson(bookEndFid: String): BookEndFid {
            val json = bookEndFid.replace(PREFIX, "")
            return GsonUtils.fromJson(json, BookEndFid::class.java)
        }

        fun isBookEndFid(fid: String): Boolean {
            return fid.startsWith(PREFIX)
        }
    }

    var bookId = ""
    var preChapterId = ""
    var recommendBookId = ""
    var recommendChapterId = ""


    override fun toString(): String {
        return PREFIX + toJson()
    }

}