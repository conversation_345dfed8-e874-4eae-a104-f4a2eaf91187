package com.dz.business.reader.data

import com.dz.foundation.base.data.kv.KVData

object ReaderKV : KVData {
    override fun getGroupName(): String {
        return "com.dz.business.reader.data.ReaderKV"
    }

    /**
     * 自动购买下一章的默认:1-默认自动订购，0-非默认自动订购
     */
    var preloadNum by delegate("preloadNum", 1)

    /**
     * 是否触发预加载上一章
     */
    var preloadPrevChapter by delegate("preloadPrevChapter", false)

    //阅读多久自动添加到书架，单位：秒
    var autoAddShelfTime by delegate("autoAddShelfTime", 180)


    //阅读多少章自动添加到书架，单位：章
    var autoAddShelfChapterNum by delegate("autoAddShelfChapterNum", 3)

    /**
     * 听书的音色
     * 默认 "id": "8"
     */
    var ttsVoiceId by delegate("ttsTimbre", "")

    /**
     * 听书的音色
     * 默认 "磁性男声：轩赫"
     */
    var ttsVoiceTitle by delegate("ttsVoiceTitle", "")

    /**
     * 听书的语速
     */
    var ttsSpeed by delegate("ttsSpeed", 1.0f)

    /**
     * 是否上报过桌面组件启动阅读器
     * 默认false
     */
    var isReportWidgetStart by delegate("isReportWidgetStart", false)


    /**
     * 广告配置数据
     */
    var adConfig by delegate("adConfig", "")
}