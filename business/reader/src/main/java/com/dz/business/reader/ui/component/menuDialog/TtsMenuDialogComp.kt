package com.dz.business.reader.ui.component.menuDialog

import android.content.Context
import androidx.lifecycle.LifecycleOwner
import com.dz.business.base.R
import com.dz.business.base.reader.ReaderME
import com.dz.business.base.reader.data.MenuItemInfoVO
import com.dz.business.base.ui.BaseDialogComp
import com.dz.business.reader.ReaderInsideEvents
import com.dz.business.reader.databinding.ReaderCompTtsMenuDialogBinding
import com.dz.business.reader.utils.ReaderConfigUtil
import com.dz.foundation.base.utils.dp
import com.dz.foundation.ui.view.recycler.DzRecyclerViewCell
import com.dz.foundation.ui.widget.getContainerActivity
import com.gyf.immersionbar.BarHide
import com.gyf.immersionbar.ImmersionBar

/**
 * tts菜单弹窗
 */
class TtsMenuDialogComp(context: Context) :
    BaseDialogComp<ReaderCompTtsMenuDialogBinding, TtsMenuDialogVM>(context) {

    private var immersionBar: ImmersionBar? = null
    override fun initData() {

    }

    override fun initView() {
        getContainerActivity()?.let { activity ->
            immersionBar = ImmersionBar.with(activity)
            immersionBar?.transparentStatusBar()
                ?.navigationBarColor(R.color.common_card_FFFFFFFF)
                ?.navigationBarDarkIcon(false)
                ?.statusBarDarkFont(false)
                ?.hideBar(BarHide.FLAG_HIDE_NAVIGATION_BAR)?.init()
        }

        val cells = createMenuCells(mViewModel.routeIntent?.menuData?.menuInfo)
        mViewBinding.rvComponents.removeAllCells()
        mViewBinding.rvComponents.addCells(cells)
        mViewBinding.tvTitle.text = mViewModel.routeIntent?.menuData?.title

        updateTheme()
    }

    override fun initListener() {
        mViewBinding.tvClose.registerClickAction {
            dismiss()
        }
        mViewBinding.viewMask.registerClickAction {
            dismiss()
        }
    }

    override fun subscribeEvent(lifecycleOwner: LifecycleOwner, lifecycleTag: String) {
        super.subscribeEvent(lifecycleOwner, lifecycleTag)
        ReaderInsideEvents.get().colorStyleChanged().observe(lifecycleOwner, lifecycleTag) {
            updateTheme()
        }
        ReaderME.get().onAudioBookPlayEnd().observe(lifecycleOwner, lifecycleTag) {
            dismiss()
        }
    }

    private fun updateTheme() {
        if (ReaderConfigUtil.isNightMode()) {
            mViewBinding.viewLine1.setBackgroundColor(getColor(R.color.common_0FFFFFFF))
            mViewBinding.viewLine2.setBackgroundColor(getColor(R.color.common_0FFFFFFF))
            mViewBinding.tvTitle.setTextColor(getColor(R.color.common_FF8A8A8A))
            mViewBinding.tvClose.setTextColor(getColor(R.color.common_FF8A8A8A))
            mViewBinding.rvComponents.setBackgroundColor(getColor(R.color.color_262626))
            mViewBinding.layoutMenu.setShapeBackground(
                leftTopRadius = 12f.dp,
                rightTopRadius = 12f.dp,
                solidColor = getColor(R.color.color_262626)
            )

        } else {
            mViewBinding.rvComponents.setBackgroundColor(getColor(R.color.white))
            mViewBinding.tvTitle.setTextColor(getColor(R.color.color_262626))
            mViewBinding.tvClose.setTextColor(getColor(R.color.color_262626))
            mViewBinding.viewLine1.setBackgroundColor(getColor(R.color.common_FFEFF2F4))
            mViewBinding.viewLine2.setBackgroundColor(getColor(R.color.common_FFEFF2F4))
            mViewBinding.layoutMenu.setShapeBackground(
                leftTopRadius = 12f.dp,
                rightTopRadius = 12f.dp,
                solidColor = getColor(R.color.common_FFFFFFFF)
            )
        }
    }



    override fun getEnterAnim(): Int {
        return R.anim.common_bottom_in
    }

    override fun getExitAnim(): Int {
        return R.anim.common_bottom_out
    }
    /**
     * 创建菜单 Cells
     */
    private fun createMenuCells(menuList: List<MenuItemInfoVO>?): MutableList<DzRecyclerViewCell<*>> {
        val allCell = mutableListOf<DzRecyclerViewCell<*>>()
        menuList?.forEach { item ->
            //生成 cell
            DzRecyclerViewCell<MenuItemInfoVO>().apply {
                viewClass = TtsMenuItemComp::class.java
                viewData = item
                setActionListener(object : TtsMenuItemComp.ViewActionListener {
                    override fun onMenuSelected(menu: MenuItemInfoVO?) {
                        mViewModel.routeIntent?.onMenuSelected(menu)
                        dismiss()
                    }
                })
                allCell.add(this)
            }
        }
        return allCell
    }

}