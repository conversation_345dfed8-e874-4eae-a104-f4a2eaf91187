package com.dz.business.reader.ui.component.menuDialog

import android.content.Context
import android.graphics.Typeface
import android.util.AttributeSet
import com.dz.business.base.R
import com.dz.business.base.reader.data.MenuItemInfoVO
import com.dz.business.reader.databinding.ReaderCompTtsMenuItemBinding
import com.dz.business.reader.utils.ReaderConfigUtil
import com.dz.foundation.ui.view.custom.ActionListener
import com.dz.foundation.ui.view.custom.ActionListenerOwner
import com.dz.platform.common.base.ui.component.UIConstraintComponent

/**
 * TTS 操作菜单item
 */
class TtsMenuItemComp : UIConstraintComponent<ReaderCompTtsMenuItemBinding, MenuItemInfoVO>,
    ActionListenerOwner<TtsMenuItemComp.ViewActionListener> {

    override var mActionListener: ViewActionListener? = null

    @JvmOverloads
    constructor(
        context: Context,
        attrs: AttributeSet? = null,
        defStyleAttr: Int = 0,
    ) : super(context, attrs, defStyleAttr)


    override fun initData() {
    }

    override fun initView() {


    }

    override fun initListener() {
        mViewBinding.layoutRoot.registerClickAction {
            mActionListener?.onMenuSelected(mData)
        }
    }

    override fun bindData(data: MenuItemInfoVO?) {
        super.bindData(data)
        mViewBinding.tvName.text = data?.name

        if (data?.isChecked == true) {
            mViewBinding.tvName.setTextColor(getColor(R.color.common_A74E41))
            mViewBinding.tvName.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD))
            mViewBinding.ivSelect.visibility = VISIBLE
        } else {
            mViewBinding.tvName.setTypeface(Typeface.defaultFromStyle(Typeface.NORMAL))
            if (ReaderConfigUtil.isNightMode()) {
                mViewBinding.tvName.setTextColor(getColor(R.color.common_FF8A8A8A))
            } else {
                mViewBinding.tvName.setTextColor(getColor(R.color.color_262626))
            }

            mViewBinding.ivSelect.visibility = GONE
        }
    }

    interface ViewActionListener : ActionListener {

        fun onMenuSelected(menuInfo: MenuItemInfoVO?)
    }
}