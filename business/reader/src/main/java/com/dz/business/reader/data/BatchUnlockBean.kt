package com.dz.business.reader.data

import com.dz.business.base.data.bean.BaseBean
import com.dz.business.base.data.bean.UserTacticInfoBean

/**
 *@Author: shidz
 *@Date: 2022/11/9 10:59
 *@Description: 批量解锁
 *@Version:1.0
 */
data class BatchUnlockAct(
    var allZcList: List<RechargePayWayBean?>? = null,
    var batchUnlockGear: List<BatchUnlockGear?>? = null,
    var isPriorityPop: Int? = null,
    var showZffs: Int? = null,
    var chapterIndex: String? = null,
    var subtitle: String? = null,
    var text: String? = null,
    var title: String? = null,
    var title2: String? = null,
    var checkAgreement: Int = 0,//是否默认勾选协议，0-否，1-是
    var pop: Int? = 0,
    var showAgreement: Int? = 0,//是否显示协议，0-否，1-是
    var operateId: String? = null,//运营位id
    var userTacticInfo: UserTacticInfoBean? = null,

    ) : BaseBean() {
    //书籍来源 本地流转，非服务下发
    var source: String? = null
    var bookId: String? = null
    var bookName: String? = null
    var chapterId: String? = null
}


/**
 *@Author: shidz
 *@Date: 2022/11/9 11:09
 *@Description: 批量解锁档位
 *@Version:1.0
 */
data class BatchUnlockGear(
    var buttonText: String? = null,
    var id: String? = null,
    var price: String? = null,
    var priceText: String? = null,
    var srcPrice: String? = null,
    var srcPriceText: String? = null,
    var unlockNum: Int? = null,
    var verifyParam: String? = null
) : BaseBean() {
    var isSelected: Boolean = false
}

