package com.dz.business.reader.ui.component.block

import android.content.Context
import android.util.AttributeSet
import androidx.lifecycle.LifecycleOwner
import com.dz.business.base.main.MainMS
import com.dz.business.base.reader.ReaderMR
import com.dz.business.reader.R
import com.dz.business.reader.ReaderInsideEvents
import com.dz.business.reader.data.ReadEndResponse
import com.dz.business.reader.databinding.ReaderBookEndRecommendCompBinding
import com.dz.business.reader.utils.BookNameTitleUtil
import com.dz.business.reader.utils.ReaderConfigUtil
import com.dz.business.track.events.DzTrackEvents
import com.dz.business.track.events.sensor.PositionActionTE
import com.dz.business.track.trackProperties
import com.dz.foundation.base.utils.dp
import com.dz.foundation.imageloader.loadRoundImg
import com.dz.platform.common.base.ui.component.UIConstraintComponent

/**
 * <AUTHOR>
 * @description: 终章推荐顶部状态相关
 * @date :2022/11/16 14:28
 */
class BookEndRecommendBookComp :
    UIConstraintComponent<ReaderBookEndRecommendCompBinding, ReadEndResponse> {
    @JvmOverloads
    constructor(
        context: Context,
        attrs: AttributeSet? = null,
        defStyleAttr: Int = 0
    ) : super(context, attrs, defStyleAttr)

    override fun initData() {
        trackProperties(ignoreAutoTrack = true)
    }

    override fun initView() {

    }

    override fun initListener() {
        registerClickAction {
            trackProperties(
                elementContent = "终章推荐书封"
            )
//            mData?.recommendBookInfo?.run {
//                val sourceNode = OmapNode().apply {
//                    origin = SourceNode.origin_ydq
//                    channelId = SourceNode.MODULE_YDQ_ZZTJ
//                    channelName = "终章推荐"
//                    columnId = mData?.bookId ?: ""
//                    columnName = mData?.bookName ?: ""
//                    contentId = bookId ?: ""
//                    contentName = bookName ?: ""
//                    contentType = BookDetailMR.BOOK_DETAIL//条目跳转类型 路由 action
//                    logId = bigDataDotInfoVo?.logId ?: ""//请求ID
//                    strategyId = bigDataDotInfoVo?.strategyId ?: ""//大数据分组ID
//                    strategyName = bigDataDotInfoVo?.strategyName ?: ""//大数据分组名称
//                }
//                SourceTrace.putSourceNode(sourceNode)
//
//                //去书籍详情
//                BookDetailMR.get().bookDetail().apply {
//                    bookId = mData?.recommendBookInfo?.bookId
//                }.start()
//            }

        }
    }

    override fun bindData(data: ReadEndResponse?) {
        super.bindData(data)
        data?.recommendBookInfo?.run {
            BookNameTitleUtil.setTitleStr(data.bookName?:"",data.titlePlaceHolder,data.recommendBookTitle?:"",mViewBinding.tvTitle)
            mViewBinding.ivBookCover.loadRoundImg(coverWap, 4.dp, width = 52, height = 68)
            mViewBinding.tvBookName.text = bookName
            mViewBinding.tvAuthor.text = "作者：$author"
            mViewBinding.tvBookStatus.text = statusTips
            mViewBinding.tvBookNum.text = " · $wordSizeText"
            doLog(PositionActionTE.ACTION_SHOW)
            MainMS.get()?.activityReportEvent(data.operateId ?: "", "", 1)
            setViewColor()
        }
    }

    override fun subscribeEvent(lifecycleOwner: LifecycleOwner, lifecycleTag: String) {
        ReaderInsideEvents.get().colorStyleChanged().observe(lifecycleOwner, lifecycleTag) {
            setViewColor()
        }
    }

    private fun setViewColor() {
        if (ReaderConfigUtil.isNightMode()) {
            mViewBinding.tvTitle.setTextColor(getColor(R.color.reader_color_666666))
            mViewBinding.tvBookName.setTextColor(getColor(R.color.reader_color_FFD0D0D0))
            mViewBinding.tvAuthor.setTextColor(getColor(R.color.reader_color_FF8A8A8A))
            mViewBinding.tvBookStatus.setTextColor(getColor(R.color.reader_color_B45244))
            mViewBinding.tvBookNum.setTextColor(getColor(R.color.reader_color_FF8A8A8A))
        } else {
            mViewBinding.tvTitle.setTextColor(getColor(R.color.reader_color_40_000000))
            mViewBinding.tvBookName.setTextColor(getColor(R.color.reader_color_FF222222))
            mViewBinding.tvAuthor.setTextColor(getColor(R.color.reader_color_40_000000))
            mViewBinding.tvBookStatus.setTextColor(getColor(R.color.reader_color_E55749))
            mViewBinding.tvBookNum.setTextColor(getColor(R.color.reader_color_40_000000))
        }
    }

    private fun doLog(action: Int) {
        //神策打点
        doTrackShow(action)
        //HIVE打点
        doHiveShow()
    }

    /**
     * Hive
     */
    private fun doHiveShow() {
        mData?.recommendBookInfo?.run {

        }
    }

    private fun doTrackShow(action: Int) {
        mData?.run {
            DzTrackEvents.get()
                .positionAction()
                .action(action)
                .bookId(bookId)
                .bookName(bookName)
                .userTacticInfo(userTacticInfo)
                .oTypeId(operateId)
                .title(recommendBookInfo?.bookName)
                .contentId(recommendBookInfo?.bookId)
                .contentName(recommendBookInfo?.bookName)
                .contentType(ReaderMR.READER)
                .track()
        }

    }
}