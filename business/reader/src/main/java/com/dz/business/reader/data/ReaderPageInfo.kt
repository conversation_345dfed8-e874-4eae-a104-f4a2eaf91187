package com.dz.business.reader.data

/**
 *作者: shidz
 *创建时间 :  2023/11/22 17:56
 *功能描述:阅读器显示的页面信息
 *
 */
class ReaderPageInfo {
    companion object {
        const val PAGE_TYPE_BOOK_TEXT = 0 //书籍内容页面
        const val PAGE_TYPE_INSERT_BLOCK = 1 //插入的广告Block页面
        fun getBlockId(chapterId:String,blockPageIndex:Int):String{
           return "${chapterId}_${blockPageIndex}"
        }
    }
    var pageIndex:Int = 0
    var pageType = PAGE_TYPE_BOOK_TEXT
    var blockId:String? = null

    fun isBlockPage():Boolean{
        return pageType== PAGE_TYPE_INSERT_BLOCK
    }
}