package com.dz.business.reader.ui.dialog

import android.app.Activity
import android.content.Context
import android.view.Gravity
import android.view.ViewGroup
import com.dz.business.base.reader.data.NovelTimbreVo
import com.dz.business.base.ui.BaseDialogComp
import com.dz.business.reader.R
import com.dz.business.reader.audio.TtsPlayer
import com.dz.business.reader.data.ReaderKV
import com.dz.business.reader.databinding.ReaderDialogTimbreBinding
import com.dz.business.reader.ui.component.menu.MenuTtsTimbreItemComp
import com.dz.business.reader.utils.ReaderConfigUtil
import com.dz.business.reader.vm.ReaderDialogTimbreVM
import com.dz.foundation.base.utils.ScreenUtil
import com.dz.foundation.base.utils.dp
import com.dz.foundation.ui.view.recycler.DzRecyclerViewCell

/**
 *@Author: wanxin
 *@Date: 2023/8/22 21:00
 *@Description:
 *@Version: 1.0
 */
class DialogTimbreComp(context: Context) : BaseDialogComp<ReaderDialogTimbreBinding, ReaderDialogTimbreVM>(context) {

    private var selectedVoice: NovelTimbreVo? = null
    private var voices: MutableList<NovelTimbreVo> = mutableListOf()

    override fun initData() {
        dialogSetting.cancelable = true
        voices = TtsPlayer.instance.ttsConfig?.toneList as MutableList<NovelTimbreVo>
        if (ReaderKV.ttsVoiceId.isNotEmpty()) {
            for (i in voices.indices) {
                if (ReaderKV.ttsVoiceId.equals(voices[i].toneId)) {
                    selectedVoice = voices[i]
                }
            }
        } else {
            selectedVoice = voices[0]
        }
    }

    override fun initView() {
        mViewBinding.run {
            if (ReaderConfigUtil.isNightMode()) {
                menuBottom.setShapeBackground(
                    leftTopRadius = 22f.dp,
                    rightTopRadius = 22f.dp,
                    solidColor = getColor(R.color.reader_color_FF262626)
                )
                ivClose.setImageResource(R.drawable.reader_timer_dialog_top_arrow_night)
                tvTitle.setTextColor(getColor(R.color.reader_DBFFFFFF))
                rv.showDivider(R.color.reader_color_33FFFFFF)
            } else {
                menuBottom.setShapeBackground(
                    leftTopRadius = 22f.dp,
                    rightTopRadius = 22f.dp,
                    solidColor = getColor(R.color.reader_FFFFFFFF)
                )
                ivClose.setImageResource(R.drawable.reader_timer_dialog_top_arrow)
                tvTitle.setTextColor(getColor(R.color.reader_E6000000))
                rv.showDivider(R.color.reader_color_33000000)
            }
            menuBottom.setPadding(0, 0, 0, ScreenUtil.getNavigationHeight(context as Activity))
            rv.addCells(createCells())
        }
    }

    override fun initListener() {
        mViewBinding.ivClose.registerClickAction {
            dismiss()
        }
    }

    private fun createCells(): MutableList<DzRecyclerViewCell<*>> {
        val cellList = mutableListOf<DzRecyclerViewCell<*>>()
        for (i in voices.indices) {
            cellList.add(createItemCell(voices[i]))
        }
        return cellList
    }

    /**
     * 创建两种背景颜色的item
     *
     * @param item
     * @return
     */
    private fun createItemCell(item: NovelTimbreVo): DzRecyclerViewCell<*> {
        val cellItem = DzRecyclerViewCell<NovelTimbreVo>()
        cellItem.viewClass = MenuTtsTimbreItemComp::class.java
        cellItem.viewData = item
        cellItem.setActionListener(object : MenuTtsTimbreItemComp.TimbreChangeListener {

            override fun onChange(data: NovelTimbreVo) {
                if (selectedVoice?.index == data.index) {
                    return
                }
                selectedVoice?.apply {
                    selected = false
                    mViewBinding.rv.updateCell(index, this)
                }

                data.selected = true
                mViewBinding.rv.updateCell(data.index, data)
                selectedVoice = data

                selectedVoice?.let { voice ->
                    voice.toneId?.let { id ->
                        ReaderKV.ttsVoiceId = id
                        ReaderKV.ttsVoiceTitle = voice.name ?: ""
                        TtsPlayer.instance.voicePresenter.onVoiceChanged(voice)
                    }
                }
            }
        })
        return cellItem
    }

    override fun getEnterAnim(): Int = R.anim.common_bottom_in

    override fun getExitAnim(): Int = R.anim.common_bottom_out

}