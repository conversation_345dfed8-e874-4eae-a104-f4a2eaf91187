package com.dz.business.reader.ui.component.ad

import android.content.Context
import android.util.AttributeSet
import android.view.ViewGroup
import com.dz.business.reader.audio.TtsPlayer
import com.dz.business.reader.utils.ReaderAdUtil
import com.dz.foundation.base.utils.LogUtil
import reader.xo.base.XoFile
import reader.xo.block.Block
import reader.xo.block.ExtPageBlockView

/**
 * <AUTHOR>
 * @description:书籍插页容器
 * @date :2023/9/21
 */
class AdReaderPageContainerComp @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ExtPageBlockView(context, defStyleAttr, attrs) {

    private var mAdPageComp: AdReaderPageComp? = null

    override fun bindData(file: XoFile, block: Block) {
        super.bindData(file, block)

        if (TtsPlayer.instance.isRunning()) {
            return
        }

        mAdPageComp?.run {

            val adPageTag = getAdPageTag()

            val oldBlock = ReaderAdUtil.getInstance().getAdPageBlock(adPageTag)
            LogUtil.d(
                "king-AdReader",
                "bindData  adPageTag = $adPageTag  blockId=${block.id} oldBlock=${oldBlock} oldBlockId = ${oldBlock?.id}  == ${oldBlock==block}"
            )
            if (block.id==oldBlock?.id) {
                LogUtil.d(
                    "king-AdReader",
                    "bindData block相同 不触发重新渲染 上次刚曝光或加载过广告，不再请求新广告"
                )
                return
            }
            ReaderAdUtil.getInstance().setAdPageBlock(adPageTag,block)
            setBlock(block)
            bindData(ReaderAdUtil.getInstance().getAdBookInsertVo())


        }
    }


    fun addAdPageComp(adPageComp: AdReaderPageComp) {
        if (adPageComp.parent != null) {
            (adPageComp.parent as ViewGroup).removeView(adPageComp)
        }
        mAdPageComp = adPageComp
        val layoutParams = ViewGroup.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.MATCH_PARENT
        )
        LogUtil.d(
            "king-AdReaderPageContainerComp",
            "addAdPageComp"
        )
        addView(adPageComp, layoutParams)
    }

}