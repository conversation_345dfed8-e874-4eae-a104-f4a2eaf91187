package com.dz.business.reader.data

import com.dz.business.base.data.bean.BaseBean

/**
 *@Author: shidz
 *@Date: 2022/10/19 0:39
 *@Description: 章节基础信息 别的地方如有需要可继承扩展
 *@Version:1.0
 */
data class NovelChapterInfo(
    var bookId: String? = null,
    var chapterId: String? = null,
    var bookName: String? = null,
    var chapterName: String? = null,
    var content: String? = null,
    var expireDate: Long? = null,
    var index: Int? = null,
    var isCharge: Int? = null,
    var nextChapterId: String? = null,
    var nextChapterName: String? = null,
    var preChapterId: String? = null,
    var preChapterName: String? = null,
    var ver: String? = null,
    var chapterWordNumber: Int? = null,//章节总字数
    var buyWay: String? = null,//章节的购买情况，目前是单章加载和预加载的时候会有这个字段的赋值

    /**
     * 下发章节内容对应的密钥，用户自有听书验签
     */
    var secretKey: String? = null,

    var audioUrl: String?, //音频说明地址 有效期 3天,可以短期存储
    var mrcUrl: String?, //音频段落地址 有效期 3天,可以短期存储
    var audioFileDuration: String?, //音频时长 有效期 3天,可以短期存储

    //有声书： 1完结 0连载
    var bookStatus: Int? = null,

) : BaseBean()
