package com.dz.business.reader.load

import com.dz.business.reader.DataRepository
import com.dz.business.base.data.BBaseKV
import com.dz.business.base.helper.FloatWindowManage.Companion.TTS_TYPE
import com.dz.business.base.shelf.ShelfME
import com.dz.business.reader.data.NovelChapterInfo
import com.dz.business.reader.repository.entity.AudioBookEntity
import com.dz.business.reader.repository.entity.AudioChapterEntity
import com.dz.business.reader.repository.entity.NovelBookEntity
import com.dz.business.reader.repository.entity.NovelChapterEntity
import com.dz.business.track.trace.OmapNode
import com.dz.foundation.base.utils.LogUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch

/**
 *@Author: shidz
 *@Date: 2022/10/19 17:31
 *@Description:数据库数据处理
 *@Version:1.0
 */
class DBHelper {
    companion object {
        suspend fun insertOrUpdateChapter(novelChapterInfo: NovelChapterInfo): NovelChapterEntity {
            val chapterEntity = convertChapterInfoToEntity(novelChapterInfo)
            chapterEntity.uid = BBaseKV.userId
            DataRepository.chapterDao().insertOrUpdate(chapterEntity)
            return chapterEntity
        }

        suspend fun insertOrUpdateAudioChapter(novelChapterInfo: NovelChapterInfo): AudioChapterEntity {
            val p = DataRepository.audioChapterDao()
                .queryByCid(novelChapterInfo.bookId ?: "", novelChapterInfo.chapterId ?: "")
            LogUtil.d(
                "打印",
                "insertOrUpdateAudioChapter audioChapterDao=${p?.ptime}  ${novelChapterInfo.bookId} cid=${novelChapterInfo.chapterId}"
            )
            val time = p?.ptime ?: -1
            val chapterEntity = convertChapterInfoToAudioEntity(novelChapterInfo, time)
            LogUtil.d(
                "打印",
                "insertOrUpdateAudioChapter time=${time} ${novelChapterInfo.bookId} ${chapterEntity.ptime} cid=${novelChapterInfo.chapterId}"
            )
            chapterEntity.uid = BBaseKV.userId
            DataRepository.audioChapterDao().insertOrUpdate(chapterEntity)
            return chapterEntity
        }

        private fun convertChapterInfoToEntity(chapterInfo: NovelChapterInfo): NovelChapterEntity {
            var novelChapterEntity = NovelChapterEntity()
            novelChapterEntity.cid = chapterInfo.chapterId.toString()
            novelChapterEntity.bid = chapterInfo.bookId.toString()
            novelChapterEntity.book_name = chapterInfo.bookName
            novelChapterEntity.chapter_name = chapterInfo.chapterName
            novelChapterEntity.chapter_num = chapterInfo.index
            novelChapterEntity.content = chapterInfo.content
            novelChapterEntity.next_cid = chapterInfo.nextChapterId
            novelChapterEntity.pre_cid = chapterInfo.preChapterId
            novelChapterEntity.etime = chapterInfo.expireDate ?: 0
            novelChapterEntity.download =
                chapterInfo.content?.run { if (isNotBlank()) 1 else -1 } ?: -1
            novelChapterEntity.ver = chapterInfo.ver
            novelChapterEntity.secret_key = chapterInfo.secretKey
            novelChapterEntity.word_num = chapterInfo.chapterWordNumber
            novelChapterEntity.buy_way = chapterInfo.buyWay
            novelChapterEntity.charge = chapterInfo.isCharge
            return novelChapterEntity
        }

        private fun convertChapterInfoToAudioEntity(
            chapterInfo: NovelChapterInfo,
            time: Long
        ): AudioChapterEntity {
            LogUtil.d("XXX", "convertChapterInfoToAudioEntity,chapterInfo=${chapterInfo}")
            val novelChapterEntity = AudioChapterEntity()
            novelChapterEntity.cid = chapterInfo.chapterId.toString()
            novelChapterEntity.bid = chapterInfo.bookId.toString()
            novelChapterEntity.book_name = chapterInfo.bookName
            novelChapterEntity.chapter_name = chapterInfo.chapterName
            novelChapterEntity.chapter_num = chapterInfo.index
            novelChapterEntity.content = chapterInfo.content
            novelChapterEntity.next_cid = chapterInfo.nextChapterId
            novelChapterEntity.pre_cid = chapterInfo.preChapterId
            novelChapterEntity.etime = chapterInfo.expireDate ?: 0
            novelChapterEntity.ptime = time
            novelChapterEntity.download =
                chapterInfo.content?.run { if (isNotBlank()) 1 else -1 } ?: -1
            novelChapterEntity.ver = chapterInfo.ver
            novelChapterEntity.secret_key = chapterInfo.secretKey
            novelChapterEntity.word_num = chapterInfo.chapterWordNumber
            novelChapterEntity.buy_way = chapterInfo.buyWay
            novelChapterEntity.charge = chapterInfo.isCharge
            novelChapterEntity.audioUrl = chapterInfo.audioUrl
            novelChapterEntity.status = chapterInfo.bookStatus ?: -1
            novelChapterEntity.audioFileDuration = chapterInfo.audioFileDuration
            return novelChapterEntity
        }

        suspend fun insertOrUpdateBook(
            bookId: String?,
            bookName: String?,
            coverWap: String?,
            addToShelf: Int?,
            bookStatus: Int?,
            bookType: String? = null,
            categoryFirst: String? = null,
            author: String? = null,
            bookSource: String? = null,
            categoryThree: String? = null,
            bookUpdateTime: Long? = null,
            audioType: Int? = TTS_TYPE,
            mark: String? = null,
        ) {
            if (bookId != null) {
                LogUtil.d(
                    "打印",
                    "插入小说数据库：insertOrUpdateBook: ${bookName} audioType =${audioType}"
                )
                if (audioType == TTS_TYPE) {
                    var format = "1"
                    if (bookType == "2") {
                        format = "2"//听书
                    }
                    val queryByBid = DataRepository.bookDao().queryByBid(bookId)
                    val oldSource = queryByBid?.source
                    val novelBookEntity = NovelBookEntity(bookId).apply {
                        book_name = bookName
                        coverurl = coverWap
                        status = bookStatus
                        addToShelf?.let { add ->
                            add_to_shelf = add
                        }
                        this.format = format
                        this.category_first = categoryFirst
                        this.category_three = categoryThree
                        this.update_time = bookUpdateTime
                        this.marketing_ext = mark
                        this.author = author
                        this.source = OmapNode.handleSource(oldSource, bookSource)
                    }
                    DataRepository.bookDao().insertOrUpdateBooks(novelBookEntity)
                } else {
                    var format = "1"
                    if (bookType == "2") {
                        format = "2"//听书
                    }
                    val queryByBid = DataRepository.audioDao().queryByBid(bookId)
                    val oldSource = queryByBid?.source
                    val audioBookEntity = AudioBookEntity(bookId).apply {
                        book_name = bookName
                        coverurl = coverWap
                        status = bookStatus
                        addToShelf?.let { add ->
                            add_to_shelf = add
                        }
                        this.format = format
                        this.category_first = categoryFirst
                        this.category_three = categoryThree
                        this.update_time = bookUpdateTime
                        this.author = author
                        this.marketing_ext = mark
                        this.source = OmapNode.handleSource(oldSource, bookSource)
                    }
                    DataRepository.audioDao().insertOrUpdateBooks(audioBookEntity)
                }


            }

        }

        fun checkBookAddToShelf(
            bookId: String,
            bookName: String? = null,
            coverWap: String? = null,
            audioType: Int? = TTS_TYPE,
        ) {
            val mainScope = MainScope()
            mainScope.launch(Dispatchers.IO) {
                if (audioType == TTS_TYPE) {
                    val hasOnShelf = DataRepository.bookDao().queryByBid(bookId)?.add_to_shelf == 1
                    LogUtil.d(
                        "打印",
                        "插入前的参数：insertOrUpdateBook: checkBookAddToShelf 11  ${bookName} audioType =${TTS_TYPE}"
                    )
                    insertOrUpdateBook(bookId, bookName, coverWap, 1, null)
                    if (!hasOnShelf) {
                        postBookAddToShelf(bookId)
                    }
                } else {
                    val hasOnShelf = DataRepository.audioDao().queryByBid(bookId)?.add_to_shelf == 1
                    LogUtil.d(
                        "打印",
                        "插入前的参数：insertOrUpdateBook: checkBookAddToShelf 22  ${bookName} audioType =${audioType}"
                    )
                    insertOrUpdateBook(bookId, bookName, coverWap, 1, null, audioType = audioType)
                    if (!hasOnShelf) {
                        postBookAddToShelf(bookId, audioType = audioType)
                    }
                }
            }
        }

        /**
         * 发送通知有书籍被加入了书架
         */
        private suspend fun postBookAddToShelf(bookId: String, audioType: Int? = TTS_TYPE) {
            if (audioType == TTS_TYPE) {
                val bookEntity = DataRepository.bookDao().queryByBid(bookId)
                //需要添加到书架原来又没在书架上，则发送通知书籍被加入到书架中，外部需要处理书籍是否加书架的显示状态
                bookEntity?.run {
                    ShelfME.get().bookAddToShelf().post(this)
                }
                ShelfME.get().refreshShelf().post(null)
            } else {
                val audioEntity = DataRepository.audioDao().queryByBid(bookId)
                //需要添加到书架原来又没在书架上，则发送通知书籍被加入到书架中，外部需要处理书籍是否加书架的显示状态
                audioEntity?.run {
                    ShelfME.get().addAudioToFavorite().post(this)
                }
                ShelfME.get().refreshAudioFavorite().post(null)
            }
        }
    }
}