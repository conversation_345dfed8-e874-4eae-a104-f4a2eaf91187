package com.dz.business.reader.ui.component.ad


import android.animation.Animator
import android.content.Context
import android.os.SystemClock
import android.util.AttributeSet
import android.view.View
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.LifecycleOwner
import com.dz.business.reader.R
import com.dz.business.reader.ReaderInsideEvents
import com.dz.business.reader.ad.ReaderAdManager
import com.dz.business.reader.ad.ReaderFeedAd
import com.dz.business.reader.ad.callback.ReaderAdActionCallback
import com.dz.business.reader.ad.callback.ReaderAdLoadCallback
import com.dz.business.reader.data.ReaderAdConfigInfo
import com.dz.business.reader.databinding.ReaderBookAdBottomContainerCompBinding
import com.dz.business.reader.utils.ReaderAdUtil
import com.dz.business.reader.utils.ReaderConfigUtil
import com.dz.business.track.events.sensor.AdTE
import com.dz.foundation.base.manager.task.Task
import com.dz.foundation.base.manager.task.TaskManager
import com.dz.foundation.base.utils.AppActiveManager
import com.dz.foundation.base.utils.LogUtil
import com.dz.foundation.base.utils.ScreenUtil
import com.dz.foundation.base.utils.dp
import com.dz.foundation.ui.widget.getContainerActivity
import com.dz.platform.ad.AdManager
import com.dz.platform.common.base.ui.component.UIConstraintComponent

/**
 * <AUTHOR>
 * @description:底部banner广告
 * @date :2023/9/21
 */
class AdReaderBottomContainerComp @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : UIConstraintComponent<ReaderBookAdBottomContainerCompBinding, ReaderAdConfigInfo.BottomAdConfig>(
    context,
    attrs,
    defStyleAttr
) {

    //广告是否加载成功
    private var isAdLoadedSuccess = false

    //插页广告是否正在显示
    private var isInsertPageAdShowing = false

    //当前广告是否被遮挡
    private var isOnPause = false

    //当前广告是否被点击
    private var isOnClick = false

    private var currentFeedAd: ReaderFeedAd? = null


    private var intervalTime = 20 * 1000L //间隔时间

    private var lastShowTime: Long = -1  //广告上一次显示时间

    private var isCloseStatus = false
    private var lastCloseTime: Long = -1  //上次关闭广告的时间

    private var closeShowAgainIntervalTime = 30L * 1000

    override fun initData() {
    }

    override fun initView() {

    }

    override fun initListener() {
    }

    fun onResume() {
        if (isOnPause) {
            //点击后跳出阅读器，返回端内直接刷新广告
            LogUtil.e(
                "king_ad_bottom",
                "底通广告--刷新  onResume "
            )
            restartLoadTask()
        }
        isOnPause = false
    }

    fun onPause() {
        cancelTask()
        isOnPause = true
    }

    fun onDestroy() {

        cancelTask()
    }

    private fun restartLoadTask() {

        val period = SystemClock.elapsedRealtime() - lastShowTime

        var delay = if (period > intervalTime) {
            0L
        } else {
            intervalTime - period
        }

        var minDelay = (delay).coerceAtMost(intervalTime)

        LogUtil.e(
            "king_ad_bottom",
            "底通广告--刷新 restartLoadTask delay= $minDelay intervalTime = $intervalTime period=$period"
        )

        if (isCloseStatus) {
            val periodClose = SystemClock.elapsedRealtime() - lastCloseTime  //距上次关闭的时间间隔
            if (periodClose < closeShowAgainIntervalTime) {
                val diff = closeShowAgainIntervalTime - periodClose
                minDelay = (minDelay).coerceAtLeast(diff)
            }
            LogUtil.e(
                "king_ad_bottom",
                "底通广告--刷新 restartLoadTask isCloseStatus closeShowAgainIntervalTime $closeShowAgainIntervalTime periodClose = $periodClose delay=$minDelay"
            )
        }



        doDelayTask(minDelay)
    }

    private fun cancelTask() {
        mTask?.cancel()
        mTask = null
    }

    override fun subscribeEvent(lifecycleOwner: LifecycleOwner, lifecycleTag: String) {
        ReaderInsideEvents.get().colorStyleChanged().observe(lifecycleOwner, lifecycleTag) {
            refreshBackgroundColor()
        }
        ReaderInsideEvents.get().onPageShow().observe(lifecycleOwner, lifecycleTag) { pageInfo ->
            isInsertPageAdShowing = pageInfo.isBlockPage()
            refreshView()

        }
    }

    private fun refreshView() {
        if (needHidden()) {

            refreshViewVisible(
                isAdContentVis = false,
                isImgVis = true
            )
        } else {
            if (isAdLoadedSuccess) {
                refreshViewVisible(
                    isAdContentVis = true,
                    isImgVis = false
                )
            } else {

                refreshViewVisible(
                    isAdContentVis = false,
                    isImgVis = true
                )
            }


        }
    }


    private fun needHidden(): Boolean {
        return !bottomAndInsertCanTogetherShow() && isInsertPageAdShowing
    }

    /**
     * 底通广告是否与插页同时展示
     */
    private fun bottomAndInsertCanTogetherShow(): Boolean {
        return true
    }

    private var mTask: Task? = null
    override fun bindData(data: ReaderAdConfigInfo.BottomAdConfig?) {
        super.bindData(data)
        startLoadTask()
    }


    private fun startLoadTask() {
        mData?.let {
            if (it.isValidAd()) {
                doDelayTask(0)
            }
        }
    }


    //VIEW是否聚焦
    private var isVisibilityAggregated = false
    override fun onVisibilityAggregated(isVisible: Boolean) {
        isVisibilityAggregated = isVisible
        super.onVisibilityAggregated(isVisible)
    }


    private fun doDelayTask(delay: Long) {
        cancelTask()
        LogUtil.i(
            "king_ad_bottom",
            "底通广告--执行定时任务-->doDelayTask delay = $delay"
        )
        mTask = TaskManager.delayTask(delay) {
            if (!AppActiveManager.isOnBackground() && !needHidden() && !isOnPause) {
                LogUtil.i(
                    "king_ad_bottom",
                    "底通广告--执行定时任务-->请求底部通栏广告数据"
                )
                loadAd()
                exposure()
            } else {
                LogUtil.i(
                    "king_ad_bottom",
                    "底通广告--执行定时任务-->不请求底部通栏广告数据 前台状态 ${!AppActiveManager.isOnBackground()} isVisibilityAggregated= $isVisibilityAggregated isOnPause= $isOnPause"
                )
            }
        }
    }

    /**
     * 底部广告曝光打点
     */
    private fun exposure() {
        LogUtil.i(
                "king_ad_bottom_exposure",
                "底部广告运营位曝光"
        )
        val adId = mData?.adId ?: ""
        val blockConfigId = mData?.blockConfigId ?: ""
        LogUtil.d(
            "king_ad_bottom",
            "senADTrafficReachEvent 小说页底部banner广告触发上报流量请求事件埋点 pos=${AdTE.READER_BOTTOM_BANNER} adId=$adId"
        )
        AdManager.sendTrafficReachLog(AdTE.READER_BOTTOM_BANNER, adId, blockConfigId)
//        if (!CommInfoUtil.isVip()) {
//            DzTrackEvents.get().operationExposureTE()
//                    .operationPosition(OperationExposureTE.POSITION_READER_BOTTOM_AD)
//                    .operationName(OperationExposureTE.OPERATION_TYPE_AD_READER_BOTTOM)
//                    .track()
//        }
    }

    private fun loadAd() {
        mData?.let { data ->
            TaskManager.mainTask {
                ReaderAdManager.loadBottomAdView(
                    mViewBinding.flAdContent, data.getLoadAdParam(
                        68.dp,
                        46.dp,
                        ScreenUtil.getScreenWidth(),
                        56.dp,
                        "",
                        false
                    ),
                    object : ReaderAdLoadCallback {
                        override fun onLoaded(readerFeedAd: ReaderFeedAd) {
                            LogUtil.e("king_ad_bottom", "底通广告--onLoaded")
                            renderAd(readerFeedAd)

                            readerFeedAd.getIntervalTime()?.let {
                                LogUtil.e("king_ad_bottom", "底通广告-- onLoaded 设置间隔时间=$it")
                                intervalTime = it
                            }
                            doDelayTask(intervalTime)
                        }

                        override fun onAdFailed() {
                            LogUtil.e("king_ad_bottom", "底通广告--onAdFailed ")
                            isAdLoadedSuccess = false
                            isOnClick = false
                            // refreshViewVisible(isAdContentVis = false, isImgVis = true)
                            doDelayTask(intervalTime)
                        }

                    }
                )
            }
        }

    }


    fun renderAd(readerFeedAd: ReaderFeedAd) {
        currentFeedAd?.destroy()
        currentFeedAd = readerFeedAd
        val actionCallback = object : ReaderAdActionCallback {

            override fun onRenderSuccess() {

                isOnClick = false
                isAdLoadedSuccess = true
                if (ReaderAdUtil.getInstance().isAdShowNotice && mData != null) {

                    refreshViewVisible(
                        isAdContentVis = false,
                        isImgVis = true
                    )
                } else {

                    refreshViewVisible(
                        isAdContentVis = true,
                        isImgVis = false
                    )
                }

            }

            override fun onShow() {
                isCloseStatus = false
                lastShowTime = SystemClock.elapsedRealtime()
            }

            override fun onClick() {
                isOnClick = true
            }

            override fun onClose() {
                isCloseStatus = true
                lastCloseTime = SystemClock.elapsedRealtime()
                isAdLoadedSuccess = false
                isOnClick = false
                refreshViewVisible(isAdContentVis = false, isImgVis = true)

                currentFeedAd?.destroy()
                currentFeedAd = null

                closeShowAgainIntervalTime = (mData?.closeAdIntervalNum ?: 30) * 1000L

                val showAgainDelay = closeShowAgainIntervalTime

                LogUtil.e("king_ad_bottom", "底通广告--onClose showAgainDelay=$showAgainDelay")
                doDelayTask(showAgainDelay)

            }

            override fun onRenderFail() {
                LogUtil.e("king_ad_bottom", "底通广告--onRenderFail ")
                isAdLoadedSuccess = false
                isOnClick = false
                refreshViewVisible(isAdContentVis = false, isImgVis = true)
            }

        }
        readerFeedAd.render(mViewBinding.flAdContent, actionCallback)
    }

    private fun refreshViewVisible(isAdContentVis: Boolean, isImgVis: Boolean) {
        if (isAdContentVis) {
            if (mViewBinding.flAdContent.visibility == VISIBLE) {
                return
            }
            mViewBinding.flAdContent.visibility = VISIBLE
            mViewBinding.imgDefault.visibility = GONE
            startViewAnimate(measuredHeight.toFloat(), 0f, isAdContentVis, isImgVis)
        } else {
            if (mViewBinding.flAdContent.visibility == GONE) {
                return
            }
            startViewAnimate(0f, measuredHeight.toFloat(), isAdContentVis, isImgVis)
        }


    }

    private fun startViewAnimate(
        startY: Float,
        endY: Float,
        isAdContentVis: Boolean,
        isImgVis: Boolean
    ) {

        mViewBinding.flAdContent.translationY = startY
        mViewBinding.flAdContent.animate().translationY(endY).setListener(object :
            Animator.AnimatorListener {
            override fun onAnimationStart(animation: Animator) {
            }

            override fun onAnimationEnd(animation: Animator) {
                mViewBinding.flAdContent.visibility = if (isAdContentVis) VISIBLE else GONE
                mViewBinding.imgDefault.visibility = if (isImgVis) VISIBLE else GONE
                if (isImgVis) {
                    mViewBinding.imgDefault.alpha = 0f
                    mViewBinding.imgDefault.animate().alpha(1f).setListener(null)
                }

            }

            override fun onAnimationCancel(animation: Animator) {
            }

            override fun onAnimationRepeat(animation: Animator) {
            }

        })
    }


    fun refreshBackgroundColor() {

        currentFeedAd?.setNightMode(ReaderConfigUtil.isNightMode())
        if (ReaderConfigUtil.isNightMode()) {
            setBackgroundColor(getColor(R.color.reader_config_color_style_bg_night))
            mViewBinding.imgDefault.setImageResource(R.drawable.reader_ic_ad_bottom_night)
        } else {
            val currentColorStyle = ReaderConfigUtil.getCurrentColorStyle()
            currentColorStyle?.run {
                setBackgroundColor(bgColor)
            }
            mViewBinding.imgDefault.setImageResource(R.drawable.reader_ic_ad_bottom_default)
        }

    }


    private var lifecycleObserver: LifecycleObserver? = null
    private fun addActivityLifeListener() {
        getContainerActivity()?.let { activity ->
            if (activity is LifecycleOwner) {
                lifecycleObserver = object : DefaultLifecycleObserver {

                    override fun onDestroy(owner: LifecycleOwner) {
                        super.onDestroy(owner)
                        currentFeedAd?.destroy()
                    }

                }
                lifecycleObserver?.let {
                    activity.lifecycle.addObserver(it)
                }

            }
        }
    }

    private fun removeActivityLifeListener() {
        getContainerActivity()?.let { activity ->
            if (activity is LifecycleOwner) {
                lifecycleObserver?.let {
                    activity.lifecycle.removeObserver(it)
                }

            }
        }
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        addActivityLifeListener()
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        removeActivityLifeListener()
    }


    /**
     * 显示广告
     */
    fun show() {
        visibility = View.VISIBLE
        refreshBackgroundColor()
    }

    fun startLoadAd(bottomAdConfig: ReaderAdConfigInfo.BottomAdConfig?){
        bottomAdConfig?.let {
            bindData(it)
        }
    }
    /**
     *作者: shidz
     *创建时间 :  2023/11/21 16:22
     *功能描述: 开通vip 后去除广告
     *
     */
    fun hidden() {
        visibility = View.GONE
        cancelTask()
        currentFeedAd?.destroy()
        currentFeedAd = null
        lastShowTime = -1
        lastCloseTime = -1
        isCloseStatus = false

    }
}