package com.dz.business.reader.data

import com.dz.business.base.data.bean.BaseBean
import com.dz.business.base.data.bean.UserTacticInfoBean
import com.dz.foundation.base.utils.dp2px
import java.util.*

data class ChapterOpenBean(
    var currentChatperId: String? = null,
    var bookId: String? = null,
    var bookName: String? = null,
    var diff: Int? = null,
    var ownTts: Int? = null,
    var recommendBookInfo: RecommendBookInfo? = null,
    var scoreBannerInfo: ScoreBannerInfo? = null,
    var textActionAct: TextActionAct? = null,
//    var ttsEnable: Int? = null,  // 听书是否可用。历史代码，已经不再使用。以1502接口下发的ttsEnable为准。v2.4开始。
    var isVip: String? = null,//是否是vip
    var novelAdVo: ReaderAdConfigInfo? = null, //广告配置
    var totalSize: Int? = null, //截止本章节的总字数（p>13）
    var wordNum: Int? = null, //本章节的总字数（p>13）
) : BaseBean() {
    val dataId = UUID.randomUUID().toString()

    /**
     * 是否有章末运营位
     *
     * @return
     */
    fun hasMarketingItem(): Boolean {
        return !(recommendBookInfo == null && scoreBannerInfo == null && textActionAct == null)
    }

    private var shownMarketingItem = false

    fun hasShownMarketingItem(): Boolean {

        return shownMarketingItem
    }

    fun setHasShownMarketingItem() {
        shownMarketingItem = true
    }


    /**
     * 获取能显示运营位的最小高度
     *
     */
    fun getMarketingMinHeight(): Float {

        val marginTop = 20.dp2px
        val marginBottom = 20.dp2px
        var minHeightItem: ChapterEndBlockItem? = textActionAct

        if (minHeightItem == null) {
            minHeightItem = scoreBannerInfo
        }

        if (minHeightItem == null) {
            minHeightItem = recommendBookInfo
        }
        return minHeightItem?.run { viewHeight + marginTop + marginBottom } ?: 0f

    }

    /**
     * 获取能显示运营位的最小高度
     *
     */
    fun getMarketingHeight(): Float {

        val marginTop = 20.dp2px
        val marginBottom = 20.dp2px
        var marketingItem: ChapterEndBlockItem? = scoreBannerInfo

        if (marketingItem == null) {
            marketingItem = recommendBookInfo
        }

        if (marketingItem == null) {
            marketingItem = textActionAct
        }
        return marketingItem?.run { viewHeight + marginTop + marginBottom } ?: 0f

    }

}

data class RecommendBookInfo(
    var id: String? = null,
    var tags: List<String>? = null,
    var title: String? = null,
    var userTacticInfo: UserTacticInfoBean? = null,//用户分层信息
    var titlePlaceHolder:String?=null
) : NovelBookInfo(), ChapterEndBlockItem {
    override val viewHeight: Float = 148.dp2px
    var currentBookId: String? = ""
    var currentBookName: String? = ""
}

data class ScoreBannerInfo(
    var title: String? = null,
    var totalScore: Int? = null
) : BaseBean(), ChapterEndBlockItem {
    var bookId: String? = null
    var bookName: String? = null
    var chapterId: String? = null
    override val viewHeight: Float = 110.dp2px
    var hasScored = false//是否已经评分
    var score: Int = 0//评分分数

}

data class TextActionAct(
    var action: String? = null,
    var allClick: Int? = null,
    var buttonTxt: String? = null,
    var countDown: Int? = null,
    var description: String? = null,
    var id: String? = null,
    var img: String? = null,
    var title: String? = null,
    var activityId: String? = null,
    var userTacticInfo: UserTacticInfoBean? = null//用户分层信息
) : BaseBean(), ChapterEndBlockItem {
    override val viewHeight: Float = 45.dp2px
    var bookId: String? = null
    var bookName: String? = null
}

interface ChapterEndBlockItem {
    val viewHeight: Float
}

