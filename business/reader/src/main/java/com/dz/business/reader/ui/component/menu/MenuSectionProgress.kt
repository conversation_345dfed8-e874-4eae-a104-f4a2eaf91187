package com.dz.business.reader.ui.component.menu

import android.content.Context
import android.util.AttributeSet
import android.widget.SeekBar
import androidx.lifecycle.LifecycleOwner
import com.dz.business.base.reader.ReaderME
import com.dz.business.reader.audio.TtsPlayer
import com.dz.business.reader.databinding.ReaderMenuSeekbarCompBinding
import com.dz.business.reader.utils.MenuTtsConfig
import com.dz.foundation.base.utils.LogUtil
import com.dz.foundation.ui.view.custom.ActionListener
import com.dz.foundation.ui.view.custom.ActionListenerOwner
import com.dz.platform.common.base.ui.component.UIConstraintComponent

/**
 * @Author: guyh
 * @Date: 2022/11/3 10:38
 * @Description: 章节进度条
 * @Version:1.0
 */
class MenuSectionProgress @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : UIConstraintComponent<ReaderMenuSeekbarCompBinding, MenuSectionProgress.SectionProgressBean>(
    context,
    attrs,
    defStyleAttr
), ActionListenerOwner<MenuSectionProgress.ViewActionListener> {

    data class SectionProgressBean(var sectionProgress: Int = 0)

    private var isTouching: Boolean = false

    override var mActionListener: ViewActionListener? = null

    interface ViewActionListener : ActionListener, SeekBar.OnSeekBarChangeListener {
    }

    override fun initData() {
    }

    override fun initView() {
        mViewBinding.seekbarSectionProgress.progressDrawable =
            getDrawable(MenuTtsConfig.getProgressDrawableNormal())
        mViewBinding.seekbarSectionProgress.thumb =
            getDrawable(MenuTtsConfig.getProgressThumbNormal())
        mViewBinding.seekbarSectionProgress.setOnSeekBarChangeListener(object :
            SeekBar.OnSeekBarChangeListener {

            override fun onProgressChanged(seekBar: SeekBar, i: Int, fromUser: Boolean) {
                mActionListener?.onProgressChanged(seekBar, i, fromUser)
            }

            override fun onStartTrackingTouch(seekBar: SeekBar) {
                mActionListener?.onStartTrackingTouch(seekBar)
                isTouching = true
                mViewBinding.seekbarSectionProgress.progressDrawable =
                    getDrawable(MenuTtsConfig.getProgressDrawableTouch())
                mViewBinding.seekbarSectionProgress.thumb =
                    getDrawable(MenuTtsConfig.getProgressThumbTouch())

            }

            override fun onStopTrackingTouch(seekBar: SeekBar) {
                mActionListener?.onStopTrackingTouch(seekBar)
                isTouching = false
                mViewBinding.seekbarSectionProgress.progressDrawable =
                    getDrawable(MenuTtsConfig.getProgressDrawableNormal())
                mViewBinding.seekbarSectionProgress.thumb =
                    getDrawable(MenuTtsConfig.getProgressThumbNormal())
            }
        })
    }

    override fun initListener() {
    }

    override fun setActionListener(actionListener: ViewActionListener?) {
        super.setActionListener(actionListener)

    }

    fun setMaxCount(count: Int) {
        LogUtil.d("XXX", "setMaxCount,count=$count")
        mViewBinding.seekbarSectionProgress.max = count
    }

    override fun bindData(data: SectionProgressBean?) {
        super.bindData(data)
        data?.let {
            if (!isTouching) {
                mViewBinding.seekbarSectionProgress.progress = it.sectionProgress
            }
        }
    }

    override fun subscribeEvent(lifecycleOwner: LifecycleOwner, lifecycleTag: String) {
        super.subscribeEvent(lifecycleOwner, lifecycleTag)
        ReaderME.get().ttsStatusChanged().observe(lifecycleOwner) {
            // 当暂停状态且不可恢复时，禁用 seekbar
            mViewBinding.seekbarSectionProgress.isEnabled = (it != TtsPlayer.STATUS_LOCKED &&
                    it != TtsPlayer.STATUS_LOADING)
        }
    }
}