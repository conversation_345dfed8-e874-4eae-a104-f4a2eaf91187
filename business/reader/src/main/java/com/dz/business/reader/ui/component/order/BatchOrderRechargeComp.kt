package com.dz.business.reader.ui.component.order

import android.content.Context
import android.util.AttributeSet
import com.dz.business.reader.data.RechargePayWayBean
import com.dz.business.reader.data.*
import com.dz.business.reader.databinding.ReaderBatchOrderRechargeCompBinding
import com.dz.foundation.ui.view.custom.ActionListener
import com.dz.foundation.ui.view.custom.ActionListenerOwner
import com.dz.foundation.ui.view.recycler.DzRecyclerViewCell
import com.dz.platform.common.base.ui.component.UIConstraintComponent

/**
 *@Author: shidz
 *@Date: 2022/11/10 16:51
 *@Description: 批量订购充值信息组件
 *@Version:1.0
 */
class BatchOrderRechargeComp @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : UIConstraintComponent<ReaderBatchOrderRechargeCompBinding, BatchOrderBean>(
    context,
    attrs,
    defStyleAttr
), ActionListenerOwner<BatchOrderRechargeActionListener> {


    override fun initData() {
    }

    override fun initView() {
    }

    override fun initListener() {

    }

    override fun bindData(data: BatchOrderBean?) {
        super.bindData(data)
        data?.let { setViewData(data) }

    }

    private fun setViewData(data: BatchOrderBean) {
        data.batchOrderGearInfo?.let { setGearInfo(it) }
        data.allZcList?.let {
            val payWayBean = PayWayBean().apply {
                title = data.title2
                isValid = data.isValidBatchOrderGear()
                payWayItemList = it
            }
            mViewBinding.compPayWay.setActionListener(object : RechargePayWayCompActionListener {
                override fun onPayWayChecked(bean: RechargePayWayBean) {
                    onPayWayItemChecked(bean)
                }
            })
            mViewBinding.compPayWay.bindData(payWayBean)
        }
    }


    private fun setGearInfo(gearList: List<BatchOrderGear?>) {
        mViewBinding.rvMoney.addCells(createGearCells(gearList))
    }

    private fun createGearCells(gearList: List<BatchOrderGear?>): MutableList<DzRecyclerViewCell<BatchOrderGear>>? {
        val cellList = mutableListOf<DzRecyclerViewCell<BatchOrderGear>>()
        var defaultSelectIndex = -1
        gearList.forEachIndexed { index, batchOrderGear ->
            batchOrderGear?.run {
                if (valid == 1 && defaultSelectIndex == -1) {
                    defaultSelectIndex = index
                    isSelected = true
                    onGearItemChecked(this)
                }
                this.chapterUnit = mData?.chapterUnit
                val cellItem = createGearItemCell(this)
                if (cellItem != null) {
                    cellList.add(cellItem)
                }
            }

        }
        return cellList
    }

    private fun onGearItemChecked(batchUnlockGear: BatchOrderGear) {
        mActionListener?.onGearChecked(batchUnlockGear)
        //点击挡位判断余额是否足够，余额足则不显示充值页面
        if (mData?.showZffs == 1 && batchUnlockGear.isEnough != 1) {
            mViewBinding.compPayWay.visibility = VISIBLE
        } else {
            mViewBinding.compPayWay.visibility = GONE
        }

    }

    private fun onPayWayItemChecked(bean: RechargePayWayBean) {
        mActionListener?.onPayWayItemChecked(bean)
    }

    private fun createGearItemCell(
        item: BatchOrderGear
    ): DzRecyclerViewCell<BatchOrderGear> {
        return DzRecyclerViewCell<BatchOrderGear>().apply {

            viewClass =
                if (item.valid == 1) BatchOrderGearItemComp::class.java else BatchOrderInvalidGearItemComp::class.java
            viewData = item
            if (item.valid == 1) setActionListener(gearItemActionListener)
        }
    }

    private val gearItemActionListener =
        object : BatchOrderGearActionListener {
            override fun onGearChecked(gear: BatchOrderGear) {
                val allCells = mViewBinding.rvMoney.allCells
                for (cellItem in allCells) {
                    val batchOrderGear = cellItem.viewData as BatchOrderGear
                    if (batchOrderGear == gear) {
                        if (!batchOrderGear.isSelected) {
                            batchOrderGear.isSelected = true
                            mViewBinding.rvMoney.updateCell(cellItem, batchOrderGear)
                            onGearItemChecked(batchOrderGear)
                        }
                    } else if (batchOrderGear.isSelected) {
                        batchOrderGear.isSelected = false
                        mViewBinding.rvMoney.updateCell(cellItem, batchOrderGear)
                    }
                }

            }

        }


    override var mActionListener: BatchOrderRechargeActionListener? = null


}

interface BatchOrderRechargeActionListener : ActionListener {
    fun onGearChecked(gear: BatchOrderGear)
    fun onPayWayItemChecked(bean: RechargePayWayBean)
}