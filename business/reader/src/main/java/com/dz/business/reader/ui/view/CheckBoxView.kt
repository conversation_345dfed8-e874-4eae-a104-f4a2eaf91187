package com.dz.business.reader.ui.view

import android.animation.Animator
import android.animation.AnimatorSet
import android.animation.PointFEvaluator
import android.animation.ValueAnimator
import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Path
import android.graphics.PointF
import android.util.AttributeSet
import android.view.View
import androidx.core.content.ContextCompat
import androidx.interpolator.view.animation.FastOutSlowInInterpolator
import com.dz.business.reader.R
import com.dz.foundation.base.utils.dp

/**
 *@Author: wanxin
 *@Date: 2023/8/16 10:37
 *@Description: 荣耀多选view
 *@Version: 1.0
 */
class CheckBoxView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) :
    View(context, attrs, defStyleAttr) {
    private val animatorDuration = 150L

    private var mWidth = 0f
    private var mHeight = 0f
    private var centerX = 0f
    private var centerY = 0f
    var checked = false
        set(value) {
            field = value
            changeCheckedWithoutAnimator()
        }
    private var hasAnimation = false

    private var blueRadius = 0f
    private var blueAlpha = 255
    private var tickAlpha = 0

    private val path = Path()

    // 对勾开始点
    private lateinit var startPoint: PointF

    // 对勾转折点
    private lateinit var breakPoint: PointF

    // 对勾结束点
    private lateinit var endPoint: PointF

    private var movePoint: PointF = PointF()

    private val paintBaseCircle = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        color = ContextCompat.getColor(getContext(), R.color.reader_26000000_4DFFFFFF)
        style = Paint.Style.STROKE
        strokeWidth = 1.5f.dp
    }

    private val paintSelectCircle = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        color = ContextCompat.getColor(getContext(), R.color.reader_FF00AAEE_FF007AAC)
        style = Paint.Style.FILL
    }

    private val paintTick = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        color = ContextCompat.getColor(getContext(), R.color.reader_FFFFFFFF_FFFFFFFF)
        style = Paint.Style.STROKE
        strokeWidth = 2f.dp
    }

    fun setDartTheme(isDark: Boolean) {
        if (isDark) {
            paintBaseCircle.color = ContextCompat.getColor(context, R.color.reader_4DFFFFFF_4DFFFFFF)
            paintSelectCircle.color =
                ContextCompat.getColor(context, R.color.reader_FF007AAC_FF007AAC)
        } else {
            paintBaseCircle.color = ContextCompat.getColor(context, R.color.reader_26000000_26000000)
            paintSelectCircle.color =
                ContextCompat.getColor(context, R.color.reader_FF00AAEE_FF00AAEE)
        }
        invalidate()
    }


    fun changeChecked(checked: Boolean) {
        if (this.checked == checked) {
            return
        }
        this.checked = checked
        if (::startPoint.isInitialized) {
            hasAnimation = true
            if (checked) {
                selectAnimator()
            } else {
                unSelectAnimator()
            }
        } else {
            changeCheckedWithoutAnimator()
        }
    }

    private fun changeCheckedWithoutAnimator() {
        if (::startPoint.isInitialized) {
            hasAnimation = false
            if (checked) {
                selectAnimator()
            } else {
                unSelectAnimator()
            }
        } else {
            post {
                changeCheckedWithoutAnimator()
            }
        }

    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        mWidth = width.toFloat()
        mHeight = height.toFloat()
        centerX = mWidth / 2
        centerY = mHeight / 2

        // 对勾开始点
        startPoint =
            PointF().apply {
                set(centerX * 0.47f, centerX * 0.93f)
            }


        // 对勾转折点
        breakPoint =
            PointF().apply {
                set(centerX * 0.86f, centerX * 1.3f)
            }


        // 对勾结束点
        endPoint =
            PointF().apply {
                set(centerX * 1.47f, centerX * 0.7f)
            }

    }

    override fun onDraw(canvas: Canvas) {

        //画底层圆环
        canvas.drawCircle(
            centerX,
            centerY,
            centerX - paintBaseCircle.strokeWidth / 2,
            paintBaseCircle
        )
        //画蓝色圆形
        paintSelectCircle.alpha = blueAlpha
        canvas.drawCircle(centerX, centerY, blueRadius, paintSelectCircle)
        //画对钩
        if (checked) {
            path.lineTo(movePoint.x, movePoint.y)
            canvas.drawPath(path, paintTick)
        } else {
            paintTick.alpha = tickAlpha
            canvas.drawPath(path, paintTick)
        }

    }


    private fun selectAnimator() {
        if (!hasAnimation) {
            blueRadius = centerX
            blueAlpha = 255
            paintTick.alpha = 255
            path.reset()
            path.moveTo(startPoint.x, startPoint.y)
            path.lineTo(breakPoint.x, breakPoint.y)
            path.lineTo(endPoint.x, endPoint.y)
            movePoint = endPoint
            invalidate()
            return
        }
        val animator1 = ValueAnimator.ofFloat(0.8f, 1f).apply {
            addUpdateListener { animation ->
                val percent = animation.animatedValue as Float
                blueRadius = centerX * percent
                invalidate()
            }
        }
        val animator2 = ValueAnimator.ofFloat(0f, 1f).apply {
            addUpdateListener { animation ->
                val percent = animation.animatedValue as Float
                blueAlpha = (255 * percent).toInt()
                invalidate()
            }
        }

        val animator3 =
            ValueAnimator.ofObject(PointFEvaluator(), startPoint, breakPoint, breakPoint, endPoint)
                .apply {
                    addUpdateListener { animation ->
                        movePoint = animation.animatedValue as PointF
                        invalidate()
                    }
                    addListener(object : Animator.AnimatorListener {
                        override fun onAnimationStart(animation: Animator) {
                            paintTick.alpha = 255
                            path.reset()
                            path.moveTo(startPoint.x, startPoint.y)
                        }

                        override fun onAnimationEnd(animation: Animator) {
                        }

                        override fun onAnimationCancel(animation: Animator) {
                        }

                        override fun onAnimationRepeat(animation: Animator) {
                        }
                    })
                }
        val animatorSet = AnimatorSet().apply {
            interpolator = FastOutSlowInInterpolator()
            duration = animatorDuration
            playTogether(animator1, animator2, animator3)
        }
        animatorSet.start()
    }

    private fun unSelectAnimator() {
        if (!hasAnimation) {
            blueRadius = 0f
            blueAlpha = 0
            tickAlpha = 0
            invalidate()
            return
        }
        val animator1 = ValueAnimator.ofFloat(1f, 0.8f).apply {
            addUpdateListener { animation ->
                val percent = animation.animatedValue as Float
                blueRadius = centerX * percent
                invalidate()
            }
        }
        val animator2 = ValueAnimator.ofFloat(1f, 0f).apply {
            addUpdateListener { animation ->
                val percent = animation.animatedValue as Float
                blueAlpha = (255 * percent).toInt()
                tickAlpha = (255 * percent).toInt()
                invalidate()
            }
        }

        val animatorSet = AnimatorSet().apply {
            interpolator = FastOutSlowInInterpolator()
            duration = animatorDuration
            playTogether(animator1, animator2)
            addListener(object : Animator.AnimatorListener {
                override fun onAnimationStart(animation: Animator) {
                }

                override fun onAnimationEnd(animation: Animator) {
                    path.reset()
                    path.moveTo(startPoint.x, startPoint.y)
                }

                override fun onAnimationCancel(animation: Animator) {
                }

                override fun onAnimationRepeat(animation: Animator) {
                }
            })
        }
        animatorSet.start()
    }

}