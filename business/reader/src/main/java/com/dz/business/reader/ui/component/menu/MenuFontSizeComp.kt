package com.dz.business.reader.ui.component.menu

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import androidx.lifecycle.LifecycleOwner
import com.dz.business.reader.R
import com.dz.business.reader.ReaderInsideEvents
import com.dz.business.reader.databinding.ReaderMenuFontSizeCompBinding
import com.dz.business.reader.utils.ReaderConfigUtil
import com.dz.business.track.trackProperties
import com.dz.foundation.base.utils.ScreenUtil
import com.dz.foundation.base.utils.dp
import com.dz.foundation.ui.view.custom.ActionListener
import com.dz.foundation.ui.view.custom.ActionListenerOwner
import com.dz.platform.common.base.ui.component.UIConstraintComponent
import com.dz.platform.common.toast.ToastManager

/**
 *@Author: shidz
 *@Date: 2022/10/26 10:33
 *@Description: 设置字体大小
 *@Version:1.0
 */
class MenuFontSizeComp @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : UIConstraintComponent<ReaderMenuFontSizeCompBinding, MenuFontSizeComp.FontSizeBean>(
    context,
    attrs,
    defStyleAttr
),
    ActionListenerOwner<MenuFontSizeComp.ViewActionListener> {

    private var isReduceDown = false//缩小按钮是否按下
    private var isIncreaseDown = false//放大按钮是否按下

    override fun initData() {

    }

    override fun initView() {
        setMode()
        trackConfig()
    }

    /**
     *
     *打点配置
     */
    private fun trackConfig() {
        mViewBinding.llReduce.trackProperties(ignoreAutoTrack = true)
        mViewBinding.llIncrease.trackProperties(ignoreAutoTrack = true)
    }

    override fun bindData(data: FontSizeBean?) {
        super.bindData(data)
        data?.let {
            setViewData(it)
        }
    }

    private fun setViewData(data: FontSizeBean) {
        mViewBinding.tvFontSize.text = data.fontSize.toString()
    }

    override fun initListener() {
        mViewBinding.llReduce.registerClickAction { reduceFontSize() }
        mViewBinding.llReduce.setOnTouchListener { v, event ->
            when (event?.action) {
                MotionEvent.ACTION_DOWN -> {
                    if (!isReduceDown) {
                        isReduceDown = true
                        mViewBinding.tvReduce.alpha = 0.5F
                        mViewBinding.llReduce.alpha = 0.7F
                    }
                }
                MotionEvent.ACTION_UP -> {
                    if (isReduceDown) {
                        isReduceDown = false
                        mViewBinding.tvReduce.alpha = 1F
                        mViewBinding.llReduce.alpha = 1F
                    }
                }
            }
            false
        }
        mViewBinding.llIncrease.registerClickAction { increaseFontSize() }
        mViewBinding.llIncrease.setOnTouchListener { v, event ->
            when (event?.action) {
                MotionEvent.ACTION_DOWN -> {
                    if (!isIncreaseDown) {
                        isIncreaseDown = true
                        mViewBinding.tvIncrease.alpha = 0.5F
                        mViewBinding.llIncrease.alpha = 0.7F
                    }
                }
                MotionEvent.ACTION_UP -> {
                    if (isIncreaseDown) {
                        isIncreaseDown = false
                        mViewBinding.tvIncrease.alpha = 1F
                        mViewBinding.llIncrease.alpha = 1F
                    }
                }
            }
            false
        }
    }

    private fun reduceFontSize() {
        mData?.run {
            if (fontSize <= 12) {
                ToastManager.showToast("已是最小字号")
                return
            }
            fontSize -= 2
            setFontSize(fontSize)
            setViewData(this)
            mViewBinding.llReduce.trackProperties(elementParam = fontSize)
        }
    }

    private fun increaseFontSize() {
        mData?.run {
            if (fontSize >= 60) {
                ToastManager.showToast("已是最大字号")
                return
            }
            fontSize += 2
            setFontSize(fontSize)
            setViewData(this)
            mViewBinding.llIncrease.trackProperties(elementParam = fontSize)
        }
    }

    private fun setFontSize(fontSize: Int) {
        mActionListener?.setFontSize(fontSize)
    }

    override var mActionListener: ViewActionListener? = null

    interface ViewActionListener : ActionListener {
        fun setFontSize(fontSize: Int)
    }

    data class FontSizeBean(var fontSize: Int = ReaderConfigUtil.getDefaultFontSize()) {

    }

    override fun subscribeEvent(lifecycleOwner: LifecycleOwner, lifecycleTag: String) {
        ReaderInsideEvents.get().colorStyleChanged().observe(lifecycleOwner, lifecycleTag) {
            setMode()
        }
    }

    private fun setMode() {
        if (ReaderConfigUtil.isNightMode()) setNightMode() else setDayMode()
    }

    private fun setDayMode() {
        mViewBinding.run {
            tvFontSizeTitle.setTextColor(getColor(R.color.reader_title_text_color_day))
            llReduce.setShapeBackground(
                solidColor = getColor(R.color.reader_menu_item_bkg_day),
                radius = 18.dp.toFloat()
            )
            tvReduce.setTextColor(getColor(R.color.reader_title_text_color_day))
            tvFontSize.setTextColor(getColor(R.color.reader_title_text_color_day))
            llIncrease.setShapeBackground(
                solidColor = getColor(R.color.reader_menu_item_bkg_day),
                radius = ScreenUtil.dip2px(context, 18.5F)
            )
            tvIncrease.setTextColor(getColor(R.color.reader_title_text_color_day))
        }
    }

    private fun setNightMode() {
        mViewBinding.run {
            tvFontSizeTitle.setTextColor(getColor(R.color.reader_title_text_color_night))
            llReduce.setShapeBackground(
                solidColor = getColor(R.color.reader_menu_item_bkg_night),
                radius = 18.dp.toFloat()
            )
            tvReduce.setTextColor(getColor(R.color.reader_title_text_color_night))
            tvFontSize.setTextColor(getColor(R.color.reader_title_text_color_night))
            llIncrease.setShapeBackground(
                solidColor = getColor(R.color.reader_menu_item_bkg_night),
                radius = ScreenUtil.dip2px(context, 18.5F)
            )
            tvIncrease.setTextColor(getColor(R.color.reader_title_text_color_night))
        }
    }
}