package com.dz.business.reader.ui.component.order

import android.content.Context
import android.util.AttributeSet
import com.dz.business.reader.data.BatchOrderGear
import com.dz.business.reader.databinding.ReaderBatchOrderInvalidGearItemCompBinding
import com.dz.platform.common.base.ui.component.UIConstraintComponent

/**
 *@Author: shidz
 *@Date: 2022/11/15 9:49
 *@Description:无效的批量订购挡位
 *@Version:1.0
 */
class BatchOrderInvalidGearItemComp :
    UIConstraintComponent<ReaderBatchOrderInvalidGearItemCompBinding, BatchOrderGear> {
    @JvmOverloads
    constructor(
        context: Context,
        attrs: AttributeSet? = null,
        defStyleAttr: Int = 0
    ) : super(context, attrs, defStyleAttr)

    override fun initData() {
    }

    override fun initView() {
    }

    override fun initListener() {
    }

    override fun bindData(data: BatchOrderGear?) {
        super.bindData(data)
        data?.run {
            setViewData(this)

        }
    }

    private fun setViewData(gear: BatchOrderGear) {
        mViewBinding.tvChapterNum.text = gear.batchNum.toString()
        mViewBinding.tvAmount.text = gear.amountText
        mViewBinding.clRoot.isSelected = gear.isSelected
        if (gear.cornerMark.isNullOrEmpty()) {
            mViewBinding.tvCorner.visibility = GONE
        } else {
            mViewBinding.tvCorner.visibility = VISIBLE
            mViewBinding.tvCorner.text = gear.cornerMark
        }

    }

}