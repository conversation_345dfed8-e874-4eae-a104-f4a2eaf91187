package com.dz.business.reader.ui.component.order


import android.content.Context
import android.graphics.Paint
import android.util.AttributeSet
import androidx.lifecycle.LifecycleOwner
import com.dz.business.base.data.bean.BaseOperationBean
import com.dz.business.base.reader.intent.SingleOrderIntent
import com.dz.business.reader.R
import com.dz.business.reader.ReaderInsideEvents
import com.dz.business.reader.data.BatchUnlockAct
import com.dz.business.reader.data.EmptyBlockInfo
import com.dz.business.reader.data.LoadOneChapterBean
import com.dz.business.reader.data.OrderPageVo
import com.dz.business.reader.databinding.ReaderChapterPreviewOrderCompBinding
import com.dz.business.reader.ui.component.block.ReaderBlockComp
import com.dz.business.reader.ui.page.ReaderActivity
import com.dz.business.reader.utils.ChapterOrderRefreshCheckUtil
import com.dz.business.reader.utils.ReaderConfigUtil
import com.dz.foundation.base.utils.dp
import com.dz.foundation.base.utils.dp2px
import com.dz.foundation.ui.view.custom.ActionListener
import com.dz.foundation.ui.view.custom.ActionListenerOwner
import com.dz.foundation.ui.widget.getContainerActivity
import com.dz.platform.common.base.ui.component.UIConstraintComponent
import com.dz.platform.common.router.SchemeRouter
import reader.xo.block.Block
import reader.xo.config.ColorStyle
import reader.xo.config.LayoutStyle

/**
 *@Author: shidz
 *@Date: 2022/11/9 18:05
 *@Description: 订购章节预览页面
 *@Version:1.0
 */
class ChapterPreviewOrderComp @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : UIConstraintComponent<ReaderChapterPreviewOrderCompBinding, LoadOneChapterBean>(
    context,
    attrs,
    defStyleAttr
), ActionListenerOwner<ChapterPreviewOrderComp.ViewActionListener>, ReaderBlockComp {

    override fun initData() {

    }

    override fun initView() {
        resetColorMode()
        mViewBinding.tvTextPreview.containsTitle = true

    }


    override fun initListener() {
        mViewBinding.btnAction.registerClickAction { mData?.let { toBuy(it) } }
    }

    private fun toBuy(data: LoadOneChapterBean) {
        if (data.status == LoadOneChapterBean.STATUS4) {
            //确认订购
            confirmPay(data, getAutoPayChecked())
        } else {
            //拉起充值弹窗页面
            showOrderDialog(data)
        }
    }

    private fun showOrderDialog(data: LoadOneChapterBean) {
        data.orderPageVo?.singleOrderDialogShowing = true
//        ReaderMR.get().singleOrder().apply {
//            activityPageId = getActivityPageId()
//            this.data = data
//            onDismiss {
//                data.orderPageVo?.singleOrderDialogShowing = false
//            }
//            setRouteCallback(getUiId(), rechargeCallback)
//        }.start()
    }

    private val rechargeCallback = object : SingleOrderIntent.SingleOrderCallback {
        override fun onRechargeSuccess(autoPay: Boolean?) {
            ChapterOrderRefreshCheckUtil.jumpCheckReload = true
            confirmPay(mData, autoPay)
        }
    }

    /**
     * 确认订购
     *
     * @param data
     */
    private fun confirmPay(
        data: LoadOneChapterBean?,
        autoPay: Boolean?
    ) {
        data?.run {
            getContainerActivity()?.run {
                val bookId = data.bookId ?: ""
                val chapterId = data.orderPageVo?.chapterId ?: ""
                (this as ReaderActivity).confirmPay(bookId, chapterId, autoPay)
            }
        }

    }

    private fun getAutoPayChecked(): Boolean? {
        if (mViewBinding.compAutoPayCheck.visibility == VISIBLE) {
            return mViewBinding.compAutoPayCheck.isAutoPayChecked()
        }
        return null
    }

    override fun bindData(data: LoadOneChapterBean?) {
        super.bindData(data)
        data?.orderPageVo?.let { setViewData(it) }
        trackShow()
    }

    private fun trackShow() {
//        DzTrackEvents.get().hivePv().pType(ReaderMR.SINGLE_ORDER).addSource(mData?.source)
//            .apply {
//                addParam("bid", mData?.bookId)
//                addParam("cid", mData?.orderPageVo?.chapterId)
//                mData?.chapterInfo?.index?.let { chapterIndex ->
//                    addParam("cid_numb", chapterIndex + 1)
//                }
//
//            }.track()
    }

    private fun setViewData(orderInfo: OrderPageVo) {

        mViewBinding.btnAction.text = orderInfo.buttonTips
        mViewBinding.tvPriceTitle.text = orderInfo.bookAmountTitle ?: "本章价格"
        mViewBinding.tvPriceValue.text = orderInfo.bookAmount.toString()
        mViewBinding.tvPriceUnit.text = orderInfo.bookAmountUnit ?: "看点"
        mViewBinding.tvAmountTitle.text = orderInfo.totalAmountTitle ?: "账户余额"
        mViewBinding.tvAmountValue.text = orderInfo.totalAmount.toString()
        mViewBinding.tvAmountUnit.text = orderInfo.totalAmountUnit ?: "看点"

        mViewBinding.tvTextPreview.containsTitle = true
        mViewBinding.tvTextPreview.text = orderInfo.previewContent

        //设置自动订购是否显示
        mData?.let {
            //余额充足确认订购才显示
            if (it.status == LoadOneChapterBean.STATUS4) {
                setAutoPayView(orderInfo.showAutoPay == 1, orderInfo.selectAutoPay == 1)
            } else {
                setAutoPayView(false, false)
            }
        }


        //设置底部文字链运营位
        mViewBinding.tvTextLink.visibility = GONE
        orderInfo.vipTipVo?.let { baseOperationBean ->
            setBottomOperation(baseOperationBean)
        }
    }

    private fun setBottomOperation(baseOperationBean: BaseOperationBean) {
        mViewBinding.tvTextLink.apply {
            visibility = VISIBLE
            text = baseOperationBean.buttonText
            registerClickAction {
                SchemeRouter.doUriJump(baseOperationBean.action)
            }
        }
    }

    private fun showPayDialog() {
        if (mData?.status == LoadOneChapterBean.STATUS5) {
            if (mData?.orderPageVo?.batchUnlockAct?.isPriorityPop == 1) {
                //弹出批量解锁
                mData?.orderPageVo?.batchUnlockAct?.let { showBatchUnLockDialog(it) }
            } else {
                mData?.let { showOrderDialog(it) }

            }
        }
    }

    private fun setBottomTextLink(batchUnlockAct: BatchUnlockAct) {
        batchUnlockAct.text?.let {
            if (!it.isNullOrBlank()) {
                mViewBinding.tvTextLink.apply {
                    visibility = VISIBLE
                    text = it
                    paint.flags = Paint.UNDERLINE_TEXT_FLAG
                    registerClickAction {
                        showBatchUnLockDialog(batchUnlockAct)
                    }
                }
            }

        }


    }

    /**
     * 弹出批量解锁订购页
     *
     * @param batchUnlockAct
     */
    private fun showBatchUnLockDialog(batchUnlockAct: BatchUnlockAct) {
        mData?.orderPageVo?.batchUnLockDialogShowing = true
//        ReaderMR.get().batchUnlock().apply {
//            activityPageId = getActivityPageId()
//            jsonData = batchUnlockAct.toJson()
//            onDismiss {
//                mData?.orderPageVo?.batchUnLockDialogShowing = false
//            }
//            setRouteCallback(getUiId(), batchUnlockCallback)
//        }.start()
    }

    /**
     *  自动扣费是否显示及是否默认选中配置
     *
     * @param needShowAutoPay
     * @param checked
     */
    private fun setAutoPayView(needShowAutoPay: Boolean, checked: Boolean) {
        mViewBinding.compAutoPayCheck.visibility = if (needShowAutoPay) VISIBLE else GONE
        mViewBinding.compAutoPayCheck.bindData(checked)

        if (needShowAutoPay) {
            mViewBinding.spaceView.height = 12.dp
        } else {
            mViewBinding.spaceView.height = 16.dp
        }
    }

    override fun subscribeEvent(lifecycleOwner: LifecycleOwner, lifecycleTag: String) {
        ReaderInsideEvents.get().onOrderPageChapterOpen()
            .observe(lifecycleOwner, lifecycleTag) { orderPageInfo ->
                orderPageInfo?.run {
                    if ((mData?.orderPageVo == orderPageInfo) && orderPageInfo.needAutoShowPayDialog) {
                        //showPayDialog()
                    }
                }
            }
    }

    private fun resetColorMode() {
        if (ReaderConfigUtil.isNightMode()) {
            setNightMode()
        } else {
            setColorMode()
        }

    }

    private fun setNightMode() {
        val gradientStartColor: Int = R.color.reader_config_color_style_gradient_start_night
        val gradientEndColor: Int = R.color.reader_config_color_style_gradient_end_night
        val solid: Int = R.color.reader_config_color_style_bg_night
        mViewBinding.vTopCover.setShapeBackground(
            gradientOrientation = 1,
            gradientStartColor = getColor(gradientStartColor),
            gradientEndColor = getColor(gradientEndColor)
        )
        mViewBinding.vLeftLine.setShapeBackground(
            gradientOrientation = 0,
            gradientStartColor = getColor(R.color.reader_color_00FFFFFF),
            gradientEndColor = getColor(R.color.reader_color_33FFFFFF)
        )
        mViewBinding.vRightLine.setShapeBackground(
            gradientOrientation = 0,
            gradientStartColor = getColor(R.color.reader_color_33FFFFFF),
            gradientEndColor = getColor(R.color.reader_color_00FFFFFF)
        )
        mViewBinding.clOrder.setBackgroundResource(solid)
        mViewBinding.tvAlertTitle.setTextColor(getColor(R.color.reader_color_3DFFFFFF))
        mViewBinding.tvPriceTitle.setTextColor(getColor(R.color.reader_color_FFAEAEAE))
        mViewBinding.tvPriceValue.setTextColor(getColor(R.color.reader_color_FFB45244))
        mViewBinding.tvPriceUnit.setTextColor(getColor(R.color.reader_color_FFAEAEAE))
        mViewBinding.tvAmountTitle.setTextColor(getColor(R.color.reader_color_FFAEAEAE))
        mViewBinding.tvAmountValue.setTextColor(getColor(R.color.reader_color_FFB45244))
        mViewBinding.tvAmountUnit.setTextColor(getColor(R.color.reader_color_FFAEAEAE))
        mViewBinding.btnAction.setShapeBackground(
            radius = 24.dp2px,
            solidColor = getColor(R.color.reader_FFBA5500),


        )


        mViewBinding.tvTextLink.setShapeBackground(
            radius = 24.dp2px,
            solidColor = getColor(R.color.reader_color_00000000),
            stokeWidth = 1.dp2px,
            stokeColor = getColor(R.color.reader_FFBA5500),

        )
        mViewBinding.tvTextLink.setTextColor(getColor(R.color.reader_FFBA5500))


        mViewBinding.compAutoPayCheck.setNightMode(true)
    }

    private fun setColorMode() {
        val currentColorStyleIndex = ReaderConfigUtil.getCurrentColorStyleIndex()

        val gradientStartColor: Int
        val gradientEndColor: Int
        val solid: Int
        when (currentColorStyleIndex) {
            0 -> {
                gradientStartColor = R.color.reader_config_color_style_gradient_start_0
                gradientEndColor = R.color.reader_config_color_style_bg_0
                solid = R.color.reader_config_color_style_bg_0
            }

            1 -> {
                gradientStartColor = R.color.reader_config_color_style_gradient_start_1
                gradientEndColor = R.color.reader_config_color_style_bg_1
                solid = R.color.reader_config_color_style_bg_1
            }

            2 -> {
                gradientStartColor = R.color.reader_config_color_style_gradient_start_2
                gradientEndColor = R.color.reader_config_color_style_bg_2
                solid = R.color.reader_config_color_style_bg_2
            }

            3 -> {
                gradientStartColor = R.color.reader_config_color_style_gradient_start_3
                gradientEndColor = R.color.reader_config_color_style_bg_3
                solid = R.color.reader_config_color_style_bg_3
            }

            else -> {
                gradientStartColor = R.color.reader_config_color_style_gradient_start_0
                gradientEndColor = R.color.reader_config_color_style_bg_0
                solid = R.color.reader_config_color_style_bg_0
            }
        }
        mViewBinding.vTopCover.setShapeBackground(
            gradientOrientation = 1,
            gradientStartColor = getColor(gradientStartColor),
            gradientEndColor = getColor(gradientEndColor)
        )
        mViewBinding.vLeftLine.setShapeBackground(
            gradientOrientation = 0,
            gradientStartColor = getColor(R.color.reader_color_02000000),
            gradientEndColor = getColor(R.color.reader_color_14000000)
        )
        mViewBinding.vRightLine.setShapeBackground(
            gradientOrientation = 0,
            gradientStartColor = getColor(R.color.reader_color_14000000),
            gradientEndColor = getColor(R.color.reader_color_02000000)
        )
        mViewBinding.clOrder.setBackgroundResource(solid)
        mViewBinding.tvAlertTitle.setTextColor(getColor(R.color.reader_color_3D000000))
        mViewBinding.tvPriceTitle.setTextColor(getColor(R.color.reader_color_FF555555))
        mViewBinding.tvPriceValue.setTextColor(getColor(R.color.reader_color_FFE55749))
        mViewBinding.tvPriceUnit.setTextColor(getColor(R.color.reader_color_FF555555))
        mViewBinding.tvAmountTitle.setTextColor(getColor(R.color.reader_color_FF555555))
        mViewBinding.tvAmountValue.setTextColor(getColor(R.color.reader_color_FFE55749))
        mViewBinding.tvAmountUnit.setTextColor(getColor(R.color.reader_color_FF555555))
        mViewBinding.btnAction.setShapeBackground(
            radius = 24.dp2px,
            solidColor = getColor(R.color.reader_FFFF7500),


        )

        mViewBinding.tvTextLink.setShapeBackground(
            radius = 24.dp2px,
            solidColor = getColor(R.color.reader_color_00000000),
            stokeWidth = 1.dp2px,
            stokeColor = getColor(R.color.reader_FFFF7500),

        )
        mViewBinding.tvTextLink.setTextColor(getColor(R.color.reader_FFFF7500))

        mViewBinding.compAutoPayCheck.setNightMode(false)

    }


    override var mActionListener: ViewActionListener? = null

    interface ViewActionListener : ActionListener {


    }

    override fun bindViewData(fid: String, block: Block) {

        block.tag?.run {
            if (this is EmptyBlockInfo) {
                if (this.blockData is LoadOneChapterBean) {
                    val loadOneChapterBean =
                        this.blockData as LoadOneChapterBean
                    bindData(loadOneChapterBean)
                }

            }
        }
    }

    override fun setFontSize(fontSize: Int) {
        mViewBinding.tvTextPreview.reload()
    }

    override fun setColorStyle(colorStyle: ColorStyle) {
        resetColorMode()
        mViewBinding.tvTextPreview.reload()
    }

    override fun setLayoutStyle(layoutStyle: LayoutStyle) {
        mViewBinding.tvTextPreview.reload()
    }
}