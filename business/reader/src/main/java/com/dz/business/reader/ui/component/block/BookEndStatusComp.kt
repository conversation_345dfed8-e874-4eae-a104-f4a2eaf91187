package com.dz.business.reader.ui.component.block

import android.content.Context
import android.graphics.Point
import android.graphics.Typeface
import android.util.AttributeSet
import android.util.TypedValue
import androidx.lifecycle.LifecycleOwner
import com.dz.business.base.data.bean.BaseOperationBean
import com.dz.business.base.vm.getViewModel
import com.dz.business.reader.data.ReadEndResponse
import com.dz.business.reader.R
import com.dz.business.reader.ReaderInsideEvents
import com.dz.business.reader.databinding.ReaderBookEndStatusCompBinding
import com.dz.business.reader.ui.view.PushMoreAnimView
import com.dz.business.reader.utils.ReaderConfigUtil
import com.dz.business.reader.vm.BookEndStatusCompVM
import com.dz.foundation.base.utils.LogUtil
import com.dz.foundation.base.utils.dp
import com.dz.platform.common.base.ui.component.UIConstraintComponent
import com.dz.platform.common.toast.ToastManager


/**
 * <AUTHOR>
 * @description: 终章推荐顶部状态相关
 * @date :2022/11/16 14:28
 */
class BookEndStatusComp : UIConstraintComponent<ReaderBookEndStatusCompBinding, ReadEndResponse> {
    @JvmOverloads
    constructor(
        context: Context,
        attrs: AttributeSet? = null,
        defStyleAttr: Int = 0
    ) : super(context, attrs, defStyleAttr)

    private var mViewModel: BookEndStatusCompVM? = null
    override fun initAttrs(context: Context?, attrs: AttributeSet?, defStyleAtt: Int) {
        mViewModel = getViewModel(BookEndStatusCompVM::class.java)
    }

    override fun initData() {
    }

    override fun initView() {
    }

    override fun initListener() {
        mViewBinding.tvEndPushMore.registerClickAction {
            if (mData != null) {
                //是否催更过：1-是，0-否
                if (mData!!.isPushMore == 1) {
                    showAlertToast()
                } else {
                    mViewModel?.doPushMoreRequest(mData!!.bookId)
                }
            }

        }
    }

    private fun showAlertToast() {
        ToastManager.showToast(mData!!.pushMoreText ?: "您已催更，我们已经快马加鞭联系作者更新啦~")
    }

    override fun subscribeEvent(lifecycleOwner: LifecycleOwner, lifecycleTag: String) {
        ReaderInsideEvents.get().colorStyleChanged().observe(lifecycleOwner, lifecycleTag) {
            bindData(mData)
        }
    }

    override fun subscribeObserver(lifecycleOwner: LifecycleOwner) {
        mViewModel?.mPushMoreLd?.observe(lifecycleOwner) {
            it?.run {
                //点击催更结果状态，0-失败，1-成功
                if (it.status == 1) {
                    mData?.isPushMore = 1
                    mData?.pushMoreNum = mData?.pushMoreNum?.plus(1)
                    bindData(mData)
                    createAddView()
                    showAlertToast()
                    checkShowMarketingDialog(it.operating)
                }
            }
        }
    }

    private fun checkShowMarketingDialog(operating: BaseOperationBean?) {

    }

    private fun createAddView() {
        val endPosition = IntArray(2)
        mViewBinding.tvEndPushMore.getLocationInWindow(endPosition)
        LogUtil.i("king_location", "getLocationInWindow X ${endPosition[0]} Y ${endPosition[1]}")
        val endPos = Point(
            (mViewBinding.tvEndPushMore.x + mViewBinding.tvEndPushMore.width - 20.dp).toInt(),
            (mViewBinding.tvEndPushMore.y).toInt() - 5.dp
        )
        LogUtil.i(
            "king_location",
            "endPos X ${endPos.x} Y ${endPos.y} height${mViewBinding.tvEndPushMore.height}"
        )
        val itemView = PushMoreAnimView(context)
        itemView.setStartPosition(endPos)
        itemView.text = "+1"
        itemView.setTextSize(TypedValue.COMPLEX_UNIT_PX, 24f.dp)
        itemView.typeface = Typeface.defaultFromStyle(Typeface.BOLD)
        itemView.setTextColor(getColor(getStatusDesColor()))
        addView(itemView)
        itemView.startAnimation()
    }

    override fun bindData(data: ReadEndResponse?) {
        super.bindData(data)
        data?.run {
            //书籍完本状态，0-连载，1-完本

            if (bookStatus == 0) {
                mViewBinding.clStatusSerial.visibility = VISIBLE
                mViewBinding.clStatusFinish.visibility = GONE
                mViewBinding.ivEndStatus.setImageResource(getSerialImageResource())
                mViewBinding.tvEndStatusDes.text = serialText
                if (ReaderConfigUtil.isNightMode()) {
                    mViewBinding.tvEndStatusDes.setTextColor(getColor(R.color.common_99FFFFFF))
                } else {
                    mViewBinding.tvEndStatusDes.setTextColor(getColor(R.color.common_99000000))
                }


                val preText = if (isPushMore == 1) {
                    "已催"
                } else {
                    "催更"
                }
                val pushText = preText + "(${pushMoreNum}人)"
                mViewBinding.tvEndPushMore.text = pushText
//                    SpannableUtils.setBoldFontSize(pushText, preText, 0)
                setPushMoreColor()
            } else {
                mViewBinding.clStatusSerial.visibility = GONE
                mViewBinding.clStatusFinish.visibility = VISIBLE
                mViewBinding.ivEndStatusFinish.setImageResource(getFinishedImageResource())
                if (ReaderConfigUtil.isNightMode()) {
                    mViewBinding.tvEndStatusFinish.setTextColor(getColor(R.color.common_99FFFFFF))
                } else {
                    mViewBinding.tvEndStatusFinish.setTextColor(getColor(R.color.common_99000000))
                }
            }

        }
    }

    private fun getFinishedImageResource(): Int {
        return if (ReaderConfigUtil.isNightMode()) {
            R.drawable.reader_ic_book_end_finish_txt_night
        } else {
            R.drawable.reader_ic_book_end_finish_txt
        }
    }

    private fun getSerialImageResource(): Int {
        return if (ReaderConfigUtil.isNightMode()) {
            R.drawable.reader_ic_book_end_serial_txt_night
        } else {
            R.drawable.reader_ic_book_end_serial_txt
        }
    }

    private fun getStatusDesColor(): Int {
        return if (ReaderConfigUtil.isNightMode()) {
            if (mData?.isPushMore == 1) {
                R.color.common_33FFFFFF
            } else {
                R.color.common_99FFFFFF
            }

        } else {
            if (mData?.isPushMore == 1) {
                R.color.common_33000000
            } else {
                R.color.common_99000000
            }

        }
    }

    private fun getStatusPushMoreBgColor(): Int {
        return if (ReaderConfigUtil.isNightMode()) {
            R.color.reader_FF262626
        } else {
            R.color.reader_61FFFFFF
        }
    }

    private fun getStatusPushMoreStrokeColor(): Int {
        return if (ReaderConfigUtil.isNightMode()) {
            //是否催更过：1-是，0-否
            if (mData?.isPushMore == 1) {
                R.color.reader_0DFFFFFF
            } else {
                R.color.common_1AFFFFFF
            }

        } else {
            if (mData?.isPushMore == 1) {
                R.color.reader_0D000000
            } else {
                R.color.reader_0D000000
            }

        }
    }

    /**
     * 催更
     */
    private fun getStatusPushMoreTextColor(): Int =
        if (ReaderConfigUtil.isNightMode()) {
            R.color.reader_61FFFFFF
        } else {
            R.color.reader_61000000
        }

    /**
     * 已催
     */
    private fun getStatusAlreadyPushMoreTextColo(): Int =
        if (ReaderConfigUtil.isNightMode())
            R.color.reader_color_33FFFFFF
        else
            R.color.reader_color_33000000

    var style_color: Int = R.color.reader_color_666666
    private fun setPushMoreColor() {
        mData?.run {
            style_color = getStatusPushMoreBgColor()
            mViewBinding.tvEndPushMore.setShapeBackground(
                radius = 18f.dp,
                solidColor = getColor(style_color),
                stokeColor = getColor(getStatusPushMoreStrokeColor()),
                stokeWidth = 0.5f.dp
            )
            mViewBinding.tvEndPushMore.setTextColor(
                getColor(
                    if (isPushMore == 1)
                        getStatusAlreadyPushMoreTextColo()
                    else
                        getStatusPushMoreTextColor()
                )
            )
        }
    }
}