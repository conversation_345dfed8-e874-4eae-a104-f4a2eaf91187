package com.dz.business.reader.vm

import android.text.TextUtils
import androidx.lifecycle.viewModelScope
import com.dz.business.reader.DataRepository
import com.dz.business.base.R
import com.dz.business.base.helper.FloatWindowManage.Companion.TTS_TYPE
import com.dz.business.base.livedata.CommLiveData
import com.dz.business.base.reader.ReaderMR
import com.dz.business.base.reader.intent.ReaderCatalogIntent
import com.dz.business.base.shelf.AddBookToShelfCallback
import com.dz.business.base.shelf.ShelfME
import com.dz.business.base.shelf.ShelfMS
import com.dz.business.base.vm.PageVM
import com.dz.business.base.vm.event.RequestEventCallback
import com.dz.business.base.vm.event.VMEventOwner
import com.dz.business.reader.data.CatalogBookInfo
import com.dz.business.reader.data.CatalogInfoBaseBean
import com.dz.business.reader.data.Chapter
import com.dz.business.reader.load.ContentLoader
import com.dz.business.reader.load.LoadCallback
import com.dz.business.reader.load.LoadResult
import com.dz.business.reader.network.LoadOneChapterParamBean
import com.dz.business.reader.network.ReaderNetwork
import com.dz.business.reader.ui.component.CHAPTER_TYPE_LOAD
import com.dz.business.reader.ui.component.CHAPTER_TYPE_READING
import com.dz.business.reader.ui.component.CHAPTER_TYPE_UNLOAD
import com.dz.business.reader.ui.component.ChapterItemBean
import com.dz.business.reader.repository.entity.NovelBookEntity
import com.dz.business.track.trace.OmapNode
import com.dz.foundation.base.module.AppModule
import com.dz.foundation.base.utils.LogUtil
import com.dz.foundation.network.onError
import com.dz.foundation.network.onResponse
import com.dz.foundation.network.onStart
import com.dz.platform.common.toast.ToastManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext


/**
 *@Author: wanxin
 *@Date: 2022/10/19 17:29
 *@Description:
 *@Version: 1.0
 */
class ReaderCatalogVM : PageVM<ReaderCatalogIntent>(), VMEventOwner<CatalogVMEventCallback> {
    //书籍信息
    val bookInfoData = CommLiveData<CatalogBookInfo>()

    //目录信息LiveData
    val catalogLiveData = CommLiveData<MutableList<ChapterItemBean>>()

    //批量下载支持
    val supportBatchLiveData = CommLiveData<BatchBtnBean>()

    //可以批量下载的章节总数
    private var downLoadTotalSize: Int = 0

    //批量下载进度
    private var downLoadSize: Int = 0

    //批量下载成功个数
    var downLoadSuccessSize: Int = 0

    //下载更新
    val updateChapterData = CommLiveData<BatchLoadBean>()


    //排序方式是否是正序
    var sortIsPositiveType = true

    //排序方式是否改变
    var sortTypeIsChanged = false


    //目录信息 正序
    private val catalogListData = mutableListOf<ChapterItemBean>()

    //发起请求的章节索引
    var requestChapterLastIndex = 0

    //更新范围
    var startRefreshIndex = 0
    var endRefreshIndex = 0

    //请求数据中，已有缓存的章节
    private var hasCacheIndexSet = mutableSetOf<String?>()


    var currentPosition: Int? = 0

    //分3次请求全量计数
    private var alreadyRequestCount = 0

    private val downloadHandle by lazy { DownLoadHandle() }

    var needCancelDownLoad = false

//    /**
//     * 初始化目录数据
//     */
//    private fun initCatalogInfo(totalCount: Int) {
//        setDefaultCatalogInfo(totalCount)
//        catalogLiveData.value = catalogListData
//    }


    /**
     * 设置默认章节数据
     */
    private fun setDefaultCatalogInfo(totalCount: Int) {
        catalogListData.clear()
        repeat(totalCount) { index ->
            catalogListData.add(ChapterItemBean(null, null, index, CHAPTER_TYPE_UNLOAD, 0))
        }
    }


    /**
     * 获取真实章节数据
     */
    fun getRealCatalogInfo(requestChapterIndex: Int?, requestChapterId: String?) {
        currentPosition = requestChapterIndex

        var bookId = routeIntent?.bookId ?: ""
        if (NovelBookEntity.isLocalBook(bookId)) {
            //本地书
            getLocalCatalogInfo(bookId, requestChapterIndex, requestChapterId)
        } else {
            //网络书籍
            getRemoteCatalogInfo(bookId, requestChapterIndex, requestChapterId)
        }


    }

    /**
     * 获取网络书籍目录
     */
    private fun getRemoteCatalogInfo(
        bookId: String,
        requestChapterIndex: Int?,
        requestChapterId: String?
    ) {
        //后台根据userChapterId，下发批量下载按钮数据，非详情页进入、页面无数据时传(用户进入目录时候的chapterId)
        val userChapterId =
            if ((TextUtils.equals(
                    routeIntent?.referrer,
                    ReaderMR.READER
                )) && catalogListData.isEmpty()
            ) {
                routeIntent?.chapterId
            } else {
                null
            }
        val request = if (routeIntent?.audioType == TTS_TYPE) {
            ReaderNetwork.get().catalogInfo().setParams(
                bookId,
                requestChapterIndex,
                requestChapterId,
                userChapterId,
                null
            )
        } else {
            ReaderNetwork.get().audioCatalog().setParams(
                bookId,
                requestChapterIndex,
                requestChapterId,
                userChapterId,
                null
            )
        }
        request.onStart {
            eventCallback?.onRequestStart(catalogLiveData.value != null)
        }
            .onResponse {
                it.data?.run {
                    //记录请求成功时候的index
                    requestChapterLastIndex = requestChapterIndex ?: 0
                    //设置书籍信息
                    bookInfo?.let {
                        bookInfoData.value = it
                    }
                    //设置是否支持批量下载
                    if (userChapterId != null) {
                        supportBatchLiveData.value = BatchBtnBean(supportBatch == 1, title)
                    }
                    //设置目录总条数
                    var realSize = bookInfo?.totalChapterNum ?: 0
                    //处理请求的数据最后一条的index,超过总章节的情况
                    if (!chapterList.isNullOrEmpty()) {
                        val currentLastIndex = chapterList.last().index
                        if (currentLastIndex != 0 && currentLastIndex + 1 > realSize) {
                            realSize = currentLastIndex + 1
                        }
                    }

                    if (catalogListData.size != realSize) {
                        setDefaultCatalogInfo(realSize)
                    }
                    //设置目录数据
                    viewModelScope.launch {
                        if (!chapterList.isNullOrEmpty()) {
                            startRefreshIndex = chapterList.first().index
                            endRefreshIndex = chapterList.last().index
                            LogUtil.d(
                                "catalogRequest",
                                "查询缓存范围 $startRefreshIndex - $endRefreshIndex"
                            )
                            queryChapterCache(startRefreshIndex, endRefreshIndex)
                            LogUtil.d(
                                "catalogRequest",
                                "查询缓存完毕"
                            )
                            for (chapter in chapterList) {
                                catalogListData[chapter.index].run {
                                    chapterId = chapter.chapterId
                                    chapterName = chapter.chapterName
                                    chapterIndex = chapter.index
                                    hasLock = chapter.hasLock
                                    //设置章节状态：正在阅读、已下载、未下载
                                    readType = if (chapter.chapterId == routeIntent?.chapterId) {
                                        if (currentPosition == null) {
                                            currentPosition = chapter.index
                                        }
                                        //正在阅读:对比进入页面章节index判断是否正在阅读
//                                        if (routeIntent?.isFromBookDetail == true) {
//                                            //书籍详情页面 设置是否已经下载
//                                            if (hasCacheIndexSet.contains(chapter.chapterId)) CHAPTER_TYPE_LOAD else CHAPTER_TYPE_UNLOAD
//                                        } else {
                                        //阅读器 设置正在阅读
                                        CHAPTER_TYPE_READING
//                                        }
                                    } else {
                                        //已经下载并且解锁状态设为已加载
                                        if (hasLock != 1 && hasCacheIndexSet.contains(chapter.chapterId)) CHAPTER_TYPE_LOAD else CHAPTER_TYPE_UNLOAD
                                    }

                                }
                            }
                        }
                        //根据排序设置目录数据
                        if (sortIsPositiveType) {
                            //正序
                            catalogLiveData.value = catalogListData
                        } else {
                            //倒序
                            val list = mutableListOf<ChapterItemBean>()
                            list.addAll(catalogListData)
                            list.reverse()
                            catalogLiveData.value = list
                        }



                        if (alreadyRequestCount == 0 && catalogListData.size > 50) {
                            requestAllDataPart3(catalogListData.size / 3 + 1)
                        }
                    }


                }
                eventCallback?.onResponse()
            }.onError {
                eventCallback?.onRequestError(it, catalogLiveData.value != null)
            }.doRequest()
    }

    /**
     * 本地书
     */
    private fun getLocalCatalogInfo(
        bookId: String,
        requestChapterIndex: Int?,
        requestChapterId: String?
    ) {

        viewModelScope.launch(Dispatchers.IO) {

            var catalogInfoBaseBean: CatalogInfoBaseBean

            //获取书籍信息
            val bookEntity = DataRepository.bookDao().queryByBid(bookId)
            val chapterList = DataRepository.chapterDao().queryByBid(bookId)
            val catalogBookInfo = CatalogBookInfo(
                bookId = bookId,
                bookName = bookEntity?.book_name,
                totalChapterNum = bookEntity?.total_chapter_num
            )
            val list = mutableListOf<Chapter>()

            chapterList?.forEach { chapterEntity ->
                chapterEntity?.let {
                    val chapter = Chapter(
                        chapterId = it.cid,
                        chapterName = it.chapter_name ?: "",
                        hasLock = 0,
                        index = it.chapter_num ?: 0
                    )
                    list.add(chapter)
                }
            }
            catalogInfoBaseBean = CatalogInfoBaseBean(catalogBookInfo, list, null, null)
            handleLocalCatalogInfo(requestChapterIndex, catalogInfoBaseBean)


        }


    }


    /**
     * 处理本地书目录信息
     */
    private fun handleLocalCatalogInfo(requestChapterIndex: Int?, data: CatalogInfoBaseBean) {
        currentPosition = requestChapterIndex
        data.run {
            //记录请求成功时候的index
            requestChapterLastIndex = requestChapterIndex ?: 0
            //设置书籍信息
            bookInfo?.let {
                bookInfoData.postValue(it)
            }
            //设置目录总条数
            val realSize = bookInfo?.totalChapterNum ?: 0
            if (catalogListData.size != realSize) {
                setDefaultCatalogInfo(realSize)
            }
            //设置目录数据
            if (!chapterList.isNullOrEmpty()) {
                startRefreshIndex = chapterList.first().index
                endRefreshIndex = chapterList.last().index

                for (chapter in chapterList) {
                    catalogListData[chapter.index].run {
                        chapterId = chapter.chapterId
                        chapterName = chapter.chapterName
                        chapterIndex = chapter.index
                        hasLock = chapter.hasLock
                        //设置章节状态：正在阅读、已下载、未下载
                        readType = if (chapter.chapterId == routeIntent?.chapterId) {
                            if (currentPosition == null) {
                                currentPosition = chapter.index
                            }
                            //阅读器 设置正在阅读
                            CHAPTER_TYPE_READING
                        } else {
                            //判断是否已经下载:查库设置
                            CHAPTER_TYPE_LOAD
                        }

                    }
                }
            }
            //根据排序设置目录数据
            if (sortIsPositiveType) {
                //正序
                catalogLiveData.postValue(catalogListData)
            } else {
                //倒序
                val list = mutableListOf<ChapterItemBean>()
                list.addAll(catalogListData)
                list.reverse()
                catalogLiveData.postValue(list)
            }
        }
    }

    private fun requestAllDataPart3(requestPerCount: Int) {
        if (alreadyRequestCount > 2) {
            return
        }
        ReaderNetwork.get()
            .catalogInfo()
            .setParams(
                routeIntent?.bookId ?: "",
                requestPerCount * alreadyRequestCount,
                null,
                null,
                requestPerCount
            )
            .onResponse {
                it.data?.run {
                    //设置目录数据
                    viewModelScope.launch {
                        if (!chapterList.isNullOrEmpty()) {
                            startRefreshIndex = chapterList.first().index
                            endRefreshIndex = chapterList.last().index
                            queryChapterCache(startRefreshIndex, endRefreshIndex)
                            for (chapter in chapterList) {
                                if (chapter.index < catalogListData.size) {
                                    catalogListData[chapter.index].run {
                                        chapterId = chapter.chapterId
                                        chapterName = chapter.chapterName
                                        chapterIndex = chapter.index
                                        hasLock = chapter.hasLock
                                        //设置章节状态：正在阅读、已下载、未下载
                                        readType =
                                            if (chapter.chapterId == routeIntent?.chapterId) {
                                                if (currentPosition == null) {
                                                    currentPosition = chapter.index
                                                }
                                                //正在阅读:对比进入页面章节index判断是否正在阅读
//                                                if (routeIntent?.isFromBookDetail == true) {
//                                                    //书籍详情页面 设置是否已经下载
//                                                    if (hasCacheIndexSet.contains(chapter.chapterId)) CHAPTER_TYPE_LOAD else CHAPTER_TYPE_UNLOAD
//                                                } else {
                                                //阅读器 设置正在阅读
                                                CHAPTER_TYPE_READING
//                                                }
                                            } else {
                                                //已经下载并且解锁状态设为已加载
                                                if (hasLock != 1 && hasCacheIndexSet.contains(
                                                        chapter.chapterId
                                                    )
                                                ) CHAPTER_TYPE_LOAD else CHAPTER_TYPE_UNLOAD
                                            }

                                    }
                                }
                            }
                        }
                        if (alreadyRequestCount < 2) {
                            alreadyRequestCount++
                            requestAllDataPart3(requestPerCount)
                        }
                    }
                }
            }.doRequest()
    }


    /**
     * 更改排序类型
     */
    fun changeSortType() {
        sortIsPositiveType = !sortIsPositiveType
        sortTypeIsChanged = true
    }

    /**
     * 查询章节下载状态
     */
    private suspend fun queryChapterCache(startIndex: Int, endIndex: Int) {
        LogUtil.d("catalogRequest", "开始查找本地缓存")
        val list = withContext(Dispatchers.IO) {
            DataRepository.chapterDao().getChapterAvailableList(
                bid = routeIntent?.bookId ?: "",
                startIndex = startIndex,
                endIndex = endIndex
            )
        }
        if (!list.isNullOrEmpty()) {
            list.forEach {
                if (it != null) {
                    hasCacheIndexSet.add(it.cid)
                }
            }
        }
        LogUtil.d("catalogRequest", "已有缓存章节=${hasCacheIndexSet}")
    }

    /**
     * 获取首章章节Id
     */
    fun getFirstChapterId(): String? {
        if (catalogListData.isEmpty()) {
            return null
        }
        return catalogListData.first().chapterId
    }

    /**
     * 获取可下载章节列表
     */
    fun getLoadCatalogInfo(requestChapterId: String) {
        ReaderNetwork.get()
            .chapterLoadList()
            .setParams(routeIntent?.bookId!!, requestChapterId)
            .onStart {
                eventCallback?.onRequestDownloadStart()
            }
            .onResponse {
                eventCallback?.onResponse()
                it.data?.run {
                    if (status != 1) {
                        //无可下载章节
                        if (!msg.isNullOrEmpty()) {
                            ToastManager.showToast(msg)
                        } else {
                            ToastManager.showToast(
                                AppModule.getApplication()
                                    .getString(com.dz.business.reader.R.string.reader_no_chapter_download)
                            )
                        }
                    } else {
                        //有可下载章节
                        if (chapterIdList.isNullOrEmpty()) {
                            ToastManager.showToast(
                                AppModule.getApplication()
                                    .getString(com.dz.business.reader.R.string.reader_no_chapter_download)
                            )
                        } else {
                            downLoadSize = 0
                            downLoadSuccessSize = 0
                            downLoadTotalSize = chapterIdList.size
                            downloadHandle.downLoadData = chapterIdList
                            viewModelScope.launch {
                                queryChapterCache(startIndex ?: 0, endIndex ?: 0)
                                val alreadyLoadList = mutableListOf<String>()
                                var alreadyLoadSize = 0
                                downloadHandle.downLoadData.forEach { d ->
                                    if (hasCacheIndexSet.contains(d)) {
                                        alreadyLoadSize++
                                        alreadyLoadList.add(d)
                                    }
                                }
                                downloadHandle.downLoadData.removeAll(alreadyLoadList)
                                if (downloadHandle.downLoadData.isEmpty()) {
                                    ToastManager.showToast(
                                        AppModule.getApplication()
                                            .getString(com.dz.business.reader.R.string.reader_already_download)
                                    )
                                } else {
                                    downLoadSize = alreadyLoadSize
                                    LogUtil.d("批量下载", "需要下载章节总数：$downLoadTotalSize")
                                    updateChapterData.value = BatchLoadBean(
                                        downLoadTotalSize,
                                        downLoadSize,
                                        downLoadSuccessSize,
                                        null,
                                        null
                                    )
                                    downloadHandle.downChapterContent(
                                        routeIntent?.bookId!!,
                                        chapterIdList[0]
                                    )
                                }
                            }

                        }
                    }
                }
            }.onError {
                ToastManager.showToast(
                    AppModule.getApplication().getString(R.string.bbase_net_error)
                )
                eventCallback?.onResponse()
            }.doRequest()
    }


    /**
     * 下载批量下载自动加书架
     */

    fun checkAutoAddShelf() {

        if (adding) {
            return
        }
        if (hasAddToShelf) {
            return
        }
        var bookId = bookInfoData.value?.bookId
        if (bookId.isNullOrEmpty()) {
            return
        }
        var chapterId = routeIntent?.chapterId
        val mainScope = MainScope()
        mainScope.launch() {
            val isOnShelf = withContext(Dispatchers.IO) {
                bookIsOnShelf(bookId ?: "")
            }
            LogUtil.d("ReaderCatalog", "checkAutoAddShelf  bookIsOnShelf=$isOnShelf")
            if (isOnShelf) {
                return@launch
            }
            addBookToShelf(bookId, chapterId)
        }


    }

    private suspend fun bookIsOnShelf(mBookId: String): Boolean {
        //书籍是否已在书籍
        val bookEntity = withContext(Dispatchers.IO) {
            DataRepository.bookDao().queryByBid(mBookId)
        }
        return bookEntity?.add_to_shelf == 1

    }

    private fun addBookToShelf(mBookId: String, mChapterId: String?) {
        var bookName = bookInfoData.value?.bookName
        ShelfMS.get()
            ?.addBookToShelf(
                mBookId,
                mChapterId,
                getBookSource(mBookId, bookName ?: ""),
                "批量下载加入",
                addBookToShelfCallback
            )
    }

    private var adding = false
    private var hasAddToShelf = false
    private val addBookToShelfCallback = object : AddBookToShelfCallback() {
        override fun onStart() {
            adding = true
        }

        override fun onSuccess(bookInfo: NovelBookEntity) {
            hasAddToShelf = true
            adding = false
            ShelfME.get().bookAddToShelf().post(bookInfo)
        }

        override fun onFail(errorCode: Int, msg: String) {
            adding = false
        }

    }

    private fun getBookSource(bookId: String, bookName: String): String {
//        return routeIntent?.getSourceNode()?.apply {
//            contentId = bookId
//            contentName = bookName
//
//        }?.toJson() ?: ""
        return ""
    }

    /**
     * 章节内容下载处理
     */
    private inner class DownLoadHandle {
        private val contentLoader: ContentLoader = ContentLoader()

        var downLoadData = mutableListOf<String>()
        private var currentLoadChapterId = ""

        private val loadCallback: LoadCallback = object : LoadCallback {
            override fun onResult(loadResult: LoadResult) {
                viewModelScope.launch(Dispatchers.Main) {
                    handleResult(loadResult)
                }
            }

            override fun onRequestStart() {
                LogUtil.d("批量下载", "$currentLoadChapterId 开始下载")
            }

            override fun onRequestEnd() {

            }

        }

        //加载章节内容
        fun downChapterContent(bookId: String, chapterId: String) {
            currentLoadChapterId = chapterId
            if (needCancelDownLoad) {
                return
            }
            val loadParam = LoadOneChapterParamBean(
                bookId = bookId,
                chapterId = chapterId,
                audioType = routeIntent?.audioType ?: TTS_TYPE
            )
            contentLoader.loadChapterContent(loadParam, loadCallback)
        }


        //成功或失败进度都加1
        fun handleResult(loadResult: LoadResult) {
            //下载完成
            downLoadSize++
            if (loadResult.isSuccess()) {
                downLoadSuccessSize++
            }
            downLoadData.remove(currentLoadChapterId)
            LogUtil.d(
                "批量下载",
                "$currentLoadChapterId 下载完成，已下载个数：$downLoadSize  新下载个数：$downLoadSuccessSize"
            )
            //寻找目录对应章节
            var updateChapterBean: ChapterItemBean? = null
            var updatePosition: Int? = null

            catalogLiveData.value?.let {
                it.forEachIndexed { index, chapterItemBean ->
                    if (chapterItemBean.chapterId == currentLoadChapterId) {
                        updateChapterBean = chapterItemBean
                        updatePosition = index
                        return@let
                    }
                }
            }


            updateChapterData.value = BatchLoadBean(
                downLoadTotalSize,
                downLoadSize,
                downLoadSuccessSize,
                updateChapterBean?.apply {
                    readType = if (chapterIndex == routeIntent?.chapterIndex) {
                        //正在阅读:对比进入页面章节index判断是否正在阅读
//                        if (routeIntent?.isFromBookDetail == true) {
//                            if (loadResult.isSuccess()) CHAPTER_TYPE_LOAD else CHAPTER_TYPE_UNLOAD
//                        } else {
                        CHAPTER_TYPE_READING
//                        }
                    } else {
                        //判断是否已经下载:查库设置
                        if (loadResult.isSuccess()) CHAPTER_TYPE_LOAD else CHAPTER_TYPE_UNLOAD
                    }
                },
                updatePosition
            )

            if (downLoadData.isNotEmpty()) {
                downChapterContent(routeIntent?.bookId!!, downLoadData[0])
            }
        }
    }

    fun getSourceNode(): OmapNode? {
        if (routeIntent?.routeSource.isNullOrEmpty()) {
            return null
        }
        return OmapNode.fromJson(routeIntent?.routeSource)
    }

}

interface CatalogVMEventCallback : RequestEventCallback {
    fun onRequestDownloadStart()
}

data class BatchBtnBean(
    val supportBatch: Boolean,
    val btnText: String?
)

data class BatchLoadBean(
    val totalCount: Int,
    val downLoadSize: Int,
    val downLoadSuccessSize: Int,
    val bean: ChapterItemBean?,
    val updatePosition: Int?
)