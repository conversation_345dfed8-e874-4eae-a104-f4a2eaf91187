package com.dz.business.reader.ui.component.block

import android.content.Context
import android.util.AttributeSet
import com.dz.business.reader.data.EmptyBlockInfo
import com.dz.business.reader.data.LoadOneChapterBean
import com.dz.business.reader.data.ReadEndResponse
import com.dz.business.reader.databinding.ReaderBookEndCompBinding

import com.dz.platform.common.base.ui.component.UIConstraintComponent
import reader.xo.block.Block
import reader.xo.config.ColorStyle
import reader.xo.config.LayoutStyle


/**
 *@Author: shidz
 *@Date: 2022/11/15 12:36
 *@Description:终章推荐 页面
 *@Version:1.0
 */
class BookEndComp @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : UIConstraintComponent<ReaderBookEndCompBinding, ReadEndResponse>(
    context,
    attrs,
    defStyleAttr
), ReaderBlockComp {
    override fun initData() {

    }

    override fun initView() {

    }

    override fun initListener() {

    }

    override fun bindViewData(fid: String, block: Block) {
        block.tag?.run {
            if (this is EmptyBlockInfo) {
                if (blockData is LoadOneChapterBean) {
                    val oneChapterBean = blockData as LoadOneChapterBean
                    bindData(oneChapterBean?.readEndResponse?.apply {
                        bookStatus = oneChapterBean.bookStatus
                        bookId = oneChapterBean.bookId
                        bookName = oneChapterBean.bookName
                    })
                }
            }
        }

    }

    override fun setFontSize(fontSize: Int) {
        mViewBinding.tvTextPreview.reload()
    }

    override fun setColorStyle(colorStyle: ColorStyle) {
        mViewBinding.tvTextPreview.reload()
    }

    override fun setLayoutStyle(layoutStyle: LayoutStyle) {
        mViewBinding.tvTextPreview.reload()
    }

    override fun bindData(data: ReadEndResponse?) {
        super.bindData(data)
        data?.let { setViewData(it) }
    }

    private fun setViewData(readerEndData: ReadEndResponse) {
       var chapterContent = (readerEndData.chapterInfo?.content?:"").trim()
       var chapterNameTem =  (readerEndData.chapterInfo?.chapterName?:"").trim()
        if (chapterContent.startsWith(chapterNameTem)) {
            chapterContent = chapterContent.replaceFirst(chapterNameTem,"")
        }

        if (!chapterContent.startsWith("\n")) {
            //防止第一段被加粗,第一段强制变为换行
            chapterContent="\n"+chapterContent
        }

        mViewBinding.tvTextPreview.text = chapterContent
        mViewBinding.compStatusRoot.bindData(readerEndData)
        if (readerEndData.recommendBookInfo != null) {
            mViewBinding.compRecommendRoot.visibility = VISIBLE
            mViewBinding.compRecommendRoot.bindData(readerEndData)
        } else {
            mViewBinding.compRecommendRoot.visibility = GONE
        }

    }

}