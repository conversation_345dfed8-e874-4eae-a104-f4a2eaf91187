package com.dz.business.reader.data

import com.dz.business.base.data.bean.BaseBean
import com.dz.business.track.events.sensor.AdTE
import com.google.gson.Gson


/**
 *作者: shidz
 *创建时间 :  2023/11/13 15:53
 *功能描述:阅读器内广告配置数据模型
 *
 */
class ReaderAdConfigInfo : BaseBean() {

    fun setAdPosition() {
        bottomAdVo?.adPosition = AdTE.READER_BOTTOM_BANNER
        pageAdConfigVo?.adPosition = AdTE.READER_INSERT_PAGE
    }

    var bottomAdVo: BottomAdConfig? = null //底通广告位广告信息
    var pageAdConfigVo: InsertPageAdConfig? = null //阅读器插页广告信息
    var noticePage: NoticeAdConfig? = null //公告页广告位广告信息

    /**
     *作者: shidz
     *创建时间 :  2023/11/13 17:50
     *功能描述: 插页广告配置
     *
     */
    data class InsertPageAdConfig(
        var endIndex: Int? = Int.MAX_VALUE, //结束广告显示章节序号
        var intervalPage: Int? = 3,//间隔几页显示
        var preloadPage: Int? = 1, //提前几页触发预加载
        var startIndex: Int? = 1,//开始出现广告的章节序号

    ) : BookAdConfigInfo()


    /**
     *作者: shidz
     *创建时间 :  2023/11/14 17:12
     *功能描述:底部banner 广告配置
     *
     */
    data class BottomAdConfig(
        var adfailRetry: Int?,
        var closeAdIntervalNum: Int?,//关闭广告后再次展示的时间间隔，单位秒
        var maxShowNum: Int?,
        var obtainAdfailInterval: Int?,
    ) : BookAdConfigInfo()

    /**
     *作者: shidz
     *创建时间 :  2023/11/14 17:12
     *功能描述:底部banner 广告配置
     *
     */
    data class NoticeAdConfig(
        var noticeDisplayTag: Int? = 0,//底通广告是否在公告页展示 0展示 1不展示
        var noticeDesc: String? = null,//公告展示文案
        var noticeTitle: String? = "内容免费公告",//公告展示文案标题
    ) : BookAdConfigInfo()
}