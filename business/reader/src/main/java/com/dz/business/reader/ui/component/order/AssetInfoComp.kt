package com.dz.business.reader.ui.component.order

import android.content.Context
import android.util.AttributeSet
import android.view.View
import com.dz.business.reader.databinding.ReaderAssetInfoCompBinding
import com.dz.foundation.base.utils.ScreenUtil
import com.dz.platform.common.base.ui.component.UIConstraintComponent

/**
 * @author: king
 * @description：用于账户余额、本章价格展示
 * @date: 2023/8/22
 */
class AssetInfoComp : UIConstraintComponent<ReaderAssetInfoCompBinding, Any> {
    @JvmOverloads
    constructor(
            context: Context,
            attrs: AttributeSet? = null,
            defStyleAttr: Int = 0
    ) : super(context, attrs, defStyleAttr)

    override fun initData() {
    }

    override fun initView() {
    }

    override fun initListener() {
    }

    fun setData(title: String, isShowTip: Boolean, value: String, unit: String) {

        mViewBinding.tvAssetTitle.text = title
        mViewBinding.tvAssetValue.text = value
        mViewBinding.tvAssetUnit.text = unit
        mViewBinding.tvAssetTip.visibility = if (isShowTip) View.VISIBLE else View.GONE
    }
    fun setIsShowTip(isShowTip: Boolean){
        mViewBinding.tvAssetTip.visibility = if (isShowTip) View.VISIBLE else View.GONE
    }
    fun setAssetValue(value: String){
        mViewBinding.tvAssetValue.text = value
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        val mHeight = ScreenUtil.dip2px(context, 31)
        val heightMeasureSpec = MeasureSpec.makeMeasureSpec(mHeight, MeasureSpec.EXACTLY)
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
    }
}