package com.dz.business.reader.ui.component.emptyDataComp

import android.content.Context
import android.util.AttributeSet
import com.dz.business.base.R
import com.dz.business.base.data.bean.DiscussInfoVo
import com.dz.business.base.ui.component.status.Status
import com.dz.business.reader.databinding.ReaderCompEmptyCardItemBinding
import com.dz.foundation.imageloader.load
import com.dz.foundation.ui.view.custom.ActionListener
import com.dz.foundation.ui.view.custom.ActionListenerOwner
import com.dz.platform.common.base.ui.component.UIConstraintComponent

/**
 * 社区信息流 - 空数据组件
 */
class EmptyDataComp : UIConstraintComponent<ReaderCompEmptyCardItemBinding, DiscussInfoVo>,
    ActionListenerOwner<EmptyDataComp.ViewListener> {

    override var mActionListener: ViewListener? = null

    @JvmOverloads
    constructor(
        context: Context,
        attrs: AttributeSet? = null,
        defStyleAttr: Int = 0,
    ) : super(context, attrs, defStyleAttr)

    override fun initData() {
    }

    override fun initView() {
    }

    override fun onExpose(isFirstExpose: Boolean) {
        super.onExpose(isFirstExpose)
    }

    override fun initListener() {
        mViewBinding.btnRefresh.registerClickAction {
            mActionListener?.onRefreshClick()
        }
    }

    fun registerCompListener(listener: ViewListener) {
        mActionListener = listener
    }

    fun updateCompStatus(status: Int?) {
        when (status) {
            Status.EMPTY -> {
                mViewBinding.tvTip.text = "暂无数据"
                mViewBinding.ivCover.load(R.drawable.bbase_ic_no_follow_drama)
            }

            Status.NORMAL -> {
            }

            Status.NET_ERROR -> {
                mViewBinding.tvTip.text = "无网络连接，请检查网络设置"
                mViewBinding.ivCover.load(R.drawable.bbase_ic_net_error)
            }

        }
    }


    override fun onBindRecyclerViewItem(itemData: DiscussInfoVo?, position: Int) {
        super.onBindRecyclerViewItem(itemData, position)
    }


    interface ViewListener : ActionListener {
        fun onRefreshClick()
    }


}