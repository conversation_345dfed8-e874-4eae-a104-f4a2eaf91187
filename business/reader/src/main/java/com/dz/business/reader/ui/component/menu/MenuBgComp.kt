package com.dz.business.reader.ui.component.menu

import android.content.Context
import android.util.AttributeSet
import androidx.lifecycle.LifecycleOwner
import com.dz.business.reader.R
import com.dz.business.reader.ReaderInsideEvents
import com.dz.business.reader.databinding.ReaderMenuBgCompBinding
import com.dz.business.reader.utils.ReaderConfigUtil
import com.dz.foundation.ui.view.custom.ActionListener
import com.dz.foundation.ui.view.custom.ActionListenerOwner
import com.dz.foundation.ui.view.recycler.DzRecyclerViewCell
import com.dz.platform.common.base.ui.component.UIConstraintComponent

/**
 *@Author: shidz
 *@Date: 2022/9/29 15:26
 *@Description: 阅读器主菜单背景设置组件
 *@Version:1.0
 */
class MenuBgComp @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : UIConstraintComponent<ReaderMenuBgCompBinding, Any>(context, attrs, defStyleAttr),
    ActionListenerOwner<MenuBgComp.MenuBgCompActionListener> {

    override fun initData() {

    }

    override fun initView() {
        setMode()
        mViewBinding.drv.addCells(createCells())
    }

    private fun createCells(): MutableList<DzRecyclerViewCell<BgItemBean>> {
        val cells = mutableListOf<DzRecyclerViewCell<BgItemBean>>()
        val colorStyles = ReaderConfigUtil.getColorStyles()

        repeat(colorStyles.size) {
            val bgItemBean = BgItemBean()
            bgItemBean.bgColor = colorStyles[it].bgColor
            if (ReaderConfigUtil.isNightMode()) {
                bgItemBean.checked = it == 4
            } else {
                bgItemBean.checked = it == ReaderConfigUtil.getCurrentColorStyleIndex()
            }
            bgItemBean.index = it
            cells.add(createCellItem(bgItemBean))
        }
        return cells
    }

    private fun createCellItem(data: BgItemBean): DzRecyclerViewCell<BgItemBean> {
        val itemCell = DzRecyclerViewCell<BgItemBean>()
        itemCell.viewClass = MenuBgItemComp::class.java
        itemCell.viewData = data
        itemCell.setActionListener(getListener())
        itemCell.spanSize = 1
        return itemCell
    }

    override fun initListener() {

    }

    private var mListener: MenuBgItemComp.ViewActionListener? = null
    private fun getListener(): MenuBgItemComp.ViewActionListener {
        if (mListener == null) {
            mListener = object : MenuBgItemComp.ViewActionListener {
                override fun onItemChecked(data: BgItemBean) {
                    unCheckedLastItem()
                    when(data.index) {
                        4 -> mActionListener?.switchNightMode()
                        else -> mActionListener?.onCheckColorStyle(data.index)
                    }
                }
            }
        }
        return mListener!!
    }

    private fun unCheckedLastItem() {
        for (itemCell in mViewBinding.drv.allCells) {
            val bgItemBean = itemCell.viewData as BgItemBean
            if (bgItemBean.checked) {
                bgItemBean.checked = false
                mViewBinding.drv.updateCell(itemCell, bgItemBean)
                break
            }
        }
    }

    override var mActionListener: MenuBgCompActionListener? = null

    interface MenuBgCompActionListener : ActionListener {
        fun onCheckColorStyle(colorStyleIndex: Int)
        fun switchNightMode()
    }

    override fun subscribeEvent(lifecycleOwner: LifecycleOwner, lifecycleTag: String) {
        ReaderInsideEvents.get().colorStyleChanged().observe(lifecycleOwner, lifecycleTag) {
            setMode()
        }
    }

    private fun setMode() {
        if (ReaderConfigUtil.isNightMode()) setNightMode() else setDayMode()
    }

    private fun setDayMode() {
        mViewBinding.tvBgTitle.setTextColor(getColor(R.color.reader_title_text_color_day))
    }

    private fun setNightMode() {
        mViewBinding.tvBgTitle.setTextColor(getColor(R.color.reader_title_text_color_night))
    }
}