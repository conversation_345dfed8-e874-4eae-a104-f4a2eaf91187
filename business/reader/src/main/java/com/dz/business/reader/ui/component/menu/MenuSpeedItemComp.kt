package com.dz.business.reader.ui.component.menu

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import com.dz.business.reader.R
import com.dz.business.reader.databinding.ReaderSpeedItemCompBinding
import com.dz.business.reader.utils.ReaderConfigUtil
import com.dz.foundation.ui.view.custom.ActionListener
import com.dz.foundation.ui.view.custom.ActionListenerOwner
import com.dz.platform.common.base.ui.component.UIConstraintComponent

/**
 *@Author: wanxin
 *@Date: 2023/8/21 17:52
 *@Description:
 *@Version: 1.0
 */
class MenuSpeedItemComp @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : UIConstraintComponent<ReaderSpeedItemCompBinding, AudioBookSpeedBean>(
    context,
    attrs,
    defStyleAttr
), ActionListenerOwner<MenuSpeedItemComp.SpeedChangeListener> {

    override fun initData() {
    }

    override fun initView() {
        mViewBinding.run {
            tvName.setTextColor(
                getColor(
                    if (ReaderConfigUtil.isNightMode())
                        R.color.reader_DBFFFFFF
                    else
                        R.color.reader_E6000000
                )
            )
        }
    }

    override fun initListener() {
        registerClickAction {
            if (mData?.check == true) {
                return@registerClickAction
            }
            mActionListener?.onChange(mData?.speed)
        }
    }

    @SuppressLint("SetTextI18n")
    override fun bindData(data: AudioBookSpeedBean?) {
        super.bindData(data)
        data?.run {
            mViewBinding.run {
                tvName.text = context.getString(R.string.reader_speed, speed.toString())
                ivCheck.setImageResource(
                    if (check) {
                        if (ReaderConfigUtil.isNightMode())
                            R.drawable.reader_ic_night_check
                        else
                            R.drawable.reader_ic_check
                    } else {
                        if (ReaderConfigUtil.isNightMode())
                            R.drawable.reader_ic_night_check_normal
                        else
                            R.drawable.reader_ic_check_normal
                    }
                )
            }
        }
    }

    override var mActionListener: SpeedChangeListener? = null

    interface SpeedChangeListener : ActionListener {
        fun onChange(checkSpeed: Float?)
    }
}

data class AudioBookSpeedBean(
    val index: Int,
    val speed: Float
) {
    var check: Boolean = false
}
