package com.dz.business.reader.vm


import android.graphics.Bitmap
import android.text.TextUtils
import androidx.lifecycle.viewModelScope
import com.dz.business.base.data.bean.BaseOperationBean
import com.dz.business.base.helper.FloatWindowManage.Companion.AUDIO_TYPE
import com.dz.business.base.helper.FloatWindowManage.Companion.TTS_TYPE
import com.dz.business.base.livedata.CommLiveData
import com.dz.business.base.main.MainMS
import com.dz.business.base.reader.ReaderME
import com.dz.business.base.reader.ReaderMR
import com.dz.business.base.reader.intent.ReaderIntent
import com.dz.business.base.shelf.ShelfME
import com.dz.business.base.vm.PageVM
import com.dz.business.reader.DataRepository
import com.dz.business.reader.R
import com.dz.business.reader.audio.TtsPlayer
import com.dz.business.reader.data.BookEndFid
import com.dz.business.reader.data.BookOpenBean
import com.dz.business.reader.data.EmptyBlockInfo
import com.dz.business.reader.data.LoadOneChapterBean
import com.dz.business.reader.data.NovelBookInfo
import com.dz.business.reader.data.ReadEndResponse
import com.dz.business.reader.data.ReaderKV
import com.dz.business.reader.data.TtsConfigDataVo
import com.dz.business.reader.load.ContentLoader
import com.dz.business.reader.load.LoadCallback
import com.dz.business.reader.load.LoadResult
import com.dz.business.reader.network.LoadOneChapterParamBean
import com.dz.business.reader.network.ReaderNetwork
import com.dz.business.reader.repository.entity.AudioBookEntity
import com.dz.business.reader.repository.entity.AudioChapterEntity
import com.dz.business.reader.repository.entity.NovelBookEntity
import com.dz.business.reader.repository.entity.NovelChapterEntity
import com.dz.business.reader.ui.page.AudioOpener
import com.dz.business.reader.utils.ReaderAdUtil
import com.dz.business.track.events.DzTrackEvents
import com.dz.business.track.events.sensor.PositionActionTE
import com.dz.business.track.trace.OmapNode
import com.dz.foundation.base.manager.task.TaskManager
import com.dz.foundation.base.module.AppModule
import com.dz.foundation.base.utils.LogUtil
import com.dz.foundation.base.utils.SystemTimeUtils
import com.dz.foundation.network.onResponse
import com.dz.platform.common.toast.ToastManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import reader.xo.base.DocInfo
import reader.xo.base.XoFile


class ReaderVM : PageVM<ReaderIntent>() {
    private val contentLoader: ContentLoader = ContentLoader()
    val chapterContentLd = CommLiveData<LoadResult>()
    val novelBookEntityLd = CommLiveData<NovelBookEntity>()
    val audioBookEntityLd = CommLiveData<AudioBookEntity>()
    val ttsEnable = CommLiveData<Boolean>()  // 当前章节是否支持听书
    private var currentFid = ""//阅读器当前内容的fid
    private var currentCid: String? = null
    private var currentBid: String? = null
    var currentCoverBitmap: Bitmap? = null
    private var exitReaderOperating: BaseOperationBean? = null
    private var bookOpenInfo: BookOpenBean? = null
    val bookOpenBeanLd = CommLiveData<BookOpenBean>()

    //切章时支持返回原阅读位置
    var isCheckPreOrNextChapter: Boolean = false
    val showRevokeTip = CommLiveData<Boolean>()
    var preChapterId: String? = null
    var preChapterIndex: Int? = 0


    /**
     * 章末推荐：是否拦截
     * ps:做这个变量是针对短篇进行处理的，短篇不需要添加章末推荐，走自己的底部View处理，
     */
    var genInterceptGenBookEndXoFile = false
        get() = contentLoader.mInterceptGenBookEndXoFile
        set(value) {
            contentLoader.mInterceptGenBookEndXoFile = value
            field = value
        }


    /**
     * 章末推荐：是否触发
     * ps:做这个变量是针对短篇进行处理的，章末推荐添加之前this=true，
     * 因章末推荐是xoFile，重新load章节时内核会清除所有的xoFile。所以在此时也需要将this=false
     */
    var genBookEndXoFileState: Boolean = false
        get() = contentLoader.mGenBookEndXoFile
        set(value) {
            contentLoader.mGenBookEndXoFile = value
            field = value
        }


    private val loadCallback: LoadCallback = object : LoadCallback {
        override fun onResult(loadResult: LoadResult) {
            LogUtil.d("XXX", "loadCallback onResult, loadResult=${loadResult.getResultCode()}")
            viewModelScope.launch(Dispatchers.Main) {
                chapterContentLd.value = loadResult

                //tts详情页同步更新章节信息
                ReaderME.get().onChapterLoadFinish().post(loadResult)

                //撤销切章
                if (isCheckPreOrNextChapter) {
                    if (AudioOpener.instance.audioType != TTS_TYPE) { //不是tts模式，
                        showRevokeTip.value = true
                    } else if (!AudioOpener.instance.listeningBook(getBookId())) { //是tts模式，但是当前书没在听
                        showRevokeTip.value = true
                    }
                }
            }
        }

        override fun onRequestStart() {
            statusPoster.statusLoading().post()
        }

        override fun onRequestEnd() {
            statusPoster.statusDismiss().post()
        }


    }

    /**
     * 重置章节内容 并定位到当前章节
     *
     * @param chapterId
     */
    fun resetChapterContent(chapterId: String) {
        routeIntent?.chapterId = chapterId
        initChapterContent()
    }

    fun initChapterContent(isFirstOpenChapter: Boolean = false) {
        routeIntent?.let { intent ->
            viewModelScope.launch {
                var bookSource = getBookSource()
                val intentBookId = intent.bookId
                val intentChapterId = intent.chapterId
                var lastReadChapterId: String? = null
                var lastReadCurPos: Int? = null
                var intentCurPos = intent.currentPos
                LogUtil.d(
                    "打印",
                    "插入小说数据库：insertOrUpdateBook: ${intent.bookId} audioType =${intent.audioType}"
                )
                if (intent.audioType == TTS_TYPE) {
                    val bookEntity = withContext(Dispatchers.IO) {
                        DataRepository.bookDao().queryByBid(intentBookId)
                    }
                    bookEntity?.run {
                        LogUtil.d("XXX", "bookEntity cur_cid=${cur_cid},cur_pos=${cur_pos}")
                        novelBookEntityLd.value = this
                        lastReadChapterId = cur_cid
                        lastReadCurPos = cur_pos

                        var openTimes = this.open_times ?: 1
                        bookSource = OmapNode.handleSource(bookEntity.source, bookSource) ?: ""
                        DataRepository.bookDao()
                            .insertOrUpdateBooks(NovelBookEntity(intentBookId).apply {
                                if (!bookSource.isNullOrEmpty()) {
                                    source = bookSource
                                }
                                if (isFirstOpenChapter && (lastReadCurPos ?: -1 >= 0)) {
                                    open_times = openTimes + 1
                                }
                            })
//                    contentLoader.setCurrentBook(bookEntity)
                    }
                } else {
                    val bookEntity = withContext(Dispatchers.IO) {
                        DataRepository.audioDao().queryByBid(intentBookId)
                    }
                    bookEntity?.run {
                        audioBookEntityLd.value = this
                        LogUtil.d(
                            "XXX",
                            "刷新数据库书架 bookEntity cur_cid=${cur_cid},cur_pos=${cur_pos} add_to_shelf=${audioBookEntityLd.value?.add_to_shelf}"
                        )
                        lastReadChapterId = cur_cid
                        lastReadCurPos = cur_pos

                        var openTimes = this.open_times ?: 1
                        bookSource = OmapNode.handleSource(bookEntity.source, bookSource) ?: ""
                        DataRepository.audioDao()
                            .insertOrUpdateBooks(AudioBookEntity(intentBookId).apply {
                                if (!bookSource.isNullOrEmpty()) {
                                    source = bookSource
                                }
                                if (isFirstOpenChapter && (lastReadCurPos ?: -1 >= 0)) {
                                    open_times = openTimes + 1
                                }
                            })
//                    contentLoader.setCurrentBook(bookEntity)
                    }
                }

                contentLoader.setBookSource(bookSource)

                var loadChapterId: String? = null
                var curPos: Int? = null
                if (!TextUtils.isEmpty(intentChapterId)) {
                    loadChapterId = intentChapterId
                    curPos = intentCurPos
                    if (curPos == null && TextUtils.equals(intentChapterId, lastReadChapterId)) {
                        curPos = lastReadCurPos
                    }
                } else if (!lastReadChapterId.isNullOrEmpty()) {
                    loadChapterId = lastReadChapterId
                    curPos = lastReadCurPos
                }


                val wrapperCallback = object : LoadCallback {
                    override fun onResult(loadResult: LoadResult) {
                        loadResult.getResult()?.getXoFile()?.let { xoFile ->
                            currentBid = getBookId()

                            if (TextUtils.equals(xoFile.fid, loadChapterId) && isFirstOpenChapter) {
                                xoFile.pos = curPos
                            }
                        }
                        loadCallback.onResult(loadResult)
                    }

                    override fun onRequestStart() {
                        loadCallback.onRequestStart()
                    }

                    override fun onRequestEnd() {
                        loadCallback.onRequestEnd()
                    }

                }
                val loadOneChapterParamBean =
                    LoadOneChapterParamBean(
                        bookId = intentBookId,
                        chapterId = loadChapterId,
                        audioType = routeIntent?.audioType ?: TTS_TYPE,
                    )

                LogUtil.d("打印", "initChapterContent vm 1503")
                loadChapterContent(loadOneChapterParamBean, wrapperCallback)
            }
        }
    }

    private fun loadChapterContent(
        loadParam: LoadOneChapterParamBean,
        loadCallback: LoadCallback,
    ) {
        LogUtil.d("打印", "loadChapterContent callback 1503")
        contentLoader.loadChapterContent(loadParam, loadCallback)
    }

    fun decryptFile(xoFile: XoFile): String {
        return contentLoader.decryptFile(xoFile)
    }

    /**
     * fromUser 是否用户主动触发
     */
    fun getNextDoc(bookId: String, chapterId: String, fromUser: Boolean): XoFile? {
        val runBlocking = runBlocking {
            return@runBlocking withContext(Dispatchers.IO) {
                contentLoader.getNextDoc(
                    bookId,
                    chapterId,
                    fromUser,
                    type = routeIntent?.audioType ?: TTS_TYPE
                )
            }
        }
        return runBlocking
    }


    fun getPreDoc(bookId: String, chapterId: String, fromUser: Boolean): XoFile? {

        val runBlocking = runBlocking {
            return@runBlocking withContext(Dispatchers.IO) {
                contentLoader.getPreDoc(
                    bookId,
                    chapterId,
                    fromUser,
                    type = routeIntent?.audioType ?: TTS_TYPE,
                )
            }
        }

        return runBlocking
    }

    fun getChapterDoc(bookId: String, chapterId: String): XoFile? {
        val runBlocking = runBlocking {
            return@runBlocking withContext(Dispatchers.IO) {
                contentLoader.getChapterDoc(bookId, chapterId)
            }
        }

        return runBlocking
    }


    fun getBookId(): String {
        return routeIntent?.bookId ?: ""
    }

    fun getCurrentChapterId(): String? {
        if (currentCid != null) {
            return currentCid
        }
        return routeIntent?.chapterId
    }


    fun refreshBookInfo() {
        viewModelScope.launch {
            if (routeIntent?.audioType == TTS_TYPE) {
                val bookInfo = withContext(Dispatchers.IO) {
                    DataRepository.bookDao().queryByBid(getBookId())
                }
                bookInfo?.let {
                    withContext(Dispatchers.Main) {
                        LogUtil.d(
                            "AudioActivity",
                            "refreshBookInfo bookInfo curid=${bookInfo.cur_cid} cur_pos=${bookInfo.cur_pos} cur_index=${bookInfo.cur_index}"
                        )
                        novelBookEntityLd.value = it
                    }
                }
            } else {
                val bookInfo = withContext(Dispatchers.IO) {
                    DataRepository.audioDao().queryByBid(getBookId())
                }
                bookInfo?.let {
                    withContext(Dispatchers.Main) {
                        LogUtil.d(
                            "AudioActivity",
                            "refreshBookInfo bookInfo curid=${bookInfo.cur_cid} cur_pos=${bookInfo.cur_pos} cur_index=${bookInfo.cur_index}  ChapterInfo=${AudioOpener.instance.getCurrentAudioChapterInfo()?.chapter_name}"
                        )
                        audioBookEntityLd.value = it
                        AudioOpener.instance.refreshChapterOpen()
                    }
                }
            }
        }
    }


    fun loadChapter(
        chapterId: String?,
        refreshRequest: Boolean? = null,
        autoShowPayDialog: Boolean? = null
    ) {
        val bookId = getBookId()
        val loadOneChapterParamBean = LoadOneChapterParamBean(
            bookId = bookId,
            chapterId = chapterId,
            audioType = routeIntent?.audioType ?: TTS_TYPE,
            needAutoShowPayDialog = autoShowPayDialog ?: false,
            refreshOrderInfoRequest = refreshRequest ?: false
        )
        loadChapterContent(loadOneChapterParamBean, loadCallback)
    }

    fun loadPreChapter(onNoMorePage: Boolean = false, isPreViewPayPage: Boolean = false) {
        viewModelScope.launch {
            LogUtil.d("XXX", "loadPreChapter isBookEndFid=${BookEndFid.isBookEndFid(currentFid)} ")
            withContext(Dispatchers.IO) {
                val bookId = getBookId()
                val chapterId = getCurrentChapterId()
                if (BookEndFid.isBookEndFid(currentFid)) {
                    //终章特殊处理
                    val bookEndFid = BookEndFid.parseJson(currentFid)
                    loadChapter(bookEndFid.preChapterId)
                    return@withContext
                }
                if (!chapterId.isNullOrBlank()) {

                    val wrapperCallback = object : LoadCallback {

                        override fun onResult(loadResult: LoadResult) {
                            loadResult.getResult()?.getXoFile()?.let { xoFile ->
                                if (onNoMorePage && isPreViewPayPage && !TtsPlayer.instance.isRunning()) {
                                    //订购页 触发 onNoMorePage 加载上一章定位到章末,非听书模式
                                    xoFile.pos = Int.MAX_VALUE
                                }
                            }
                            loadCallback.onResult(loadResult)
                        }

                        override fun onRequestStart() {
                            loadCallback.onRequestStart()
                        }

                        override fun onRequestEnd() {
                            loadCallback.onRequestEnd()
                        }

                    }
                    contentLoader.loadPreChapter(
                        onNoMorePage,
                        bookId,
                        chapterId,
                        wrapperCallback,
                        type = routeIntent?.audioType ?: TTS_TYPE
                    )
                } else {
                    val result =
                        LoadResult(LoadResult.CODE_EXCEPTION).apply { setMsg("找不到当前章节id") }
                    loadCallback.onResult(result)
                }
            }
        }
    }

    /**
     * 触发请求下一页 等待结果返回，需显示loading
     *
     */
    fun loadNextChapter() {
        viewModelScope.launch {
            LogUtil.d(
                "XXX",
                "loadNextChapter isBookEndFid=${BookEndFid.isBookEndFid(currentFid)} ChapterInfo完结=${AudioOpener.instance.getCurrentAudioChapterInfo()?.status}  bookInfo完结=${AudioOpener.instance.getCurrentAudioBookInfo()?.status}"
            )
            if (BookEndFid.isBookEndFid(currentFid)) {
                var str = AppModule.getResources().getString(R.string.reader_is_last_chapter)
                if (routeIntent?.audioType == AUDIO_TYPE) {
                    var final =
                        if (AudioOpener.instance.getCurrentAudioChapterInfo()?.status == 1) {
                            true
                        } else if (AudioOpener.instance.getCurrentAudioBookInfo()?.status == 1) {
                            true
                        } else {
                            false
                        }
                    if (final) {
                        str = "当前已是最终章节"
                    } else {
                        str = "当前已是最新章节"
                    }
                    AudioOpener.instance.onAudioPlayEnd()

                }
                LogUtil.d("打印", "提示最终章 1111")
                TaskManager.delayTask(200) {
                    ToastManager.showToast(str)
                }
                return@launch
            }
            withContext(Dispatchers.IO) {
                val bookId = getBookId()
                val chapterId = getCurrentChapterId()
                if (chapterId != null) {
                    contentLoader.loadNextChapter(
                        bookId,
                        chapterId,
                        loadCallback,
                        type = routeIntent?.audioType ?: TTS_TYPE
                    )
                }
            }
        }

    }


    private fun setCurrentFid(fid: String) {
        currentFid = fid
        if (isBookEndBlock()) {
            return
        }
        currentCid = fid
    }

    /**
     * 获取当前章节
     *
     * @return
     */
    fun getCurrentChapterInfo(block: (chapterInfo: NovelChapterEntity?) -> Unit) {
        viewModelScope.launch {
            val chapterInfo = withContext(Dispatchers.IO) {
                queryChapterEntity()
            }
            block(chapterInfo)
        }
    }

    /**
     * 获取当前章节
     *
     * @return
     */
    fun getCurrentAudioChapterInfo(block: (chapterInfo: AudioChapterEntity?) -> Unit) {
        viewModelScope.launch {
            val chapterInfo = withContext(Dispatchers.IO) {
                queryAudioChapterEntity()
            }
            block(chapterInfo)
        }
    }

    suspend fun queryChapterEntity(): NovelChapterEntity? {
        return getCurrentChapterId()?.let {
            DataRepository.chapterDao().queryByCid(getBookId(), it)
        }
    }

    suspend fun queryAudioChapterEntity(): AudioChapterEntity? {
        return getCurrentChapterId()?.let {
            DataRepository.audioChapterDao().queryByCid(getBookId(), it)
        }
    }


    fun getEmptyXoFile(
        fid: String,
        blockType: Int,
        loadBean: LoadOneChapterBean
    ): XoFile {
        return contentLoader.genEmptyXoFile(fid, blockType, loadBean)

    }

    fun genBookEndXoFile(loadBean: LoadOneChapterBean): XoFile? {
        return contentLoader.genBookEndXoFile(loadBean)
    }

    /**
     * 确认订购
     *
     * @param bookId
     * @param chapterId
     */
    fun confirmPay(bookId: String, chapterId: String, autoPay: Boolean? = null) {
        val loadOneChapterParamBean = LoadOneChapterParamBean(
            bookId = bookId,
            chapterId = chapterId,
            autoPay = autoPay,
            confirmPay = true,
            audioType = routeIntent?.audioType ?: TTS_TYPE
        )
        loadChapterContent(loadOneChapterParamBean, loadCallback)
    }

    var hasInitBookOpenConfig = false
    fun initBookOpenConfig() {
        LogUtil.e("打印", "initBookOpenConfig: 1502")
        val request = if (routeIntent?.audioType == AUDIO_TYPE) {
            ReaderNetwork.get().getAudioInfo().setParams(
                getBookId(),
                getBookSource(),
                false,
            )
        } else {
            ReaderNetwork.get().bookOpen().setParams(
                getBookId(),
                getBookSource(),
                false
            )
        }
        request.onResponse {
            it.data?.run {
                ReaderAdUtil.getInstance().setAdConfig(novelAdVo)
                hasInitBookOpenConfig = true
                saveBookOpenConfig(this)
                exitReaderOperating = operating
                exitReaderOperating?.let { exitReaderMarketing ->
                    preloadExitReaderOperationDialog(
                        exitReaderMarketing
                    )
                }
            }
        }.doRequest()
    }


    private fun preloadExitReaderOperationDialog(exitReaderOperating: BaseOperationBean) {
        // getActivity()?.let { MarketingDialogManager.preloadMarketing(it, exitReaderOperating) }
    }

    /**
     * 运营弹窗
     */
    fun showExitReaderOperationDialog(closeAction: () -> Unit): Boolean {
        return false
    }

    fun isShowTts(): Boolean {
        return bookOpenInfo?.ttsConfigData?.showListen == 1
    }

    private fun saveBookOpenConfig(bookOpenBean: BookOpenBean) {
        bookOpenInfo = bookOpenBean
//
//        // 解析音色
//        voiceListConf = bookOpenBean.voiceListConf
//        voiceListConf?.voiceInfoList?.forEachIndexed { index, voiceInfo ->
//            voiceInfo?.let {
//                it.index = index
//                it.selected = it.speechId == ReaderKV.ttsVoiceId
//            }
//        }
//
//        // 更新听书的可用状态
////        ttsEnable.value = BBaseKV.isYoungMode == 0 && bookOpenBean.ttsEnable == 1
//        ttsEnable.value = BBaseKV.isYoungMode == 0 && bookOpenBean.ttsEnable == 1 &&
//                voiceListConf != null && voiceListConf!!.voiceInfoList != null &&
//                voiceListConf!!.voiceInfoList!!.isNotEmpty() && currentCid != null && currentBid != null

        bookOpenBean.preloadNum?.let {
            ReaderKV.preloadNum = it
        }
        ReaderKV.preloadPrevChapter = (bookOpenBean.preloadPrevChapter == 1)

        bookOpenBean.addBookshelfChapterNum?.let {
            ReaderKV.autoAddShelfChapterNum = it
        }

        bookOpenBean.addBookshelfTime?.let {
            ReaderKV.autoAddShelfTime = it
        }


        //更新书籍信息
        bookOpenBean.bookInfo?.run {
            add_to_shelf = bookOpenBean.onTheShelf
            source?.let { bookSource ->
//                serverBookSource = bookSource
                contentLoader.setBookSource(bookSource)
            }

            insertOrUpdateBook(this)
        }
        //tts配置
        if (routeIntent?.audioType == AUDIO_TYPE) {
            bookOpenBean.ttsConfigData = TtsConfigDataVo(
                speedList = bookOpenBean.speedList,
                timeList = bookOpenBean.timeList,
                showListen = 1,
                ttsEnable = 1,
            )
        }
        LogUtil.d(
            "XXX",
            "audioType=${routeIntent?.audioType},ttsConfigData=${bookOpenBean.ttsConfigData}"
        )
        ttsEnable.value = bookOpenBean.ttsConfigData?.ttsEnable == 1
        TtsPlayer.instance.saveTtsConfig(bookOpenBean.ttsConfigData)
        bookOpenBeanLd.value = bookOpenBean
    }

    fun resetData() {
    }

    /**
     * 把书籍添加进书架
     *
     * @param bookInfo
     */
    private fun insertOrUpdateBook(bookInfo: NovelBookInfo) {
        routeIntent?.let {
            viewModelScope.launch {
                withContext(Dispatchers.IO) {
                    if (it.audioType == TTS_TYPE) {
                        var bookEntity = DataRepository.bookDao().queryByBid(bookInfo.bookId ?: "")
                        val oldSource = bookEntity?.source
                        if (bookEntity == null) {
                            bookEntity = NovelBookEntity(bookInfo.bookId ?: "")
                        }

                        bookEntity.apply {
                            book_name = bookInfo.bookName
                            coverurl = bookInfo.coverWap
                            introduction = bookInfo.introduction
                            unit = bookInfo.unit
                            source = OmapNode.handleSource(oldSource, getBookSource())
                            marketing_ext = bookInfo.mark
                            total_chapter_num = bookInfo.totalChapterNum
                            status = bookInfo.status
                            role_name = bookInfo.roleName
                            bookInfo.add_to_shelf?.let { needAddToShelf ->
                                add_to_shelf = needAddToShelf
                            }
                        }
                        LogUtil.d(
                            "打印",
                            "插入小说数据库：insertOrUpdateBook:tts${bookEntity.book_name}"
                        )
                        DataRepository.bookDao().insertOrUpdateBooks(bookEntity)
                        bookEntity?.run {
                            novelBookEntityLd.postValue(this)
                        }
                    } else {
                        var bookEntity = DataRepository.audioDao().queryByBid(bookInfo.bookId ?: "")
                        val oldSource = bookEntity?.source
                        if (bookEntity == null) {
                            bookEntity = AudioBookEntity(bookInfo.bookId ?: "")
                        }

                        bookEntity.apply {
                            book_name = bookInfo.bookName
                            coverurl = bookInfo.coverWap
                            introduction = bookInfo.introduction
                            unit = bookInfo.unit
                            source = OmapNode.handleSource(oldSource, getBookSource())
                            marketing_ext = bookInfo.mark
                            total_chapter_num = bookInfo.totalChapterNum
                            status = bookInfo.status
                            role_name = bookInfo.roleName
                            bookInfo.add_to_shelf?.let { needAddToShelf ->
                                add_to_shelf = needAddToShelf
                            }
                        }
                        bookEntity?.run {
                            audioBookEntityLd.postValue(this)
                        }
                        LogUtil.d(
                            "打印",
                            "刷新数据库书架：insertOrUpdateBook:Audio ${bookEntity.book_name}  ${bookEntity.add_to_shelf}"
                        )
                        DataRepository.audioDao().insertOrUpdateBooks(bookEntity)
                    }
                }
            }
        }
    }

    /**
     * 章节打开
     *
     * @param xoFile
     */
    fun onChapterOpen(xoFile: XoFile) {
        setCurrentFid(xoFile.fid)
    }


    fun getBookEndBlockInfo(xoFile: XoFile): EmptyBlockInfo? {
        return xoFile.tag?.run {
            if ((this is EmptyBlockInfo) && (blockType == EmptyBlockInfo.BLOCK_BOOK_END)) this else null
        }
        null

    }

    /**
     *
     *预览订购支付页面
     * @param xoFile
     * @return
     */
    fun getPreviewPayBlockInfo(xoFile: XoFile): EmptyBlockInfo? {
        return xoFile.tag?.run {
            if ((this is EmptyBlockInfo) && (blockType == EmptyBlockInfo.BLOCK_PREVIEW_PAY)) this else null
        }
        null

    }

    /**
     * 是否是预览订购页
     */
    fun isPreViewPayBlock(xoFile: XoFile): Boolean {
        return getPreviewPayBlockInfo(xoFile) != null
    }

    /**
     *
     * 是否需要阻断用户翻页操作，解决快速翻页导致的连续跳两章订购页的问题
     * @param xoFile
     * @return
     */
    fun blockTurnPage(xoFile: XoFile): Boolean {
        val previewPayBlockInfo = getPreviewPayBlockInfo(xoFile)
        return previewPayBlockInfo?.let { emptyBlockInfo ->
            (emptyBlockInfo.blockData is LoadOneChapterBean)
                    && (emptyBlockInfo.blockData as LoadOneChapterBean).orderPageVo?.blockTurnPage() == true
        } == true

    }

    fun isBookEndBlock(): Boolean {
        return BookEndFid.isBookEndFid(currentFid)
    }

    fun isEmptyBlock(xoFile: XoFile): Boolean {
        return xoFile.tag is EmptyBlockInfo
    }

    fun loadBookEndNextChapter(bookEndEmptyBlockInfo: EmptyBlockInfo) {
        bookEndEmptyBlockInfo.blockData?.let { loadOneChapterBean ->
            if (loadOneChapterBean is LoadOneChapterBean) {
                loadOneChapterBean?.readEndResponse?.let {
                    loadBookEndNextChapter(it)
                }
            }
        }
    }


    fun loadBookEndNextChapter(readEndResponse: ReadEndResponse) {
        val nextBookId = readEndResponse.recommendBookInfo?.bookId
        val nextChapterId = readEndResponse.chapterInfo?.chapterId
        doLog(readEndResponse)
        ReaderMR.get().reader().apply {

            bookId = nextBookId ?: ""
            chapterId = nextChapterId
            this.routeSource = routeIntent?.routeSource
        }.start()
    }

    private fun doLog(readEndResponse: ReadEndResponse?) {
        //神策打点
        doTrackClick(readEndResponse)
        //HIVE打点
        doHiveClick(readEndResponse)
        //点击上报后台
        MainMS.get()?.activityReportEvent(readEndResponse?.operateId ?: "", "", 0)
    }

    /**
     * Hive
     */
    private fun doHiveClick(readEndResponse: ReadEndResponse?) {
//        readEndResponse?.recommendBookInfo?.run {
//            DzTrackEvents.get()
//                .hiveExposure()
//                .click(SourceNode().apply {
//                    origin = SourceNode.origin_ydq
//                    channelId = SourceNode.MODULE_YDQ_ZZTJ
//                    channelName = "终章推荐"
//                    columnId = readEndResponse!!.bookId ?: ""
//                    columnName = readEndResponse!!.bookName ?: ""
//                    contentId = bookId ?: ""
//                    contentName = bookName ?: ""
//                    contentType = ReaderMR.READER//条目跳转类型 路由 action
//                    logId = bigDataDotInfoVo?.logId ?: ""//请求ID
//                    strategyId = bigDataDotInfoVo?.strategyId ?: ""//大数据分组ID
//                    strategyName = bigDataDotInfoVo?.strategyName ?: ""//大数据分组名称
//                })
//                .track()
//        }
    }

    private fun doTrackClick(readEndResponse: ReadEndResponse?) {
        readEndResponse?.recommendBookInfo?.run {
            DzTrackEvents.get()
                .positionAction()
                .action(PositionActionTE.ACTION_CLICK)
                .activityId("")
                .bookId(getBookId())
                .bookName(novelBookEntityLd.value?.book_name)
                .userTacticInfo(readEndResponse.userTacticInfo)
                .title(bookName)
                .contentId(readEndResponse?.recommendBookInfo?.bookId)
                .contentName(readEndResponse?.recommendBookInfo?.bookName)
                .contentType(ReaderMR.READER)
                .track()
        }

    }

    fun loadBookEndPreChapter(bookEndEmptyBlockInfo: EmptyBlockInfo) {
        bookEndEmptyBlockInfo.preChapterId?.let { loadChapter(it) }
    }


    /**
     * 更新小说阅读进度
     */
    fun updateTtsReaderProgress(docInfo: DocInfo) {
        viewModelScope.launch(Dispatchers.IO) {
            val chapterId = docInfo.fid
            if (BookEndFid.isBookEndFid(chapterId)) {
                return@launch
            }

            val localBook = DataRepository.bookDao().queryByBid(getBookId())
            val lastCid = localBook?.last_cid
            val bookEntity = NovelBookEntity(getBookId())
            val isLastPage = docInfo.pageIndex == (docInfo.pageCount - 1)

            bookEntity.cur_cid = chapterId
            bookEntity.cur_pos = docInfo.charIndex
            bookEntity.read_to_end =
                if (TextUtils.equals(chapterId, lastCid) && isLastPage) 1 else 0
            bookEntity.utime = SystemTimeUtils.currentTimeMills()

            LogUtil.d("XXX","updateTtsReaderProgress,cur_pos=${bookEntity.cur_pos},cur_cid=${bookEntity.cur_cid} ")

            DataRepository.bookDao().insertOrUpdateBooks(bookEntity)
        }
    }




    /**
     * 更新保存阅读进度
     *
     */
    fun updateAudioBookReaderProgress(chapterId: String, pos: Int, isLastPage: Boolean) {
        LogUtil.d("XXX", "章节进度发生变化，此时preChapterId=${preChapterId}")
        viewModelScope.launch(Dispatchers.IO) {
            val lastCid = DataRepository.audioDao().queryByBid(getBookId())?.last_cid
            val bookEntity = AudioBookEntity(getBookId())
            bookEntity.cur_cid = chapterId
            bookEntity.cur_pos = pos
            bookEntity.read_to_end =
                if (TextUtils.equals(chapterId, lastCid) && isLastPage) 1 else 0
            bookEntity.utime = SystemTimeUtils.currentTimeMills()
            val currentChapterEntity = queryAudioChapterEntity()
            currentChapterEntity?.run {
                bookEntity.cur_index = chapter_num ?: 0
            }
            LogUtil.d(
                "打印",
                "插入小说数据库：updateReaderProgress: ${bookEntity.book_name} routeIntent?.audioType=${routeIntent?.audioType}"
            )
            DataRepository.audioDao().insertOrUpdateBooks(bookEntity)
        }

    }


    /**
     * 更新书籍的读完状态
     *
     */
    fun updateReadEndStatus() {
        viewModelScope.launch(Dispatchers.IO) {
            val novelBookEntity = NovelBookEntity(getBookId())
            novelBookEntity.read_to_end = 1
            LogUtil.d("打印", "插入小说数据库：updateReadEndStatus: ${novelBookEntity.book_name}")
            DataRepository.bookDao().insertOrUpdateBooks(novelBookEntity)
        }
    }

    private fun getCurrentChapterIndex(): Int {
        var index = 0
        getChapterEntity(getBookId(), getCurrentChapterId())?.run {
            index = chapter_num ?: 0
        }
        return index
    }

    fun getChapterEntity(bookId: String, chapterId: String?): NovelChapterEntity? {
        if (chapterId.isNullOrEmpty()) {
            return null
        }
        return runBlocking {
            withContext(Dispatchers.IO) {
                DataRepository.chapterDao().queryByCid(bookId, chapterId)
            }
        }
    }

    fun chapterContentAvailable(): Boolean {
        return getChapterEntity(getBookId(), currentCid)?.contentAvailable() ?: false
    }

    fun updateBookShelfIndex() {
        LogUtil.d("updateBookShelfIndex", "updateBookShelfIndex")
        viewModelScope.launch {
            val firstShelBookEntity = withContext(Dispatchers.IO) {
                DataRepository.bookDao().queryShelfFirstBook()
            }
            firstShelBookEntity?.let { firstShelBook ->
                if (TextUtils.equals(firstShelBook.bid, getBookId())) {
                    LogUtil.d("updateBookShelfIndex", "is firstBook return")
                    notifyShelfCheckPositionChange(firstShelBookEntity)
                    return@launch
                }
            }

            val curBookEntity = withContext(Dispatchers.IO) {
                DataRepository.bookDao().queryByBid(getBookId())
            }
            curBookEntity?.let { curBook ->
                if (curBook.add_to_shelf == 1) {
                    if (firstShelBookEntity != null) {
                        withContext(Dispatchers.IO) {
                            val updateBook = NovelBookEntity(curBook.bid)
                            firstShelBookEntity.shelf_index?.let { firstShelfIndex ->
                                updateBook.shelf_index = firstShelfIndex - 1
                            }

                            updateBook.utime = SystemTimeUtils.currentTimeMills()
                            updateBook.need_upload_record = 1
                            LogUtil.d(
                                "打印",
                                "插入小说数据库：updateBookShelfIndex: ${updateBook.book_name}"
                            )
                            DataRepository.bookDao().insertOrUpdateBooks(updateBook)
                            LogUtil.d(
                                "updateBookShelfIndex",
                                "update firstBook=" + firstShelBookEntity.book_name + " first index=" + firstShelBookEntity.shelf_index
                            )
                        }
                    }
                    notifyShelfCheckPositionChange(curBookEntity)
                }
            }


        }

    }

    fun updateAudioShelfIndex() {
    }

    /**
     *
     *通知书架书籍位置变化
     */
    private fun notifyShelfCheckPositionChange(novelBookEntity: NovelBookEntity) {

        LogUtil.d(
            "updateBookShelfIndex",
            "notifyShelfCheckPositionChange  cur_index=${novelBookEntity.cur_index} cur_cid= ${novelBookEntity.cur_cid}"
        )
        ReaderME.get().checkShelfBookPositionChange().post(novelBookEntity)
    }

    /**
     *
     *通知书架书籍位置变化
     */
    private fun notifyAudioShelfCheckPositionChange(novelBookEntity: AudioBookEntity) {

        LogUtil.d(
            "updateBookShelfIndex",
            "notifyShelfCheckPositionChange  cur_index=${novelBookEntity.cur_index} cur_cid= ${novelBookEntity.cur_cid}"
        )
//        ReaderME.get().checkShelfBookPositionChange()
//            .post(novelBookEntity)
    }

    fun getBookSource(): String {
        return routeIntent?.getBookRouteSource() ?: ""
    }


    /**
     * 一级阅读来源
     */
    fun getFirstReadingSource(): String? {
        return getSourceNode()?.reader_first_reading_source
    }

    /**
     * 二级阅读来源
     */
    fun getSecondReadingSource(): String? {
        return getSourceNode()?.reader_second_reading_source
    }


    /**
     * 三级阅读来源
     */
    fun getThirdReadingSource(): String? {
        return getSourceNode()?.reader_third_reading_source
    }



    fun getAudioColumnName(): String? {
        return getSourceNode()?.audio_column_name
    }

    fun getReaderColumnId(): Long? {
        return getSourceNode()?.reader_column_id
    }

    fun getReaderColumnPos(): String? {
        return getSourceNode()?.reader_column_pos
    }


    fun getSourceNode(): OmapNode? {
        if (routeIntent?.routeSource.isNullOrEmpty()) {
            return null
        }
        return OmapNode.fromJson(routeIntent?.routeSource)
    }


    /**
     * 书籍是否已加入书架
     *
     * @return
     */
    fun isBookAddToShelf(): Boolean {
        var isAddToShelf = false
        runBlocking {
            val curBookEntity = withContext(Dispatchers.IO) {
                DataRepository.bookDao().queryByBid(getBookId())
            }
            if (curBookEntity != null) {
                if (curBookEntity.add_to_shelf == 1) {
                    isAddToShelf = true
                }
            }
        }
        return isAddToShelf
    }

    /**
     * 桌面组件启动跳转阅读器上报完成任务
     */
    fun welfareWidgetReport(actionType: Int) {
//        ReaderNetwork.get()
//            .widgetStartReport()
//            .paramActionType(actionType)
//            .onResponse {
//                if (it?.data?.code == 200) {
//                    ReaderKV.isReportWidgetStart = true
//                    val welfare_21_widget_start = 21
//                    WelfareME.get().resumeRefresh().post(welfare_21_widget_start)
//                }
//            }.doRequest()
    }

    /**
     * 刷新是否在书架状态 ，用户发生变更时调用
     */
    fun refreshAddShelfStatus() {
        ReaderNetwork.get()
            .getAddShelfStatus()
            .paramBookId(getBookId())
            .onResponse {
                it.data?.let { addShelfStatus ->
                    LogUtil.d("ReaderVM", "refreshAddShelfStatus")
                    TaskManager.ioTask {
                        var bookInfo = novelBookEntityLd.value
                        bookInfo?.let { bookEntity ->
                            bookEntity.add_to_shelf = addShelfStatus.onTheShelf
                            LogUtil.d(
                                "打印",
                                "插入小说数据库：refreshAddShelfStatus: ${bookEntity.book_name}"
                            )
                            DataRepository.bookDao().insertOrUpdateBooks(bookEntity)
                            //通知书籍被加入了书架
                            ShelfME.get().bookAddToShelf().post(bookEntity)
                            LogUtil.d(
                                "ReaderVM",
                                "refreshAddShelfStatus bookAddToShelf bookId=" + bookEntity.book_name
                            )
                        }

                    }
                }

            }.doRequest()
    }


    /**
     * 重新触发章节加载
     */
    fun reloadChapterContent(
        refreshRequest: Boolean? = null,
        needAutoShowPayDialog: Boolean? = null
    ) {
        getCurrentChapterId()
            ?.let { curChapterId ->
                loadChapter(curChapterId, refreshRequest, needAutoShowPayDialog)
            }
    }


    fun getCurrentXoFile(): XoFile? {
        chapterContentLd.value?.let {
            return it.getResult()?.getXoFile()
        }
        return null
    }
}
