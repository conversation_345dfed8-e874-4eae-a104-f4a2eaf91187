package com.dz.business.reader.ui.component.block

import android.content.Context
import android.util.AttributeSet
import androidx.lifecycle.LifecycleOwner
import com.dz.business.reader.R
import com.dz.business.reader.ReaderInsideEvents
import com.dz.business.reader.databinding.ReaderCompProgressRevokeBinding
import com.dz.business.reader.utils.ReaderConfigUtil
import com.dz.foundation.base.manager.task.Task
import com.dz.foundation.base.manager.task.TaskManager
import com.dz.foundation.base.utils.dp
import com.dz.foundation.ui.view.custom.ActionListener
import com.dz.foundation.ui.view.custom.ActionListenerOwner
import com.dz.platform.common.base.ui.component.UIConstraintComponent

/**
 * 阅读模式下切章时，撤销浮窗
 */
class ReaderProgressRevokeComp @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : UIConstraintComponent<ReaderCompProgressRevokeBinding, Any>(
    context, attrs, defStyleAttr
), ActionListenerOwner<ReaderProgressRevokeComp.ViewActionListener> {
    interface ViewActionListener : ActionListener {
        fun onRevokeClick()

        fun onDismiss()
    }

    companion object {
        const val DELAY_TIME = 5000L
    }

    override var mActionListener: ReaderProgressRevokeComp.ViewActionListener? = null

    // 保存延迟任务的引用
    private var hideTask: Task? = null

    override fun initData() {

    }

    override fun initView() {
        updateTheme()
    }

    override fun initListener() {
        mViewBinding.viewOperation.registerClickAction {
            mActionListener?.onRevokeClick()
        }
    }

    fun updateTipData(chapterName: String, progress: String) {
        mViewBinding.tvTitle.text = chapterName
        mViewBinding.tvProgress.text = progress

        // 取消之前的延迟任务
        hideTask?.cancel()

        // 创建新的延迟任务
        hideTask = TaskManager.delayTask(DELAY_TIME) {
            mActionListener?.onDismiss()
        }
    }

    override fun subscribeEvent(lifecycleOwner: LifecycleOwner, lifecycleTag: String) {
        super.subscribeEvent(lifecycleOwner, lifecycleTag)
        ReaderInsideEvents.get().colorStyleChanged().observe(lifecycleOwner, lifecycleTag) {
            updateTheme()
        }
    }

    private fun updateTheme() {
        val bgColor = when (ReaderConfigUtil.getCurrentColorStyleIndex()) {
            0 -> R.color.reader_revoke_bg_theme_yellow
            1 -> R.color.reader_revoke_bg_theme_white
            2 -> R.color.reader_revoke_bg_theme_brown
            3 -> R.color.reader_revoke_bg_theme_blue
            else -> R.color.reader_revoke_bg_theme_dark
        }
        mViewBinding.rootLayout.setShapeBackground(
            solidColor = getColor(bgColor),
            radius = 8f.dp
        )
    }
}