package com.dz.business.reader.ui.component.block

import android.content.Context
import android.util.AttributeSet
import androidx.core.content.ContextCompat
import androidx.lifecycle.LifecycleOwner
import com.dz.business.reader.R
import com.dz.business.reader.ReaderInsideEvents
import com.dz.business.reader.databinding.ReaderTtsBackToCurrentBinding
import com.dz.business.reader.utils.ReaderConfigUtil
import com.dz.foundation.base.utils.ScreenUtil
import com.dz.platform.common.base.ui.component.UIConstraintComponent

/**
 *@Author: zhanggy
 *@Date: 2023-01-12
 *@Description: 听书回到当前播放位置的组件
 *@Version:1.0
 */
class ReaderTtsBackToCurrentComp @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : UIConstraintComponent<ReaderTtsBackToCurrentBinding, Any>(
    context,
    attrs,
    defStyleAttr
){
    override fun initData() {

    }

    override fun initView() {
        updateTheme()
    }

    override fun initListener() {
    }

    override fun subscribeEvent(lifecycleOwner: LifecycleOwner, lifecycleTag: String) {
        super.subscribeEvent(lifecycleOwner, lifecycleTag)
        ReaderInsideEvents.get().colorStyleChanged().observe(lifecycleOwner, lifecycleTag) {
            updateTheme()
        }
    }

    private fun updateTheme() {
        mViewBinding.layoutBackToCurrent.setShapeBackground(
            radius = ScreenUtil.dip2px(context, 21F),
            solidColor =
            if (ReaderConfigUtil.isNightMode())
                getColor(R.color.reader_tts_back_to_current_bkg_night)
            else
                getColor(R.color.reader_tts_back_to_current_bkg_day),
        )
        mViewBinding.tvContent.setTextColor(
            ContextCompat.getColor(
                context,
                R.color.common_FFFFFFFF
            )
        )
        mViewBinding.ivArrow.setImageResource(R.drawable.reader_tts_back_to_current_arrow)
    }
}