package com.dz.business.reader.ui.component.block

import android.content.Context
import android.util.AttributeSet
import com.dz.business.base.databinding.BbaseTagFlowLayoutCompBinding
import com.dz.platform.common.base.ui.component.UIConstraintComponent

/**
 *@Author: xuanpeng
 *@Date:  2022/11/24
 *@Description:通用的标签组件
 *@Version:1.0
 */
class ChapterEndBookTagFlowComp @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null
) : UIConstraintComponent<BbaseTagFlowLayoutCompBinding, List<String>>(context, attrs) {
    override fun initData() {
    }

    override fun initView() {
    }

    override fun initListener() {
    }

    override fun bindData(data: List<String>?) {
        super.bindData(data)
        if (!data.isNullOrEmpty()) {
            mViewBinding.flowLayout.removeAllViews()
            for (tag in data) {
                mViewBinding.flowLayout.addView(ChapterEndBookTagComp(context).apply {
                    bindData(tag)
                })
            }

            visibility = VISIBLE
        } else {
            visibility = GONE
        }
    }
}