package com.dz.business.reader.ui.component.menu

import android.content.Context
import android.util.AttributeSet
import androidx.lifecycle.LifecycleOwner
import com.dz.business.reader.R
import com.dz.business.reader.ReaderInsideEvents
import com.dz.business.reader.databinding.ReaderMenuTurnPageItemCompBinding
import com.dz.business.reader.utils.ReaderConfigUtil
import com.dz.foundation.base.utils.ScreenUtil
import com.dz.foundation.ui.view.custom.ActionListener
import com.dz.foundation.ui.view.custom.ActionListenerOwner
import com.dz.platform.common.base.ui.component.UIFrameComponent
import reader.xo.config.AnimType

/**
 *@Author: shidz
 *@Date: 2022/10/9 23:22
 *@Description: 菜单翻页选项组件
 *@Version:1.0
 */
class MenuTurePageItemComp @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : UIFrameComponent<ReaderMenuTurnPageItemCompBinding, TurnPageItemBean>(
    context,
    attrs,
    defStyleAttr
),
    ActionListenerOwner<MenuTurePageItemComp.ViewActionListener> {

    private var isChecked = false

    override var mActionListener: ViewActionListener? = null

    override fun initData() {

    }

    override fun initView() {
        setMode()
    }

    override fun initListener() {
        registerClickAction {
            mData?.let {
                if (it.checked) {
                    return@registerClickAction
                }
                mActionListener?.onItemChecked(it)
                it.checked = true
                setCheckedStatus(it)
            }
        }
    }

    override fun bindData(data: TurnPageItemBean?) {
        super.bindData(data)
        data?.let {
            setCheckedStatus(it)
            mViewBinding.tvName.text = it.name
        }
    }

    private fun setCheckedStatus(data: TurnPageItemBean) {
        isChecked = data.checked
        setMode()
    }

    interface ViewActionListener : ActionListener {
        fun onItemChecked(data: TurnPageItemBean)
    }

    override fun subscribeEvent(lifecycleOwner: LifecycleOwner, lifecycleTag: String) {
        ReaderInsideEvents.get().colorStyleChanged().observe(lifecycleOwner, lifecycleTag) {
            setMode()
        }
    }

    private fun setMode() {
        if (ReaderConfigUtil.isNightMode())
            setNightMode()
        else
            setDayMode()
    }

    private fun setDayMode() {
        mViewBinding.run {
            tvName.setTextColor(getColor(R.color.reader_title_text_color_day))
            tvName.setBackgroundColor(getColor(R.color.reader_menu_item_bkg_day))
            if (isChecked) {
                tvName.setShapeBackground(
                    radius = ScreenUtil.dip2px(context, 18.5F),
                    stokeColor = getColor(R.color.reader_menu_item_stroke_selected),
                    solidColor = getColor(R.color.reader_menu_item_bkg_day),
                    stokeWidth = ScreenUtil.dip2px(context, 1.0F)
                )
            } else {
                tvName.setShapeBackground(
                    radius = ScreenUtil.dip2px(context, 18.5F),
                    solidColor = getColor(R.color.reader_menu_item_bkg_day),
                    stokeWidth = ScreenUtil.dip2px(context, 0F)
                )
            }
        }
    }

    private fun setNightMode() {
        mViewBinding.run {
            tvName.setTextColor(getColor(R.color.reader_title_text_color_night))
            tvName.setBackgroundColor(getColor(R.color.reader_menu_item_bkg_night))
            if (isChecked) {
                tvName.setShapeBackground(
                    radius = ScreenUtil.dip2px(context, 18.5F),
                    stokeColor = getColor(R.color.reader_menu_item_stroke_selected),
                    solidColor = getColor(R.color.reader_menu_item_bkg_night),
                    stokeWidth = ScreenUtil.dip2px(context, 1.0F)
                )
            } else {
                tvName.setShapeBackground(
                    radius = ScreenUtil.dip2px(context, 18.5F),
                    solidColor = getColor(R.color.reader_menu_item_bkg_night),
                    stokeWidth = ScreenUtil.dip2px(context, 0F)
                )
            }
        }
    }
}

/**
 * 菜单背景数据bean
 */
class TurnPageItemBean {
    var animType: AnimType = AnimType.COVER
    var name = ""
    var checked = false
}