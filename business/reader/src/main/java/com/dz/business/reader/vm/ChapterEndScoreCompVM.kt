package com.dz.business.reader.vm

import com.dz.business.base.livedata.CommLiveData
import com.dz.business.base.vm.ComponentVM
import com.dz.business.reader.data.ScoreBean
import com.dz.business.reader.network.ReaderNetwork
import com.dz.foundation.network.*
import com.dz.platform.common.toast.ToastManager

/**
 * <AUTHOR>
 * @description:
 * @date :2022/11/16 18:48
 */
class ChapterEndScoreCompVM : ComponentVM() {
    var mScoreLd = CommLiveData<ScoreBean?>()

    fun doScoreRequest(bookId: String?, score: Int?) {
        if (bookId == null || score == null) {
            return
        }
        ReaderNetwork.get()
            .scoreRequest()
            .setTag(uiId)
            .setParams(bookId, score)
            .onStart {
            }
            .onResponse {
                if (it.isSuccess() && it.data != null) {
                    mScoreLd.value = it.data
                }
            }
            .onError {
                mScoreLd.value = null
                ToastManager.showToast(it.message)
            }
            .onEnd {
            }
            .doRequest()
    }
}