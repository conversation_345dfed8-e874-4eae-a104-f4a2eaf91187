package com.dz.business.community.ui.page

import android.util.DisplayMetrics
import android.view.View
import androidx.core.content.ContextCompat
import androidx.lifecycle.LifecycleOwner
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.LinearSmoothScroller
import com.dz.business.base.community.data.TopicInfoVo
import com.dz.business.base.community.data.TopicTypeVo
import com.dz.business.base.data.BBaseKV
import com.dz.business.base.data.bean.ListResponseBean
import com.dz.business.base.ui.BaseActivity
import com.dz.business.base.ui.component.status.Status
import com.dz.business.base.ui.component.status.StatusComponent
import com.dz.business.base.utils.CommInfoUtil
import com.dz.business.community.R
import com.dz.business.community.databinding.CommunityTrendTopicBinding
import com.dz.business.community.interfaces.TrendTopicActivityContract
import com.dz.business.community.ui.adapter.SpaceItemDecoration
import com.dz.business.community.ui.adapter.TopicTagAdapter
import com.dz.business.community.ui.component.TrendTopicItemComp
import com.dz.business.community.vm.TrendTopicActivityVM
import com.dz.business.track.events.DzTrackEvents
import com.dz.business.track.trace.OmapNode
import com.dz.business.track.trace.QmapNode
import com.dz.business.track.trace.SourceNode
import com.dz.foundation.base.utils.dp
import com.dz.foundation.ui.view.recycler.DzRecyclerViewCell
import com.dz.platform.common.toast.ToastManager

/**
 * @Description:热门话题
 * @Version:1.0
 */
class TrendTopicActivity : BaseActivity<CommunityTrendTopicBinding, TrendTopicActivityVM>() {
    private var contract: TrendTopicActivityContract? = null
    private var adapter: TopicTagAdapter? = null
    private val limitTabSize = 9 // 最多显示多少个tab
    private var firstLoad = true // 是否第一次加载

    // 初始化状态页面
    override fun initStatusComponent(): StatusComponent {
        return StatusComponent.create(this)
            .bellow(mViewBinding.tvTitle)//设置位置是在某个view 下方，不设置默认覆盖整个页面
    }

    override fun initData() {
        // 发布按钮一期隐藏 翔宇确认的
        mViewBinding.clPublish.visibility = View.GONE
    }

    override fun initView() {
        contract = mViewModel
        mViewBinding.rvTabs.itemAnimator = null
        mViewBinding.rvTabs.animation = null
        mViewBinding.rvComponents.itemAnimator = null
        mViewBinding.refreshLayout.whenDataNotFullShowFooter = true
    }

    override fun onResume() {
        super.onResume()
        if (!firstLoad) {
            appViewTrack()
        }
        firstLoad = false
    }

    override fun initListener() {
        mViewBinding.refreshLayout.setDzRefreshListener {
            contract?.refresh(all = true)
            adapter?.changeTab(null)
            smoothScrollToCenter(0)
            changeRightView()
        }
        mViewBinding.refreshLayout.setDzLoadMoreListener {
            contract?.loadMore()
        }
        mViewBinding.clRight.registerClickAction {
            contract?.clickDialog {
                smoothScrollToIndex(it)
            }
        }
    }

    override fun subscribeObserver(lifecycleOwner: LifecycleOwner) {
        super.subscribeObserver(lifecycleOwner)
        contract?.response?.observe(lifecycleOwner) { r ->
            r?.let {
                val hasMore = it.hasMore
                when (it.state) {
                    // 刷新成功
                    ListResponseBean.STATE_REFRESH_SUCCESS -> {
                        appViewTrack()
                        mViewBinding.rvComponents.smoothScrollToPosition(0)
                        mViewBinding.rvComponents.removeAllCells()
                        if (it.dataList.isNullOrEmpty()) {
                            mViewBinding.statusCom.bindData(Status().setStatus(Status.EMPTY)
                                .setActionText("刷新")
                                .setActionBg(
                                    ContextCompat.getColor(
                                        this@TrendTopicActivity,
                                        R.color.common_FFF55041
                                    )
                                )
                                .setActionTextColor(
                                    ContextCompat.getColor(
                                        this@TrendTopicActivity,
                                        R.color.common_FFF55041
                                    )
                                )
                                .setActionBgResource(R.drawable.common_refresh_btn_bg)
                                .setDes("暂无话题")
                                .setDesColor(
                                    ContextCompat.getColor(
                                        this@TrendTopicActivity,
                                        R.color.common_FFB6BABE
                                    )
                                )
                                .setException(it.error)
                                .setActionListener {
                                    contract?.refresh()
                                })
                        } else {
                            mViewBinding.statusCom.bindData(Status().setStatus(Status.DISMISS))
                            setDataCells(it.dataList)
                            mViewBinding.refreshLayout.finishDzRefresh(hasMore)
                        }
                    }

                    // 加载更多成功
                    ListResponseBean.STATE_LOAD_MORE_SUCCESS -> {
                        setDataCells(it.dataList)
                        mViewBinding.refreshLayout.finishDzLoadMoreSuccess(
                            hasMore, "没有更多了", hideNoMore = false
                        )
                    }

                    // 刷新失败
                    ListResponseBean.STATE_REFRESH_FAILED -> {
                        mViewBinding.rvComponents.smoothScrollToPosition(0)
                        mViewBinding.rvComponents.removeAllCells()
                        mViewBinding.statusCom.bindData(
                            Status().setStatus(Status.NET_ERROR)
                                .setActionText("刷新")
                                .setActionBg(
                                    ContextCompat.getColor(
                                        this@TrendTopicActivity,
                                        R.color.common_FFF55041
                                    )
                                )
                                .setActionTextColor(
                                    ContextCompat.getColor(
                                        this@TrendTopicActivity,
                                        R.color.common_FFF55041
                                    )
                                )
                                .setActionBgResource(R.drawable.common_refresh_btn_bg)
                                .setDes("当前网络欠佳，点击重新尝试")
                                .setDesColor(
                                    ContextCompat.getColor(
                                        this@TrendTopicActivity,
                                        R.color.common_FFB6BABE
                                    )
                                )
                                .setException(it.error)
                                .setActionListener {
                                    contract?.refresh()
                                }
                        )
                    }

                    // 加载更多失败
                    ListResponseBean.STATE_LOAD_MORE_FAILED -> {
                        mViewBinding.refreshLayout.finishDzRefresh(true)
                        ToastManager.showToast("网络不稳定，请稍后重试")
                    }

                    else -> {}
                }
            }
        }
        contract?.tabs?.observe(lifecycleOwner) { r ->
            r?.let {
                // 初始化tabs
                if (!it.dataList.isNullOrEmpty() && it.state == ListResponseBean.STATE_REFRESH_SUCCESS) {
                    it.dataList?.let { l ->
                        initTagView(l)
                    }
                }
            }
        }
        contract?.pageIsLoading?.observe(lifecycleOwner) {
            if (it == true) {
                // 子页面刷新，被动加载数据中
                mViewBinding.statusCom.bindData(Status().setStatus(Status.LOADING))
            }
        }
    }

    //大数据曝光埋点
    private fun appViewTrack() {
        DzTrackEvents.get().hivePv()
            .pType("page_view")
            .withOmapSource(OmapNode().apply {
                origin = SourceNode.origin_sqym
                originName = SourceNode.origin_name_sqym
                channelId = SourceNode.channel_id_htlb
                channelName = SourceNode.channel_name_htlb
                channelPos = "0"
                channelGroupId = (contract?.currentTab?.typeCode ?: 0).toString()
                channelGroupName = contract?.currentTab?.typeName ?: "全部"
                channelGroupPos = contract?.currentTab?.index ?: "0"
                rgts = BBaseKV.regTime
                nowChTime = BBaseKV.chTime
                is_login = if (CommInfoUtil.hasLogin()) 1 else 0
            }).withQmapSource(QmapNode().apply {
                eventType = "page_view"
            }).track()
    }

    // 切换右侧按钮显示view
    private fun changeRightView() {
        var notHas = false
        //全部按钮取取消红色1
        notHas =
            !(contract?.currentTab?.typeCode == null && contract?.currentTab?.typeName == "全部")
        if (notHas) {
            notHas =
                contract?.tabs?.value?.dataList?.none { data -> data.typeCode == contract?.currentTab?.typeCode }
                    ?: false
        }
        mViewBinding.ivRectangleRight.visibility = if (notHas) View.VISIBLE else View.GONE
        mViewBinding.tvRectangleRight.visibility = if (notHas) View.VISIBLE else View.GONE
        mViewBinding.ivArrowRight.visibility = if (notHas) View.GONE else View.VISIBLE
    }

    // 滑动到选中按钮中间
    private fun smoothScrollToIndex(topic: TopicTypeVo?) {
        changeRightView()
        contract?.tabs?.value?.dataList?.forEachIndexed { index, topicTypeVo ->
            val shouldSelect = when {
                // 场景1：被动滑动（带参数topic）
                topic?.typeCode != null && topicTypeVo.typeCode == topic.typeCode -> true
                // 场景2：主动滑动（无参数时找当前选中项）
                topic == null && topicTypeVo.typeCode == (contract?.currentTab?.typeCode
                    ?: 0) -> true

                else -> false
            }

            if (shouldSelect) {
                // 如果当前选中项和选中项不一致，则切换选中项并滚动到选中项中间
                if (index != adapter?.selectedPosition) {
                    adapter?.changeTab(index)
                    smoothScrollToCenter(index)
                } else {
                    // 如果当前选中项和选中项一致，则切换到全部选项
                    adapter?.changeTab(null)
                    smoothScrollToCenter(0)
                }
                return // 找到后立即返回
            }
        }

        // 未找到匹配项时的处理
        adapter?.changeTab(null)
        smoothScrollToCenter(0)
    }

    // 列表数据
    private fun setDataCells(dataList: List<TopicInfoVo>?) {
        dataList?.let {
            val cells = mutableListOf<DzRecyclerViewCell<*>>()
            it.forEach { data ->
                cells.add(DzRecyclerViewCell<TopicInfoVo>().apply {
                    viewClass = TrendTopicItemComp::class.java
                    viewData = data
                })
            }
            mViewBinding.rvComponents.addCells(cells)
        }
    }

    // 初始化tab
    private fun initTagView(list: List<TopicTypeVo>) {
        adapter = TopicTagAdapter(list)
        adapter?.selectedPosition = -1
        adapter?.setOnItemClickListener { channelId ->
            list.firstOrNull { it.typeCode == channelId }?.let {
                mViewBinding.rvComponents.removeAllCells()
                contract?.clickTab(it)
                smoothScrollToCenter(
                    if ((adapter?.selectedPosition ?: 0) > 0) (adapter?.selectedPosition
                        ?: 0) else 0
                )
                mViewBinding.rvComponents.smoothScrollToPosition(0)
                changeRightView()
            }
        }
        mViewBinding.rvTabs.layoutManager =
            LinearLayoutManager(this, LinearLayoutManager.HORIZONTAL, false)
        mViewBinding.rvTabs.adapter = adapter
        mViewBinding.rvTabs.addItemDecoration(SpaceItemDecoration(8.dp))
        mViewBinding.clRight.visibility = if (list.size < limitTabSize) View.GONE else View.VISIBLE
    }

    // 点击tab 滚动到中间
    private fun smoothScrollToCenter(position: Int) {
        val layoutManager = mViewBinding.rvTabs.layoutManager as LinearLayoutManager
        val smoothScroller = object : LinearSmoothScroller(this) {
            override fun calculateSpeedPerPixel(displayMetrics: DisplayMetrics): Float {
                return 100f / displayMetrics.densityDpi
            }

            override fun calculateDtToFit(
                viewStart: Int, viewEnd: Int, boxStart: Int, boxEnd: Int, snapPreference: Int
            ): Int {
                return (boxStart + (boxEnd - boxStart) / 2) - (viewStart + (viewEnd - viewStart) / 2)
            }
        }
        smoothScroller.targetPosition = position
        layoutManager.startSmoothScroll(smoothScroller)
    }

    override fun getPageName(): String {
        return "话题列表页"
    }
}