package com.dz.business.main.ui

import BBaseME
import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ObjectAnimator
import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.ShortcutInfo
import android.content.pm.ShortcutManager
import android.content.res.Configuration
import android.content.res.Resources
import android.graphics.drawable.Icon
import android.net.ConnectivityManager
import android.net.NetworkRequest
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.Choreographer
import android.view.KeyEvent
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.GONE
import android.view.ViewGroup.MarginLayoutParams
import android.view.ViewGroup.VISIBLE
import android.view.animation.DecelerateInterpolator
import androidx.annotation.RequiresApi
import androidx.fragment.app.Fragment
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import androidx.viewpager2.widget.ViewPager2
import com.blankj.utilcode.util.GsonUtils
import com.dz.business.base.BBaseMC
import com.dz.business.base.BBaseMC.welfarePopVo
import com.dz.business.base.BBaseMR
import com.dz.business.base.SpeedUtil
import com.dz.business.base.bcommon.BCommonMR
import com.dz.business.base.data.BBaseKV
import com.dz.business.base.data.BBaseKV.followBubbleStatus
import com.dz.business.base.data.BBaseKV.hasShowPacketComp
import com.dz.business.base.data.PageConstant
import com.dz.business.base.data.ab.config.ColdLaunchTestConfig
import com.dz.business.base.data.bean.CommonConfigBean
import com.dz.business.base.data.bean.OperateReportBean
import com.dz.business.base.data.bean.SeekBarEvent
import com.dz.business.base.data.bean.ShowTabVo
import com.dz.business.base.data.bean.ToastVo
import com.dz.business.base.data.bean.UninstallRetainVo
import com.dz.business.base.data.bean.VersionUpdateVo
import com.dz.business.base.data.bean.VideoInfoVo
import com.dz.business.base.data.bean.WelfarePendantConfigVo
import com.dz.business.base.detail.DetailMC
import com.dz.business.base.detail.DetailME
import com.dz.business.base.dialog.DialogMC
import com.dz.business.base.dialog.DialogMS
import com.dz.business.base.download.DownloadMS
import com.dz.business.base.flutter.FlutterMS
import com.dz.business.base.home.HomeMC
import com.dz.business.base.home.HomeME
import com.dz.business.base.home.HomeMR
import com.dz.business.base.home.HomeMS
import com.dz.business.base.livedata.AppEvent
import com.dz.business.base.main.MainMC
import com.dz.business.base.main.MainME
import com.dz.business.base.main.MainMR
import com.dz.business.base.main.MainMS
import com.dz.business.base.main.intent.MainIntent
import com.dz.business.base.main.intent.MainIntent.Companion.TAB_PERSONAL
import com.dz.business.base.main.intent.onDialogListener
import com.dz.business.base.main.priority.MainPriorityDialogTask
import com.dz.business.base.network.BBaseNetWork
import com.dz.business.base.notification.NotificationMC
import com.dz.business.base.notification.NotificationMS
import com.dz.business.base.operation.OperationMC
import com.dz.business.base.operation.OperationMS
import com.dz.business.base.operation.intent.OperationIntent
import com.dz.business.base.operation.interfaces.OperationReportCallback
import com.dz.business.base.personal.PersonalMC
import com.dz.business.base.personal.PersonalME
import com.dz.business.base.personal.PersonalMR
import com.dz.business.base.priority.PriorityConstants
import com.dz.business.base.priority.PriorityMC
import com.dz.business.base.priority.PriorityTaskManager
import com.dz.business.base.priority.tasks.PriorityDialogTask
import com.dz.business.base.splash.SplashDisplayCallback
import com.dz.business.base.splash.SplashMS
import com.dz.business.base.teen.TeenME
import com.dz.business.base.teen.TeenMR
import com.dz.business.base.teen.TeenMS
import com.dz.business.base.theatre.TheatreMC
import com.dz.business.base.theatre.TheatreME
import com.dz.business.base.theatre.data.TheatreChannelInfo
import com.dz.business.base.track.ITracker
import com.dz.business.base.track.IWebPage
import com.dz.business.base.ui.BaseDialogComp
import com.dz.business.base.ui.BaseFragment
import com.dz.business.base.ui.BaseTabActivity
import com.dz.business.base.ui.BaseVisibilityFragment
import com.dz.business.base.utils.CommInfoUtil
import com.dz.business.base.utils.DeviceInfoHelper
import com.dz.business.base.utils.GrayModeUtils
import com.dz.business.base.utils.GsonUtil
import com.dz.business.base.utils.HmAbUtil.TEST_KEY_COLD_LAUNCH
import com.dz.business.base.utils.HmAbUtil.getConfigContentByKey
import com.dz.business.base.utils.IdleUtils
import com.dz.business.base.utils.NotificationStyleLikeDialogUtils
import com.dz.business.base.utils.OCPCManager
import com.dz.business.base.utils.OaidUtil
import com.dz.business.base.video.VideoMS
import com.dz.business.base.web.WebME
import com.dz.business.base.web.WebMS
import com.dz.business.base.welfare.WelfareMC
import com.dz.business.base.welfare.WelfareME
import com.dz.business.base.welfare.WelfareMS2
import com.dz.business.base.welfare.widget.FloatWidgetListener
import com.dz.business.base.welfare.widget.IProgressDragPendantComp
import com.dz.business.base.welfare.widget.IProgressDynamicPendantComp
import com.dz.business.base.welfare.widget.IProgressPendantComp
import com.dz.business.base.welfare.widget.PendantComp
import com.dz.business.base.widget.WidgetMS
import com.dz.business.bridge.util.AppManager
import com.dz.business.main.R
import com.dz.business.main.adapter.FragmentViewPagerAdapter
import com.dz.business.main.data.MainKV
import com.dz.business.main.databinding.MainActivityBinding
import com.dz.business.main.db.ToastDatabase
import com.dz.business.main.db.entity.ToastTask
import com.dz.business.main.detain.DetainPresenter
import com.dz.business.main.repository.HomeTabBarRepository
import com.dz.business.main.ui.NotificationStyleLikeComp.Companion.NOTIFICATION_TYPE_REGULAR
import com.dz.business.main.ui.NotificationStyleLikeComp.Companion.NOTIFICATION_TYPE_RUNTIME
import com.dz.business.main.util.DotManager
import com.dz.business.main.util.UpdateAppUtil
import com.dz.business.main.util.VideoNetworkUtil
import com.dz.business.main.vm.MainActVM
import com.dz.business.notification.utils.AppIconBadgeUtils
import com.dz.business.notification.work.DelayPresenter
import com.dz.business.track.events.DzTrackEvents
import com.dz.business.track.events.sensor.AdTE
import com.dz.business.track.events.sensor.ErrorTE
import com.dz.business.track.monitor.MonitorMC
import com.dz.business.track.tracker.Tracker
import com.dz.business.track.utis.OaidTrackUtils
import com.dz.business.welfare.WelfareMS
import com.dz.business.welfare.data.WelfareKV.hasShowNewType
import com.dz.business.welfare.floatting.FloatWidgetManager
import com.dz.foundation.base.component.splash.SplashDisplayStateManager
import com.dz.foundation.base.friendly.noOpDelegate
import com.dz.foundation.base.manager.task.Task
import com.dz.foundation.base.manager.task.TaskManager
import com.dz.foundation.base.manager.task.TaskManager.Companion.delayTask
import com.dz.foundation.base.module.AppModule
import com.dz.foundation.base.utils.CrashUtils
import com.dz.foundation.base.utils.DeviceInfoUtil
import com.dz.foundation.base.utils.LocalActivityMgr
import com.dz.foundation.base.utils.LogUtil
import com.dz.foundation.base.utils.PermissionUtils
import com.dz.foundation.base.utils.ScreenUtil
import com.dz.foundation.base.utils.ViewUtils
import com.dz.foundation.base.utils.monitor.PlayerMonitorManager
import com.dz.foundation.base.utils.monitor.TimeMonitorManager
import com.dz.foundation.event2.EventBus
import com.dz.foundation.network.onResponse
import com.dz.foundation.ui.view.navigation.OnTabSelectedListener
import com.dz.platform.ad.AdManager
import com.dz.platform.ad.sky.FeedAd
import com.dz.platform.common.base.ui.UIPage
import com.dz.platform.common.base.ui.dialog.PDialogComponent
import com.dz.platform.common.base.ui.dialog.PriorityDialogManager
import com.dz.platform.common.router.SchemeRouter
import com.dz.platform.common.router.addDismissListener
import com.dz.platform.common.router.addShowListener
import com.dz.platform.common.router.onDismiss
import com.dz.platform.common.router.onShow
import com.dz.platform.common.toast.ToastManager
import com.gyf.immersionbar.BarHide
import com.sensorsdata.analytics.android.sdk.SensorsDataIgnoreTrackAppViewScreen
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch
import org.json.JSONObject


@SensorsDataIgnoreTrackAppViewScreen
class MainActivity : BaseTabActivity<MainActivityBinding, MainActVM>(), IWebPage {
    companion object {
        var isEditBook: Boolean = false//是否编辑书籍状态
    }

    private var pagerAdapter: FragmentViewPagerAdapter? = null
    private var networkCallback: ConnectivityManager.NetworkCallback? = null
    private var currentFragment: Fragment? = null
    private var toastList: List<ToastTask>? = null

    /**
     * 通知栏常驻
     */
    private val detainPresenter = DetainPresenter()
    private val notificationPresenter = DelayPresenter() //本地Push推送提醒

    /**
     * 个人中心正在编辑？
     */
    private var personalIsEditing = false
    private var selectShortcutTab: String? = null

//    private val dialogLayerMgr = DialogLayerManager("main")

    private var inviteToast: String? = null

    /**
     * 青少年弹窗
     */
    private var teenDialog: PDialogComponent<*>? = null

    /**
     * 福利挂件
     */
    private var welfareWidget: View? = null

    private var currentPosition: Int = 0
    private var currentTab = MainIntent.TAB_HOME

    private var globalConfigSaved = false

    //js是否接管返回键时间
    private var mIsTakeOverBackPressEvent = false

    private var mOnTabSelectedListener: OnTabSelectedListener? = null
    private var vpPageChangeCallback: ViewPager2.OnPageChangeCallback? = null

    private var resumeCount = 0

    private var isFirstCreate = true

    //页面有没有初始化过
    private var isPageInit=false
    private var isShowSplash = false

    private var notificationDisplayPage: String? = "0"
    private val TAG = "MainActivity"

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        LogUtil.d(TAG, "onNewIntent received")
        val selectedTab = mViewModel.getSelectTabIndex()
        selectTab(selectedTab)
        if (mViewModel.checkCurTabType(selectedTab, MainIntent.TAB_HOME)) {
            mViewModel.mHomeTabPage?.let {
                HomeME.get().locateChannel().post(it)
            }
        } else if (mViewModel.checkCurTabType(selectedTab, MainIntent.TAB_THEATRE)) {
            jumpToTheatre()
        }
        shortcutJump()
    }

    private fun jumpToTheatre() {
        TheatreMC.channelTabName = mViewModel.routeIntent?.channelTabName
        TheatreMC.ocpcBookId = mViewModel.routeIntent?.ocpcBookId
        TheatreME.get().locateChannel().post(
            TheatreChannelInfo(
                mViewModel.mChannel ?: 0, mViewModel.routeIntent?.channelTabName
            )
        )
    }


    private var versionUpdateData: String? = null
    override fun getTabName(): String {
        return currentTab
    }

    override fun onSaveInstanceState(outState: Bundle) {
        try {
            mViewModel.routeIntent?.selectedTab = currentTab
            outState.putInt("position", currentPosition)
            versionUpdateData?.let {
                LogUtil.d(UpdateAppUtil.TAG, "页面异常销毁，保存数据==$versionUpdateData")
                outState.putString("updateAppData", versionUpdateData)
            }
        } catch (e: Exception) {
            e.printStackTrace()
            LogUtil.d(
                BaseVisibilityFragment.Companion.TAG,
                "onSaveInstanceState error:" + e.message
            )
        }
        super.onSaveInstanceState(outState)
    }

    override fun onRestoreInstanceState(savedInstanceState: Bundle) {
        super.onRestoreInstanceState(savedInstanceState)
        currentPosition = savedInstanceState.getInt("position")
        currentTab = mViewModel.getTabName(currentPosition)
        versionUpdateData = savedInstanceState.getString("updateAppData", null)
        LogUtil.d(
            UpdateAppUtil.TAG, "页面重建，版本升级数据versionUpdateData==$versionUpdateData"
        )
        showUpdateDialog(versionUpdateData)
        selectTab(currentPosition)
    }

    override fun initImmersionBar() {
        LogUtil.d("MainActivity","initImmersionBar isSplashShowing=${isShowSplash}")
        if (isShowSplash) {
            return
        }
        var isDarkFont = true
        if (mViewModel.tabBean.tabList[currentPosition].tabType == MainIntent.TAB_HOME) {
            isDarkFont = false
        }
        getImmersionBar().transparentStatusBar().navigationBarColor(
            if (mViewModel.tabBean.tabList[currentPosition].tabType == MainIntent.TAB_HOME) {
                R.color.common_FF161718
            } else {
                R.color.common_card_FFFFFFFF
            }
        ).navigationBarDarkIcon(isDarkFont).statusBarDarkFont(isDarkFont)
            .hideBar(BarHide.FLAG_SHOW_BAR).init()

    }

    override fun onWindowFocusChanged(hasFocus: Boolean) {
        if (hasFocus) {
            initImmersionBar()
            SpeedUtil.mainShowTime = System.currentTimeMillis()
        }
        super.onWindowFocusChanged(hasFocus)
    }

    override fun initData() {
        LogUtil.d("MainActivity", "main density:${resources.displayMetrics.density}")
        setActivityTitle("顶级-主tab")
        // 渲染耗时监听
        MainME.get().refreshToast().post(Any())
        TimeMonitorManager.getMonitor(MonitorMC.SCENE_RCMD).startMonitor()
        TimeMonitorManager.getMonitor(MonitorMC.SCENE_THEATER).startMonitor()
        TimeMonitorManager.getMonitor(MonitorMC.SCENE_WELFARE).startMonitor()
        // 监听网络
        registerNetworkCallback()
        // 通知栏常驻
        detainPresenter.start(this)
        notificationPresenter.start(this)
        mViewModel.initColor()
        mViewModel.initTabData(true)

        // 等待升级弹窗
        PriorityTaskManager.addTask(
            MainPriorityDialogTask(
                PriorityMC.TASK_UPDATE_DIALOG,
                PageConstant.MAIN_ID,
                PriorityMC.PRIORITY_MAIN_UPDATE_DIALOG,
                null
            ).apply {
                status = PriorityConstants.STATUS_WAITING
                needWait = true
            }
        )
        PriorityTaskManager.executeTask()

        WelfareMS.get()?.init()
        DialogMS.get()?.init()

        // 初始化腾讯剧聚通SDK
        HomeMS.get()?.initTencentVideoSDK()

        // 初始化福利页广告预加载管理器
        runCatching {
            WebMS.get()?.initPreloadManager("MainActivity-WelfareAdPreload")
        }.onFailure { e ->
            e.printStackTrace()
            LogUtil.e(TAG, "Failed to initialize WelfareADPreloadManager,message=${e.message}")
        }
    }

    private fun registerNetworkCallback() {
        try {
            val connMgr = getSystemService(CONNECTIVITY_SERVICE) as? ConnectivityManager
            networkCallback = VideoNetworkUtil()
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                connMgr?.registerDefaultNetworkCallback(networkCallback!!)
            } else {
                connMgr?.registerNetworkCallback(NetworkRequest.Builder().build(), networkCallback!!)
            }
        } catch (e: Exception) {
            networkCallback = null
            CrashUtils.reportError("网络监听异常", e)
        }
    }

    private fun unregisterNetworkCallback() {
        try {
            networkCallback?.let {
                val connMgr = getSystemService(Context.CONNECTIVITY_SERVICE) as? ConnectivityManager
                connMgr?.unregisterNetworkCallback(it)
                networkCallback = null
            }
        } catch (e: Exception) {
            networkCallback = null
            CrashUtils.reportError("网络监听注销异常", e)
        }
    }


    override fun initView() {
        LogUtil.d("MainActivity", "initView")
        initViewPager()
        configBottomNavigationBar(mViewModel.getSelectTabIndex())
        if (selectShortcutTab == MainIntent.TAB_WELFARE) {
            MainMR.get().main().apply { selectedTab = MainIntent.TAB_WELFARE }.start()
            selectShortcutTab = null
        }
    }


    private fun changeDotColor(tabCode: String?) {
        tabCode?.let {
            mViewBinding.bottomBar.setNewMessageStroke(
                mViewModel.getTabPosition(it),
                mViewModel.tabBean.tabList[currentPosition].tabType == MainIntent.TAB_HOME
            )
        }
        mViewBinding.bottomBar.setNewMessageStroke(
            mViewModel.getTabPosition(TAB_PERSONAL),
            mViewModel.tabBean.tabList[currentPosition].tabType == MainIntent.TAB_HOME
        )
    }

    private fun initViewPager() {
        vpPageChangeCallback = object : ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                mViewBinding.bottomBar.visibility = View.VISIBLE
                LogUtil.d(mViewModel.TAG, "viewPage回调position==$position")
                mViewModel.setTabSelected(position)
                changeDotColor(DotManager.currentShowTabVo?.tabCode)
                initImmersionBar()
                if (mViewModel.checkCurTabType(position, MainIntent.TAB_THEATRE)) {
                    DotManager.hideTheatreRedDots(this@MainActivity)
                    TimeMonitorManager.getMonitor(MonitorMC.SCENE_THEATER)
                        .recordTime(MonitorMC.STAGE_START)
                } else if (mViewModel.checkCurTabType(position, MainIntent.TAB_WELFARE)) {
                    DotManager.hideWelfareRedDots(this@MainActivity)
                }
                FlutterMS.get()?.sendEventToFlutter(
                    "tabbarUpdate",
                    mapOf("name" to mViewModel.getTabForFlutter(position))
                )

                updateCurrentFragment()
                PriorityTaskManager.executeTask()
                checkCanShowNotificationLikeDialog()
            }
        }
        pagerAdapter = FragmentViewPagerAdapter(this, mViewModel.getTabBeans())
        mViewBinding.viewPager.isUserInputEnabled = false
        mViewBinding.viewPager.offscreenPageLimit = mViewModel.getTabBeans().size
        mViewBinding.viewPager.registerOnPageChangeCallback(vpPageChangeCallback!!)
        mViewBinding.viewPager.adapter = pagerAdapter
    }

    private fun configBottomNavigationBar(selectTabIndex: Int) {
        if (mOnTabSelectedListener == null) {
            mOnTabSelectedListener = object : OnTabSelectedListener {
                override fun onTabSelected(position: Int, view: View, isClick: Boolean) {
                    LogUtil.d(mViewModel.TAG, "Tab回调position==$position")
                    Tracker.setViewTitle(
                        view, if (DotManager.isShowing() && mViewModel.getTabPosition(
                                DotManager.currentShowTabVo?.tabCode ?: ""
                            ) == position
                        ) "有红点" else "无红点"
                    )
                    if (toastList?.isNotEmpty() == true) {
                        showToast(returnTabName(position))
                    }
                    if (!mViewModel.checkCurTabType(position, MainIntent.TAB_HOME)) {
                        MainME.get().onTabChange().post(Any())
                    }
                    setViewPagerItemSelected(position)
                    PersonalME.get().upSetTab()
                        .post(mViewModel.checkCurTabType(position, MainIntent.TAB_PERSONAL))
                    if (isClick && hasShowNewType == 1 && mViewModel.checkCurTabType(
                            position,
                            MainIntent.TAB_WELFARE
                        )
                    ) {
                        LogUtil.d(mViewModel.TAG, "默认位置执行position==$position")
                        hasShowNewType = 2
                    }

                    if (mViewBinding.ivBubble.visibility == VISIBLE && followBubbleStatus == 1) {
                        if (mViewModel.checkCurTabType(position, MainIntent.TAB_WATCHING)) {
                            mViewBinding.ivBubble.visibility = GONE
                            followBubbleStatus = 2
                            DzTrackEvents.get().operationClickTE().operationID("追剧气泡曝光时点击")
                                .track()
                        }
                    }

                    trackAppClick(position)


                }

                override fun onTabUnselected(position: Int) {}
                override fun onTabReselected(position: Int, isClick: Boolean) {
                    if (isClick) {
                        MainME.get().onTabReselected().post(mViewModel.getTabBean(position))
                        FlutterMS.get()?.sendEventToFlutter(
                            "tabbarUpdate",
                            mapOf(
                                "name" to mViewModel.getTabForFlutter(position),
                                "isClick" to true
                            )
                        )
                    }
                }

                override fun onDoubleClick(position: Int) {
                    if (mViewModel.checkCurTabType(position, MainIntent.TAB_WELFARE)) {
                        WebME.get().refreshWelfare().post(Any())
                    }
                    mViewModel.getTabBean(position)?.tabName.takeIf { !it.isNullOrEmpty() }?.let {
                        MainME.get().onTabDoubleClick().post(it)
                    }
                }
            }
        }
        mViewBinding.bottomBar.addOnTabSelectedListener(mOnTabSelectedListener)
        mViewBinding.bottomBar.addTabItems(mViewModel.getTabBeans())
        mViewBinding.bottomBar.setTabStyle(BBaseKV.bottomBarStyle, BBaseKV.welfareBottomBarStyle)
        showDot(DotManager.currentShowTabVo)
        selectTab(selectTabIndex)
    }


    //打点
    private fun trackAppClick(position: Int) {
        kotlin.runCatching {
            mViewModel.getTabBean(position)?.let { tabBean ->
                val jsonObject = JSONObject().apply {
                    put("\$element_content", tabBean.tabText)
                    put("\$element_type", "底tab")
                    put("\$element_position", tabBean.tabText)
                    put("\$screen_name", tabBean.tabText)
                    put("\$title", getPageName())
                    put("Type", if (BBaseKV.bottomBarStyle == 1) "纯文字" else "icon+文字")
                }
                Tracker.trackToSensor("\$AppClick", jsonObject)
            }
        }.onFailure { error ->
            error.printStackTrace()
        }
    }

    /**
     * 检测是否可以显示通知样式的弹窗
     */
    private fun checkCanShowNotificationLikeDialog() {
        if (SplashDisplayStateManager.isSplashShowing()) {
            LogUtil.d(TAG,"开屏页正在显示，跳过通知弹窗检查")
            return
        }
        runCatching {
            val commonConfigBean =
                GsonUtil.fromJson(BBaseKV.commonConfig, CommonConfigBean::class.java)

            val selectedTab = MainMC.selectedTab
            val pageType = when (selectedTab) {
                MainIntent.TAB_HOME -> "1"
                MainIntent.TAB_THEATRE -> "2"
                MainIntent.TAB_PERSONAL -> "4"
                MainIntent.TAB_WELFARE -> "3"
                MainIntent.TAB_WATCHING -> "5"
                else -> "0"
            }


            mViewBinding.notificationDialog.visibility = GONE
            if (NotificationStyleLikeDialogUtils.isShowingFloatComp) {
                LogUtil.d(TAG, "checkCanShowNotificationLikeDialog，有悬浮窗正在显示，此次跳过--> notificationDisplayPage=${notificationDisplayPage}, pageType=${pageType}")
                if (notificationDisplayPage == pageType) {
                    mViewBinding.notificationDialog.visibility = VISIBLE
                }

                return
            }


            //运营位弹窗
            commonConfigBean?.operlocationMap?.appTopPopVo?.let { config ->
                if (NotificationStyleLikeDialogUtils.canDisplayOperationDialog(pageType)) {
                    notificationDisplayPage = pageType
                    mViewBinding.notificationDialog.visibility = VISIBLE
                    mViewBinding.notificationDialog.setData(
                        NotificationStyleLikeComp.TipData(
                            mainTitle = config.popMainTitle ?: "",
                            subTitle = config.popSubTitle ?: "",
                            iconUrl = config.popImage,
                            deepLink = config.deeplink,
                            buttonContent = config.topPopButtonName ?: "",
                            notificationType = NOTIFICATION_TYPE_REGULAR
                        )
                    )
                    return
                }
            }


            //金币自动涨任务提醒弹窗
            commonConfigBean?.coinAutoUpPop?.let { config ->
                if (NotificationStyleLikeDialogUtils.canWelfareTaskDialog(pageType)) {
                    notificationDisplayPage = pageType
                    mViewBinding.notificationDialog.visibility = VISIBLE
                    mViewBinding.notificationDialog.setData(
                        NotificationStyleLikeComp.TipData(
                            mainTitle = config.popMainTitle,
                            subTitle = config.popSubTitle,
                            iconUrl = config.popImage,
                            deepLink = config.deeplink,
                            buttonContent = config.topPopButtonName,
                            notificationType = NOTIFICATION_TYPE_RUNTIME
                        )
                    )
                }
            }
        }


    }

    /**               position
     *                 a. 1-二级播放器
     *                 b. 2-剧场页
     *                 c. 3-首页
     *                 d. 4-福利页
     *                 e. 5-我的页面
     *                 f. 6-追剧
     */
    private fun showToast(position: Int) {
        LogUtil.d(TAG, " toastlist11  = ${toastList}")
        LogUtil.d(TAG, " toast position = ${position}")
        lifecycleScope.launch(Dispatchers.IO) {
            val dao = ToastDatabase.get().getCommentsDAO()
            toastList?.let { list ->
                // 添加一个标签，用于退出整个 forEach
                loop@ for (toastTask in list) {
                    if (System.currentTimeMillis() - toastTask.timeAdded >= 120 * 60 * 1000) {
                        dao.deleteToast(toastTask.id)
                    }
                    if (toastTask.page == position && System.currentTimeMillis() - BBaseKV.toastTimeLastShow >= toastTask.period * 60 * 1000) {
                        if (toastTask.page == 1) {
                            DetailME.get().showDetailToast().post(
                                ToastVo(
                                    page = listOf(1),
                                    showDuration = toastTask.showDuration,
                                    toast = toastTask.toast,
                                    timeAdded = toastTask.timeAdded,
                                    period = toastTask.period,
                                    id = toastTask.idNative
                                )
                            )
                        } else {
                            ToastManager.showToast(
                                toastTask.toast,
                                (toastTask.showDuration * 1000).toLong()
                            )
                        }

                        DzTrackEvents.get().operationExposureTE().operationType("toast")
                            .operationPlace(
                                when (toastTask.page) {
                                    1 -> "二级播放器"
                                    2 -> "剧场页"
                                    3 -> "首页"
                                    4 -> "福利页"
                                    5 -> "我的页面"
                                    6 -> "追剧"
                                    else -> "未定义页面"
                                }
                            ).operationName(toastTask.toast).track()

                        BBaseKV.toastTimeLastShow = System.currentTimeMillis()
                        dao.deleteToastDiff(toastTask.idNative)


                        MainME.get().refreshToast().post(Any())
                        break@loop // 跳出整个循环
                    }
                }
            }
        }
    }


    private fun returnTabName(position: Int): Int {
        return when {
            mViewModel.checkCurTabType(position, MainIntent.TAB_HOME) -> 3
            mViewModel.checkCurTabType(position, MainIntent.TAB_THEATRE) -> 2
            mViewModel.checkCurTabType(position, MainIntent.TAB_WELFARE) -> 4
            mViewModel.checkCurTabType(position, MainIntent.TAB_WATCHING) -> 6
            mViewModel.checkCurTabType(position, MainIntent.TAB_PERSONAL) -> 5
            else -> -1 // 默认返回 -1 其他情况
        }
    }


    private fun setBubblePosition(num: Int) {
        mViewBinding.ivBubble.apply {
            val lp = layoutParams
            if (lp is MarginLayoutParams) {
                val margin = num - ScreenUtil.dip2px(context, 93)
                lp.marginStart = margin
                LogUtil.d(TAG, "mViewBinding.ivBubble左边距 = ${margin}")

            }
            layoutParams = lp
            DzTrackEvents.get().operationExposureTE().operationID("追剧气泡曝光").track()
        }
        // 延迟 5 秒后将该部件不可见
        mViewModel.recommendGuideInfo?.showTime?.times(1000)?.let { time ->
            if (time > 0) { // 校验 showTime 是否为 0
                mViewBinding.root.postDelayed({
                    mViewBinding.ivBubble.visibility = View.GONE // 设置为不可见
                    if (followBubbleStatus != 2) {
                        MainME.get().bubbleDismiss().post(1)
                    }
                    followBubbleStatus = 2
                }, time.toLong())
            }
        }

    }

    private fun getWatchingPosition(position: Int): Int {
        // 假设你有一个 BottomBarLayout 作为底部导航栏
        val watchingView: View? = mViewBinding.bottomBar.getTabViewAtPosition(position)
        val watchingNextView: View? = mViewBinding.bottomBar.getTabViewAtPosition(position + 1)


        // 获取该视图在屏幕上的位置
        val location = IntArray(2)
        val nextLocation = IntArray(2)
        watchingNextView?.getLocationOnScreen(nextLocation)
        watchingView?.getLocationOnScreen(location)

        // 将像素位置转换为 dp 并赋值给 mViewModel
        val density = Resources.getSystem().displayMetrics.density
        LogUtil.d(
            mViewModel.TAG,
            "nextLocation[0] / density).toInt() = ${(nextLocation[0] / density).toInt()}  (location[0] / density).toInt()) = ${(location[0] / density).toInt()}"
        )
        mViewModel.watchingX = ((nextLocation[0]) + (location[0])) / 2 // x 坐标转换为 dp
        mViewModel.watchingY = (location[1] / density).toInt()

        LogUtil.d(
            mViewModel.TAG,
            "watching mViewModel.watchingX = ${mViewModel.watchingX}  mViewModel.watchingY = ${mViewModel.watchingY}"
        )
        return mViewModel.watchingX
    }

    private fun setViewPagerItemSelected(position: Int) {
        LogUtil.d(mViewModel.TAG, "setViewPagerItemSelected position=$position")
        currentPosition = position
        currentTab = mViewModel.getTabName(position)
        /**
         * 这里的目的是为了切换tab展示青少年弹窗
         * 延迟一下，不然取到的tab是切换前的页面，导致在不同tab上展示的弹窗对不上
         */
        mViewBinding.viewPager.post {
            selectPageShowTeenDialog()
        }
        MainME.get().onTabSelected().post(mViewModel.getTabBean(position))
        mViewBinding.viewPager.setCurrentItem(position, false)
        updateCurrentFragment()
        switchWelfareWidget()
        changeDotColor(DotManager.currentShowTabVo?.tabCode)
        initImmersionBar()
        LogUtil.d(MainMC.TAG, "tab被选中 $currentTab position:$position")
        if (currentTab == MainIntent.TAB_THEATRE && globalConfigSaved) {
            LogUtil.d(OperationMC.TAG, "剧场tab被选中")
            handleTheaterPopup()
        } else {
            theaterOperationDialog?.let {
                LogUtil.d(OperationMC.TAG, "非剧场tab被选中，隐藏剧场弹窗")
                it.dismiss()
            }
        }
        tryToShowAppStoreDialog()
        checkCanShowNotificationLikeDialog()
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun initListener() {
        mViewBinding.bottomBar.setOnTouchListener { motionEvent ->
            HomeMS.get()?.let {
                if (currentPosition == 0 && isRecommendActive()) {
                    if (VideoMS.get()?.expandSeekBarArea() == true) {
                        EventBus.post(AppEvent.TouchEvent(this, motionEvent))
                    } else {
                        BBaseME.get().onActionEvent()
                            .post(SeekBarEvent(this.javaClass.name, motionEvent))
                    }
                }
            }
        }
        mViewBinding.ivBubble.registerClickAction {

        }
        mViewBinding.clPauseAd.registerClickAction {
            MainME.get().closePauseAd().post(true)
        }
        mViewBinding.seekbarArea.registerClickAction { }
        mViewBinding.seekbarArea.setOnTouchListener { _, motionEvent ->
            if (currentPosition == 0 && isRecommendActive()) {
                BBaseME.get().onActionEvent().post(SeekBarEvent(this.javaClass.name, motionEvent))
                true
            } else {
                false
            }
        }

        mViewBinding.notificationDialog.registerListener(object :
            NotificationStyleLikeComp.ActionListener {
            override fun onDismiss() {
                mViewBinding.notificationDialog.visibility = GONE
                NotificationStyleLikeDialogUtils.isShowingFloatComp = false
                MainME.get().floatCompDisplayStatus().post(3)
            }

            override fun onShow() {
                NotificationStyleLikeDialogUtils.isShowingFloatComp = true
                LogUtil.d(TAG, "onShow")
            }

        })

    }

    private fun exitToHome(activity: Activity) {
        val intent = Intent(Intent.ACTION_MAIN).apply {
            addCategory(Intent.CATEGORY_HOME)
            flags = Intent.FLAG_ACTIVITY_NEW_TASK
        }
        activity.startActivity(intent)
    }



    @RequiresApi(Build.VERSION_CODES.O)
    override fun subscribeEvent(lifecycleOwner: LifecycleOwner, lifecycleTag: String) {
        super.subscribeEvent(lifecycleOwner, lifecycleTag)
        //书架页是否编辑书架
        HomeME.get().editFavorite().observe(lifecycleOwner, lifecycleTag) {
            isEditBook = it
            if (isEditBook) {
                mViewBinding.bottomBar.visibility = View.GONE
            } else {
                mViewBinding.bottomBar.visibility = View.VISIBLE
            }
        }
        PersonalME.get().onLoginResult().observeForever {
            //只要有登录结果就更新widget
            WidgetMS.get()?.updateIncomeWidget()
        }
        MainME.get().jumpToHome().observeForever {
            exitToHome(this)
        }
        // 个人中心进入编辑状态
        PersonalME.get().setEditState().observe(lifecycleOwner) {
            if (personalIsEditing == it) return@observe
            personalIsEditing = it
            if (personalIsEditing) {
                animateViewAlpha(mViewBinding.bottomBar, 0f, View.GONE)
            } else {
                animateViewAlpha(mViewBinding.bottomBar, 1f, View.VISIBLE)
            }
        }
        MainME.get().getUninstallData().observeSticky(lifecycleOwner) { data ->
            LogUtil.d(TAG, "getUninstallData = $data")
            clearAllShortcuts(this)
            data?.takeIf { it.isNotEmpty() }?.let { createDynamicShortcuts(this, it) }
        }

        //拿到toast的数据,插入数据库后取出最新数据
        MainME.get().getToast().observeForever() {
            lifecycleScope.launch(Dispatchers.IO) {
                val dao = ToastDatabase.get().getCommentsDAO()
                LogUtil.d(TAG, "insertToast = ${it}")
                val idDiff = (System.currentTimeMillis() % 1000000).toInt()
                LogUtil.d(TAG, "insertToast idDiff= ${idDiff}")
                it.page.forEach { page ->
                    LogUtil.d(TAG, "insertToast page = ${page}")
                    dao.insertToast(
                        ToastTask(
                            id = System.currentTimeMillis().toString(),
                            page = page,
                            toast = it.toast,
                            period = it.period,
                            showDuration = it.showDuration,
                            idNative = idDiff,
                            timeAdded = it.timeAdded
                        )
                    )
                }
                toastList = dao.getAllToasts()
                LogUtil.d(TAG, "insertToast后toastList = ${toastList}")
            }
        }
        //登录成功后移除新用户任务
        PersonalME.get().onLoginResult().observeForever() {
            if (it == 1) {
                PriorityTaskManager.removeTask(PriorityMC.TASK_NEW_USER_PACKET)
            }
        }

        //拿到toast的数据,插入数据库后取出最新数据
        MainME.get().refreshToast().observeForever() {

            lifecycleScope.launch(Dispatchers.IO) {
                val dao = ToastDatabase.get().getCommentsDAO()
                LogUtil.d(TAG, "弹出toast后获取最新对象")
                toastList = dao.getAllToasts()
                LogUtil.d(TAG, "最新toast = $toastList")
            }
        }

        //拿到toast的数据,插入数据库后取出最新数据
        MainME.get().detailToast().observeForever() {
            LogUtil.d(DetailMC.APP_ERROR_TAG, "二级页面 要展示 toast")
            showToast(1)
        }
        //拿到气泡的数据
        MainME.get().getRecommendGuideData().observe(lifecycleOwner) {
            if (it.newUserGuide == 1) {
                mViewModel.showBubbleDismiss = 1
                mViewModel.recommendGuideInfo = it.followBubbleBBean
                LogUtil.d(TAG, "mViewModel.recommendGuideInfo = ${mViewModel.recommendGuideInfo}")
            }
        }
        MainME.get().exitDetail().observe(lifecycleOwner) {
            LogUtil.d(OperationMC.TAG, "执行展示气泡逻辑")
            if (!mViewModel.checkCurTabType(
                    currentPosition,
                    MainIntent.TAB_WATCHING
                ) && followBubbleStatus == 1 && mViewModel.recommendGuideInfo?.showBubble == 1
            ) {
                setBubblePosition(getWatchingPosition(mViewModel.getWatchingPosition()))
                mViewBinding.ivBubble.visibility = VISIBLE
            }

        }

        MainME.get().bubbleDismiss().observe(lifecycleOwner) {
            if (followBubbleStatus == 1) {
                mViewBinding.ivBubble.visibility = GONE
                followBubbleStatus = 2
            }
        }
        HomeME.get().tabBarStatus().observe(lifecycleOwner, lifecycleTag) {
            mViewBinding.bottomBar.visibility = if (it) View.VISIBLE else View.INVISIBLE
            mViewBinding.bottomView.visibility = if (it) View.GONE else View.VISIBLE
        }

        HomeME.get().welfareTabBarStatus().observe(lifecycleOwner, lifecycleTag) {
            if (mViewModel.checkCurTabType(currentPosition, MainIntent.TAB_WELFARE)) {
                if (WelfareMS2.get()?.hasWelfareMall() == false) {
                    mViewBinding.bottomBar.visibility = if (it) View.VISIBLE else View.GONE
                }
            } else {
                mViewBinding.bottomBar.visibility = View.VISIBLE
            }
        }

        HomeME.get().recommendIsWatching().observe(lifecycleOwner, lifecycleTag) {
            mViewBinding.seekbarArea.visibility = if (it) View.VISIBLE else View.GONE
        }
        //版本更新弹窗
        BBaseME.get().showUpdateAppEvent().observeSticky(lifecycleOwner, lifecycleTag) { info ->
            info?.let {
                LogUtil.d(UpdateAppUtil.TAG, "首页收到粘性消息")
                showUpdateDialog(info)
            } ?: let {
                PriorityTaskManager.removeTask(PriorityMC.TASK_UPDATE_DIALOG)
                PriorityTaskManager.executeTask()
            }
        }
        //版本更新弹窗
        BBaseME.get().refreshOperation().observeSticky(lifecycleOwner, lifecycleTag) { info ->
            BBaseNetWork.get().commonConfig().setParam().onResponse {
                LogUtil.d(OperationMC.TAG, " vip 后 refresh 1150 success")
                it.data?.let { conf ->
                    OperationMS.get()?.saveOperationConfig(conf)
                }
            }.doRequest()
        }
        // 青少年弹窗
        BBaseME.get().showTeenDialogEvent().observeSticky(lifecycleOwner, lifecycleTag) { info ->
            if (mViewModel.hasShowTeenDialog) {
                PriorityDialogManager.updateStatus(PriorityDialogManager.teen)
                return@observeSticky
            }
            info?.let {
                TaskManager.delayTask(90) {
                    // 判断是否显示福利弹窗 如果展示 需要等到福利弹窗消失后展示青少年弹窗
                    if (!BBaseMC.isShowWelfareDialog) {
                        BBaseMC.needShowTeenDialog = false
                        showTeenDialog()
                    } else {
                        BBaseMC.needShowTeenDialog = true
                    }
                }
            } ?: let {
                PriorityDialogManager.updateStatus(PriorityDialogManager.teen)
            }
        }

        //显示toast
        MainME.get().showToastEvent().observeForever(lifecycleTag) {
            ToastManager.showToastNotAllowClear(it)
        }
        MainME.get().setWelfareWidgetVisible().observe(lifecycleOwner) {
            isDrawAdPageShowing = !it
            welfareWidget?.visibility = if (it) View.VISIBLE else View.GONE
            LogUtil.d(
                OperationMC.TAG_PENDANT,
                "打印隐藏和显示 setWelfareWidgetVisible=${welfareWidget?.visibility}"
            )
        }
        // 福利挂件配置
        WelfareME.get().welfareConfigReady().observeSticky(lifecycleOwner) {
            if (it > 0) addWelfareWidget()
        }

        /**
         * 刷新tab
         */
        BBaseME.get().updateTabStatus().observeSticky(lifecycleOwner, lifecycleTag) {
            if (pagerAdapter != null && mViewModel.initTabData()) {
                mViewModel.updateTabBeans()
                mViewBinding.viewPager.offscreenPageLimit = mViewModel.getTabBeans().size
                pagerAdapter!!.updateFragment(mViewModel.getTabBeans())
                currentPosition = mViewModel.getSelectTabIndex(currentTab)
                mViewBinding.viewPager.setBackgroundResource(mViewModel.getTabBeans()[mViewModel.getSelectTabIndex()].tab_bg_color)
                configBottomNavigationBar(currentPosition)
                LogUtil.d(
                    UpdateAppUtil.TAG,
                    "刷新tab MainKV.jumpType = ${MainKV.jumpType} currentPosition = $currentPosition"
                )
                if (MainKV.jumpType == 3 && !mViewModel.checkCurTabType(
                        currentPosition,
                        MainIntent.TAB_WELFARE
                    )
                ) {
                    selectTab(mViewModel.getSelectTabIndex(MainIntent.TAB_WELFARE))
                }
                Handler(Looper.getMainLooper()).postDelayed({
                    // 延迟执行的代码
                    if (currentTab == MainIntent.TAB_THEATRE && welfarePopVo?.actType == 23 && hasShowPacketComp == 0) {
                        // 要展示且未展示的时候才展示红包弹窗
                        WelfareMS2.get()?.tryToShowPacketDialog(getPageId())
                    } else if (welfarePopVo?.actType == 22) {
                        WelfareMS2.get()?.tryToShowPacketDialog(getPageId())
                    }
                }, 300)  // 1秒延迟


            }
        }
        MainME.get().inviteCodeResult().observeSticky(lifecycleOwner) {
            if (!it.isNullOrEmpty()) {
                if (!SplashDisplayStateManager.isSplashShowing()) {
                    LogUtil.d("inviteCode", "inviteCode 展示= ${it}")
                    ToastManager.showToast(it)
                } else {
                    inviteToast = it
                }
            }
        }
        //弹窗优先级处理。后续有时间优化代码
        BBaseME.get().onGlobalConfigSaved().observeForeverSticky(lifecycleTag) {
            // 邀请好友奖励弹窗 优先级在青少年弹窗后面
            mViewModel.commonConfigBean = it
            mViewModel.inviteResultShow(currentTab)

            if (!globalConfigSaved && currentTab == MainIntent.TAB_THEATRE) {
                handleTheaterPopup()
            }
            globalConfigSaved = true


            val selectTabIndex = mViewModel.getSelectedTabFromJumpType()
            val topActivity = LocalActivityMgr.getTopActivity()
            LogUtil.d(
                "XXX",
                "1150请求结束，selectTabIndex=${selectTabIndex},topActivity=${topActivity} "
            )
            if (topActivity is MainActivity) {
                if (selectTabIndex == 0) {
                    if (MainMC.isVideoStartPlay && !SplashDisplayStateManager.isSplashShowing()) {
                        LogUtil.d("XXX", "1150请求结束，开屏页没显示，且首页视频已经播放，触发预加载-->")
                        //视频起播后延迟2秒开始预加载福利页激励视频
                        delayTask(2000) {
                            LogUtil.d("XXX","视频起播，开屏页关闭2秒后，触发预加载-->")
                            WebMS.get()?.preloadWelfareRewardAd(0)
                        }
                    }
                } else {
                    LogUtil.d("XXX", "1150请求结束，非首页，触发预加载-->")
                    WebMS.get()?.preloadWelfareRewardAd(0)
                }
            }
        }

        BBaseME.get().onGlobalConfigShowDialog().observeSticky(lifecycleOwner, lifecycleTag) {
            mViewBinding.root.postDelayed({ mViewModel.inviteResultShow(currentTab) }, 500)
        }

        /**
         * 应用内红点
         */
        MainME.get().showRedDots().observe(lifecycleOwner) {
            showDot(it)
        }
        /**
         * 隐藏应用内红点
         */
        MainME.get().hideRedDots().observe(lifecycleOwner) {
            hideRedDots()
        }
        MainME.get().hideRedDot().observe(lifecycleOwner) {
            hideRedDot(it)
        }
        BBaseME.get().showRedDots().observeForeverSticky(lifecycleTag) {
            showDot(it, true)
        }
        BBaseME.get().hideRedDot().observeForeverSticky(lifecycleTag) {
            hideRedDot(it)
        }
        MainME.get().loadPauseAd().observe(lifecycleOwner) {
            loadPauseAd(it, false)
        }
        MainME.get().errorLoadPauseAd().observe(lifecycleOwner) {
            loadPauseAd(it, true)
        }
        MainME.get().closePauseAd().observe(lifecycleOwner) {
            if (mViewBinding.clPauseAd.visibility == View.VISIBLE) {
                if (it == false) {
                    MainMC.allowPlay = false
                }
                mPauseAd?.close()
            }
            hidePauseAd()
        }
        MainME.get().showPauseAd().observe(lifecycleOwner) {
            showPauseAd(it)
        }
        /**
         * 进入青少年模式
         */
        TeenME.get().enterTeenMode().observeForever(getUiId()) {
            if (it) {
                finish()
            }
        }
        /**
         * 检测到切换为青少年模式，则直接跳转青少年模式
         */
        BBaseME.get().onModeChange().observeSticky(lifecycleOwner, lifecycleTag) { mode ->
            if (mode == 0) {
                TeenMR.get().teenMode().start()
                finish()
            }
        }
        BBaseME.get().onVipStatusChanged().observeForever(lifecycleTag) {
            DownloadMS.get()?.onVipStateChanged()
        }
        MainME.get().onNetworkChanged().observeForever(lifecycleTag) {
            DownloadMS.get()?.onNetworkChanged(it == MainMC.NETWORK_CONNECTED)
        }
        WebME
            .get().takeoverBackPressEvent().observe(lifecycleOwner, lifecycleTag) {
                LogUtil.d("BaseVisibilityFragment", "收到takeOverBackPressEvent事件:${it}")
                mIsTakeOverBackPressEvent = it
            }

        //切换首页tab
        BBaseME.get().getHomeTag().observe(lifecycleOwner, lifecycleTag) {
            if (welfareWidget is IProgressDynamicPendantComp) {
                if (it == PageConstant.PAGE_RCMD_NAME_1 && !MainMC.pauseAdShown && !isDrawAdPageShowing) {
                    welfareWidget?.visibility = VISIBLE
                } else {
                    welfareWidget?.visibility = GONE
                }
                LogUtil.d(
                    OperationMC.TAG_PENDANT,
                    "打印隐藏和显示 getHomeTag=${welfareWidget?.visibility}"
                )
            }
        }

        //首帧监听
        Choreographer.getInstance().postFrameCallback(object : Choreographer.FrameCallback {
            override fun doFrame(frameTimeNanos: Long) {
                Choreographer.getInstance().removeFrameCallback(this)
                SpeedUtil.mainFirstFrameRenderTime = System.currentTimeMillis()
                TimeMonitorManager.getMonitor(MonitorMC.SCENE_LAUNCH)
                    .recordTime(MonitorMC.STAGE_MAIN_FIRST_VIEW_FRAME)
                ScreenUtil.dp = resources.displayMetrics.density
            }
        })
        // 应用灰色模式
        BBaseME.get().appGrayMode().observeForeverSticky(lifecycleTag) {
            GrayModeUtils.setGrayTheme(this, !isShowRecommendPage())
        }

        //开屏页结束后展示toast
        SplashDisplayStateManager.splashDisplayStatus.observe(lifecycleOwner) { visible ->
            if (!visible && inviteToast?.isNotEmpty() == true) {
                LogUtil.d("inviteCode", "开屏页结束 inviteCode 展示 inviteToast = $inviteToast")
                ToastManager.showToast(inviteToast)
                inviteToast = null
            }
        }

        //是否有悬浮窗正在显示
        MainME.get().floatCompDisplayStatus().observe(lifecycleOwner) { status ->
            if (status == 2) {
                LogUtil.d(TAG, "其它弹窗显示完毕，可以重新检测要不要显示了!")
                checkCanShowNotificationLikeDialog()
            }

        }

        MainME.get().bottomBarStyle().observeSticky(lifecycleOwner) { type ->
            LogUtil.d(
                TAG,
                "bottomBar样式配置变化，bottomBarStyle = $type  BBaseKV.welfareBottomBarStyle=${BBaseKV.welfareBottomBarStyle}"
            )
            mViewBinding.bottomBar.setTabStyle(type, BBaseKV.welfareBottomBarStyle)
        }
    }

    /**
     * 当前在推荐播放页
     */
    private fun isShowRecommendPage(): Boolean {
        val curTab = mViewModel.tabBean.tabList[currentPosition].tabType
        return (curTab == MainIntent.TAB_HOME && MainMC.homeFragmentChildIndex == 2)
    }

    /**
     * 此函数首先检查是否已经展示过青少年模式对话框，如果已经展示，则不重复展示，直接更新对话框状态为青少年模式。
     * 如果尚未展示，则调用 [showTeenDialog] 函数来展示青少年模式对话框。
     * 这个函数的目的是为了切换tab页展示青少年弹窗
     */
    private fun selectPageShowTeenDialog() {
        if (mViewModel.hasShowTeenDialog) {
            PriorityDialogManager.updateStatus(PriorityDialogManager.teen)
            return
        }
        showTeenDialog()
    }

    private fun triggerClickAtPosition(activity: Activity, x: Float, y: Float) {
        // 创建一个 ACTION_DOWN 事件
        LogUtil.d(OperationMC.TAG, "执行手动点击事件 x=$x y=$y")
        val downEvent = MotionEvent.obtain(
            System.currentTimeMillis(), // 时间戳
            System.currentTimeMillis(), // 时间戳
            MotionEvent.ACTION_DOWN, // 动作类型
            x.toFloat(), // x 坐标
            y.toFloat(), // y 坐标
            0 // 压力（通常为0）
        )

        // 创建一个 ACTION_UP 事件
        val upEvent = MotionEvent.obtain(
            System.currentTimeMillis(), // 时间戳
            System.currentTimeMillis(), // 时间戳
            MotionEvent.ACTION_UP, // 动作类型
            x.toFloat(), // x 坐标
            y.toFloat(), // y 坐标
            0 // 压力（通常为0）
        )

        // 获取目标视图，这里假设是 Activity 的根视图
        val view = activity.findViewById<View>(android.R.id.content)

        // 分别发送 ACTION_DOWN 和 ACTION_UP 事件
        view.dispatchTouchEvent(downEvent)
        view.dispatchTouchEvent(upEvent)
    }


    /**
     * 这个函数的目的是为了展示青少年弹窗
     */
    private fun showTeenDialog() {
        TeenMS.get()?.getTeenDialogIntent(currentTab)?.let { intent ->
            var task: Task? = null
            intent.addShowListener {
                teenDialog = it
                mViewBinding.bottomBar.blockClick(true)
                task = TaskManager.delayTask(500) {
                    mViewBinding.bottomBar.blockClick(false)
                }
                mViewModel.hasShowTeenDialog = true
                //青少年弹窗曝光打点
                teenDialogShowTrack()
            }
            intent.addDismissListener {
                teenDialog = null
                task?.cancel()
                mViewBinding.bottomBar.blockClick(false)
            }
            intent.activityPageId = this.getActivityPageId()
            PriorityTaskManager.addTask(
                MainPriorityDialogTask(
                    PriorityMC.TASK_TEEN_DIALOG,
                    PageConstant.MAIN_ID,
                    PriorityMC.PRIORITY_MAIN_TEEN_DIALOG,
                    intent
                )
            )
            PriorityTaskManager.executeTask()
        }
    }

    private fun loadPauseAd(it: VideoInfoVo?, isLooping: Boolean) {
        mViewModel.loadPauseAd(mViewBinding.flPauseAd, this, it, isLooping) {
            hidePauseAd()
            LogUtil.d(
                OperationMC.TAG_PENDANT,
                "打印隐藏和显示 loadPauseAd=${welfareWidget?.visibility}"
            )
        }
    }

    private fun hidePauseAd() {
        welfareWidget?.visibility = if (canShowRecommendPageWidget()) View.VISIBLE else View.GONE
        LogUtil.d(
            OperationMC.TAG_PENDANT,
            "打印隐藏和显示 hidePauseAd=${welfareWidget?.visibility} ${isDrawAdPageShowing}"
        )
        mViewBinding.clPauseAd.visibility = View.GONE
        MainMC.pauseAdShown = false
    }

    /**
     * 青少年弹窗曝光打点
     */
    private fun teenDialogShowTrack() {
        val name: String = when (currentTab) {
            MainIntent.TAB_HOME -> "首页"
            MainIntent.TAB_THEATRE -> "剧场"
            MainIntent.TAB_WATCHING -> "追剧"
            MainIntent.TAB_PERSONAL -> "我的"
            MainIntent.TAB_WELFARE -> "福利"
            else -> "首页"
        }
        DzTrackEvents.get().popupShow().popupName("青少年弹窗")
            .positionName(name)
            .track()
    }

    /**
     * 显示红点
     * @param showTabInfo
     */
    private fun showDot(showTabInfo: ShowTabVo?, alwaysShow: Boolean? = false) {
        showTabInfo?.tabCode?.run {
            val position = mViewModel.getTabPosition(this)
            if (position != currentPosition || alwaysShow == true) {
                LogUtil.d(
                    DotManager.TAG,
                    "显示红点，position==$position  红点内容==" + showTabInfo.dotText()
                )
                mViewBinding.bottomBar.setShowMessageAlways(position, alwaysShow ?: false)
                mViewBinding.bottomBar.showNewMessage(position, showTabInfo.dotText())
                changeDotColor(this)
                DotManager.showBadge(this@MainActivity)
            } else {
                DotManager.resetStatus()
            }
        }
    }

    private fun hideRedDot(showTabVo: ShowTabVo?) {
        for (position in 0 until mViewModel.getTabBeans().size) {
            if (mViewModel.getTabBeans()[position].tabName == showTabVo?.tabCode) {
                mViewBinding.bottomBar.hideNewMessage(position)
            }
        }
    }

    private fun hideRedDots() {
        for (position in 0 until mViewModel.getTabBeans().size) {
            mViewBinding.bottomBar.hideNewMessage(position)
        }
        DotManager.hideBadge(this)
    }

    private fun showUpdateDialog(versionUpdateData: String?) {
        versionUpdateData?.let {
            val info = GsonUtils.fromJson(versionUpdateData, VersionUpdateVo::class.java)
            if (info == null || info.downloadUrl.isNullOrEmpty()) {
                return
            }
            showUpdateDialog(info, true)
        }

    }

    private fun showUpdateDialog(info: VersionUpdateVo, forceShow: Boolean = false) {
        LogUtil.d(UpdateAppUtil.TAG, "开始拉起版本升级弹窗forceShow==$forceShow")
        val updateIntent =
            UpdateAppUtil.getUpdateAppDialog(info, forceShow, object : onDialogListener {
                override fun onShow(dialogComp: BaseDialogComp<*, *>) {
                    versionUpdateData = info.toJson()
                    BBaseMC.isShowMainDialog = true
                    SpeedUtil.isShowDialog = true
                    MainME.get().showDialogEvent().post(true)
                }

                override fun onDismiss() {
                    BBaseMC.isShowMainDialog = false
                    MainME.get().showDialogEvent().post(false)
                }
            })
//        (PriorityTaskManager.getTask(PriorityMC.TASK_OLD_PRIORITY_DIALOG) as? OldPriorityDialogTask
//            ?: OldPriorityDialogTask()).apply {
//            setIntent(updateIntent)
//            PriorityTaskManager.addTask(this)
//        }.also {
//            PriorityTaskManager.executeTask()
//        }
        if (updateIntent != null) {
            (PriorityTaskManager.getTask(PriorityMC.TASK_UPDATE_DIALOG) as? PriorityDialogTask)
                ?.apply {
                    intent = updateIntent
                    status = PriorityConstants.STATUS_READY
                } ?: let {
                PriorityTaskManager.addTask(
                    MainPriorityDialogTask(
                        PriorityMC.TASK_UPDATE_DIALOG,
                        PageConstant.MAIN_ID,
                        PriorityMC.PRIORITY_MAIN_UPDATE_DIALOG,
                        updateIntent
                    )
                )
            }
            PriorityTaskManager.executeTask()
        } else {
            PriorityTaskManager.removeTask(PriorityMC.TASK_UPDATE_DIALOG)
        }
    }

    //选中tab
    private fun selectTab(tabIndex: Int) {
        try {
            mViewBinding.bottomBar.setSelect(tabIndex, false)
        } catch (e: Exception) {
            e.printStackTrace()
            LogUtil.d(TAG, "selectTab error:message=${e.message}")
        }
    }

    override fun exitAnim() {
        overridePendingTransition(R.anim.common_ac_out_keep, R.anim.common_ac_out_keep)
    }

    override fun onBackPressAction() {
        LogUtil.d("MainActivity","onBackPressAction:${SplashDisplayStateManager.isSplashShowing()}")
        //冷启动开屏页显示时屏蔽双击事件
        if (SplashDisplayStateManager.isSplashShowing()) {
            return
        }
        if (mViewBinding.clPauseAd.visibility == View.VISIBLE) {
            MainME.get().closePauseAd().post(true)
            LogUtil.d("MainActivity", "clPauseAd可见")
        } else {
            if (isEditBook) {
                LogUtil.d("MainActivity", "正在编辑...")
                HomeME.get().editFavorite().post(false)
            } else if (mIsTakeOverBackPressEvent) {
                LogUtil.d("MainActivity", "h5页面接管了返回事件，不做任何处理")
                WebME.get().onActivityBackPressClick().post(true)
            } else if (personalIsEditing) {
                PersonalME.get().setEditState().post(false)
            } else {
                doDoubleClickExit()
            }
        }
    }

    private var lastMills = 0L

    private fun doDoubleClickExit() {
        var currentMills = System.currentTimeMillis()
        val diffValue = currentMills - lastMills
        LogUtil.d("MainActivity", "doDoubleClickExit,diffValue=$diffValue")
        if (diffValue < 1000) {
            LogUtil.d("MainActivity", "退出退出退出")
            //退出应用
            MainME.get().appExit().post(1)
//            exitToDesktop()
            doAppExit()
        } else {
            //在首页点击返回跳转到剧场开关为开并且当前选中的是首页，则执行跳转到剧场代码
            if (BBaseKV.homeExitToTheatre == 1 && mViewModel.checkCurTabType(
                    currentPosition, MainIntent.TAB_HOME
                )
            ) {
                LogUtil.d("MainActivity", "跳转剧场-->")
                MainMR.get().main().apply {
                    selectedTab = MainIntent.TAB_THEATRE
                }.start()
                currentMills = 0
            } else {
                //再次点击退出
                ToastManager.showToast("再按一次，退出${CommInfoUtil.getAppName()}")
            }
        }
        lastMills = currentMills
    }

    /**
     *  应用退出到桌面
     */
    private fun exitToDesktop() {
        try {
            getContentView().postDelayed({
                val home = Intent(Intent.ACTION_MAIN)
                home.addCategory(Intent.CATEGORY_HOME)
                startActivity(home)
            }, 200)
        } catch (e: Exception) {
//            CrashUtils.reportError(e)
            doAppExit()
        }
    }

    private fun doAppExit() {
        if (BBaseKV.isFirstShowPlotOcpcTips && OCPCManager.getPlotOcpcBookId() != null) {
            BBaseKV.isFirstShowPlotOcpcTips = false
        }
        AppManager.exit()
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        when (keyCode) {
            KeyEvent.KEYCODE_VOLUME_DOWN, KeyEvent.KEYCODE_VOLUME_UP -> {
                if (BBaseKV.isMute && mViewModel.checkCurTabType(
                        currentPosition, MainIntent.TAB_THEATRE
                    )
                ) {
                    BBaseKV.isMute = false
                    BBaseME.get().onMuteChanged().post(BBaseKV.isMute)
                }
            }
        }
        return super.onKeyDown(keyCode, event)
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray,
    ) {
        PermissionUtils.onRequestPermissionsResult(requestCode, permissions, grantResults)
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
    }

    private var isPaused = false


    override fun onCreate(savedInstanceState: Bundle?) {
        LogUtil.d("MainActivity", "MainActivity onCreate:savedInstanceState=$savedInstanceState")
        TimeMonitorManager.getMonitor("冷启动")
            .recordTime("${this.javaClass.simpleName} onCreate start")
        SpeedUtil.mainOnCreateTime = System.currentTimeMillis()
        if (SpeedUtil.mainShowTime == 0L) {
            SpeedUtil.mainShowTime = System.currentTimeMillis()
        }
        super.onCreate(savedInstanceState)

        //只有在savedInstanceState中没有数据时才显示开屏页，
        if (savedInstanceState == null) {
            showSplash()
        } else {
            doInit()
        }
        shortcutJump()
        TimeMonitorManager.getMonitor("冷启动")
            .recordTime("${this.javaClass.simpleName} onCreate end")
        trackOaidByShumeng()
    }

    override fun beforeInit(): Boolean {
        LogUtil.d("MainActivity","beforeInit, isFirstCreate=$isFirstCreate ")
        if (isFirstCreate) {
            initBaseLogic()
            return true
        }
        return false
    }


    private fun shortcutJump() {
        // 获取传递的参数
        val extraValue = intent?.getStringExtra("extra_key")
        val type = intent?.getStringExtra("type")
        LogUtil.d("shortcuts", " shortcuts Received extra: $extraValue  type = $type")
        if (extraValue != null) {
            if (extraValue == PersonalMC.SHORTCUT_FROM) {
                PersonalMR.get().setting().apply {
                    from = PersonalMC.SHORTCUT_FROM
                }.start()
            } else {
                SchemeRouter.doUriJump(extraValue)
                LogUtil.d("shortcuts", "shortcutJump deeplink: $extraValue")
            }
        }
        if (type == MainMC.SHORTCUT_WELFARE) {
            selectShortcutTab = MainIntent.TAB_WELFARE
        }
        if (type != null && type != "") {
            DzTrackEvents.get().uninstallTrigger().type(type).track()
        }
    }

    fun trackOaidByShumeng() {
        try {
            val oaid = OaidUtil.getOAIdTemp()
            OaidTrackUtils.oaidTempMain = oaid
            if (oaid != "OAID_DEFAULT") {
                DzTrackEvents.get().error().type(ErrorTE.CODE_GET_OAID_BY_SHUMENG)
                    .message(oaid ?: "null").track()
            }
            LogUtil.d(com.dz.platform.ad.TAG, "trackOaidByShumeng oaid = $oaid")
        } catch (e: Exception) {
            LogUtil.e(com.dz.platform.ad.TAG, "trackOaidByShumeng exception = ${e.message}")
        }
    }

    override fun onStart() {
        TimeMonitorManager.getMonitor("冷启动")
            .recordTime("${this.javaClass.simpleName} onStart start")
        super.onStart()
        TimeMonitorManager.getMonitor("冷启动")
            .recordTime("${this.javaClass.simpleName} onStart end")
    }

    override fun onResume() {
        SpeedUtil.mainOnResumeTime = System.currentTimeMillis()

        TimeMonitorManager.getMonitor("冷启动")
            .recordTime("${this.javaClass.simpleName} onResume start")
        super.onResume()
        LogUtil.d("MainActivity", "main onResume")
        LogUtil.d("StartUp", "main onResume")
        isPaused = false

        // 标记中转中
        if (MainMS.get()?.isInTransiting() == true) {
            resumeCount++
            if (resumeCount > 1) {
                MainMS.get()?.setTransitState(false)
            }
        }
        //oppo有活跃行为就清除红点
        if (DeviceInfoUtil.isOppo()) {
            LogUtil.d("main onResume", "main onResume  oppo有活跃行为就清除红点")
            AppIconBadgeUtils.clearBadgeCount(
                AppModule.getApplication(),
                BBaseMC.LAUNCHER_CLASS_NAME
            )
        }

        val abConfig = getConfigContentByKey<ColdLaunchTestConfig>(TEST_KEY_COLD_LAUNCH)
        if (abConfig?.enableAdLazyInit == true) {
            IdleUtils.post {
                AdManager.checkAdSdkStart("idle init")
            }
        }
        tryToShowAppStoreDialog()
        DialogMS.get()?.onPageVisible(PageConstant.MAIN_ID)

        // 检测是否有要添加的组件没有添加上
        WidgetMS.get()?.checkWaitingConfirmWidget()


        // 统计耗时
        TimeMonitorManager.getMonitor("冷启动")
            .recordTime("${this.javaClass.simpleName} onResume end")

        // 在每次回到有 tab bottom 的时候发消息判断是否展示气泡
        if (!mViewModel.checkCurTabType(currentPosition, MainIntent.TAB_WATCHING)) {
            // 本次生命周期内，且不在追页面时发消息判断展示气泡
            if (mViewModel.showBubbleDismiss == 1) {
                LogUtil.d("BaseVisibilityFragment", "不再在追页面，展示气泡")
                MainME.get().exitDetail().post(1)
            }
        } else {
            // 如果在追页面，直接不展示气泡
            LogUtil.d("BaseVisibilityFragment", "在追页面，直接不展示气泡")
            followBubbleStatus = 2
        }

        // 触发页面可见时要执行的优先级任务
        PriorityTaskManager.executeTask()
    }

    private fun initAfterPrivacyAgree() {
        try {
            if (isPageInit) {
                return
            }
            doInit()
            isPageInit = true
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 开屏页结束后，首页逻辑需要更新的写在这里
     */
    private fun updateMainLogicAfterSplash(){
        runCatching {
            //更新选中的Tab
            val selectTabIndex = mViewModel.getSelectedTabFromJumpType()
            selectTab(selectTabIndex)
            LogUtil.d("XXX","updateMainLogicAfterSplash,selectTabIndex=${selectTabIndex}")
            //非首页推荐，触发广告预加载逻辑
            if (selectTabIndex != 0) {
                LogUtil.d("XXX","页面创建，触发预加载-->,pageName=${currentFragment}")
                BBaseME.get().preloadWelfareAd().post(1)
            } else {
                //首页推荐页，检测用户感知耗时打点是否正确
                if ((PlayerMonitorManager.getPlayerMonitor(HomeMC.RECOMMEND_TRACK_TAG)
                        .getTime(PlayerMonitorManager.TAG_USER_SENSE_TIME_START) ?: 0) > 0
                ) {
                    LogUtil.d("player_splash_tag", "开屏页消失，用户感知耗时起始时间有值，需要更正")
                    PlayerMonitorManager.getPlayerMonitor(HomeMC.RECOMMEND_TRACK_TAG)
                        .recordTime(PlayerMonitorManager.TAG_USER_SENSE_TIME_START)
                }
            }
            OaidTrackUtils.trackOaidEvent()

        }.onFailure {
            it.printStackTrace()
        }
    }

    private fun dealSplashDismiss(){
        isShowSplash = false
        initImmersionBar()
        if (CommInfoUtil.isTeenMode()){
            TeenMR.get()
                .teenMode()
                .start()
            finish()
        }else{
            LogUtil.d("XXX","开屏页显示结束-->")
            updateMainLogicAfterSplash()
            SplashMS.get()?.handleDeepLinkIfNeeded(this@MainActivity)
        }
        if (selectShortcutTab == MainIntent.TAB_WELFARE) {
            MainMR.get().main().apply { selectedTab = MainIntent.TAB_WELFARE }.start()
            selectShortcutTab = null
        }
    }

    private fun showSplash() {
        LogUtil.d(TAG, "showSplash >>>>")
        // 初始化SplashView并显示
        SplashMS.get()?.showSplash(this, object : SplashDisplayCallback {
            override fun onShow() {
                LogUtil.d("MainActivity", "开屏页显示-->")
                getImmersionBar()
                    .navigationBarColor(R.color.common_bg_FFFFFFFF)
                    .hideBar(BarHide.FLAG_HIDE_STATUS_BAR)
                    .init()
                isShowSplash = true
                if (SplashDisplayStateManager.isInitAvailable()) {
                    LogUtil.d("MainActivity", "满足初始化条件,直接加载页面....")
                    initAfterPrivacyAgree()
                } else {
                    LogUtil.d("MainActivity", "不满足初始化条件,等待状态同步....")
                    //开屏页结束后视频起播
                    SplashDisplayStateManager.businessInitAvailableStatus.observe(this@MainActivity) { available ->
                        LogUtil.d(TAG, "初始化条件变化,available=$available ")
                        if (available) {
                            initAfterPrivacyAgree()
                        }
                    }
                }
            }

            override fun onDismiss() {
                LogUtil.d("MainActivity", "showSplash >>>> onDismiss")
                dealSplashDismiss()
            }

            override fun onError(message: String) {
                LogUtil.d("MainActivity", "showSplash >>>> onError, message=${message}")
                dealSplashDismiss()
            }

        })
    }

    override fun onPause() {
        super.onPause()
        isPaused = true
//        teenDialog?.dismiss()
        WidgetMS
            .get()
            ?.sendAppWidgetRefreshBroadcast()
    }

    override fun onDestroy() {
        super.onDestroy()
        try {
            unregisterNetworkCallback()

            currentFragment = null
            pagerAdapter = null
            mViewBinding.viewPager.adapter = null

            mViewBinding.bottomBar.removeOnTabSelectedListener(mOnTabSelectedListener)
            mOnTabSelectedListener = null

            vpPageChangeCallback?.let { mViewBinding.viewPager.unregisterOnPageChangeCallback(it) }
            vpPageChangeCallback = null
            mPauseAd?.destroy()
            mPauseAd = null
            networkCallback = null

            mViewModel.onPauseAdDestroy()
            detainPresenter.stop(this)
            notificationPresenter.stop(this)
            NotificationMS.get()?.stopLocalPush(this, NotificationMC.NOTIFICATION_ID_AD_JUMP_OUT)
            WelfareMS.get()?.onActivityFinish(WelfareMC.POSITION_MAIN, this)
//        (welfareWidget as? PendantComp)?.close()
            PriorityTaskManager.removeTaskByPageId(getPageId())

            // 清理福利页广告预加载管理器
            WebMS.get()?.destroyPreloadManager()

        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override fun finish() {
        super.finish()
        WelfareMS.get()?.onActivityFinish(WelfareMC.POSITION_MAIN, this)
        HomeTabBarRepository.clearTabList()
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        restoreWelfare()
    }

    private fun restoreWelfare() {
        welfareWidget?.run {
            post {
                (parent as? ViewGroup)?.removeView(this)
                addWelfareWidget(false)
            }
        }
    }

    /**
     * 展示福利挂件
     */
    private fun addWelfareWidget(needCheck: Boolean = true) {
        WelfareMS.get()?.apply {
            if (!needCheck || whetherAddWidget(WelfareMC.POSITION_MAIN, this@MainActivity)) {
                addWelfareWidget(
                    WelfareMC.POSITION_MAIN,
                    this@MainActivity,
                    object : FloatWidgetListener by noOpDelegate() {
                        override fun onLoadSuccess(widget: View, config: WelfarePendantConfigVo?) {
                            welfareWidget = widget
                            MainME.get().showWelfareWidget().postSticky(welfareWidget)
                            switchWelfareWidget()
                            LogUtil.d(
                                OperationMC.TAG_PENDANT,
                                "Main Pendant show success welfareWidget=${welfareWidget}  widget=${widget}"
                            )
                            OperationMS.get()?.operationExposureReport(config?.id, object :
                                OperationReportCallback {
                                override fun onReportSuccess(data: OperateReportBean?) {
                                    LogUtil.d(
                                        OperationMC.TAG_PENDANT, "Main Pendant refresh config"
                                    )
                                    refreshConfig.invoke()
                                }

                                override fun onReportFailed() {

                                }

                            })
                            if (welfareWidget is IProgressDragPendantComp) {
                                //禁用拖拽
                                (welfareWidget as? IProgressDragPendantComp)?.setIsDragEnable(
                                    false
                                )
                            }
                            if (welfareWidget is IProgressPendantComp) {
                                MainME.get().pendantExposure().postSticky(true)
                            } else {
                                config?.let {
                                    DzTrackEvents.get().operationExposureTE().apply {
                                        operationID(it.id?.toString() ?: "")
                                        operationPosition(it.operationPosition)
                                        operationName(it.name)
                                        operationType(it.operationType)
                                        userTacticInfo(it.userTacticsVo).bookId(it.bookId)
                                        bookName(it.bookName)
                                        if (welfareWidget is IProgressPendantComp) {
                                            buttonName("福利中心挂件")
                                            pendantPage("首页")
                                        }
                                    }.track()
                                }
                            }
                        }

                        override fun onClick(
                            view: View,
                            viewName: String,
                            config: WelfarePendantConfigVo?,
                        ) {
                            HomeME.get().editFavorite().post(false)
                            config?.let {
                                DzTrackEvents.get().operationClickTE().apply {
                                    operationID(config.id?.toString() ?: "")
                                    operationPosition(config.operationPosition)
                                    operationName(config.name)
                                    operationType(config.operationType)
                                    userTacticInfo(config.userTacticsVo)
                                    bookId(config.bookId)
                                    if (welfareWidget is IProgressPendantComp) {
                                        buttonName("福利中心挂件")
                                        pendantPage("首页")
                                    }
                                    bookName(config.bookName)
                                    positionName(viewName)
                                }.track()
                            }
                        }

                        override fun onClose(view: View) {
                            welfareWidget = null
                        }

                        override fun onMoveFloat(view: View, supClose: Boolean) {
                            widgetMove(view, supClose)
                        }

                        override fun onMoveStart(view: View, supClose: Boolean) {
                            widgetMoveStart(view, supClose)
                        }

                        override fun onMoveEnd(view: View, supClose: Boolean) {
                            widgetMoveEnd(view, supClose)
                        }
                    },
                    autoShow = false
                )
            }
        }
    }

    private var hasTriggeredCondition = false
    //  挂件拖动
    private fun widgetMoveStart(view: View, supClose: Boolean) {
        if (supClose) {
            if (isFinishing || isDestroyed) return
            hasTriggeredCondition = false
            if ((welfareWidget as? IProgressDynamicPendantComp)?.getPendantStatus() != true) {
                mViewBinding.clWelfareClose.apply {
                    translationY = 0f
                    visibility = GONE
                }
            }
        }
    }

    //挂件拖动结束
    private fun widgetMoveEnd(view: View, supClose: Boolean) {
        if (supClose) {
            if (isFinishing || isDestroyed) return
            hasTriggeredCondition = false
            if ((welfareWidget as? IProgressDynamicPendantComp)?.getPendantStatus() == true) {
                if (ViewUtils.isViewsIntersecting(view, mViewBinding.clWelfareClose)) {
                    (welfareWidget as? PendantComp)?.close()
                }
            }
            mViewBinding.clWelfareClose.apply {
                translationY = 0f
                visibility = GONE
            }
        }
    }

    //挂件拖动中
    private fun widgetMove(view: View, supClose: Boolean) {
        if (supClose) {
            if (isFinishing || isDestroyed) return
            if ((welfareWidget as? IProgressDynamicPendantComp)?.getPendantStatus() == true) {
                val intersecting =
                    ViewUtils.isViewsIntersecting(view, mViewBinding.clWelfareClose)
                mViewBinding.tvWelfareClose.text =
                    if (intersecting) "松手收起挂件" else "拖动至此收起挂件"
                mViewBinding.ivWelfareCloseH.visibility =
                    if (intersecting) GONE else VISIBLE
                mViewBinding.ivWelfareCloseS.visibility =
                    if (intersecting) VISIBLE else GONE
                val screenHeight = ScreenUtil.getScreenHeight()
                val viewLocation = IntArray(2)
                view.getLocationOnScreen(viewLocation)
                val thresholdPx = ScreenUtil.dip2px(this, 200f)
                val viewBottom = viewLocation[1] + view.height
                val triggerPosition = screenHeight - thresholdPx
                if (!hasTriggeredCondition && viewBottom > triggerPosition) {
                    hasTriggeredCondition = true
                    mViewBinding.clWelfareClose.apply {
                        translationY = height.toFloat()
                        visibility = View.INVISIBLE
                        post {
                            visibility = View.VISIBLE
                            ObjectAnimator.ofFloat(
                                this,
                                "translationY",
                                height.toFloat(),
                                0f
                            ).apply {
                                duration = 300
                                interpolator = DecelerateInterpolator()
                                start()
                            }
                        }
                    }
                }
            }
        }
    }

    // 是否展示沉浸式广告
    private var isDrawAdPageShowing = false

    /**
     * 显示福利挂件
     */
    private fun switchWelfareWidget(): Boolean {
        return if (canShowRecommendPageWidget()) {
            val result = WelfareMS.get()?.showWelfareWidget(this) ?: false
            if (result) {
                OperationMS.get()?.getPendantConfig(OperationMC.POSITION_THEATER)?.let { config ->
                    DzTrackEvents.get().operationExposureTE()
                        .operationID(config.id?.toString() ?: "")
                        .operationPosition(config.operationPosition).operationName(config.name)
                        .operationType(config.operationType).userTacticInfo(config.userTacticsVo)
                        .bookId(config.bookId).bookName(config.bookName).track()
                }
            }
            result
        } else {
            LogUtil.d("welfare", "Main 隐藏挂件， ${isDrawAdPageShowing}")
            WelfareMS.get()?.hideWelfareWidget(this)
            false
        }
    }

    //当前可以显示全局挂件或者福利挂件
    private fun canShowRecommendPageWidget(): Boolean {
        isShowRecommendPage()
        val curTab = mViewModel.tabBean.tabList[currentPosition].tabType
        // 首页挂件配置
        val config = OperationMS.get()?.getPendantConfig(WelfareMC.POSITION_MAIN)?.locations
        // 当前tab在挂件展示位置列表中，并且不是福利挂件
        val isShowTab = (config?.contains(curTab) == true && !FloatWidgetManager.isHomeWidget)
        // 当前tab是首页，并且下发福利挂件，并且现在就在推荐页
        val isHome =
            (curTab == MainIntent.TAB_HOME && FloatWidgetManager.isHomeWidget && MainMC.homeFragmentChildIndex == 2 && !isDrawAdPageShowing)
        LogUtil.d(
            "welfare",
            "Main 展示福利挂件，当前tab：$curTab ${!isDrawAdPageShowing} isShowTa=${isShowTab}|| isHome=${isHome}"
        )
        return isShowTab || isHome
    }


    private var mPauseAd: FeedAd? = null
    private fun showPauseAd(videoInfo: VideoInfoVo?) {
        if (mViewModel.getPauseAd() != null) {
            if (checkCanShowPauseAd()) {
                LogUtil.d(
                    MainMC.AD_PAUSE_TAG,
                    "首页暂停广告1-广告流量请求,have PauseAd,pos=${AdTE.PAUSE_109}"
                )
                mViewModel.senADTrafficReachEvent()
            } else {
                LogUtil.d(MainMC.AD_PAUSE_TAG, "未达到暂停广告展示间隔，不显示广告")
                return
            }
            if (mViewModel.getPauseAd() != null) {
                mViewModel.getPauseAd()?.let { pauseAd ->
                    try {
                        if (mPauseAd != pauseAd) {
                            mPauseAd?.destroy()
                            mPauseAd = null
                        }
                        mViewModel.updateVideoInfo(videoInfo)
                        mViewBinding.clPauseAd.visibility = View.VISIBLE
                        MainMC.pauseAdShown = true
                        val templateView = pauseAd.mFeedAdHolder?.getTemplateView(this)
                        mViewBinding.flPauseAd.removeAllViews()
                        templateView?.parent?.run {
                            (templateView.parent as ViewGroup).removeView(templateView)
                        }
                        mViewBinding.flPauseAd.addView(templateView)
                        BBaseKV.homePauseAdNum++
                        pauseAd.isShow = true
                        mPauseAd = pauseAd
                        welfareWidget?.visibility = View.GONE
                        LogUtil.d(
                            OperationMC.TAG_PENDANT,
                            "打印隐藏和显示 mPauseAd=${welfareWidget?.visibility}"
                        )
                    } catch (e: Exception) {
                        pauseAd.isShow = true
                        LogUtil.d(MainMC.AD_PAUSE_TAG, "广告显示异常")
                        hidePauseAd()
                        loadPauseAd(null, false)
                    }
                }
            } else {
                LogUtil.d(MainMC.AD_PAUSE_TAG, "没有广告，无法展示，开始预加载")
                hidePauseAd()
                loadPauseAd(videoInfo, false)
            }
        } else {
            LogUtil.d(MainMC.AD_PAUSE_TAG, "没有广告，无法展示，开始预加载")
            hidePauseAd()
            loadPauseAd(videoInfo, false)
            if (checkCanShowPauseAd()) {
                LogUtil.d(
                    MainMC.AD_PAUSE_TAG,
                    "首页暂停广告-广告流量请求,PauseAd is null, pos=${AdTE.PAUSE_109}"
                )
                mViewModel.senADTrafficReachEvent()
            }
        }
    }

    private fun checkCanShowPauseAd(): Boolean {
        if (!isRecommendActive()) {
            return false
        }
        if (!mViewModel.canShowPauseAd()) {
            LogUtil.d(MainMC.AD_PAUSE_TAG, "未达到暂停广告展示间隔，不显示广告")
            return false
        }
        return true
    }

    private val refreshConfig = {
        BBaseNetWork.get().commonConfig().setParam().onResponse {
            LogUtil.d(OperationMC.TAG, "refresh 1150 success")
            it.data?.let { conf ->
                OperationMS.get()?.saveOperationConfig(conf)
            }
        }.doRequest()
    }

    private var theaterOperationDialog: PDialogComponent<*>? = null
    private fun showTheaterPopup(dialogIntent: OperationIntent) {
        dialogIntent.apply {
            refreshBlock = refreshConfig
            activityPageId = <EMAIL>()
            beforeShowCallbackBlock = {
                currentTab == MainIntent.TAB_THEATRE
            }
            var task: Task? = null
            addShowListener {
                theaterOperationDialog = it
                mViewBinding.bottomBar.blockClick(true)
                task = TaskManager.delayTask(500) {
                    mViewBinding.bottomBar.blockClick(false)
                }
            }
            addDismissListener {
                theaterOperationDialog = null
                task?.cancel()
                mViewBinding.bottomBar.blockClick(false)
            }
            // 添加优先级
            PriorityTaskManager.addTask(
                MainPriorityDialogTask(
                    PriorityMC.TASK_THEATRE_OPERATION_DIALOG,
                    PageConstant.THEATRE_ID,
                    PriorityMC.PRIORITY_THEATRE_OPERATION_DIALOG,
                    this
                )
            )
            PriorityTaskManager.executeTask()
        }
    }

    /**
     * 当前是否是推荐在屏幕上，判断是否显示暂停广告
     */
    private fun isRecommendActive(): Boolean {
        return !isPaused && mViewModel.checkCurTabType(
            currentPosition,
            MainIntent.TAB_HOME
        ) && HomeMC.isRecommendActive
    }

    private fun updateCurrentFragment() {
        val position = mViewBinding.viewPager.currentItem
        if (position > -1 && position < mViewModel.getTabBeans().size) {
            currentFragment = mViewModel.getTabBeans()[position].tab_fragment
        }
    }

    override fun getPageName(): String {
        return (currentFragment as? ITracker)?.getPageName() ?: "顶级-主tab"
    }

    override fun getScreenUrl(): String {
        return (currentFragment as? BaseFragment<*, *>)?.getScreenUrl() ?: super.getScreenUrl()
    }

    override fun getTrackProperties(): JSONObject {
        return super.getTrackProperties().apply {
            put("PositionName", mViewModel.getTabBean(currentPosition)?.tabText)
            val deviceInfoHelper = DeviceInfoHelper(this@MainActivity)
            put("IsDarkMode", deviceInfoHelper.isNightModeEnabled())
            put("ScreenBrightness", deviceInfoHelper.getScreenBrightness())
            if (SpeedUtil.homeNeedReport && !SpeedUtil.hasPrivacyDialog) {
                put("AdSdkInit", SpeedUtil.adSdkInitStart - SpeedUtil.appAttachTime)
                put("AdSdkInitDuration", SpeedUtil.adSdkInitEnd - SpeedUtil.adSdkInitStart)
                if (SpeedUtil.mainShowTime == 0L) {
                    SpeedUtil.mainShowTime = System.currentTimeMillis()
                }
                put("ColdStartDuration", SpeedUtil.mainShowTime - SpeedUtil.appAttachTime)
                SpeedUtil.homeNeedReport = false
            }
        }
    }

    override fun onStop() {
        super.onStop()
        // 大数据上报设备信息
        DzTrackEvents.get().hiveDeviceInfo().supportH265(DeviceInfoUtil.isH265HWDecoderSupport())
            .track()
    }

    override fun autoTrack(): Boolean {
        return false
    }

    private fun tryToShowAppStoreDialog() {
        if (currentTab == MainIntent.TAB_THEATRE) {
            DialogMS.get()?.tryToShowCommentDialog(PageConstant.THEATRE_ID)
        }
    }

    override fun getPageId(): String {
        return PageConstant.MAIN_ID + "/" + (currentFragment as? UIPage)?.getPageId()
    }

    override fun getHtmlTitle(): String {
        // 当福利页在前台时，返回福利页的html标题
        return (currentFragment as? IWebPage)?.getHtmlTitle() ?: ""
    }

    /**
     * 处理剧场弹窗
     */
    private fun handleTheaterPopup() {
        // 优先获取预约新剧的上架状态
        OperationMS.get()?.getReservationDialog()?.let {
            showTheaterPopup(it)
            return
        }

        // 记录未能获取预约对话框的日志
        LogUtil.d(
            DialogMC.DIALOG_RESERVATION,
            "Failed to get reservation dialog, attempting to get theater operation dialog"
        )

        // 尝试获取操作对话框，如果成功则显示，否则记录日志
        val operationDialogIntent =
            OperationMS.get()?.getOperationDialogIntent(OperationMC.POSITION_THEATER, this)
        if (operationDialogIntent != null) {
            LogUtil.d(OperationMC.TAG, "Theater operation dialog intent retrieved successfully")
            showTheaterPopup(operationDialogIntent)
        } else {
            LogUtil.d(OperationMC.TAG, "Failed to retrieve theater operation dialog intent")
        }
    }

    /**
     * 创建一个视图的淡入淡出动画
     * @param view View
     * @param toAlpha Float
     * @param endVisibility Int
     */
    private fun animateViewAlpha(view: View, toAlpha: Float, endVisibility: Int) {
        view.visibility = View.VISIBLE // 确保视图可见，以便进行动画
        view.animate().alpha(toAlpha).apply {
            duration = PersonalMC.PERSONAL_EDIT_ANIMATION_DURATION
            interpolator = DecelerateInterpolator()
            setListener(object : AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: Animator) {
                    view.visibility = endVisibility // 动画结束后设置 visibility
                }
            })
            start()
        }
    }

    @RequiresApi(Build.VERSION_CODES.O)
    private fun createDynamicShortcuts(context: Context, dataList: List<UninstallRetainVo>?) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N_MR1) {
            val shortcutManager = context.getSystemService(ShortcutManager::class.java)
            val maxShortcuts = shortcutManager?.maxShortcutCountPerActivity ?: 0
            LogUtil.d("SHORTCUT_DEBUG", "Vivo 设备支持的最大快捷方式数: $maxShortcuts")
            // 判断是否是华为或小米设备
            val isSpecialBrand = DeviceInfoUtil.isMIUI() || DeviceInfoUtil.isHuawei()
            // 处理数据列表，华为/小米设备需要倒序
            val processedList = if (isSpecialBrand) dataList?.reversed() else dataList
            // 构建多个 ShortcutInfo

            LogUtil.d("processedList", "processedList--> == $processedList")
            val shortcutList = processedList?.mapNotNull { data ->
                data.message?.let {
                    val shortcutIntent = Intent(context, MainActivity::class.java).apply {
                        action = Intent.ACTION_VIEW
                        putExtra("extra_key", data.deepLink ?: "shortcut") // 根据不同的 shortcut 传递不同参数
                        putExtra(
                            "type",
                            data.type ?: "缓存类"
                        ) // 根据不同的 shortcut 传递不同参数
                    }

                    ShortcutInfo.Builder(context, data.type)
                        .setShortLabel(it)
                        .setLongLabel(it)
                        .setIcon(Icon.createWithResource(context, getVendorPicture(data.type)))
                        .setIntent(shortcutIntent)
                        .build()
                }
            } ?: emptyList()

            // 更新动态快捷方式
            if (shortcutList.isNotEmpty()) {
                shortcutManager.dynamicShortcuts = shortcutList
            }
            val existingShortcuts = shortcutManager.dynamicShortcuts
            LogUtil.d("SHORTCUT_DEBUG", "当前已存在的动态快捷方式数: ${existingShortcuts.size}")
        }
    }

    private fun clearAllShortcuts(context: Context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N_MR1) {
            val shortcutManager = context.getSystemService(ShortcutManager::class.java)
            shortcutManager.removeAllDynamicShortcuts()
        }
    }


    private fun getVendorPicture(type: String?): Int {
        val typeMap = mapOf(
            "宣发类" to listOf(
                R.drawable.main_xm_oppo_new, R.drawable.main_vivo_new, R.drawable.main_hw_new
            ),
            "内容类" to listOf(
                R.drawable.main_xm_oppo_rank, R.drawable.main_vivo_rank, R.drawable.main_hw_rank
            ),
            "缓存类" to listOf(
                R.drawable.main_xm_oppo_clear, R.drawable.main_vivo_clear, R.drawable.main_hw_clear
            )
        )

        val defaultList = listOf(
            R.drawable.main_xm_oppo_gold, R.drawable.main_vivo_gold, R.drawable.main_hw_gold
        )

        val vendorIndex = when {
            DeviceInfoUtil.isMIUI() || DeviceInfoUtil.isOppo() -> 0
            DeviceInfoUtil.isVivo() -> 1
            else -> 2
        }

        return typeMap[type]?.get(vendorIndex) ?: defaultList[vendorIndex]
    }

    //    private var recommendContinueWatchingDialog: ContinueWatchFloatComp? = null
    //2.10.0 福利券弹窗
    private fun showWelfareCouponPopup() {
        val continueWatchIntent = BCommonMR.get().welfareCouponComp().apply {
//            continueWatchVo = data
            activityPageId = <EMAIL>()
        }.onShow {
            NotificationStyleLikeDialogUtils.isShowingFloatComp = true
//            recommendContinueWatchingDialog = it as ContinueWatchFloatComp
        }.onDismiss {
            NotificationStyleLikeDialogUtils.isShowingFloatComp = false
//            recommendContinueWatchingDialog = null
        }.onClose {
//            coroutineScope?.cancel()
        }
        PriorityTaskManager.apply {
            PriorityDialogTask(
                PriorityMC.TASK_CONTINUE_WATCH,
                PageConstant.HOME_ID,
                PriorityMC.PRIORITY_MAIN_CONTINUE_WATCH,
                continueWatchIntent
            )
            executeTask()
        }
    }

}