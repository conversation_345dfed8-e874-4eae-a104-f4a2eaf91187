<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:ignore="MissingDefaultResource">

    <merge tools:parentTag="com.dz.foundation.ui.widget.DzConstraintLayout">

        <com.dz.foundation.ui.widget.DzConstraintLayout
            android:layout_width="@dimen/common_dp290"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHeight_default="percent"
            app:layout_constraintHeight_max="@dimen/common_dp414"
            app:layout_constraintHeight_percent="0.65"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:shape_radius="@dimen/common_dp8"
            app:shape_solid_color="@color/common_card_FFFFFFFF">

            <com.dz.foundation.ui.widget.DzImageView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:src="@drawable/privacy_policy_top"
                    app:layout_constraintTop_toTopOf="parent"/>

            <com.dz.foundation.ui.widget.DzTextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/common_dp78"
                android:text="@string/splash_privacy_policy_title"
                android:textColor="@color/common_FF161718"
                android:textSize="@dimen/common_dp20"
                android:textStyle="bold"
                app:layout_constraintBottom_toTopOf="@+id/layout_content"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_chainStyle="spread_inside" />

            <com.dz.foundation.ui.widget.DzNestedScrollView
                    android:id="@+id/layout_content"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/common_dp28"
                    android:layout_marginTop="@dimen/common_dp12"
                    android:layout_marginEnd="@dimen/common_dp28"
                    app:layout_constrainedHeight="true"
                    app:layout_constraintBottom_toTopOf="@+id/btn_agree"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHeight_max="@dimen/common_dp236"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tv_title"
                    app:layout_constraintVertical_weight="1">

                <TextView
                        android:id="@+id/tv_content"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="start"
                        android:lineSpacingExtra="@dimen/common_dp7"
                        android:lineSpacingMultiplier="1"
                        android:textColor="@color/common_FF5E6267"
                        android:textSize="@dimen/common_dp15"
                        tools:ignore="SpUsage"/>

            </com.dz.foundation.ui.widget.DzNestedScrollView>

            <com.dz.foundation.ui.widget.DzView
                android:id="@+id/view_mask"
                android:layout_width="match_parent"
                android:layout_height="@dimen/common_dp32"
                android:visibility="invisible"
                app:layout_constraintBottom_toBottomOf="@+id/layout_content"
                app:layout_constraintEnd_toEndOf="@+id/layout_content"
                app:layout_constraintStart_toStartOf="@+id/layout_content"
                app:shape="rectangle"
                app:shape_solid_gradient_end_color="@color/common_FFFFFFFF_FF242424"
                app:shape_solid_gradient_orientation="top_bottom"
                app:shape_solid_gradient_start_color="@color/common_00FFFFFF_242424" />

            <com.dz.foundation.ui.widget.DzButton
                android:id="@+id/btn_refuse"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:background="@null"
                android:gravity="center"
                android:text="@string/splash_privacy_policy_cancel"
                android:textColor="@color/common_FF929AA1"
                android:textSize="@dimen/common_dp13"
                app:layout_constraintBottom_toTopOf="@id/btn_agree"
                app:layout_constraintEnd_toEndOf="@+id/btn_agree"
                app:layout_constraintStart_toStartOf="@+id/btn_agree"
                app:layout_constraintTop_toBottomOf="@+id/layout_content" />

            <com.dz.foundation.ui.widget.DzButton
                    android:id="@+id/btn_agree"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/common_dp40"
                    android:gravity="center"
                    android:layout_marginTop="@dimen/common_dp16"
                    android:includeFontPadding="false"
                    android:text="@string/splash_privacy_policy_sure"
                    android:textColor="@color/common_FFFFFFFF"
                    android:textSize="@dimen/common_dp16"
                    app:layout_constraintEnd_toEndOf="@+id/layout_content"
                    app:layout_constraintStart_toStartOf="@+id/layout_content"
                    app:layout_constraintTop_toBottomOf="@+id/btn_refuse"
                    app:layout_constraintBottom_toBottomOf="parent"
                    android:layout_marginBottom="@dimen/common_dp24"
                    app:shape_radius="@dimen/common_dp20"
                    app:shape_selector_state_pressed_alpha="0.5"
                    app:shape_solid_color="@color/common_FF64B972"
                    tools:ignore="SpUsage" />

        </com.dz.foundation.ui.widget.DzConstraintLayout>

    </merge>
</layout>