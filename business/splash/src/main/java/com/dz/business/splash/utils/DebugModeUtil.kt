package com.dz.business.splash.utils

import android.content.Context
import android.content.pm.PackageManager
import android.content.res.Resources
import android.text.TextUtils
import android.util.DisplayMetrics
import android.util.Log
import com.blankj.utilcode.util.GsonUtils
import com.dz.business.DzDataRepository
import com.dz.business.base.BBaseMC
import com.dz.business.base.data.BBaseKV
import com.dz.business.base.network.DomainManager
import com.dz.business.base.utils.CommInfoUtil
import com.dz.business.base.utils.OaidUtil
import com.dz.business.base.utils.PackageControlUtil
import com.dz.business.splash.utils.DebugUtils.DebugCallBack
import com.dz.foundation.base.data.kv.KVData
import com.dz.foundation.base.manager.task.TaskManager
import com.dz.foundation.base.module.AppModule
import com.dz.foundation.base.utils.DeviceInfoUtil
import com.dz.foundation.base.utils.LogUtil
import com.dz.foundation.base.utils.LogUtil.Companion.printStackTrace
import com.google.gson.Gson
import org.json.JSONObject
import java.io.ByteArrayInputStream
import java.io.Serializable
import java.security.cert.CertificateException
import java.security.cert.CertificateFactory
import java.security.cert.X509Certificate
import java.text.SimpleDateFormat
import java.util.*


object DebugModeUtil {
    fun checkDebugLaunch() {

        LogUtil.setDebugMode(BBaseKV.debugMode)

        DebugUtils.checkDebugLaunch(AppModule.getApplication(), object : DebugCallBack {

            override fun onDebugInfo(debugJson: String?) {
                LogUtil.d("debugJson", "onDebugInfo   debugJson=" + debugJson)
                val debugBean = GsonUtils.fromJson(debugJson, DebugBean::class.java)
                setDebugConfig(debugBean)
            }

            override fun onCommand(commandJson: String?) {
                commandJson?.run {
                    executeCommand(this)
                }

            }
        })
    }

    private fun setDebugConfig(debugBean: DebugBean?) {
        if (debugBean == null) {
            LogUtil.d("debugJson", "debugBean == null ")
            return
        }
        sendAppBaseInfo()
        BBaseKV.debugMode = true
        LogUtil.setDebugMode(true)
        val oldHost: String = DomainManager.getHost()
        if (debugBean != null) {
            if (!TextUtils.isEmpty(debugBean.host) && !TextUtils.equals(oldHost, debugBean.host)) {
                //如果本次改变了 host 则需重置用户
                debugBean.resetUser = true
            }

            if (debugBean.resetUser) {
                //如果已初始化过，则需清空sp 数据
                if (BBaseKV.appInitialized) {
                    TaskManager.ioTask { DzDataRepository.clearData() }
                    KVData.clearData()
                }
            }

            if (!TextUtils.isEmpty(debugBean.imei)) {
                OaidUtil.setOAIDCache(debugBean.imei ?: "")
            }

            if (!TextUtils.isEmpty(debugBean.style)) {
                //样式
            }

            //设置渠道号
            if (!TextUtils.isEmpty(debugBean.channel)) {
                CommInfoUtil.changeAppChannel(debugBean.channel, true)
            }

            //设置域名
            if (!TextUtils.isEmpty(debugBean.host)) {
                BBaseKV.testHost = debugBean.host ?: ""
                DomainManager.resetDomainList()
                LogUtil.d("debugJson", "setDebugConfig testHost= " + BBaseKV.testHost)
            }

            //设置录屏
            if (debugBean.recording != null) {
                BBaseKV.testRecording = (debugBean.recording == true)
                LogUtil.d("debugJson", "setDebugConfig recording= " + BBaseKV.testRecording)
            }
        }
    }


    /**
     * 执行调试指令
     *
     * @param commandJson
     */
    private fun executeCommand(commandJson: String) {
        try {
            val paramJObj = JSONObject(commandJson)
            val packageName = paramJObj.optString("packageName", "")
            val action = paramJObj.optString("action")
            val extraJObj = paramJObj.optJSONObject("extra")
            when (action) {
                "showAppInfo" -> sendAppBaseInfo()
                "open_book" -> if (extraJObj != null) {
                    val bookId = extraJObj.optString("bookId")
                    Log.d("DebugSetUtil", "open_book=$bookId")
                }
                else -> {}
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun sendAppBaseInfo() {
        try {
            LogUtil.d("debugJson", "sendAppBaseInfo ")
            val softInfo: String? = getSoftInfo(AppModule.getApplication())
            LogUtil.d("debugJson", "sendAppBaseInfo = $softInfo")
            DebugUtils.sendLog(AppModule.getApplication(), "appBaseInfo", softInfo)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }


    private fun getSoftInfo(context: Context): String? {
        val metrics = DisplayMetrics()

        val displayMetrics = context.resources.displayMetrics

        val info = StringBuilder()
        info.append("USER:").append(BBaseKV.userId)
        info.append("\np:").append(BBaseMC.VER_P)
        info.append("\n当前样式:").append(PackageControlUtil.packageStyle)
        info.append("\n当前用户渠道:").append(CommInfoUtil.getAppChannel())
        info.append("\n安装包渠道:").append(CommInfoUtil.getCurrentApkChannel())
        info.append("\n┏━━app info━━━")
        info.append("\nPKNA:").append(context.packageName)
        info.append("\nVER:").append(AppModule.getAppVersionName()).append('[')
            .append(AppModule.getAppVersionCode(context)).append(']')
        info.append("\nBUILD:").append(
            getFormatTime(
                PackageControlUtil.appBuildTime,
                "yyyy-MM-dd HH:mm"
            )
        )
        info.append("\nUID:").append(getSharedUserId(context))
        info.append("\nise:")
            .append(getFormatTime(CommInfoUtil.getInstallTime(), "yyyy-MM-dd HH:mm"))
        info.append("\n┏━━phone info━━━")
        info.append("\n[").append(DeviceInfoUtil.getBrand()).append("]")
            .append(DeviceInfoUtil.getModel())
        val w = displayMetrics.widthPixels
        val h = displayMetrics.heightPixels
        val dpNum = Math.sqrt(((w * w + h * h) / metrics.density).toDouble()).toInt()
        info.append("\n").append(DeviceInfoUtil.getOsVersionInt()).append(",").append(w).append("x")
            .append(h)
            .append(",").append(metrics.density).append(", ").append(metrics.densityDpi)
            .append(", ").append(dpNum)
        info.append("\nOAID:").append(OaidUtil.getOAIdCache())

        info.append("\n┏━━Git info━━━\n")
        info.append(UtilGit.getDefault(context).toString())
        info.append("\n┏━━sign info━━━\n")
        info.append(signInfo(context))
        info.append("\n---Base gitCode:").append(getGitCode(context)).append("---")

        return info.toString()
    }

    private fun getMetaData(context: Context, metaName: String?): String? {
        val metaData: String? = getMetaDataValue(context, metaName)
        return if (TextUtils.isEmpty(metaData)) {
            ""
        } else metaData
    }

    private fun getMetaDataValue(context: Context, name: String?): String? {
        var value: String? = null
        try {
            val appInfo = context.packageManager.getApplicationInfo(
                context.packageName,
                PackageManager.GET_META_DATA
            )
            value = appInfo.metaData.getString(name)
        } catch (e: PackageManager.NameNotFoundException) {

        }
        return value
    }

    private fun getGitCode(context: Context): String? {
        return getMetaData(context, "GIT_CODE")
    }

    private fun getSharedUserId(context: Context): String? {
        try {
            val packName = context.packageName
            val pm = context.packageManager
            val pi = pm.getPackageInfo(packName, 0)
            return pi.sharedUserId
        } catch (e: PackageManager.NameNotFoundException) {

        }
        return "_unknown_"
    }

    private fun signInfo(context: Context): String? {
        var certFactory: CertificateFactory? = null
        try {
            certFactory = CertificateFactory
                .getInstance("X.509")
        } catch (e: CertificateException) {
            e.printStackTrace()
        }
        val pm = context.packageManager
        try {
            val packageinfo = pm
                .getPackageInfo(context.packageName, PackageManager.GET_SIGNATURES)
            if (certFactory != null) {
                try {
                    val sBuf = java.lang.StringBuilder()
                    val cert = certFactory.generateCertificate(
                        ByteArrayInputStream(packageinfo.signatures[0].toByteArray())
                    ) as X509Certificate
                    sBuf.append("issuerDN:").append(cert.issuerDN).append("\n")
                    sBuf.append("signNumber:").append(cert.serialNumber).append("\n")
                    sBuf.append("signName:").append(cert.sigAlgName)
                    return sBuf.toString().trim { it <= ' ' }
                } catch (e: CertificateException) {
                    e.printStackTrace()
                } catch (e: NullPointerException) {
                    e.printStackTrace()
                }
            }
        } catch (e: PackageManager.NameNotFoundException) {
            e.printStackTrace()
        }
        return null
    }

    /**
     * 根据指定的时间戳，返回指定格式的日期时间
     *
     * @param time   时间戳
     * @param format 指定的日期格式<br></br>
     * eg:<br></br>
     * "yyyy-MM-dd HH:mm:ss"<br></br>
     * "yyyy-MM-dd"<br></br>
     * "yyyyMMddHHmmss"<br></br>
     * "yyyy/MM/dd HH:mm:ss.SSS"<br></br>
     * "HH:mm:ss"<br></br>
     * @return
     */
    private fun getFormatTime(time: Long, format: String?): String? {
        val date = Date(time)
        var strs = ""
        try {
            val sdf = SimpleDateFormat(format)
            strs = sdf.format(date)
        } catch (e: java.lang.Exception) {
            printStackTrace(e)
        }
        return strs
    }

    class DebugBean : Serializable {
        var host: String? = null
        var imei: String? = null
        var channel: String? = null
        var style: String? = null
        var resetUser = false
        var net_protocol: String? = null
        var ad_host_key: String? = null
        var encrypt = true
        var recording: Boolean? = null
    }

}