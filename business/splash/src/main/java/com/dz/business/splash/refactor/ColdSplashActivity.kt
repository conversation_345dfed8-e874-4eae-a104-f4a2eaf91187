package com.dz.business.splash.refactor

import BBaseME
import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import com.dz.business.base.BBaseMC
import com.dz.business.base.SpeedUtil
import com.dz.business.base.data.BBaseKV
import com.dz.business.base.detail.DetailME
import com.dz.business.base.detail.DetailMS
import com.dz.business.base.main.MainMR
import com.dz.business.base.main.MainMS
import com.dz.business.base.splash.SplashMC
import com.dz.business.base.teen.TeenMR
import com.dz.business.base.track.TrackUtil
import com.dz.business.base.utils.CommInfoUtil
import com.dz.business.base.utils.DeviceInfoHelper
import com.dz.business.base.utils.PIPUtil
import com.dz.business.splash.ui.BaseSplashActivity.Companion.TAG_SPLASH
import com.dz.business.splash.utils.HotSplashManager
import com.dz.business.splash.utils.InitUtil
import com.dz.business.splash.utils.JumpUtil
import com.dz.business.splash.utils.LaunchUtil
import com.dz.business.splash.utils.LaunchUtil.Companion.SHORTCUT
import com.dz.foundation.base.manager.task.TaskManager
import com.dz.foundation.base.utils.LocalActivityMgr
import com.dz.foundation.base.utils.LogUtil
import com.sensorsdata.analytics.android.sdk.SensorsDataAPI
import org.json.JSONObject

/**
 * LauncherActivity，只处理 deeplink 和 app被拉起的逻辑，不处理任何业务逻辑
 */
class ColdSplashActivity : AppCompatActivity() {

    private var TAG: String = "ColdSplashActivity"

    override fun onCreate(savedInstanceState: Bundle?) {
        SpeedUtil.splashActivityOnCreateTime = System.currentTimeMillis()
        super.onCreate(savedInstanceState)
        trackAppViewScreen()
        checkJumpRightNow()
    }

    private fun trackAppViewScreen() {
        kotlin.runCatching {
            val deviceInfoHelper = DeviceInfoHelper(this)
            val jsonObject = JSONObject().apply {
                val pageName = "新冷启动"
                var firstPageName = ""
                kotlin.runCatching {
                    firstPageName = pageName.split("-").first()
                }
                put("\$screen_name", firstPageName)
                put("\$title", pageName)
                put("\$referrer_title", TrackUtil.previousPage)
                put(
                    "SmallWindowSwitch",
                    if (PIPUtil.checkPipEnable(this@ColdSplashActivity) == true) "开" else "关"
                )
                put("IsDarkMode", deviceInfoHelper.isNightModeEnabled())
                put("ScreenBrightness", deviceInfoHelper.getScreenBrightness())

            }
            SensorsDataAPI.sharedInstance().track("\$AppViewScreen", jsonObject)

        }.onFailure { it.printStackTrace() }
    }

    /**
     * 根据拉起 App的场景，判断是直接处理deeplink跳转，还是跳转到 MainActivity中
     */
    private fun checkJumpRightNow() {
        LogUtil.d(
            TAG,
            "checkJumpRightNow isTaskRoot=$isTaskRoot ,isMainActive=${
                MainMS.get()?.isMainActive() == true
            }"
        )

        if (CommInfoUtil.isTeenMode()) {
            LogUtil.d(TAG, "青少年模式直接跳转")
            doDefaultJump()
            return
        }

        if (!isTaskRoot || MainMS.get()?.isMainActive() == true) {
            //MainActivity已经存在
            val intent = intent
            val action = intent.action
            if (intent.hasCategory(Intent.CATEGORY_LAUNCHER) && Intent.ACTION_MAIN == action || MainMS.get()
                    ?.isMainActive() == true
            ) {
                //从桌面图标启动
                printLog("APP已经启动，重新解析launchFrom")
                // 重新解析LaunchFrom
                LaunchUtil.initLaunchFrom(this)
                // 热启动页面不需要重新解析Intent
                HotSplashManager.needParseIntent = false
                if (!LocalActivityMgr.isBackground()) {
                    // 当前在前台，进行归因，无需关心结果
                    printLog("APP在前台，请求1103进行归因")
                    InitUtil.doInitRequest(SplashMC.SPLASH_COLD, "ColdSplashActivity checkJumpRightNow", null)
                }

                // 关闭画中画
                if (!DetailMS.get()?.getPipPlayingBookId().isNullOrEmpty()) {  // 当前正在小窗播放
                    LogUtil.d(TAG, "当前正在小窗播放")
                    if (BBaseMC.launchFrom != SHORTCUT) {
                        HotSplashManager.needParseIntent = true
                        if (LaunchUtil.getDeepLinkBookId().isNullOrEmpty()) {
                            LogUtil.d(TAG, "需要跳转到二级页")
                            DetailME.get().closePipPlayer().post("push点击")
                            TaskManager.delayTask(500) {
                                finishWithJump(!LocalActivityMgr.isBackground() || BBaseMC.launchFrom == LaunchUtil.LOCAL_PUSH || BBaseMC.launchFrom == LaunchUtil.LOCAL_DELAY_PUSH)
                            }
                        } else {
                            LogUtil.d(TAG, "小窗不需要跳转二级页")
                            DetailME.get().exitPip().post("启动APP")
                            finish()
                        }
                    } else {
                        LogUtil.d(TAG, "桌面图标启动，直接关闭页面")
                        DetailME.get().exitPip().post("启动APP")
                        finish()
                    }

                }
                // 直接跳转的逻辑：
                // 1. 如果app在前台就直接跳转，如果app不在前台就不跳转等待热启动进行跳转
                // 2. 从本地推送进入
                finishWithJump(!LocalActivityMgr.isBackground() || BBaseMC.launchFrom == LaunchUtil.LOCAL_PUSH || BBaseMC.launchFrom == LaunchUtil.LOCAL_DELAY_PUSH)
            } else {
                LogUtil.d(TAG, "检测到MainActivity已存在，但不满足从桌面图标启动的条件，进行默认跳转")
                doDefaultJump()
            }
        } else {
            LogUtil.d(TAG, "MainActivity 不存在，正常冷启动")
            doDefaultJump()
        }
    }


    private fun doDefaultJump() {
        LogUtil.d(TAG, "doDefaultJump")
        // 重新解析LaunchFrom
        LaunchUtil.initLaunchFrom(this)
        toMain()
        // 使用 overridePendingTransition 替代 finish
        finish()
        overridePendingTransition(0, 0)
    }

    private fun toMain() {
        printLog("去主页")
        LogUtil.d("StartUp", "去主页")
        if (CommInfoUtil.isTeenMode()) {  // 青少年模式
            TeenMR.get().teenMode().start()
        } else {
            MainMR.get().main().start()
        }
        // 设置无动画过渡
        overridePendingTransition(android.R.anim.fade_in, android.R.anim.fade_out)
    }

    fun finishWithJump(needJump: Boolean) {
        LogUtil.d(BBaseMC.OCPC_TAG, "进行页面跳转")
        if (isFinishing) {
            printLog("Splash isFinishing. Dont jump.")
            return
        }

        if (!BBaseKV.appInitialized) {
            BBaseKV.appInitialized = true
        }

        printLog("关闭页面 needJump=${needJump}")
        if (needJump && JumpUtil.jump()) {
            BBaseMC.launchJumpTime = System.currentTimeMillis()
        }
        finish()
        // 添加无动画过渡
        overridePendingTransition(0, 0)
    }


    override fun finish() {
        SpeedUtil.splashActivityFinishTime = System.currentTimeMillis()
        super.finish()
        BBaseME.get().onGlobalConfigShowDialog().postSticky(true)
        LogUtil.d("SplashActivity", "finish")
        // 确保无动画过渡
        overridePendingTransition(0, 0)
    }


    fun printLog(msg: String, tag: String = TAG_SPLASH) {
        LogUtil.d(tag, "冷启动 $msg")
    }

}