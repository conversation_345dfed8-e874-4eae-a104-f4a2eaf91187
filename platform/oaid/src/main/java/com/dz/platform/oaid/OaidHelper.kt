package com.dz.platform.oaid

import android.content.Context
import android.util.Log
import com.bun.miitmdid.core.InfoCode
import com.bun.miitmdid.core.MdidSdkHelper
import com.bun.miitmdid.interfaces.IIdentifierListener
import com.bun.miitmdid.interfaces.IPermissionCallbackListener
import com.bun.miitmdid.interfaces.IdSupplier
import com.bun.miitmdid.pojo.IdSupplierImpl
import com.dz.foundation.base.module.AppModule
import com.dz.foundation.base.utils.CrashUtils
import com.dz.foundation.base.utils.LogUtil
import com.dz.platform.dzcert.ConnectionHttpUtil
import com.dz.platform.dzcert.GetCertHelper
import com.dz.support.smid.SmSdk
import java.io.BufferedReader
import java.io.IOException
import java.io.InputStreamReader

/**
 * 使用
 *  OaidHelper.getOaId {
 *      //注意这里是子线程
 *      Log.d(TAG, "oadi = $it")
 *  }
 */
object OaidHelper : IIdentifierListener {
    private const val TAG = "OaidHelper"

    // OaidHelper版本号
    private const val HELPER_VERSION_CODE = 20230919
    const val OAIDTEMP_DEFAULT = "OAID_DEFAULT"

    //是否初始化SDK证书
    private var isCertInit = false

    // 设置是否开启sdk日志
    private var isSDKLogOn = true

    @Volatile
    private var isInLoading = false

    //是否正在获取证书
    private var isCertGetting = false

    @Volatile
    private lateinit var oaid: String

    private lateinit var callBack: (String) -> Unit

    private var isSupported = false
    private var isLimited = false
    private var isSupportRequestOAIDPermission = false
    private var oaidTemp: String? = OAIDTEMP_DEFAULT

    init {
        //（1）加固版本在调用前必须载入SDK安全库,因为加载有延迟，推荐在application中调用loadLibrary方法
        // System.loadLibrary("msaoaidsec");
        // OaidHelper版本建议与SDK版本一致，每次升级sdk 要更新HELPER_VERSION_CODE
        kotlin.runCatching {
            if (MdidSdkHelper.SDK_VERSION_CODE != HELPER_VERSION_CODE) {
                Log.w(TAG, "SDK version not match.")
            }
        }.onFailure { e ->
            CrashUtils.reportError("OaidHelper报错", e)
        }

    }

    /**
     * 1、从内存中获取
     * 2、从本地存储中获取
     * 3、从sdk中获取
     */
    fun getOaId(callBack: (String) -> Unit) {
        this.callBack = callBack
        LogUtil.d(TAG, "开始获取oaid")
        //1
        if (this::oaid.isInitialized && !isOaidEmpty(oaid)) {
            LogUtil.d(TAG, "从内存中获取返回：$oaid")
            callBack.invoke(oaid)
            return
        } else {
            LogUtil.d(TAG, "内存oaid无效")
        }
        //2
        val oaidLocal = OaidKVData.oaidLocal
        if (!isOaidEmpty(oaidLocal)) {
            LogUtil.d(TAG, "从本地存储中获取返回：$oaidLocal")
            oaid = oaidLocal
            callBack.invoke(oaid)
            return
        } else {
            LogUtil.d(TAG, "本地存储oaid无效")
        }

        if (!isInLoading && !OaidKVData.hasResetIllegalOaid) {
            //通过sdk获取开始获取oaid
            isInLoading = true
            LogUtil.d(TAG, "开始从sdk中获取oaid")
            // getDeviceIds(AppModule.getApplication())
            getOaidByShuMeng(callBack)
        }

    }

    fun resetIllegalOAID() {
        oaid = ""
        OaidKVData.oaidLocal = ""
        OaidKVData.hasResetIllegalOaid = true
    }

    private fun getOaidByShuMeng(callBack: (String) -> Unit) {
        try {
            SmSdk.getOaid { oaid ->
                LogUtil.d(TAG, "getOaidByShuMeng oaid = $oaid")
                this.oaid = oaid ?: ""
                if (oaid.isNotEmpty()) {
                    OaidKVData.oaidLocal = oaid
                }
                oaidTemp = oaid
                isInLoading = false
                callBack.invoke(oaid)
            }
        } catch (e: Exception) {
            isInLoading = false
            oaidTemp = "获取oaid失败${e.message}"
            oaid = ""
            OaidKVData.oaidLocal = ""
            LogUtil.e(TAG, "getOaidByShuMeng exception = $e")
        }
    }

    fun getOAIdCache(): String {
        return OaidKVData.oaidLocal
    }

    fun setOAIDCache(value: String) {
        OaidKVData.oaidLocal = value
    }

    fun getOAIdTemp(): String? {
        return oaidTemp
    }

    fun resetOAIdTemp() {
        oaidTemp = OAIDTEMP_DEFAULT
    }

    fun isOaidEmpty(oaid: String): Boolean {
        return OaidBlacks.blacks.contains(oaid) || oaid.isBlank()
    }

    /**
     * 获取ID
     * @param cxt
     */
    private fun getDeviceIds(
        cxt: Context,
        isGetOAID: Boolean = true,
        isGetVAID: Boolean = false,
        isGetAAID: Boolean = false,
    ) {
        //初始化SDK证书 证书只需初始化一次
        if (!isCertInit) {
            try {
                //useLocalCert 判断 证书内容使用AssetFile 还是使用DataStore
                var pemString: String = OaidKVData.oaidCert
                if (pemString.isEmpty()) {
                    LogUtil.d(TAG, "存储证书为null，读取本地证书文件")
                    pemString = loadPemFromAssetFile(cxt, cxt.packageName + ".cert.pem")
                } else {
                    LogUtil.d(TAG, "使用了存储的证书")
                }
                isCertInit = MdidSdkHelper.InitCert(cxt, pemString)
            } catch (e: Error) {
                e.printStackTrace()
            }
            if (!isCertInit) {
                CrashUtils.reportError("oaid证书初始化错误")
                Log.w(TAG, "getDeviceIds: cert init failed")
            }
        }

        //（可选）设置InitSDK接口回调超时时间(仅适用于接口为异步)，默认值为5000ms.
        // 注：请在调用前设置一次后就不再更改，否则可能导致回调丢失、重复等问题
        try {
            MdidSdkHelper.setGlobalTimeout(5000)
        } catch (error: Error) {
            error.printStackTrace()
        }
        var code = 0
        // （5）调用SDK获取ID
        try {
            code = MdidSdkHelper.InitSdk(cxt, isSDKLogOn, isGetOAID, isGetVAID, isGetAAID, this)
        } catch (error: Error) {
            error.printStackTrace()
        }

        //（6）根据SDK返回的code进行不同处理
        val unsupportedIdSupplier = IdSupplierImpl()
        when (code) {
            // 证书未初始化或证书无效
            InfoCode.INIT_ERROR_CERT_ERROR -> {
                Log.w(TAG, "cert not init or check not pass")
                onErrorSupport(code, unsupportedIdSupplier)
            }
            // 不支持的设备
            InfoCode.INIT_ERROR_DEVICE_NOSUPPORT -> {
                Log.w(TAG, "device not supported")
                onErrorSupport(code, unsupportedIdSupplier)
            }
            // 加载配置文件出错
            InfoCode.INIT_ERROR_LOAD_CONFIGFILE -> {
                Log.w(TAG, "failed to load config file")
                onErrorSupport(code, unsupportedIdSupplier)
            }
            // 不支持的设备厂商
            InfoCode.INIT_ERROR_MANUFACTURER_NOSUPPORT -> {
                Log.w(TAG, "manufacturer not supported")
                onErrorSupport(code, unsupportedIdSupplier)
            }
            // sdk调用出错
            InfoCode.INIT_ERROR_SDK_CALL_ERROR -> {
                Log.w(TAG, "sdk call error")
                onErrorSupport(code, unsupportedIdSupplier)
            }
            // 获取接口是异步的
            InfoCode.INIT_INFO_RESULT_DELAY -> {
                Log.i(TAG, "result delay (async)")
            }
            // 获取接口是同步的
            InfoCode.INIT_INFO_RESULT_OK -> {
                Log.i(TAG, "result ok (sync)")
            }

            else -> {
                // sdk版本高于DemoHelper代码版本可能出现的情况，无法确定是否调用onSupport
                // 不影响成功的OAID获取
                Log.w(TAG, "getDeviceIds: unknown code: $code")
            }
        }
    }

    private fun getDeviceIdsWithRequestPermission(cxt: Context) {
        // 获取当前isSupported状态
        getDeviceIds(AppModule.getApplication())
        if (isSupported) {
            if (isLimited) {
                // 如果支持请求OAID获取权限，就请求权限
                if (isSupportRequestOAIDPermission) {
                    Log.w(TAG, "initSdkWithPermissionCheck: requestOAIDPermission")
                    requestOAIDPermission(cxt, object : IPermissionCallbackListener {
                        // 获取授权成功
                        override fun onGranted(grPermission: Array<String>) {
                            Log.w(TAG, "requestOAIDPermission 允许授权")
                            getDeviceIds(AppModule.getApplication())
                        }

                        // 获取授权失败
                        override fun onDenied(dePermissions: List<String>) {
                            Log.w(TAG, "requestOAIDPermission 拒绝授权")
                            // 处理代码
                        }

                        // 禁止再次询问
                        override fun onAskAgain(asPermissions: List<String>) {
                            Log.w(TAG, "requestOAIDPermission 拒绝且不再询问")
                            // 处理代码
                        }
                    })
                }
            }
        }
    }

    private fun requestOAIDPermission(
        appContext: Context?,
        listener: IPermissionCallbackListener?,
    ) {
        MdidSdkHelper.requestOAIDPermission(appContext, listener)
    }

    /**
     * APP自定义的getDeviceIds(Context cxt)的接口回调
     *
     * @param supplier
     */
    override fun onSupport(supplier: IdSupplier) {
        // 获取Id信息
        // 注：IdSupplier中的内容为本次调用MdidSdkHelper.InitSdk()的结果，不会实时更新。 如需更新，需调用MdidSdkHelper.InitSdk()
        isSupported = supplier.isSupported
        isLimited = supplier.isLimited
        isSupportRequestOAIDPermission = supplier.isSupportRequestOAIDPermission
        val oaid = supplier.oaid
        val vaid = supplier.vaid
        val aaid = supplier.aaid

        // (7) 自定义后续流程，以下显示到UI的示例
        val idsText = """
             support: $isSupported
             limit: $isLimited
             isSupportRequestOAIDPermission: $isSupportRequestOAIDPermission
             OAID: $oaid
             VAID: $vaid
             AAID: $aaid
             """.trimIndent()
        LogUtil.d(TAG, "sdk 回调信息: idsText: $idsText")
        LogUtil.d(TAG, "sdk onSupport oaid = $oaid")
        this.oaid = oaid ?: ""
        if (!oaid.isNullOrEmpty()) {
            OaidKVData.oaidLocal = oaid
        }
        isInLoading = false
        if (this::callBack.isInitialized && !isOaidEmpty(oaid)) {
            callBack.invoke(oaid)
        }
    }

    private fun onErrorSupport(code: Int, supplier: IdSupplier) {
        if (code == InfoCode.INIT_ERROR_CERT_ERROR) {
            // 证书未初始化或证书无效
            //请求网络证书
            LogUtil.d(TAG, "证书未初始化或证书无效,从网路获取")
            if (!isCertGetting) {
                isCertGetting = true
                GetCertHelper.getDzCert(
                    3,
                    1,
                    AppModule.getApplication().packageName,
                    OaidKVData.appCertMd5,
                    object : ConnectionHttpUtil.OnRequestCallback {
                        override fun onSuccess(certStr: String?, md5: String?) {
                            LogUtil.d(TAG, "证书无效后获取网络证书\n certStr：$certStr \n md5：$md5")
                            if (!certStr.isNullOrEmpty() && !md5.isNullOrEmpty()) {
                                OaidKVData.oaidCert = certStr
                                OaidKVData.appCertMd5 = md5
                                getDeviceIds(AppModule.getApplication())
                            }
                            isCertGetting = false
                        }

                        override fun onError(resultCode: Int, errorMsg: String) {
                            isCertGetting = false
                        }

                    }
                )
            }


        } else {
            this.oaid = ""
            isInLoading = false
            if (this::callBack.isInitialized) {
                callBack.invoke(oaid)
            }
        }
    }

    /**
     * 从asset文件读取证书内容
     * @return 证书字符串
     */
    private fun loadPemFromAssetFile(context: Context, assetFileName: String): String {
        return try {
            val inputStream = context.assets.open(assetFileName)
            val br = BufferedReader(InputStreamReader(inputStream))
            val builder = StringBuilder()
            var line: String?
            while (br.readLine().also { line = it } != null) {
                builder.append(line)
                builder.append('\n')
            }
            builder.toString()
        } catch (e: IOException) {
            Log.e(TAG, "loadPemFromAssetFile failed")
            ""
        }
    }

    fun initOaidCert() {
        //网络获取oaid cert
        GetCertHelper.getDzCert(
            3,
            1,
            AppModule.getApplication().packageName,
            OaidKVData.appCertMd5,
            object : ConnectionHttpUtil.OnRequestCallback {
                override fun onSuccess(certStr: String?, md5: String?) {
                    LogUtil.d("OaidHelper", "启动app获取网络证书 \n certStr：$certStr \n md5：$md5")

                    if (!certStr.isNullOrEmpty() && !md5.isNullOrEmpty()) {
                        OaidKVData.oaidCert = certStr
                        OaidKVData.appCertMd5 = md5
                    }
                }

                override fun onError(resultCode: Int, errorMsg: String) {

                }

            }
        )
    }

}