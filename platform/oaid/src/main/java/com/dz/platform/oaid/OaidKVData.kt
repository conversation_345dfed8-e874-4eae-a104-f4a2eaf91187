package com.dz.platform.oaid

import com.dz.foundation.base.data.kv.KVData


object OaidKVData : KVData {

    var oaidCert by delegate("oaidCert", "")
    var oaidLocal by delegate("oaidLocal", "")
    var appCertMd5 by delegate("appCertMd5", "")
    var hasResetIllegalOaid: <PERSON>olean by delegate("resetOaid", false)
    override fun getGroupName(): String {
        return "com.dz.platform.oaid.OaidKVData"
    }


}