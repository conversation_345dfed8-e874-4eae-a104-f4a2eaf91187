package com.dz.platform.common.router

import android.text.TextUtils
import com.dz.foundation.base.utils.TypeUtils
import com.dz.foundation.event.Event
import com.dz.foundation.event.IModuleEvent
import com.dz.foundation.event.get
import com.dz.foundation.router.RouteIntent
import java.lang.reflect.*
import java.util.*

/**
 *@Author: shidz
 *@Date: 2022/9/26 14:17
 *@Description:  路由跳转回调 处理类   回调弱引用持有，通过事件总线通知处理，事件生命周期与lifecycleTag绑定
 *@Version:1.0
 */


open interface RouteCallback {


}

open interface RouteCallbackOwner<T : RouteCallback> {
    private val routeCallbackHandler: RouteCallbackHandler<T>?
        get() {
            if (this is RouteIntent) {
                var callbackHandler = this.filedMap["routeCallbackHandler"]
                if (callbackHandler == null|| callbackHandler !is RouteCallbackHandler<*>) {
                    callbackHandler = RouteCallbackHandler<T>()
                    this.filedMap["routeCallbackHandler"] = callbackHandler
                }
                return callbackHandler as RouteCallbackHandler<T>
            }
            return null
        }

    fun setRouteCallback(lifecycleTag: String, callback: T) {
        routeCallbackHandler?.setRouteCallback(lifecycleTag, callback)
    }

    fun getRouteCallback(): T? {
        return routeCallbackHandler?.getRouteCallbackProxy()
    }
}

private class RouteCallbackHandler<T : RouteCallback> {


    private var lifecycleTag: String? = null
    private var callbackId: String? = null
    private var callbackProxy: T? = null
    fun setRouteCallback(lifecycleTag: String, callback: T) {
        this.lifecycleTag = lifecycleTag
        this.callbackId = callback.hashCode().toString() + "_" + UUID.randomUUID()
        val routeCallbackInterface = TypeUtils.getImplInterface(callback, RouteCallback::class.java)
        routeCallbackInterface?.let { initCallbackProxy(it as Class<T>) }

        RouteCallbackEvent.get().callback().observeForever(lifecycleTag) {
            if (TextUtils.equals(callbackId, it.callBackId)) {
                callback?.run {
                    if (it.callBackParams != null) {
                        it.method.invoke(this, *it.callBackParams)
                    } else {
                        it.method.invoke(this)
                    }
                }
            }
        }
    }

    private fun initCallbackProxy(callBackClass: Class<T>) {
        val invocationHandler = InvocationHandler { proxy, method, args: Array<out Any>? ->
            callbackId?.let {

                RouteCallbackEvent.get().callback().post(CallbackResult(it, method, args))

            }

        }
        val proxy = Proxy.newProxyInstance(
            RouteCallback::class.java.classLoader,
            arrayOf(callBackClass),
            invocationHandler
        )
        callbackProxy = proxy as T
    }

    fun getRouteCallbackProxy(): T? {
        return callbackProxy
    }

}

private data class CallbackResult(
    val callBackId: String,
    val method: Method,
    val callBackParams: Array<out Any>?
)

private interface RouteCallbackEvent : IModuleEvent {
    companion object {
        private val instance by lazy { RouteCallbackEvent::class.java.get() }
        fun get(): RouteCallbackEvent {
            return instance
        }
    }

    fun callback(): Event<CallbackResult>
}