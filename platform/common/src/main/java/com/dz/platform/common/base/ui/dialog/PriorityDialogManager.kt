package com.dz.platform.common.base.ui.dialog

import com.dz.foundation.base.utils.CrashUtils
import com.dz.foundation.base.utils.LocalActivityMgr
import com.dz.foundation.base.utils.LogUtil
import com.dz.platform.common.base.ui.UIPage
import com.dz.platform.common.router.DialogRouteIntent
import com.dz.platform.common.router.addDismissListener
import com.dz.platform.common.toast.ToastManager

/**
 *@Author: zhanggy
 *@Date: 2023-11-24
 *@Description:全局优先级Dialog管理器
 *@Version:1.0
 */
@Deprecated(message = "使用PriorityTaskManager")
object PriorityDialogManager {

    const val TAG = "dialog_priority"

    //准备四个事件名
    const val update:String = "update"  // 升级弹窗
    const val teen:String = "teen"  // 青少年弹窗
    const val invite:String = "invite"  // 邀请弹窗
    const val theater:String = "theater"  // 剧场
    const val SIGN_IN:String = "signIn"  // 签到弹窗
    const val PUSH_2: String = "push2"  // 第二类push开关弹窗
    const val WIDGET: String = "widget"  // 加桌弹窗
    const val REWARD: String = "reward" //红包弹窗
    const val PACKET: String = "packet" //红包弹窗
    const val GUIDE: String = "guide" //红包弹窗

    private var inviteToast:String = ""
    private var inviteShowToast:Boolean = false
    // 0 未添加 1 添加未消费 2消费完
    val listApply = mutableMapOf<String, Int>(
        update to 0,
        teen to 0,
        invite to 0,
        theater to 0,
        PACKET to 0,
        SIGN_IN to 0,
        PUSH_2 to 0,
        GUIDE to 0,
    )

    private var isShowDialog:Boolean = false;

    //缓存弹窗的顺序调整用
    private val listDialogs = mutableMapOf<String, DialogRouteIntent?>()

    //手动消费执行下个责任事件
    fun updateStatus(str: String) {
        kotlin.runCatching {
            if(listApply[str]!=1){
                listApply[str] = 2
            }
            listDialogs[str] = null
            when (str) {
                update -> {
                    teenApply()
                }

                teen -> {
                    if(listApply[invite]==1){
                        inviteApply()
                    } else if (getTopPage()?.split("/")?.contains("home") == true) {
                        push2Apply()
                    }else if(listApply[theater]==1) {
                        theaterApply()
                    }else{
                        guideApply()
                    }
                }
                GUIDE -> {
                    signInApply()
                }

                invite -> {
                    signInApply()
                }

                theater -> {
                    signInApply()
                }
                PACKET -> {
                    rewardApply()
                }
                REWARD -> {
                    signInApply()
                }
                SIGN_IN -> {
                    teenApply()
                }

                PUSH_2 -> {
                    widgetApply()
                }

                WIDGET -> {

                }
            }
        }.onFailure {
            CrashUtils.reportError("弹窗优先级异常", it)
        }
    }

    //更新 事件
    fun updateApply(dialog: DialogRouteIntent? = null) {
        if (dialog != null) {
            listDialogs[update] = dialog
        }
        if (listApply[update] == 1 && (!isShowDialog)) {
            listDialogs[update]?.start()
            isShowDialog = true
            val dismiss: () -> Unit = {
                listApply[update] = 2
                listDialogs[update] = null
                isShowDialog = false
                teenApply()
            }
            listDialogs[update]?.addDismissListener (dismiss)
            listDialogs[update]?.notAllowedToShowBlock?.add(dismiss)
        }
    }

    //青少年 事件
    fun teenApply(dialog: DialogRouteIntent? = null) {
        if (dialog != null) {
            listDialogs[teen] = dialog
        }
        LogUtil.d(TAG, "listApply = $listApply")
        if (listApply[update] == 2) {
            if (listDialogs[teen] != null&&(!isShowDialog)) {
                listDialogs[teen]?.start()
                isShowDialog = true
                val dismiss: () -> Unit = {
                    listApply[teen] = 2
                    listDialogs[teen] = null
                    isShowDialog = false
                    LogUtil.d(TAG, "listDialogs = $listDialogs")
                    if(listApply[invite] == 1 && listDialogs[invite] !=null){
                        inviteApply()
                    } else if((listDialogs[GUIDE] != null)){
                        LogUtil.d(TAG, "执行guideApply")
                        guideApply()
                    }else if (getTopPage()?.split("/")?.contains("home") == true) {
                        push2Apply()
                    }else if(listDialogs[theater] != null){
                        theaterApply()
                    }else if((listDialogs[SIGN_IN] != null)){
                        signInApply()
                    }
                }
                listDialogs[teen]?.addDismissListener(dismiss)
                listDialogs[teen]?.notAllowedToShowBlock?.add(dismiss)
            } else {
                if(listApply[invite]==1){
                    inviteApply()
                } else if (getTopPage()?.split("/")?.contains("home") == true) {
                    push2Apply()
                }else{
                    theaterApply()
                }
            }
        }
    }

    //引导事件
    fun guideApply(dialog: DialogRouteIntent? = null) {
        if (dialog != null) {
            listDialogs[GUIDE] = dialog
        }
        if (listApply[update] == 2 && listApply[teen] == 2) {
            if (listDialogs[GUIDE] != null&&(!isShowDialog)) {
                listDialogs[GUIDE]?.start()
                isShowDialog = true
                val dismiss: () -> Unit = {
                    listApply[GUIDE] = 2
                    listDialogs[GUIDE] = null
                    isShowDialog = false
                    teenDialogDelayShow()
                }
                listDialogs[GUIDE]?.addDismissListener (dismiss)
                listDialogs[GUIDE]?.notAllowedToShowBlock?.add(dismiss)
            }
        }else {
            if(listApply[teen]==1){
                inviteApply()
            } else if (getTopPage()?.split("/")?.contains("home") == true) {
                push2Apply()
            }else{
                theaterApply()
            }
        }
    }

    //邀请奖励 事件
    //这里Toast也可以消费，所以加进去事件处理里
    fun inviteApply(
        dialog: DialogRouteIntent? = null,
        showToast: Boolean? = false,
        str: String? = ""
    ) {
        if (dialog != null) {
            listDialogs[invite] = dialog
        }
        if(showToast==true&&(!str.isNullOrBlank())){
            inviteToast = str
            inviteShowToast = showToast
        }
        if (listApply[teen] == 2 && listApply[update] == 2) {
            if (inviteShowToast) {
                ToastManager.showToast(inviteToast)
                listApply[invite] = 2
                inviteToast = ""
                inviteShowToast = false
            } else {
                if (listDialogs[invite] != null&&(!isShowDialog)) {
                    listDialogs[invite]?.start()
                    isShowDialog = true
                    listDialogs[invite]?.addDismissListener {
                        listApply[invite] = 2
                        listDialogs[invite] = null
                        isShowDialog = false
                        teenDialogDelayShow()
                    }
                } else if (getTopPage()?.split("/")?.contains("home") == true) {
                    push2Apply()
                }
            }
        }
    }

    //剧场渠道 事件
    fun theaterApply(dialog: DialogRouteIntent? = null) {
        if (dialog != null) {
            listDialogs[theater] = dialog
        }
        if (listApply[update] == 2 && listApply[teen] == 2) {
            if (listDialogs[theater] != null&&(!isShowDialog)) {
                listDialogs[theater]?.start()
                isShowDialog = true
                val dismiss: () -> Unit = {
                    listApply[theater] = 2
                    listDialogs[theater] = null
                    isShowDialog = false
                    teenDialogDelayShow()
                }
                listDialogs[theater]?.addDismissListener (dismiss)
                listDialogs[theater]?.notAllowedToShowBlock?.add(dismiss)
            }
        }
    }

    fun signInApply(dialog: DialogRouteIntent? = null) {
        if (dialog != null) {
            listDialogs[SIGN_IN] = dialog
        }
        if (listApply[update] == 2 && listApply[teen] == 2 && listApply[invite] == 2) {
            if (listDialogs[SIGN_IN] != null&&(!isShowDialog)) {
                listDialogs[SIGN_IN]?.start()
                isShowDialog = true
                val dismiss: () -> Unit = {
                    listApply[SIGN_IN] = 2
                    listDialogs[SIGN_IN] = null
                    isShowDialog = false
                    teenApply()
                }
                listDialogs[SIGN_IN]?.addDismissListener (dismiss)
                listDialogs[SIGN_IN]?.notAllowedToShowBlock?.add(dismiss)
            }
        }
    }

//    fun packetApply(dialog: DialogRouteIntent? = null) {
//        // 设置弹窗的对话框
//        dialog?.let {
//            listDialogs[PACKET] = it
//        }
//
//        // 确保 packetApply 具有最高优先级，首先检查所有优先级条件
//        if (listApply[PACKET] == 1) {
//            // 只在未展示弹窗时显示
//            if (listDialogs[PACKET] != null && !isShowDialog) {
//                listDialogs[PACKET]?.start()
//                isShowDialog = true
//                val dismiss: () -> Unit = {
//                    listApply[PACKET] = 2
//                    listDialogs[PACKET] = null
//                    isShowDialog = false
//                    rewardApply()
//                }
//                listDialogs[PACKET]?.addDismissListener(dismiss)
//                listDialogs[PACKET]?.notAllowedToShowBlock?.add(dismiss)
//            }
//        }
//    }



    // reward 事件
    fun rewardApply(dialog: DialogRouteIntent? = null) {
        if (dialog != null) {
            listDialogs[REWARD] = dialog
        }
        if (listApply[PACKET] == 2 && listApply[update] == 2 && listApply[teen] == 2) {
            if (listDialogs[REWARD] != null && !isShowDialog) {
                listDialogs[REWARD]?.start()
                isShowDialog = true
                val dismiss: () -> Unit = {
                    listApply[REWARD] = 2
                    listDialogs[REWARD] = null
                    isShowDialog = false
                    signInApply()
                }
                listDialogs[REWARD]?.addDismissListener(dismiss)
                listDialogs[REWARD]?.notAllowedToShowBlock?.add(dismiss)
            }
        }
    }



    //青少年弹窗如果存在，先弹出青少年弹窗 不存在的话弹出签单弹窗
    private fun teenDialogDelayShow(){
        if(listDialogs[teen] !=null){
            teenApply()
        } else if (getTopPage()?.split("/")?.contains("home") == true) {
            push2Apply()
        }else if(listDialogs[SIGN_IN] !=null){
            signInApply()
        }else{
            guideApply()
        }
    }

    /**
     * 第二种类型的push开关
     * 展示位置：首页
     * 优先级：升级弹窗 > 青少年弹窗 > 邀请VIP弹窗 > 本弹窗
     */
    fun push2Apply(dialog: DialogRouteIntent? = null) {
        LogUtil.d("DialogPushGuide", "push2Apply")
//        LogUtil.d("DialogPushGuide", "listApply:$listApply")
        dialog?.let {
            listDialogs[PUSH_2] = it
        }
        if (listApply[update] == 2 && listApply[teen] == 2 && listApply[invite] == 2) {
            if (listApply[PUSH_2] == 2) {
                widgetApply()
                return
            }
            if (listDialogs[PUSH_2] != null && !isShowDialog) {
                listDialogs[PUSH_2]?.start()
                isShowDialog = true
                val dismiss: () -> Unit = {
                    listApply[PUSH_2] = 2
                    listDialogs[PUSH_2] = null
                    isShowDialog = false
                    if (getTopPage()?.split("/")?.contains("home") == true) {
                        widgetApply()
                    }
                }
                listDialogs[PUSH_2]?.addDismissListener(dismiss)
                listDialogs[PUSH_2]?.notAllowedToShowBlock?.add(dismiss)
            }
        }
    }

    fun widgetApply(dialog: DialogRouteIntent? = null) {
        if (dialog == null) {
            guideApply()
            return
        }
        dialog?.let {
            listDialogs[WIDGET] = it
        }
        if (listApply[update] == 2 && listApply[teen] == 2 && listApply[invite] == 2 &&
            listApply[PUSH_2] == 2 && listApply[WIDGET] == 1) {
            if (listDialogs[WIDGET] != null && !isShowDialog) {
                listDialogs[WIDGET]?.start()
                isShowDialog = true
                val dismiss: () -> Unit = {
                    listApply[WIDGET] = 2
                    listDialogs[WIDGET] = null
                    isShowDialog = false
                    guideApply()
                }
                listDialogs[WIDGET]?.addDismissListener(dismiss)
                listDialogs[WIDGET]?.notAllowedToShowBlock?.add(dismiss)
            }
        }
    }

    //冷启动重置状态
    fun reSet() {
        listApply.mapKeys { (key: String, _) ->
            listApply[key] = 0
        }
        listDialogs.mapKeys { (key: String, _) ->
            listDialogs[key] = null
        }
        inviteToast = ""
        inviteShowToast = false
        isShowDialog = false
    }

    private fun getTopPage(): String? {
        return (LocalActivityMgr.getTopActivity() as? UIPage)?.getPageId()
    }
}
