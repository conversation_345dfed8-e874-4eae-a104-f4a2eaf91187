package com.dz.platform.common.base.ui.component

import android.content.Context
import android.util.AttributeSet
import androidx.annotation.AttrRes
import androidx.databinding.ViewDataBinding
import com.dz.platform.common.base.ui.UIPage

abstract class PPageComponent<VB : ViewDataBinding> @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, @AttrRes defStyleAttr: Int = 0
) : UIConstraintComponent<VB, Any>(context, attrs, defStyleAttr), UIPage {

}
