package com.dz.platform.common.base.ui

import android.content.Context
import android.content.Intent
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.FrameLayout
import com.dz.platform.common.R
import com.dz.platform.common.base.ui.component.PPageComponent
import com.dz.platform.common.base.ui.dialog.PDialogComponent
import com.dz.platform.common.router.DialogRouteIntent
import com.gyf.immersionbar.BarHide
import java.lang.reflect.Constructor

class PBaseDialogActivity : PageComponentActivity() {
    private lateinit var page: PDialogComponent<*>
    override fun initBaseLogic() {
        val params: WindowManager.LayoutParams = window.attributes

        params.height = WindowManager.LayoutParams.MATCH_PARENT
        params.width = WindowManager.LayoutParams.MATCH_PARENT
        window.attributes = params
        setFinishOnTouchOutside(false)
    }

    override fun addPageComponent(componentClazzP: Class<PPageComponent<*>>) {
        val constructor: Constructor<*> = componentClazzP.getConstructor(
            Context::class.java
        )
        page = constructor.newInstance(this) as PDialogComponent<*>

        var routeIntent = getRouteIntent()
        if (routeIntent is DialogRouteIntent) {
            if (routeIntent.showNavigationBar) {
                getImmersionBar()
                    .hideBar(BarHide.FLAG_SHOW_BAR)
                    .init()
            }else{
                getImmersionBar()
                    .hideBar(BarHide.FLAG_HIDE_NAVIGATION_BAR)
                    .init()
            }
        }

        page.doInitByIntent(routeIntent)
        page.show()
    }

    override fun initImmersionBar() {
        getImmersionBar()
            .hideBar(BarHide.FLAG_HIDE_NAVIGATION_BAR)
            .init()
    }

    /**
     * 每个activity可以单独处理
     * 启动动画
     */
    override fun enterAnim(intent: Intent?) {
        overridePendingTransition(R.anim.common_ac_out_keep, R.anim.common_ac_out_keep)
    }

    /**
     * 结束动画
     */
    override fun exitAnim() {
        overridePendingTransition(R.anim.common_ac_out_keep, R.anim.common_ac_out_keep)
    }

}