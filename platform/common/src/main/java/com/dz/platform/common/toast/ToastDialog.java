package com.dz.platform.common.toast;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.content.ContextWrapper;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.dz.foundation.base.utils.LogUtil;
import com.dz.platform.common.R;


/**
 * dialog 实现 toast的功能 ，解决部分手机设备，不显示toast/或者需要开启通知权限才能显示toast的问题
 */
class ToastDialog extends Dialog {

    private final static int WHAT_DISMISS = 0;
    //自动消失
    private boolean autoDismiss = true;

    /**
     * 展示时是否清除焦点
     */
    private boolean clearFocus = true;
    private Handler handler = new Handler(Looper.getMainLooper()) {
        @Override
        public void handleMessage(@NonNull Message msg) {
            if (msg.what == WHAT_DISMISS) {
                if (toastMessage != null) {
                    toastMessage.setAutoDismiss();
                }
                dismiss();
            }
        }
    };

    public ToastDialog(@NonNull Context context) {
        super(context, R.style.common_dialog_toast);
    }

    public ToastDialog(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    protected ToastDialog(@NonNull Context context, boolean cancelable, @Nullable OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }


    @Override
    protected void onStart() {
        super.onStart();
        getWindow().setFlags(WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL, WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL);


    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setCancelable(false);
        Window window = getWindow();
        WindowManager.LayoutParams params = window.getAttributes();
        params.width = WindowManager.LayoutParams.MATCH_PARENT;
        params.height = WindowManager.LayoutParams.WRAP_CONTENT;
        params.gravity = Gravity.CENTER;
        window.setAttributes(params);
        setContentView(R.layout.common_toast_dialog);
        initView();
    }

    private ViewGroup rootView;
    private ImageView iv_top;
    private ImageView iv_left;
    private TextView text;

    private void initView() {
        rootView = findViewById(R.id.root);
        text = rootView.findViewById(R.id.text);
        iv_left = rootView.findViewById(R.id.iv_left);
        iv_top = rootView.findViewById(R.id.iv_top);
//        Window window = getWindow();
//        window.setWindowAnimations(R.style.common_toast_anim);
        bindData();
    }


    private void bindData() {
        if (text != null) {
            if (!TextUtils.isEmpty(toastMessage.getHotWords())) {
                text.setText(toastMessage.getHotWords());
            } else {
                text.setText(toastMessage.getMessage());
            }
        }

        // 应用ToastMessage中的Gravity设置
        if (toastMessage != null && getWindow() != null) {
            Window window = getWindow();
            WindowManager.LayoutParams params = window.getAttributes();
            params.gravity = toastMessage.getGravity();
            params.x = toastMessage.getXOffset();
            params.y = toastMessage.getYOffset();
            window.setAttributes(params);
        }
    }


    public ToastDialog setToastMessage(ToastMessage toastMessage, boolean clearFocus) {
        this.toastMessage = toastMessage;
        this.clearFocus = clearFocus;
        bindData();
        return this;
    }

    private ToastMessage toastMessage;

    public ToastMessage getToastMessage() {
        return toastMessage;
    }

    @Override
    public void show() {
        NavigationBarUtil.focusNotAle(getWindow());
        super.show();

        Animation animationIn = AnimationUtils.loadAnimation(getContext(), R.anim.common_toast_in);
        rootView.startAnimation(animationIn);
        NavigationBarUtil.immersive(getWindow(), getContainerActivity());
        if (clearFocus) {
            NavigationBarUtil.clearFocusNotAle(getWindow());
        }
        if (toastMessage == null) {
            return;
        }
        toastMessage.setStartShow();
        handler.removeMessages(WHAT_DISMISS);
        if (autoDismiss) {
            handler.sendEmptyMessageDelayed(WHAT_DISMISS, toastMessage.getRemainDuration());
        }
    }


    @Override
    public void onAttachedToWindow() {
        LogUtil.d("ToastAlert", "onAttachedToWindow ");
        super.onAttachedToWindow();
    }

    @Override
    public void onDetachedFromWindow() {
        LogUtil.d("ToastAlert", "onDetachedFromWindow ");
        super.onDetachedFromWindow();
        if (handler != null) {
            handler.removeMessages(WHAT_DISMISS);
            handler.removeCallbacks(null);
        }

    }

    @Override
    public void onBackPressed() {
        LogUtil.d("onBackPressed", "ToastDialog");
        Activity ownerActivity = getContainerActivity();
        if (ownerActivity != null) {
            LogUtil.d("onBackPressed", "ToastDialog ownerActivity:" + ownerActivity.toString());
            ownerActivity.onBackPressed();
        }
    }

    @Override
    public void dismiss() {
        try {
            // 解决 dialog java.lang.IllegalArgumentException: View=DecorView not attached to window manager  dismiss异常
            Activity containerActivity = getContainerActivity();
            if (containerActivity == null) {
                return;
            }
            if (containerActivity.isDestroyed()) {
                return;
            }
            if (!isShowing()) {
                return;
            }
            Animation animationOut = AnimationUtils.loadAnimation(getContext(), R.anim.common_toast_out);
            animationOut.setAnimationListener(new Animation.AnimationListener() {
                @Override
                public void onAnimationStart(Animation animation) {

                }

                @Override
                public void onAnimationEnd(Animation animation) {
                    realDismiss();
                }

                @Override
                public void onAnimationRepeat(Animation animation) {

                }
            });
            rootView.startAnimation(animationOut);

        } catch (Throwable throwable) {
            throwable.printStackTrace();
        }

    }

    private void realDismiss() {
        super.dismiss();
    }

    private Activity getContainerActivity() {

        Context context = getContext();
        if (context == null) {
            return null;
        }
        while (context instanceof ContextWrapper) {
            if (context instanceof Activity) {
                return (Activity) context;
            }
            context = ((ContextWrapper) context).getBaseContext();
        }

        return null;
    }

}
