package com.dz.platform.common.base.ui.dialog


import android.content.Context
import android.content.res.Configuration
import android.util.AttributeSet
import android.view.ViewGroup
import android.view.animation.Animation
import android.view.animation.AnimationUtils
import android.widget.FrameLayout
import androidx.core.content.ContextCompat
import androidx.databinding.ViewDataBinding
import com.dz.foundation.base.module.AppModule
import com.dz.foundation.base.utils.LogUtil
import com.dz.foundation.router.RouteIntent
import com.dz.foundation.ui.widget.getContainerActivity
import com.dz.platform.common.R
import com.dz.platform.common.base.ui.PBaseDialogActivity
import com.dz.platform.common.base.ui.component.PPageComponent


abstract class PDialogComponent<VB : ViewDataBinding> :
    PPageComponent<VB> {

    private var animatorEndTimes = 0
    val dialogSetting = DialogSetting()

    /**
     * 当前屏幕饭方向
     */
    var orientation = Configuration.ORIENTATION_UNDEFINED

    constructor(context: Context) : super(context) {
        setTag(R.id.common_container_tag, getUiTag())
        LogUtil.d("PDialogComponent", "constructor finish")
    }

    override fun doInit(context: Context, attrs: AttributeSet?, defStyleAtt: Int) {

    }

    /**
     * 当前dialog类型在当前activity中 最多允许的实例数
     */
    open fun maxInstanceSize(): Int {
        return 1
    }

    open fun doInitByIntent(routeIntent: RouteIntent?) {
        super.doInit(context, null, 0)
    }

    private fun getCancel(): Boolean {
        LogUtil.d("PDialogComponent", "getCancel")
        return false
    }

    protected open fun getEnterAnim(): Int {
        return R.anim.common_dialog_in
    }

    protected open fun getExitAnim(): Int {
        return R.anim.common_dialog_out
    }


    open fun show() {
        LogUtil.d("thisDialog", this::class.java.simpleName)

        getContainerActivity()?.let {
            if(!dialogSetting.canpropagation){
                mViewBinding.root.setOnClickListener {}
                this.setOnClickListener {
                    if (dialogSetting.cancelable) {
                        dismiss()
                    }
                }
            }
            val decorView = (it.window.decorView as FrameLayout)
            var dialogManager = decorView.getTag(R.id.common_dialog_manager_tag)
            if (dialogManager == null) {
                dialogManager = DialogManager(decorView)
                decorView.setTag(R.id.common_dialog_manager_tag, dialogManager)
            }
            (dialogManager as DialogManager).add(this)
            if (getEnterAnim() > 0) {
                executeEnterAnim()
            }
            onShow()
        }

    }

    /**
     * 静默关闭
     */
    fun silentExit() {
        if (dismissing) {
            return
        }
        dismissing = true
        doDismiss()
    }

    private var dismissing = false
    open fun dismiss() {
        if (dismissing) {
            return
        }
        dismissing = true
        if (getExitAnim() > 0) {
            executeExitAnim()
        } else {
            doDismiss()
        }
    }

    private fun executeEnterAnim() {
        val animation = AnimationUtils.loadAnimation(context, getEnterAnim())
        animation.setAnimationListener(object : Animation.AnimationListener {
            override fun onAnimationStart(animation: Animation) {

            }

            override fun onAnimationEnd(animation: Animation) {

            }

            override fun onAnimationRepeat(animation: Animation) {

            }
        })
        LogUtil.d("是否有多个弹窗", "show ${hasMoreDialog()}")

        if (!hasMoreDialog()) {
            (parent as ViewGroup).startAnimation(
                AnimationUtils.loadAnimation(
                    context,
                    R.anim.common_alpha_in
                )
            )
        }

        startAnimation(animation)
    }

    private fun executeExitAnim() {
        val animation = AnimationUtils.loadAnimation(context, getExitAnim())
        animation.setAnimationListener(object : Animation.AnimationListener {
            override fun onAnimationStart(animation: Animation) {

            }

            override fun onAnimationEnd(animation: Animation) {
                doDismissAfterAnimator()

            }


            override fun onAnimationRepeat(animation: Animation) {

            }
        })
        val animationBg = AnimationUtils.loadAnimation(context, R.anim.common_alpha_out)
        //保证背景淡出动画时间，与dialog退出动画时间一致
        animationBg.duration = animation.duration
        animationBg.setAnimationListener(object : Animation.AnimationListener {
            override fun onAnimationStart(animation: Animation) {

            }

            override fun onAnimationEnd(animation: Animation) {
                doDismissAfterAnimator()

            }


            override fun onAnimationRepeat(animation: Animation) {

            }
        })
        startAnimation(animation)
        LogUtil.d("是否有多个弹窗", "dismiss ${hasMoreDialog()}")
        if (!hasMoreDialog()) {
            kotlin.runCatching {
                (parent as? ViewGroup)?.startAnimation(animationBg) ?: silentExit()
            }
        }
    }

    private fun doDismissAfterAnimator() {
        animatorEndTimes++
        if (animatorEndTimes == if (hasMoreDialog()) 1 else 2) {
            animatorEndTimes = 0
            doDismiss()
        }
    }

    private fun doDismiss() {
        getContainerActivity()?.let {
            val decorView = (it.window.decorView as FrameLayout)
            val managerTag = decorView.getTag(R.id.common_dialog_manager_tag)
            if (managerTag != null && (managerTag is DialogManager) && managerTag.size() > 0) {
                managerTag.remove(this)
            }

            if (it is PBaseDialogActivity) {
                it.finish()
            }
        }
        onDismiss()
    }

    //当前窗口包含弹窗个数超过1个，超过1个情况，背景不要执行透明度动画
    private fun hasMoreDialog(): Boolean {
        getContainerActivity()?.let {
            val decorView = (it.window.decorView as FrameLayout)
            val managerTag = decorView.getTag(R.id.common_dialog_manager_tag)
            return managerTag != null && (managerTag is DialogManager) && managerTag.size() > 1
        }
        return false
    }

    open fun onBackPress() {
        if (dialogSetting.cancelable) {
            dismiss()
        }
    }

    class DialogSetting {
        var backgroundColor =
            ContextCompat.getColor(AppModule.getApplication(), R.color.common_75_000000_60_000000)
        var cancelable = true
        var canpropagation = false  //增加一个新参数用于判断点击是否透传到下层，应用场景：弹窗需要能点击tabBar
        var elevation: Float? = null  //  显示层级

    }

    private var dismissListenerBlock: (() -> Unit)? = null
    fun setOnDismissListener(block: () -> Unit) {
        dismissListenerBlock = block
    }

    protected open fun onDismiss() {
        dismissListenerBlock?.invoke()
    }

    private var showListenerBlock: ((dialogComp: PDialogComponent<*>) -> Unit)? = null
    fun setOnShowListener(block: (dialogComp: PDialogComponent<*>) -> Unit) {
        showListenerBlock = block
    }

    private fun onShow() {
        showListenerBlock?.invoke(this)
    }

    private var hasFocus = false

    override fun onWindowFocusChanged(hasWindowFocus: Boolean) {
        super.onWindowFocusChanged(hasWindowFocus)
        if (!hasFocus && hasWindowFocus) {
            getContainerActivity()?.let {
                val decorView = (it.window.decorView as FrameLayout)
                val managerTag = decorView.getTag(R.id.common_dialog_manager_tag)
                if (managerTag != null && (managerTag is DialogManager) && managerTag.size() > 0) {
                    managerTag.resetLayoutParams(this@PDialogComponent)
                }
            }
        }
        hasFocus = hasWindowFocus

    }
}

