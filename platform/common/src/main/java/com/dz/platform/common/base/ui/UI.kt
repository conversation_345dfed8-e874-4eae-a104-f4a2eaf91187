package com.dz.platform.common.base.ui

import android.app.Activity
import android.view.View
import androidx.activity.ComponentActivity
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ProcessLifecycleOwner
import com.dz.foundation.base.utils.LogUtil
import com.dz.foundation.base.utils.UIIdentify
import com.dz.foundation.event.DzEventManager
import com.dz.foundation.ui.utils.click.ClickEventHandler
import com.dz.foundation.ui.widget.getContainerActivity
import com.dz.platform.common.base.ui.component.UIComponent

/**
 *@Author: shidz
 *@Date: 2022/9/1 17:34
 *@Description: UI 声明接口  包括 UIPage 页面 UIBlock 块
 *@Version:1.0
 */


interface UI : UIIdentify {


    fun getActivityPageId(): String {
        return when (this) {
            is Activity -> getUiId()
            is Fragment -> this.activity?.let { UIIdentify.getObjId(it) } ?: ""
            is View -> this.getContainerActivity()?.let { UIIdentify.getObjId(it) } ?: ""
            else -> ""
        }
    }


    /**
     * 注册 点击 事件
     *
     * @param views
     */
    private fun registerClickView(view: View, action: (v: View) -> Unit) {
        return getClickEventHandler().registerClickView(-1, view, action)
    }

    fun getClickEventHandler(): ClickEventHandler {
        return getLazyFiledOrPut("mClickEventHandler") { ClickEventHandler.newInstance() }
    }


    val mLazyFiledMap: MutableMap<String, Any>
    private fun <T : Any> getLazyFiledOrPut(tag: String, initBlock: () -> T): T {
        val value = mLazyFiledMap.getOrPut(tag) {
            initBlock()
        }
        return value as T
    }

    fun getUILifecycleOwner(): LifecycleOwner? {
        return when (this) {
            is ComponentActivity -> this
            is Fragment -> this
            is UIComponent<*, *> -> {
                getContainerFragment() ?: (this as View).getFragmentActivity()
            }
            else -> null
        }
    }

    fun <T : View> T.registerClickAction(clickAction: (view: View) -> Unit) {
        registerClickView(this, clickAction)
    }

    fun <T : View> T.registerClickAction(intervalMills: Long, clickAction: (view: View) -> Unit) {
        getClickEventHandler().registerClickView(intervalMills, this, clickAction)
    }

    fun View.getFragmentActivity(): FragmentActivity? {
        getContainerActivity().let {
            if (it is FragmentActivity) {
                return it
            }
        }
        return null
    }

    fun subscribe() {
        val lifecycleOwner = getUILifecycleOwner()
        if (lifecycleOwner != null) {
            LogUtil.d(
                "UI",
                "subscribe,lifecycleOwner=${lifecycleOwner},组件: ${this.javaClass.simpleName}"
            )
            subscribeObserver(lifecycleOwner)
            subscribeEvent(lifecycleOwner, getUiId())
        } else {
            // 降级方案：当无法获取LifecycleOwner时，使用ProcessLifecycleOwner
            // 这主要针对FlutterContainerActivity等非ComponentActivity的情况
            val processLifecycleOwner = ProcessLifecycleOwner.get()
            LogUtil.d(
                "UI",
                "subscribe,lifecycleOwner=ProcessLifecycleOwner，组件: ${this.javaClass.simpleName}"
            )
            subscribeObserver(processLifecycleOwner)
            subscribeEvent(processLifecycleOwner, getUiId())
        }
    }

    fun unsubscribe() {
        unSubscribeEvent(getUiId())
    }

    fun subscribeObserver(lifecycleOwner: LifecycleOwner) {}
    fun subscribeEvent(lifecycleOwner: LifecycleOwner, lifecycleTag: String) {}

    private fun unSubscribeEvent(tag: String) {
        DzEventManager.removeObservers(tag)
    }
}



