package com.dz.platform.common.base.ui.dialog

import com.dz.platform.common.router.DialogRouteIntent
import java.util.PriorityQueue

/**
 *@Author: zhanggy
 *@Date: 2023-11-27
 *@Description: Dialog Z轴显示层级控制器
 *@Version:1.0
 */
class DialogLayerManager(private val page: String = "default") {

    companion object{
        const val TAG = "dialog_layer"
    }

    private val targetDialog = mutableListOf<Int>()

    private val comparator =
        Comparator<DialogRouteIntent> { dialog1, dialog2 ->
            (dialog2?.getPriorityId() ?: 0) - (dialog1?.getPriorityId() ?: 0)
        }

    private val preparedDialog = PriorityQueue(11, comparator)

    fun addDialogId(vararg dialogIds: Int) {
        dialogIds.forEach { id ->
            targetDialog.add(id)
        }
        tryToShow()
    }

    fun removeDialogId(dialogId: Int) {
        targetDialog.remove(dialogId)
        tryToShow()
    }

    fun showDialog(dialog: DialogRouteIntent) {
//        LogUtil.d(TAG, "$page add dialog:${dialog::class.java.simpleName}")
        preparedDialog.add(dialog)
        tryToShow()
    }

    private fun tryToShow() {
        if (preparedDialog.size >= targetDialog.size) {
            preparedDialog.forEach {
//                LogUtil.d(TAG, "$page show dialog:${it::class.java.simpleName}")
                it.start()
            }
            preparedDialog.clear()
        }
    }
}