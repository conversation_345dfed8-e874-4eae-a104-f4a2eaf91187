package com.dz.platform.common.router

import androidx.annotation.IntDef
import com.dz.foundation.router.RouteIntent
import com.dz.platform.common.base.ui.dialog.PDialogComponent

const val VIEW = 0
const val ACTIVITY = 1

@IntDef(VIEW, ACTIVITY)
@Target(AnnotationTarget.FIELD, AnnotationTarget.VALUE_PARAMETER)
@Retention(AnnotationRetention.SOURCE)
annotation class MODE
open class DialogRouteIntent : RouteIntent() {

    /**
     * 用于优先级排序，数值越小优先级越高
     */
    open fun getPriorityId(): Int = Int.MAX_VALUE


    override fun getEnterAnim(): Int {
        return 0
    }

    override fun getExitAnim(): Int {
        return 0
    }

    @MODE
    open var mode = VIEW
    open var activityPageId: String? = null
    //是否显示导航栏
    open var showNavigationBar = false

    var dismissCallbackBlock: (() -> Unit)? = null

    var beforeShowCallbackBlock: (() -> Boolean)? = null

    var showCallbackBlock: ((dialogComp: PDialogComponent<*>) -> Unit)? = null

    val dismissListeners = mutableListOf<(() -> Unit)>()

    val showListeners = mutableListOf<((dialogComp: PDialogComponent<*>) -> Unit)>()

    var notAllowedToShowBlock = mutableListOf<(() -> Unit)>()

    fun beforeShow(): Boolean {
        return beforeShowCallbackBlock?.invoke() ?: true
    }
}

fun <T : DialogRouteIntent> T.onDismiss(block: () -> Unit): T {
    this.dismissCallbackBlock = block
    return this
}

fun <T : DialogRouteIntent> T.onShow(block: (dialogComp: PDialogComponent<*>) -> Unit): T {
    this.showCallbackBlock = block
    return this
}

fun <T : DialogRouteIntent> T.addDismissListener(block: () -> Unit): T {
    this.dismissListeners.add(block)
    return this
}

fun <T : DialogRouteIntent> T.addShowListener(block: (dialogComp: PDialogComponent<*>) -> Unit): T {
    this.showListeners.add(block)
    return this
}
