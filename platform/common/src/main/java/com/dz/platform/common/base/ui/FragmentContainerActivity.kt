package com.dz.platform.common.base.ui

import android.view.View
import com.dz.foundation.router.DzRouterManager
import com.dz.foundation.router.RouteIntent
import com.dz.platform.common.router.NavigateUtil

class FragmentContainerActivity : PBaseActivity() {
    override fun maxInstanceSize(): Int {
        return Int.MAX_VALUE
    }

    override fun initBaseLogic() {
    }

    override fun initImmersionBar() {
    }

    override fun initBaseView() {
    }

    override fun loadView() {

    }

    override fun initData() {

    }

    override fun initView() {
        try {
            intent.extras?.let {
                val currentFragmentClazz: Class<out PBaseFragment?>? =
                    it.getSerializable(NavigateUtil.FRAGMENT_CLAZZ) as Class<PBaseFragment?>?
                currentFragmentClazz?.run { addFragment(this) }
            }

        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun addFragment(clazz: Class<out PBaseFragment>) {
        val fragment = clazz.newInstance()

        fragment.arguments = intent.extras
        val transaction = supportFragmentManager
            .beginTransaction()
        transaction.add(android.R.id.content, fragment, fragment.javaClass.name)
        transaction.show(fragment)
        transaction.commitNowAllowingStateLoss()
    }

    override fun initListener() {

    }


    fun getRouteIntent(): RouteIntent {
        return DzRouterManager.getInstance().getRouteIntent(intent)
    }
}