package com.dz.platform.common.router;

import static com.dz.platform.common.router.DialogRouteIntentKt.VIEW;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.Application;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;

import com.dz.foundation.base.module.AppModule;
import com.dz.foundation.base.utils.LocalActivityMgr;
import com.dz.foundation.router.RouteIntent;
import com.dz.foundation.router.RouterIntentContainer;
import com.dz.platform.common.base.ui.FragmentContainerActivity;
import com.dz.platform.common.base.ui.PBaseDialogActivity;
import com.dz.platform.common.base.ui.PBaseFragment;
import com.dz.platform.common.base.ui.PageComponentActivity;
import com.dz.platform.common.base.ui.component.PPageComponent;
import com.dz.platform.common.base.ui.dialog.PDialogComponent;

import java.lang.reflect.Constructor;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;

public class NavigateUtil {
    private static final String TAG = "NavigateUtil";
    public final static String FRAGMENT_CLAZZ = "fragmentClazz";
    public final static String COMPONENT_CLAZZ = "componentClazz";


    public static void jumpToTarget(Class clazz, RouteIntent routeIntent) {

        if (Activity.class.isAssignableFrom(clazz)) {
            jumpToActivity(clazz, routeIntent, null);
        } else if ((PBaseFragment.class.isAssignableFrom(clazz))) {
            jumpToFragment(clazz, routeIntent);
        } else if (PDialogComponent.class.isAssignableFrom(clazz)) {
            jumpToDialog(clazz, routeIntent);
        } else if (PPageComponent.class.isAssignableFrom(clazz)) {
            jumpToComponent(clazz, routeIntent);
        } else {
            throw new IllegalArgumentException("Expected BaseActivity, BaseFragment, PageComponent"
                    + ", but is " + clazz.getName());
        }
    }

    private static void jumpToDialog(Class clazz, RouteIntent routeIntent) {
        try {
            if (routeIntent instanceof DialogRouteIntent && (((DialogRouteIntent) routeIntent).getMode() == VIEW)) {

                Constructor constructor = clazz.getConstructor(Context.class);
                Activity containerActivity = null;
                String activityPageId = ((DialogRouteIntent) routeIntent).getActivityPageId();
                if (!TextUtils.isEmpty(activityPageId)) {
                    containerActivity = LocalActivityMgr.INSTANCE.getActivityByUiId(activityPageId);
                }
                if (containerActivity == null) {
                    containerActivity = LocalActivityMgr.INSTANCE.getTopActivity();
                }
                Object dialogComp = constructor.newInstance(containerActivity);
                if (dialogComp instanceof PDialogComponent) {
                    PDialogComponent<?> dialog = (PDialogComponent<?>) dialogComp;
                    dialog.doInitByIntent(routeIntent);
                    if (((DialogRouteIntent) routeIntent).beforeShow()) {
                        dialog.show();
                    } else {
                        for (Function0<Unit> listener : ((DialogRouteIntent) routeIntent).getNotAllowedToShowBlock()) {
                            listener.invoke();
                        }
                    }
                }

            } else {
                jumpToComponent(clazz, routeIntent);
            }
        } catch (Exception e) {
            e.printStackTrace();
            routeIntent.onStartError(e);
        }

    }

    private static void jumpToFragment(Class fragmentClazz, RouteIntent routeIntent) {
        Bundle extras = new Bundle();
        extras.putSerializable(FRAGMENT_CLAZZ, fragmentClazz);
        jumpToActivity(FragmentContainerActivity.class, routeIntent, extras);
    }


    private static void jumpToComponent(Class<? extends PPageComponent> clazz, RouteIntent routeIntent) {
        Bundle extras = new Bundle();
        extras.putSerializable(COMPONENT_CLAZZ, clazz);
        if ((PDialogComponent.class.isAssignableFrom(clazz))) {
            jumpToActivity(PBaseDialogActivity.class, routeIntent, extras);
        } else {
            jumpToActivity(PageComponentActivity.class, routeIntent, extras);
        }

    }

    @SuppressLint("WrongConstant")
    private static void jumpToActivity(Class activityClass, RouteIntent routeIntent, Bundle extras) {
        Context context = LocalActivityMgr.INSTANCE.getTopActivity();
        if (context == null) {
            context = AppModule.INSTANCE.getApplication();
        }
        Intent intent = new Intent();
        intent.setClass(context, activityClass);
        if (context instanceof Application) {
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        } else {
            Integer flags = routeIntent.getIntentFlags();
            if (flags != null) {
                intent.setFlags(flags);
            }

        }

        String intent_id = RouterIntentContainer.getInstance().put(routeIntent);
        if (extras == null) {
            extras = new Bundle();
        }
        extras.putString(RouteIntent.INTENT_ID, intent_id);
        intent.putExtras(extras);
        if (routeIntent.getRequestCode() != null && context instanceof Activity) {
            ((Activity) context).startActivityForResult(intent, routeIntent.getRequestCode());
        } else {
            context.startActivity(intent);
        }

        if (context instanceof Activity) {
            if (routeIntent.getEnterAnim() != null) {
                ((Activity) context).overridePendingTransition(routeIntent.getEnterAnim(), routeIntent.getExitAnim());
            }
        }
    }
}
