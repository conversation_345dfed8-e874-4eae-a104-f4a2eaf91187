package com.dz.platform.common.toast;

import android.os.SystemClock;
import android.text.SpannableStringBuilder;
import android.view.Gravity;

public class ToastMessage {
    //图标在左侧样式
    public final static int STYLE_LEFT_ICON = 0;
    private final static long MIN_DURATION = 1500;

    //图标在上方样式
    public final static int STYLE_TOP_ICON = 1;
    private int style = STYLE_LEFT_ICON;
    private String message;
    private SpannableStringBuilder hotWords;
    //显示时长，单位毫秒

    private long duration = MIN_DURATION;

    // 添加Gravity相关属性
    private int gravity = Gravity.CENTER;
    private int xOffset = 0;
    private int yOffset = 0;

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public SpannableStringBuilder getHotWords() {
        return hotWords;
    }

    public void setHotWords(SpannableStringBuilder hotWords) {
        this.hotWords = hotWords;
    }

    /**
     * 获取改消息还能显示多少时间单位毫秒
     *
     * @return
     */
    public long getRemainDuration() {
        long elapsedRealtime = SystemClock.elapsedRealtime();
        if (startShowTime == 0) {
            startShowTime = SystemClock.elapsedRealtime();
        }
        long tokeTime = elapsedRealtime - startShowTime;

        long remainTime = duration - tokeTime;
        if (remainTime < 0) {
            remainTime = 0;
        }
        if (remainTime > duration) {
            remainTime = duration;
        }

        return remainTime;
    }

    public void setDuration(long duration) {
        this.duration = duration;
    }


    private boolean hasAutoDismiss;

    public void setAutoDismiss() {
        hasAutoDismiss = true;
    }

    public boolean isHasAutoDismiss() {
        return hasAutoDismiss;
    }

    private long startShowTime;

    public void setStartShow() {
        if (startShowTime == 0) {
            startShowTime = SystemClock.elapsedRealtime();
        }
    }

    public int getGravity() {
        return gravity;
    }

    public void setGravity(int gravity) {
        this.gravity = gravity;
    }

    public int getXOffset() {
        return xOffset;
    }

    public void setXOffset(int xOffset) {
        this.xOffset = xOffset;
    }

    public int getYOffset() {
        return yOffset;
    }

    public void setYOffset(int yOffset) {
        this.yOffset = yOffset;
    }

    // 便捷方法设置位置
    public void setPosition(int gravity, int xOffset, int yOffset) {
        this.gravity = gravity;
        this.xOffset = xOffset;
        this.yOffset = yOffset;
    }
}
