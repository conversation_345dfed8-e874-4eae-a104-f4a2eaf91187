package com.dz.platform.common.base.ui.component

import android.content.Context
import android.text.TextUtils
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.ViewDataBinding
import androidx.fragment.app.Fragment
import com.dz.foundation.base.utils.LogUtil
import com.dz.foundation.ui.view.recycler.DzRecyclerViewItem
import com.dz.platform.common.R
import com.dz.platform.common.base.ui.UI
import java.lang.reflect.Method
import java.lang.reflect.ParameterizedType

interface UIComponent<VB : ViewDataBinding, M : Any> : UI, DzRecyclerViewItem<M> {
    var mViewBinding: VB
    var mData: M?
    fun bindData(data: M?) {
        mData = data
    }

    override fun onBindRecyclerViewItem(model: M?, position: Int) {
        bindData(model)
    }

    fun doInit(context: Context, attrs: AttributeSet?, defStyleAtt: Int) {
        initAttrs(context, attrs, defStyleAtt)
        loadView()
        initData()
        initView()
        initListener()
    }

    fun initAttrs(context: Context?, attrs: AttributeSet?, defStyleAtt: Int) {}
    fun loadView() {
        LogUtil.d("渲染耗时","${this::class.java.simpleName} initViewBinding 前 真实时间 时间：${System.currentTimeMillis()}")
        mViewBinding = initViewBinding()
        LogUtil.d("渲染耗时","${this::class.java.simpleName} initViewBinding 后 真实时间 时间：${System.currentTimeMillis()}")
    }

    private fun initViewBinding(): VB {
        val superClass = javaClass.genericSuperclass
        val aClass = (superClass as ParameterizedType).actualTypeArguments[0] as Class<*>
        var result: VB
        thisView().run {
            val layoutInflater = LayoutInflater.from(context)
            var inflateMethod: Method = aClass.getDeclaredMethod(
                "inflate",
                LayoutInflater::class.java,
                ViewGroup::class.java,
                Boolean::class.java
            )
            result = inflateMethod.invoke(null, layoutInflater, this, true) as VB

        }
        return result
    }


    fun initData()
    fun initView()
    fun initListener()


    private fun thisView(): ViewGroup {
        return this as ViewGroup
    }

    //是否在fragment 中
    fun isInFragment(): Boolean {
        if (this is View) {
            val fragmentId = getFragmentId(thisView())
            if (!TextUtils.isEmpty(fragmentId)) {
                return true
            }
        }
        return false
    }


    private fun getFragmentId(view: View): String {

        var tag = view.getTag(R.id.common_fragment_id)
        if (tag == null && view.parent != null && view.parent is View) {
            tag = getFragmentId(view.parent as View)
        }
        if (tag != null) {
            return tag.toString()
        }
        return ""
    }

    fun getContainerFragment(): Fragment? {
        if (this is View) {
            return getViewContainerFragment(this)
        }
        return null
    }

    private fun getViewContainerFragment(view: View): Fragment? {

        var tag = view.getTag(R.id.common_fragment_instance)

        if (tag == null && view.parent != null && view.parent is View) {
            tag = getViewContainerFragment(view.parent as View)
        }
        if (tag != null && tag is Fragment) {
            LogUtil.d("isInFragment", " return: fragment$tag")
            return tag
        }

        return null

    }
}