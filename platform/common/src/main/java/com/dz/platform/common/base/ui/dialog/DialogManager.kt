package com.dz.platform.common.base.ui.dialog

import android.graphics.Color
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import com.dz.foundation.base.component.splash.SplashDisplayStateManager
import com.dz.foundation.base.utils.LogUtil

class DialogManager(private val decorView: FrameLayout) {
    private val dialogList = mutableListOf<PDialogComponent<*>>()
    // 添加需要显示在 Splash 上层的弹窗标记
    private val splashOverlayTags = setOf(
        DialogConstant.DIALOG_UI_TAG_PRIVACY_POLICY,      // 隐私政策弹窗标记
        DialogConstant.DIALOG_UI_TAG_PRIVACY_POLICY_HOLD  // 隐私政策确认弹窗标记
    )

    private fun getDialogByTag(uiTag: String): List<PDialogComponent<*>> {
        return dialogList.filter { uiTag == it.getUiTag() }
    }

    fun add(dialogComp: PDialogComponent<*>) {

        val dialogListByTag = getDialogByTag(dialogComp.getUiTag())
        dialogListByTag?.let { list ->
            if (list.size >= dialogComp.maxInstanceSize()) {
                LogUtil.d("DialogManager", "超过最大个数 关闭一个实例")
                remove(list[list.size - 1])
            }
        }

        if (dialogComp.parent != null) {
            (dialogComp.parent as ViewGroup).removeView(dialogComp)
        }
        // 判断是否为隐私协议弹窗
        val shouldShowOverSplash = splashOverlayTags.contains(dialogComp.getUiTag())
        LogUtil.d("DialogManager", "Dialog tag: ${dialogComp.getUiTag()}, shouldShowOverSplash: $shouldShowOverSplash,isSplashShowing=${SplashDisplayStateManager.isSplashShowing()}")

        val last = dialogList.lastOrNull()

        last?.parent?.let {
            if (it is ViewGroup) {
                it.setBackgroundColor(Color.parseColor("#00000000"))
            }
        }

        val layoutParams = createLayoutParams()
        val container = FrameLayout(decorView.context)
        val dialogContentLayoutParams = FrameLayout.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.MATCH_PARENT
        )
        container.addView(dialogComp, dialogContentLayoutParams)
        container.setBackgroundColor(dialogComp.dialogSetting.backgroundColor)

        // 只有非隐私协议弹窗才清除上一个弹窗的背景
        if (!SplashDisplayStateManager.isSplashShowing() && !shouldShowOverSplash) {
            val last = dialogList.lastOrNull()
            last?.parent?.let {
                if (it is ViewGroup) {
                    it.setBackgroundColor(Color.parseColor("#00000000"))
                }
            }
        }

        // 设置弹窗的 elevation
        container.elevation = if (shouldShowOverSplash) {
            Float.MAX_VALUE
        } else {
            dialogComp.dialogSetting.elevation ?: (Float.MAX_VALUE - 2)
        }

        // 非隐私政策弹窗，显示在 Splash 下层
        if (SplashDisplayStateManager.isSplashShowing() && !shouldShowOverSplash) {
            val insertIndex = (decorView.childCount - 1).coerceAtLeast(0)
            decorView.addView(container, insertIndex, layoutParams)
        } else {
            // 隐私政策弹窗或无 Splash 时，正常添加到顶层
            decorView.addView(container,layoutParams)
        }

        dialogList.add(dialogComp)


    }

    private fun createLayoutParams(): FrameLayout.LayoutParams {
        val contentView = decorView.findViewById<FrameLayout>(android.R.id.content)
        val contentLocationArr = IntArray(2)
        contentView.getLocationOnScreen(contentLocationArr)
        val contentBottom = contentView.height + contentLocationArr[1]
        val decorLocationArr = IntArray(2)
        decorView.getLocationOnScreen(decorLocationArr)
        val decorBottom = decorView.height + decorLocationArr[1]
        var top = contentLocationArr[1] - decorLocationArr[1]
        var bottom = decorBottom - contentBottom
        if (bottom < 0) bottom = 0

        val layoutParams = FrameLayout.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.MATCH_PARENT
        )
        layoutParams.topMargin = top
        layoutParams.bottomMargin = bottom
        return layoutParams
    }

    fun remove(dialogComp: PDialogComponent<*>) {
        dialogList.remove(dialogComp)
        decorView.post {
            decorView.removeView(dialogComp.parent as ViewGroup)
        }

        val last = dialogList.lastOrNull()
        last?.let { lastDialogComp ->
            lastDialogComp.parent?.let {
                if (it is ViewGroup) {
                    it.setBackgroundColor(lastDialogComp.dialogSetting.backgroundColor)
                }
            }
        }
    }

    fun onBackPress(): Boolean {
        LogUtil.d("DialogManager", "onBackPress $this")
        val last = dialogList.lastOrNull()
        if (last != null) {
            LogUtil.d("DialogManager", "onBackPress $this dialogView=$last")
            last.onBackPress()
            return true
        }
        return false
    }

    fun size(): Int {
        return dialogList.size
    }

    fun resetLayoutParams(dialogComp: PDialogComponent<*>) {
        (dialogComp.parent as FrameLayout).layoutParams = createLayoutParams()
    }

    /**
     * 暂存dialog显示状态。有些需要要求dialog全部隐藏，然后再回复之前状态。
     */
    private val dialogVisibility = mutableMapOf<View, Int>()

    private var hasHideAllDialog = false

    fun hideAllDialog() {
        if (hasHideAllDialog) return
        dialogList.forEach { dialog ->
            dialogVisibility[dialog] = dialog.visibility
            dialog.visibility = View.GONE
            (dialog.parent as? ViewGroup)?.setBackgroundColor(Color.TRANSPARENT)
        }
        hasHideAllDialog = true
    }

    fun resumeDialog() {
        if (!hasHideAllDialog) return
        dialogList.forEach { dialog ->
            dialog.visibility = dialogVisibility[dialog]?: View.GONE
            (dialog.parent as? ViewGroup)?.setBackgroundColor(dialog.dialogSetting.backgroundColor)
        }
        hasHideAllDialog = false
    }

    /**
     * 关闭所有弹窗
     */
    fun closeAll() {
        LogUtil.d("DialogManager", "关闭所有弹窗")
        dialogVisibility.clear()
        kotlin.runCatching {
            // 创建一个新的列表来存储所有对话框，避免并发修改异常
            val dialogsToRemove = ArrayList(dialogList)
            dialogList.clear() // 清空原始列表
            for (dialog in dialogsToRemove) {
                dialog.silentExit()
            }
        }
        hasHideAllDialog = false
    }
}