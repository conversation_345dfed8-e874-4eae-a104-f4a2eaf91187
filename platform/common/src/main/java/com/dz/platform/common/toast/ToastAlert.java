package com.dz.platform.common.toast;

import android.app.Activity;
import android.app.Application;
import android.content.DialogInterface;
import android.os.Build;
import android.os.Bundle;
import android.os.Looper;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.dz.foundation.base.module.AppModule;
import com.dz.foundation.base.utils.LocalActivityMgr;
import com.dz.foundation.base.utils.LogUtil;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Set;


/**
 * @Description: 实现 自定义 toast
 * @Author: shidz
 * @CreateDate: 2021/3/13 19:49
 */
class ToastAlert {
    private static class SingletonHolder {
        private static ToastAlert instance = new ToastAlert();
    }

    public static ToastAlert getInstance() {
        return SingletonHolder.instance;
    }


    private HashMap<String, ToastDialog> dialogContainer = new HashMap<>();


    private ToastAlert() {

        Application application = AppModule.INSTANCE.getApplication();
        if (application != null) {
            application.registerActivityLifecycleCallbacks(new Application.ActivityLifecycleCallbacks() {
                @Override
                public void onActivityCreated(@NonNull Activity activity, @Nullable Bundle savedInstanceState) {

                }

                @Override
                public void onActivityStarted(@NonNull Activity activity) {

                }

                @Override
                public void onActivityResumed(@NonNull Activity activity) {
//                    checkAndShowMessage(activity);
                }

                @Override
                public void onActivityPaused(@NonNull Activity activity) {

                }

                @Override
                public void onActivityStopped(@NonNull Activity activity) {
                    dismissToastDialog(getActivityUIId(activity));
                }

                @Override
                public void onActivitySaveInstanceState(@NonNull Activity activity, @NonNull Bundle outState) {

                }

                @Override
                public void onActivityDestroyed(@NonNull Activity activity) {

                }
            });
        }

    }

    private void checkAndShowMessage(Activity activity) {
        LogUtil.d("ToastAlert", "checkAndShowMessage activity:" + activity);
        show(activity, mToastMessage);
    }


    private ToastMessage mToastMessage;
    private boolean mAllowClear = true;

    /**
     * 展示时是否清除焦点
     */
    private boolean clearFocus = true;

    public synchronized ToastAlert setMessage(ToastMessage toastMessage, boolean allowClear, boolean clearFocus) {
        mToastMessage = toastMessage;
        mAllowClear = allowClear;
        this.clearFocus = clearFocus;
        return this;
    }

    /**
     * 设置位置
     * @param gravity
     * @param xOffset
     * @param yOffset
     * @return
     */
    public synchronized ToastAlert setGravity(int gravity, int xOffset, int yOffset) {
        if (mToastMessage != null) {
            mToastMessage.setPosition(gravity, xOffset, yOffset);
        }
        return this;
    }


    private DialogInterface.OnDismissListener onDismissListener = new DialogInterface.OnDismissListener() {

        @Override
        public void onDismiss(DialogInterface dialog) {
            mAllowClear = true;
        }
    };

    public synchronized void show() {
        Activity topActivity = LocalActivityMgr.INSTANCE.getTopActivity();
        show(topActivity, mToastMessage);
    }

    private synchronized void show(Activity activity, ToastMessage toastMessage) {
        LogUtil.d("ToastAlert", "show size:" + dialogContainer.size());
        if (toastMessage == null) {
            return;
        }
        long remainDuration = toastMessage.getRemainDuration();
        LogUtil.d("ToastAlert", "show remainDuration:" + remainDuration);
        if (remainDuration <= 0) {
            mToastMessage = null;
            return;
        }
        if (!isMainThread()) {
            activity.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    doShowOnMainThread(activity, toastMessage);
                }
            });
        } else {
            doShowOnMainThread(activity, toastMessage);
        }
    }

    private void doShowOnMainThread(Activity activity, ToastMessage toastMessage) {
        LogUtil.d("ToastAlert", "doShowOnMainThread:toastMessage:" + toastMessage.getMessage());

        if (activity == null) {
            return;
        }
        LogUtil.d("ToastAlert", "doShowOnMainThread:topActivity:" + activity.getClass().getName());
        if (activity.isDestroyed()) {
            LogUtil.d("ToastAlert", "doShowOnMainThread:topActivity:" + activity.getClass().getName() + " isDestroyed");
            return;
        }
        if (activity.isFinishing()) {
            LogUtil.d("ToastAlert", "doShowOnMainThread:topActivity:" + activity.getClass().getName() + " isFinishing");
            return;
        }
        // 画中画模式
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N && activity.isInPictureInPictureMode()) {
            LogUtil.d("ToastAlert", "doShowOnMainThread:topActivity:" + activity.getClass().getName() + " is in pip");
            return;
        }

        String activityUIId = getActivityUIId(activity);
        LogUtil.d("ToastAlert", "doShowOnMainThread:activityUIId:" + activityUIId);
        dismissOtherDialog(activityUIId);
        ToastDialog toastDialog = dialogContainer.get(activityUIId);
        LogUtil.d("ToastAlert", "doShowOnMainThread:dialogContainer:" + dialogContainer.toString());

        if (toastDialog == null) {
            toastDialog = createToastDialog(activity);
            dialogContainer.put(activityUIId, toastDialog);
        }
        toastDialog.setToastMessage(toastMessage, clearFocus);
        toastDialog.show();


    }

    private void dismissOtherDialog(String activityUIId) {
        LogUtil.d("ToastAlert", "dismissOtherDialog:" + activityUIId);
        if (dialogContainer == null) {
            return;
        }
        if (dialogContainer.size() == 0) {
            return;
        }

        Set<String> keySet = dialogContainer.keySet();
        Iterator<String> iterator = keySet.iterator();
        while (iterator.hasNext()) {
            String key = iterator.next();
            if (!TextUtils.equals(key, activityUIId)) {
                dismissToastDialog(key);
            }

        }

    }

    private void dismissToastDialog(String activityUIId) {
        try {
            LogUtil.d("ToastAlert", "dismissToastDialog:" + activityUIId + "  dialogContainer=" + dialogContainer.toString());
            if (dialogContainer == null) {
                return;
            }
            if (!dialogContainer.containsKey(activityUIId)) {
                return;
            }
            ToastDialog toastDialog = dialogContainer.get(activityUIId);
            if (toastDialog != null) {
                toastDialog.dismiss();
                dialogContainer.remove(activityUIId);
                LogUtil.d("ToastAlert", "dismissToastDialog: remove " + activityUIId);
            }
        } catch (Exception e) {

        }

    }

    public void clearToast() {
        LogUtil.d("UPDATE_APP_TAG","mAllowClear=="+mAllowClear);
        if (!mAllowClear) {
            return;
        }
        Activity topActivity = LocalActivityMgr.INSTANCE.getTopActivity();
        if (topActivity != null) {
            String activityUIId = getActivityUIId(topActivity);
            dismissToastDialog(activityUIId);
            dismissOtherDialog(activityUIId);
            if (mToastMessage != null) {
                mToastMessage.setDuration(0);
                mToastMessage = null;
            }
        }
    }

    private ToastDialog createToastDialog(Activity topActivity) {
        ToastDialog toastDialog = new ToastDialog(topActivity);
        toastDialog.setOnDismissListener(onDismissListener);
        return toastDialog;
    }

    private boolean isMainThread() {
        return Looper.myLooper() == Looper.getMainLooper();
    }

    private String getActivityUIId(Activity obj) {
        return "Activity_" + obj.getClass().getName() + "@" + Integer.toHexString(obj.hashCode());
    }
}
