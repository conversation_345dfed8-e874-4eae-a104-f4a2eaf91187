package com.dz.platform.common.base.ui.component

import android.content.Context
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import androidx.annotation.AttrRes
import androidx.annotation.ColorRes
import androidx.annotation.DrawableRes
import androidx.core.content.ContextCompat
import androidx.databinding.ViewDataBinding
import com.dz.foundation.ui.widget.DzConstraintLayout


abstract class UIConstraintComponent<VB : ViewDataBinding, M : Any> : DzConstraintLayout,
    UIComponent<VB, M> {

    override lateinit var mViewBinding: VB

    override var mData: M? = null
    override val mLazyFiledMap: MutableMap<String, Any> = mutableMapOf()

    @JvmOverloads
    constructor(
        context: Context,
        attrs: AttributeSet? = null,
        @AttrRes defStyleAttr: Int = 0
    ) : super(context, attrs, defStyleAttr) {
        doInit(context, attrs, defStyleAttr)
    }

    override fun onAttachedToWindow() {
        subscribe()
        super.onAttachedToWindow()
    }

    override fun onDetachedFromWindow() {
        unsubscribe()
        super.onDetachedFromWindow()
    }

    final override fun subscribe() {
        super.subscribe()
    }

    final override fun unsubscribe() {
        super.unsubscribe()
    }

    fun getColor(@ColorRes id: Int): Int {
        return ContextCompat.getColor(context, id)
    }

    fun getDrawable(@DrawableRes id: Int): Drawable? {
        return ContextCompat.getDrawable(context, id)
    }
}

