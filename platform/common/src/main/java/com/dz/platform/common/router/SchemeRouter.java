package com.dz.platform.common.router;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;

import com.blankj.utilcode.util.GsonUtils;
import com.dz.foundation.base.module.AppModule;
import com.dz.foundation.base.utils.LogUtil;
import com.dz.foundation.router.DzRouterManager;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Set;

public class SchemeRouter {
    private static final String TAG = "SchemeRouter";

    public static String buildDeepLink(String action) {
        return buildDeepLink(action, "");
    }

    public static String buildDeepLink(String action, HashMap<String, Object> params) {
        String jsonParam = "";
        if (params != null && params.size() > 0) {
            //map 转成json
            jsonParam = GsonUtils.toJson(params);
            try {
                jsonParam = URLEncoder.encode(jsonParam, "utf-8");
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
        }

        return buildDeepLink(action, jsonParam);
    }

    public static String buildDeepLink(String action, String jsonParam) {
        return "dz://android?action=" + action + "&param=" + jsonParam;
    }

    public static String getActionFromDeepLink(String deepLink) {
        String result = Uri.parse(deepLink).getQueryParameter("action");
        if (result == null) {
            return "";
        } else {
            return result;
        }
    }


    // dz://com.ebook.sun?page=bookDetail&param={'bookId':'1200399'}
    public static void doUriJump(Uri uri) {
        UriInfo schemeInfo = parseUri(uri);
        doJump(schemeInfo);

    }

    public static void doUriJump(String uri) {
        if (TextUtils.isEmpty(uri)) {
            return;
        }
        doUriJump(Uri.parse(uri));
    }


    private static void doJump(UriInfo uriInfo) {
        switch (uriInfo.goTo) {
            case UriInfo.WEBVIEW:
                jumpToWebView(uriInfo.uri, false);
                break;
            case UriInfo.BROWSER:
                jumpToBrowser(uriInfo.uri);
                break;
            case UriInfo.NATIVE:
                jumpToNativePage(uriInfo);
                break;
            case UriInfo.OTHER_APP:
                jumpToOtherApp(uriInfo.uri);
                break;
            default:
                break;
        }


    }

    private static void jumpToOtherApp(String url) {
        try {
            Uri uri = Uri.parse(url);
            Intent stoneIntent = new Intent();
            stoneIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            stoneIntent.setAction(Intent.ACTION_VIEW);
            stoneIntent.setData(uri);
            AppModule.INSTANCE.getApplication().startActivity(stoneIntent);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    private static void jumpToBrowser(String linkUrl) {
        Uri uri = Uri.parse(linkUrl);
        Intent intent = new Intent(Intent.ACTION_VIEW, uri);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        AppModule.INSTANCE.getApplication().startActivity(intent);
    }

    private static void jumpToWebView(String linkUrl, boolean isAddParam) {
        LogUtil.Companion.d(TAG, "jumpToWebView url:" + linkUrl);
        //转换为内部浏览器打开
        HashMap<String, Object> paramMap = new HashMap<>();
        paramMap.put("url", linkUrl);
        String webDeepLink = buildDeepLink("web", paramMap);
        doUriJump(webDeepLink);
    }

    private static void jumpToNativePage(UriInfo schemeInfo) {
        LogUtil.d("deeplink", "jumpToNativePage " + schemeInfo);
        DzRouterManager.getInstance()
                .build(schemeInfo.action)
                .withJsonParams(schemeInfo.jsonParam)
                .setRouterType(schemeInfo.routerType)
                .setForceLogin(schemeInfo.forceLogin)
                .setPageName(schemeInfo.pageName)
                .setParams(schemeInfo.extras)
                .start();

    }


    /**
     * http://www.java2s.com:8080/yourpath/fileName.htm?stove=10&path=32&id=4#harvic
     * getScheme() :获取Uri中的scheme字符串部分，在这里即，http
     * getSchemeSpecificPart():获取Uri中的scheme-specific-part:部分，这里是：//www.java2s.com:8080/yourpath/fileName.htm?
     * getFragment():获取Uri中的Fragment部分，即harvic
     * getAuthority():获取Uri中Authority部分，即www.java2s.com:8080
     * getPath():获取Uri中path部分，即/yourpath/fileName.htm
     * getQuery():获取Uri中的query部分，即stove=10&path=32&id=4
     * getHost():获取Authority中的Host字符串，即www.java2s.com
     * getPost():获取Authority中的Port字符串，即8080
     * 另外还有两个常用的：getPathSegments()、getQueryParameter(String key)
     * List< String> getPathSegments():上面我们的getPath()是把path部分整个获取下来：/yourpath/fileName.htm，getPathSegments()的作用就是依次提取出Path的各个部分的字符串，以字符串数组的形式输出。以上面的Uri为例：
     * getQueryParameter(String key):在上面我们通过getQuery()获取整个query字段：stove=10&path=32&id=4，getQueryParameter(String key)作用就是通过传进去path中某个Key的字符串，返回他对应的值。
     * 针对某一个KEY不对它赋值是允许的，但在利用getQueryParameter()获取该KEY对应的值时，获取到的是null;
     * <p>
     * <p>
     * iwin://com.xskj?param={json}
     *
     * @param uriStr
     */

    public static UriInfo parseUri(String uriStr) {
        try {
            Uri uri = Uri.parse(uriStr);
            return parseUri(uri);
        } catch (Exception e) {

        }
        return null;
    }

    private static ArrayList<String> hostList;

    private static ArrayList<String> getSupportHostList() {
        if (hostList == null) {
            hostList = new ArrayList<>();
            hostList.add("android");
            hostList.add("app.client");
            hostList.add(AppModule.INSTANCE.getPackageName());
            hostList.add("android.family");
            hostList.add("app.client");
        }

        return hostList;
    }

    public static UriInfo parseUri(Uri uri) {


        String uiClassName = null;
        String scheme = uri.getScheme();
        String host = uri.getAuthority();
        String path = uri.getPath();
        UriInfo uriInfo = new UriInfo();
        uriInfo.scheme = scheme;
        if ((TextUtils.equals("dz", scheme) || TextUtils.equals("hmjc", scheme))
                && getSupportHostList().contains(host)) {
            Bundle bundle = parseExtras(uri);
            LogUtil.d("deeplink", "SchemeRouter parseUri bundle:" + bundle.toString());
            if (bundle != null) {
                // dz://com.ebook.sun?action=bookDetail&param={'bookId':'1200399'}
                try {
                    String page = bundle.getString("action", "");
                    String param = bundle.getString("param", "");
                    String routerType = bundle.getString("routerType", "-1");
                    uriInfo.setParam(UriInfo.NATIVE, page, param);
                    uriInfo.routerType = Integer.parseInt(routerType);

                    String forceLogin = bundle.getString("forceLogin", "0");
                    uriInfo.forceLogin = Integer.parseInt(forceLogin);

                    uriInfo.pageName = bundle.getString("pageName", "");

                    uriInfo.uri = uri.toString();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

        } else if (TextUtils.equals("http", scheme) || TextUtils.equals("https", scheme)) {
            //http https  跳转内部网页
            uriInfo.goTo = UriInfo.WEBVIEW;
            uriInfo.uri = uri.toString();
        } else {
            uriInfo.goTo = UriInfo.OTHER_APP;
            uriInfo.uri = uri.toString();
        }

        LogUtil.d("deeplink", "SchemeRouter parseUri result:" + uriInfo);
        return uriInfo;
    }

    private static Bundle parseExtras(Uri uri) {
        LogUtil.Companion.d(TAG, "parseExtras:" + uri.toString());
        Bundle extras = null;
        Set<String> queryParameterNames = uri.getQueryParameterNames();
        Iterator<String> iterator = queryParameterNames.iterator();
        while (iterator.hasNext()) {
            String key = iterator.next();
            String value = uri.getQueryParameter(key);//得到的是 url解码后的字符
            if (extras == null) {
                extras = new Bundle();
            }
            extras.putString(key, value);
        }

        return extras;
    }


    public static class UriInfo implements Serializable {

        public static final String WEBVIEW = "webView";  //访问内部webView
        public static final String BROWSER = "browser";  //访问系统浏览器
        public static final String NATIVE = "native";  //打开app内部页面
        public static final String OTHER_APP = "other_app";//跳转打开别家app

        public String uri;

        private String goTo = "";
        public String action = "";
        public String scheme;
        public int routerType = -1;
        public String jsonParam;
        public Bundle extras;
        public int forceLogin = 0;
        public String pageName = "";

        public Bundle getExtras() {
            return extras;
        }

        public void setParam(String goTo, String action, String jsonParam) {
            try {
                this.goTo = goTo;
                this.action = action;
                this.jsonParam = jsonParam;
                this.extras = parseAppPageParam(jsonParam);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        private Bundle parseAppPageParam(String jsonParam) {
            Bundle bundle = new Bundle();
            putBundleFromJson(bundle, jsonParam);
            return bundle;
        }

        public static void putBundleFromJson(Bundle bundle, String jsonParam) {
            try {
                if (TextUtils.isEmpty(jsonParam)) {
                    return;
                }
                JSONObject jsonObject = new JSONObject(jsonParam);
                JSONArray names = jsonObject.names();
                if (names != null) {
                    int length = names.length();
                    for (int i = 0; i < length; i++) {
                        String key = names.get(i).toString();
                        Object value = jsonObject.opt(key);
                        if (value != null) {
                            if (value instanceof Boolean) {
                                bundle.putBoolean(key, (Boolean) value);
                            } else if (value instanceof Integer) {
                                bundle.putInt(key, (Integer) value);
                            } else if (value instanceof Long) {
                                bundle.putLong(key, (Long) value);
                            } else if (value instanceof Double) {
                                bundle.putDouble(key, (Double) value);
                            } else if (value instanceof String) {
                                bundle.putString(key, (String) value);
                            } else {
                                bundle.putString(key, value.toString());
                            }
                        }
                    }
                }
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }

        public String getGoTo() {
            return goTo;
        }


        public String getAction() {
            return action;
        }


        public String getScheme() {
            return scheme;
        }

        public void appendParam(String key, Object value) {
            try {
                JSONObject jsonObject = new JSONObject(jsonParam);
                if (!jsonObject.has(key)) {
                    jsonObject.put(key, value);
                }
                jsonParam = jsonObject.toString();
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }

        public String toDeepLink() {
            return SchemeRouter.buildDeepLink(action, jsonParam);
        }

        @Override
        public String toString() {
            return "UriInfo{" +
                    "uri='" + uri + '\'' +
                    ", goTo='" + goTo + '\'' +
                    ", action='" + action + '\'' +
                    ", scheme='" + scheme + '\'' +
                    ", routerType=" + routerType + '\'' +
                    ", forceLogin=" + forceLogin + '\'' +
                    ", pageName=" + pageName + '\'' +
                    ", jsonParam='" + jsonParam + '\'' +
                    ", extras=" + extras +
                    '}';
        }
    }

}
