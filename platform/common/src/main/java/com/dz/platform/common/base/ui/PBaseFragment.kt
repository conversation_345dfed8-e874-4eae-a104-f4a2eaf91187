package com.dz.platform.common.base.ui

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import com.dz.foundation.base.utils.LogUtil
import com.dz.foundation.event.DzEventManager
import com.dz.foundation.network.DzNetWorkManager
import com.dz.platform.common.R


/**
 *@Description: 平台activity 基类
 *@Author: stone
 *@Date: 2022/8/21 19:19
 *
 *@Version:1.0
 */
abstract class PBaseFragment : Fragment(), UIPage {

    protected var rootView: View? = null
    override val mLazyFiledMap: MutableMap<String, Any> = mutableMapOf()
    protected var firstLoadFlag = true
    protected var everyLoadEnable = true

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        LogUtil.d("HomeDataRepository", "PBaseFragment onCreate ,")
        doInit()
    }




    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        // 缓存的rootView需要判断是否已经被加过parent，如果有parent需要从parent删除，要不然会发生这个rootview已经有parent的错误。
        rootView?.let {
            it.parent
        }?.let {
            it as ViewGroup
        }?.let {
            it.removeView(rootView)
        }

        rootView?.let {
            if (it.parent != null) {
                (it.parent as ViewGroup).removeView(it)
            }
            it.setTag(R.id.common_fragment_id, getUiId())
            it.setTag(R.id.common_fragment_instance, this)
            it.setTag(R.id.common_container_tag, getUiTag())
        }

        return rootView
    }

    open fun doInit() {
        initBaseLogic()
        initBaseView()
        loadView()
        initData()
        initView()
        initListener()
        subscribe()
    }


    /**
     * 初始化基础功能
     */
    abstract fun initBaseLogic()

    /**
     * 初始化沉浸式状态栏
     */
    abstract fun initImmersionBar()

    /**
     * 初始化基类 view
     */
    abstract fun initBaseView()

    /**
     * 加载页面内容
     */
    abstract fun loadView()

    /**
     * 初始化数据
     */
    abstract fun initData()

    /**
     * 初始化 view
     */

    abstract fun initView()


    /**
     * 初始化监听
     */
    abstract fun initListener()

    /**
     * 可见时候加载,只执行1次
     */
    open fun onLazyLoad() {}

    /**
     * 每次可见都加载
     */
    open fun onEveryLoad() {}

    /**
     * 刷新当前fragment
     */
    open fun refreshFragment() {}

    override fun onResume() {
        LogUtil.d("PBaseFragment", "onResume${getUiTag()}")
        super.onResume()

        initImmersionBar()
        if (firstLoadFlag) {
            firstLoadFlag = false
            onLazyLoad()
        }
        if (everyLoadEnable) {
            onEveryLoad()
        }
    }

    override fun onPause() {
        LogUtil.d("PBaseFragment", "onPause${getUiTag()}")
        super.onPause()
    }


    final override fun subscribe() {
        super.subscribe()
    }

    final override fun unsubscribe() {
        super.unsubscribe()
    }

    override fun onDestroy() {
        super.onDestroy()
        LogUtil.d("ColdLaunch", "${javaClass.simpleName} onDestroy")
        unsubscribe()
        recycle()
    }

    /**
     * 回收资源
     */
    private fun recycle() {
        LogUtil.d("ColdLaunch", "${javaClass.simpleName} recycle")
        recycleRes()
    }

    protected open fun recycleRes() {
        //回收界面绑定的事件监听网络请求等
        DzEventManager.removeObservers(this)
        DzNetWorkManager.cancel(getUiId())
    }
}