package com.dz.platform.common.base.ui

import android.app.Activity
import android.content.Intent
import android.content.pm.ActivityInfo
import android.content.res.TypedArray
import android.os.Build
import android.os.Bundle
import android.os.SystemClock
import android.text.TextUtils
import android.view.Window
import android.widget.FrameLayout
import androidx.annotation.ColorRes
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import com.dz.foundation.base.utils.AppActiveManager
import com.dz.foundation.base.utils.LocalActivityMgr
import com.dz.foundation.base.utils.LogUtil
import com.dz.foundation.event.DzEventManager
import com.dz.foundation.network.DzNetWorkManager
import com.dz.foundation.router.RouteIntent
import com.dz.foundation.router.RouterIntentContainer
import com.dz.foundation.ui.utils.click.ClickEventHandler
import com.dz.foundation.ui.utils.click.OnClickListenerWrapper
import com.dz.platform.common.R
import com.dz.platform.common.base.ui.dialog.DialogManager
import com.gyf.immersionbar.ImmersionBar

/**
 *@Description: 平台activity 基类
 *@Author: stone
 *@Date: 2022/8/21 19:19
 *
 *@Version:1.0
 */
abstract class PBaseActivity : AppCompatActivity(), UIPage {
    override val mLazyFiledMap: MutableMap<String, Any> = mutableMapOf()
    override fun onCreate(savedInstanceState: Bundle?) {
        LogUtil.d("StartUp", "PBaseActivity onCreate: ${this.javaClass.simpleName}")
        LogUtil.d("thisPage", this::class.java.simpleName)
        checkFixAndroid8OrientationBug()
        LogUtil.d("StartUp", "PBaseActivity onCreate super: ${this.javaClass.simpleName}")
        super.onCreate(savedInstanceState)
        LogUtil.d("StartUp", "PBaseActivity onCreate super end: ${this.javaClass.simpleName}")
        checkMaxInstanceToFinishFirst()
        //取消标题
        supportRequestWindowFeature(Window.FEATURE_NO_TITLE)
        setTag()
        val intercept: Boolean = beforeInit()
        if (intercept) {
            return
        }
        doInit()
        LogUtil.d("StartUp", "PBaseActivity onCreate end: ${this.javaClass.simpleName}")
    }

    protected open fun beforeInit(): Boolean {
        return false
    }

    private fun setTag() {
        var contentView = findViewById<FrameLayout>(android.R.id.content)
        contentView.setTag(R.id.common_container_tag, getUiTag())
    }

    /**
     * 检查当前类型的activity 所允许存在的最多activity 页面数 若超过则关闭最早加入栈中的activity
     */
    private fun checkMaxInstanceToFinishFirst() {
        val activityList = LocalActivityMgr.getActivityByTag(getUiTag())
        val maxSize = maxInstanceSize()
        LogUtil.d(
            getUiTag(),
            getUiTag() + "  checkMaxInstanceToFinishFirst ${activityList.size} maxInstanceSize${maxSize}  "
        )
        if (activityList.size > maxSize) {
            var firstActivity = activityList[0]
            LogUtil.d(getUiTag(), getUiTag() + " finishActivity $firstActivity")
            firstActivity.finish()

        }
    }

    private fun checkFixAndroid8OrientationBug() {
        if (Build.VERSION.SDK_INT == Build.VERSION_CODES.O && isTranslucentOrFloating()) {
            //解决 target 升级到28 透明主题 在Android 8.0上出现的崩溃问题
            //Only fullscreen activities can request orientation
            fixOrientation()
        }
    }

    //判断是否试透明主题
    private fun isTranslucentOrFloating(): Boolean {
        var isTranslucentOrFloating = false
        try {
            val styleableRes = Class.forName("com.android.internal.R\$styleable")
                .getField("Window")[null] as IntArray
            val ta = obtainStyledAttributes(styleableRes)
            val m =
                ActivityInfo::class.java.getMethod(
                    "isTranslucentOrFloating",
                    TypedArray::class.java
                )
            m.isAccessible = true
            isTranslucentOrFloating = m.invoke(null, ta) as Boolean
            m.isAccessible = false
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return isTranslucentOrFloating
    }

    //更改 屏幕方向取值为 ActivityInfo.SCREEN_ORIENTATION_UNSPECIFIED
    private fun fixOrientation(): Boolean {
        try {
            val field = Activity::class.java.getDeclaredField("mActivityInfo")
            field.isAccessible = true
            val o = field[this] as ActivityInfo
            o.screenOrientation = ActivityInfo.SCREEN_ORIENTATION_UNSPECIFIED
            field.isAccessible = false
            return true
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return false
    }

     fun doInit() {
        LogUtil.d("StartUp", "PBaseActivity doInit1: ${this.javaClass.simpleName}")
        initBaseLogic()
        LogUtil.d("StartUp", "PBaseActivity doInit2: ${this.javaClass.simpleName}")
        initImmersionBar()
        LogUtil.d("StartUp", "PBaseActivity doInit3: ${this.javaClass.simpleName}")
        initBaseView()//耗时方法
        LogUtil.d("StartUp", "PBaseActivity doInit4: ${this.javaClass.simpleName}")
        loadView()
        LogUtil.d("StartUp", "PBaseActivity doInit5: ${this.javaClass.simpleName}")
        initData()//耗时方法
        LogUtil.d("StartUp", "PBaseActivity doInit6: ${this.javaClass.simpleName}")
        initView()
        LogUtil.d("StartUp", "PBaseActivity doInit7: ${this.javaClass.simpleName}")
        initListener()
        LogUtil.d("StartUp", "PBaseActivity doInit8: ${this.javaClass.simpleName}")
        subscribe()
        LogUtil.d("StartUp", "PBaseActivity doInit9: ${this.javaClass.simpleName}")
    }


    override fun startActivity(intent: Intent?) {
        super.startActivity(intent)
        enterAnim(intent)
    }

    override fun startActivity(intent: Intent?, options: Bundle?) {
        super.startActivity(intent, options)
        enterAnim(intent)
    }

    override fun startActivityForResult(intent: Intent, requestCode: Int, options: Bundle?) {
        super.startActivityForResult(intent, requestCode, options)
        enterAnim(intent)
    }

    override fun finish() {
        super.finish()
        exitAnim()
        recycle()
    }

    override fun onDestroy() {
        recycle()
        super.onDestroy()
    }

    final override fun subscribe() {
        super.subscribe()
    }

    final override fun unsubscribe() {
        super.unsubscribe()
    }

    /**
     * 当前类型的activity 最多允许的实例数
     */
    open fun maxInstanceSize(): Int {
        return 1
    }

    companion object {
        const val NAME_MAIN_ACTIVITY = "com.dz.business.main.ui.MainActivity"
        const val NAME_PAY_CORE_ACTIVITY = "com.dz.platform.pay.paycore.ui.PayCoreActivity"
        const val NAME_FLUTTER_DIALOG_ACTIVITY =
            "com.dz.platform.common.base.ui.PBaseDialogActivity"
        const val NAME_SCORE_ACTIVITY = "com.dz.business.detail.ui.page.VideoScoreActivity"
    }

    //进入动画
    protected open fun enterAnim(intent: Intent?) {
        val className = intent?.component?.className
        className?.also {
            if (listOf(
                    NAME_MAIN_ACTIVITY,
                    NAME_PAY_CORE_ACTIVITY,
                    NAME_FLUTTER_DIALOG_ACTIVITY,
                    NAME_SCORE_ACTIVITY
                ).contains(className)
            ) {
                overridePendingTransition(0, 0)
            } else {
                overridePendingTransition(
                    R.anim.common_ac_in_from_right,
                    R.anim.common_ac_out_from_left
                )
            }
        } ?: overridePendingTransition(
            R.anim.common_ac_in_from_right,
            R.anim.common_ac_out_from_left
        )
    }

    //退出动画
    protected open fun exitAnim() {
        overridePendingTransition(R.anim.common_ac_in_from_left, R.anim.common_ac_out_from_right)
    }


    /**
     * 初始化基础功能
     */
    abstract fun initBaseLogic()

    /**
     * 初始化沉浸式状态栏
     */
    protected abstract fun initImmersionBar()

    private lateinit var immersionBar: ImmersionBar
    fun getImmersionBar(): ImmersionBar {
        if (!::immersionBar.isInitialized) {
            immersionBar = ImmersionBar.with(this)
        }
        return immersionBar
    }

    /**
     * 初始化基类 view
     */
    abstract fun initBaseView()

    /**
     * 加载页面内容
     */
    abstract fun loadView()

    /**
     * 初始化 view
     */

    abstract fun initView()

    /**
     * 初始化数据
     * TODO 这个需要是异步获取数据，否则会影响到订阅，能否把订阅操作提前
     */
    abstract fun initData()

    /**
     * 初始化监听
     */
    abstract fun initListener()
    final override fun onBackPressed() {

        //屏蔽返回键和页面功能按钮连续点击
        val currentTimeMillis = SystemClock.elapsedRealtime()
        val globalDistance = currentTimeMillis - OnClickListenerWrapper.lastGlobalClickTime
        if (globalDistance < ClickEventHandler.GLOBAL_CLICK_INTERVAL_MILLS && OnClickListenerWrapper.lastClickViewId != R.id.common_back_pressed_id) {
            return
        }
        OnClickListenerWrapper.lastClickViewId = R.id.common_back_pressed_id
        OnClickListenerWrapper.lastGlobalClickTime = currentTimeMillis

        var intercept = false
        getDialogManager()?.let {
            intercept = it.onBackPress()
        }

        if (intercept) {
            return
        }
        onBackPressAction()

    }

    open fun onBackPressAction() {
        super.onBackPressed()
    }

    fun getDialogManager(): DialogManager? {
        val tag = window.decorView.getTag(R.id.common_dialog_manager_tag)
        if (tag != null && tag is DialogManager) {
            return tag
        }
        return null
    }

    /**
     * 回收释放资源
     */
    private var hasRecycle: Boolean = false

    /**
     * 回收资源
     */
    private fun recycle() {
        if (hasRecycle) {
            return
        }
        hasRecycle = true
        recycleRes()
    }

    protected open fun recycleRes() {
        unsubscribe()
        LocalActivityMgr.removeActivity(this)
        if (intent != null) {
            val routeIntentId = intent.getStringExtra(RouteIntent.INTENT_ID)
            RouterIntentContainer.getInstance().remove(routeIntentId)
        }
        //回收界面绑定的事件监听网络请求等
        DzEventManager.removeObservers(this)
        DzNetWorkManager.cancel(getUiId())
        AppActiveManager.removeListener(getUiId())
    }

    fun getColorByCompat(@ColorRes id: Int): Int {
        return ContextCompat.getColor(
            this,
            id
        )
    }

}