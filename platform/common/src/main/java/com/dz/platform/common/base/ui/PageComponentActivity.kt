package com.dz.platform.common.base.ui

import android.content.Context
import android.view.ViewGroup
import android.widget.FrameLayout
import com.dz.foundation.router.DzRouterManager
import com.dz.foundation.router.RouteIntent
import com.dz.platform.common.base.ui.component.PPageComponent
import com.dz.platform.common.router.NavigateUtil
import java.lang.reflect.Constructor


open class PageComponentActivity : PBaseActivity() {
    override fun maxInstanceSize(): Int {
        return 1
    }

    override fun initBaseLogic() {
    }

    override fun initImmersionBar() {
    }

    override fun initBaseView() {
    }

    override fun loadView() {

    }

    override fun initData() {

    }

    override fun getUiTag(): String {

        return super.getUiTag() + "_" + getPageComponentClazz()?.name
    }

    private fun getPageComponentClazz(): Class<PPageComponent<*>>? {
        return intent.extras?.let {
            it.getSerializable(NavigateUtil.COMPONENT_CLAZZ) as Class<PPageComponent<*>>?
        }
    }

    override fun initView() {
        try {
            getPageComponentClazz()?.let {
                addPageComponent(it)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    open fun addPageComponent(componentClazzP: Class<PPageComponent<*>>) {
        val constructor: Constructor<*> = componentClazzP.getConstructor(
            Context::class.java
        )
        val page = constructor.newInstance(this) as PPageComponent<*>
        val layoutParams = FrameLayout.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.MATCH_PARENT
        )
        setContentView(page, layoutParams)
    }

    override fun initListener() {

    }


    fun getRouteIntent(): RouteIntent? {
        return DzRouterManager.getInstance().getRouteIntent(intent)
    }
}