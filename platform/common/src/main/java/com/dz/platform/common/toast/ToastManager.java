package com.dz.platform.common.toast;

import android.app.Activity;
import android.content.Context;
import android.os.Looper;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.StringRes;

import com.dz.foundation.base.module.AppModule;
import com.dz.foundation.base.utils.LocalActivityMgr;
import com.dz.foundation.base.utils.LogUtil;
import com.dz.foundation.base.utils.ScreenUtil;
import com.dz.platform.common.R;

/**
 * @Description: toast 管理工具类
 * @Author: shidz
 * @CreateDate: 2021/3/15 11:42
 */
public class ToastManager {

    public static void showDebugToast(String msg){
        if (LogUtil.Companion.isDebugMode()){
            showToast(msg);
        }
    }

    /**
     * 显示短时吐司
     *
     * @param text 文本
     */
    public static void showToast(CharSequence text) {
        if (TextUtils.isEmpty(text)) {
            return;
        }
        Activity topActivity = LocalActivityMgr.INSTANCE.getTopActivity();
        if (topActivity == null || topActivity.isFinishing() || topActivity.isDestroyed()) {
            return;
        }
        //  showSystemToast(text);

        showCustomToast(text);


    }

    /**
     * 显示短时吐司
     *
     * @param text 文本
     */
    public static void showToast(SpannableStringBuilder text) {
        if (TextUtils.isEmpty(text)) {
            return;
        }
        showCustomToast(text);
    }

    /**
     * 显示短时吐司
     *
     * @param text 文本
     */
    public static void showToastNotAllowClear(CharSequence text) {
        if (TextUtils.isEmpty(text)) {
            return;
        }
        //  showSystemToast(text);

        showCustomToastNotAllowClear(text);


    }

    /**
     * 显示短时吐司
     *
     * @param text 文本
     */
    public static void showToast(CharSequence text, long duration) {
        if (TextUtils.isEmpty(text)) {
            return;
        }
        //  showSystemToast(text);

        showCustomToast(text, duration, true);


    }

    /**
     * 显示短时吐司
     *
     * @param resId 资源Id
     */
    public static void showToast(@StringRes int resId) {
        showToast(AppModule.INSTANCE.getResources().getString(resId));
    }

    /**
     * 显示短时吐司
     *
     * @param resId 资源Id
     */
    public static void showToast(@StringRes int resId, long duration) {
        showToast(AppModule.INSTANCE.getResources().getString(resId), duration);
    }

    /**
     * 显示Toast
     * 为解决当软键盘弹起时，再弹出Toast，软键盘会自动关闭问题。
     * @param text
     * @param clearFocus 是否要清除获取焦点的View。传false，就不会关闭软键盘
     */
    public static void showToast(CharSequence text, boolean clearFocus) {
        if (TextUtils.isEmpty(text)) {
            return;
        }
        showCustomToast(text, 1500, true, clearFocus);
    }

    /**
     * 增加全局关闭toast的方法
     */
    public static void dismissToast() {
        ToastAlert.getInstance().clearToast();
    }


    /**
     * 显示自定义toast 使用特殊dialog 实现 解决部分手机上弹不出的问题
     *
     * @param text
     */
    private static void showCustomToastNotAllowClear(CharSequence text) {
        showCustomToast(text, 1500, false);
    }

    /**
     * 显示自定义toast 使用特殊dialog 实现 解决部分手机上弹不出的问题
     *
     * @param text
     */
    private static void showCustomToast(CharSequence text) {
        showCustomToast(text, 1500, true);
    }

    /**
     * 显示自定义toast 使用特殊dialog 实现 解决部分手机上弹不出的问题
     *
     * @param text
     */
    private static void showCustomToast(SpannableStringBuilder text) {
        showCustomToast(text, 1500, true);
    }

    /**
     * 显示自定义toast 使用特殊dialog 实现 解决部分手机上弹不出的问题
     *
     * @param text
     */
    private static void showCustomToast(SpannableStringBuilder text, long duration, boolean allowClear) {
        ToastMessage toastMessage = new ToastMessage();
        toastMessage.setHotWords(text);
        toastMessage.setDuration(duration);
        ToastAlert.getInstance().setMessage(toastMessage, allowClear, true).show();
    }

    /**
     * 显示自定义toast 使用特殊dialog 实现 解决部分手机上弹不出的问题
     *
     * @param text
     */
    private static void showCustomToast(CharSequence text, long duration, boolean allowClear) {
        ToastMessage toastMessage = new ToastMessage();
        toastMessage.setMessage(text.toString());
        toastMessage.setDuration(duration);
        ToastAlert.getInstance().setMessage(toastMessage, allowClear, true).show();
    }

    private static void showCustomToast(CharSequence text, long duration, boolean allowClear, boolean clearFocus) {
        ToastMessage toastMessage = new ToastMessage();
        toastMessage.setMessage(text.toString());
        toastMessage.setDuration(duration);
        ToastAlert.getInstance()
                .setMessage(toastMessage, allowClear, clearFocus)
                .show();
    }

    /**
     * 显示系统 toast 部分手机 toast 弹不出或者需要打开通知权限后才能弹出
     *
     * @param text
     */
    public static void showSystemToast(CharSequence text) {
        if (isMainThread()) {
            Toast toast = toast(text);
            if (toast != null) {
                toast.show();
            }
        } else {
            Activity topActivity = LocalActivityMgr.INSTANCE.getTopActivity();
            if (topActivity != null) {
                topActivity.runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        Toast toast = toast(text);
                        if (toast != null) {
                            toast.show();
                        }
                    }
                });
            }
        }
    }

    /**
     * 显示短时吐司 不会收起键盘
     *
     * @param text 文本
     */

    private static Toast mToast2;
    public static void showToast2(Context context, CharSequence text) {
        if (TextUtils.isEmpty(text)) {
            return;
        }
        try {
            View toastView = LayoutInflater.from(context).inflate(R.layout.common_toast_dialog, null);
            TextView textView = toastView.findViewById(R.id.text);
            textView.setText(text);
            if (mToast2 != null) {
                mToast2.cancel();
            }
            mToast2 = new Toast(context);
            mToast2.setView(toastView);//android 11 后此方法废弃
            mToast2.setDuration(Toast.LENGTH_SHORT);
            mToast2.setGravity(Gravity.CENTER, 0, 0);
            mToast2.show();
        } catch (Throwable throwable) {
            LogUtil.e("ToastManager", "showToast2 throwable = " + throwable);
        }

    }

    /**
     * 显示自定义位置的Toast
     *
     * @param text 文本
     * @param gravity 位置
     * @param xOffset X偏移量
     * @param yOffset Y偏移量
     */
    public static void showToastWithGravity(CharSequence text, int gravity, int xOffset, int yOffset) {
        if (TextUtils.isEmpty(text)) {
            return;
        }
        showCustomToastWithGravity(text, 1500, true, gravity, xOffset, yOffset);
    }

    /**
     * 显示自定义位置和时长的Toast
     *
     * @param text 文本
     * @param duration 持续时间
     * @param gravity 位置
     * @param xOffset X偏移量
     * @param yOffset Y偏移量
     */
    public static void showToastWithGravity(CharSequence text, long duration, int gravity, int xOffset, int yOffset) {
        if (TextUtils.isEmpty(text)) {
            return;
        }
        showCustomToastWithGravity(text, duration, true, gravity, xOffset, yOffset);
    }

    /**
     * 在底部显示Toast
     *
     * @param text 文本
     */
    public static void showBottomToast(CharSequence text) {
        if (TextUtils.isEmpty(text)) {
            return;
        }
        showCustomToastWithGravity(text, 1500, true, Gravity.BOTTOM | Gravity.CENTER_HORIZONTAL, 0, 100);
    }

    /**
     * 在顶部显示Toast
     *
     * @param text 文本
     */
    public static void showTopToast(Context context, CharSequence text) {
        if (TextUtils.isEmpty(text)) {
            return;
        }
        int offset = ScreenUtil.Companion.dip2px(context, 92);
        showCustomToastWithGravity(text, 1500, true, Gravity.TOP | Gravity.CENTER_HORIZONTAL, 0, offset);
    }

    private static void showCustomToastWithGravity(CharSequence text, long duration, boolean allowClear,
                                                   int gravity, int xOffset, int yOffset) {
        ToastMessage toastMessage = new ToastMessage();
        toastMessage.setMessage(text.toString());
        toastMessage.setDuration(duration);
        toastMessage.setPosition(gravity, xOffset, yOffset);

        ToastAlert.getInstance()
                .setMessage(toastMessage, allowClear, true)
                .show();
    }

    private static Toast mToast;

    private ToastManager() {

    }


    private static Toast toast(CharSequence text) {

        synchronized (ToastManager.class) {
            makeToast(text);
        }

        return mToast;
    }

    private static void makeToast(CharSequence text) {
        if (mToast != null) {
            mToast.cancel();
            mToast = null;
        }
        mToast = Toast.makeText(AppModule.INSTANCE.getApplication(), text, Toast.LENGTH_LONG);
        mToast.setGravity(Gravity.CENTER, 0, 0);
    }

    private static boolean isMainThread() {
        return Looper.myLooper() == Looper.getMainLooper();
    }
}
