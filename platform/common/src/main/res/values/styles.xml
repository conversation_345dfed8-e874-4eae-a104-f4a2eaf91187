<resources>

    <!-- Base application theme. -->
    <style name="common_AppTheme" parent="Theme.AppCompat.Light.DarkActionBar">
        <!-- Customize your theme here. -->
        <item name="windowNoTitle">true</item>
    </style>

    <style name="common_AppTheme.FullScreen" parent="common_AppTheme">
        <item name="android:windowBackground">@color/common_FFFFFFFF</item>
        <item name="android:windowFullscreen">true</item>
    </style>

    <style name="common_Translucent" parent="common_AppTheme">
        <item name="android:windowBackground">@color/common_transparent</item>
        <item name="android:windowIsTranslucent">true</item>
    </style>

    <style name="common_activity_dialog" parent="@style/Theme.AppCompat.Light.Dialog">
        <!-- 去黑边 -->
        <item name="android:windowFrame">@null</item>
        <!-- 设置是否可滑动 -->
        <item name="android:windowIsFloating">true</item>
        <!-- 设置是否透明 -->
        <item name="android:windowIsTranslucent">false</item>
        <!-- 无标题 -->
        <item name="android:windowNoTitle">true</item>
        <!-- 背景 -->
        <item name="android:background">@null</item>
        <!-- 窗口背景 -->
        <item name="android:windowBackground">@android:color/transparent</item>
        <!-- 是否变暗 -->
        <item name="android:backgroundDimEnabled">true</item>
        <!-- 点击空白部分activity不消失 -->
        <item name="android:windowCloseOnTouchOutside">true</item>
        <!-- 无标题 有的手机设置这行代码-->
        <item name="windowNoTitle">true</item>
        <item name="android:windowAnimationStyle">@style/common_noAnimation</item>
    </style>

    <style name="common_translucent_dialog_activity" parent="common_activity_dialog">
        <!-- 去黑边 -->
        <item name="android:windowFrame">@null</item>
        <!-- 设置是否可滑动 -->
        <item name="android:windowIsFloating">true</item>
        <!-- 设置是否透明 -->
        <item name="android:windowIsTranslucent">true</item>
        <!-- 无标题 -->
        <item name="android:windowNoTitle">true</item>
        <!-- 背景 -->
        <item name="android:background">@null</item>
        <!-- 窗口背景 -->
        <item name="android:windowBackground">@android:color/transparent</item>
        <!-- 是否变暗 -->
        <item name="android:backgroundDimEnabled">false</item>
        <!-- 点击空白部分activity不消失 -->
        <item name="android:windowCloseOnTouchOutside">true</item>
        <!-- 无标题 有的手机设置这行代码-->
        <item name="windowNoTitle">true</item>
        <item name="android:windowAnimationStyle">@style/common_noAnimation</item>
    </style>

    <style name="common_noAnimation">
        <item name="android:activityOpenEnterAnimation">@null</item>
        <item name="android:activityOpenExitAnimation">@null</item>
        <item name="android:activityCloseEnterAnimation">@null</item>
        <item name="android:activityCloseExitAnimation">@null</item>
        <item name="android:taskOpenEnterAnimation">@null</item>
        <item name="android:taskOpenExitAnimation">@null</item>
        <item name="android:taskCloseEnterAnimation">@null</item>
        <item name="android:taskCloseExitAnimation">@null</item>
        <item name="android:taskToFrontEnterAnimation">@null</item>
        <item name="android:taskToFrontExitAnimation">@null</item>
        <item name="android:taskToBackEnterAnimation">@null</item>
        <item name="android:taskToBackExitAnimation">@null</item>
    </style>


    <style name="common_translucent_theme" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:background">#00000000</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:windowIsTranslucent">true</item>
    </style>


    <style name="common_dialog_normal" parent="@android:style/Theme.Dialog">
        <item name="android:windowFrame">@null</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:background">@android:color/transparent</item>
        <item name="android:backgroundDimEnabled">true</item>
    </style>

    <style name="common_dialog_toast" parent="@style/Theme.AppCompat.Dialog">

        <!-- 无标题 -->
        <item name="android:windowNoTitle">true</item>
        <!-- 背景 -->
        <item name="android:background">@null</item>
        <!-- 窗口背景 -->
        <item name="android:windowBackground">@android:color/transparent</item>
        <!-- 是否变暗 -->
        <item name="android:backgroundDimEnabled">false</item>

    </style>

    <style name="common_toast_anim" parent="android:Animation">
        <item name="android:windowEnterAnimation">@anim/common_toast_in</item>
        <item name="android:windowExitAnimation">@anim/common_toast_out</item>
    </style>
</resources>
