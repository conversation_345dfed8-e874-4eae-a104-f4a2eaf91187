<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">


        <com.dz.foundation.ui.widget.DzRelativeLayout
            android:id="@+id/root"
            android:layout_width="wrap_content"
            android:layout_marginHorizontal="@dimen/common_dp8"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:paddingLeft="@dimen/common_dp16"
            android:paddingTop="@dimen/common_dp13"
            android:paddingRight="@dimen/common_dp16"
            android:paddingBottom="@dimen/common_dp13"
            app:shape="rectangle"
            app:shape_radius="@dimen/common_dp12"
            app:shape_solid_color="@color/common_F55E6267">

            <com.dz.foundation.ui.widget.DzImageView
                android:id="@+id/iv_top"
                android:layout_width="@dimen/common_dp55"
                android:layout_height="@dimen/common_dp55"
                android:layout_centerHorizontal="true"
                tools:visibility="gone"
                android:visibility="gone" />

            <com.dz.foundation.ui.widget.DzImageView
                android:id="@+id/iv_left"
                android:layout_width="@dimen/common_dp24"
                android:layout_height="@dimen/common_dp24"
                android:layout_marginRight="@dimen/common_dp12"
                tools:visibility="gone"
                android:visibility="gone" />

            <com.dz.foundation.ui.widget.DzTextView
                android:id="@+id/text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/iv_top"
                android:layout_toRightOf="@+id/iv_left"
                android:gravity="center"
                android:includeFontPadding="false"
                android:lineSpacingExtra="@dimen/common_dp1"
                tools:text="测试行间距测试行间距测试行间距测试行间距测试行间距测试行间距测试行间距测试行间距"
                android:textColor="@color/common_FFFFFFFF_FFD0D0D0"
                android:textSize="@dimen/common_dp15" />
        </com.dz.foundation.ui.widget.DzRelativeLayout>


    </FrameLayout>


</layout>