package com.dz.platform.hook

import android.annotation.SuppressLint
import android.app.ActivityManager
import android.app.ActivityManager.RunningAppProcessInfo
import android.content.ClipData
import android.content.ClipboardManager
import android.content.ContentResolver
import android.content.Intent
import android.content.pm.ApplicationInfo
import android.content.pm.PackageInfo
import android.content.pm.PackageManager
import android.content.pm.ResolveInfo
import android.hardware.Sensor
import android.hardware.SensorEventListener
import android.hardware.SensorManager
import android.location.Location
import android.location.LocationListener
import android.location.LocationManager
import android.net.ConnectivityManager
import android.net.DhcpInfo
import android.net.NetworkInfo
import android.net.NetworkRequest
import android.net.wifi.ScanResult
import android.net.wifi.WifiConfiguration
import android.net.wifi.WifiInfo
import android.net.wifi.WifiManager
import android.os.Build
import android.os.Environment
import android.os.Handler
import android.provider.Settings
import android.telephony.CellInfo
import android.telephony.TelephonyManager
import android.util.Log
import androidx.annotation.Keep
import com.dz.asm.hook.annotation.AsmMethodOpcodes
import com.dz.asm.hook.annotation.AsmMethodReplace
import com.dz.foundation.base.module.AppModule
import com.heytap.openid.base.m_c
import java.io.File
import java.lang.reflect.Method
import java.net.InetAddress
import java.net.NetworkInterface
import java.util.*
import java.util.concurrent.Executor

@Keep
object DzHookMethodUtil {
    private const val TAG = "dzHook"

    //是否可以正常调用
    private var isOpenPass: Boolean = true

    //是否使用缓存，开启后可以避免调用频次问题
    var isUseCache: Boolean = false

    private var openDebugLog = true

    private var anyCache = HashMap<String, Any>()

    //记录次数
    val countMap = HashMap<String, Int?>()

    private val logLine =
        "################################################################################################"

    private fun logCallStack(name: String, danger: Boolean = true) {
        if (countMap.containsKey(name)) {
            countMap[name] = countMap[name]?.plus(1)
        } else {
            countMap[name] = 1
        }

        val msg = " \n$logLine" +
                "\n监控方法 ==>：$name " +
                "\n调用次数：第${countMap[name]}次" +
                "\n是否可以实际调用：$isOpenPass," +
                "\n是否使用缓存：$isUseCache" +
                "\n调用方法堆栈---> ${Log.getStackTraceString(Throwable())}"
        if (danger) {
            logE(msg)
        }else{
            logW(msg)
        }
    }

    private fun <T> getListCache(key: String): List<T>? {
        if (!isUseCache) {
            return null
        }
        val cache = anyCache[key]
        if (cache != null && cache is List<*>) {
            try {
                return cache as List<T>
            } catch (e: Exception) {
                logW("获取缓存：getListCache: key=$key,e=${e.message}")
            }
        }
        logW("获取缓存：getListCache key=$key,return null")
        return null
    }

    private fun <T> getCache(key: String): T? {
        if (!isUseCache) {
            return null
        }
        val cache = anyCache[key]
        if (cache != null) {
            try {
                logW("获取缓存：getCache: key=$key,value=$cache")
                return cache as T
            } catch (e: Exception) {
                logW("获取缓存：getListCache: key=$key,e=${e.message}")
            }
        }
        logW("获取缓存：getCache key=$key,return null")
        return null
    }


    private fun <T> putCache(key: String, value: T): T {
        logW("存入缓存： key=$key --> value=$value")
        value?.let {
            anyCache[key] = value
        }
        return value
    }


    /**
     * 获得当前正在运行的所有应用程序的进程
     */
    @JvmStatic
    @AsmMethodReplace(oriClass = ActivityManager::class, oriAccess = AsmMethodOpcodes.INVOKEVIRTUAL)
    fun getRunningAppProcesses(manager: ActivityManager): List<RunningAppProcessInfo?> {
        //logW("隐私api调用：获得当前正在运行的所有应用程序的进程")
        val key = "getRunningAppProcesses"
        logCallStack(key)
        val cache = getListCache<RunningAppProcessInfo?>(key)
        if (cache != null) {
            return cache
        }
        if (!isOpenPass) {
            return emptyList()
        }
        val value = manager.runningAppProcesses
        return putCache(key, value)
    }

    /**
     * 返回用户最近启动的任务列表
     */
    @JvmStatic
    @AsmMethodReplace(oriClass = ActivityManager::class, oriAccess = AsmMethodOpcodes.INVOKEVIRTUAL)
    fun getRecentTasks(
        manager: ActivityManager,
        maxNum: Int,
        flags: Int
    ): List<ActivityManager.RecentTaskInfo>? {
        //logW("隐私api调用：获得用户最近启动的任务列表")
        val key = "getRecentTasks"
        logCallStack(key)
        val cache = getListCache<ActivityManager.RecentTaskInfo>(key)
        if (cache != null) {
            return cache
        }
        if (!isOpenPass) {
            return emptyList()
        }
        val value = manager.getRecentTasks(maxNum, flags)
        return putCache(key, value)

    }

    /**
     * 返回当前正在运行的任务列表
     */
    @JvmStatic
    @AsmMethodReplace(oriClass = ActivityManager::class, oriAccess = AsmMethodOpcodes.INVOKEVIRTUAL)
    fun getRunningTasks(
        manager: ActivityManager,
        maxNum: Int
    ): List<ActivityManager.RunningTaskInfo>? {
        //logW("隐私api调用：获得当前正在运行的任务列表")
        val key = "getRunningTasks"
        logCallStack(key)
        val cache = getListCache<ActivityManager.RunningTaskInfo>(key)
        if (cache != null) {
            return cache
        }
        if (!isOpenPass) {
            return emptyList()
        }
        val value = manager.getRunningTasks(maxNum)
        return putCache(key, value)

    }

    /**
     * 读取基站信息，需要开启定位
     * 设备上的所有无线电请求所有可用的小区信息
     */
    @JvmStatic
    @SuppressLint("MissingPermission")
    @AsmMethodReplace(
        oriClass = TelephonyManager::class,
        oriAccess = AsmMethodOpcodes.INVOKEVIRTUAL
    )
    fun getAllCellInfo(manager: TelephonyManager): List<CellInfo>? {
        //logW("隐私api调用：读取基站信息")
        val key = "getAllCellInfo"
        logCallStack(key)
        val cache = getListCache<CellInfo>(key)
        if (cache != null) {
            return cache
        }
        if (!isOpenPass) {
            return emptyList()
        }
        val value = manager.getAllCellInfo()
        return putCache(key, value)
    }

    /**
     * 获取唯一的设备ID：DeviceId
     */
    @SuppressLint("HardwareIds", "MissingPermission")
    @JvmStatic
    @AsmMethodReplace(
        oriClass = TelephonyManager::class,
        oriAccess = AsmMethodOpcodes.INVOKEVIRTUAL
    )
    fun getDeviceId(manager: TelephonyManager): String? {
        //logW("隐私api调用：获取唯一的设备ID->DeviceId")
        val key = "getDeviceId"
        logCallStack(key)
        val cache = getCache<String>(key)
        if (cache != null) {
            return cache
        }
        if (!isOpenPass) {
            return null
        }
        val value = manager.deviceId
        return putCache(key, value)

    }

    /**
     * 读取Imei
     */
    @SuppressLint("HardwareIds", "MissingPermission")
    @JvmStatic
    @AsmMethodReplace(
        oriClass = TelephonyManager::class,
        oriAccess = AsmMethodOpcodes.INVOKEVIRTUAL
    )
    fun getImei(manager: TelephonyManager): String? {
        //logW("隐私api调用： 读取Imei")
        val key = "getImei"
        logCallStack(key)
        val cache = getCache<String>(key)
        if (cache != null) {
            return cache
        }
        if (!isOpenPass) {
            return null
        }
        val value = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            manager.imei
        } else {
            ""
        }
        return putCache(key, value)
    }


    /**
     * 读取Imei
     */
    @SuppressLint("HardwareIds", "MissingPermission")
    @JvmStatic
    @AsmMethodReplace(
        oriClass = TelephonyManager::class,
        oriAccess = AsmMethodOpcodes.INVOKEVIRTUAL
    )
    fun getSubscriberId(manager: TelephonyManager): String? {
        //logW("隐私api调用： 读取Imei")
        val key = "getSubscriberId"
        logCallStack(key)
        val cache = getCache<String>(key)
        if (cache != null) {
            return cache
        }
        if (!isOpenPass) {
            return null
        }
        val value = manager.subscriberId

        return putCache(key, value)
    }


    /**
     * 读取WIFI的SSID
     * 返回当前802.11网络的服务集标识(SSID)
     */
    @JvmStatic
    @AsmMethodReplace(oriClass = WifiInfo::class, oriAccess = AsmMethodOpcodes.INVOKEVIRTUAL)
    fun getSSID(manager: WifiInfo): String? {
        //logW("隐私api调用： 读取WIFI的SSID")
        val key = "getSSID"
        logCallStack(key)
        val cache = getCache<String?>(key)
        if (cache != null) {
            return cache
        }

        if (!isOpenPass) {
            return ""
        }

        val value = manager.ssid
        return putCache(key, value)
    }

    /**
     * 读取WIFI的BSSID
     * 返回当前接入点的基本服务集标识符(BSSID)。
     */
    @JvmStatic
    @AsmMethodReplace(oriClass = WifiInfo::class, oriAccess = AsmMethodOpcodes.INVOKEVIRTUAL)
    fun getBSSID(manager: WifiInfo): String? {
        //logW("隐私api调用： 读取WIFI的BSSID")
        val key = "getBSSID"
        logCallStack(key)
        val cache = getCache<String?>(key)
        if (cache != null) {
            return cache
        }
        if (!isOpenPass) {
            return ""
        }
        val value = manager.bssid
        return putCache(key, value)
    }

    /**
     * 读取网络mac地址
     */
    @SuppressLint("HardwareIds")
    @JvmStatic
    @AsmMethodReplace(oriClass = WifiInfo::class, oriAccess = AsmMethodOpcodes.INVOKEVIRTUAL)
    fun getMacAddress(manager: WifiInfo): String? {
        //logW("隐私api调用： 读取网络mac地址")
        val key = "getMacAddress"
        logCallStack(key)
        val cache = getCache<String?>(key)
        if (cache != null) {
            return cache
        }
        if (!isOpenPass) {
            return ""
        }
        val value = manager.macAddress
        return putCache(key, value)
    }

    /**
     * 读取AndroidId
     */
    @JvmStatic
    @AsmMethodReplace(oriClass = Settings.System::class, oriAccess = AsmMethodOpcodes.INVOKESTATIC)
    fun getString(resolver: ContentResolver, name: String): String? {
        //处理AndroidId
        if (Settings.Secure.ANDROID_ID == name) {
            //logW("隐私api调用： 获取AndroidId")
            val key = "getAndroidId"
            logCallStack(key)
            val cache = getCache<String?>(key)
            if (cache != null) {
                return cache
            }
            if (!isOpenPass) {
                return ""
            }
            val value = Settings.System.getString(resolver, name)
            return putCache(key, value)
        }

        return Settings.System.getString(resolver, name)
    }

    /**
     * 获取某类型传感器列表
     */
    @JvmStatic
    @AsmMethodReplace(oriClass = SensorManager::class, oriAccess = AsmMethodOpcodes.INVOKEVIRTUAL)
    fun getSensorList(manager: SensorManager, type: Int): List<Sensor>? {
        //logW("隐私api调用： 获取某类型传感器列表")
        val key = "getSensorList:$type"
        logCallStack(key)
        val cache = getListCache<Sensor>(key)
        if (cache != null) {
            return cache
        }
        if (!isOpenPass) {
            return mutableListOf()
        }
        val value = manager.getSensorList(type)
        return putCache(key, value)

    }

    /**
     * 获取某类型传感器列表
     */
    @JvmStatic
    @AsmMethodReplace(oriClass = SensorManager::class, oriAccess = AsmMethodOpcodes.INVOKEVIRTUAL)
    fun getDynamicSensorList(manager: SensorManager, type: Int): List<Sensor>? {
        //logW("隐私api调用： 获取某类型传感器列表")
        val key = "getDynamicSensorList:$type"
        logCallStack(key)
        val cache = getListCache<Sensor>(key)
        if (cache != null) {
            return cache
        }
        if (!isOpenPass) {
            return mutableListOf()
        }
        val value = manager.getDynamicSensorList(type)
        return putCache(key, value)

    }

    @JvmStatic
    @AsmMethodReplace(oriClass = SensorManager::class, oriAccess = AsmMethodOpcodes.INVOKEVIRTUAL)
    fun registerListener(
        manager: SensorManager,
        listener: SensorEventListener,
        sensor: Sensor,
        samplingPeriodUs: Int,
        handler: Handler
    ): Boolean {
        val key = "sensor registerListener "
        logCallStack(key)
        return manager.registerListener(listener, sensor, samplingPeriodUs, handler)
    }

    /**
     * 获取某类型传感器列表
     */
    @JvmStatic
    @AsmMethodReplace(oriClass = SensorManager::class, oriAccess = AsmMethodOpcodes.INVOKEVIRTUAL)
    fun getDefaultSensor(manager: SensorManager, type: Int): Sensor? {
        //logW("隐私api调用： 获取某类型传感器列表")
        val key = "getDefaultSensor:$type"
        logCallStack(key)
        val cache = getCache<Sensor>(key)
        if (cache != null) {
            return cache
        }
        if (!isOpenPass) {
            return null
        }
        val value = manager.getDefaultSensor(type)
        return putCache(key, value)

    }

    /**
     * 读取WIFI接入点扫描结果
     */
    @JvmStatic
    @AsmMethodReplace(oriClass = WifiManager::class, oriAccess = AsmMethodOpcodes.INVOKEVIRTUAL)
    fun getScanResults(manager: WifiManager): List<ScanResult>? {
        //logW("隐私api调用： 读取WIFI接入点扫描结果")
        val key = "getScanResults"
        logCallStack(key)
        val cache = getListCache<ScanResult>(key)
        if (cache != null) {
            return cache
        }
        if (!isOpenPass) {
            return mutableListOf()
        }
        val value = manager.scanResults
        return putCache(key, value)
    }

    /**
     * 读取DHCP信息
     */
    @JvmStatic
    @AsmMethodReplace(oriClass = WifiManager::class, oriAccess = AsmMethodOpcodes.INVOKEVIRTUAL)
    fun getDhcpInfo(manager: WifiManager): DhcpInfo? {
        //logW("隐私api调用： 读取DHCP信息")
        val key = "getDhcpInfo"
        logCallStack(key)
        val cache = getCache<DhcpInfo>(key)
        if (cache != null) {
            return cache
        }
        if (!isOpenPass) {
            return null
        }
        val value = manager.dhcpInfo
        return putCache(key, value)

    }


    /**
     * 获取当前前台用户配置的所有网络的列表
     */
    @SuppressLint("MissingPermission")
    @JvmStatic
    @AsmMethodReplace(oriClass = WifiManager::class, oriAccess = AsmMethodOpcodes.INVOKEVIRTUAL)
    fun getConfiguredNetworks(manager: WifiManager): List<WifiConfiguration>? {
        //logW("隐私api调用： 获取当前前台用户配置的所有网络的列表")
        val key = "getConfiguredNetworks"
        logCallStack(key)
        val cache = getListCache<WifiConfiguration>(key)
        if (cache != null) {
            return cache
        }
        if (!isOpenPass) {
            return mutableListOf()
        }
        val value = manager.configuredNetworks
        return putCache(key, value)

    }


    /**
     * 读取位置信息
     */
    @JvmStatic
    @SuppressLint("MissingPermission")
    @AsmMethodReplace(oriClass = LocationManager::class, oriAccess = AsmMethodOpcodes.INVOKEVIRTUAL)
    fun getLastKnownLocation(
        manager: LocationManager, provider: String
    ): Location? {
        //logW("隐私api调用： 读取位置信息")
        logCallStack("getLastKnownLocation")
        if (!isOpenPass) {
            return null
        }
        return manager.getLastKnownLocation(provider)
    }


    /**
     * 监视精细行动轨迹
     */
    @SuppressLint("MissingPermission")
    @JvmStatic
    @AsmMethodReplace(oriClass = LocationManager::class, oriAccess = AsmMethodOpcodes.INVOKEVIRTUAL)
    fun requestLocationUpdates(
        manager: LocationManager, provider: String, minTime: Long, minDistance: Float,
        listener: LocationListener
    ) {
        //logW("隐私api调用： 监视精细行动轨迹")
        logCallStack("requestLocationUpdates")
        if (!isOpenPass) {
            return
        }
        manager.requestLocationUpdates(provider, minTime, minDistance, listener)

    }

    /**
     * 读取剪切板
     */
    @JvmStatic
    @AsmMethodReplace(
        oriClass = ClipboardManager::class,
        oriAccess = AsmMethodOpcodes.INVOKEVIRTUAL
    )
    fun getPrimaryClip(manager: ClipboardManager): ClipData? {
        //logW("隐私api调用： 读取剪切板")
        val key = "getPrimaryClip"
        logCallStack(key)
//        val cache = getCache<ClipData>(key)
//        if (cache != null) {
//            return cache
//        }
        if (!isOpenPass) {
            return null
        }
        val value = manager.primaryClip
        return putCache(key, value)

    }


    /**
     * 创建目录
     */
    @JvmStatic
    @AsmMethodReplace(
        oriClass = File::class,
        oriAccess = AsmMethodOpcodes.INVOKEVIRTUAL
    )
    fun mkdirs(manager: File): Boolean {
        //logW("隐私api调用： 读取剪切板")
        val key = "mkdirs"
        //  logCallStack(key)
        if (!isOpenPass) {
            return false
        }
        return manager.mkdirs()
    }

    /**
     * 创建目录
     */
    @JvmStatic
    @AsmMethodReplace(
        oriClass = File::class,
        oriAccess = AsmMethodOpcodes.INVOKEVIRTUAL
    )
    fun mkdir(manager: File): Boolean {
        // logW("隐私api调用： 文件操作")
        val key = "mkdir"
        // logCallStack(key)
        if (!isOpenPass) {
            return false
        }
        return manager.mkdir()
    }


    /**
     * 获取sd 卡状态
     */
    @JvmStatic
    @AsmMethodReplace(
        oriClass = Environment::class,
        oriAccess = AsmMethodOpcodes.INVOKESTATIC
    )
    fun getExternalStorageState(): String? {
        logD("隐私api调用： 文件操作 getExternalStorageState")
        val key = "getExternalStorageState "
        logCallStack(key)
        if (!isOpenPass) {
            return ""
        }
        return Environment.getExternalStorageState()
    }


    /**
     * 获取sd 卡状态
     */
    @JvmStatic
    @AsmMethodReplace(
        oriClass = Environment::class,
        oriAccess = AsmMethodOpcodes.INVOKESTATIC
    )
    fun getExternalStorageDirectory(): File? {
        logD("隐私api调用： 文件操作 getExternalStorageDirectory")
        val key = "getExternalStorageDirectory "
        logCallStack(key)
        if (!isOpenPass) {
            return null
        }
        return Environment.getExternalStorageDirectory()
    }

    /**
     * 读取Ip地址
     */
    @JvmStatic
    @AsmMethodReplace(
        oriClass = NetworkInterface::class,
        oriAccess = AsmMethodOpcodes.INVOKEVIRTUAL
    )
    fun getInetAddresses(ni: NetworkInterface): Enumeration<InetAddress>? {
        val key = "getInetAddresses"
        logCallStack(key)
        return ni.inetAddresses
    }

    /**
     * 读取Ip地址
     */
    @JvmStatic
    @AsmMethodReplace(
        oriClass = InetAddress::class,
        oriAccess = AsmMethodOpcodes.INVOKESTATIC
    )
    fun getByName(host: String): InetAddress? {
        logD("InetAddress getByName")
        val key = "InetAddress getByName "
        logCallStack(key)
        return InetAddress.getByName(host)
    }

    /**
     * 读取Ip地址
     */
    @JvmStatic
    @AsmMethodReplace(
        oriClass = WifiInfo::class,
        oriAccess = AsmMethodOpcodes.INVOKEVIRTUAL
    )
    fun getIpAddress(wi: WifiInfo): Int {
        val key = "getIpAddress"
        logCallStack(key)
        return wi.ipAddress
    }

    /**
     * getInstalledPackages
     */
    @JvmStatic
    @AsmMethodReplace(
        oriClass = PackageManager::class,
        oriAccess = AsmMethodOpcodes.INVOKEVIRTUAL
    )
    fun getInstalledPackages(packageManager: PackageManager, flag: Int): MutableList<PackageInfo> {


        val key = "getInstalledPackages"
        logCallStack(key)
        return packageManager.getInstalledPackages(flag)
    }

    /**
     * getInstalledApplications
     */
    @JvmStatic
    @AsmMethodReplace(
        oriClass = PackageManager::class,
        oriAccess = AsmMethodOpcodes.INVOKEVIRTUAL
    )
    fun getInstalledApplications(
        packageManager: PackageManager,
        flag: Int
    ): MutableList<ApplicationInfo> {


        val key = "getInstalledApplications"
        logCallStack(key)
        return packageManager.getInstalledApplications(flag)
    }


    /**
     * queryIntentActivities
     */
    @JvmStatic
    @AsmMethodReplace(
        oriClass = PackageManager::class,
        oriAccess = AsmMethodOpcodes.INVOKEVIRTUAL
    )
    fun queryIntentActivities(
        packageManager: PackageManager,
        intent: Intent,
        flag: Int
    ): MutableList<ResolveInfo> {


        val key = "queryIntentActivities"
        logCallStack(key)
        return packageManager.queryIntentActivities(intent, flag)
    }


    /**
     * queryIntentActivities
     */
    @JvmStatic
    @AsmMethodReplace(
        oriClass = PackageManager::class,
        oriAccess = AsmMethodOpcodes.INVOKEVIRTUAL
    )
    fun getPackageInfo(
        packageManager: PackageManager,
        packageName: String,
        flag: Int
    ): PackageInfo? {
        var key = "getPackageInfo  packageName = $packageName"
        var danger = false
        if (AppModule.getPackageName() != packageName) {
            //视为获取应用安装列表的行为
            key += "获取了应用安装列表"
            danger = true
        }
        logCallStack(key, danger)
        return packageManager.getPackageInfo(packageName, flag)
    }

    /**
     * getApplicationInfo
     */
    @JvmStatic
    @AsmMethodReplace(
        oriClass = PackageManager::class,
        oriAccess = AsmMethodOpcodes.INVOKEVIRTUAL
    )
    fun getApplicationInfo(
        packageManager: PackageManager,
        packageName: String,
        flag: Int
    ): ApplicationInfo {
        var key = "getApplicationInfo  packageName = $packageName"
        var danger = false
        if (AppModule.getPackageName() != packageName) {
            //视为获取应用安装列表的行为
            key += "获取了应用安装列表"
            danger = true
        }
        logCallStack(key, danger)
        return packageManager.getApplicationInfo(packageName, flag)
    }

    /**
     * getSimOperator 运营商信息
     */
    @JvmStatic
    @AsmMethodReplace(
        oriClass = TelephonyManager::class,
        oriAccess = AsmMethodOpcodes.INVOKEVIRTUAL
    )
    fun getSimOperator(packageManager: TelephonyManager): String? {
        val key = "getSimOperator SIM运营商代码"
        logCallStack(key)
        return packageManager.simOperator
    }

    /**
     * getSimOperator 运营商信息
     */
    @JvmStatic
    @AsmMethodReplace(
        oriClass = TelephonyManager::class,
        oriAccess = AsmMethodOpcodes.INVOKEVIRTUAL
    )
    fun getSimOperatorName(packageManager: TelephonyManager): String? {
        val key = "getSimOperatorName SIM运营商"
        logCallStack(key)
        return packageManager.simOperatorName
    }


    @JvmStatic
    @AsmMethodReplace(
        oriClass = TelephonyManager::class,
        oriAccess = AsmMethodOpcodes.INVOKEVIRTUAL
    )
    fun getNetworkOperator(packageManager: TelephonyManager): String? {
        val key = "getNetworkOperator 网络运营商代码"
        logCallStack(key)
        return packageManager.networkOperator
    }

    @JvmStatic
    @AsmMethodReplace(
        oriClass = TelephonyManager::class,
        oriAccess = AsmMethodOpcodes.INVOKEVIRTUAL
    )
    fun getNetworkOperatorName(packageManager: TelephonyManager): String? {
        val key = "getNetworkOperatorName 网络运营商"
        logCallStack(key)
        return packageManager.networkOperatorName
    }


    @JvmStatic
    @AsmMethodReplace(
        oriClass = WifiManager::class,
        oriAccess = AsmMethodOpcodes.INVOKEVIRTUAL
    )
    fun getConnectionInfo(wifiManager: WifiManager): WifiInfo {
        val key = "getConnectionInfo "
        logCallStack(key)
        return wifiManager.connectionInfo
    }

    @JvmStatic
    @AsmMethodReplace(
        oriClass = NetworkInfo::class,
        oriAccess = AsmMethodOpcodes.INVOKEVIRTUAL
    )
    fun isConnected(networkInfo: NetworkInfo): Boolean {
        val key = "isConnected"
        logCallStack(key)
        return networkInfo.isConnected
    }

    @JvmStatic
    @AsmMethodReplace(
        oriClass = WifiManager::class,
        oriAccess = AsmMethodOpcodes.INVOKEVIRTUAL
    )
    fun isWifiEnabled(wifiManager: WifiManager): Boolean {
        val key = "isWifiEnabled"
        logCallStack(key)
        return wifiManager.isWifiEnabled
    }

    @JvmStatic
    @AsmMethodReplace(
        oriClass = WifiManager::class,
        oriAccess = AsmMethodOpcodes.INVOKEVIRTUAL
    )
    fun getWifiState(wifiManager: WifiManager): Int {
        val key = "getWifiState"
        logCallStack(key)
        return wifiManager.getWifiState()
    }

    @JvmStatic
    @AsmMethodReplace(
        oriClass = NetworkInfo::class,
        oriAccess = AsmMethodOpcodes.INVOKEVIRTUAL
    )
    fun getState(networkInfo: NetworkInfo): NetworkInfo.State {

        val key = "getNetWorkState ${networkInfo.typeName}"
        logCallStack(key)
        return networkInfo.getState()
    }

    @JvmStatic
    @AsmMethodReplace(
        oriClass = WifiManager::class,
        oriAccess = AsmMethodOpcodes.INVOKEVIRTUAL
    )
    fun registerSubsystemRestartTrackingCallback(
        wifiManager: WifiManager,
        executor: Executor,
        callback: WifiManager.SubsystemRestartTrackingCallback
    ) {
        val key = "registerSubsystemRestartTrackingCallback"
        logCallStack(key)
        return wifiManager.registerSubsystemRestartTrackingCallback(executor, callback)
    }


    @JvmStatic
    @AsmMethodReplace(
        oriClass = NetworkInfo::class,
        oriAccess = AsmMethodOpcodes.INVOKEVIRTUAL
    )
    fun isAvailable(networkInfo: NetworkInfo): Boolean {
        val key = "getWifiState"
        logCallStack(key)
        return networkInfo.isAvailable()
    }


    @JvmStatic
    @AsmMethodReplace(
        oriClass = ConnectivityManager::class,
        oriAccess = AsmMethodOpcodes.INVOKEVIRTUAL
    )
    fun getActiveNetworkInfo(connectivityManager: ConnectivityManager): NetworkInfo? {
        val key = "getActiveNetworkInfo"
        logCallStack(key)
        return connectivityManager.getActiveNetworkInfo()
    }

    @JvmStatic
    @AsmMethodReplace(
        oriClass = ConnectivityManager::class,
        oriAccess = AsmMethodOpcodes.INVOKEVIRTUAL
    )
    fun getNetworkInfo(connectivityManager: ConnectivityManager, type: Int): NetworkInfo? {

        var typeName = ""
        if (ConnectivityManager.TYPE_MOBILE == type) {
            typeName = "TYPE_MOBILE"
        } else if (ConnectivityManager.TYPE_WIFI == type) {
            typeName = "TYPE_WIFI"
        }
        val key = "getNetworkInfo $typeName"
        logCallStack(key)
        var networkInfo = connectivityManager.getNetworkInfo(type)

        return networkInfo
    }

    @JvmStatic
    @AsmMethodReplace(
        oriClass = ConnectivityManager::class,
        oriAccess = AsmMethodOpcodes.INVOKEVIRTUAL
    )
    fun isActiveNetworkMetered(connectivityManager: ConnectivityManager): Boolean? {
        val key = "isActiveNetworkMetered"
        logCallStack(key)
        return connectivityManager.isActiveNetworkMetered()
    }


    @JvmStatic
    @AsmMethodReplace(
        oriClass = ConnectivityManager::class,
        oriAccess = AsmMethodOpcodes.INVOKEVIRTUAL
    )
    fun registerNetworkCallback(
        connectivityManager: ConnectivityManager,
        netRequest: NetworkRequest,
        networkCallback: ConnectivityManager.NetworkCallback
    ) {
        val key = "registerNetworkCallback"
        logCallStack(key)
        return connectivityManager.registerNetworkCallback(netRequest, networkCallback)
    }


    @JvmStatic
    @AsmMethodReplace(
        oriClass = Method::class,
        oriAccess = AsmMethodOpcodes.INVOKEVIRTUAL
    )
    fun invoke(method: Method, obj: Any?, args: Array<out Any>?): Any? {

        val methodList = mutableListOf<String>()

        methodList.add("android.content.pm.PackageManager.getPackageInfo")
        methodList.add("android.content.ContentResolver.query")
        methodList.add("java.net.InetAddress.getHostAddress")
        methodList.add("java.net.InetAddress.getAddress")
        methodList.add("android.os.Environment.getExternalStorageDirectory")
        methodList.add("android.app.ActivityManager.getRunningAppProcesses")
        methodList.add("android.content.ContentResolver.insert")
        methodList.add("android.provider.Settings\$Secure.getString")
        methodList.add("android.telephony.TelephonyManager.getSimOperator")
        methodList.add("android.telephony.TelephonyManager.getNetworkOperator")
        methodList.add("android.telephony.TelephonyManager.getSimState")
        methodList.add("android.content.pm.PackageManager.queryIntentActivities")
        methodList.add("android.app.Activity.requestPermissions")
        methodList.add("java.net.NetworkInterface.getHardwareAddress")
        methodList.add("android.net.wifi.WifiInfo.getBSSID")
        methodList.add("android.telephony.TelephonyManager.getDeviceId")
        methodList.add("android.hardware.SensorManager.getSensorList")
        methodList.add("android.net.wifi.WifiInfo.getMacAddress")
        methodList.add("android.hardware.SensorManager.registerListener")
        methodList.add("android.os.Build.getSerial")
        methodList.add("android.net.wifi.WifiManager.getDhcpInfo")
        methodList.add("java.net.Inet4Address.getAddress")
        methodList.add("android.net.wifi.WifiManager.isWifiEnabled")
        methodList.add("android.net.wifi.WifiInfo.getSSID")
        methodList.add("android.provider.Settings\$System.getString")
        methodList.add("android.content.ClipboardManager.getPrimaryClip")
        methodList.add("android.telephony.TelephonyManager.getSimSerialNumber")
        methodList.add("android.app.ActivityManager.getRunningTasks")
        methodList.add("android.telephony.TelephonyManager.getSubscriberId")

        var methodApi = "${method.declaringClass.name}.${method.name}"
        val key = "reflect 反射方法调用 method=$methodApi"

        if (methodList.contains(methodApi)) {
            logCallStack(key)
        }

        return if (args != null) {
            method.invoke(obj ?: null, *args)
        } else {
            method.invoke(obj ?: null)
        }

    }

//    /**
//     * 读取Ip地址
//     */
//    @JvmStatic
//    @AsmMethodReplace(
//        oriClass = m_c::class,
//        oriAccess = AsmMethodOpcodes.INVOKEVIRTUAL
//    )
//    fun m_b(ni: m_c) {
//        val key = "oaid获取"
//        logCallStack(key)
//        return ni.m_b()
//    }

    private fun logD(log: String) {
        if (openDebugLog) {
            Log.d(TAG, log)
        }
    }

    private fun logE(log: String) {
        if (openDebugLog) {
            Log.e(TAG, log)
        }
    }

    private fun logW(log: String) {
        if (openDebugLog) {
            Log.w(TAG, log)
        }
    }
}