package com.dz.platform.bugly

import android.content.Context
import android.os.Build
import android.text.TextUtils
import com.dz.foundation.base.data.kv.GlobalKV
import com.dz.foundation.base.manager.task.TaskManager
import com.dz.foundation.base.module.AppInfoUtil
import com.dz.foundation.base.utils.LogUtil
import com.dz.foundation.base.utils.ProcessUtil
import com.dz.support.monitor.MonitorUtils
import com.networkbench.agent.impl.harvest.ConfigurationName.deviceId
import com.tencent.bugly.crashreport.CrashReport


object BuglyUtil {
    private var hasInit = false


    private fun initTingYun(context: Context, userId: String, appChannel: String) {
        //        听云 sdk
        MonitorUtils.initSdk(
            context,
            "c0598f5e64744e26aba2de2556339223",
            "wkrt.tingyun.com",
            userId,
            appChannel
        )
    }

    /**
     * 初始化bugly
     */
    fun initBugly(
        context: Context,
        userId: String,
        appChannel: String,
    ) {
        if (hasInit) {
            return
        }
        initTingYun(context, userId, appChannel)
        hasInit = true

        if (isBuglyEnable()) {
            val strategy = CrashReport.UserStrategy(context).apply {
                //设备id
                deviceID = deviceId
                //设备型号
                deviceModel = "${Build.BRAND} ${Build.MODEL}"
                //App的版本
                appVersion = AppInfoUtil.getAppVersionName()
                //App的包名
                appPackageName = context.packageName
                //上报进程控制,只在主进程下上报数据
//            LogUtil.d(
//                "CrashReportInfo",
//                "isUploadProcess ${ProcessUtil.getCurrentProcessName(context) == context.packageName}"
//            )
                isUploadProcess = ProcessUtil.getCurrentProcessName(context) == context.packageName
            }

            //Crash回调:返回的数据将伴随Crash一起上报到Bugly平台，并展示在附件中
            strategy.setCrashHandleCallback(object : CrashReport.CrashHandleCallback() {
                override fun onCrashHandleStart(
                    crashType: Int, errorType: String,
                    errorMessage: String, errorStack: String
                ): Map<String, String>? {
                    return null
                }
            })

            CrashReport.initCrashReport(context, "3062ed8ead", LogUtil.isDebugMode(), strategy)
            if (!TextUtils.isEmpty(userId)) {
                CrashReport.setUserId(userId)
            }
        }
    }

    fun resetParams(userId: String) {
        if (!TextUtils.isEmpty(userId)) {
            //由于flutter侧也有设置sentry user 的调用(调用时机在与初始化接口之前且很接近)，这里做延迟与flutter 侧的调用拉开时间差，防止安装后首次打开  sentry userId 被flutter侧 设置为NONE
            TaskManager.delayTask(500) {
                MonitorUtils.setUid(userId)
            }

            if (isBuglyEnable()) {
                CrashReport.setUserId(userId)
            }
        }
    }

    private inline fun isBuglyEnable(): Boolean {
        return GlobalKV.buglyEnable == 1
    }
}