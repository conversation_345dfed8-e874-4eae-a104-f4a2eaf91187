<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.dz.platform.uplog">

    <application>

        <!-- 在使用神策系统中的 Debug 实时查看、App 点击分析、可视化全埋点等需要扫码的功能时 配置后扫码即可拉起该 Activity -->
        <activity
            android:name="com.sensorsdata.analytics.android.sdk.dialog.SchemeActivity"
            android:configChanges="orientation|screenSize"
            android:exported="true"
            android:launchMode="singleTask">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.BROWSABLE" />
                <category android:name="android.intent.category.DEFAULT" />

                <data android:scheme="sa14ce156b" />
            </intent-filter>
        </activity>


    </application>

</manifest>