package com.dz.platform.uplog

import android.content.Context
import android.util.Log
import com.dz.foundation.base.module.AppModule
import com.dz.foundation.base.utils.LocalActivityMgr
import org.json.JSONObject

object UploadLogUtil {
    private const val TAG = "UploadLogUtil"

    fun init() {
        //初始化dzLog
        DzLog.initDzLog()
        //初始化神策，开启全埋点
        var content: Context? = LocalActivityMgr.getTopActivity()
        if (content == null) {
            content = AppModule.getApplication()
        }
        SensorLog.initSensor(content)
    }


    fun bindUser(uid: String) {
        SensorLog.bindUser(uid)
    }

    fun uploadLogAll(eventName: String, priority: Int, map: MutableMap<String, Any?>) {
        uploadLog(eventName, priority, map)
    }

    fun uploadDzLog(eventName: String, priority: Int, map: MutableMap<String, Any?>) {
        uploadLog(eventName, priority, map, uploadSensor = false)
    }

    fun upLoadSensorLog(eventName: String, map: MutableMap<String, Any?>) {
        uploadLog(eventName, 0, map, uploadDz = false)
    }


    private fun uploadLog(
        eventName: String,
        priority: Int,
        map: MutableMap<String, Any?>,
        uploadDz: Boolean = true,
        uploadSensor: Boolean = true
    ) {
        val jsonLog = JSONObject(map)
        Log.d(TAG, "准备上传埋点事件$eventName($priority)：$jsonLog")

        if (uploadDz) {
            DzLog.uploadLog(eventName, priority, jsonLog)
        }
        if (uploadSensor) {
            SensorLog.uploadLog(eventName, jsonLog)
        }
    }
}