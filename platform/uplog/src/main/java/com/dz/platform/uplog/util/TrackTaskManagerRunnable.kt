package com.dz.platform.uplog.util

import java.util.concurrent.ExecutorService
import java.util.concurrent.PriorityBlockingQueue
import java.util.concurrent.ThreadPoolExecutor
import java.util.concurrent.TimeUnit

class TrackTaskManagerRunnable : Runnable {

    private lateinit var mPool: ExecutorService

    /**
     * 是否停止
     */
    private var isStopped = false

    init {
        try {
            mPool = ThreadPoolExecutor(
                POOL_SIZE, POOL_SIZE,
                0L, TimeUnit.MILLISECONDS,
                PriorityBlockingQueue()
            )
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override fun run() {
        try {
            while (!isStopped) {
                val downloadTask = DzLogTaskQueue.takeTask()
                mPool.execute(downloadTask)
            }
            mPool.shutdown()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun stop() {
        isStopped = true
    }

    companion object {
        private const val POOL_SIZE = 1
    }


}