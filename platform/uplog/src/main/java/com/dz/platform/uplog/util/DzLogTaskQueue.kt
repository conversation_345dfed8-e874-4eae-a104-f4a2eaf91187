package com.dz.platform.uplog.util

import java.util.concurrent.PriorityBlockingQueue

object DzLogTaskQueue {
    //    private val logTaskQueue = LinkedBlockingQueue<Runnable>()
    private val logTaskQueue = PriorityBlockingQueue<PriorityDzLog>()

    fun addTask(trackEvenTask: PriorityDzLog?) {
        try {
            logTaskQueue.put(trackEvenTask)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun takeTask(): PriorityDzLog? {
        try {
            return logTaskQueue.take()
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return null
    }

    fun pollTask(): PriorityDzLog? {
        try {
            return logTaskQueue.poll()
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return null
    }

    fun isEmpty(): Boolean {
        return logTaskQueue.isEmpty()
    }
}