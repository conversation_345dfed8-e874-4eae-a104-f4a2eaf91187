package com.dz.platform.uplog

import android.content.Context
import android.text.TextUtils
import android.view.View
import com.blankj.utilcode.util.GsonUtils
import com.dz.foundation.base.data.kv.GlobalKV
import com.dz.foundation.base.utils.LogUtil
import com.google.gson.Gson
import com.sensorsdata.analytics.android.sdk.SAConfigOptions
import com.sensorsdata.analytics.android.sdk.SensorsAnalyticsAutoTrackEventType
import com.sensorsdata.analytics.android.sdk.SensorsDataAPI
import com.sensorsdata.analytics.android.sdk.SensorsDataDynamicSuperProperties
import org.json.JSONObject


object SensorLog {
    // private const val SA_SERVER_URL = "xhttp://scfx.dzeread.cn:8106/sa?project=production"
    private const val SA_SERVER_URL = "https://sc-sa.dzfread.cn/sa?project=dzmf_quick";

    fun initSensor(context: Context) {
        // 初始化配置
        val saConfigOptions = SAConfigOptions(SA_SERVER_URL)
        // 开启全埋点
        saConfigOptions.setAutoTrackEventType(
            SensorsAnalyticsAutoTrackEventType.APP_CLICK or
                    SensorsAnalyticsAutoTrackEventType.APP_END or
                    SensorsAnalyticsAutoTrackEventType.APP_VIEW_SCREEN
        )
            .enableLog(LogUtil.isDebugMode())
//            .enableTrackPageLeave(true, true)
        // 需要在主线程初始化神策 SDK
        SensorsDataAPI.startWithConfigOptions(context, saConfigOptions)
        SensorsDataAPI.sharedInstance().trackFragmentAppViewScreen()
        setUserProperties()  // 设置用户属性
    }


    /**
     * 设置全局动态公共属性
     */
    fun registerDynamicSuperProperties(block: () -> JSONObject) {
        try {
            SensorsDataAPI.sharedInstance()
                .registerDynamicSuperProperties(object : SensorsDataDynamicSuperProperties {
                    override fun getDynamicSuperProperties(): JSONObject {
                        return block.invoke()
                    }
                })

        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 关联用户id
     */
    fun bindUser(id: String) {
        if (TextUtils.isEmpty(id)) {
            return
        }
        SensorsDataAPI.sharedInstance().login(id)
    }

    /**
     *  设定用户属性
     */
    private fun setUserProperties() {
        try {
            val properties = JSONObject().apply {
                put("PushID", GlobalKV.pushId)
            }
            SensorsDataAPI.sharedInstance().profileSet(properties)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }


    /**
     * 记录激活事件
     *  6.0 以上，先申请 READ_PHONE_STATE 权限。
     */
    fun trackAppInstall() {
        try {
            val properties = JSONObject().apply {
                put("channel", "XXX")
            }
            SensorsDataAPI.sharedInstance().trackAppInstall(properties)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 自定义事件
     */
    fun uploadLog(eventName: String, jsonLog: JSONObject) {
        LogUtil.d("sensorLog", "eventName:$eventName jsonLog=$jsonLog")
        SensorsDataAPI.sharedInstance().track(eventName, jsonLog)
    }

    fun clearPubProperties() {
        SensorsDataAPI.sharedInstance().clearSuperProperties()//删除所有已设置的公共属性
    }

    fun setViewContent(view: View, content: String) {
        val properties = JSONObject()
        properties.put("\$element_content", content)
        SensorsDataAPI.sharedInstance().setViewProperties(view, properties)
    }

    fun setViewTitle(view: View, title: String) {
        val properties = JSONObject()
        properties.put("Title", title)
        SensorsDataAPI.sharedInstance().setViewProperties(view, properties)
    }

    /**
     * 忽略view 的自动采集
     *
     * @param view
     */
    fun ignoreViewAutoTrack(view: View) {
        SensorsDataAPI.sharedInstance().ignoreView(view)
    }

    fun setViewProperties(view: View, properties: MutableMap<String, Any>) {
        var jsonObject: JSONObject? = null
        if (properties != null) {
            val toJson = GsonUtils.toJson(properties)
            jsonObject = JSONObject(toJson)
        }

        SensorsDataAPI.sharedInstance().setViewProperties(view, jsonObject);
    }

    fun trackViewAppClick(view: View, properties: JSONObject?) {
        //触发该 View 的点击事件，并加上自定义属性
        SensorsDataAPI.sharedInstance().trackViewAppClick(view, properties)

    }

    fun trackViewScreen(view: View, properties: JSONObject?) {
        val eventName = "\$AppViewScreen"
        //触发该 View 的点击事件，并加上自定义属性
        SensorsDataAPI.sharedInstance().track(eventName, properties)

    }

    /**
     * 删除 App 本地存储的所有事件
     *
     */
    fun deleteAllCacheEvent() {
        SensorsDataAPI.sharedInstance().deleteAll()
    }

    fun getSensorUID(): String? {
        return SensorsDataAPI.sharedInstance().anonymousId
    }

}