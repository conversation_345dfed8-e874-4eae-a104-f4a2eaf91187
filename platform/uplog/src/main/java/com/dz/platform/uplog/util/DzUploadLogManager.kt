package com.dz.platform.uplog.util

import com.dz.foundation.base.utils.LogUtil
import org.json.JSONObject
import java.io.BufferedOutputStream
import java.net.HttpURLConnection
import java.net.URL

class DzUploadLogManager {
    /**
     * 上传日志到服务器
     */
    fun uploadLog(eventName: String, jsLog: JSONObject) {
        synchronized(this) {
            sendHttp(eventName, jsLog.toString())
        }
    }

    private fun sendHttp(eventName: String, jsonStr: String, isEncrypt: Boolean = true) {
        try {
            LogUtil.d("DzLog", "----------------------------------------------")
            LogUtil.d("DzLog", "开始上传-- $jsonStr")
            val connection = URL(LOG_URL).openConnection() as HttpURLConnection
            connection.run {
                requestMethod = "POST"
                //设置连接超时时间
                connectTimeout = 10 * 1000
                //设置读取超时时间
                readTimeout = 10 * 1000
                doOutput = true
            }
            var uploadLog = jsonStr
            //加密
            if (isEncrypt) {
                uploadLog = encryptJson(jsonStr)
            }
            val params = "json=${uploadLog}&isencrypt=${if (isEncrypt) "1" else "0"}"
            val out = connection.outputStream
            val bout = BufferedOutputStream(out)
            bout.write(params.toByteArray())
            bout.flush()

            val responseCode = connection.responseCode
            if (responseCode >= HttpURLConnection.HTTP_OK &&
                responseCode < HttpURLConnection.HTTP_MULT_CHOICE
            ) {
                LogUtil.d("DzLog", "成功上传-- $jsonStr")
            } else {
                LogUtil.d("DzLog", "失败上传-- $jsonStr")
            }

        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 加密
     */
    private fun encryptJson(logStr: String): String {
        //TODO 加密log
        return logStr
    }

    companion object {
        //    private val LOG_URL = "https://log.ssread.cn/client_standard_log.php"
        private const val LOG_URL = "http://www.baidu.com"
    }

}