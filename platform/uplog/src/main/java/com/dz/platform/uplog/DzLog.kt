package com.dz.platform.uplog

import com.blankj.utilcode.util.ThreadUtils
import com.dz.foundation.base.utils.LogUtil
import com.dz.platform.uplog.util.DzLogTaskQueue
import com.dz.platform.uplog.util.DzUploadLogManager
import com.dz.platform.uplog.util.PriorityDzLog
import com.dz.platform.uplog.util.TrackTaskManagerRunnable
import org.json.JSONException
import org.json.JSONObject

object DzLog {
    const val TAG = "DzLog"
    private var initFlag = false
    private val uploadLogManager = DzUploadLogManager()

    fun initDzLog() {
        if (!initFlag) {
            LogUtil.d(TAG, "dzLog初始化")

            ThreadUtils.getCachedPool().execute(TrackTaskManagerRunnable())
            initFlag = true
        }
    }


    fun uploadLog(eventName: String, priority: Int, json: JSONObject) {
        val cloneJsonLog = cloneJsonObject(json)
        DzLogTaskQueue.addTask(PriorityDzLog(priority) {
            uploadLogManager.uploadLog(eventName, cloneJsonLog)
        })
    }


    private fun cloneJsonObject(jsonObject: JSONObject?): JSONObject {
        if (jsonObject == null) {
            return JSONObject()
        }
        val cloneProperties: JSONObject = try {
            JSONObject(jsonObject.toString())
        } catch (e: JSONException) {
            jsonObject
        }
        return cloneProperties
    }
}