package com.dz.platform.player.listener

/**
 * @Author: guyh
 * @Date: 2023/2/8 16:53
 * @Description:播放器中的一些信息，包括：type包括了：循环播放开始，缓冲位置，当前播放位置，自动播放开始等
 * @Version:1.0
 */
interface OnInfoListener {
    companion object {
        var LoopingStart = 0
        var BufferedPosition = 1
        var CurrentPosition = 2
        var AutoPlayStart = 3
        var CurrentDownloadSpeed = 4
        var UtcTime = 5
        var LocalCacheLoaded = 6
    }

    /**
     *
     * @param code  信息码
     * @param extraMsg  信息内容
     * @param extraValue  信息值
     */
    fun onInfo(code: Int, extraMsg: String?, extraValue: Long = 0)
}