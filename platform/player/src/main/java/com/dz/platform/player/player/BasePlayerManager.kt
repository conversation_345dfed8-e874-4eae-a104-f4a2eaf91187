package com.dz.platform.player.player

import com.aliyun.player.AliPlayerFactory
import com.aliyun.player.AliPlayerGlobalSettings
import com.aliyun.player.IPlayer
import com.dz.platform.player.listener.ConvertURLCallback

/**
 * @Author: guyh
 * @Date: 2024/8/23
 * @Description:
 * @Version:1.0
 */
object BasePlayerManager {
    //设置播放器播放回调监听
    fun registerConvertURLCallback(callback: ConvertURLCallback?) {
        kotlin.runCatching {
            AliPlayerFactory.setConvertURLCallback(object : IPlayer.ConvertURLCallback {
                override fun convertURL(srcURL: String, srcFormat: String): String? {
                    return callback?.convertURL(srcURL, srcFormat)
                }
            })
        }
    }

    // 设置播放器播放回调监听为null，防止内存泄露
    fun unRegisterConvertURLCallback() {
        kotlin.runCatching {
            AliPlayerFactory.setConvertURLCallback(null)
        }
    }

    /**
     *  开启本地缓存，开启之后，会缓存到本地文件中。
     *
     *  @param enable - 本地缓存功能开关。true：开启本地缓存，false：关闭，默认关闭。
     *  @param maxBufferMemoryKB - 新版本已废弃，暂无作用
     *  @param localCacheDir - 本地缓存的文件目录，为绝对路径。
     */
    fun enableLocalCache(enable: Boolean, maxBufferMemoryKB: Int, localCacheDir: String) {
        kotlin.runCatching {
            AliPlayerGlobalSettings.enableLocalCache(enable, maxBufferMemoryKB, localCacheDir)
        }
    }

    /**
     * 本地缓存文件清理相关配置。
     *
     * @param expireMin - 新版本已废弃，暂无作用。
     * @param maxCapacityMB - 最大缓存容量。单位：兆，默认值20GB，在清理时，如果缓存总容量超过此大小，则会以cacheItem为粒度，按缓存的最后时间排序，一个一个的删除最旧的缓存文件，直到小于等于最大缓存容量。
     * @param freeStorageMB - 磁盘最小空余容量。单位：兆，默认值0，在清理时，同最大缓存容量，如果当前磁盘容量小于该值，也会按规则一个一个的删除缓存文件，直到freeStorage大于等于该值或者所有缓存都被清理掉。
     */
    fun setCacheFileClearConfig(expireMin: Long, maxCapacityMB: Long, freeStorageMB: Long) {
        kotlin.runCatching {
            AliPlayerGlobalSettings.setCacheFileClearConfig(expireMin, maxCapacityMB, freeStorageMB)
        }
    }

    /**
     *  开启关闭http dns
     * @param openHttpDns - 是否开启http dns。true：开启；false:关闭。默认关闭
     */
    fun enableHttpDns(openHttpDns: Boolean) {
        kotlin.runCatching {
            if (openHttpDns) {
                AliPlayerGlobalSettings.enableHttpDns(openHttpDns)
            }
        }
    }

}