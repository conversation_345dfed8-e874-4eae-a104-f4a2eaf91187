package com.dz.platform.player.player

import com.aliyun.player.AliListPlayer
import com.aliyun.player.IListPlayer
import com.aliyun.player.nativeclass.PreloadConfig
/**
 * @Author: guyh
 * @Date: 2023/2/8 14:59
 * @Description:列表播放器
 * @Version:1.0
 */
class DzListPlayer : BasePlayer() {

    /**
     * 设置预加载个数
     * @param count  设置预加载个数。总共加载的个数为： 1 + count*2
     */
    fun setPreloadCount(count: Int) {
        getListPlayer()?.setPreloadCount(count)
    }

    /**
     *  设置动态预加载
     *  @param duration 预加载时长 ms
     *  @param offset   动态预加载的递减值 ms
     *  @param fCount   向前预加载的数量
     *  @param bCount   向后预加载的数量
     */
    fun setDynamicPreLoad(duration: Int, offset: Int, fCount: Int, bCount: Int) {
        getListPlayer().let { aliListPlayer ->
            setPreloadScene(true)
            updatePreloadConfig(aliListPlayer, duration)
            setPreloadCount(fCount, bCount)
            setPreloadStrategy(aliListPlayer, offset)
        }
    }

    /**
     * 开启推荐配置和动态预加载
     */
    fun setPreloadScene(isOpenPreLoad: Boolean) {
        getListPlayer()?.setPreloadScene(
            if (isOpenPreLoad) {
                IListPlayer.SceneType.SCENE_SHORT
            } else {
                IListPlayer.SceneType.SCENE_NONE
            }
        )
    }

    /**
     * 配置基准预加载时长
     * @param preloadDuration  设置预加载时长为preloadDuration ms
     */
    private fun updatePreloadConfig(player: AliListPlayer?, preloadDuration: Int) {
        val config = PreloadConfig()
        config.mPreloadDuration = preloadDuration
        player?.updatePreloadConfig(config)
    }

    /**
     * 配置预加载的数量，支持双向
     * @param forwardCount  向前预加载的数量
     * @param backwardCount  向后预加载的数量
     */
    fun setPreloadCount(forwardCount: Int, backwardCount: Int) {
        getListPlayer()?.setPreloadCount(forwardCount, backwardCount)
    }

    /**
     * 配置动态预加载的递减的offset
     */
    private fun setPreloadStrategy(player: AliListPlayer?, offset: Int) {
        player?.enablePreloadStrategy(
            IListPlayer.StrategyType.STRATEGY_DYNAMIC_PRELOAD_DURATION, true
        )
        player?.setPreloadStrategy(
            IListPlayer.StrategyType.STRATEGY_DYNAMIC_PRELOAD_DURATION,
            "{\"algorithm\": \"sub\",\"offset\": \"$offset\"}"
        )
    }
    /**
     *  添加UrlSource播放源
     * @param url
     * @param uid
     */
    fun addUrl(url: String, uid: String) {
        getListPlayer()?.addUrl(url, uid)
    }

    /**
     *  清空播放源
     */
    fun clear() {
        getListPlayer()?.clear()
    }

    /**
     *  移除某个源
     * @param uid
     */
    fun removeSource(uid: String) {
        getListPlayer()?.removeSource(uid)
    }

    /**
     *  url时使用此接口
     * @param uid
     */
    fun moveTo(uid: String) {
        getListPlayer()?.moveTo(uid)
    }

    /**
     *移动到下一个视频。 注意：只能用于url的源。这个
     */
    fun moveToNext() {
        getListPlayer()?.moveToNext()
    }

    /**
     *移动到上一个视频。注意：只能用于url的源。这个方法不适用于vid的播放
     */
    fun moveToPrev() {
        getListPlayer()?.moveToPrev()
    }

    private fun getListPlayer(): AliListPlayer? {
        return aliPlayer as? AliListPlayer
    }

}