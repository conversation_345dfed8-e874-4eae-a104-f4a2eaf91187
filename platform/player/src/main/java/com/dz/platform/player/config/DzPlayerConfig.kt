package com.dz.platform.player.config

/**
 * @Author: guyh
 * @Date: 2023/2/8 13:55
 * @Description:
 * @Version:1.0
 */
class DzPlayerConfig {
    //埋点日志上报功能默认开启，当traceId设置为DisableAnalytics时，则关闭埋点日志上报。当traceId设置为其他参数时，则开启埋点日志上报。
    //建议传递traceId，便于跟踪日志。traceId为设备或用户的唯一标识符，通常为imei或idfa。
    var traceId: String = "DisableAnalytics"

    var isAutoPlay: Boolean = false//开启自动播放，默认为关闭状态
    var isLoop: Boolean = false//开启循环播放，默认为关闭状态
    var mNetworkTimeout: Int = 5000//设置网络超时时间，单位：毫秒
    var mNetworkRetryCount: Int = 2 //设置超时重试次数

    // 如果设置了NetworkRetryCount，若此时发生网络问题，导致出现loading后，那么将会重试NetworkRetryCount次，每次的间隔时间为mNetworkTimeout。
    //如果重试多次之后，还是loading的状态，那么就会回调onError事件，此时，ErrorInfo.getCode()=ErrorCode.ERROR_LOADING_TIMEOUT。
    //如果NetworkRetryCount设置为0，当网络重试超时的时候，播放器就会回调onInfo事件，事件的InfoBean.getCode()=InfoCode.NetworkRetry。 此时，可以调用播放器的reload方法进行重新加载网络，或者进行其他的处理。
    var mMaxBufferDuration: Int = 60000//当最大缓冲区时长
    var mHighBufferDuration: Int = 2000//高缓冲时长。单位ms。当网络不好导致加载数据时，如果加载的缓冲时长到达这个值，结束加载状态。
    var mStartBufferDuration: Int = 500//起播缓冲区时长
    var mMaxBackwardBufferDurationMs: Long = 0//往前缓存的最大时长
    var mStartBufferLimit: Int = 20000//开始预加载、下载的阈值
    var mStopBufferLimit: Int = 3000// 停止预加载、下载阈值。单位ms 默认值3000

    //三个缓冲区时长的大小关系必须为：mStartBufferDuration ≤ mHighBufferDuration ≤ mMaxBufferDuration
    //当最大缓冲区时长（mMaxBufferDuration）大于5分钟时，为防止因为缓冲区过大导致的内存异常，系统将默认按5分钟生效。当mMaxBufferDuration设置超过50000 ms（即50s）时，可以启用大缓存，来降低内存占用，进一步提高播放性能
    var mClearFrameWhenStop: Boolean = false

    //开启httpDns，默认关闭
    var openHttpDns: Boolean = false

    //是否是debug模式
    var debug: Boolean = false

    //播放器适配模式
    var scaleMode: ScaleMode = ScaleMode.SCALE_ASPECT_FILL

    /**
     * 设置倍速播放
     */
    var speed: Float = 1.0f
}

enum class ScaleMode {
    SCALE_ASPECT_FIT,//设置宽高比适应（将按照视频宽高比等比缩小到view内部，不会有画面变形）
    SCALE_ASPECT_FILL,//设置宽高比填充（将按照视频宽高比等比放大，充满view，不会有画面变形）
    SCALE_TO_FILL//设置拉伸填充（如果视频宽高比例与view比例不一致，会导致画面变形）
}

enum class RotateMode {
    ROTATE_0,//设置画面顺时针旋转0度
    ROTATE_90,//设置画面顺时针旋转90度
    ROTATE_180,//设置画面顺时针旋转180度
    ROTATE_270//设置画面顺时针旋转270度
}

enum class MirrorMode {
    MIRROR_MODE_NONE,//设置无镜像
    MIRROR_MODE_HORIZONTAL,//设置水平镜像
    MIRROR_MODE_VERTICAL//设置垂直镜像
}

enum class Option {
    RENDER_FPS,
    VIDEO_BITRATE,
    DOWNLOAD_BITRATE,
    AUDIO_BITRATE,
}

enum class DzPlayerType {
    PLAYER_TYPE_NORMAL,
    PLAYER_TYPE_LIST
}