package com.dz.platform.player.listener

/**
 * @Author: guyh
 * @Date: 2023/2/8 16:55
 * @Description:
 * @Version:1.0
 */
interface OnStateChangedListener {
    companion object {
        var unknow: Int = -1
        var idle = 0
        var initalized = 1
        var prepared = 2
        var started = 3
        var paused = 4
        var stopped = 5
        var completion = 6
//        TODO@GANQUAN 当前错误和播放错误监听是否一致？
        var error = 7
    }

    fun onStateChanged(status: Int)
}