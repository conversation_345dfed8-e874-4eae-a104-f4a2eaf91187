package com.dz.platform.player.player

import android.text.TextUtils
import com.aliyun.loader.MediaLoader
import com.dz.foundation.base.utils.FileUtil
import java.io.File

/**
 * 实现添加N个视频url进行缓存。可以反复添加，反复添加的同时要对正在缓存的url进行取消。
 */
object DzMediaLoaderCacheManager {
    private val mediaLoader = MediaLoader.getInstance()
    private var urlList: List<String?> = emptyList()

    /**
     * 预加载的时长。
     */
    private const val PRELOAD_BUFFER_DURATION = 3 * 1000L

    /**
     * 当前缓存的视频索引。
     */
    private var currentIndex = 0

    init {
        BasePlayerManager.enableLocalCache(
            true,
            1024,
            FileUtil.getSDCardAndroidRootDir() + "download" + File.separator
        )
        BasePlayerManager.setCacheFileClearConfig(0, 500, 100)
        mediaLoader.setOnLoadStatusListener(object : MediaLoader.OnLoadStatusListener {
            override fun onError(p0: String?, p1: Int, p2: String?) {
                preCacheNext()
            }

            override fun onCompleted(p0: String?) {
                preCacheNext()
            }

            override fun onCanceled(p0: String?) {

            }
        })
    }

    /**
     * 缓存视频。
     */
    fun cacheUrls(urls: List<String?>) {
        stopAll()
        urlList = urls
        currentIndex = 0
        preCacheNext()
    }

    /**
     * 缓存下一个视频。
     */
    private fun preCacheNext() {
        if (currentIndex >= urlList.size) {
            return
        }
        val url = urlList.get(currentIndex)
        currentIndex += 1
        if (!TextUtils.isEmpty(url)) {
            mediaLoader.load(url, PRELOAD_BUFFER_DURATION)
        }
    }

    /**
     * 取消视频缓存
     */
    fun stopAll() {
        for (url in urlList) {
            mediaLoader.cancel(url)
        }
        urlList = emptyList()
        currentIndex = 0
    }
}