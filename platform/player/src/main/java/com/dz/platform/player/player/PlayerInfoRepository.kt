package com.dz.platform.player.player

import com.dz.foundation.base.utils.LogUtil
import java.io.Serializable

/**
 * @Author: guyh
 * @Date: 2024/10/14
 * @Description:播放器数据存储库
 * @Version:1.0
 */
class PlayerInfoRepository {

    private val videoList: MutableList<PlayerInfo> = mutableListOf()
    private var currentPosition = -1
    private val TAG = "player_info_repository_tag"

    /**
     * 释放media loader
     */
    @Synchronized
    fun release() {
        currentPosition = -1
        videoList.clear()
    }

    /**
     * 强制更新预加载列表，用于页面刷新场景
     */
    @Synchronized
    fun setUrls(videoBeanList: List<PlayerInfo>) {
        release()
        videoList.addAll(videoBeanList)
    }

    /**
     * 强制更新预加载列表，用于页面刷新场景
     */
    @Synchronized
    fun setUrl(playerInfo: PlayerInfo) {
        LogUtil.d(
            TAG,
            "addUrl   添加预加载url ==${playerInfo.url}  vid==${playerInfo.vid}"
        )
        videoList.add(playerInfo)
    }

    /**
     * 更新插入的播放链接
     */
    @Synchronized
    fun updateUrlByBookId(playerInfo: PlayerInfo) {
        LogUtil.d(
            TAG,
            "开始更新预加数据，不变更位置，列表长度==${videoList.size}"
        )
        videoList.find { it.bookId == playerInfo.bookId }?.apply {
            LogUtil.d(
                TAG,
                "更新预加数据，不变更位置，列表长度==${videoList.size}   当前剧的位置==${
                    videoList.indexOf(
                        this
                    )
                }"
            )
            vid = playerInfo.vid
            url = playerInfo.url
            index = playerInfo.index
        } ?: let {
            videoList.add(playerInfo)
        }
    }

    /**
     * 更新插入的播放链接
     */
    @Synchronized
    fun updateUrlByVid(playerInfo: PlayerInfo) {
        LogUtil.d(
            TAG,
            "开始更新预加数据，不变更位置，列表长度==${videoList.size}"
        )
        videoList.find { it.vid == playerInfo.vid }?.apply {
            LogUtil.d(
                TAG,
                "更新预加数据，不变更位置，列表长度==${videoList.size}   当前剧的位置==${
                    videoList.indexOf(
                        this
                    )
                }"
            )
            url = playerInfo.url
            index = playerInfo.index
        } ?: let {
            videoList.add(playerInfo)
        }
    }

    /**
     * 将数据列表拼接到列表后面
     */
    @Synchronized
    fun addUrls(dataList: List<PlayerInfo>) {
        videoList.addAll(dataList)
    }

    /**
     * 通过下标插入列表，自动按照index进行排序,index为空，则自动插入队尾
     */
    @Synchronized
    fun setUrlByIndex(bookId: String, vid: String, url: String?, index: Int?, playerVid: String?) {
        index?.let {
            url?.let {
                if (videoList.isEmpty()) {
                    videoList.add(PlayerInfo(bookId, vid, url, index, playerVid))
                    LogUtil.d(TAG, "addUrl   首次添加预加载url ==$url")
                } else {
                    videoList.forEachIndexed { index, playerInfo ->
                        if (playerInfo.index == index) {
                            playerInfo.url = url
                            playerInfo.vid = vid
                            LogUtil.d(
                                TAG,
                                "addUrl   预加载列表中有当前播放链接，需要更新当前播放地址  index ==$index"
                            )
                            return@let
                        }
                        if (playerInfo.index > index) {
                            videoList.add(index, PlayerInfo(bookId, vid, url, index, playerVid))
                            LogUtil.d(
                                TAG,
                                "addUrl   预加载列表中没有当前播放链接，当前位置比要插入的链接靠后，在当前位置插入  index ==$index"
                            )
                            return@let
                        }
                    }
                    videoList.add(PlayerInfo(bookId, vid, url, index, playerVid))
                    LogUtil.d(
                        TAG,
                        "addUrl   预加载列表中没有当前播放链接，直接向后添加播放链接  url ==$url"
                    )
                }
            }
        } ?: addUrl(bookId, vid, url, playerVid)
    }

    /**
     * 插入url，插入到列表最后
     */
    @Synchronized
    private fun addUrl(bookId: String, vid: String, url: String?, playerVid: String?) {
        url?.let {
            val index = videoList.indexOfFirst { it.vid == vid }
            if (index != -1) {
                videoList[index].apply {
                    this.url = url
                    this.vid = vid
                }
                LogUtil.d(
                    TAG,
                    "addUrl   预加载列表中有当前播放链接，需要更新当前播放地址  index ==$index"
                )
            } else {
                videoList.add(PlayerInfo(bookId, vid, url, 0, playerVid))
                LogUtil.d(
                    TAG,
                    "addUrl   预加载列表中没有当前播放链接，直接向后添加播放链接  url ==$url"
                )
            }
        }
    }

    /**
     * 当前播放到vid
     */
    @Synchronized
    fun moveTo(vid: String) {
        currentPosition = videoList.indexOfFirst { it.vid == vid }
    }

    @Synchronized
    fun getCurPosition(): Int {
        return currentPosition
    }

    @Synchronized
    fun getCurPositionByVid(vid: String): Int {
        return videoList.indexOfFirst { it.vid == vid }
    }

    @Synchronized
    fun getPlayerInfo(index: Int): PlayerInfo? {
        if (index >= 0 && videoList.size > index) {
            return videoList[index]
        }
        return null
    }

    @Synchronized
    fun getNextPlayerInfo(): PlayerInfo? {
        val index: Int = currentPosition + 1
        if (index >= 0 && videoList.size > index) {
            return videoList[index]
        }
        return null
    }

    @Synchronized
    fun getListSize(): Int {
        return videoList.size
    }
}

data class PlayerInfo(
    var bookId: String?,
    var vid: String,
    var url: String?,
    var index: Int,
    var playerVid: String?
) : Serializable