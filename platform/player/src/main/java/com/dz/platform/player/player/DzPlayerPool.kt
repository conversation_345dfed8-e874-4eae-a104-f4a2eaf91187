package com.dz.platform.player.player

import android.content.Context
import com.blankj.utilcode.util.ThreadUtils
import com.dz.foundation.base.utils.LogUtil
import com.dz.platform.player.config.DzPlayerConfig
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors

/**
 * @Author: guyh
 * @Date: 2024/8/8
 * @Description:播放器创建与回收池
 * @Version:1.0
 */
class DzPlayerPool {
    private val TAG = "DZ_PLAYER_POOL_TAG"

    private val INITIAL_CAPACITY = 2
    private val mPlayerPool = LinkedHashMap<Int, DzPlayer?>(INITIAL_CAPACITY, 0.75f, true)

    private var mContext: Context? = null
    private lateinit var mDzPlayerConfig: DzPlayerConfig
    private val mExecutorService: ExecutorService = ThreadUtils.getCachedPool()

    /**
     * 初始化播放器池
     */
    fun init(context: Context, config: DzPlayerConfig) {
        release()
        mContext = context
        mDzPlayerConfig = config
    }

    /**
     * 释放播放器实例和资源
     */
    fun releaseCurPlayer(keyIndex: Int?) {
        keyIndex?.let { key ->
            synchronized(mPlayerPool) {
                if (mPlayerPool.keys.contains(key)) {
                    val player = mPlayerPool.remove(key)
                    mExecutorService.execute {
                        destroyPlayerInstance(player)
                    }
                }
            }
        }
    }

    /**
     * 释放未使用的播放器实例和资源
     */
    fun releasePrePlayer(currentPlayerKey: Int?) {
        val preloadKey: MutableList<Int> = mutableListOf()
        currentPlayerKey?.let { currentKey ->
            synchronized(mPlayerPool) {
                mPlayerPool.keys.forEach { key ->
                    if (currentKey != key) {
                        preloadKey.add(key)
                    }
                }
            }
            preloadKey.forEach { key ->
                val maxPlayer = mPlayerPool.remove(key)
                // post destroy task to sub-thread
                mExecutorService.execute {
                    destroyPlayerInstance(maxPlayer)
                }
            }
        }
    }

    fun stopAllPlayer() {
        synchronized(mPlayerPool) {
            for (dzPlayer in mPlayerPool.values) {
                dzPlayer?.stop()
            }
        }
    }

    /**
     * 释放所有播放器实例和资源
     */
    fun release() {
        synchronized(mPlayerPool) {
            for (dzPlayer in mPlayerPool.values) {
                // post destroy task to sub-thread
                mExecutorService.execute {
                    destroyPlayerInstance(dzPlayer)
                }
            }
            mPlayerPool.clear()
        }
        mContext = null
    }

    fun cancelAll() {
        synchronized(mPlayerPool) {
            for (dzPlayer in mPlayerPool.values) {
                // post destroy task to sub-thread
                mExecutorService.execute {
                    destroyPlayerInstance(dzPlayer)
                }
            }
            mPlayerPool.clear()
        }
    }

    /**
     * 根据key获取播放器实例
     */
    fun getPlayer(
        key: Int,
        noJustCreate: Boolean = true,
        preload: Boolean = false,
        playerVid: String?
    ): DzPlayer? {
        synchronized(mPlayerPool) {
            var dzPlayer = mPlayerPool[key]
            if (dzPlayer != null) {
                dzPlayer.tagIndex = key
                dzPlayer.mVid = playerVid
                LogUtil.d(
                    TAG,
                    "${dzPlayer.hashCode()}     获取播放器对象，缓存中存在  position==$key"
                )
                return dzPlayer
            }
            if (!noJustCreate) {
                return null
            }
            if (mPlayerPool.size == INITIAL_CAPACITY) {
                var oldestKey = mPlayerPool.keys.iterator().next()
                if (key < oldestKey) {
                    mPlayerPool.keys.forEach {
                        if (oldestKey < it) {
                            oldestKey = it
                            LogUtil.d(
                                TAG,
                                "反向滑动，播放器池子里面的播放器达到上限了，遍历播放器对象   key==$it"
                            )
                        }
                    }
                } else {
                    mPlayerPool.keys.forEach {
                        if (oldestKey > it) {
                            oldestKey = it
                            LogUtil.d(
                                TAG,
                                "正向滑动，播放器池子里面的播放器达到上限了， key ==$key oldestKey==$oldestKey"
                            )
                        }
                    }
                }
                val oldPlayer = mPlayerPool.remove(oldestKey)
                // post destroy task to sub-thread
                mExecutorService.execute {
                    destroyPlayerInstance(oldPlayer)
                }
            }
            dzPlayer = initNewPlayerInstance(preload)
            dzPlayer.tagIndex = key
            dzPlayer.mVid = playerVid
            LogUtil.d(
                TAG,
                "${dzPlayer.hashCode()}     获取播放器对象，当前没有创建过，直接创建新的 position==$key  "
            )
            mPlayerPool[key] = dzPlayer
            return dzPlayer
        }
    }

    /**
     * 初始化并创建一个播放器实例
     */
    private fun initNewPlayerInstance(preload: Boolean): DzPlayer {
        val dzPlayer = DzPlayer()
        dzPlayer.isPreload = preload
        mContext?.let {
            dzPlayer.init(it, playerConfig = mDzPlayerConfig)
        }
        return dzPlayer
    }

    /**
     * 销毁播放器实例以及资源回收
     */
    private fun destroyPlayerInstance(dzPlayer: DzPlayer?) {
        LogUtil.d(TAG, "${dzPlayer.hashCode()}     回收播放器")
        dzPlayer?.setSurface(null)
        dzPlayer?.stop()
        dzPlayer?.release()
    }

    fun updateSpeedConfig(speed: Float) {
        mDzPlayerConfig.speed = speed
    }
}