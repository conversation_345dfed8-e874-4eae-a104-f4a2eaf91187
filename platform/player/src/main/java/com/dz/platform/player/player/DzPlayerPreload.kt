package com.dz.platform.player.player

import com.aliyun.loader.MediaLoader
import com.dz.foundation.base.utils.LogUtil
import com.dz.platform.player.PlayerKV
import java.util.LinkedList
import java.util.Queue
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors

/**
 * @Author: guyh
 * @Date: 2024/8/8
 * @Description:预加载处理类
 * @Version:1.0
 */
class DzPlayerPreload(infoRepository: PlayerInfoRepository?, forceInit: Boolean = true) {

    private val PRELOAD_BUFFER_DURATION = 3 * 1000
    private val TAG = "media_loader_preload_tag"
    private var preloadSize = 2
    private var preloadStartNum = 2

    private val mExecutorService: ExecutorService = Executors.newSingleThreadExecutor()

    private var mInfoRepository: PlayerInfoRepository? = null
    private val loadQueue: Queue<String> = LinkedList()
    private val loadedUrls = HashSet<String>()
    private val hasLoadedUrls = HashSet<String>()

    private var mMediaLoader: MediaLoader? = null
    private var hasInit: Boolean = false

    /**
     * 初始化预加载器
     */
    init {
        release()
//        initMediaLoader()
//        mInfoRepository = infoRepository
        if (forceInit || PlayerKV.mediaLoaderSwitch) {
            initMediaLoader()
            mInfoRepository = infoRepository
            LogUtil.d(TAG, "mediaLoader开关开启")
            hasInit = true
        } else {
            hasInit = false
            LogUtil.d(TAG, "mediaLoader开关关闭")
        }
    }

    fun initConfig(
        enable: Boolean,
        localCacheDir: String,
        maxCapacityMB: Long,
        freeStorageMB: Long,
        hasPlayerPreload: Boolean = true,
        preloadSize: Int = 2
    ) {
        BasePlayerManager.enableLocalCache(enable, 1024, localCacheDir)
        BasePlayerManager.setCacheFileClearConfig(0, maxCapacityMB, freeStorageMB)
        this.preloadSize = preloadSize
        preloadStartNum = if (hasPlayerPreload) 2 else 1
        LogUtil.d(TAG, "初始化配置 hasPlayerPreload==$hasPlayerPreload  preloadSize==$preloadSize")
    }

    /**
     * 释放media loader
     */
    fun release() {
        cancelAll()
        releaseMediaLoader()
        mInfoRepository?.release()
        mInfoRepository = null
        loadQueue.clear()
    }

    /**
     * 强制更新预加载列表，用于页面刷新场景
     */
    @Synchronized
    fun setUrls(videoBeanList: List<PlayerInfo>) {
        cancelAll()
        mInfoRepository?.setUrls(videoBeanList)
    }

    /**
     * 当前播放到vid
     */
    @Synchronized
    fun moveTo(vid: String) {
        val position = mInfoRepository?.getCurPositionByVid(vid) ?: 0
        LogUtil.d(TAG, "取出position ==$position    vid==$vid")
        if (position < 0 || position >= (mInfoRepository?.getListSize() ?: 0)) {
            return
        }
        val currentPosition = mInfoRepository?.getCurPosition() ?: 0
        if (position == currentPosition) {
            checkAndLoad()
            return
        }

        val newWindow = getWindowIndices(position)
        val toLoad: MutableList<Int> = LinkedList(newWindow)
        if (currentPosition < 0) {
            loadUrlsInWindow(newWindow)
        } else {
            val prevWindow = getWindowIndices(currentPosition)
            val toCancel: MutableList<Int> = LinkedList(prevWindow)
            toCancel.removeAll(newWindow)
            cancelUrlsInWindow(toCancel)
            toLoad.removeAll(prevWindow)
        }
        loadUrlsInWindow(toLoad)
        mInfoRepository?.moveTo(vid)
    }

    @Synchronized
    fun setPreloadCount(preLoadNum: Int?) {
        preLoadNum?.let {
            if (preloadSize != preLoadNum) {
                preloadSize = preLoadNum
                LogUtil.d(TAG, "setPreLoad      preloadSize==$preloadSize")
                checkAndLoad()
            }
        }
    }

    @Synchronized
    private fun checkAndLoad(): Boolean {
        if (mInfoRepository?.getCurPosition() != -1) {
            if (preloadSize < preloadStartNum) {
                return false
            }
            var result = false
            for (i in preloadStartNum..preloadSize) {
                val index = (mInfoRepository?.getCurPosition() ?: 0) + i
                if (index >= 0 && index < (mInfoRepository?.getListSize() ?: 0)) {
                    mInfoRepository?.getPlayerInfo(index)?.url?.let { urlToLoad ->
                        if (!hasLoadedUrls.contains(urlToLoad)) {
                            if (!loadedUrls.contains(urlToLoad)) {
                                loadedUrls.add(urlToLoad)
                                loadQueue.offer(urlToLoad)
                                result = true
                            }
                        }
                    }
                }
            }
            if (result) {
                loadNextInQueue()
            }
            return result
        }
        return false
    }

    /**
     * 取消所有的预加载
     */
    @Synchronized
    fun cancelAll() {
        cancel("")
        loadedUrls.clear()
        loadQueue.clear()
        mInfoRepository?.release()
        hasLoadedUrls.clear()
    }


    private fun cancelUrlsInWindow(indices: List<Int>) {
        for (index in indices) {
            mInfoRepository?.getPlayerInfo(index)?.url?.let {
                cancel(it)
            }
        }
    }

    @Synchronized
    private fun loadUrlsInWindow(indices: List<Int>) {
        for (index in indices) {
            mInfoRepository?.getPlayerInfo(index)?.url?.let { urlToLoad ->
                if (!loadedUrls.contains(urlToLoad)) {
                    loadedUrls.add(urlToLoad)
                    loadQueue.offer(urlToLoad)
                }
            }
        }
        loadNextInQueue()
    }

    private fun getWindowIndices(position: Int): List<Int> {
        val windowIndices: MutableList<Int> = LinkedList()
        if (preloadSize < 2) {
            return windowIndices
        }
        for (i in 2..preloadSize) {
            val index = position + i
            if (index >= 0 && index < (mInfoRepository?.getListSize() ?: 0)) {
                if (!mInfoRepository?.getPlayerInfo(index)?.url.isNullOrEmpty()) {
                    windowIndices.add(index)
                }
            }
        }
        return windowIndices
    }

    @Synchronized
    private fun loadNextInQueue() {
        val urlToLoad = loadQueue.poll()
        if (urlToLoad != null) {
            load(urlToLoad)
        }
    }

    fun load(videoUrl: String) {
//        mExecutorService.execute {
//            LogUtil.d(TAG, "预加载视频url==$videoUrl")
//            mMediaLoader?.load(videoUrl, PRELOAD_BUFFER_DURATION.toLong())
//            synchronized(this) {
//                loadedUrls.add(videoUrl)
//            }
        if (hasInit) {
            LogUtil.d(TAG, "mediaLoader开关开启，开启预加载")
            mExecutorService.execute {
                LogUtil.d(TAG, "预加载视频url==$videoUrl")
                mMediaLoader?.load(videoUrl, PRELOAD_BUFFER_DURATION.toLong())
                synchronized(this) {
                    loadedUrls.add(videoUrl)
                }
            }
        } else {
            LogUtil.d(TAG, "mediaLoader开关关闭，释放mediaLoader")
        }
    }

    private fun cancel(videoUrl: String) {
        mExecutorService.execute {
            mMediaLoader?.cancel(videoUrl)
            synchronized(this) {
                loadedUrls.remove(videoUrl)
                loadQueue.remove(videoUrl)
            }
        }
    }

    private fun initMediaLoader() {
        mMediaLoader = MediaLoader.getInstance()
        mMediaLoader?.setOnLoadStatusListener(object : MediaLoader.OnLoadStatusListener {
            override fun onError(url: String, code: Int, msg: String) {
                LogUtil.d(
                    TAG,
                    "预加载失败  url==$url    code===$code   msg==$msg  onError"
                )
                loadNextInQueue()
            }

            override fun onCompleted(url: String) {
                LogUtil.d(TAG, "预加载完成  url==$url")
                hasLoadedUrls.add(url)
                loadNextInQueue()
            }

            override fun onCanceled(url: String) {
                LogUtil.d(TAG, "预加载取消  url==$url")
                loadNextInQueue()
            }
        })
    }

    private fun releaseMediaLoader() {
        mMediaLoader?.setOnLoadStatusListener(null)
        mMediaLoader = null
    }
}