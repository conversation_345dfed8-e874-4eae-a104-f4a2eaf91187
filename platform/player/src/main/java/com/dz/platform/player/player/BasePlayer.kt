package com.dz.platform.player.player

import android.content.Context
import android.view.Surface
import android.view.ViewGroup
import com.aliyun.player.AliPlayer
import com.aliyun.player.AliPlayerFactory
import com.aliyun.player.IPlayer
import com.aliyun.player.bean.InfoCode
import com.aliyun.player.source.UrlSource
import com.aliyun.subtitle.SubtitleView
import com.cicada.player.utils.Logger
import com.cicada.player.utils.ass.AssSubtitleView
import com.dz.foundation.base.utils.LogUtil
import com.dz.platform.player.config.DzPlayerConfig
import com.dz.platform.player.config.DzPlayerType
import com.dz.platform.player.config.MirrorMode
import com.dz.platform.player.config.Option
import com.dz.platform.player.config.RotateMode
import com.dz.platform.player.config.ScaleMode
import com.dz.platform.player.listener.ConvertURLCallback
import com.dz.platform.player.listener.OnCompletionListener
import com.dz.platform.player.listener.OnErrorListener
import com.dz.platform.player.listener.OnInfoListener
import com.dz.platform.player.listener.OnLoadingStatusListener
import com.dz.platform.player.listener.OnPreparedListener
import com.dz.platform.player.listener.OnRenderingStartListener
import com.dz.platform.player.listener.OnSeekCompleteListener
import com.dz.platform.player.listener.OnSnapShotListener
import com.dz.platform.player.listener.OnStateChangedListener
import com.dz.platform.player.listener.OnSubtitleDisplayListener
import com.dz.platform.player.listener.OnVideoSizeChangedListener


/**
 * @Author: guyh
 * @Date: 2023/2/7 19:36
 * @Description:通用播放器
 * @Version:1.0
 */
open class BasePlayer {
    private var mPlayerState = 0
    private var hasRenderingStart = false
    private var isLoading = false//是否在加载中
    private var loadingBeginTime = 0L//视频开始卡顿时间戳
    var mUrl: String? = null
    private var currentDuration: Long = 0
    private var bufferDuration: Long = 0

    //播放完成事件监听
    var mOnCompletionListener: OnCompletionListener? = null

    //出错事件
    var mOnErrorListener: OnErrorListener? = null

    //播放器中的一些信息，包括：type包括了：循环播放开始，缓冲位置，当前播放位置，自动播放开始等
    var mOnInfoListener: OnInfoListener? = null

    //播放器的加载状态
    var mOnLoadingStatusListener: OnLoadingStatusListener? = null

    //准备成功事件
    var mOnPreparedListener: OnPreparedListener? = null

    //首帧渲染显示事件
    var mOnRenderingStartListener: OnRenderingStartListener? = null

    //拖动监听
    var mOnSeekCompleteListener: OnSeekCompleteListener? = null

    //截图监听
    var mOnSnapShotListener: OnSnapShotListener? = null

    //当前播放器状态监听
    var mOnStateChangedListener: OnStateChangedListener? = null

    /**
     * 视频size变化监听
     */
    var mVideoSizeChangedListener: OnVideoSizeChangedListener? = null

    //用于显示SRT和VTT字幕
    private var subtitleView: SubtitleView? = null

    //替换播放链接
    var mConvertURLCallback: ConvertURLCallback? = null

    //用于显示ASS和SSA字幕
    private var assSubtitleView: AssSubtitleView? = null

    protected var aliPlayer: AliPlayer? = null
    fun init(
        context: Context,
        dzPlayerType: DzPlayerType = DzPlayerType.PLAYER_TYPE_NORMAL,
        playerConfig: DzPlayerConfig,
    ) {
        aliPlayer = when (dzPlayerType) {
            DzPlayerType.PLAYER_TYPE_NORMAL -> {
                AliPlayerFactory.createAliPlayer(context)
            }

            DzPlayerType.PLAYER_TYPE_LIST -> {
                AliPlayerFactory.createAliListPlayer(context)
            }
        }
        initConfig(context, playerConfig)
    }

    private fun initConfig(context: Context, playerConfig: DzPlayerConfig) {
        kotlin.runCatching {
            //埋点日志上报功能默认开启，当traceId设置为DisableAnalytics时，则关闭埋点日志上报。当traceId设置为其他参数时，则开启埋点日志上报。
            //建议传递traceId，便于跟踪日志。traceId为设备或用户的唯一标识符，通常为imei或idfa。
//        if (playerConfig.debug) {
//            Logger.getInstance(context).logLevel = Logger.LogLevel.AF_LOG_LEVEL_TRACE
//            Logger.getInstance(context).enableConsoleLog(true)
//            LogUtil.d("BasePlayer", "开启阿里云TRACE级别日志")
//        }
            aliPlayer?.setTraceId(playerConfig.traceId)
            aliPlayer?.isAutoPlay = playerConfig.isAutoPlay
            aliPlayer?.isLoop = playerConfig.isLoop
            //先获取配置
            var config = aliPlayer?.config
            config?.let {
                //设置网络超时时间，单位：毫秒
                config.mNetworkTimeout = playerConfig.mNetworkTimeout
                //设置超时重试次数。每次重试间隔为networkTimeout。networkRetryCount=0则表示不重试，重试策略app决定，默认值为2
                config.mNetworkRetryCount = playerConfig.mNetworkRetryCount
                LogUtil.d("BASE_PLAYER", "init   mNetworkTimeout==" + playerConfig.mNetworkTimeout)
                LogUtil.d(
                    "BASE_PLAYER",
                    "init   mNetworkRetryCount==" + playerConfig.mNetworkRetryCount
                )
                // 最大缓冲区时长。单位ms。播放器每次最多加载这么长时间的缓冲数据。
                config.mMaxBufferDuration = playerConfig.mMaxBufferDuration
                //高缓冲时长。单位ms。当网络不好导致加载数据时，如果加载的缓冲时长到达这个值，结束加载状态。
                config.mHighBufferDuration = playerConfig.mHighBufferDuration
                // 起播缓冲区时长。单位ms。这个时间设置越短，起播越快。也可能会导致播放之后很快就会进入加载状态。
                config.mStartBufferDuration = playerConfig.mStartBufferDuration
//        // 停止预加载、下载阈值。单位ms
                config.mStopBufferLimit = playerConfig.mStopBufferLimit
                config.mStartBufferLimit = playerConfig.mStartBufferLimit
                //停止时清楚帧
                config.mClearFrameWhenStop = playerConfig.mClearFrameWhenStop
                //往前缓存的最大时长。单位ms。默认为0。
                config.mMaxBackwardBufferDurationMs = playerConfig.mMaxBackwardBufferDurationMs
                //设置配置给播放器
                aliPlayer?.config = config
            }
            setScaleMode(playerConfig.scaleMode)

            setSpeed(playerConfig.speed)
            aliPlayer?.setOnErrorListener { errorInfo -> //出错事件
                //此回调会在使用播放器的过程中，出现了任何错误，都会回调此接口。
                val errorCode: Int = errorInfo.code.value //错误码。
                val errorMsg = errorInfo.msg //错误描述。
                //出错后需要停止掉播放器。
//            TODO@GANQUAN
                LogUtil.d(
                    "BASE_PLAYER", "ERROR:errorCode==$errorCode\nerrorMsg==$errorMsg"
                )
                replenishLoadingEnd()
                mOnErrorListener?.onError(errorInfo.code.value, errorInfo.msg, errorInfo.extra)
            }
            BasePlayerManager.enableHttpDns(playerConfig.openHttpDns)
            aliPlayer?.setOnPreparedListener { //准备成功事件,一般调用start开始播放视频。
                mOnPreparedListener?.onPrepared()
            }
            aliPlayer?.setOnCompletionListener { //播放完成事件,一般调用stop停止播放视频。设置循环播放后，无法响应该监听，需要在setOnInfoListener中判断处理
                mOnCompletionListener?.onCompletion()
            }
            aliPlayer?.setOnInfoListener { infoBean ->
                //播放器中的一些信息，包括：type包括了：循环播放开始，缓冲位置，当前播放位置，自动播放开始等
                val code: InfoCode = infoBean.code //信息码。
                val msg = infoBean.extraMsg //信息内容。
                val value = infoBean.extraValue //信息值。

                //当前进度：InfoCode.CurrentPosition
                //当前缓存位置：InfoCode.BufferedPosition
                //TODO:
                if (code == InfoCode.LoopingStart) {
                    //循环播放开始事件。
                }
                LogUtil.d("BASE_PLAYER", "InfoCode==${code.value}\nmsg==$msg\nvalue$value")
                mOnInfoListener?.onInfo(infoBean.code.value, infoBean.extraMsg, infoBean.extraValue)

                when (infoBean.code.value) {
                    OnInfoListener.CurrentPosition -> {
                        currentDuration = infoBean.extraValue
                    }
                    OnInfoListener.BufferedPosition -> {
                        bufferDuration = infoBean.extraValue - currentDuration
                    }
                }
            }
            aliPlayer?.setOnLoadingStatusListener(object : IPlayer.OnLoadingStatusListener {
                //播放器的加载状态, 网络不佳时，用于展示加载画面。
                override fun onLoadingBegin() {
                    //开始加载。画面和声音不足以播放。
                    //一般在此处显示圆形加载。
                    //TODO:
                    LogUtil.d("BASE_PLAYER", "onLoadingBegin")
                    isLoading = true
                    loadingBeginTime = System.currentTimeMillis()
                    mOnLoadingStatusListener?.onLoadingBegin()
                }

                override fun onLoadingProgress(percent: Int, netSpeed: Float) {
                    //加载进度。百分比和网速。
                    //TODO:
                    mOnLoadingStatusListener?.onLoadingProgress(percent, netSpeed)
                }

                override fun onLoadingEnd() {
                    //结束加载。画面和声音可以播放。
                    //一般在此处隐藏圆形加载。
                    //TODO:
                    LogUtil.d("BASE_PLAYER", "onLoadingEnd")
                    if (isLoading) {
                        mOnLoadingStatusListener?.onLoadingEnd()
                    }
                    isLoading = false
                    loadingBeginTime = 0
                }
            })
            //指监听播放器的状态，onStateChanged回调参数为当前播放器状态
            aliPlayer?.setOnStateChangedListener {
                /*
              int idle = 0;
              int initalized = 1;
              int prepared = 2;
              int started = 3;
              int paused = 4;
              int stopped = 5;
              int completion = 6;
              int error = 7;
          */
                mPlayerState = it
                mOnStateChangedListener?.onStateChanged(it)
            }
            aliPlayer?.setOnRenderingStartListener {//首帧渲染显示事件
                mOnRenderingStartListener?.onRenderingStart()
                hasRenderingStart = true
            }
            aliPlayer?.setOnSeekCompleteListener {////拖动结束
                mOnSeekCompleteListener?.onSeekComplete()
            }
            aliPlayer?.setOnSnapShotListener { bitmap, width, height ->
                mOnSnapShotListener?.onSnapShot(bitmap, width, height)
            }
            aliPlayer?.setOnVideoSizeChangedListener { width, height ->
                mVideoSizeChangedListener?.onVideoSizeChanged(width, height)
            }
        }
    }

    //设置播放器播放回调监听
    fun registerConvertURLCallback() {
        AliPlayerFactory.setConvertURLCallback(object : IPlayer.ConvertURLCallback {
            override fun convertURL(srcURL: String, srcFormat: String): String? {
                return mConvertURLCallback?.convertURL(srcURL, srcFormat)
            }
        })
    }

    /**
     * 更新预加载开始与停止时间
     */
    fun updateBufferLimit(startBufferLimit: Int, stopBufferLimit: Int) {
        kotlin.runCatching {
            val config = aliPlayer?.config
            config?.let {
                config.mStartBufferLimit = startBufferLimit
                config.mStopBufferLimit = stopBufferLimit
                aliPlayer?.config = config
            }
        }
    }

    /**
     * 最大缓冲区时长
     */
    fun updateMaxBufferDuration(duration: Int) {
        kotlin.runCatching {
            val config = aliPlayer?.config
            config?.let {
                config.mMaxBufferDuration = duration
                aliPlayer?.config = config
            }
        }
    }

    /**
     * 更新开始预加载、下载的阈值。单位ms
     */
    fun updateStartBufferLimit(startBufferLimit: Int) {
        kotlin.runCatching {
            var config = aliPlayer?.config
            config?.let {
                config.mStartBufferLimit = startBufferLimit
                //设置配置给播放器
                aliPlayer?.config = config
            }
        }
    }

    /**
     * 获取当前播放器的剩余缓存进度
     */
    fun getCurBuffer(): Long {
        LogUtil.d("player_cur_buffer", "BasePlayer：当前播放器剩余缓存== $bufferDuration")
        return bufferDuration
    }

    fun setScaleMode(scaleMode: ScaleMode) {
        //填充
        when (scaleMode) {
            ScaleMode.SCALE_ASPECT_FIT -> {
                kotlin.runCatching {
                    aliPlayer?.scaleMode = IPlayer.ScaleMode.SCALE_ASPECT_FIT
                }
            }

            ScaleMode.SCALE_ASPECT_FILL -> {
                kotlin.runCatching {
                    aliPlayer?.scaleMode = IPlayer.ScaleMode.SCALE_ASPECT_FILL
                }
            }

            ScaleMode.SCALE_TO_FILL -> {
                kotlin.runCatching {
                    aliPlayer?.scaleMode = IPlayer.ScaleMode.SCALE_TO_FILL
                }
            }
        }
    }

    fun isLoop(): Boolean {
        return runCatching { aliPlayer?.isLoop }
            .getOrNull() ?: false
    }

    fun isLoop(isLoop: Boolean) {
        kotlin.runCatching {
            aliPlayer?.isLoop = isLoop
        }
    }

    fun setRotateMode(rotateMode: RotateMode) {
        //旋转
        when (rotateMode) {
            RotateMode.ROTATE_0 -> {
                aliPlayer?.rotateMode = IPlayer.RotateMode.ROTATE_0
            }

            RotateMode.ROTATE_90 -> {
                aliPlayer?.rotateMode = IPlayer.RotateMode.ROTATE_90
            }

            RotateMode.ROTATE_180 -> {
                aliPlayer?.rotateMode = IPlayer.RotateMode.ROTATE_180
            }

            RotateMode.ROTATE_270 -> {
                aliPlayer?.rotateMode = IPlayer.RotateMode.ROTATE_270
            }
        }
    }

    fun setMirrorMode(mirrorMode: MirrorMode) {
        //镜像
        when (mirrorMode) {
            MirrorMode.MIRROR_MODE_NONE -> {
                aliPlayer?.mirrorMode = IPlayer.MirrorMode.MIRROR_MODE_NONE
            }

            MirrorMode.MIRROR_MODE_HORIZONTAL -> {
                aliPlayer?.mirrorMode = IPlayer.MirrorMode.MIRROR_MODE_HORIZONTAL
            }

            MirrorMode.MIRROR_MODE_VERTICAL -> {
                aliPlayer?.mirrorMode = IPlayer.MirrorMode.MIRROR_MODE_VERTICAL
            }
        }
    }

    open fun bindUrl(url: String) {
        kotlin.runCatching {
            val urlSource = UrlSource()
            urlSource.uri = url
            mUrl = url
            aliPlayer?.setDataSource(urlSource)
        }
    }

    fun prepare() {
        kotlin.runCatching {
            aliPlayer?.prepare()//调用aliPlayer?.prepare()开始读取并解析数据
        }
    }

    fun start() {
        kotlin.runCatching {
            aliPlayer?.start()
        }
    }

    fun pause() {
        kotlin.runCatching {
            aliPlayer?.pause()
        }
    }

    fun stop() {
        kotlin.runCatching {
            LogUtil.d("base_player_load_tag", "player  stop()  isLoading==${isLoading}")
            replenishLoadingEnd()
            aliPlayer?.stop()
            hasRenderingStart = false
            mUrl = null
            currentDuration = 0
            bufferDuration = 0
        }
    }

    /**
     * 加载结束补偿
     */
    fun replenishLoadingEnd(){
        if (isLoading && loadingBeginTime != 0L) {
            LogUtil.d("base_player_load_tag", "replenishLoadingEnd  补偿卡顿结束打点")
            mOnLoadingStatusListener?.onLoadingEnd()
            isLoading = false
            loadingBeginTime = 0
        }
    }

    fun release() {
        kotlin.runCatching {
            aliPlayer?.release()
            mPlayerState = -1
            aliPlayer = null
            hasRenderingStart = false
            mUrl = null
            currentDuration = 0
            bufferDuration = 0
            mOnStateChangedListener?.onStateChanged(mPlayerState)
        }
    }

    /**
     *  获取视频总时长  需要在视频加载完成以后才可以获取到，可以在onPrepared事件后获取
     * @return
     */
    fun getDuration(): Long {
        kotlin.runCatching {
            return aliPlayer?.duration ?: 0L
        }
        return 0L
    }

    /**
     * 播放器卡顿时长
     */
    fun getLoadingTime(): Long {
        if (loadingBeginTime != 0L) {
            return System.currentTimeMillis() - loadingBeginTime
        }
        return 0L
    }

    /**
     * 音量调节
     * @param volume 指调节音量大小，支持0～2倍，当音量大于1时，可能出现噪音，不推荐使用
     */
    fun setVolume(volume: Float) {
        aliPlayer?.volume = volume
    }

    fun getVolume(): Float {
        kotlin.runCatching {
            return aliPlayer?.volume ?: 0F
        }
        return 0F
    }

    /**
     *  静音设置
     * @param mute
     */
    fun setMute(mute: Boolean) {
        kotlin.runCatching {
            aliPlayer?.isMute = mute
        }
    }

    /**
     * 是否是静音
     * @return
     */
    fun isMute(): Boolean {
        kotlin.runCatching {
            return aliPlayer?.isMute ?: false
        }
        return false
    }

    /**
     *  跳转到某个时刻附近关键帧进行播放
     * @param position
     */
    fun seekTo(position: Long) {
        kotlin.runCatching {
            //非精准seek。
            aliPlayer?.seekTo(position)
//        aliPlayer?.seekTo(position, IPlayer.SeekMode.Inaccurate)
        }
    }

    /**
     *  精准跳转到某个时刻进行播放
     * @param position
     */
    fun seekToAccurate(position: Long) {
        kotlin.runCatching {
            //精准seek。
            aliPlayer?.seekTo(position, IPlayer.SeekMode.Accurate)
        }
    }

    /**
     * 前进xxx毫秒
     * @param step Long
     */
    fun forward(step: Long) {
        aliPlayer?.apply {
            seekTo(currentDuration + step, IPlayer.SeekMode.Accurate)
        }
    }

    /**
     * 后退xxx毫秒
     * @param step Long
     */
    fun backward(step: Long) {
        aliPlayer?.apply {
            val target = if (currentDuration - step < 0) 0 else currentDuration - step
            seekTo(target, IPlayer.SeekMode.Accurate)
        }
    }

    /**
     * 显示首帧
     */
    fun showFirstFrame() {
        kotlin.runCatching {
            aliPlayer?.setOption(IPlayer.ALLOW_PRE_RENDER, 1)
        }

    }

    /**
     * 设置开始播放的时间（精准时刻）
     */
    fun setStartTime(position: Long) {
        kotlin.runCatching {
            aliPlayer?.setStartTime(position, IPlayer.SeekMode.Accurate)
        }
    }

    /**
     *  倍速播放
     * @param speed 支持0.5～5倍速的播放，通常按0.5的倍数来设置，例如0.5倍、1倍、1.5倍等
     */
    fun setSpeed(speed: Float) {
        kotlin.runCatching {
            aliPlayer?.speed = speed
        }
    }

    fun getSpeed(): Float {
        kotlin.runCatching {
            return aliPlayer?.speed ?: 0F
        }
        return 0F
    }

    /**
     *
     * @param surface
     */
    fun setSurface(surface: Surface?) {
        kotlin.runCatching {
            aliPlayer?.setSurface(surface)
        }
    }

    fun surfaceChanged() {
        kotlin.runCatching {
            aliPlayer?.surfaceChanged()
        }
    }

    /**
     * 获取顺时针旋转角度
     */
    fun getRotateMode(): Int {
        return when (aliPlayer?.rotateMode ?: IPlayer.RotateMode.ROTATE_0) {
            IPlayer.RotateMode.ROTATE_0 -> {
                0
            }

            IPlayer.RotateMode.ROTATE_90 -> {
                90
            }

            IPlayer.RotateMode.ROTATE_180 -> {
                180
            }

            IPlayer.RotateMode.ROTATE_270 -> {
                270
            }
        }
    }

    /**
     *  获取实时渲染帧率、音视频码率、网络下行码率
     * @param option
     * @return
     */
    fun getOption(option: Option): Float {
        kotlin.runCatching {
            return when (option) {
                Option.RENDER_FPS -> {
                    //获取当前渲染的帧率，数据类型为Float。
                    aliPlayer?.getOption(IPlayer.Option.RenderFPS) as Float
                }

                Option.VIDEO_BITRATE -> {
                    //获取当前播放的视频码率，数据类型为Float，单位为bps。
                    aliPlayer?.getOption(IPlayer.Option.VideoBitrate) as Float
                }

                Option.AUDIO_BITRATE -> {
                    //获取当前播放的音频码率，数据类型为Float，单位为bps。
                    aliPlayer?.getOption(IPlayer.Option.AudioBitrate) as Float
                }

                Option.DOWNLOAD_BITRATE -> {
//                TODO@GANQUAN 播放报错需要进行当前播放环境的数据
                    //获取当前的网络下行码率，数据类型为Float，单位为bps。
                    aliPlayer?.getOption(IPlayer.Option.DownloadBitrate) as Float
                }
            }
        }
        return 0F
    }

    fun snapshot() {
        kotlin.runCatching {
            aliPlayer?.snapshot()
        }
    }

    /**
     *  创建SRT和VTT字幕显示view
     * @param viewGroup
     * @param context
     */
    fun initSubtitleView(viewGroup: ViewGroup, context: Context) {
        //用于显示SRT和VTT字幕
        subtitleView = SubtitleView(context)
        //将字幕View添加到布局视图中
        viewGroup.addView(subtitleView)
    }

    /**
     *  创建ASS和SSA字幕显示view
     * @param viewGroup
     * @param context
     */
    fun initAssSubtitleView(viewGroup: ViewGroup, context: Context) {
        //用于显示ASS和SSA字幕
        assSubtitleView = AssSubtitleView(context)
        //将字幕View添加到布局视图中
        viewGroup.addView(assSubtitleView)
    }

    fun setOnSubtitleDisplayListener(isAss: Boolean, listener: OnSubtitleDisplayListener?) {
        aliPlayer?.setOnSubtitleDisplayListener(object : IPlayer.OnSubtitleDisplayListener {
            override fun onSubtitleExtAdded(trackIndex: Int, url: String?) {
                listener?.onSubtitleExtAdded(trackIndex, url)
            }

            override fun onSubtitleShow(trackIndex: Int, id: Long, data: String?) {
                if (isAss) {
                    // ass 字幕
                    assSubtitleView?.show(id, data)
                } else {
                    // srt 字幕
                    val subtitle = SubtitleView.Subtitle()
                    subtitle.id = id.toString() + ""
                    subtitle.content = data
                    subtitleView?.show(subtitle)
                }

            }

            override fun onSubtitleHide(trackIndex: Int, id: Long) {
                if (isAss) {
                    // ass 字幕
                    assSubtitleView?.dismiss(id)
                } else {
                    // srt 字幕
                    subtitleView?.dismiss(id.toString() + "")
                }
            }

            override fun onSubtitleHeader(trackIndex: Int, header: String?) {
                listener?.onSubtitleHeader(trackIndex, header)
            }
        })
    }

    /**
     *  添加字幕
     * @param url
     */
    fun addExtSubtitle(url: String) {
        aliPlayer?.addExtSubtitle(url)
    }

    /**
     * 显示隐藏字幕(在收到onSubtitleExtAdded回调后，可通过如下方法进行显示或隐藏字幕)
     * @param trackIndex 传入字幕索引
     * @param showExtSubtitle true：表示显示传入的字幕；false：表示隐藏传入的字幕
     */
    fun selectExtSubtitle(trackIndex: Int, showExtSubtitle: Boolean) {
        aliPlayer?.selectExtSubtitle(trackIndex, showExtSubtitle)
    }

    fun isPlaying(): Boolean {
        return mPlayerState == OnStateChangedListener.started
    }

    fun isPrepared(): Boolean {
        return (mPlayerState > OnStateChangedListener.initalized && mPlayerState < OnStateChangedListener.stopped) || mPlayerState == OnStateChangedListener.completion
    }

    fun hasRenderingStart(): Boolean {
        return hasRenderingStart
    }

    fun getPlayerState(): Int {
        return mPlayerState
    }
}