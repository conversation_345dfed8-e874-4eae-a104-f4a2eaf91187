package com.dz.platform.share.impl.wx;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.net.Uri;
import android.text.TextUtils;


import androidx.annotation.NonNull;
import androidx.core.content.FileProvider;

import com.dz.foundation.base.manager.task.TaskManager;
import com.dz.foundation.base.module.AppModule;
import com.dz.platform.common.toast.ToastManager;
import com.dz.platform.share.base.ShareBean;
import com.dz.platform.share.base.ShareCallback;
import com.dz.platform.share.base.ShareErrorData;
import com.dz.platform.share.base.ShareMsgAction;
import com.dz.platform.share.impl.ImageUtils;
import com.tencent.mm.opensdk.modelmsg.SendMessageToWX;
import com.tencent.mm.opensdk.modelmsg.WXImageObject;
import com.tencent.mm.opensdk.modelmsg.WXMediaMessage;
import com.tencent.mm.opensdk.modelmsg.WXTextObject;
import com.tencent.mm.opensdk.modelmsg.WXWebpageObject;
import com.tencent.mm.opensdk.openapi.IWXAPI;
import com.tencent.mm.opensdk.openapi.WXAPIFactory;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;

public class WXShareUtils {
    public static void wechatShare(Activity activity, String wxAppId, ShareBean shareBean, ShareCallback callback) {
        if (!TextUtils.isEmpty(wxAppId)) {
            IWXAPI api = WXAPIFactory.createWXAPI(activity, wxAppId);
            // 将该app注册到微信
            api.registerApp(wxAppId);
            if (!isWXAppInstalled(api)) {
                callback.onFail(new ShareErrorData(ShareErrorData.RESULT_CLIENT_NOT_INSTALL));
                return;
            }
            if (!checkWXSupport(api)) {
                callback.onFail(new ShareErrorData(ShareErrorData.RESULT_VERSION_NOT_SUPPORTED));
                return;
            }
            int contentType = shareBean.contentType;
            if (contentType == ShareBean.SHARE_WEB) {
                doWechatShareForWeb(activity, api, shareBean, callback);
            } else if (contentType == ShareBean.SHARE_IMG) {
                doWechatShareForImg(activity, api, shareBean, callback);
            } else if (contentType == ShareBean.SHARE_TEXT) {
                doWechatShareForText(activity, api, shareBean, callback);
            }

        } else {
            callback.onFail(new ShareErrorData(ShareErrorData.RESULT_STAY_TUNED));
        }

    }

    /**
     * 微信分享 网页
     */
    private static void doWechatShareForWeb(Activity activity, IWXAPI api, ShareBean shareBean, ShareCallback callback) {

        WXWebpageObject webPage = new WXWebpageObject();
        webPage.webpageUrl = shareBean.shareUrl;

        //WxMsg
        WXMediaMessage msg = new WXMediaMessage(webPage);
        msg.title = shareBean.title;
        msg.description = shareBean.description;
        byte[] image = ImageUtils.compressBitmap(activity, shareBean.shareBitmap, 30, shareBean.isRecycler);

        msg.thumbData = image;

        //WxRequest
        SendMessageToWX.Req req = new SendMessageToWX.Req();
        req.transaction = buildTransaction("webpage");
        req.message = msg;
        sendMessageToWx(activity, api, req, shareBean, callback);
    }


    /**
     * 微信分享 文本
     */
    private static void doWechatShareForText(Activity activity, IWXAPI api, ShareBean shareBean, ShareCallback callback) {

        String text = shareBean.title;
        //初始化一个 WXTextObject 对象，填写分享的文本内容
        WXTextObject textObj = new WXTextObject();
        textObj.text = text;

        //用 WXTextObject 对象初始化一个 WXMediaMessage 对象
        WXMediaMessage msg = new WXMediaMessage();
        msg.mediaObject = textObj;
        msg.description = text;

        SendMessageToWX.Req req = new SendMessageToWX.Req();
        req.transaction = buildTransaction("text");
        req.message = msg;
        //调用api接口，发送数据到微信
        api.sendReq(req);

        sendMessageToWx(activity, api, req, shareBean, callback);
    }


    /**
     * 微信分享 图片
     */
    private static void doWechatShareForImg(Activity activity, IWXAPI api, ShareBean shareBean, ShareCallback callback) {
        Bitmap bitmap = shareBean.shareBitmap;
        if (bitmap == null) {
            ToastManager.showToast("分享失败");
            return;
        }
        if (checkAndroidNotBelowN()) {
            getShareDataFilePath(AppModule.INSTANCE.getApplication(), bitmap, new WxCallBack() {
                @Override
                public void onResult(String str) {
                    WXImageObject imageObject = new WXImageObject();
                    imageObject.setImagePath(str);
                    WXMediaMessage msg = new WXMediaMessage(imageObject);
                    Bitmap thumbBmp = Bitmap.createScaledBitmap(bitmap, 135, 240, true);
                    msg.thumbData = ImageUtils.compressBitmap(activity, thumbBmp, 100, true);
                    SendMessageToWX.Req req = new SendMessageToWX.Req();
                    req.transaction = buildTransaction("img");
                    req.message = msg;
                    sendMessageToWx(activity, api, req, shareBean, callback);
                }
            });
        } else {
            WXImageObject imgObj = new WXImageObject(bitmap);
            WXMediaMessage msg = new WXMediaMessage();
            msg.mediaObject = imgObj;
            Bitmap thumbBmp = Bitmap.createScaledBitmap(bitmap, 135, 240, true);
            msg.thumbData = ImageUtils.compressBitmap(activity, thumbBmp, 100, true);
            SendMessageToWX.Req req = new SendMessageToWX.Req();
            req.transaction = buildTransaction("img");
            req.message = msg;
            sendMessageToWx(activity, api, req, shareBean, callback);
        }
        //说是可以等系统回收，不用做处理
//        if (shareBean.isRecycler) {
//            bitmap.recycle();
//        }
    }

    public static void getShareDataFilePath(@NonNull final Context context, @NonNull final Bitmap bitmap, WxCallBack callback) {
        TaskManager.Companion.ioTask(continuation -> {
            BufferedOutputStream outputStream = null;
            try {
                String fileName = "img_" + System.currentTimeMillis() + ".jpg";
                final File screenshotsDir = new File(context.getExternalFilesDir(null), "shareData");

                if (!screenshotsDir.exists()) { //如果该文件夹不存在，则进行创建
                    screenshotsDir.mkdirs();//创建文件夹
                }else {
                    for (File file : screenshotsDir.listFiles()) {
                        if (file.isFile()){
                            file.delete(); // 删除所有文件
                        }
                    }
                }
                File screenshotFile = new File(screenshotsDir, fileName);
                outputStream = new BufferedOutputStream(new FileOutputStream(screenshotFile));
                bitmap.compress(Bitmap.CompressFormat.JPEG, 100, outputStream);
                outputStream.flush();
                callback.onResult(getFileUri(context,screenshotFile));
            } catch (final IOException e) {
                e.printStackTrace();
            } finally {
                if (outputStream != null) {
                    try {
                        outputStream.close();
                    } catch (final IOException ignored) {
                        ignored.printStackTrace();
                    }
                }

            }
            return null;
        });
    }

    public static String getFileUri(Context context, File file) {
        if (file == null || !file.exists()) {
            return null;
        }

        Uri contentUri = FileProvider.getUriForFile(context,
                context.getPackageName() + ".fileprovider",  // 要与`AndroidManifest.xml`里配置的`authorities`一致，假设你的应用包名为com.example.app
                file);

        // 授权给微信访问路径
        context.grantUriPermission("com.tencent.mm",  // 这里填微信包名
                contentUri, Intent.FLAG_GRANT_READ_URI_PERMISSION);

        return contentUri.toString();   // contentUri.toString() 即是以"content://"开头的用于共享的路径
    }

    public static boolean checkAndroidNotBelowN() {
        return android.os.Build.VERSION.SDK_INT >= 30;
    }

    private static void sendMessageToWx(Activity activity, IWXAPI api, SendMessageToWX.Req req, ShareBean shareBean, ShareCallback callback) {
        boolean isShareToFriends = shareBean.shareType == ShareBean.SHARE_FRIENDS;
        req.scene = (isShareToFriends ? SendMessageToWX.Req.WXSceneSession : SendMessageToWX.Req.WXSceneTimeline);
        int msgAction = shareBean.isReplaceMode ? ShareMsgAction.SHARE_STYLE_REPLACE : ShareMsgAction.SHARE_STYLE_NORMAL;
        libWxShare(activity, api, msgAction, req, callback);
    }

    private static String buildTransaction(final String type) {
        return (type == null) ? String.valueOf(System.currentTimeMillis()) : type + System.currentTimeMillis();
    }


    private static boolean isWXAppInstalled(IWXAPI api) {
        if (!api.isWXAppInstalled()) {
            return false;
        }
        return true;
    }

    private static boolean checkWXSupport(IWXAPI api) {
        if (api.getWXAppSupportAPI() < 620823552) {
            return false;
        }
        return true;
    }

    /**
     * 包含替身模式 正式模式的分享 封装
     *
     * @param context
     * @param api
     * @param style   style =1 替身模式 其他为sdk模式 默认style = -1
     * @param req
     */
    private static void libWxShare(Context context, IWXAPI api, int style, SendMessageToWX.Req req, ShareCallback callback) {
        try {
            boolean b = api.sendReq(req);
            if (!b) {
                callback.onFail(new ShareErrorData(ShareErrorData.RESULT_SHARE_FAIL));
            }
        } catch (Throwable e) {
            e.printStackTrace();
            callback.onFail(new ShareErrorData(ShareErrorData.RESULT_SHARE_FAIL));
        } finally {
            //回收资源
        }
    }

}
