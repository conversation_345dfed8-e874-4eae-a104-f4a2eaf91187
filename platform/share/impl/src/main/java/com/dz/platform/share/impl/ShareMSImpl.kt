package com.dz.platform.share.impl

import android.app.Activity
import com.dz.platform.share.base.ShareBean
import com.dz.platform.share.base.ShareCallback
import com.dz.platform.share.base.ShareMS
import com.dz.platform.share.impl.wx.WXShareUtils

class ShareMSImpl : ShareMS {
    override fun doShare(activity: Activity, shareBean: ShareBean, callback: ShareCallback) {
        WXShareUtils.wechatShare(activity,shareBean.appId,shareBean,callback)
    }

}