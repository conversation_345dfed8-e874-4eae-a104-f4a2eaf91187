package com.dz.platform.share.impl;

import android.app.Activity;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.util.DisplayMetrics;


import com.dz.foundation.base.utils.LogUtil;

import java.io.ByteArrayOutputStream;
import java.io.IOException;

public class ImageUtils {

    private ImageUtils() {
    }

    /**
     * 压缩图片到指定大小
     *
     * @param context       Activity
     * @param bitmap
     * @param specifiedSize 单位 K
     * @return byte[]
     */
    public static byte[] compressBitmap(Activity context, Bitmap bitmap, int specifiedSize, boolean isRecycler) {
        if (bitmap == null || bitmap.isRecycled()) {
            return null;
        }
        DisplayMetrics dm = new DisplayMetrics();
        context.getWindowManager().getDefaultDisplay().getMetrics(dm);
        float hh = dm.heightPixels;
        float ww = dm.widthPixels;
        BitmapFactory.Options opts = new BitmapFactory.Options();
        opts.inJustDecodeBounds = true;
        opts.inJustDecodeBounds = false;
        int w = opts.outWidth;
        int h = opts.outHeight;
        int size = 0;
        if (w <= ww && h <= hh) {
            size = 1;
        } else {
            double scale = w >= h ? w / ww : h / hh;
            double log = Math.log(scale) / Math.log(2);
            double logCeil = Math.ceil(log);
            size = (int) Math.pow(2, logCeil);
        }
        opts.inSampleSize = size;
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        int quality = 100;
        bitmap.compress(Bitmap.CompressFormat.PNG, quality, baos);
        System.out.println(baos.toByteArray().length);
        while (baos.toByteArray().length > specifiedSize * 1024 && quality >= 10) {//不能无限制的压缩图片，所以添加了质量大于10的判断
            baos.reset();
            bitmap.compress(Bitmap.CompressFormat.JPEG, quality, baos);
            quality -= 5;
            LogUtil.d("ImageUtils","baos.toByteArray().length:" + baos.toByteArray().length);
        }
        try {
            return baos.toByteArray();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (baos != null) {
                    baos.flush();
                    baos.close();
                }
                if (isRecycler) {
                    bitmap.recycle();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

}
