package com.dz.platform.share.base

/**
 * @Author: guyh
 * @Date: 2023/6/5 16:44
 * @Description:
 * @Version:1.0
 */
data class ShareErrorData(var resultCode: Int = -1) {
    companion object {
        /**
         * 最终返回给调用者的结果
         */
        const val RESULT_UNKNOWN = 0  // 未知
        const val RESULT_SHARE_NOT_ENOUGH_TIME = 1//时间不够
        const val RESULT_SHARE_FAIL = 2  // 分享失败
        const val RESULT_VERSION_NOT_SUPPORTED = 4  // 您的微信版本过低，不支持分享，请升级微信版本
        const val RESULT_STAY_TUNED = 5//分享功能正在争取中，敬请期待
        const val RESULT_CLIENT_NOT_INSTALL = 6  // 用户未安装客户端

    }

    fun getResultMsg(): String {
        return when (resultCode) {
            RESULT_UNKNOWN -> "未知错误"
            RESULT_SHARE_FAIL -> "分享失败"
            RESULT_VERSION_NOT_SUPPORTED -> "您的微信版本过低，不支持分享，请升级微信版本"
            RESULT_STAY_TUNED -> "分享功能正在争取中，敬请期待"
            RESULT_CLIENT_NOT_INSTALL -> "您暂未安装微信"
            RESULT_SHARE_NOT_ENOUGH_TIME-> "分享失败"
            else -> "未知错误"
        }
    }
}