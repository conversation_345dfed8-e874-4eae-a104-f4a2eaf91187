package com.dz.platform.share.base

import android.app.Activity
import com.dz.platform.common.service.IModuleService
import com.dz.platform.common.service.get

interface ShareMS : IModuleService {

    fun doShare(activity: Activity, shareBean:ShareBean, callback: ShareCallback)

    companion object {
        private val instance = ShareMS::class.java.get()
        fun get(): ShareMS? {
            return instance
        }
    }
}