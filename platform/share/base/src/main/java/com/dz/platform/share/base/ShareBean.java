package com.dz.platform.share.base;

import android.graphics.Bitmap;

public class ShareBean {

    /**
     * 分享网页
     */
    public static final int SHARE_WEB = 1;
    /**
     * 分享图片
     */
    public static final int SHARE_IMG = SHARE_WEB + 1;


    /**
     * 分享文本
     */
    public static final int SHARE_TEXT = SHARE_IMG + 1;

    /**
     * 分享到朋友圈
     */
    public static final int SHARE_FRIEND_CIRCLE = SHARE_IMG + 1;
    /**
     * 分享到好友
     */
    public static final int SHARE_FRIENDS = SHARE_FRIEND_CIRCLE + 1;

    /**
     * 分享类型
     * {@link #SHARE_FRIEND_CIRCLE}
     * {@link #SHARE_FRIENDS}
     */
    public int shareType;

    public String appId;
    /**
     * 分享内容类型
     * {@link #SHARE_WEB}
     * {@link #SHARE_IMG}
     */
    public int contentType;
    public String title;
    public String description;
    public String shareUrl;     //分享链接
    public Bitmap shareBitmap;  //分享图片Bitmap
    public String imgUrl;       //分享图片Url
    public boolean isRecycler;      //是否回收Bitmap
    public boolean isReplaceMode;   //1是启动替身模式 其他是sdk分享

    public ShareBean() {

    }

}
