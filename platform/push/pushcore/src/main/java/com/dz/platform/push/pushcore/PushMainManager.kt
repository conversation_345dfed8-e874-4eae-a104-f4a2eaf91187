package com.dz.platform.push.pushcore

import android.content.Context
import android.util.Log
import com.dz.foundation.base.service.DzServiceManager
import com.dz.platform.push.pushbase.HonorMS
import com.dz.platform.push.pushbase.HuaweiMS
import com.dz.platform.push.pushbase.OppoMS
import com.dz.platform.push.pushbase.PushOnRegisterCallback
import com.dz.platform.push.pushbase.VivoMS
import com.dz.platform.push.pushbase.XiaomiMS

object PushMainManager {
    const val TAG = "PUSH"
    private var hasInit = false

    fun initPush(context: Context, registerCallback: PushOnRegisterCallback) {
        Log.d(TAG, "push register...")
        if (hasInit) {
            return
        }
        hasInit = true
        try {
            val oppoPush = DzServiceManager.getService(OppoMS::class.java)
            Log.d(TAG, "get oppoPush is : $oppoPush")
            oppoPush?.run {
                if (isSupportPush(context)) {
                    Log.d(TAG, "oppoPush is support!")
                    setRegisterCallback(registerCallback)
                    register(context)
                    return
                }
            }

            val vivoPush = DzServiceManager.getService(VivoMS::class.java)
            Log.d(TAG, "get vivoPush is : $vivoPush")
            vivoPush?.run {
                if (isSupportPush(context)) {
                    Log.d(TAG, "vivoPush is support!")
                    setRegisterCallback(registerCallback)
                    register(context)
                    return
                }
            }

            val miPush = DzServiceManager.getService(XiaomiMS::class.java)
            Log.d(TAG, "get miPush is : $miPush")
            miPush?.run {
                if (isSupportPush(context)) {
                    Log.d(TAG, "miPush is support!")
                    setRegisterCallback(registerCallback)
                    register(context)
                    return
                }
            }

            val honorPush = DzServiceManager.getService(HonorMS::class.java)
            Log.d(TAG, "get honorPush is : $honorPush")
            honorPush?.run {
                if (isSupportPush(context)) {
                    Log.d(TAG, "honorPush is support!")
                    setRegisterCallback(registerCallback)
                    register(context)
                    return
                }
            }

            val hwPush = DzServiceManager.getService(HuaweiMS::class.java)
            Log.d(TAG, "get hwPush is : $hwPush")
            hwPush?.run {
                if (isSupportPush(context)) {
                    Log.d(TAG, "hwPush is support!")
                    setRegisterCallback(registerCallback)
                    register(context)
                    return
                }
            }

        } catch (e: Exception) {
            e.printStackTrace()
            Log.d(TAG, "出现异常: ${e.message}")
        }
    }
}