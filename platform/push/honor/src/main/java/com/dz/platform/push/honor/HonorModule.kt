package com.dz.platform.push.honor

import com.dz.foundation.base.module.LibModule
import com.dz.foundation.base.module.ModulePriority
import com.dz.foundation.base.service.DzServiceManager
import com.dz.platform.push.pushbase.HonorMS

class HonorModule : LibModule() {
    override fun getPriority(): Int {
        return ModulePriority.LOW
    }

    override fun onCreate() {
        DzServiceManager.registerService(HonorMS::class.java, HonorPushManager::class.java)
    }

}