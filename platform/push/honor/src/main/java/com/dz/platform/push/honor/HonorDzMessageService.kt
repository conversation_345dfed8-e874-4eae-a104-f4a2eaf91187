package com.dz.platform.push.honor

import com.hihonor.push.sdk.HonorMessageService
import com.hihonor.push.sdk.HonorPushDataMsg

class HonorDzMessageService : HonorMessageService() {
    override fun onNewToken(pushToken: String?) {
        super.onNewToken(pushToken)
        if (!pushToken.isNullOrEmpty()) {
            HonorPushManager.pushToken = pushToken
        }
    }

    override fun onMessageReceived(msg: HonorPushDataMsg?) {
        // 处理收到的透传消息。
    }

}