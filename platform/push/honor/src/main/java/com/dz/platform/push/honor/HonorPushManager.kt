package com.dz.platform.push.honor

import android.content.Context
import com.dz.foundation.base.utils.LogUtil
import com.dz.platform.push.pushbase.HonorMS
import com.dz.platform.push.pushbase.PushOnRegisterCallback
import com.dz.platform.push.pushbase.PushType
import com.hihonor.push.sdk.HonorPushCallback
import com.hihonor.push.sdk.HonorPushClient
class HonorPushManager : HonorMS {
    companion object {
        var pushToken: String? = null
    }

    private var registerCallback: PushOnRegisterCallback? = null

    override fun setRegisterCallback(registerCallback: PushOnRegisterCallback) {
        this.registerCallback = registerCallback
    }

    override fun register(context: Context) {
        if (!pushToken.isNullOrEmpty()) {
            registerCallback?.onReceiveRegisterId(PushType.PUSH_TYPE_HONOR, pushToken!!)
        } else {
            getToken(context)
        }
    }

    override fun isSupportPush(context: Context): Boolean {
        val isSupport = HonorPushClient.getInstance().checkSupportHonorPush(context)
        if (isSupport) {
            HonorPushClient.getInstance().init(context, true)
        }

        return isSupport
    }

//    fun setNotificationOpenStatus(boolean: Boolean, callback: NotificationOpenCallback?) {
//        if (boolean) {
//            //打开通知栏消息状态
//            HonorPushClient.getInstance()
//                .turnOnNotificationCenter(object : HonorPushCallback<Void?> {
//                    override fun onSuccess(aVoid: Void?) {
//                        callback?.onSuccess()
//                    }
//
//                    override fun onFailure(errorCode: Int, errorString: String) {
//                        callback?.onFailure()
//                    }
//                })
//        } else {
//            //设置通知栏消息不显示
//            HonorPushClient.getInstance()
//                .turnOffNotificationCenter(object : HonorPushCallback<Void?> {
//                    override fun onSuccess(aVoid: Void?) {
//                        callback?.onSuccess()
//                    }
//
//                    override fun onFailure(errorCode: Int, errorString: String) {
//                        callback?.onFailure()
//                    }
//                })
//        }
//    }

    private fun getToken(context: Context) {
        // 获取PushToken
        HonorPushClient.getInstance().getPushToken(object : HonorPushCallback<String?> {
            override fun onSuccess(pushToken: String?) {
                pushToken?.let {
                    <EMAIL>?.onReceiveRegisterId(
                        PushType.PUSH_TYPE_HONOR,
                        it
                    )
                }
            }

            override fun onFailure(errorCode: Int, errorString: String) {
                LogUtil.d("push配置信息", "honor push getToken error : $errorString")
            }
        })
    }

}