<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.dz.platform.push.honor">

    <application>

        <meta-data
            android:name="com.dz.platform.push.honor.HonorModule"
            android:value="com.dz.module.LibModule" />

        <meta-data
            android:name="com.hihonor.push.app_id"
            android:value="104414338"/>

        <service
            android:name=".HonorDzMessageService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.hihonor.push.action.MESSAGING_EVENT" />
            </intent-filter>
        </service>

    </application>

    <queries>
        <intent>
            <action android:name="com.hihonor.push.action.BIND_PUSH_SERVICE" />
        </intent>
    </queries>
</manifest>