package com.dz.platform.push.pushbase

import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build

object PushUtil {

    fun getMetaInfo(context: Context, key: String, splitStr: String? = null): String {
        return try {
            val info = context.packageManager.getApplicationInfo(
                context.packageName,
                PackageManager.GET_META_DATA
            )
            val valueStr = info.metaData.getString(key)
            if (splitStr.isNullOrEmpty()) {
                valueStr ?: ""
            } else {
                if (valueStr.isNullOrEmpty()) {
                    ""
                } else {
                    valueStr.split(splitStr)[1]
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
            ""
        }
    }

    //兜底打开app 首页
    fun defaultOpenApp(context: Context) {
        val schemeUri = "dz://${context.packageName}?"
        val intent = Intent(Intent.ACTION_VIEW).apply {
            data = Uri.parse(schemeUri)
            flags = Intent.FLAG_ACTIVITY_NEW_TASK
        }
        context.startActivity(intent)
    }

    /**
     * 小米系统 官网提供
     *
     * @return
     */
    fun isMIUI(): Boolean {
        val manufacturer = Build.MANUFACTURER
        //这个字符串可以自己定义,例如判断华为就填写huawei,魅族就填写meizu
        return "xiaomi".equals(manufacturer, ignoreCase = true)
    }

    /**
     * 是否华为手机
     *
     * @return
     */
    fun isEMUI(): Boolean {
        val manufacturer = Build.MANUFACTURER
        return "HUAWEI".equals(manufacturer, ignoreCase = true)
    }

    /**
     * 是否OPPO手机
     *
     * @return
     */
    fun isOPPO(): Boolean {
        val manufacturer = Build.MANUFACTURER
        return "OPPO".equals(manufacturer, ignoreCase = true)
    }

    /**
     * 是否vivo手机
     *
     * @return
     */
    fun isVIVO(): Boolean {
        val manufacturer = Build.MANUFACTURER
        return "vivo".equals(manufacturer, ignoreCase = true)
    }
}