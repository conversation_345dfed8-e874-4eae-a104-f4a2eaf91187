package com.dz.platform.push.oppo

import android.content.Context
import android.util.Log
import com.dz.foundation.base.BuildConfig
import com.dz.foundation.base.module.AppModule
import com.dz.foundation.base.utils.LocalActivityMgr
import com.dz.foundation.base.utils.LogUtil
import com.dz.platform.push.pushbase.OppoMS
import com.dz.platform.push.pushbase.PushOnRegisterCallback
import com.dz.platform.push.pushbase.PushUtil
import com.heytap.msp.push.HeytapPushManager
import com.heytap.msp.push.callback.INotificationPermissionCallback

class OppoPushManager : OppoMS {
    private var registerCallback: PushOnRegisterCallback? = null

    override fun setRegisterCallback(registerCallback: PushOnRegisterCallback) {
        this.registerCallback = registerCallback
    }

    override fun register(context: Context) {
        HeytapPushManager.init(context, BuildConfig.DEBUG)
        val appKey: String = PushUtil.getMetaInfo(context, "OPPO_APPKEY")
        val appSecret: String = PushUtil.getMetaInfo(context, "OPPO_APPSECRET")
        val pushCallback = OppoPushCallback(registerCallback)
//        Log.d(TAG, "OPPO推送进行注册 appKey:$appKey, appSecret:$appSecret")
        HeytapPushManager.register(context, appKey, appSecret, pushCallback)
    }

    override fun isSupportPush(context: Context) = HeytapPushManager.isSupportPush(context)


    fun showOppoDialog(callBack: () -> Unit, callBackFail: () -> Unit) {
        LogUtil.d(TAG, "showOppoDialog")
        if (HeytapPushManager.isSupportPush(AppModule.getApplication())) {
            val topActivity = LocalActivityMgr.getTopActivity()
            LogUtil.d("PUSH_OPPO", "满足OPPO厂商条件，系统自动弹出  topActivity = $topActivity")
            if (topActivity != null) {
                HeytapPushManager.requestNotificationAdvance(
                    topActivity,
                    object : INotificationPermissionCallback {
                        override fun onSuccess() {
                            LogUtil.d("PUSH_OPPO", "满足OPPO厂商条件，系统自动弹出  onSuccess")
                            LogUtil.d(TAG, "OPush 权限申请成功")
                            callBack.invoke()
                        }

                        override fun onFail(code: Int, msg: String?) {
                            LogUtil.d(
                                "PUSH_OPPO",
                                "满足OPPO厂商条件，系统自动弹出  onFail code = $code msg = $msg"
                            )
                            if (code == 1000 || code == 1001 || code == 2001) {
                                callBack.invoke()
                            } else {
                                callBackFail.invoke()
                            }
                        }
                    },
                    1001
                )
                return
            }
        }

        // 继续原有流程
    }
}
const val TAG = "PUSH_OPPO"