package com.dz.platform.push.oppo

import com.dz.foundation.base.utils.LogUtil
import com.dz.platform.push.pushbase.PushOnRegisterCallback
import com.dz.platform.push.pushbase.PushType
import com.heytap.msp.push.callback.ICallBackResultService

class OppoPushCallback(private val registerCallback: PushOnRegisterCallback?) :
    ICallBackResultService {
    override fun onRegister(code: Int, regId: String, p2: String?, p3: String?) {
        registerCallback?.onReceiveRegisterId(PushType.PUSH_TYPE_OPPO, regId)
    }

    override fun onUnRegister(p0: Int, p1: String?, p2: String?) {
        LogUtil.d(TAG, "onUnRegister $p0")
    }

    override fun onSetPushTime(p0: Int, p1: String?) {
        LogUtil.d(TAG, "onSetPushTime $p0 $p1")
    }

    override fun onGetPushStatus(p0: Int, p1: Int) {
        LogUtil.d(TAG, "onGetPushStatus $p0 $p1")
    }

    override fun onGetNotificationStatus(p0: Int, p1: Int) {
        LogUtil.d(TAG, "onGetNotificationStatus $p0 $p1")
    }

    override fun onError(p0: Int, p1: String?, p2: String?, p3: String?) {
        LogUtil.e(TAG, "onError code:$p0, msg:$p1")
        registerCallback?.onError(PushType.PUSH_TYPE_OPPO, "出现异常：code:$p0, msg:$p1")
    }

}