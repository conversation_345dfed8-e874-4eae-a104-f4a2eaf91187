<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.dz.platform.push.oppo">

    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="com.heytap.mcs.permission.RECIEVE_MCS_MESSAGE" />

    <application>

        <meta-data
            android:name="com.dz.platform.push.oppo.OppoModule"
            android:value="com.dz.module.LibModule" />

        <meta-data
            android:name="OPPO_APPKEY"
            android:value="e2dffd8419af406690fd9b55bb059f2c" />
        <!--            android:value="${OPPO_APP_KEY}" />-->
        <meta-data
            android:name="OPPO_APPSECRET"
            android:value="863dcb69a998408fbff037dbe47ba0a8" />
        <!--            android:value="${OPPO_APP_SEC}" />-->

        <!-- 兼容Q版本，继承DataMessageCallbackService -->
        <service
            android:name="com.heytap.msp.push.service.DataMessageCallbackService"
            android:exported="true"
            android:permission="com.heytap.mcs.permission.SEND_PUSH_MESSAGE">
            <intent-filter>
                <action android:name="com.heytap.mcs.action.RECEIVE_MCS_MESSAGE" />
                <action android:name="com.heytap.msp.push.RECEIVE_MCS_MESSAGE" />
            </intent-filter>
        </service>

        <!-- 兼容Q以下版本，继承CompatibleDataMessageCallbackService -->
        <service
            android:name="com.heytap.msp.push.service.CompatibleDataMessageCallbackService"
            android:exported="true"
            android:permission="com.coloros.mcs.permission.SEND_MCS_MESSAGE">
            <intent-filter>
                <action android:name="com.coloros.mcs.action.RECEIVE_MCS_MESSAGE" />
            </intent-filter>
        </service>

    </application>
</manifest>