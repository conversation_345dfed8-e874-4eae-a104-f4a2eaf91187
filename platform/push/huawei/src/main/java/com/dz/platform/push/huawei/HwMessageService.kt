package com.dz.platform.push.huawei

import android.os.Bundle
import android.util.Log
import com.dz.foundation.base.utils.LogUtil
import com.dz.platform.push.pushbase.PushOnRegisterCallback
import com.dz.platform.push.pushbase.PushType
import com.huawei.hms.push.HmsMessageService

class HwMessageService : HmsMessageService() {
    companion object {
        var registerCallback: PushOnRegisterCallback? = null
    }

    override fun onNewToken(token: String?, bundle: Bundle?) {
        super.onNewToken(token, bundle)
        Log.d(TAG, "华为推送获取token:$token")
        if (!token.isNullOrEmpty()) {
            registerCallback?.onReceiveRegisterId(PushType.PUSH_TYPE_HUAWEI, token)
        } else {
            LogUtil.e(TAG, "获取token为空")
            registerCallback?.onError(PushType.PUSH_TYPE_HUAWEI, "获取pushId失败")
        }
    }
}