package com.dz.platform.push.huawei

import android.content.Context
import com.dz.foundation.base.utils.LogUtil
import com.huawei.hms.push.HmsMessaging
import com.therouter.app.flowtask.lifecycle.FlowTask

/**
 *@Author: zhanggy
 *@Date: 2024-04-10
 *@Description:
 *@Version:1.0
 */
@FlowTask(taskName="initHuaweiPush", dependsOn = "initBugly", async = true)
fun initHuaweiPush(context: Context) {
    LogUtil.d("StartUp", "initHuaweiPush start")
    val startTime = System.currentTimeMillis()
    HmsMessaging.getInstance(context).isAutoInitEnabled = true
    LogUtil.d("StartUp_Consume", "initHuaweiPush 耗时:${System.currentTimeMillis() - startTime}. Thread:${Thread.currentThread().name}")
}