package com.dz.platform.push.huawei

import android.content.Context
import android.text.TextUtils
import android.util.Log
import com.blankj.utilcode.util.ThreadUtils
import com.dz.platform.push.pushbase.*
import com.huawei.hms.aaid.HmsInstanceId
import com.huawei.hms.api.HuaweiApiAvailability
import com.huawei.hms.api.HuaweiMobileServicesUtil
import com.huawei.hms.common.ApiException

class HwPushManager : HuaweiMS {
    private var registerCallback: PushOnRegisterCallback? = null

    override fun setRegisterCallback(registerCallback: PushOnRegisterCallback) {
        this.registerCallback = registerCallback
    }

    override fun register(context: Context) {
        HwMessageService.registerCallback = registerCallback
        getToken(context)
    }

    override fun isSupportPush(context: Context): Boolean {
//        val manufacturer = Build.MANUFACTURER
//        val brand = Build.BRAND
//        if (!TextUtils.isEmpty(manufacturer) && !TextUtils.isEmpty(brand)) {
//            if (manufacturer.equals(
//                    "HUAWEI",
//                    ignoreCase = true
//                ) || Build.BRAND == "Huawei" || Build.BRAND == "HONOR"
//            ) {
//                return true
//            }
//        }
        val a = HuaweiMobileServicesUtil.isHuaweiMobileServicesAvailable(
            context,
            HuaweiApiAvailability.getServicesVersionCode()
        )

        return a == 0
    }

    private fun getToken(context: Context) {
        ThreadUtils.getCachedPool().execute {
            try {
                /*
                * 华为的debug包获取不到token 需要打成release包获取
                * */
                val appId = PushUtil.getMetaInfo(context, "HWPUSH_APPID", "appid=")
                Log.e(TAG, "HWPUSH_APPID====：${appId}")
                val token = HmsInstanceId.getInstance(context)
                    .getToken(appId, "HCM")
                Log.d(TAG, "华为推送获取token:$token")
                // 判断token是否为空
                if (!TextUtils.isEmpty(token)) {
                    <EMAIL>?.onReceiveRegisterId(
                        PushType.PUSH_TYPE_HUAWEI,
                        token
                    )
                } else {
                    <EMAIL>?.onError(
                        PushType.PUSH_TYPE_HUAWEI,
                        "获取pushId失败"
                    )
                }
            } catch (e: ApiException) {
                e.printStackTrace()
                Log.e(TAG, "华为推送获取token失败：${e.message}")
            }
        }
    }
}

const val TAG = "PUSH_HUAWEI"
