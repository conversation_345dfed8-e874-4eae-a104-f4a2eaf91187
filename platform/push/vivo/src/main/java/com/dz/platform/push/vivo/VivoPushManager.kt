package com.dz.platform.push.vivo

import android.content.Context
import android.text.TextUtils
import com.dz.platform.push.pushbase.PushOnRegisterCallback
import com.dz.platform.push.pushbase.PushType
import com.dz.platform.push.pushbase.VivoMS
import com.vivo.push.PushClient
import com.vivo.push.PushConfig
import com.vivo.push.listener.IPushQueryActionListener

class VivoPushManager : VivoMS {
    private var registerCallback: PushOnRegisterCallback? = null

    override fun setRegisterCallback(registerCallback: PushOnRegisterCallback) {
        this.registerCallback = registerCallback
    }

    override fun register(context: Context) {
        val config = PushConfig.Builder()
            .agreePrivacyStatement(true)
            .build()
        PushClient.getInstance(context).initialize(config)
        PushClient.getInstance(context).turnOnPush { state ->
            if (state == 0) {
                PushClient.getInstance(context).getRegId(object : IPushQueryActionListener {
                    override fun onSuccess(regid: String) {
                        //获取成功
                        if (!TextUtils.isEmpty(regid)) {
                            registerCallback?.onReceiveRegisterId(PushType.PUSH_TYPE_VIVO, regid)
                        } else {
                            registerCallback?.onError(PushType.PUSH_TYPE_VIVO, "获取pushId失败")
                        }
                    }

                    override fun onFail(errerCode: Int) {
                        registerCallback?.onError(PushType.PUSH_TYPE_VIVO, "获取pushId失败, $errerCode")
                    }
                })
            }
        }
    }

    override fun isSupportPush(context: Context) = PushClient.getInstance(context).isSupport
}