package com.dz.platform.push.vivo

import android.content.Context
import android.content.Intent
import android.util.Log
import com.dz.platform.push.pushbase.PushUtil
import com.vivo.push.model.UPSNotificationMessage
import com.vivo.push.sdk.OpenClientPushMessageReceiver
import java.net.URISyntaxException

class VivoPushReceiver : OpenClientPushMessageReceiver() {
    override fun onNotificationMessageClicked(
        context: Context,
        upsNotificationMessage: UPSNotificationMessage
    ) {
        val skipContent = upsNotificationMessage.skipContent
        val skipType = upsNotificationMessage.skipType
        Log.d(TAG, "skipContent$skipContent")
        if (skipType == 3) {
            try {
                val intent = Intent.parseUri(skipContent, Intent.URI_INTENT_SCHEME)
                intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
                context.startActivity(intent)
            } catch (e: URISyntaxException) {
                e.printStackTrace()
                PushUtil.defaultOpenApp(context)
            }
        }
    }
}