<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.dz.platform.push.vivo">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="com.vivo.notification.permission.BADGE_ICON" />
    <uses-permission android:name="com.vivo.abe.permission.launcher.notification.num" />


    <application>

        <meta-data
            android:name="com.dz.platform.push.vivo.VivoModule"
            android:value="com.dz.module.LibModule" />

        <meta-data
            android:name="api_key"
            android:value="d1fff05c7c73f9c172db7eee74475eb9" />
        <!--        android:value="${VIVO_APP_KEY}" />-->

        <meta-data
            android:name="app_id"
            android:value="105633858" />
        <!--        android:value="${VIVO_APP_ID}" />-->

        <!--push应用定义消息receiver声明-->
        <receiver
            android:name="com.dz.platform.push.vivo.VivoPushReceiver"
            android:exported="false">
            <intent-filter>
                <!--接收push消息-->
                <action android:name="com.vivo.pushclient.action.RECEIVE" />
            </intent-filter>
        </receiver>

        <service
            android:name="com.vivo.push.sdk.service.CommandClientService"
            android:permission="com.push.permission.UPSTAGESERVICE"
            android:exported="true" />

    </application>

</manifest>