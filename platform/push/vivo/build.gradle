apply from: rootProject.ext.common_android_module

dependencies {
    //应用角标文档：https://dev.vivo.com.cn/documentCenter/doc/459
    //push开发者文档：https://dev.vivo.com.cn/documentCenter/doc/365
    //vivo推送产品说明：https://dev.vivo.com.cn/documentCenter/doc/180
    //注意：
    //1.vivo推送服务SDK支持的最低android版本为Android 6.0。
    //2.当通过"自定义/打开应用页面"方式启动应用内Activity时，该Activity在AndroidManifest.xml必须配置属性android:exported="true"。
    api rootProject.ext.getDep('pushbase')
    api 'com.dianzhong.third:vpush:4.0.0@aar'

}