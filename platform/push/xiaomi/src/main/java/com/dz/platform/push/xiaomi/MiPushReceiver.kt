package com.dz.platform.push.xiaomi

import android.content.Context
import android.content.Intent
import android.util.Log
import com.dz.platform.push.pushbase.PushOnRegisterCallback
import com.dz.platform.push.pushbase.PushType
import com.dz.platform.push.pushbase.PushUtil
import com.xiaomi.mipush.sdk.*
import java.net.URISyntaxException

class MiPushReceiver : PushMessageReceiver() {
    companion object {
        var registerCallback: PushOnRegisterCallback? = null
    }


    /**
     * 接受客户端向服务器发送注册命令消息后返回的响应。
     * @param context
     * @param message
     */
    override fun onCommandResult(context: Context, message: MiPushCommandMessage) {
        Log.d(TAG, "收到响应：$message")
        val command = message.command
        val arguments = message.commandArguments
        val regId = if (arguments != null && arguments.size > 0) arguments[0] else null

        if (MiPushClient.COMMAND_REGISTER == command) {
            if (message.resultCode == ErrorCode.SUCCESS.toLong()) {
                registerCallback?.onReceiveRegisterId(PushType.PUSH_TYPE_XIAOMI, regId ?: "")
            } else {
                registerCallback?.onError(PushType.PUSH_TYPE_XIAOMI, "获取pushId失败: ${message.resultCode}")
            }
        }
    }

    /**
     * 用来接收服务器发来的通知栏消息（用户点击通知栏时触发）
     * @param context
     * @param miPushMessage
     */
    override fun onNotificationMessageClicked(context: Context, miPushMessage: MiPushMessage) {
        val intentParam = miPushMessage.extra["intent"]
        try {
            val intent = Intent.parseUri(intentParam, Intent.URI_INTENT_SCHEME)
            intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
            context.startActivity(intent)
        } catch (e: URISyntaxException) {
            e.printStackTrace()
            //兜底打开app
            PushUtil.defaultOpenApp(context)
        }
    }
}