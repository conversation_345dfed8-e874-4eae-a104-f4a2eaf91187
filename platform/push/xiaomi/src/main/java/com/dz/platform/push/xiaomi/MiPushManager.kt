package com.dz.platform.push.xiaomi

import android.content.Context
import android.util.Log
import com.dz.foundation.base.utils.ProcessUtil.Companion.getCurrentProcessName
import com.dz.platform.push.pushbase.PushOnRegisterCallback
import com.dz.platform.push.pushbase.PushUtil
import com.dz.platform.push.pushbase.XiaomiMS
import com.xiaomi.channel.commonutils.logger.LoggerInterface
import com.xiaomi.mipush.sdk.Logger
import com.xiaomi.mipush.sdk.MiPushClient

class MiPushManager : XiaomiMS {
    private var registerCallback: PushOnRegisterCallback? = null

    override fun setRegisterCallback(registerCallback: PushOnRegisterCallback) {
        this.registerCallback = registerCallback
    }

    override fun register(context: Context) {
        if (shouldInit(context)) {

            MiPushReceiver.registerCallback = registerCallback
            val appID = PushUtil.getMetaInfo(context, "MIPUSH_APPID", "XM_")
            val appKey = PushUtil.getMetaInfo(context, "MIPUSH_APPKEY", "XM_")
//            Log.d(TAG, "小米推送进行注册 appID：$appID, appKey: $appKey")
            MiPushClient.registerPush(context, appID, appKey)

        }
        val newLogger: LoggerInterface = object : LoggerInterface {
            override fun setTag(tag: String) {
                // ignore
            }

            override fun log(content: String, t: Throwable) {
                Log.d(TAG, content, t)
            }

            override fun log(content: String) {
                Log.d(TAG, content)
            }
        }
        Logger.setLogger(context, newLogger)
    }

    override fun isSupportPush(context: Context) = MiPushClient.shouldUseMIUIPush(context)

    private fun shouldInit(context: Context) = context.packageName == getCurrentProcessName(context)

}
const val TAG = "PUSH_MI"