<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.dz.platform.push.xiaomi">

    <application>

        <meta-data
            android:name="com.dz.platform.push.xiaomi.MiModule"
            android:value="com.dz.module.LibModule" />

        <meta-data
            android:name="MIPUSH_APPID"
            android:value="XM_2882303761520232094" />
        <!--            android:value="XM_${XIAOMI_APP_ID}" />-->
        <meta-data
            android:name="MIPUSH_APPKEY"
            android:value="XM_5642023295094" />
        <!--            android:value="XM_${XIAOMI_APP_KEY}" />-->

        <receiver
            android:exported="true"
            android:name="com.dz.platform.push.xiaomi.MiPushReceiver">
            <intent-filter>
                <action android:name="com.xiaomi.mipush.RECEIVE_MESSAGE" />
            </intent-filter>
            <intent-filter>
                <action android:name="com.xiaomi.mipush.MESSAGE_ARRIVED" />
            </intent-filter>
            <intent-filter>
                <action android:name="com.xiaomi.mipush.ERROR" />
            </intent-filter>
        </receiver>

    </application>
</manifest>