package com.dz.platform.ad.data

import com.dz.foundation.base.data.kv.KVData

object AdKV : KVData {

    override fun getGroupName() = "com.dianzhong.ad"

    // 用户当天观看时长 秒
    var todayWatchedDurationSec by todayDelegate("todayWatchedDuration", 0)

    // 用户当天观看时长 秒
    var todayWatchedDurationSec2 by todayDelegate("todayWatchedDuration2", 0F)

    // 用户总的观看时长 秒
    var historyWatchedDurationSec by delegate("historyWatchedDuration", 0F)

    // draw广告曝光时候的历史观看时长 秒
    var drawImpHistoryWatchedSec by delegate("drawImpHistoryWatchedSec", 0F)
    // 两次draw广告曝光的看剧时长 毫秒 上报用
    var drawImpWatchedMillisGap by delegate("drawImpWatchedMillisGap", 0L)

    // 二级页福利锚点广告曝光时候的历史观看时长 秒
    var flmdImpHistoryWatchedSec by delegate("flmdImpHistoryWatchedSec", 0F)

    // 记录 请求位序接口20001-slotId关闭报错 重新请求新SlotId的时间戳
    var requestNewSlotIdTime by delegate("requestNewSlotIdTime", 0L)

    //请求2105接口的间隔时间 默认60s 单位s
    var requestNewSlotSlotRefreshInterval by delegate("requestNewSlotSlotRefreshInterval", 60L)


    var drawFeedbackGuidePopShowCnt by todayDelegate("drawFeedbackGuidePopShowCnt", 0)
    var enableAdHookFastApp by delegate("enableAdHookFastApp", false)
    var enableRewardAdReuseCrossPage by delegate("enableRewardAdReuseCrossPage", false)
}