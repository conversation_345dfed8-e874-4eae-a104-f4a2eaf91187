package com.dz.platform.ad.lifecycle

import com.dz.foundation.base.utils.LogUtil
import com.dz.platform.ad.data.AdKV
import com.dz.platform.ad.draw.DrawSession

object WatchTimeLifeCycle {

    fun savePlayTimeToDB(duration: Int) { // 秒
        AdKV.todayWatchedDurationSec += duration // 用户当日观看时长
        LogUtil.d(
            "WatchTimeLifeCycle",
            "calculateUserDailyWatchDuration today watch duration=${AdKV.todayWatchedDurationSec} historyWatchedDuration=${AdKV.historyWatchedDurationSec}"
        )
    }

    private var accumulativeTotal = 0F
    fun savePlayTimeToDB2(duration: Float, playScene: PlayScene) {
        LogUtil.d(
            "WatchTimeLifeCycle",
            "savePlayTimeToDB2 duration=$duration"
        )
        AdKV.historyWatchedDurationSec += duration // 用户总的观看时长
        accumulativeTotal += duration
        if (accumulativeTotal >= 1) {
            AdKV.todayWatchedDurationSec2 += accumulativeTotal // 用户当日观看时长
            DrawSession.saveDetailSessionWatchDurationOnPlaying(playScene, accumulativeTotal)
            accumulativeTotal = 0F
        }
    }

    fun onPlayStart(playScene: PlayScene) {
        DrawSession.onPlayStart(playScene)
    }

    fun onPlayStop(playScene: PlayScene) {
       DrawSession.onPlayStop(playScene)
    }
}


enum class PlayScene(val value: String) {
    HOME("home"),     // 首页
    DETAIL("detail"); // 详情页
}