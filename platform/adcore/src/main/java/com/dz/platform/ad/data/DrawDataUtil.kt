package com.dz.platform.ad.data

import com.dianzhong.common.util.DzLog
import com.dz.foundation.base.utils.LogUtil
import com.dz.platform.ad.ChapterIntervalUtil
import com.dz.platform.ad.CoinsDropIntervalUtil
import com.dz.platform.ad.DrawForceViewTimeUtil
import com.dz.platform.ad.vo.BehaviorCtrlCfg
import com.dz.platform.ad.vo.DrawAdVo

object DrawDataUtil {

    var isDetailFromColdStart = false

    /**
     * @param reopen:是否是重新进入二级页
     * */
    fun onDetailDrawAdConfigFetched(drawAdVo: DrawAdVo, reopen: Boolean? = true) {

        updateDetailDrawAdData(drawAdVo)

        if (reopen == true) {
            // 每次进入二级页递减
            decreaseDrawStartIndex()
        }
    }

    private fun updateDetailDrawAdData(drawAdVo: DrawAdVo) {
        CoinsDropIntervalUtil.drawTaskVo = drawAdVo.drawTask

        val adConfExt = drawAdVo.getAdConf()
        adConfExt ?: return

        adConfExt.intervalChapter?.let {
            DrawAdKV.detailDrawAdDefaultStartIndex = it.startIndex.coerceAtLeast(ChapterIntervalUtil.MIN_START_INDEX)
            DrawAdKV.detailDrawAdDecrement = it.decrement
            DrawAdKV.detailDrawAdTermination = it.termination
            ChapterIntervalUtil.remoteDetailAdIntervals = it.adIntervals
        }
        adConfExt.intervalChapterEpd?.let {
            ChapterIntervalUtil.remoteDetailAdIntervalsEpd = it.adIntervals
        }
//        ChapterIntervalUtil.remoteDetailAdIntervals = ChapterIntervalUtil.mockAdIntervals() // todo comment out test code
        DrawAdKV.detailDrawAdMinWatchTimeSec = adConfExt.getMinWatchTimeForAdSec()

//        DrawViewTimeUtil.remoteDetailAdDrawViewTimes = DrawViewTimeUtil.mockAdTimes() // todo comment out test code
        adConfExt.mandatoryViewingDuration?.let {
            DrawForceViewTimeUtil.remoteDetailAdDrawViewTimes = it.adDurations
        }
        adConfExt.adCacheConfig?.let {
            DrawAdKV.detailDrawMultiCacheSwitch = it
            DzLog.d("SkyLoader", "adRequestSeq 多缓存开关: $it")
        }

        adConfExt.behaviorCtrlCfg?.recordToMMKV() ?: BehaviorCtrlCfg.ReSetter.resetValue()
        adConfExt.coinDropConfig?.let { CoinsDropIntervalUtil.coinDropConfig = it }
    }

    private fun decreaseDrawStartIndex() {
        if (isDetailFromColdStart) {
            isDetailFromColdStart = false
            DrawAdKV.detailDrawAdStartIndex = DrawAdKV.detailDrawAdDefaultStartIndex
            LogUtil.d("interval_chapter_detail", "decreaseDrawStartIndex 冷起后重置 startIndex: ${DrawAdKV.detailDrawAdStartIndex}")
        } else {
            val old = DrawAdKV.detailDrawAdStartIndex
            val dec = DrawAdKV.detailDrawAdDecrement
            val ter = DrawAdKV.detailDrawAdTermination
            DrawAdKV.detailDrawAdStartIndex = (old - dec).coerceAtLeast(ter)
            LogUtil.d("interval_chapter_detail", "decreaseDrawStartIndex old:$old, dec:$dec ter:$ter newStartIndex:${DrawAdKV.detailDrawAdStartIndex}")
        }
    }


    fun onHomeDrawAdConfigFetched(drawAdVo: DrawAdVo) {
        drawAdVo.apply {
            DrawAdKV.homeDrawAdBlockConfigId = blockConfigId ?: ""
            DrawAdKV.homeDrawAdId = getAdId() ?: ""
            DrawAdKV.homeDrawAdMaxShowNum = getDayMaxShowNum()
            DrawAdKV.homeDrawAdSingleTime = getSingleTime()
            DrawAdKV.homeDrawAdDayTime = getDayMaxFailLoopNum()
            userTacticsVo?.let { userTacticsVo ->
                DrawAdKV.homeDrawAdTacticsId = userTacticsVo.tacticsId ?: 0
                DrawAdKV.homeDrawAdTacticsName = userTacticsVo.tacticsName ?: ""
                DrawAdKV.homeDrawAdSourceId = userTacticsVo.sourceId ?: 0
                DrawAdKV.homeDrawAdSourceName = userTacticsVo.sourceName ?: ""
                DrawAdKV.homeDrawAdShuntID = userTacticsVo.shuntID ?: 0
                DrawAdKV.homeDrawAdShuntName = userTacticsVo.shuntName ?: ""
                DrawAdKV.homeDrawAdIsDot = userTacticsVo.isDot ?: false
            }
            getAdConf()?.let {
                it.intervalChapter?.let { intervalChapter ->
                    DrawAdKV.homeDrawAdStartIndex = intervalChapter.startIndex
                    ChapterIntervalUtil.remoteHomeAdIntervals = intervalChapter.adIntervals
                }
                DrawAdKV.homeDrawAdMinWatchTimeSec = it.getMinWatchTimeForAdSec()
            }
        }
    }

    /**
     * 首页draw Ad 不满足最小观看时长
     */
    fun minWatchTimeNotMetForHomeAd(): Boolean {
        return (AdKV.historyWatchedDurationSec < DrawAdKV.homeDrawAdMinWatchTimeSec).also {
            LogUtil.d("recommend_draw_ad_tag", "首页draw 观看时长 已观看:${AdKV.historyWatchedDurationSec} 门槛:${DrawAdKV.homeDrawAdMinWatchTimeSec}")
        }
    }

    /**
     * 二级页draw Ad 不满足最小观看时长
     */
    fun minWatchTimeNotMetForDetailAd(): Boolean {
        return (AdKV.historyWatchedDurationSec < DrawAdKV.detailDrawAdMinWatchTimeSec).also {
            LogUtil.d("detail_draw_ad_tag", "二级页draw 观看时长 已观看:${AdKV.historyWatchedDurationSec} 门槛:${DrawAdKV.detailDrawAdMinWatchTimeSec}")
        }
    }
}