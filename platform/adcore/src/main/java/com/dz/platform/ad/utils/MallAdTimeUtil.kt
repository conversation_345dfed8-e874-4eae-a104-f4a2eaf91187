package com.dz.platform.ad.utils

import com.dz.foundation.base.manager.task.Task
import com.dz.foundation.base.manager.task.TaskManager
import com.dz.foundation.base.utils.LogUtil

class MallAdTimeUtil {

    companion object {
        const val TAG = "MallAdTimeUtil"
    }

    private var upDataCueTask: Task? = null

    private var remainTime: Int = 0
    var callback: (() -> Unit)? = null
    fun createTask(time: Int) {
        val totalTimes = time + 1
        cancelTask()
        stop = false
        LogUtil.d(TAG, "倒计时开始，totaltimes==$totalTimes")
        upDataCueTask = TaskManager.intervalTask(totalTimes, 0, 1000) { newTime ->
            LogUtil.d(TAG, "计时器倒计时，time=$newTime")
            remainTime = totalTimes - newTime
            if (newTime == totalTimes - 1) {//倒计时结束
                cancelTask()
                callback?.invoke()
            }
        }
    }

    fun pauseTask() {
        cancelTask()
    }

    fun resumeTask() {
        if (remainTime > 0) {
            createTask(remainTime - 1)
        }
    }

    fun stopTask() {
        stop = true
        cancelTask()
        remainTime = 0
    }

    private fun cancelTask() {
        upDataCueTask?.cancel()
        upDataCueTask = null
    }

    var stop: Boolean = true
    fun isStop(): Boolean {
        return stop
    }
}