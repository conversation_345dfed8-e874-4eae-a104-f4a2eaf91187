package com.dz.platform.ad

import com.dianzhong.base.util.AppUtil
import com.dianzhong.core.manager.SkyManager
import com.dz.foundation.base.utils.LogUtil
import com.dz.platform.ad.data.AdKV
import com.dz.platform.ad.data.DrawAdKV
import com.dz.platform.ad.draw.DrawSession
import com.dz.platform.ad.vo.AdInterval
import com.dz.platform.ad.vo.AdIntervalEpd
import com.dz.platform.ad.vo.BasicInterval

object ChapterIntervalUtil {
    private const val DEFAULT_DETAIL_INTERVAL = "3" // 二级页默认的插入广告间隔
    private const val DEFAULT_HOME_INTERVAL = "3"   // 一级页默认的插入广告间隔
    private val DEFAULT_DETAIL_INTERVAL_ARRAY = DEFAULT_DETAIL_INTERVAL.map { it.toString().toInt() }.toIntArray()
    private val DEFAULT_HOME_INTERVAL_ARRAY = DEFAULT_HOME_INTERVAL.map { it.toString().toInt() }.toIntArray()
    const val MIN_START_INDEX = 2 // 起始章节最小值，后台不允许小于2

    var remoteDetailAdIntervals: List<AdInterval>? = null       // 二级页插入广告间隔策略
    var remoteDetailAdIntervalsEpd: List<AdIntervalEpd>? = null // 二级页插入广告间隔策略 剧集插入广告间隔策略
//    var remoteDetailAdIntervals: List<AdInterval>? = mockAdIntervals() // todo remove test code
    var remoteHomeAdIntervals: List<AdInterval>? = null   // 一级页插入广告间隔策略

    var detailInsertAdsIndexSet: Set<Int>? = null
    private var detailInsertStartIndex: Int? = null

    var homeInsertAdsIndexSet: Set<Int>? = null
    private var homeInsertStartIndex: Int? = null

    // 二级页间隔章节是否发生变化
    fun isDetailAdIntervalChanged(totalChapter: Int, currChapterIndex: Int): Boolean {
        val lastInterval = DrawAdKV.detailDrawAdLastInterval
        val newInterval = selectAdInterval(totalChapter, currChapterIndex, remoteDetailAdIntervals, remoteDetailAdIntervalsEpd)?.interval
            ?: DEFAULT_DETAIL_INTERVAL
        val isChanged = (lastInterval != newInterval).also { isChanged ->
            val msg =
                " 二级页章节配置 是否发生变更: $isChanged, lastInterval: $lastInterval, newInterval: $newInterval"
            if (isChanged) logI(msg) else logD(msg)
        }

        return if (isChanged) {
            true
        } else if (isSessionInsert()) {
            true
        } else {
            isDetailLongTimeWatch(currChapterIndex)
        }
    }

    // 获取二级页插入的广告索引set
    fun getDetailInsertAdsIndexSet(
        firstIn: Boolean = false,    // 是否首次进入二级页
        totalChapter: Int,     // 总章节个数
        upNoAdsCnt: Int,     // 上面最近的广告索引
        currChapterIndex: Int,   // 用户所在的章节索引
    ): Set<Int> {

        // 根据观看时长，ecpm计算间隔周期
        val interval = selectAdInterval(totalChapter, currChapterIndex, remoteDetailAdIntervals, remoteDetailAdIntervalsEpd)
        val intervalStr = interval?.interval ?: DEFAULT_DETAIL_INTERVAL
        val intervalTimeSec = interval?.intervalTime ?: 0
        val chapterDuration = interval?.chapterDurations

        val intervalArray = runCatching { intervalStr.split(",").map { it.toInt() }.toIntArray()
        }.onFailure { e -> LogUtil.e("interval_chapter", "出现异常：$e") }
            .getOrElse { DEFAULT_DETAIL_INTERVAL_ARRAY }

        // 保存本次使用的间隔章节，时长
        if (DrawAdKV.detailDrawAdLastInterval != intervalStr) {
            LogUtil.i("interval_chapter_detail", "间隔章节发生变化，保存新的 ${DrawAdKV.detailDrawAdLastInterval} -> 间隔章节：$intervalStr 间隔时长：$intervalTimeSec 章节分段强制观看时长：$chapterDuration")
        }
        DrawAdKV.detailDrawAdLastInterval = intervalStr

        val startIndex = DrawAdKV.detailDrawAdStartIndex.coerceAtLeast(DrawAdKV.detailDrawAdTermination)
        detailInsertStartIndex = startIndex

        val result =
            getInsertAdsIndexSet(
                firstIn = firstIn,
                interval = intervalArray,
                startIndex = startIndex,
                totalChapter = totalChapter,
                upNoAdsCnt = upNoAdsCnt,
                currChapterIndex = currChapterIndex,
                isCenterNext = callOnlyOnceIsSessionInsert || isDetailLongTimeWatch(currChapterIndex)
            )
        detailInsertAdsIndexSet = result
        return result
    }

    // 一级页间隔章节是否发生变化
    fun isHomeAdIntervalChanged(currChapterIndex: Int): Boolean {
        val lastInterval = DrawAdKV.homeDrawAdLastInterval
        val newInterval = selectEcpmInterval(currChapterIndex, remoteHomeAdIntervals)?.interval ?: DEFAULT_HOME_INTERVAL
        val isChanged =  (lastInterval != newInterval).also { isChanged ->
            val msg = " 一级页章节配置 是否发生变更: $isChanged, lastInterval: $lastInterval, newInterval: $newInterval"
            if (isChanged) logI(msg) else logD(msg)
        }

        return if (isChanged) {
            true
        } else {
            isHomeLongTimeWatch(currChapterIndex)
        }
    }

    // 获取一级页插入的广告索引set
    fun getHomeInsertAdsIndexSet(
        elementCnt: Int,        // 总的元素个数
        upNoAdsCnt: Int,        // 上面最近的广告索引
        currChapterIndex: Int,  // 用户所在的章节索引，不含广告的
    ): Set<Int> {
        // 根据观看时长，ecpm计算间隔周期
        val interval = selectEcpmInterval(currChapterIndex, remoteHomeAdIntervals)
        val intervalStr = interval?.interval ?: DEFAULT_HOME_INTERVAL
        val intervalTime = interval?.intervalTimeSec ?: 0
        saveLastHomeInsertConfig(upNoAdsCnt, currChapterIndex, intervalStr, intervalTime)
        return getHomeInsertAdsIndexSetInner(intervalStr, intervalTime, elementCnt, upNoAdsCnt, currChapterIndex)
    }

    // 获取一级页上一次插入的广告索引set, 用于翻页加载时候的广告插入
    fun getHomeLastInsertAdsIndexSet(
        elementCnt: Int // 总的元素个数(原来的 + 新增的)
    ): Set<Int> {
        return getHomeInsertAdsIndexSetInner(lastHomeIntervalStr, lastHomeIntervalTime, elementCnt, lastHomeUpNoAdsCnt, lastHomeCurrChapterIndex)
    }




    ////////////// private function ///////////

    private var lastHomeUpNoAdsCnt = 0
    private var lastHomeCurrChapterIndex = 0
    private var lastHomeIntervalStr = DEFAULT_HOME_INTERVAL
    private var lastHomeIntervalTime = 0L

    private fun saveLastHomeInsertConfig(upNoAdsCnt: Int, currChapterIndex: Int, intervalStr: String, intervalTime: Long) {
        lastHomeUpNoAdsCnt = upNoAdsCnt
        lastHomeCurrChapterIndex = currChapterIndex
        lastHomeIntervalStr = intervalStr
        lastHomeIntervalTime = intervalTime
    }

    private fun getHomeInsertAdsIndexSetInner(
        intervalStr: String,
        intervalTime: Long,
        elementCnt: Int,
        upNoAdsCnt: Int,
        currChapterIndex: Int
    ): Set<Int> {
        val intervalArray = runCatching {
            intervalStr.split(",").map { it.toInt() }.toIntArray()
        }.onFailure { e -> LogUtil.e("interval_chapter", "出现异常：$e") }
            .getOrElse { DEFAULT_HOME_INTERVAL_ARRAY }

        // 保存本次使用的间隔章节，时长
        if (DrawAdKV.homeDrawAdLastInterval != intervalStr) {
            LogUtil.i("interval_chapter_home", "间隔章节发生变化，保存新的 ${DrawAdKV.homeDrawAdLastInterval} -> $intervalStr 间隔时间:$intervalTime")
        }
        DrawAdKV.homeDrawAdLastInterval = intervalStr
        DrawAdKV.homeDrawAdLastIntervalTimeSec = intervalTime

        homeInsertStartIndex = DrawAdKV.homeDrawAdStartIndex

        val result = getInsertAdsIndexSet(
            interval = intervalArray,
            startIndex = DrawAdKV.homeDrawAdStartIndex,
            totalChapter = elementCnt,
            upNoAdsCnt = upNoAdsCnt,
            currChapterIndex = currChapterIndex,
            isCenterNext = isHomeLongTimeWatch(currChapterIndex)
        )
        homeInsertAdsIndexSet = result
        return result
    }

    // 一级页观剧时长达到最小变化
    private fun isHomeLongTimeWatch(currChapterIndex: Int): Boolean {
        return (homeInsertStartIndex?.let {
            currChapterIndex >= it && (AdKV.drawImpHistoryWatchedSec > 0 && DrawAdKV.homeDrawAdLastIntervalTimeSec > 0 &&
                    (AdKV.historyWatchedDurationSec - AdKV.drawImpHistoryWatchedSec) >= DrawAdKV.homeDrawAdLastIntervalTimeSec)
        } ?: false).also { isChanged ->
            val msg =
                " 一级页 看剧很久: $isChanged, 当前:${AdKV.historyWatchedDurationSec}-draw曝光:${AdKV.drawImpHistoryWatchedSec}=${AdKV.historyWatchedDurationSec - AdKV.drawImpHistoryWatchedSec}秒, " +
                        "间隔时间:${DrawAdKV.homeDrawAdLastIntervalTimeSec}秒 间隔章节:${DrawAdKV.homeDrawAdLastInterval} 起始章节:$homeInsertStartIndex 当前章节:$currChapterIndex"
            if (isChanged && !AppUtil.isFastPrint()) logI(msg) else logD(msg)
        }
    }

    // 二级页观剧时长达到最小变化
    private fun isDetailLongTimeWatch(currChapterIndex: Int): Boolean {
        return (detailInsertStartIndex?.let {
            currChapterIndex >= it && (AdKV.drawImpHistoryWatchedSec > 0 && DrawAdKV.detailDrawAdLastIntervalTimeSec > 0 &&
                    (AdKV.historyWatchedDurationSec - AdKV.drawImpHistoryWatchedSec) >= DrawAdKV.detailDrawAdLastIntervalTimeSec)
        } ?: false).also { isChanged ->
            val msg =
                " 二级页 看剧很久: $isChanged, 当前(${AdKV.historyWatchedDurationSec})-draw曝光(${AdKV.drawImpHistoryWatchedSec}) = ${AdKV.historyWatchedDurationSec-AdKV.drawImpHistoryWatchedSec}秒, " +
                        "间隔时长:${DrawAdKV.detailDrawAdLastIntervalTimeSec}秒 间隔章节:${DrawAdKV.detailDrawAdLastInterval} 起始章节:$detailInsertStartIndex 当前章节:${currChapterIndex+1}"
            if (isChanged && !AppUtil.isFastPrint()) logI(msg) else logD(msg)
        }
    }

    // 是否是会话时长导致的插入广告
    private var callOnlyOnceIsSessionInsert = false
        get() {
            val ret = field
            field = false // 获取一次后重置状态
            if (ret) {
                LogUtil.i("interval_chapter", "由session引发的，当前集后面插入广告")
            }
            return ret
        }
    private fun isSessionInsert(): Boolean {
        val sessionInsert = DrawSession.isSessionInsert()
        callOnlyOnceIsSessionInsert = sessionInsert
        return sessionInsert
    }

    /**
     * 按照一下优先级来选择间隔章节的配置
     * 1. 根据剧集配置      来决定使用哪个间隔章节的配置（高优先级, 没有匹配上的字段往下漏）
     * 2. 根据ecpm,观看时长 来决定使用哪个间隔章节的配置
     */
    private fun selectAdInterval(
        totalChapter: Int,
        currChapterIndex: Int,
        remoteIntervals: List<AdInterval>?,
        remoteIntervalsEpd: List<AdIntervalEpd>?
    ): BasicInterval? {
        val basicInterval = BasicInterval()

        selectChapterIntervals(
            remoteIntervalsEpd, totalChapter
        )?.find { (currChapterIndex + 1) >= it.epdStart && (currChapterIndex + 1) < it.getEpdEnd() }?.let {
            basicInterval.apply {
                interval = it.interval
                intervalTime = it.intervalTime
                chapterDurations = it.durations
                syncChapterValues()
            }
            LogUtil.d("interval_chapter", "命中剧集分段间隔配置：$it")
        }

        if (basicInterval.isAllSet()) {
            return basicInterval
        }

        selectEcpmInterval(currChapterIndex, remoteIntervals)?.let {
            // 这里要体现优先级，只有当剧集分段配置不存在的时候，才会使用ecpm配置
            if (basicInterval.interval.isNullOrEmpty()) {
                basicInterval.interval = it.interval
            }
            if ((basicInterval.intervalTime ?: 0) == 0L) {
                basicInterval.intervalTime = it.intervalTimeSec
            }
            basicInterval.syncEcpmValues()
            LogUtil.d("interval_chapter", "命中ecpm间隔配置：$it")
        }

        LogUtil.d("interval_chapter", "最终间隔配置：$basicInterval 当前章节:${currChapterIndex+1} 总章节数:$totalChapter")
        return basicInterval
    }

    // 根据当前剧的总章节数，从配置里选择匹配的区间集合，并且处理重叠的区间, 返回处理后的区间
    private fun selectChapterIntervals(
        remoteIntervalsEpd: List<AdIntervalEpd>?,
        totalChapter: Int
    ): List<AdIntervalEpd>? {
        val startMs = System.currentTimeMillis()

        // 1. 筛选符合条件的Interval
        val targetIntervals = remoteIntervalsEpd?.filter {
            totalChapter >= it.totalEpdStart && totalChapter < it.getTotalEpdEnd()
        }

        // 2. 拿出来倒数区间 (只会有一个)
        val lastEpdInterval = targetIntervals?.filter { it.lastEpd > 0 }?.getOrNull(0)

        // 3. 拿出来正常区间
        val normalIntervals = targetIntervals?.filter { it.lastEpd <= 0 }
            ?.sortedBy { it.lastEpd } // 只保留start,end的正常区间

        // 4. 错误处理
        if (lastEpdInterval == null && normalIntervals == null) {
            LogUtil.d("interval_chapter", "未下发剧集分段间隔配置")
            return null
        }

        // 5. 处理区间重叠问题。根据总集数重新分割区间（执行到这里倒数区间，正常区间必然存在一个）
        val finalIntervals = mutableListOf<AdIntervalEpd>()
        when {
            lastEpdInterval == null -> {
                // 没有倒数区间，那么只有正常区间。例如60-80集的剧配置[60-70)
                //                                            [70,80)。
                //                                            后续看当前的集数在哪个区间内即可
                finalIntervals.addAll(normalIntervals ?: emptyList())
            }

            else -> {
                // 存在倒数区间
                val lastEpdStart = totalChapter - lastEpdInterval.lastEpd + 1
                if (lastEpdStart >= totalChapter) {
                    // 倒数区间的start不合法，直接使用正常区间
                    finalIntervals.addAll(normalIntervals ?: emptyList())
                } else if (normalIntervals?.isEmpty() == true) {
                    // 只有倒数区间，没有正常区间
                    finalIntervals.add(lastEpdInterval.copy(epdStart = lastEpdStart, epdEnd = 0))
                } else {
                    // 有交集，需要重新分割区间。
                    // 例如：70集的剧，正常区间分割[1-50)，[50-70), 倒数区间为10，那么会分割为：[1-50)，[50-60)，[60-70)
                    normalIntervals?.forEach {
                        if (it.getEpdEnd() <= lastEpdStart) {
                            finalIntervals.add(it)
                        } else {
                            if (it.epdStart < lastEpdStart) {
                                finalIntervals.add(it.copy(epdEnd = lastEpdStart))
                            }
                            return@forEach
                        }
                    }
                    // 倒数第x集~最后一集（正无穷）
                    finalIntervals.add(lastEpdInterval.copy(epdStart = lastEpdStart, epdEnd = 0))
                }
            }
        }
        LogUtil.d("interval_chapter", "预处理前 的剧集分段间隔配置：$remoteIntervalsEpd")
        LogUtil.d("interval_chapter", "预处理后 的剧集分段间隔配置：$finalIntervals totalChapter:$totalChapter 耗时：${System.currentTimeMillis() - startMs}ms")
        return finalIntervals
    }

    // 根据ecpm,观看时长 来决定使用哪个间隔章节的配置
    private fun selectEcpmInterval(currChapterIndex: Int, remoteIntervals:List<AdInterval>?): AdInterval? {
        val startMs = System.currentTimeMillis()
        var result: AdInterval? = null
        val lastEcpmCent = if (DrawAdKV.todayLastDrawAdEcpmCent > 0) {
            DrawAdKV.todayLastDrawAdEcpmCent
        } else {
            SkyManager.getInstance().userDrawEcpmCent
        }
        val watchDuration = AdKV.todayWatchedDurationSec2
        // 看看lastEcpmCent和watchDuration的值落在remoteDetailAdInterval的哪个区间。注意，如果playDurationEnd为0，则代表右侧无限制，ecpmEnd为0则代表右侧无限制
        remoteIntervals?.forEach {
            if (watchDuration >= it.playDurationStartSec && (it.playDurationEndSec == 0 || watchDuration < it.playDurationEndSec) &&
                lastEcpmCent >= it.ecpmStartCent && (it.ecpmEndCent == 0L || lastEcpmCent < it.ecpmEndCent)
            ) {
                result = it
                return@forEach
            }
        }
        LogUtil.d(
            "interval_chapter",
            "选择ecpm间隔章节配置 当前章节:${currChapterIndex+1} ecpm:${lastEcpmCent}分 playDuration:${watchDuration}秒 result:$result 耗时：${System.currentTimeMillis() - startMs}ms"
        )
        return result
    }

    // 获取插入的广告索引set
    private fun getInsertAdsIndexSet(
        firstIn: Boolean = false, // 是否首次进入二级页
        interval: IntArray,
        startIndex: Int,     // 起始章节
        totalChapter: Int,     // 总的元素个数
        upNoAdsCnt: Int,     // 上面最近的广告索引
        currChapterIndex: Int,   // 用户所在的章节索引
        isCenterNext: Boolean // 是否在当前集下面插入广告（作为中心点）
    ): Set<Int> {

        val list = getInsertAdsIndexFromCenterToUpAndDown(
            firstIn = firstIn,
            totalChapters = totalChapter,
            upNoAdsCnt = upNoAdsCnt,
            startIndex = startIndex,
            currentIndex = currChapterIndex,
            interval = interval,
            isCenterNext = isCenterNext
        )
        return list.toSet()
    }

    /**
     * 一：如果currentIndex <= startIndex, 则在startIndex处开始按照2,3,4的interval作为间隔周期，不断插入广告
     * 二：从中心点center处插入广告，并且向列表上下扩散插入广告，那么规则如下：
     * 1. 索引center的计算规则(可以保证在currentIndex的后面插入)
     *     计算公式：d = currentIndex + X2 - upNoAdsCnt；用户刚切到currentIndex位置; X2为新的间隔章节；upNoAdsCnt为切剧后（包含自己）上面有几个不含广告的连续集数,如果currentIndex是广告则upNoAdsCnt=0
     *     i.  d  > currentIndex  则在center = d（ > currentIndex） 处插入广告，并且向上下扩散gap
     *     ii. d <= currentIndex  则在center = currentIndex + 1 处插入广告
     * 2. 向下插入规则：从索引center开始，center处插入广告，然后按照2,3,4的interval作为间隔周期，不断插入广告
     * 3. 向上插入规则：从索引center开始，center处插入广告，然后按照4,3,2(interval倒序)作为间隔周期，不断插入广告
     * @return 返回要插入广告的索引，从0开始计数。
     */
    private fun getInsertAdsIndexFromCenterToUpAndDown(
        firstIn: Boolean, // 是否首次进入二级页
        totalChapters: Int,     // 总的元素个数
        upNoAdsCnt: Int,     // 上面最近的广告索引
        startIndex: Int,     // 插入广告最小索引,意味着广告索引一定 >= minStartIndex
        currentIndex: Int,   // 用户所在的剧集索引
        interval: IntArray,   // 插入广告的间隔周期
        isCenterNext: Boolean // 是否在当前集下面插入广告（作为中心点）
    ): List<Int> {
        val retSet = mutableSetOf<Int>()

        if (totalChapters <= 0 || currentIndex < 0 || currentIndex >= totalChapters) {
            return emptyList()
        }
        if (interval.isEmpty()) {
            return emptyList()
        }

        /////////////  case 一 无中心点,从起始章节开始插 /////////////
        if (currentIndex <= startIndex || (firstIn && !isCenterNext)) { // 一进来18集,不是看剧很久 从头插
            var index = startIndex
            var intervalIndex = 0
            while (index < totalChapters) {
                retSet.add(index)
                index += interval[intervalIndex]
                intervalIndex = (intervalIndex + 1) % interval.size
            }
            val sortedList = retSet.sorted()
            LogUtil.i(
                "interval_chapter",
                "case1 getInsertAdsIndexFromCenterToUpAndDown result:$sortedList " +
                        "eleCnt:$totalChapters startIndex:$startIndex interval:${interval.contentToString()} chapterIndex:$currentIndex upNoAdsCnt:$upNoAdsCnt"
            )
            return sortedList
        }


        /////////////  case 二  有中心点 /////////////

        // Step 1: Determine the center index for insertion
        val center = if (isCenterNext) {
            (currentIndex + 1).also { LogUtil.i("interval_chapter", "中心点使用当前剧集:$it") }
        } else {
            // Calculate d
            val chapterPos = currentIndex + 1 // 根据公式，这里是剧集的位置，即：索引+1
            val d =
                chapterPos + interval[0] - upNoAdsCnt // 这里用interval[0], 同时要满足：如果currentIndex是广告则 upNoAdsCnt = 0
            if (d > chapterPos) d else chapterPos + 1
        }

        // Step 2: Insert ads downward from center
        var downIndex = center
        var intervalIndex = 0
        while (downIndex < totalChapters) {
            if (downIndex >= startIndex) {
                retSet.add(downIndex)
            }
            downIndex += interval[intervalIndex]
            intervalIndex = (intervalIndex + 1) % interval.size
        }

        // Step 3: Insert ads upward from center
        var upIndex = center
        intervalIndex = interval.size - 1
        while (upIndex >= startIndex) {
            if (upIndex in startIndex until totalChapters) {
                retSet.add(upIndex)
            }
            upIndex -= interval[intervalIndex]
            intervalIndex = (intervalIndex - 1 + interval.size) % interval.size
        }
        val sortedList = retSet.sorted()
        LogUtil.i(
            "interval_chapter",
            "case2 getInsertAdsIndexFromCenterToUpAndDown result:$sortedList " +
                    "eleCnt:$totalChapters startIndex:$startIndex center:$center chapterIndex:$currentIndex interval:${interval.contentToString()} upNoAdsCnt:$upNoAdsCnt 是否在当前集的下面插入广告:$isCenterNext"
        )
        return sortedList
    }


    // mock数据 test only
    fun mockAdIntervals():List<AdInterval> {
        val adIntervals = mutableListOf<AdInterval>()
        adIntervals.add(AdInterval(0, 3, 0, 0, "3", 10))
        adIntervals.add(AdInterval(3, 10, 0, 0, "3,3,2", 11))
        adIntervals.add(AdInterval(10, 0, 0, 0, "4", 12))
        return adIntervals
    }

    private fun logI(msg: String) = LogUtil.i("interval_chapter", msg)
    private fun logD(msg: String) = LogUtil.d("interval_chapter", msg)
}