package com.dz.platform.ad.sky

import android.content.Context
import com.dianzhong.base.Sky.InterstitialSky
import com.dianzhong.base.data.bean.sky.ExtraMap

/**
 * Created by FeiHK
 * Date: on 2023/2/2.
 * Desc:
 */
class InterstitialAd : Ad(), Comparable<InterstitialAd>  {
    var adSky: InterstitialSky? = null
        set(value) {
            field = value
            setBasicAdValue(value)
            setAdValue()
            setHostInfo(value?.uploadHostBean)
        }

    var isShow = false

    /**
     * 是否参与了竞价
     */
    var hasBidding = false

    private fun setAdValue() {
        adSky?.run {
            chnType = realAdSourceName
            if (isMaterialFromCache) {
                chnType += CACHE
            }
            adEdition = skyApi.sdkVersion
            codeId = slotId
            <EMAIL> = strategyInfo.slotId
            adVideoDuration = videoTime
            adSource = strategyInfo.chn_type
            cLayered = strategyInfo.layer.toString()
            order = strategyInfo.currentIdIndex.toString()
            adLoaderOrder = "$adSource-$cLayered-$order"
        }
    }

    override fun getEcpm(): Double {
//        return adSky?.strategyInfo?.ecpmYuanStr?.toDouble() ?: 0.0
        return try {
            adSky?.strategyInfo?.ecpmYuanStr?.toDouble() ?: 0.0
        } catch (e: Exception) {
            0.0
        }
    }

    fun isValid(context: Context): Boolean {
        return adSky?.isValid(context) == true
    }

    fun expiresTime(): Long {
        return adSky?.let {
            it.putTime + it.strategyInfo.cache_alive_ms
        } ?: 0L
    }

    override fun compareTo(other: InterstitialAd): Int {
        return compareValuesBy(this, other, { -it.getEcpm() }, { it.expiresTime() })
    }

    fun clickOrder(): Int? {
        return adSky?.extraMap?.get(ExtraMap.KEY.REWARD_CLICK_ORDER) as? Int
    }

    fun rewardRule(): String? {
        return adSky?.extraMap?.get(ExtraMap.KEY.REWARD_CLICK_RULE) as? String
    }
}