package com.dz.platform.ad.data

import com.blankj.utilcode.util.GsonUtils
import com.dz.foundation.base.data.kv.KVData
import com.dz.platform.ad.vo.SplashAdVo

object SplashAdKV : KVData {

    override fun getGroupName() = "com.dianzhong.ad.splash"

    /** 后台下发的开屏广告的数据持久化 */
    var splashAdOperationVo by delegate("splashAdOperationVo", "{}")         // 开屏广告信息
    var clodSplashAdId by delegate("clodSplashAdId", "")                   // 开屏冷启动广告位ID
    var splashAdMaxWaitAdTime by delegate("splashAdMaxWaitAdTime", 2000)   // 开屏启动最大等待时长
    var splashAdMinWatchTimeSec by delegate("splashAdMinWatchTime", 0L)    // 开屏出广告所需要的最小观看时间，秒

    /** 后台下发的热开屏广告的数据持久化 */
    var hotSplashAdOperationVo by delegate("hotSplashAdOperationVo", "{}")         // 热开屏广告信息
    var hotSplashAdMaxWaitAdTime by delegate("hotSplashAdMaxWaitAdTime", 2000)   // 热开屏启动最大等待时长
    var hotSplashAdMinWatchTimeSec by delegate("hotSplashAdMinWatchTime", 0L)    // 热开屏出广告所需要的最小观看时间，秒
    var hotSplashAdIntervalNum by delegate("hotSplashAdIntervalNum", 180)             // 热开屏广告展示间隔，热启动默认3分钟。退后台后，再次进入前台，会展示热启动开屏广告的时间
    var detailHotSplashAdIntervalSec by delegate("detailHotSplashAdIntervalSec", -1) // 同上。二级播放页场景。

    fun getSplashAdOperationVo(isHotSplash: Boolean): SplashAdVo {
        val vo = if (isHotSplash) hotSplashAdOperationVo else splashAdOperationVo
        return GsonUtils.fromJson(vo, SplashAdVo::class.java)
    }

    fun getSplashAdMaxWaitAdTime(isHotSplash: Boolean) = if (isHotSplash) hotSplashAdMaxWaitAdTime else splashAdMaxWaitAdTime
    fun getSplashAdMinWatchTimeSec(isHotSplash: Boolean) = if (isHotSplash) hotSplashAdMinWatchTimeSec else splashAdMinWatchTimeSec
}