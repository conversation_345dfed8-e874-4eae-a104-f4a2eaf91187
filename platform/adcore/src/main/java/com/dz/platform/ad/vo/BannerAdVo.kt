package com.dz.platform.ad.vo

import com.dianzhong.base.util.GsonUtil
import com.dz.business.base.data.bean.BaseBean
import com.dz.business.base.data.bean.RemoveAdWayVo
import com.dz.platform.ad.vo.basic.AdSharedInfo

/**
 * @Author: guyh
 * @Date: 2023/10/9 19:50
 * @Description:
 * @Version:1.0
 */
data class BannerAdVo(
    var removeAdArray: MutableList<RemoveAdWayVo>? = null, // 免广方式数组
    val adConfExt: BannerAdConfExt? = null,                // 广告配置透传字段
) : AdSharedInfo() {

    fun getAdId() = adConfExt?.adId
    fun getCloseAdIntervalNum() = adConfExt?.closeAdIntervalNum ?: 0
    fun getMaxAdFailRetryCnt() = adConfExt?.adfailRetry ?: 0
    fun getObtainAdFailInterval() = adConfExt?.obtainAdfailInterval ?: 0
    fun getMaxShowNum() = adConfExt?.maxShowNum ?: 0
    fun getExtraBannerHeight() = adConfExt?.bannerHeight ?: 0
    fun getMinWatchTimeForAdSec() = (adConfExt?.minWatchTimeForAd ?: 0L) * 60    // 出广告所需要的最小观看时间，秒

    fun getDayFirstWatchTime() = adConfExt?.firstTriggerDuration ?: 0L   // 出广告所需要的最小观看时间，秒
    fun getEcpmIntervalTime(): Long = adConfExt?.triggerInterval ?: 0L
    fun getAdEcpmFilter() = adConfExt?.filter

    fun canFeedbackAd(): Boolean = adConfExt?.adFeedbackSwitch == 1
    fun getFeedbackAdCfg(): AdFeedbackVo? = adConfExt?.adFeedbackCfg
    fun getFbCloseAdIntervalNum() = adConfExt?.adFeedbackCfg?.intervalTime ?: getCloseAdIntervalNum()//获取广告反馈后关闭间隔
}

data class MineBannerAdVo(
    val adConfExt: BannerAdConfExt? = null,                // 广告配置透传字段
) : AdSharedInfo() {
    fun getAdId() = adConfExt?.adId
    fun getCloseAdIntervalNum() = adConfExt?.closeAdIntervalNum ?: 0
    fun getObtainAdFailInterval() = adConfExt?.obtainAdfailInterval ?: 0
    fun getAdFailRetry() = adConfExt?.adfailRetry ?: 0

    fun canFeedbackAd(): Boolean = adConfExt?.adFeedbackSwitch == 1
    fun getFeedbackAdCfg(): AdFeedbackVo? = adConfExt?.adFeedbackCfg
    fun getFeedbackAdCfgStr(): String? = GsonUtil.toJson(adConfExt?.adFeedbackCfg)
}

data class BannerAdConfExt(
    var adId: String? = null,           // 广告位ID
    var closeAdIntervalNum: Int? = 0,   // 关闭广告展示间隔  单位：秒
    var adfailRetry: Int? = 0,          // 失败重试次数上限
    var obtainAdfailInterval: Int? = 0, // 获取广告失败间隔  单位：秒
    var maxShowNum: Int? = 0,           // 最大展示次数
    val bannerHeight: Int? = 0,         // 增加的高度
    val minWatchTimeForAd: Long? = 0,   // 出广告所需要的最小观看时间，分钟

    val firstTriggerDuration: Long? = 0L, //首次触发时间,单位秒
    val triggerInterval: Long? = 0L,      //不满足展示条件后，重新触发下次请求的间隔，单位秒
    val filter: List<BannerAdDurations>? = null,

    val adFeedbackSwitch: Int? = 0,//反馈功能开关：adFeedbackSwitch，int类型，1-开，0-关
    val adFeedbackCfg: AdFeedbackVo? = null,//广告反馈
) : BaseBean()

data class BannerAdDurations(
    val playDurationStart: Int = 0,
    val playDurationEnd: Int = 0, // 0 代表无限制,秒
    val ecpm: Long = 0L // 单位分
) : BaseBean()