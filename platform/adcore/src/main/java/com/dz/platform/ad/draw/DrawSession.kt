package com.dz.platform.ad.draw

import com.dz.foundation.base.utils.LogUtil
import com.dz.platform.ad.data.DrawAdKV
import com.dz.platform.ad.lifecycle.PlayScene

object DrawSession {

    private var waitInsertAdOnNewSession: Boolean = false
    private var forbidCurrentSession: Boolean = false // 在当前session周期内不插入广告，因为下个本来已经是广告了不需要通过session计算中心点插入
    private var sessionCallOnce: Boolean = false
    var checkAdIntervalAndRefreshDataList: (() -> Unit)? = null
    var nextPositionIsAd: (() -> <PERSON><PERSON>an)? = null
    var reportSessionEnd: (() -> Unit)? = null

    fun onPlayStart(playScene: PlayScene) {
        LogUtil.d("userSsBehavior", "onPlayStart:$playScene")
        if (playScene == PlayScene.DETAIL) {
            judgeDetailSessionOnStartPlay()
        }
    }

    fun onPlayStop(playScene: PlayScene) {
        LogUtil.d("userSsBehavior", "onPlayStop:$playScene")
        if (playScene == PlayScene.DETAIL) {
            saveDetailInterruptedTimeOnPlayStop()
        }
    }

    fun saveDetailSessionWatchDurationOnPlaying(playScene: PlayScene, accumulativeTotal: Float) {
        if (playScene != PlayScene.DETAIL) { // 二级draw场景
            return
        }
        DrawAdKV.sessionWatchedDurationSec += accumulativeTotal // 当前会话的观看时长
        // 开关打开，且上次会话观看的时间大于配置时间
        if (isSwitchOpen()) {
            if (nextPositionIsAd?.invoke() == true) {
                forbidCurrentSession = true
                LogUtil.i("userSsBehavior", "下一个位置已经是广告，无需用session计算中心点插入了")
                return
            }
            if (!forbidCurrentSession && DrawAdKV.sessionStartPlayDuration != 0 && DrawAdKV.lastSessionWatchedDurationSec >= DrawAdKV.lastSessionDuration &&
                DrawAdKV.sessionWatchedDurationSec > DrawAdKV.sessionStartPlayDuration
            ) {
                if (sessionCallOnce) {
                    sessionCallOnce = false
                    LogUtil.i(
                        "userSsBehavior",
                        "当前会话满足观看时长，尝试在当前集下面插入广告。当前会话观看时长:${DrawAdKV.sessionWatchedDurationSec}s 大于等于配置时长:${DrawAdKV.sessionStartPlayDuration}s"
                    )
                    sessionWatchEnoughInsertAd = true
                    checkAdIntervalAndRefreshDataList?.invoke() // 这个方法调用要保证在session内只调用一次
                }
            } else {
                LogUtil.d("userSsBehavior", "当前会话观看时长:${DrawAdKV.sessionWatchedDurationSec}s 小于配置时长:${DrawAdKV.sessionStartPlayDuration}s forbidCurrentSession:$forbidCurrentSession")
            }
        }
    }

    // 观看时间满足下一集插入广告
    private var sessionWatchEnoughInsertAd = false

    // 切集插入
    private fun switchEpisodeInsert(): Boolean =
        (!forbidCurrentSession && isSwitchOpen() && waitInsertAdOnNewSession && DrawAdKV.sessionStartCompleteVideo == 1)
            .also {
                if (it) {
                    LogUtil.i("userSsBehavior", "满足切集插入条件 interval_chapter")
                } else {
                    LogUtil.i("userSsBehavior", "不满足切集插入条件（会话内已插过或开关关） sessionStartCompleteVideo:${DrawAdKV.sessionStartCompleteVideo} forbidCurrentSession:$forbidCurrentSession isSwitchOpen:${isSwitchOpen()}")
                }
            }

    // 是否需要在当前集下面插入广告
    fun isSessionInsert(): Boolean {
        val sessionInsert = sessionWatchEnoughInsertAd || switchEpisodeInsert()
        if (sessionInsert) {
            reset()
        }
        isEarlyAd = sessionInsert
        return sessionInsert
    }

    fun reset() {
        sessionWatchEnoughInsertAd = false
        waitInsertAdOnNewSession = false
        LogUtil.d("userSsBehavior", "重置无需插入状态")
    }

    private fun judgeDetailSessionOnStartPlay() {
        if (DrawAdKV.interruptPlayEndTimeMs == 0L) {
            recordNewSessionOnStartPlay()
            LogUtil.i("userSsBehavior", "播放结束时间戳不在今天，开启新会话")
            return
        }
        // 计算当天的中断时长，秒
        val interruptDuration =
            ((System.currentTimeMillis() - DrawAdKV.interruptPlayEndTimeMs) / 1000)
        if (interruptDuration >= DrawAdKV.sessionExpireThreshold) {
            recordNewSessionOnStartPlay()
            LogUtil.i("userSsBehavior", "")
            LogUtil.i(
                "userSsBehavior",
                "开启新会话。中断时长为${interruptDuration}s 大于等于阈值${DrawAdKV.sessionExpireThreshold}s " +
                        " 旧会话时长:${DrawAdKV.lastSessionWatchedDurationSec}s 旧会话阈值:${DrawAdKV.lastSessionDuration}s"
            )
        } else {
            LogUtil.d(
                "userSsBehavior",
                "会话继续。中断时长为${interruptDuration}s 小于阈值${DrawAdKV.sessionExpireThreshold}s"
            )
        }
    }

    // 间隔时长足够了，开启新会话
    private fun recordNewSessionOnStartPlay() {
        forbidCurrentSession = false // 重置当前会话允许插入广告
        DrawAdKV.lastSessionWatchedDurationSec = DrawAdKV.sessionWatchedDurationSec     // 保存旧会话时长
        DrawAdKV.sessionWatchedDurationSec = 0F                                         // 重置当前会话的观看时长
        waitInsertAdOnNewSession = true
        sessionCallOnce = true
        if (DrawAdKV.lastSessionStartPlayTimeSec != 0L) {
            reportSessionEnd?.invoke() // 在新会话开始的时机，上报上一个会话结束的点
        }
        DrawAdKV.lastSessionStartPlayTimeSec = System.currentTimeMillis() / 1000
    }

    private fun saveDetailInterruptedTimeOnPlayStop() {
        DrawAdKV.interruptPlayEndTimeMs = System.currentTimeMillis()
    }

    private fun isSwitchOpen(): Boolean {
        return DrawAdKV.sessionExpireThreshold != 0
    }

    private var isEarlyAd: Boolean = false
    fun isEarlyAd(): Boolean {
        val ret = isEarlyAd
        isEarlyAd = false // 重置状态
        return ret
    }
}