package com.dz.platform.ad.manager

import android.app.Activity
import com.dianzhong.base.data.bean.sky.RewardCloseParams
import com.dianzhong.base.listener.ActivityLifecycleCallbackAdapter
import com.dianzhong.core.manager.loader.InterstitialLoader
import com.dz.foundation.base.utils.LogUtil
import com.dz.platform.ad.AdManager
import com.dz.platform.ad.callback.BaseAdLoadCacheCallback
import com.dz.platform.ad.callback.BaseAdLoadCallback
import com.dz.platform.ad.callback.BaseAdShowCallback
import com.dz.platform.ad.sky.InterstitialAd
import com.dz.platform.ad.sky.SkyAd

/**
 * 这个类应该叫 MallAdManager, 因为不仅是我的商城的广告，还有二级页的商城广告
 * 有缓存能力
 */
object MineMallAdManager {

    const val TAG = "MineMallAdManager"

    private var mIsLoading: Boolean = false

    private var interstitialAds: HashMap<String, InterstitialAd> = hashMapOf()

    var loader: InterstitialLoader? = null

    private var showCall: BaseAdShowCallback<InterstitialAd>? = null


    fun preloadAd(
        activity: Activity,
        adId: String,
        requestId: String?,
        blockConfigId: String?,
        width: Int,
        height: Int,
        callback: BaseAdLoadCallback<InterstitialAd>?
    ) {
        LogUtil.d(
            TAG,
            "开始预加载广告，广告位：$adId cacheSize: " + interstitialAds.size + " activity=${activity}"
        )
        AdManager.run {
            loader = loadInterstitialAd(
                activity = activity,
                adId = adId,
                requestId = requestId,
                blockConfigId,
                bookId = "",
                chapterId = "",
                width = width,
                height = height,
                loadCallback = object : BaseAdLoadCallback<InterstitialAd> {
                    override fun onStartLoad() {
                        LogUtil.d(MineMallAdManager.TAG, "preloadAd-onStartLoad")
                    }

                    override fun onLoadError(code: Int, msg: String) {
                        LogUtil.d(MineMallAdManager.TAG, "preloadAd-onLoadError $code $msg")
                        callback?.onLoadError(code, msg)
                    }

                    override fun onMaterialStartLoad(sky: SkyAd?) {

                    }

                    override fun onMaterialStatusChanged(sky: SkyAd?) {

                    }

                    override fun onLoadSuccess(ad: InterstitialAd) {
                        LogUtil.d(MineMallAdManager.TAG, "preloadAd-onLoadSuccess")
                        interstitialAds[adId] = ad
                        callback?.onLoadSuccess(ad)
                    }
                }
            )
        }
        val application = activity.application
        val loadActivityHash = activity.hashCode()
        application.registerActivityLifecycleCallbacks(object : ActivityLifecycleCallbackAdapter() {
            override fun onActivityDestroyed(activity: Activity) {
                if (loadActivityHash == activity.hashCode()) {
                    LogUtil.d(TAG, "加载的activity销毁，移除mall缓存广告：$adId")
                    interstitialAds.remove(adId)
                    application.unregisterActivityLifecycleCallbacks(this)
                }
            }
        })
    }

    fun loadAd(
        activity: Activity,
        adId: String,
        requestId: String?,
        blockConfigId: String?,
        timeout: Long,
        width: Int,
        height: Int,
        callback: BaseAdLoadCacheCallback<InterstitialAd>?,
    ) {
        LogUtil.d(TAG, "开始加载广告，广告位：$adId")
        if (interstitialAds.containsKey(adId)) {
            mIsLoading = false
            LogUtil.d(TAG, "有缓存广告，广告请求结束")
            interstitialAds[adId]?.let {
                callback?.onLoadSuccess(it, true)
            }
            interstitialAds.remove(adId)
            return
        }
        loader?.cancelAdsLoading()
        AdManager.run {
            loader = loadInterstitialAd(
                activity = activity,
                adId = adId,
                requestId = requestId,
                blockConfigId,
                bookId = "",
                chapterId = "",
                width,
                height,
                timeout * 1000L,
                loadCallback = object : BaseAdLoadCallback<InterstitialAd> {
                    override fun onStartLoad() {
                        callback?.onStartLoad()
                        mIsLoading = true
                        LogUtil.d(MineMallAdManager.TAG, "onStartLoad")
                    }

                    override fun onLoadError(code: Int, msg: String) {
                        LogUtil.d(MineMallAdManager.TAG, "onLoadError $code $msg")
                        mIsLoading = false
                        callback?.onLoadError(code, msg)
                    }

                    override fun onMaterialStartLoad(sky: SkyAd?) {
                    }

                    override fun onMaterialStatusChanged(sky: SkyAd?) {
                    }

                    override fun onLoadSuccess(ad: InterstitialAd) {
                        LogUtil.d(MineMallAdManager.TAG, "onLoadSuccess")
                        mIsLoading = false
                        callback?.onLoadSuccess(ad, false)
                    }
                }
            )
        }
    }


    fun showAd(ad: InterstitialAd, showCallback: BaseAdShowCallback<InterstitialAd>?) {
        AdManager.showInterstitialAd(ad, showCallback = object : BaseAdShowCallback<InterstitialAd> {
            override fun onShow(ad: InterstitialAd) {
                LogUtil.d(TAG, "--onShow")
                ad.isShow = true
                showCallback?.onShow(ad)
            }

            override fun onShowError(ad: InterstitialAd, code: Int, msg: String) {
                LogUtil.d(TAG, "onShowError")
            }

            override fun onClick(ad: InterstitialAd) {
                LogUtil.d(TAG, "--onClick")
                showCallback?.onClick(ad)
            }

            override fun onReward(ad: InterstitialAd) {
                LogUtil.d(TAG, "onReward")
            }

            override fun onClose(ad: InterstitialAd, rewardCloseParams: RewardCloseParams?) {
                LogUtil.d(TAG, "onClose")
                showCallback?.onClose(ad, rewardCloseParams)
            }
        })
    }

    fun cancelAdsLoading() {
        LogUtil.d(TAG, "cancelAdsLoading")
        loader?.cancelAdsLoading()  // 广告SDK暂停
        mIsLoading = false
    }

    fun onDestroy(sotId: String) {
        LogUtil.d(TAG, "onDestroy 二级Draw sotId=${sotId}")
        loader?.cancelAdsLoading()
        interstitialAds.remove(sotId)
        showCall = null
        cancelAdsLoading()
    }

    fun onDestroy() {
        LogUtil.d(TAG, "个人中心页我的商城onDestroy")
        loader?.cancelAdsLoading()
        interstitialAds.clear()
        showCall = null
        cancelAdsLoading()
    }

}