package com.dz.platform.ad

import com.dianzhong.core.manager.SkyManager
import com.dz.foundation.base.utils.LogUtil
import com.dz.platform.ad.data.AdKV
import com.dz.platform.ad.data.DrawAdKV
import com.dz.platform.ad.vo.AdDurations

object DrawForceViewTimeUtil {

    private const val TAG = "DrawViewTime"

    private const val DEFAULT_DETAIL_DRAW_FORCE_VIEW_TIME = "0" // 二级页默认的Draw强制观看时间
    private val DEFAULT_DETAIL_DRAW_VIEW_TIME_ARRAY =
        DEFAULT_DETAIL_DRAW_FORCE_VIEW_TIME.map { it.toString().toInt() }.toIntArray()

    var remoteDetailAdDrawViewTimes: List<AdDurations>? = null // 二级页Draw广告强制观看时长策略
    var chapterForceWatchTime: String? = null                  // 剧集分段

    private var recordLastTimeIndex = 0 //记录上次使用观看集合的角标

    /**
     * 放到广告加载时机，计算出一个和广告加载绑定的强制观看时间
     * 调用后会会使配置如[3,2,1]中使用位置后移一位
     */
//    fun getNextUsedDrawViewTime(): Int {
//        val result = peekNextForceViewTime("加载")
//        recordLastTimeIndex++
//        return result
//    }

    // 调用后会会使配置如[3,2,1]中使用位置后移一位
    fun increaseIndex(where: String) {
        recordLastTimeIndex++
        LogUtil.d(TAG, "")
        LogUtil.i(TAG, "$where，移动强制观看时长index到：$recordLastTimeIndex adRequestSeq")
    }

    /**
     * 只是查询一下下一个强制观看时长，但不移动角标
     */
    fun peekNextForceViewTime(
        where: String, changedCallback: ((newTime: Int) -> Unit)? = null
    ): Int {
        // 根据观看时长，ecpm计算间隔周期
        val viewTimeStr = calAdViewTimeStr(remoteDetailAdDrawViewTimes)
        val timesArray = runCatching {
            viewTimeStr.split(",").map { it.toInt() }.toIntArray()
        }.onFailure { e -> LogUtil.e(TAG, "出现异常：$e") }
            .getOrElse { DEFAULT_DETAIL_DRAW_VIEW_TIME_ARRAY }

        // 保存本次使用的时间
        val isChanged: Boolean
        if (DrawAdKV.detailDrawAdViewTime != viewTimeStr) {
            isChanged = true
            LogUtil.i(
                TAG,
                "强制观看时长发生变化，保存新的 ${DrawAdKV.detailDrawAdViewTime} -> $viewTimeStr adRequestSeq"
            )
            resetIndex()
            DrawAdKV.detailDrawAdViewTime = viewTimeStr
        } else {
            isChanged = false
        }
        if (recordLastTimeIndex >= timesArray.size) {
            resetIndex()
        }
        val result = timesArray[recordLastTimeIndex]
        if (isChanged) {
            changedCallback?.invoke(result)
        }
        LogUtil.d(
            TAG,
            "  强制观看时长 配置=$viewTimeStr 现在取第 ${recordLastTimeIndex} 个 result=$result $where  adRequestSeq"
        )
        return result
    }

    fun resetIndex() {
        recordLastTimeIndex = 0
        LogUtil.d(TAG, "重置强制观看时长index到0 adRequestSeq")
    }

    /**
     * 1. 优先使用chapterForceWatchTime
     * 2. 根据ecpm,观看时长 来决定使用哪个强制观看时长的配置
     */
    private fun calAdViewTimeStr(
        remoteTimes: List<AdDurations>?
    ): String {
        chapterForceWatchTime?.takeIf { it.isNotEmpty() }?.let {
            LogUtil.d(TAG, "使用章节分段强制观看时长 $it")
            return it
        }
        var result = DEFAULT_DETAIL_DRAW_FORCE_VIEW_TIME
        val lastEcpmCent = if (DrawAdKV.todayLastDrawAdEcpmCent > 0) {
            DrawAdKV.todayLastDrawAdEcpmCent
        } else {
            SkyManager.getInstance().userDrawEcpmCent
        }
        val watchDuration = AdKV.todayWatchedDurationSec2
        // 看看lastEcpmCent和watchDuration的值落在remoteDetailAdViewTime的哪个区间。注意，如果playDurationEnd为0，则代表右侧无限制，ecpmEnd为0则代表右侧无限制
        remoteTimes?.forEach {
            if (watchDuration >= it.playDurationStartSec && (it.playDurationEndSec == 0 || watchDuration < it.playDurationEndSec) &&
                lastEcpmCent >= it.ecpmStartCent && (it.ecpmEndCent == 0L || lastEcpmCent < it.ecpmEndCent)
            ) {
                result = it.durations ?: DEFAULT_DETAIL_DRAW_FORCE_VIEW_TIME
                return@forEach
            }
        }
        LogUtil.d(
            TAG,
            "强制观看时长 lastEcpmCent:$lastEcpmCent watchDuration:$watchDuration result:$result"
        )
        return result
    }

    fun mockAdTimes():List<AdDurations> {
        val adIntervals = mutableListOf<AdDurations>()
        adIntervals.add(AdDurations(0, 5, 0, 0, "6,5,0,4"))
        adIntervals.add(AdDurations(5, 15, 0, 0, "3,2,1,0"))
        adIntervals.add(AdDurations(15, 0, 0, 0, "4,0,3,0,2"))
        return adIntervals
    }

}