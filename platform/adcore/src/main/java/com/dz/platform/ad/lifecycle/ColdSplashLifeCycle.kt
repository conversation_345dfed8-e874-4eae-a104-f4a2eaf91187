package com.dz.platform.ad.lifecycle

import com.dz.foundation.base.utils.LogUtil
import com.dz.platform.ad.data.DrawDataUtil.isDetailFromColdStart
import com.dz.platform.ad.data.WelfareAnchorAdDataUtil.isDetailWelfareFromColdStart

class ColdSplashLifeCycle {

    // onCreate -> 同意隐私协议 -> startSplashLogic()
    fun startSplashLogic() {
        LogUtil.i("interval_chapter_detail", "startSplashLogic")
        isDetailFromColdStart = true
        isDetailWelfareFromColdStart = true
    }
}