package com.dz.platform.ad

import com.dianzhong.base.util.AppUtil
import com.dianzhong.core.manager.SkyManager
import com.dz.foundation.base.utils.LogUtil
import com.dz.platform.ad.data.AdKV
import com.dz.platform.ad.data.DrawAdKV
import com.dz.platform.ad.data.FLMDAdKV
import com.dz.platform.ad.vo.AdInterval

object WelfareAnchorIntervalUtil {

    const val TAG = "WelfareAnchor_chapter"

    private const val DEFAULT_AD_INTERVAL = "3" // 二级页福利锚点默认的插入广告间隔
    private val DEFAULT_AD_INTERVAL_ARRAY =
        DEFAULT_AD_INTERVAL.map { it.toString().toInt() }.toIntArray()

    const val MIN_START_INDEX = 2 // 起始章节最小值，后台不允许小于2

    private var remoteAdIntervals: List<AdInterval>? = null // 二级页添加福利锚点贴片广告策略
//    private var remoteAdIntervals: List<AdInterval>? = mockAdIntervals() // todo remove test code

    private var adInsertAdsIndexSet: Set<Int>? = null
    private var adInsertStartIndex: Int? = null

    fun setAdIntervals(adIntervals: List<AdInterval>?) {
        remoteAdIntervals = adIntervals
    }


    // 二级页展示广告章节是否发生变化
    fun isAdIntervalChanged(currChapterIndex: Int): Boolean {
        return isAdIntervalChanged(
            currChapterIndex = currChapterIndex,
            defaultInterval = DEFAULT_AD_INTERVAL,
            remoteIntervals = remoteAdIntervals,
            lastInterval = FLMDAdKV.detailFLMDAdLastInterval
        ) || isDetailLongTimeWatch(currChapterIndex)
    }

    // 获取二级页展示广告的索引set
    fun getInsertAdsIndexSet(
        firstIn: Boolean = false,    // 是否首次进入二级页
        elementCnt: Int,             // 总的元素个数
        upNoAdsCnt: Int,             // 上面最近的带锚点广告的剧集位置
        currChapterIndex: Int,       // 用户所在的章节索引
    ): Set<Int> {
        // 根据观看时长，ecpm计算间隔周期
        val interval = selectAdInterval(currChapterIndex, remoteAdIntervals)
        val intervalStr = interval?.interval ?: DEFAULT_AD_INTERVAL
        val intervalTimeSec = interval?.intervalTimeSec ?: 0

        val intervalArray = runCatching {
            intervalStr.split(",").map { it.toInt() }.toIntArray()
        }.onFailure { e -> LogUtil.e(TAG, "出现异常：$e") }
            .getOrElse { DEFAULT_AD_INTERVAL_ARRAY }

        // 保存本次使用的间隔章节，时长
        if (FLMDAdKV.detailFLMDAdLastInterval != intervalStr) {
            logI(
                "展示广告章节发生变化，保存新的 ${FLMDAdKV.detailFLMDAdLastInterval} -> $intervalStr timeInterval:$intervalTimeSec"
            )
        }
        FLMDAdKV.detailFLMDAdLastInterval = intervalStr
        FLMDAdKV.detailFLMDAdLastIntervalTimeSec = intervalTimeSec

        val startIndex =
            FLMDAdKV.detailFLMDAdStartIndex.coerceAtLeast(FLMDAdKV.detailFLMDAdTermination)
        adInsertStartIndex = startIndex

        val result =
            getInsertAdsIndexSet(
                firstIn = firstIn,
                interval = intervalArray,
                startIndex = startIndex,
                elementCnt = elementCnt,
                upNoAdsCnt = upNoAdsCnt,
                currChapterIndex = currChapterIndex,
                isLongTimeWatch = isDetailLongTimeWatch(currChapterIndex)
            )
        adInsertAdsIndexSet = result
        return result
    }


    ////////////// private function ///////////

    // 判断间隔章节是否发生变化
    private fun isAdIntervalChanged(
        currChapterIndex: Int,
        defaultInterval: String,
        remoteIntervals: List<AdInterval>?,
        lastInterval: String
    ): Boolean {
        val newInterval =
            selectAdInterval(currChapterIndex, remoteIntervals)?.interval ?: defaultInterval

        return (lastInterval != newInterval).also { isChanged ->
            val msg =
                "福利锚点展示位置 是否发生变更: $isChanged, lastInterval: $lastInterval, newInterval: $newInterval"
            if (isChanged) logI(msg) else logD(msg)
        }
    }

    // 福利锚点观剧时长达到最小变化
    private fun isDetailLongTimeWatch(currChapterIndex: Int): Boolean {
        //记录上次锚点出现时间
        return (adInsertStartIndex?.let {
            currChapterIndex >= it && (AdKV.flmdImpHistoryWatchedSec > 0 && FLMDAdKV.detailFLMDAdLastIntervalTimeSec > 0 &&
                    (AdKV.historyWatchedDurationSec - AdKV.flmdImpHistoryWatchedSec) >= FLMDAdKV.detailFLMDAdLastIntervalTimeSec)
        } ?: false).also { isChanged ->
            val msg =
                "二级页看剧很久: $isChanged, 当前:${AdKV.historyWatchedDurationSec}-福利锚点广告曝光:${AdKV.flmdImpHistoryWatchedSec}=${AdKV.historyWatchedDurationSec - AdKV.flmdImpHistoryWatchedSec}秒, " +
                        "间隔时间:${FLMDAdKV.detailFLMDAdLastIntervalTimeSec}秒 间隔章节:${FLMDAdKV.detailFLMDAdLastInterval} 起始章节:$adInsertStartIndex 当前章节:$currChapterIndex"
            if (isChanged && !AppUtil.isFastPrint()) logI(msg) else logD(msg)
        }
    }

    // 根据ecpm,观看时长 来决定使用哪个间隔章节的配置
    private fun selectAdInterval(
        currChapterIndex: Int,
        remoteIntervals: List<AdInterval>?
    ): AdInterval? {
        val startMs = System.currentTimeMillis()
        var result: AdInterval? = null
        val lastEcpmCent = if (FLMDAdKV.todayLastFLMDAdEcpmCent > 0) {
            FLMDAdKV.todayLastFLMDAdEcpmCent
        } else {
            SkyManager.getInstance().userDrawEcpmCent
        }
        val watchDuration = AdKV.todayWatchedDurationSec2
        // 看看lastEcpmCent和watchDuration的值落在remoteDetailAdInterval的哪个区间。注意，如果playDurationEnd为0，则代表右侧无限制，ecpmEnd为0则代表右侧无限制
        remoteIntervals?.forEach {
            if (watchDuration >= it.playDurationStartSec && (it.playDurationEndSec == 0 || watchDuration < it.playDurationEndSec) &&
                lastEcpmCent >= it.ecpmStartCent && (it.ecpmEndCent == 0L || lastEcpmCent < it.ecpmEndCent)
            ) {
                result = it
                return@forEach
            }
        }
        logD(
            "选择展示章节配置 当前章节:$currChapterIndex ecpm:${lastEcpmCent}分 playDuration:${watchDuration}秒 result:$result 耗时：${System.currentTimeMillis() - startMs}ms"
        )
        return result
    }

    // 获取展示的广告索引set
    private fun getInsertAdsIndexSet(
        firstIn: Boolean = false, // 是否首次进入二级页
        interval: IntArray,
        startIndex: Int,     // 起始章节
        elementCnt: Int,     // 总的元素个数
        upNoAdsCnt: Int,     // 上面最近的广告索引
        currChapterIndex: Int,   // 用户所在的章节索引
        isLongTimeWatch: Boolean // 是否是观看时长引发的变化
    ): Set<Int> {
        val list = getInsertAdsIndexFromCenterToUpAndDown(
            firstIn = firstIn,
            elementCnt = elementCnt,
            upNoAdsCnt = upNoAdsCnt,
            startIndex = startIndex,
            currentIndex = currChapterIndex,
            interval = interval,
            isLongTimeWatch = isLongTimeWatch
        )
        return list.toSet()
    }

    /**
     * 一：如果currentIndex <= startIndex, 则在startIndex处开始按照2,3,4的interval作为间隔周期，不断插入广告
     * 二：从中心点center处插入广告，并且向列表上下扩散插入广告，那么规则如下：
     * 1. 索引center的计算规则(可以保证在currentIndex的后面插入)
     *     计算公式：d = currentIndex + X2 - upNoAdsCnt；用户刚切到currentIndex位置; X2为新的间隔章节；upNoAdsCnt为切剧后（包含自己）上面有几个不含广告的连续集数,如果currentIndex是广告则upNoAdsCnt=0
     *     i.  d  > currentIndex  则在center = d（ > currentIndex） 处插入广告，并且向上下扩散gap
     *     ii. d <= currentIndex  则在center = currentIndex + 1 处插入广告
     * 2. 向下插入规则：从索引center开始，center处插入广告，然后按照2,3,4的interval作为间隔周期，不断插入广告
     * 3. 向上插入规则：从索引center开始，center处插入广告，然后按照4,3,2(interval倒序)作为间隔周期，不断插入广告
     * @return 返回要插入广告的索引，从0开始计数。
     */
    private fun getInsertAdsIndexFromCenterToUpAndDown(
        firstIn: Boolean, // 是否首次进入二级页
        elementCnt: Int,     // 总的元素个数
        upNoAdsCnt: Int,     // 上面最近的广告索引
        startIndex: Int,     // 插入广告最小索引,意味着广告索引一定 >= minStartIndex
        currentIndex: Int,   // 用户所在的剧集索引
        interval: IntArray,   // 插入广告的间隔周期
        isLongTimeWatch: Boolean // 是否是观看时长引发的变化
    ): List<Int> {
        val retSet = mutableSetOf<Int>()

        if (elementCnt <= 0 || currentIndex < 0 || currentIndex >= elementCnt) {
            return emptyList()
        }
        if (interval.isEmpty()) {
            return emptyList()
        }

        /////////////  case 一 无中心点,从起始章节开始插 /////////////
        if (currentIndex <= startIndex || (firstIn && !isLongTimeWatch)) { // 一进来18集,不是看剧很久 从头插
            var index = startIndex
            var intervalIndex = 0
            while (index < elementCnt) {
                retSet.add(index)
                index += (interval[intervalIndex] + 1) // 需要+1
                intervalIndex = (intervalIndex + 1) % interval.size
            }
            val sortedList = retSet.sorted()
            logI(
                "case1 getInsertAdsIndexFromCenterToUpAndDown result:$sortedList " +
                        "eleCnt:$elementCnt startIndex:$startIndex interval:${interval.contentToString()} chapterIndex:$currentIndex upNoAdsCnt:$upNoAdsCnt"
            )
            return sortedList
        }


        /////////////  case 二  有中心点 /////////////

        // Step 1: Determine the center index for insertion
        val center = if (isLongTimeWatch) {//时长变化导致的时 中心点就是当前集
            (currentIndex).also { logI("中心点使用当前剧集:$it") }
        } else {
            // Calculate d
            val chapterPos = currentIndex + 1 // 根据公式，这里是剧集的位置，即：索引+1
            val d =
                chapterPos + interval[0] - upNoAdsCnt // 这里用interval[0], 同时要满足：如果currentIndex是广告则 upNoAdsCnt = 0
            if (d > chapterPos) d else chapterPos + 1
        }
        // Step 2: Insert ads downward from center
        var adIndex = center
        var intervalIndex = 0
        while (adIndex < elementCnt) {
            if (adIndex >= startIndex) {
                retSet.add(adIndex)
            }
            adIndex += (interval[intervalIndex] + 1) // 需要+1
            intervalIndex = (intervalIndex + 1) % interval.size
        }

        // Step 3: Insert ads upward from center
        var upIndex = center
        intervalIndex = interval.size - 1
        while (upIndex >= startIndex) {
            if (upIndex in startIndex until elementCnt) {
                retSet.add(upIndex)
            }
            upIndex -= (interval[intervalIndex] + 1) // 需要+1
            intervalIndex = (intervalIndex - 1 + interval.size) % interval.size
        }
        val sortedList = retSet.sorted()
        logI(
            "case2 getInsertAdsIndexFromCenterToUpAndDown result:$sortedList " +
                    "eleCnt:$elementCnt startIndex:$startIndex center:$center chapterIndex:$currentIndex interval:${interval.contentToString()} upNoAdsCnt:$upNoAdsCnt 看剧很久:$isLongTimeWatch"
        )
        return sortedList
    }


    // mock数据 test only
    fun mockAdIntervals(): List<AdInterval> {
        val adIntervals = mutableListOf<AdInterval>()
        adIntervals.add(AdInterval(0, 3, 0, 0, "3", 10))
        adIntervals.add(AdInterval(3, 10, 0, 0, "3,3,2", 11))
        adIntervals.add(AdInterval(10, 0, 0, 0, "4", 12))
        return adIntervals
    }

    private fun logI(msg: String) = LogUtil.i("WelfareAnchor_chapter", msg)
    private fun logD(msg: String) = LogUtil.d("WelfareAnchor_chapter", msg)
}