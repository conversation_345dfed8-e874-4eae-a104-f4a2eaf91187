package com.dz.platform.ad.data

import com.dianzhong.feedwall.FeedWallManager
import com.dz.foundation.base.data.kv.KVData
import com.dz.foundation.base.utils.KVXCache
import com.dz.foundation.base.utils.LogUtil

object FeedAdKV : KVData {

    override fun getGroupName() = "com.dianzhong.ad.feed"


    // 每个 slotId 对应一个单独的流量请求序号
    /**
     * 对外调用：检查任务状态，获取并推进 seq
     */
    fun handleTrafficSeq(slotId: String): Int {
        LogUtil.d("FeedAdKV", "handleTrafficSeq slotId = $slotId")
        return if (FeedWallManager.isTaskInProgress(slotId)) {
            // 当前 slotId 有正在进行的任务，直接返回当前序号
            val seq = getTrafficSeq(slotId)
            LogUtil.d("FeedAdKV", "handleTrafficSeq 当前 slotId 有正在进行的任务，直接返回当前序号 = $seq")
            seq
        } else {
            // 无进行中任务，将序号自增后返回
            val seq = incrementTrafficSeq(slotId)
            LogUtil.d("FeedAdKV", "handleTrafficSeq 无进行中任务，将序号自增后返回 = $seq")
            seq
        }
    }

    /**
     * 获取当前 seq 值（不存在时返回默认值 1）
     */
    private fun getTrafficSeq(slotId: String): Int {
        val key = makeKey(slotId)
        val trafficSeq = KVXCache.getValue(key, 1)
        LogUtil.d("FeedAdKV", "getTrafficSeq slotId = $slotId trafficSeq=$trafficSeq ")
        return trafficSeq
    }

    /**
     * 将 slotId 对应的 seq 值 +1 并保存
     */
    private fun incrementTrafficSeq(slotId: String): Int {
        val key = makeKey(slotId)
        val current: Int = KVXCache.getValue(key, 1)
        val next = current + 1
        KVXCache.setValue(key, next)
        return next
    }

    /**
     * 生成动态 Key
     */
    private fun makeKey(slotId: String): String {
        return getGroupName() + "." + "feedWallAdTrafficReachSeq_$slotId"
    }



//
//    // 流量请求序号
//    var feedWallAdTrafficReachSeq by delegate("feedWallAdTrafficReachSeq", 1) // cpx 流量请求序列号
//
//    fun getfeedWallAdTrafficReachSeq(slotId: String){
//
//        if ( FeedWallManager.INSTANCE.isTaskInProgress(slotId)){
//            //当前soltId有正在进行中的任务
//
//        }else{
//            //当前soltId有正在进行中的任务
//
//        }
//
//    }
//
//    private fun getKey(slotId: String): String {
//        return "feedWallAdTrafficReachSeq_+$slotId"
//    }


}


