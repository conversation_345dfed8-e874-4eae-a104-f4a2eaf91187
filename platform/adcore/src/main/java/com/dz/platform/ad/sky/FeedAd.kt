package com.dz.platform.ad.sky

import android.content.Context
import android.view.View
import com.dianzhong.base.Sky.FeedSky
import com.dianzhong.base.Sky.getCacheExpireLeftTime
import com.dianzhong.base.data.bean.sky.DZFeedSky
import com.dianzhong.base.data.bean.sky.ExtraMap
import com.dianzhong.base.data.bean.sky.FeedAdHolder
import com.dianzhong.base.data.bean.sky.ShowParam
import com.dianzhong.base.data.constant.SkyStyle
import com.dianzhong.base.eventbus.AdType
import com.dianzhong.core.manager.loader.FeedLoader
import com.dz.foundation.base.utils.LogUtil
import com.dz.platform.ad.data.DrawAdKV

/**
 * Created by FeiHK
 * Date: on 2023/2/2.
 * Desc:信息流
 */
class FeedAd : Ad() {

    /**
     * 小窗播放下会跳过沉浸式广告
     * 这个标志用来记录是否上报过
     */
    var hasReportWhenPipSkip = false

    var mFeedLoader: FeedLoader? = null
    var mFeedAdHolder: FeedAdHolder? = null
    var mFeedSky: FeedSky? = null     // mFeedAdHolder?.dzFeedSkyList?.get(0)
    var mDzFeedSky: DZFeedSky? = null // mFeedSky?.resultList?.get(0)
    var mTemplateView: View? = null

    var mAdReqSeq: Int = 0
    var mReqParam: SwipeReqParam? = null
    var mAdDrawForceTime: Int = 0

    var isShow = false
        set(value) {
            field = value
            LogUtil.d("adRequestSeq", "  setIsShow:$value")
        }

    var sendAdDirection = false
    var timeSpent: Long = 0//耗时

    var showTime: Long = 0 //广告曝光时间戳

    var finishStatus: Int = 0//广告是视频时，1代表视频播完了，0代表视频未播完。

    var isLandAd = false//是否式横屏广告

    var isSdkInvokeOnShow = false //广告sdk 是否回调了曝光
    var hasDestroy = false //是否已经调用销毁
    var templateWidth: Int = 0
    var templateHeight: Int = 0

    //带激励任务的广告
    var adRewardDuration = 0
    var adRewardGold = 0
    var handedOutReward = false

    fun isRewardType(): Boolean {
        return adRewardGold > 0
    }

    // 广告加载成功后解析后续打点需要上报的参数，奇怪的是为什么点击曝光都会调用这个方法？可能有的信息只能在曝光后取到
    fun setAdValue(feedSky: FeedSky?) {
        this.mFeedSky = feedSky
        this.mDzFeedSky = feedSky?.resultList?.get(0)
        setBasicAdValue(feedSky)
        setAdValue()
        setHostInfo(feedSky?.uploadHostBean)
    }

    /**
     * 获取广告View
     */
    fun getTemplateView(
        context: Context,
        isLandMode: Boolean? = false,
        widthImgDp: Int? = null,
        heightImgDp: Int? = null,
        widthTemplatePx: Int? = null,
        heightTemplatePx: Int? = null,
    ): View? {
        //mTemplateView不为空就不用重复get，调用get会重复创建模板，影响点击
        if (mTemplateView == null) {
            val showParam = ShowParam().apply {
                isHorizontal = (isLandMode == true)
                if (widthImgDp!=null&& heightImgDp!=null) {
                    setSkySizeDp(widthImgDp,heightImgDp)
               }
                if (widthTemplatePx!=null&& heightTemplatePx!=null) {
                    setTemplateSizePx(widthTemplatePx,heightTemplatePx)
                }

            }
            mTemplateView = mFeedAdHolder?.getTemplateView(context, showParam)
        }
        return mTemplateView
    }

    fun setNightMode(isNightMode: Boolean) {
        mFeedLoader?.run {
            adLoaderParam.isNightMode = isNightMode
            notifyAd(isNightMode)
        }
    }

    fun showAdClickTips(tips: String?) {
        mFeedLoader?.showRewardTips(tips)
    }

    fun hideAdClickTips() {
        mFeedLoader?.hideRewardTips()
    }

    fun resume() {
        mDzFeedSky?.resume()
    }

    fun pause() {
        mDzFeedSky?.pause()
    }

    fun destroy() {
        hasDestroy = true
        mDzFeedSky?.destroy()
        mFeedAdHolder?.release()
    }

    fun close() {
        mDzFeedSky?.close()
    }

    private fun setAdValue() {
        mDzFeedSky?.run {
            <EMAIL> = sdkSource.strName
            if (mFeedSky != null && mFeedSky!!.isMaterialFromCache) {
                <EMAIL> = <EMAIL> + CACHE
            }
            adBrandName = brandName
            adBtnText = btnStr
            adTitle = title
            adDesc = description
            adIconUrl = iconUrl
            adImageUrl =
                if (imageUrlList != null && !imageUrlList.isEmpty()) imageUrlList[0] else ""
            adInteractionType =
                if (interactionType != null) interactionType.ordinal else 0

            adStyleType = if (skyStyle != null) skyStyle.value else 0
            codeId = slotId
            subSlotId = strategyInfo.subSlotId
            <EMAIL> = strategyInfo.slotId
            adEdition = unionSdkVersion
            stylesType = "unknown"
            if (videoInfo != null) {
                adVideoUrl = videoInfo.videoUrl
                adVideoDuration = videoInfo.videoDuration
            }

            adSource = strategyInfo.chn_type
            cLayered = strategyInfo.layer.toString()
            order = strategyInfo.currentIdIndex.toString()
            adLoaderOrder = "$adSource-$cLayered-$order"
        }
    }

    override fun getEcpm(): Double {
        return mDzFeedSky?.strategyInfo?.ecpmYuanStr?.toDouble() ?: 0.0
    }

    fun isValid(context: Context?): Boolean {
        if (mFeedAdHolder?.dzFeedSkyList?.isNotEmpty() == true) {
            return mFeedAdHolder?.dzFeedSkyList?.get(0)?.isValid(context) == true
        }
        return false
    }

    /**
     * 检查广告是否有效
     * @param bookId 剧id，用于判断是否可以在当前剧下播放
     * */
    fun isValid(context: Context?, bookId: String?): Boolean {
        if (mFeedAdHolder?.dzFeedSkyList?.isNotEmpty() == true) {
            return mFeedAdHolder?.dzFeedSkyList?.get(0)?.isValid(context, bookId) == true
        }
        return false
    }

    //获取广告过期原因
    fun getInvalidReason(): String {
        if (mFeedAdHolder?.dzFeedSkyList?.isNotEmpty() == true) {
            return mFeedAdHolder?.dzFeedSkyList?.get(0)?.invalidReason ?: "广告过期"
        }
        return "广告过期"
    }
    //当前广告是否是视频
    fun isVideo(): Boolean {
        return mDzFeedSky?.isVideo ?: false
    }

    fun getAdTag(): String {
        return javaClass.name + "@" + Integer.toHexString(hashCode())
    }

    fun isDrawFullScreen(): Boolean {
        val drawFullScreenStyle = if (mDzFeedSky?.isApi == true) {
            mFeedSky?.extraMap?.get(ExtraMap.KEY.IS_DRAW_FULL_SCREEN)
        } else {
            mFeedSky?.strategyInfo?.isDrawFullScreen
        }
        return drawFullScreenStyle == "1"
    }

    fun getAdRenderStyle(): SkyStyle? {
        return mDzFeedSky?.skyStyle
    }

    fun haveRewardTask():Boolean{
        return adRewardGold > 0
    }
}

data class SwipeReqParam(
    var reqSeq: Int = 0,        // 请求的刷次
    var insertType: Int = 0,    // 插入类型：1=否，2=普，3=强
    var adId: String = "",      // 广告id
) {
    override fun toString(): String {
        return "ReqParam(reqSeq=$reqSeq, insertType=$insertType)"
    }
}

// extension method
fun FeedAd?.getShowStyles(): String? = this?.mFeedSky?.extraMap?.get(ExtraMap.KEY.SHOW_STYLES) as? String
fun FeedAd?.isCsjLiveAd(): Boolean = "1" == this?.mFeedSky?.extraMap?.get(ExtraMap.KEY.IS_CSJ_LIVE_AD)
fun FeedAd?.getAdClickType(): String = this?.mFeedSky?.strategyInfo?.adClickType ?: ""
fun FeedAd?.getSlotId(): String = this?.mFeedSky?.strategyInfo?.slotId ?: ""
fun FeedAd?.getAgentId(): String = this?.mFeedSky?.strategyInfo?.agent_id ?: ""
fun FeedAd?.getSubSlotId(): String = this?.mFeedSky?.strategyInfo?.subSlotId ?: ""
fun FeedAd?.getCacheExpireLeftTime(): Long = this?.mFeedSky?.getCacheExpireLeftTime() ?: 0
fun FeedAd?.getDrawStartIndex(): Int = this?.mFeedSky?.adInfo?.getDrawStartIndex() ?: 0
fun FeedAd?.getDrawEndIndex(): Int = this?.mFeedSky?.adInfo?.getDrawEndIndex() ?: 0
fun FeedAd?.getDrawInsertType(): Int = this?.mFeedSky?.adInfo?.getDrawInsertType() ?: 0
fun FeedAd?.getDrawCacheNum(): Int =
    if (DrawAdKV.detailDrawMultiCacheSwitch == 0) 1 else this?.mFeedSky?.adInfo?.getDrawCacheNum() ?: 1
