package com.dz.platform.ad.sky

import android.content.Context
import com.dianzhong.base.Sky.RewardSky
import com.dianzhong.base.data.bean.sky.ExtraMap
import com.dianzhong.core.manager.loader.RewardVideoLoader

/**
 * Created by FeiHK
 * Date: on 2023/2/2.
 * Desc:
 */
class RewardAd : Ad(), Comparable<RewardAd>  {
    var rewardSky: RewardSky? = null
        set(value) {
            field = value
            setBasicAdValue(value)
            setAdValue()
            setHostInfo(value?.uploadHostBean)
        }
    var rewardLoader: RewardVideoLoader? = null
    var timeSpent: Long = 0//耗时
    var loadedTime = 0L
    var isShow = false

    /**
     * 是否参与了竞价
     */
    var hasBidding = false

    private fun setAdValue() {
        rewardSky?.run {
            chnType = realAdSourceName
            if (isMaterialFromCache) {
                chnType += CACHE
            }
            adEdition = skyApi.sdkVersion
            codeId = slotId
            <EMAIL> = strategyInfo.slotId
//            <EMAIL> = getEcpm()
            adVideoDuration = videoTime
            adSource = strategyInfo.chn_type
            cLayered = strategyInfo.layer.toString()
            order = strategyInfo.currentIdIndex.toString()
            adLoaderOrder = "$adSource-$cLayered-$order"
        }
    }

    override fun getEcpm(): Double {
        return try {
            rewardSky?.strategyInfo?.ecpmYuanStr?.toDouble() ?: 0.0
        } catch (e: Exception) {
            0.0
        }
    }

    fun isValid(context: Context): Boolean {
        return rewardSky?.isValid(context) == true
    }

    fun isValid(context: Context, bookId: String): Boolean {
        return rewardSky?.isValid(context, bookId) == true
    }

    fun expiresTime(): Long {
        return rewardSky?.let {
            it.putTime + it.strategyInfo.cache_alive_ms
        } ?: 0L
    }

    override fun compareTo(other: RewardAd): Int {
        return compareValuesBy(this, other, { -it.getEcpm() }, { it.expiresTime() })
    }

    fun clickOrder(): Int? {
        return rewardSky?.extraMap?.get(ExtraMap.KEY.REWARD_CLICK_ORDER) as? Int
    }

    fun rewardRule(): String? {
        return rewardSky?.extraMap?.get(ExtraMap.KEY.REWARD_CLICK_RULE) as? String
    }
}