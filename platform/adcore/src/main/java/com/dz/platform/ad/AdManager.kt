package com.dz.platform.ad

import android.app.Activity
import android.app.Application
import android.content.Context
import android.view.ViewGroup
import android.widget.ImageView
import com.dianzhong.HostConfigGetter
import com.dianzhong.HostInfoGetter
import com.dianzhong.base.Sky.FeedSky
import com.dianzhong.base.Sky.InterstitialSky
import com.dianzhong.base.Sky.RewardSky
import com.dianzhong.base.Sky.Sky
import com.dianzhong.base.Sky.SplashSky
import com.dianzhong.base.data.bean.UserInfo
import com.dianzhong.base.data.bean.sky.DZFeedSky
import com.dianzhong.base.data.bean.sky.FeedAdHolder
import com.dianzhong.base.data.bean.sky.RewardCloseParams
import com.dianzhong.base.data.constant.DrawInsertType
import com.dianzhong.base.data.constant.WelfareAnchorAdType
import com.dianzhong.base.data.loadparam.BusContextKey
import com.dianzhong.base.data.loadparam.DrawOrFeed
import com.dianzhong.base.data.loadparam.FeedSkyLoadParam
import com.dianzhong.base.data.loadparam.FeedWallLoadParam
import com.dianzhong.base.data.loadparam.InterstitialSkyLoadParam
import com.dianzhong.base.data.loadparam.LoaderParam
import com.dianzhong.base.data.loadparam.RewardSkyLoadParam
import com.dianzhong.base.data.loadparam.SplashSkyLoadParam
import com.dianzhong.base.data.network.UrlConfig
import com.dianzhong.base.listener.sky.BaseSkyListener
import com.dianzhong.base.listener.sky.DzFeedInteractionListener
import com.dianzhong.base.listener.sky.FeedSkyListener
import com.dianzhong.base.listener.sky.InterstitialActionListener
import com.dianzhong.base.listener.sky.InterstitialSkyLoadListener
import com.dianzhong.base.listener.sky.RewardActionListener
import com.dianzhong.base.listener.sky.RewardSkyLoadListener
import com.dianzhong.base.listener.sky.SplashSkyListener
import com.dianzhong.base.util.HostToast
import com.dianzhong.base.util.SensorLog
import com.dianzhong.base.util.SentryLogger
import com.dianzhong.base.util.setHostToast
import com.dianzhong.common.util.DzLog
import com.dianzhong.core.manager.SkyManager
import com.dianzhong.core.manager.loader.InterstitialLoader
import com.dianzhong.core.manager.loader.RewardVideoLoader
import com.dianzhong.core.manager.loader.SkyLoader.MaterialsLoadLS
import com.dianzhong.feedwall.FeedWallAd
import com.dianzhong.feedwall.FeedWallLoadListener
import com.dianzhong.feedwall.FeedWallManager
import com.dianzhong.feedwall.FeedWallShowListener
import com.dianzhong.wall.data.param.WallLoadParam
import com.dianzhong.wall.manager.WallManager
import com.dianzhong.wall.manager.listener.PreloadWallListener
import com.dianzhong.wall.manager.listener.WallSkyListener
import com.dianzhong.wall.manager.listener.wall.WallAd
import com.dz.foundation.base.data.kv.GlobalKV
import com.dz.foundation.base.utils.LocalActivityMgr.getTopActivity
import com.dz.foundation.base.utils.LogUtil
import com.dz.foundation.base.utils.ScreenUtil
import com.dz.platform.ad.callback.BaseAdLoadCallback
import com.dz.platform.ad.callback.BaseAdShowCallback
import com.dz.platform.ad.callback.FeedAdCallback
import com.dz.platform.ad.callback.RewardAdShowCallback
import com.dz.platform.ad.callback.RewardWallAdShowCallback
import com.dz.platform.ad.callback.SplashAdCallback
import com.dz.platform.ad.constant.AdEvent
import com.dz.platform.ad.data.AdKV
import com.dz.platform.ad.data.AdUserInfoBean
import com.dz.platform.ad.data.FeedAdKV
import com.dz.platform.ad.sky.FeedAd
import com.dz.platform.ad.sky.InterstitialAd
import com.dz.platform.ad.sky.RewardAd
import com.dz.platform.ad.sky.RewardWallAd
import com.dz.platform.ad.sky.RewardWallItem
import com.dz.platform.ad.sky.SkyAd
import com.dz.platform.ad.sky.SplashAd
import com.dz.platform.ad.vo.SplashAdVo
import me.jessyan.autosize.AutoSizeConfig
import org.json.JSONObject

/**
 * Created by FeiHK
 * Date: on 2023/2/2.
 * Desc:
 */
object AdManager {

    interface Callback {
        fun onStartInit(source: String)
        fun onEndInit()
        fun onStartLoadSplash()
        fun needColdSplashAd(): Boolean
    }

    /**
     * 广告的一些全局回调
     */
    interface GlobalCallback {
        fun onAdClick()
    }

    const val TAG = "AdManager"

    private const val AppKey: String = "102a5342v"
    const val POSITION_ID_SPLASH: String = "5001313000"
    const val POSITION_ID_REWARD: String = "5001313101"
    const val POSITION_ID_DRAW: String = "5001314601"

    @Volatile
    private var hasAdSdkStart = false
    private var callback: Callback? = null

    fun initAdk(
        context: Application,
        userInfoBean: AdUserInfoBean,
        uploadLog: SensorLog,
        sentryLog: SentryLogger,
        hostToast: HostToast,
        infoGetter: HostInfoGetter,
        configGetter: HostConfigGetter,
        callback: Callback?,
    ) {
        try {
            this.callback = callback
            SkyManager.getInstance().apply {
                // 必传，第一时间初始化
                application = context
                appKey = AppKey
                // 必传，第一时间初始化
                density = AutoSizeConfig.getInstance().initDensity
                // 必传，第一时间初始化
                testMode = LogUtil.isDebugMode()
                // 必传，但是不那么着急，在开屏广告请求之前ready就行
                setUploadLog(uploadLog)
                // 必传，但是不那么着急，在开屏广告请求之前ready就行
                setSentryLogger(sentryLog)
                // 必传，但是不那么着急，在开屏广告请求之前ready就行
                setHostToast(hostToast)
                // 必传，用于获取ua
                setHostInfoGetter(infoGetter)
                setHostConfigGetter(configGetter)
                // 必传，第一时间初始化
                init()
            }
            // 必传参数，尽早传，最迟在开屏广告请求之前传
            loginUserInfo(userInfoBean)
//            //影响华为广告的获取，在用户同意用户协议之后且必须在init之后
//            SkyManager.getInstance().isAgreeUserProtocol = true
            val needLoadSplashAd = callback?.needColdSplashAd() == true
            DzLog.d("SkyLoader", "initAdk() 是否需要冷开屏广告：$needLoadSplashAd GlobalKV.adLazyInitNew:${GlobalKV.adLazyInitNew}" )
            if (GlobalKV.adLazyInitNew != 1 && needLoadSplashAd) {
                checkAdSdkStart("init")
            }
        } catch (e: Throwable) {
            DzLog.e("SkyLoader", e)
            LogUtil.printStackTrace(e)
        }
    }

    fun checkAdSdkStart(source: String) {
        LogUtil.d(TAG, "SkyLoader 检查是否初始化SDK。来源:$source, hasAdSdkStart:$hasAdSdkStart")
        if (hasAdSdkStart) {
            return
        }
        LogUtil.d(TAG, "初始化广告SDK")
        callback?.onStartInit(source)
        SkyManager.getInstance().start()
        callback?.onEndInit()
        hasAdSdkStart = true
    }

    /**
     * TODO@GQ 这个接口可以获取吗，为什么需要
     * 设置imei
     * @param imei
     */
    fun setImei(imei: String) {
        if (!imei.isNullOrEmpty()) {
            LogUtil.d("ImeiTag", "设置imei给广告中台，imei==$imei")
            SkyManager.getInstance().ab(imei)
        }
    }

    fun getContext(application: Application, imageView: ImageView?): Context {
        val context = imageView?.context
        if (context != null) {
            return context
        }
        val topActivity = getTopActivity()
        if (topActivity != null) {
            return topActivity
        }
        return application
    }

    fun setEnvironment(envType: Int) {
        try {
//            //"[\"DEV\",\"TEST_1\",\"TEST_2\",\"TEST_3\",\"SAND_BOX\",\"PRE_RELEASE\",\"HOST\"]"
//            val skyManagerClass = Class.forName("com.dianzhong.core.manager.SkyManager")
//            val getInstance: Method = skyManagerClass.getDeclaredMethod("getInstance")
//            val skyManagerInstance: Any = getInstance.invoke(null, null)
//            val setEnvironment: Method = skyManagerClass.getDeclaredMethod(
//                "setEnvironment",
//                String::class.java,
//                String::class.java
//            )
//            setEnvironment.setAccessible(true)
//            setEnvironment.invoke(skyManagerInstance, "MLEgSZu0fvqiC#MH", "TEST_2")
//            UrlConfig.setEnv("MLEgSZu0fvqiC#MH", "TEST_2")
//            UrlConfig.setEnv("MLEgSZu0fvqiC#MH", "PRE_RELEASE")
//            UrlConfig.setEnv("MLEgSZu0fvqiC#MH", "HOST")
            when (envType) {
                1 -> UrlConfig.setEnv("MLEgSZu0fvqiC#MH", "HOST")  // 线上
                2 -> UrlConfig.setEnv("MLEgSZu0fvqiC#MH", "PRE_RELEASE")  // 预发
                else -> UrlConfig.setEnv("MLEgSZu0fvqiC#MH", "TEST_2")  // 测试
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }

    }

    fun loginUserInfo(userInfoBean: AdUserInfoBean) {
        try {
            SkyManager.getInstance().oaId = userInfoBean.oaid
            setChannelCode(userInfoBean.channel)
            val userInfo = UserInfo()
            userInfo.nickname = userInfoBean.nickname
            userInfo.city = userInfoBean.city
            userInfo.gender = userInfoBean.gender
            userInfo.user_id = userInfoBean.user_id
            userInfo.installTime = userInfoBean.installTime
            userInfo.nuts = userInfoBean.regTime
            userInfo.ruts = userInfoBean.chTime
            SkyManager.getInstance().userInfo = userInfo
        } catch (e: Exception) {
            LogUtil.printStackTrace(e)
        }
    }

    fun setChannelCode(channelCode: String?) {
        channelCode?.let {
            SkyManager.getInstance().setChannelCode(it)
        }
    }

    fun getAdSDKVersion(): String? {
        return SkyManager.getInstance().sdkVersion
    }

    /**
     * adPosId: 开屏广告位id
     * isHotSplash: 是否是热开屏
     * needAddLimitCnt: 是否需要增加限制次数
     */
    fun needSplashAd(adPosId: String, isHotSplash: Boolean, needAddLimitCnt: Boolean): Boolean {
        return !SkyManager.getInstance().limitSplashAdShow(adPosId, isHotSplash).also { result ->
            if (result && needAddLimitCnt) SkyManager.getInstance().recordSplashAdLimitCnt(adPosId, isHotSplash)
        }
    }

    private var globalCallback: GlobalCallback? = null

    fun setGlobalCallback(callback: GlobalCallback) {
        LogUtil.d(TAG, "setGlobalCallback")
        globalCallback = callback
    }

    /**
     * 获取开屏广告
     */
    fun loadSplashAd(
        splashAdVo: SplashAdVo,
        physicalPosId: Int,
        activity: Activity,
        adContainer: ViewGroup,
        width: Int,
        height: Int,
        adId: String,
        bookId: String?,
        chapterId:String?,
        timerStamp: Long,  // 计时器开始的时间戳
        callback: SplashAdCallback,
        autoShow: Boolean = true,
    ) {
        checkAdSdkStart("loadSplashAd")
        try {
            val userPushType = splashAdVo.userPushType

            LogUtil.d("splash_ad_tag", "AdManager loadSplashAd userPushType：$userPushType")
            val splashLoader = SkyManager.getInstance().obtainSplashLoader()
            splashLoader.apply {

                adLoaderParam = SplashSkyLoadParam()
                    .context(activity)
                    .skySize(width, height)
                    .adPositionId(adId)
                    .physicalPosId(physicalPosId)
                    .startTimer(timerStamp)
                    .setSplashUserType(userPushType)
                    .setAdCount(1)
                    .busContext(JSONObject().apply {
                        put(BusContextKey.bcids, splashAdVo.blockConfigId)
                    })
                bookId?.let {
                    adLoaderParam.book_id = it
                }
                chapterId?.let {
                    adLoaderParam.chapter_id = it
                }

                SlotIdData.markIsFirstLoad(adId)

                adLoadListener = object : SplashSkyListener {

                    var splashAd: SplashAd = SplashAd().apply {
                        adIsFirstLoad = SlotIdData.isFirstLoad(adId)
                    }

                    override fun onFail(splashSky: SplashSky?, message: String?, code: String?) {
                        splashAd.splashSky = splashSky
                        callback.onADFail(splashAd, code, message)
                    }

                    override fun onStartLoad(splashSky: SplashSky?) {
                        splashAd.splashSky = splashSky
                        splashAd.splashLoader = splashLoader
                        callback.onStartLoad(splashAd)
                    }

                    override fun onLoaded(splashSky: SplashSky?) {
                        splashAd.splashSky = splashSky
                        callback.onADLoad(splashAd)
                        if (autoShow) {
                            adContainer.removeAllViews()
                            splashSky?.show(adContainer)
                        }
                    }

                    override fun onClick(splashSky: SplashSky?) {
                        LogUtil.d(TAG, "splash click")
                        globalCallback?.onAdClick()
                        splashAd.splashSky = splashSky
                        callback.onADClick(splashAd)
                    }

                    override fun onClose(splashSky: SplashSky?) {
                        splashAd.splashSky = splashSky
                        callback.onADClose(splashAd)
                    }

                    override fun onShow(splashSky: SplashSky?) {
                        splashAd.apply {
                            adIsFirstShow = SlotIdData.markIsFirstShow(splashSky)
                            this.splashSky = splashSky
                        }
                        callback.onADShow(splashAd)
                    }

                    override fun onSeriesStartLoad() {
                        callback.onSeriesStartLoad()
                    }

                    override fun onSeriesEndLoad() {
                        callback.onSeriesEndLoad()
                    }

                    override fun onDownloadStart() {}
                    override fun onDownloadFinish(s: String?) {}
                    override fun onInstallStart() {}
                    override fun onInstalled() {}
                    override fun onInstallFail() {}
                }

                materialsLoadLS = object : MaterialsLoadLS {

                    var skyAd: SkyAd = SkyAd()

                    override fun loadStart(
                        sky: Sky<out BaseSkyListener<*>, out LoaderParam<*, *>>?,
                        totalAdNum: Int,
                        totalAdDeck: Int,
                    ) {
                        skyAd.sky = sky
                        skyAd.setTotalAdNum(totalAdNum)
                        skyAd.setTotalAdDeck(totalAdDeck)
                        callback.loadStart(skyAd)
                    }

                    override fun loadStatus(
                        loadStatus: Boolean,
                        sky: Sky<out BaseSkyListener<*>, out LoaderParam<*, *>>?,
                    ) {
                        skyAd.sky = sky
                        callback.loadStatus(skyAd)
                    }
                }
                <EMAIL>?.onStartLoadSplash()

                load()
            }
        } catch (e: Exception) {
            LogUtil.printStackTrace(e)
        }

    }


    fun preloadSplashSeries(splashAdVo: SplashAdVo) {
        val adId = splashAdVo.getAdId()
        LogUtil.d(TAG, "splash_ad_tag preloadSplashSeries adId=$adId")
        val adLoaderParam = SplashSkyLoadParam()
            .adPositionId(adId)
            .busContext(JSONObject().apply {
                put(BusContextKey.bcids, splashAdVo.blockConfigId)
            })
        SkyManager.getInstance().preloadSeries(adLoaderParam)
    }

    /**
     * 获取激励视频广告
     */
    fun loadRewardAd(
        activity: Activity,
        adId: String,
        physicalPosId: Int?,
        blockConfigId: String?,
        bookId: String?,
        chapterId: String?,
        timeout: Long?,
        requestId: String?,
        callback: BaseAdLoadCallback<RewardAd>,
    ): RewardVideoLoader? {
        checkAdSdkStart("loadRewardAd")
        LogUtil.d(TAG, "loadRewardAd adId=$adId bookId=$bookId timeout=$timeout")
        var adLoader: RewardVideoLoader?
        try {
            adLoader = SkyManager.getInstance().obtainRewardVideoLoader().apply {

                adLoaderParam = RewardSkyLoadParam().apply {
                    adPositionId = adId
                    this.physicalPosId = physicalPosId ?: 0
                    context = activity
                    orientation = LoaderParam.Oritentation.VERTICAL
                    if (timeout != null && timeout > 0) {
                        bizTotalTimeOut = timeout
                    }
                    book_id = bookId
                    chapter_id = chapterId
//                    skySize(1080, 1920)
                    getTopActivity()?.let {
                        val bounds = ScreenUtil.getWindowBounds(it)
                        skySize(bounds.right - bounds.left, bounds.bottom - bounds.top)
                    } ?: skySize(ScreenUtil.getScreenWidth(), ScreenUtil.getScreenHeight())
                    busContext(JSONObject().apply {
                        put(BusContextKey.bcids, blockConfigId)
                    })
                }

                SlotIdData.markIsFirstLoad(adId)

                adLoadListener = object : RewardSkyLoadListener {
                    var rewardAd: RewardAd = RewardAd().apply {
                        mRequestId = requestId
                        adIsFirstLoad = SlotIdData.isFirstLoad(adId)
                        loadActivityStr = activity.toString()
                    }

                    override fun onFail(rewardSky: RewardSky?, msg: String?, code: String?) {
                        rewardAd.rewardSky = rewardSky
                        LogUtil.e(TAG, "onFail 获取广告失败，code: $code, msg: $msg")
                        callback.onLoadError(code?.toIntOrNull() ?: -1, msg ?: "")
                    }

                    override fun onStartLoad(rewardSky: RewardSky?) {
                        LogUtil.d(TAG, "onStartLoad 广告位--$adId")
//                        rewardAd.rewardSky = rewardSky
                        callback.onStartLoad()
                    }

                    override fun onLoaded(rewardSky: RewardSky?) {
                        LogUtil.d(TAG, "onLoaded 广告位--$adId 代码位--${rewardSky?.slotId}")
                        rewardAd.rewardSky = rewardSky
                        rewardAd.rewardLoader = this@apply
                        callback.onLoadSuccess(rewardAd)
                    }
                }

                materialsLoadLS = object : MaterialsLoadLS {

                    var skyAd: SkyAd = SkyAd()
                    override fun loadStart(
                        sky: Sky<out BaseSkyListener<*>, out LoaderParam<*, *>>?,
                        totalAdNum: Int,
                        totalAdDeck: Int,
                    ) {
                        skyAd.sky = sky
                        skyAd.setTotalAdNum(totalAdNum)
                        skyAd.setTotalAdDeck(totalAdDeck)
                        callback.onMaterialStartLoad(skyAd)
                    }

                    override fun loadStatus(
                        loadStatus: Boolean,
                        sky: Sky<out BaseSkyListener<*>, out LoaderParam<*, *>>?,
                    ) {
                        skyAd.sky = sky
                        callback.onMaterialStatusChanged(skyAd)
                    }
                }

                load()
            }
        } catch (e: Exception) {
            LogUtil.d(TAG, "loadRewardAd Exception：${e.message}")
            LogUtil.printStackTrace(e)
            adLoader = null
            callback.onLoadError(-1, e.message ?: "加载出现异常")
        }
        return adLoader
    }

    /**
     * 显示激励视频
     *
     * @param ad
     * @param callback
     */
    fun showRewardAd(ad: RewardAd, callback: RewardAdShowCallback) {
        ad.rewardSky?.rewardActionListener = object : RewardActionListener {

            override fun onFail(rewardSky: RewardSky?, p1: String?, p2: String?) {
                LogUtil.e(TAG, "激励视频 onFail 获取广告失败，code: $p2, msg: $p1")
                callback.onShowError(ad, -1, p1 + p2)
            }

            override fun onVideoBarClick(rewardSky: RewardSky?) {
                LogUtil.d(TAG, "激励视频 onClick 代码位--${rewardSky?.slotId}")
                globalCallback?.onAdClick()
                callback.onClick(ad)
            }

            override fun onVideoStart(p0: RewardSky?) {
                LogUtil.d(TAG, "激励视频 onVideoStart 代码位--${p0?.slotId}")
                callback.onVideoStart()
            }

            override fun onVideoComplete(p0: RewardSky?) {
                LogUtil.d(TAG, "激励视频 onVideoComplete 代码位--${p0?.slotId}")
                callback.onVideoComplete()
            }

            override fun onReward(p0: RewardSky?) {
                LogUtil.d(TAG, "激励视频 onReward 代码位--${p0?.slotId}")
                callback.onReward(ad)
            }

            override fun onNoReward(p0: RewardSky?) {}

            override fun onVideoError(p0: RewardSky?) {
                LogUtil.e(TAG, "激励视频 onVideoError")
                callback.onShowError(ad, -1, "video error")
            }

            override fun downloadProgress(p0: Float) {
//                LogUtil.d(TAG, "激励视频 downloadProgress:${p0}")
            }

            override fun onShow(rewardSky: RewardSky?) {
                LogUtil.d(TAG, "激励视频 onShow 代码位--${rewardSky?.slotId}")
                ad.apply {
                    adIsFirstShow = SlotIdData.markIsFirstShow(rewardSky)
                    setHostInfo(rewardSky?.uploadHostBean)
                }
                callback.onShow(ad)
            }

            override fun onClose(rewardSky: RewardSky?, rewardCloseParams: RewardCloseParams?) {
                LogUtil.d(TAG, "激励视频 onClose 代码位--${rewardSky?.slotId}")
                callback.onClose(ad, rewardCloseParams)
            }

            override fun onDownloadStart() {
            }

            override fun onDownloadFinish(p0: String?) {
            }

            override fun onInstallStart() {
            }

            override fun onInstalled() {
            }

            override fun onInstallFail() {
            }

            override fun onSkip(p0: RewardSky?) {
            }
        }
        val activity = getTopActivity()
        ad.apply {
            showActivityStr = activity.toString()
            rewardSky?.loaderParam?.context = activity
            rewardSky?.let {
                it.show(activity)
                ad.isShow = true
            } ?: let {
                callback.onShowError(ad, -1, "sky 广告为空")
            }
        }
    }

    /**
     * 加载插屏广告
     * 注意：广告SDK在这里的回调与激励视频和激励墙不一致
     */
    fun loadInterstitialAd(
        activity: Activity,
        adId: String,
        requestId: String?,
        blockConfigId: String?,
        bookId: String?,
        chapterId:String?,
        width: Int? = 600,
        height: Int? = 600,
        timeout: Long? = 0L,
        bgTipText: String? = "",
        isNeedInterstitialBg: Boolean? = false,
        loadCallback: BaseAdLoadCallback<InterstitialAd>,
    ): InterstitialLoader {
        checkAdSdkStart("loadInterstitialAd")
        val loader: InterstitialLoader = SkyManager.getInstance().obtainInterstitialLoader()
        try {
            loader.apply {
                adLoaderParam = InterstitialSkyLoadParam().apply {
                    adPositionId = adId
                    context = activity
                    book_id = bookId
                    chapter_id = chapterId
                    this.isNeedInterstitialBg = isNeedInterstitialBg ?: false
                    this.bgTipText = bgTipText
                    if (timeout != null && timeout > 0) {
                        bizTotalTimeOut = timeout
                    }
                    skySize(width ?: 600, height ?: 600)
                    busContext(JSONObject().apply {
                        put(BusContextKey.bcids, blockConfigId)
                    })
                }

                SlotIdData.markIsFirstLoad(adId)

                adLoadListener = object : InterstitialSkyLoadListener {
                    val interstitialAd = InterstitialAd().apply {
                        mRequestId = requestId
                        adIsFirstLoad = SlotIdData.isFirstLoad(adId)
                        loadActivityStr = activity.toString()
                    }

                    override fun onFail(
                        interstitialSky: InterstitialSky?,
                        p1: String?,
                        p2: String?,
                    ) {
                        loadCallback.onLoadError(-1, p1 + p2)
                    }

                    override fun onStartLoad(interstitialSky: InterstitialSky?) {
                        loadCallback.onStartLoad()
                    }

                    override fun onLoaded(interstitialSky: InterstitialSky?) {
                        interstitialAd.adSky = interstitialSky
                        loadCallback.onLoadSuccess(interstitialAd)
                    }
                }

                materialsLoadLS = object : MaterialsLoadLS {

                    var skyAd: SkyAd = SkyAd()

                    override fun loadStart(
                        sky: Sky<out BaseSkyListener<*>, out LoaderParam<*, *>>?,
                        totalAdNum: Int,
                        totalAdDeck: Int,
                    ) {
                        skyAd.sky = sky
                        skyAd.setTotalAdNum(totalAdNum)
                        skyAd.setTotalAdDeck(totalAdDeck)
                        loadCallback.onMaterialStartLoad(skyAd)
                    }

                    override fun loadStatus(
                        loadStatus: Boolean,
                        sky: Sky<out BaseSkyListener<*>, out LoaderParam<*, *>>?,
                    ) {
                        skyAd.sky = sky
                        loadCallback.onMaterialStatusChanged(skyAd)
                    }

                }

                load()
            }
        } catch (e: Exception) {
            LogUtil.d(TAG, "loadInterstitialAd Exception：${e.message}")
            LogUtil.printStackTrace(e)
            loadCallback.onLoadError(-1, e.message ?: "加载出现异常")
        }
        return loader
    }

    /**
     * 展示插屏广告
     *
     * @param ad
     * @param callback
     */
    fun showInterstitialAd(interstitialAd: InterstitialAd, showCallback: BaseAdShowCallback<InterstitialAd>) {
        val activity = getTopActivity()
        interstitialAd.adSky?.loaderParam?.context = activity
        interstitialAd.adSky?.interstitialActionListener = object : InterstitialActionListener {

            override fun onShow(interstitialSky: InterstitialSky?) {
                interstitialAd.adIsFirstShow = SlotIdData.markIsFirstShow(interstitialSky)
                showCallback.onShow(interstitialAd)
            }

            override fun onClick(interstitialSky: InterstitialSky?) {
                LogUtil.d(TAG, "interstitial click")
                globalCallback?.onAdClick()
                showCallback.onClick(interstitialAd)
            }

            override fun onClose(interstitialSky: InterstitialSky?, rewardCloseParams: RewardCloseParams?) {
                showCallback.onClose(interstitialAd, rewardCloseParams)
            }

            override fun onReward(interstitialSky: InterstitialSky) {
                showCallback.onReward(interstitialAd)
            }

            override fun onFail(sky: InterstitialSky?, message: String?, errorCode: String?) {
            }

            override fun onNoReward(interstitialSky: InterstitialSky) {
            }

            override fun onVideoComplete(rewardSky: InterstitialSky?) {
            }

            override fun onVideoStart(rewardSky: InterstitialSky?) {
            }

        }
        interstitialAd.showActivityStr = activity.toString()
        interstitialAd.adSky?.show(activity)
            ?: let {
                showCallback.onShowError(interstitialAd, -1, "sky 广告为空")
            }
    }

    /**
     * 获取信息流广告
     *
     * @param activity
     * @param adContainer
     * @param widthImgDp      第三方SDK广告宽度，目前只有穿山甲沉浸式广告使用了（必须保证dp单位是未被autosize调整过的值）
     * @param heightImgDp     第三方SDK广告高度，目前只有穿山甲沉浸式广告使用了（必须保证dp单位是未被autosize调整过的值）
     * @param widthTemplatePx  商业化SDK自渲染模板广告宽度，单位是px
     * @param heightTemplatePx 商业化SDK自渲染模板广告高度，单位是px
     * @param adId
     * @param backgroundColor
     * @param isNightMode
     * @param enableGroupAd
     * @param isDrawAd
     * @param bookId
     * @param chapterId
     * @param callback
     * @param requestId
     * @param videoMute
     */
    fun loadFeedAd(
        activity: Activity,
        widthImgDp: Int = 0,
        heightImgDp: Int = 0,
        widthTemplatePx: Int = 0,
        heightTemplatePx: Int = 0,
        feedAdDimen: FeedAdDimen? = null,
        adId: String,
        physicalPosId: Int?,
        blockConfigId: String?,
        adDrawReqSeq: Int = 0,
        adDrawForceTime: Int = -1,
        backgroundColor: Int,
        isNightMode: Boolean,//是否是夜间模式
        isDrawAd: Boolean,//是否加载的是沉浸式,
        bookId: String?,  // 剧的id
        chapterId:String?,        // 剧集id
        requestId: String? = null,
        videoMute: Boolean? = false,
        isWelfareAnchorAdType: Boolean? = null, // 是否是福利锚点广告类型
        welfareAnchorAdType: WelfareAnchorAdType? = null, // 福利锚点广告类型
        welfareAnchorAdCloseArea: Int? = null, // 信息流关闭按钮热区
        rewardDuration: Int? = 0,
        rewardGold: Int? = 0,
        minPDReqEcpmYuan: Double = -1.0,
        callback: FeedAdCallback,
    ) {
        checkAdSdkStart("loadFeedAd")

        val finalWidthImgDp = feedAdDimen?.widthDp ?: widthImgDp
        val finalHeightImgDp = feedAdDimen?.heightDp ?: heightImgDp
        val finalWidthTemplatePx = feedAdDimen?.widthPx ?: widthTemplatePx
        val finalHeightTemplatePx = feedAdDimen?.heightPx ?: heightTemplatePx

        try {
            SkyManager.getInstance().obtainFeedLoader().apply {

                adLoaderParam = FeedSkyLoadParam().apply {
                    context = activity
                    adPositionId = adId
                    this.physicalPosId = physicalPosId ?: 0
                    orientation = LoaderParam.Oritentation.HORIZONTAL
//                    container = adContainer // 不需要传，解决内存泄漏
                    resultType = LoaderParam.ResultType.TEMPLATE
                    setLoadType(if (isDrawAd) DrawOrFeed.DRAW else DrawOrFeed.FEED)
                    this.backgroundColor = backgroundColor
                    this.isNightMode = isNightMode
                    skySize(finalWidthImgDp, finalHeightImgDp) // dp
                    drawFullScreenSkySizeDp( // 二级draw全屏样式下的尺寸
                        feedAdDimen?.drawFullWidthDp ?: 0,
                        feedAdDimen?.drawFullHeightDp ?: 0
                    ) // dp
                    isVideoMute = (videoMute == true)
                    this.minPDReqEcpmYuan = minPDReqEcpmYuan
                    if (rewardDuration != null && rewardDuration > 0) {
                        this.rewardDuration = rewardDuration
                    }
                    if (rewardGold != null && rewardGold > 0) {
                        this.rewardGold = rewardGold
                    }
                    if (isWelfareAnchorAdType == true) {
                        this.isWelfareAnchorAdType = true
                        this.welfareAnchorAdType = welfareAnchorAdType ?: WelfareAnchorAdType.ALL
                        this.welfareAnchorAdCloseArea = welfareAnchorAdCloseArea?:1
                    }

                    templateSize(finalWidthTemplatePx, finalHeightTemplatePx) // px
                    book_id = bookId
                    chapter_id = chapterId
                    busContext(JSONObject().apply {
                        put(BusContextKey.ad_request_count, adDrawReqSeq)
                        put(
                            BusContextKey.ad_request_insert_type,
                            getDrawInsertType(adDrawForceTime).also {
                                DzLog.d(
                                    "SkyLoader",
                                    "adDrawForceTime:$adDrawForceTime reqInsertType:$it"
                                )
                            }
                        )
                        put(BusContextKey.bcids, blockConfigId)
                    })
                }

                SlotIdData.markIsFirstLoad(adId)

                adLoadListener = object : FeedSkyListener {
                    val feedAd: FeedAd = FeedAd().apply {
                        mRequestId = requestId
                        adIsFirstLoad = SlotIdData.isFirstLoad(adId)
                        mAdReqSeq = adDrawReqSeq
                        mAdDrawForceTime = adDrawForceTime
                    }

                    override fun onFail(feedSky: FeedSky?, p1: String?, p2: String?) {
                        callback.onFail(feedAd, p1, p2)
                    }

                    override fun onStartLoad(feedSky: FeedSky?) {
                        callback.onStartLoad(feedAd)
                    }

                    override fun onLoaded(feedSky: FeedSky?) {

                    }

                    override fun onFeedSkyLoaded(
                        feedAdHolder: FeedAdHolder?,
                        dzFeedSkyList: MutableList<DZFeedSky>?,
                    ) {
                        val dzFeedSkyLists: MutableList<FeedSky>? = feedAdHolder?.dzFeedSkyList
                        dzFeedSkyLists?.forEach {
                            setDzFeedInteractionListener(
                                isDrawAd,
                                feedAd,
                                it.resultList[0],
                                callback
                            )
                        }
                        feedAd.mFeedLoader = this@apply
                        feedAd.mFeedAdHolder = feedAdHolder
                        feedAd.setAdValue(feedAdHolder?.dzFeedSkyList?.get(0))
                        callback.onFeedSkyLoaded(feedAd)
                    }
                }

                materialsLoadLS = object : MaterialsLoadLS {

                    var skyAd: SkyAd = SkyAd().apply {
                        mRequestId = requestId
                    }

                    override fun loadStart(
                        sky: Sky<out BaseSkyListener<*>, out LoaderParam<*, *>>?,
                        totalAdNum: Int,
                        totalAdDeck: Int,
                    ) {
                        skyAd.sky = sky
                        skyAd.setTotalAdNum(totalAdNum)
                        skyAd.setTotalAdDeck(totalAdDeck)
                        callback.loadStart(skyAd)
                    }

                    override fun loadStatus(
                        loadStatus: Boolean,
                        sky: Sky<out BaseSkyListener<*>, out LoaderParam<*, *>>?,
                    ) {
                        skyAd.sky = sky
                        callback.loadStatus(skyAd)
                    }

                }

                load()
            }

        } catch (e: Exception) {
            LogUtil.printStackTrace(e)
        }
    }

    private fun setDzFeedInteractionListener(
        isDrawAd: Boolean, // 是否加载的是沉浸式
        feedAd: FeedAd,
        dzFeedSky: DZFeedSky,
        callback: FeedAdCallback,
    ) {
        dzFeedSky.setInteractionListener(object : DzFeedInteractionListener {
            override fun onFail(feedSky: FeedSky?, p1: String?, p2: String?) {

            }

            override fun onShow(feedSky: FeedSky?) {
                if (isDrawAd) {
                    AdKV.drawImpHistoryWatchedSec.takeIf { it > 0 }?.let {
                        AdKV.drawImpWatchedMillisGap =
                            ((AdKV.historyWatchedDurationSec - it) * 1000).toLong()
                    }
                    AdKV.drawImpHistoryWatchedSec = AdKV.historyWatchedDurationSec
                }
                feedAd.apply {
                    adIsFirstShow = SlotIdData.markIsFirstShow(feedSky)
                    setAdValue(feedSky)
                    isSdkInvokeOnShow = true
                }
                callback.onShow(feedAd)
            }

            override fun onClose(feedSky: FeedSky?) {
                feedAd.setAdValue(feedSky)
                callback.onClose(feedAd)
            }

            override fun onShowFail(feedSky: FeedSky?, p1: String?) {

            }

            override fun onClick(feedSky: FeedSky?) {
                LogUtil.d(TAG, "FeedInteraction click")
                globalCallback?.onAdClick()
                feedAd.setAdValue(feedSky)
                callback.onClick(feedAd)
            }

            override fun onDownloadStart() {

            }

            override fun onDownloadFinish(p0: String?) {

            }

            override fun downloadProgress(p0: Float) {

            }

            override fun onInstallStart() {

            }

            override fun onInstalled() {

            }

            override fun onInstallFail() {

            }

            override fun onVideoStart(p0: Long) {
                callback.onVideoStart(feedAd)
            }

            override fun onVideoProgress(p0: Long, p1: Long) {
            }

            override fun onVideoComplete() {
                callback.onVideoComplete(feedAd)
            }

            override fun onVideoSilence(p0: Boolean) {
            }

            override fun onVideoClick() {
            }

            override fun onVideoPlayStateChange(p0: DZFeedSky.PlaySate?) {
            }

            override fun onVideoError(p0: String?) {
            }

            override fun isTimingInVideoView(p0: Boolean) {
            }

            override fun onReward(feedSky: FeedSky?) {
                LogUtil.d(TAG, "FeedInteraction onReward")
                callback.onReward(feedAd)
            }
        })
    }

    fun loadRewardWallAd(
        activity: Activity,
        adId: String,
        uid: String,
        bookId: String?,
        updateNum: String?,
        callback: BaseAdLoadCallback<RewardWallAd>,
    ) {
        checkAdSdkStart("loadRewardWallAd")
        try {
            val wallLoadParam = WallLoadParam(activity, adId, uid, updateNum)
            WallManager.INSTANCE.preload(wallLoadParam, object : PreloadWallListener {

                override fun onWallPreloaded(wallAd: WallAd) {
                    val ad = RewardWallAd().apply { adSky = wallAd }
                    callback.onLoadSuccess(ad)
                }

                override fun onWallPreloadFail(errMsg: String, errCode: String) {
                    callback.onLoadError(-1, "$errCode $errMsg")
                }
            })
        } catch (e: Exception) {
            LogUtil.d(TAG, "loadRewardWallAd Exception：${e.message}")
            LogUtil.printStackTrace(e)
            callback.onLoadError(-1, e.message ?: "加载出现异常")
        }
    }

    /**
     * 展示激励墙
     *
     * @param context
     * @param ad
     * @param callback
     */
    fun showRewardWallAd(
        context: Context,
        ad: RewardWallAd,
        callback: RewardWallAdShowCallback,
    ) {
        WallManager.INSTANCE.setWallSkyListener(object : WallSkyListener {

            override fun onReward(wallAd: WallAd) {
                callback.onReward(ad)
            }

            override fun onWallClose(wallAd: WallAd) {
                callback.onClose(ad, null)
            }

            override fun onAdShow(wallAd: WallAd) {
                callback.onShow(ad)
            }

            override fun onClick(wallAd: WallAd, feedSky: FeedSky) {
                LogUtil.d(TAG, "Reward wall click")
                globalCallback?.onAdClick()
                callback.onItemClick(ad, RewardWallItem(feedSky))
            }

            override fun onItemAdShow(wallAd: WallAd, feedSky: FeedSky) {
                callback.onItemShow(ad, RewardWallItem(feedSky))
            }
        })
        val topActivity = getTopActivity() ?: return
        ad.adSky?.run {
            showRewardWall(topActivity)
            ad.isShow = true
        } ?: let {
            callback.onShowError(ad, -1, "sky 广告为空")
        }
    }


    /**
     * 获取 Cpa/Cpc 广告
     *
     * */
    fun loadFeedWallAd(
        activity: Activity,
        adId: String,
        blockConfigId: String?,
        coinsRate: Int? = 0,
        roi: Int? = 0,
        awardLimit: Int? = 0,
        feedWallLoadListener: FeedWallLoadListener,
    ) {
        checkAdSdkStart("loadFeedWallAd")

        try {
            val trafficReachSeq = FeedAdKV.handleTrafficSeq(adId).toString() // 流量请求序号
            LogUtil.d(TAG, "loadFeedWallAd trafficReachSeq=$trafficReachSeq")
            val feedWallParam = FeedWallLoadParam().apply {
                context = activity
                adPositionId = adId
                this.trafficReachSeq = trafficReachSeq
                busContext(JSONObject().apply {
                    put(BusContextKey.bcids, blockConfigId)
                    put(BusContextKey.coins_rate, coinsRate)
                    put(BusContextKey.roi, roi)
                    put(BusContextKey.award_limit, awardLimit)
                })
            }
            FeedWallManager.loadFeedWallAd(feedWallParam, feedWallLoadListener)

        } catch (e: Exception) {
            LogUtil.printStackTrace(e)
        }
    }

    /**
     * 展示 Cpa/Cpc 广告
     *
     * */
    fun showFeedWallAd(
        activity: Activity,
        wallFeedAd: FeedWallAd,
        feedWallShowListener: FeedWallShowListener,
    ) {
        LogUtil.d(TAG, "showFeedWallAd")
        try {
            wallFeedAd.showFeedWallAd(activity, feedWallShowListener)
        } catch (e: Exception) {
            LogUtil.printStackTrace(e)
        }
    }

    /**
     * 流量请求事件上报中台
     * */
    fun sendTrafficReachLog(
        spaceId: Int, adSlotId: String,
        blockConfigId: String?,
        useHttp: Boolean? = false
    ) {
        sendTrafficReachLog(spaceId, adSlotId, 0, 0, "", blockConfigId, useHttp)
    }

    /**
     * 流量请求事件上报中台
     * */
    fun sendTrafficReachLog(
        spaceId: Int, adSlotId: String, drawReqSeq: Int, drawReqType: Int,
        drawSubSlotId: String, blockConfigId: String?, useHttp: Boolean? = false
    ) {
        val map = mutableMapOf<String, Any>("adslot_id" to adSlotId)
        if (drawReqSeq > 0) {
            map["req_cnt"] = drawReqSeq // 刷次
        }
        if (drawReqType > 0) {
            map["instty"] = drawReqType // 插入类型：1-不区分，2-普插，3-强插
        }
        if (drawSubSlotId.isNotEmpty()) {
            map["sub_slot_id"] = drawSubSlotId
        }
        if (blockConfigId?.isNotEmpty() == true) {
            map["bcids"] = blockConfigId
        }
        if (useHttp == true) {
            uploadHttpLog(AdEvent.AD_TRAFFIC_REACH, map)
        } else {
            uploadSlsLog(AdEvent.AD_TRAFFIC_REACH, map)
        }

    }

    /**
     * 广告反馈事件上报中台
     * @param adId: 广告id，如果有，则传值，必须为String类型
     * @param aid: 当前广告的代码位策略ID
     * @param content: 用户选择的反馈内容或建议信息
     * */
    fun sendFeedbackLog(adId: String, aid: String, content: String) {
        LogUtil.d(TAG, "sendFeedbackLog adId=$adId aid=$aid content=$content")
        val aidInt = aid.toIntOrNull() ?: 0
        val map = mutableMapOf<String, Any>(
            "adid" to adId,
            "aid" to aidInt,
            "content" to content
        )
        uploadSlsLog(AdEvent.AD_FEEDBACK, map)
    }

    /**
     * 上传事件到中台
     * */
    private fun uploadSlsLog(eventName: String, eventParams: Map<String, Any>) {
        checkAdSdkStart("uploadSlsLog")
        LogUtil.d(TAG, "uploadSlsLog eventName=$eventName eventParams=$eventParams")
        SkyManager.getInstance().uploadSlsLog(eventName, eventParams)
        LogUtil.d(TAG, "uploadSlsLog --------------------------------------------")
    }

    /**
     * 上传事件到中台
     * */
    private fun uploadHttpLog(eventName: String, eventParams: Map<String, Any>) {
        checkAdSdkStart("uploadHttpLog")
        LogUtil.d(TAG, "uploadHttpLog eventName=$eventName eventParams=$eventParams")
        SkyManager.getInstance().uploadHttpLog(eventName, eventParams)
        LogUtil.d(TAG, "uploadHttpLog --------------------------------------------")
    }

    /**
     * 获取draw广告的强普查类型
     * @param adDrawForceTime 强制观看时长
     */
    fun getDrawInsertType(adDrawForceTime: Int) =
        when {
            adDrawForceTime == 0 -> DrawInsertType.NORMAL.value
            adDrawForceTime > 0 -> DrawInsertType.STRONG.value
            else -> DrawInsertType.NONE.value
        }
}

const val TAG = "AdManager"

data class FeedAdDimen(
    val widthPx: Int,
    val heightPx: Int,
    val widthDp: Int,
    val heightDp: Int,
    val drawFullWidthPx: Int,  // 二级页draw全屏模式下的宽度
    val drawFullHeightPx: Int, // 二级页draw全屏模式下的高度
    val drawFullWidthDp: Int,  // 二级页draw全屏模式下的宽度
    val drawFullHeightDp: Int, // 二级页draw全屏模式下的高度
)

object SlotIdData {
    private val firstLoadMap = mutableMapOf<String, Boolean>()
    private val firstShowMap = mutableMapOf<String, Boolean>()

    // 第一次调用设置true, 后续false
    fun markIsFirstLoad(slotId: String) {
        firstLoadMap[slotId] = firstLoadMap[slotId] == null
    }

    fun isFirstLoad(slotId: String) = firstLoadMap[slotId] ?: false

    /**
     * 第一次调用设置true, 后续false
     * @param sky 广告sky对象
     * @return 返回是否是第一次展示
     */
    fun markIsFirstShow(sky: Sky<*, *>?): Boolean {
        val slotId = sky?.loaderParam?.adPositionId ?: ""
        firstShowMap[slotId] = firstShowMap[slotId] == null
        return firstShowMap[slotId] ?: true
    }
}