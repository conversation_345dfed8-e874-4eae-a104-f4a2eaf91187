package com.dz.platform.ad

import com.dianzhong.base.util.AppUtil
import com.dianzhong.common.util.DzLog
import com.dianzhong.core.manager.SkyManager
import com.dz.foundation.base.utils.LogUtil
import com.dz.platform.ad.data.AdKV
import com.dz.platform.ad.data.DrawAdKV
import com.dz.platform.ad.vo.CoinsDropVo
import com.dz.platform.ad.vo.DrawTaskVo
import com.dz.platform.ad.vo.DropIntervalConfigVo

object CoinsDropIntervalUtil {

    var drawTaskVo: DrawTaskVo? = null      // 业务后端下发：金币掉落任务数据
        set(value) {
            field = value
            logD("drawTaskVo updated: $value")
        }
    var coinDropConfig: CoinsDropVo? = null // 商业化后台下发的配置
//    var drawTaskVo: DrawTaskVo? = com.dz.platform.ad.vo.drawTaskVoTest      // 测试用mock数据
//    var coinDropConfig: CoinsDropVo? = com.dz.platform.ad.vo.drawDropVoTest // 测试用mock数据

    var adExposedCnt: Int = 0 // 曝光的广告次数
        set(value) {
            field = value
            logD("第${value}次曝光")
        }
    private var selectedInterval: DropIntervalConfigVo? = null

    /** selectedInterval的dropFirstOrder和adExposedCnt匹配的时候出；
     * 以及后续按照selectedInterval的dropShowInterval(2,3,4)指定的间隔出
     * 如果selectedInterval的dropFirstOrder为-1，则表示不出金币掉落
     * 例子：dropFirstOrder为1，dropShowInterval为"2,3,4"，则表示adExposedCnt=1,4,8,13,16,20,25 的时后出金币掉落
     */
    fun needInsertCoinsRain(): Boolean {
        if (drawTaskVo == null) {
            fastLogD("drawTaskVo is null，不出金币掉落")
            return false
        }

        if ((drawTaskVo?.count ?: 0) <= 0) {
            fastLogD("drawTaskVo.count <= 0，不出金币掉落")
            return false
        }

        if ((drawTaskVo?.awardNum ?: 0) <= 0) {
            fastLogD("drawTaskVo.awardNum <= 0，不出金币掉落")
        }

        // 如果商业化没有配置金币掉落，则不需要插入
        if (coinDropConfig == null) {
            fastLogD("coinDropConfig is null，不出金币掉落")
            return false
        }

        if (coinDropConfig?.disable() == true) {
            fastLogD("dropWatchTime < 0 && dropClkConfig == 0，不出金币掉落")
            return false
        }

        // 如果当前章节的间隔配置为null，则不需要插入
        if (selectedInterval == null) {
            fastLogD("当前章节没有匹配到间隔配置，不出")
            return false
        }

        val dropFirstOrder = selectedInterval?.dropFirstOrder ?: -1
        val dropShowIntervalStr = selectedInterval?.dropShowInterval

        if (dropFirstOrder == -1) {
            fastLogD("当前章节的间隔配置不出金币掉落")
            return false
        }

        // 首次展示时，直接命中
        if (adExposedCnt == dropFirstOrder) {
            fastLogD("当前章节的间隔配置满足首次展示条件，出金币掉落")
            return true
        }

        // 如果没有配置后续间隔，则后续不会出金币掉落
        if (dropShowIntervalStr.isNullOrBlank()) {
            fastLogD("没有配置后续掉落间隔，不出")
            return false
        }

        // 解析掉落间隔
        val intervals =
            dropShowIntervalStr.split(",").mapNotNull { it.trim().toIntOrNull() }.filter { it >= 0 }

        if (intervals.isEmpty()) {
            fastLogD("掉落间隔配置为空或无效，不出")
            return false
        }

        // 累加间隔，判断当前adExposedCnt是否匹配某个掉落点
        var currentDrop = dropFirstOrder
        var intervalIndex = 0
        var sb = currentDrop.toString()
        while (currentDrop < adExposedCnt) {
            val interval = intervals[intervalIndex % intervals.size]
            currentDrop += (interval + 1)
            if (currentDrop == adExposedCnt) {
                fastLogD("当前章节满足金币掉落条件")
                return true
            }
            sb = sb.plus(",").plus(currentDrop)
            intervalIndex++
        }

        fastLogD("不满足任何金币掉落条件，不出 $sb")
        return false
//        return true // 测试用，直接返回true
    }

    // 根据ecpm,观看时长 来决定使用哪个间隔的配置
    fun selectInterval() {
        val lastEcpmCent = if (DrawAdKV.todayLastDrawAdEcpmCent > 0) {
            DrawAdKV.todayLastDrawAdEcpmCent
        } else {
            SkyManager.getInstance().userDrawEcpmCent
        }
        val watchDuration = AdKV.todayWatchedDurationSec2

        if (DzLog.getDebugMode()) {
            logD("金币掉落 ecpm:${lastEcpmCent}分 watchDuration:${watchDuration}秒 selectedInterval:$selectedInterval coinDropConfig:${coinDropConfig}")
        }

        // 看看lastEcpmCent和watchDuration的值落在remoteDetailAdInterval的哪个区间。注意，如果playDurationEnd为0，则代表右侧无限制，ecpmEnd为0则代表右侧无限制
        coinDropConfig?.dropIntervalConfigs?.forEach {
            if (watchDuration >= it.playDurationStart && (it.playDurationEnd == 0 || watchDuration < it.playDurationEnd) && lastEcpmCent >= it.ecpmStart && (it.ecpmEnd == 0 || lastEcpmCent < it.ecpmEnd)) {
                if (selectedInterval != it) {
                    logI("金币掉落间隔变更 $selectedInterval -> $it")
                    selectedInterval = it
                    onIntervalChanged()
                }
                return
            }
        }
        selectedInterval = null // 没有匹配到任何间隔配置
        logD("金币掉落没有匹配到任何间隔配置")
    }

    fun getCoinsRainToast() = getToast(coinDropConfig?.dropToastContent)
    fun getProgressToast() = getToast(coinDropConfig?.dropProgressContent)

    private fun getToast(content: String?): String? {
        content ?: return null
        val time = coinDropConfig?.dropWatchTime?.toString().orEmpty()
        val coin = drawTaskVo?.awardNum?.toString().orEmpty()
        return content.replace("{time}", "${time}s").replace("{coin}", coin)
    }

    // 间隔章节发生变更
    private fun onIntervalChanged() {
        adExposedCnt = 0 // 重置曝光次数
    }

    fun onDestroy() {
        coinDropConfig = null
        selectedInterval = null
        adExposedCnt = 0
    }

    // mock数据 test only
    fun mockAdIntervals(): List<DropIntervalConfigVo> {
        val adIntervals = mutableListOf<DropIntervalConfigVo>()
        adIntervals.add(DropIntervalConfigVo(0, 3, 0, 0, 1, "1"))
        adIntervals.add(DropIntervalConfigVo(3, 10, 0, 0, 2, "2,3,4"))
        adIntervals.add(DropIntervalConfigVo(10, 0, 0, 0, 3, "3"))
        return adIntervals
    }

    fun onlyClickReward(): Boolean = coinDropConfig?.onlyClickReward() ?: true
    fun enableWatchReward(): Boolean = coinDropConfig?.enableWatchReward() ?: true
    fun enableClickReward(): Boolean = coinDropConfig?.enableClickReward() ?: true

    private fun logI(msg: String) = LogUtil.i("coins_drop", msg)
    private fun logD(msg: String) = LogUtil.d("coins_drop", msg)
    private fun fastLogD(msg: String) =
        if (!AppUtil.isFastPrint()) LogUtil.d("coins_drop", msg) else Unit
}