package com.dz.platform.ad.vo

import com.dz.business.base.data.bean.BaseBean
import com.dz.platform.ad.cache.AdVoCache
import com.dz.platform.ad.vo.basic.AdSharedInfo

data class EcMallAdConfigVo(
    private var totalCount: Int = 0, //任务总次数
    private val count: Int = 0, //剩余次数，达到最大次数下发0
    private val waitTime: Long = 0L, //时间戳（毫秒），达到最大次数下发0
    private val awardNum: Int = 0,//奖励金额，如：471
    private val taskId: Int = 0,//任务id
) : BaseBean() {

    private fun getTotalCount():Int = AdVoCache.totalCount?:totalCount
    //任务数量
    fun getAllRewardTaskCount(): Int = getTotalCount()

    //剩余任务数量
    fun getRewardTaskCount(): Int = count

    //已完成任务数量
    fun getCompletedTaskCount(): Int = getTotalCount() - count

    //奖励金币数
    fun getRewardTaskGold(): Int = awardNum

    //任务总金币 用于显示
    fun getAllRewardTaskGold(): Int = getTotalCount() * awardNum

    //剩余任务总金币 用于显示
    fun getLeftRewardTaskGold(): Int = count * awardNum

    //下次任务是否是带激励的任务 true带激励 false不带激励
    fun nextHaveRewardTask(): Boolean {
        return count > 0 && waitTime < System.currentTimeMillis()
    }

    fun getTaskId(): Int = taskId

    fun setTotalCount(totalCount: Int) {
        this.totalCount = totalCount
    }

}

data class WelfareMallAd(
    private val adId: String,//广告位
    private val adConfExt: WelfareMallAdConfExt?,
) : AdSharedInfo() {

    fun getAdId(): String = adId

    fun getAdConfExt(): WelfareMallAdConfExt? = adConfExt

    //任务需要观看时长
    fun getRewardTaskDuration(): Int = adConfExt?.getRewardMinDuration() ?: 0

    //获取跑马灯文案
    fun getTipCopy(): List<String>? =
        if (adConfExt?.getRollDocsSwitch() == 0) null else adConfExt?.getRollDocs()

    //获取广告冷却时长（曝光后在X秒内不用重新加载新的广告）
    fun getAdCoolingDuration(): Int = adConfExt?.getCoolingDuration() ?: 0

    //获取任务模块
    fun getTaskGroups(): ArrayList<TaskData>? = adConfExt?.getTaskGroups()

    fun popupSwitchOn(): Boolean = adConfExt?.getPopupSwitch() == 1

    fun countdownSwitchOn(): Boolean = adConfExt?.getShowCountdownSwitch() == 1
}

data class WelfareMallAdConfExt(
    private val rollDocs: ArrayList<String>? = null,//跑马灯文案
    private val coolingDuration: Int = 60,// 冷却时长单位秒
    private val rewardMinDuration: Int = 60,//奖励下发时长，单位秒

    private val popupSwitch: Int = 1,//宝箱弹窗开关：0-关 1-开
    private val showCountdownSwitch: Int = 1,//宝箱倒计时数值开关：0-关 1-开
    private val rollDocsSwitch: Int = 1,//跑马灯开关：0-关 1-开
    private val taskGroups: ArrayList<TaskData>? = null,//任务模块
    private val scrollGuideDuration: Int = -1,//下滑引导展示时间单位s，默认0
    private val clkGuideDuration: Int = -1,//点击引导展示时间单位s，默认0

) : BaseBean() {
    fun getRollDocs(): List<String>? = rollDocs
    fun getCoolingDuration(): Int = coolingDuration
    fun getRewardMinDuration(): Int = rewardMinDuration
    fun getTaskGroups(): ArrayList<TaskData>? = taskGroups
    fun getPopupSwitch(): Int = popupSwitch
    fun getRollDocsSwitch(): Int = rollDocsSwitch
    fun getScrollGuideDuration(): Int = scrollGuideDuration
    fun getClkGuideDuration(): Int = clkGuideDuration
    fun getShowCountdownSwitch(): Int = showCountdownSwitch
}

data class TaskData(
    val duration: Int = 60,// 任务观看时长 单位秒
    val num: Int = 3,//任务次数
) : BaseBean() {

}
