package com.dz.platform.ad.sky

import com.dianzhong.base.eventbus.AdType
import com.dz.platform.ad.data.AdLoadConfig

data class MixWinAd(
    val ad: Ad?,
    val adId: String? = null,
    val adType: AdType? = null,
    val adLoadConfig: AdLoadConfig? = null,
    val requestId: String? = null,
) {

    fun getEcpmCent(): Long { // 单位分
        return (ad?.getEcpm() ?: 0).toLong()
    }


    fun getAdType(): Int { // 广告类型 AdTE.AdType
        if (adType == AdType.REWARD) {
            return 20 // AdTE.TYPE_REWARD
        }
        if (adType == AdType.INTERSTITIAL) {
            return 40 // AdTE.TYPE_REWARD
        }
        return 0
    }
}