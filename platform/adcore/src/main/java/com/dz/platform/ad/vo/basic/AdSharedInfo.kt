package com.dz.platform.ad.vo.basic

import com.dz.business.base.data.bean.BaseBean
import com.dz.business.base.data.bean.UserTacticsVo

open class AdSharedInfo(
    /**
     * 运营位ID
     */
    val operationId: Int? = null,
    /**
     * 运营位名称
     */
    val operationName: String? = null,
    /**
     * 用户分层策略
     */
    val userTacticsVo: UserTacticsVo? = null,
    /**
     * 广告位配置ID。代表当前用户命中的是哪个配置，格式为：配置项ID-配置id ig."001-56,002-76"，多个用英文,连接
     */
    val blockConfigId: String? = null,
) : BaseBean()