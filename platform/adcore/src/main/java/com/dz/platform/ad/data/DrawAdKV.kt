package com.dz.platform.ad.data

import com.dz.foundation.base.data.kv.KVData

object DrawAdKV : KVData {

    override fun getGroupName() = "com.dianzhong.ad.draw"

    private const val DEFAULT_DETAIL_START_INDEX = 11 // 二级draw默认的首出章节
    private const val DEFAULT_DETAIL_INTERVAL = "3"   // 二级draw默认的间隔章节

    private const val DEFAULT_HOME_START_INDEX = 4 // 一级draw默认的首出章节
    private const val DEFAULT_HOME_INTERVAL = "3"  // 一级draw默认的间隔章节

    private const val DEFAULT_DETAIL_DRAW_AD_VIEW_TIME = "0"   // 二级draw默认强制观看时长

    /** 后台下发的一级draw相关的数据持久化 */
    var homeDrawAdBlockConfigId by delegate("homeDrawAdBlockConfigId", "") // 广告位ID
    var homeDrawAdId by delegate("homeDrawAdId", "") // 广告位ID
    var homeDrawAdMaxShowNum by delegate("drawAdMaxShowNum", 0)//最大展示次数
    var homeDrawAdSingleTime by delegate("drawAdSingleTime", 0)//服务器下发单次请求错误的最大轮循次数
    var homeDrawAdDayTime by delegate("drawAdDayTime", 0)//服务器下发单日请求错误的最大轮循次数
    var homeDrawAdShowNum by delegate("drawAdShowNum", 0)//已展示次数
    var homeDrawAdShowDate by delegate("drawAdShowDate", "")//已展示日期
    var homeDrawAdTacticsId by delegate("drawAdTacticsId", 0)//用户分层策略ID
    var homeDrawAdTacticsName by delegate("drawAdTacticsName", "")//用户分层策略名称
    var homeDrawAdSourceId by delegate("drawAdSourceId", 0)//来源ID
    var homeDrawAdSourceName by delegate("drawAdSourceName", "")//来源名称
    var homeDrawAdShuntID by delegate("drawAdShuntID", 0)//分组ID
    var homeDrawAdShuntName by delegate("drawAdShuntName", "")//分组名称
    var homeDrawAdIsDot by delegate("drawAdIsDot", false)//是否打点

    var homeDrawAdStartIndex by delegate("homeDrawAdStartIndex", DEFAULT_HOME_START_INDEX)    //  一级dra默认的首出章节
    var homeDrawAdMinWatchTimeSec by delegate("homeDrawAdMinWatchTime", 0L)            //  出广告所需要的最小观看时间，秒

    /** 一级draw相关的本地存储 */
    var homeDrawAdLastInterval by delegate("homeDrawAdLastInterval", DEFAULT_HOME_INTERVAL)     // 一级draw上次使用的间隔章节,"3"也可能为："2,2,3"
    var homeDrawAdLastIntervalTimeSec by delegate("homeDrawAdLastIntervalTimeSec", 0L)          // 一级draw上次使用的间隔章节, 所对应的间隔时长。


    /** 后台下发的二级draw相关的数据持久化 */
    var detailDrawAdDefaultStartIndex by delegate("detailDrawAdDefaultStartIndex", DEFAULT_DETAIL_START_INDEX) // 二级draw默认的首出章节
    var detailDrawAdTermination by delegate("detailDrawAdTermination", DEFAULT_DETAIL_START_INDEX) // 二级draw首出章节终止值
    var detailDrawAdDecrement by delegate("detailDrawAdDecrement", 0)           // 二级draw首出章节递减值，默认值是0
    var detailDrawAdMinWatchTimeSec by delegate("detailDrawAdMinWatchTime", 0L)    // 二级draw出广告所需要的最小观看时间，秒
    var detailDrawMultiCacheSwitch by delegate("detailDrawMultiCacheSwitch", 0)    // 二级draw多缓存开关

    var sessionExpireThreshold by delegate("sessionExpireThreshold", 0)   // 断观剧多久记为新会话（秒）; 值范围: >0
    var lastSessionDuration by delegate("lastSessionDuration", 0)         // 上次会话观剧时长（秒）   ; 值范围: >0; (插广告必要条件)
    var sessionStartPlayDuration by delegate("sessionStartPlayDuration", 0)   // 新会话广告起点-播放时长    ; 值范围: >=0; 0-表示未勾选，其他-满足对应时长;
    var sessionStartCompleteVideo by delegate("sessionStartCompleteVideo", 0) // 新会话广告起点-至少播放一个完整集; 值范围 : 0,1; 0-表示未勾选，1-表示勾选；


    /** 二级draw相关本地存储 */
    var detailDrawAdStartIndex by delegate("detailDrawAdStartIndex", DEFAULT_DETAIL_START_INDEX)  // 二级draw首出章节, 每次进入二级页递减；递减到detailDrawAdTermination时，不再递减
    var detailDrawAdLastInterval by delegate("detailDrawAdLastInterval", DEFAULT_DETAIL_INTERVAL) // 二级draw上次使用的间隔章节,"3"也可能为："2,2,3"
    var detailDrawAdLastIntervalTimeSec by delegate("detailDrawAdLastIntervalTimeSec", 0L)        // 二级draw上次使用的间隔章节, 所对应的间隔时长
    var detailDrawAdDate by delegate("drawAdDate", "") // 沉浸式广告展示次数生效日期
    var detailDrawAdNum by delegate("drawAdNum", 0)    // 沉浸式广告当日展示次数
    var detailDrawAdDayLoopCount by delegate("drawAdDayLoopCount", 0)//单日请求错误的总轮循次数
    // 二级draw强制观看时长 //
    var detailDrawAdViewTime by delegate("detailDrawAdViewTime", DEFAULT_DETAIL_DRAW_AD_VIEW_TIME) // 二级draw使强制观看时长使用的配置,"3"也可能为："2,2,3"
    // 用户当天的上一次的draw的ecpm
    var todayLastDrawAdEcpmCent by todayDelegate("todayLastDrawAdEcpmCent", 0)
    // session相关的本地存储
    var interruptPlayEndTimeMs by todayDelegate("interruptPlayEndTimeMs", 0L)               // 中断播放结束时间戳，单位ms
    var sessionWatchedDurationSec by todayDelegate("sessionWatchedDurationSec", 0F)           // 当前会话的观剧时长，单位秒
    var lastSessionWatchedDurationSec by todayDelegate("lastSessionWatchedDurationSec", 0F)   // 上一次会话的观剧时长，单位秒
    var lastSessionStartPlayTimeSec by todayDelegate("lastSessionStartPlayTimeSec", 0L)       // 上次会话开始播放时间戳，单位ms
}


