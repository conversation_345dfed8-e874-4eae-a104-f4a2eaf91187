package com.dz.platform.ad.data

import com.dz.foundation.base.utils.LogUtil
import com.dz.platform.ad.AdManager
import com.dz.platform.ad.vo.SplashAdVo

object SplashDataUtil {

    fun onSplashAdFetched(splashAdVo: SplashAdVo?, hotSplashAdVo: SplashAdVo?) {
        splashAdVo?.let {
            if (it.getAdId().isNotEmpty() && SplashAdKV.clodSplashAdId != it.getAdId()) {
                if (SplashAdKV.clodSplashAdId.isNotEmpty()) {
                    //非第一次获取到广告位
                    LogUtil.d("splash_ad_tag", "获取新广告位:${it.getAdId()} 广告位需要更新")
                    AdManager.preloadSplashSeries(it)
                }
                SplashAdKV.clodSplashAdId = it.getAdId()
            }
        }

        SplashAdKV.splashAdOperationVo = (splashAdVo ?: SplashAdVo()).toJson()
        splashAdVo?.let {
            SplashAdKV.splashAdMaxWaitAdTime = it.getMaxWaitAdTime()
            SplashAdKV.splashAdMinWatchTimeSec = it.getMinWatchTimeForAdSec()
            LogUtil.d("splash_ad_tag", "冷起配置：${SplashAdKV.splashAdOperationVo}")
        }

        SplashAdKV.hotSplashAdOperationVo = (hotSplashAdVo ?: SplashAdVo()).toJson()
        hotSplashAdVo?.let {
            SplashAdKV.hotSplashAdIntervalNum = it.getAdIntervalSec()
            SplashAdKV.detailHotSplashAdIntervalSec = it.getDetailAdIntervalSec()
            SplashAdKV.hotSplashAdMaxWaitAdTime = it.getMaxWaitAdTime()
            SplashAdKV.hotSplashAdMinWatchTimeSec = it.getMinWatchTimeForAdSec()
            LogUtil.d("splash_ad_tag", "热起配置：${SplashAdKV.hotSplashAdOperationVo}")
        }
    }

    /**
     * splash Ad 不满足最小观看时长
     */
    fun minWatchTimeNotMetForSplashAd(isHotSplash: Boolean): Boolean {
        val minWatchSec = SplashAdKV.getSplashAdMinWatchTimeSec(isHotSplash)
        return (AdKV.historyWatchedDurationSec < minWatchSec).also {
            if (it) LogUtil.i("splash_ad_tag", "开屏不满足最小观看时长 已观看:${AdKV.historyWatchedDurationSec} 门槛:$minWatchSec")
        }
    }
}