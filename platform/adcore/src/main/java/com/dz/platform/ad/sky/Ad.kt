package com.dz.platform.ad.sky

import com.dianzhong.base.Sky.Sky
import com.dianzhong.base.data.PerformanceHolder
import com.dianzhong.base.data.RespMonitorInfo
import com.dianzhong.base.data.ShowCloseMonitorInfo
import com.dianzhong.base.data.bean.sky.ExtraMap
import com.dianzhong.base.util.AdAppNameHelper

/**
 * Created by FeiHK
 * Date: on 2023/2/2.
 * Desc:
 */
open abstract class Ad() {
    val CACHE: String = "_LOCAL_CACHE"
    var mRequestId: String? = null
    var adIsFirstLoad: Boolean = false
    var adIsFirstShow: Boolean = false
    var adIsFromCache: Boolean = false // 是否来自winner缓存

    var loadActivityStr: String? = null // 加载广告的activity的类名
    var showActivityStr: String? = null // 展示广告的activity的类名
    /**
     * 广告素材信息
     */
    var chnType: String? = ""
    var appPackage: String? = ""
    var adBrandName: String? = ""
    var adBtnText: String? = ""
    var adTitle: String? = ""
    var adDesc: String? = ""
    var adIconUrl: String? = ""
    var adImageUrl: String? = ""
    var adInteractionType: Int? = 0
    var adId: String? = "" // 三方广告自己的请求id
    var adStyleType: Int? = 0
    var codeId: String? = ""
    var slotId: String? = "" // 广告位id
    var subSlotId: String? = ""
    var preEcpc: Double? = 0.0
    var adEdition: String? = ""//广告源sdk版本号
    var stylesType: String? = ""
    var adVideoUrl: String? = ""
    var adVideoDuration: Int? = 0
    var adLoaderOrder: String? = ""//加载顺序
    var adSource: String? = ""//广告源
    var cLayered: String? = ""//层级
    var order: String? = ""//顺序
    var actionUrl: String = ""  // 广告跳转链接

    // 上报广告位相关信息

    abstract fun getEcpm(): Double

    var respMonitorInfo: RespMonitorInfo? = null
    var showCloseMonitorInfo: ShowCloseMonitorInfo? = null
    fun setBasicAdValue(sky: Sky<*, *>?) {
        val performanceHolder =
            sky?.extraMap?.get(ExtraMap.KEY.PERFORMANCE_DATA) as? PerformanceHolder
        respMonitorInfo = performanceHolder?.respInfo
        showCloseMonitorInfo = performanceHolder?.showCloseInfo
    }

    /**
     * 设置广告主信息
     */
    fun setHostInfo(host: AdAppNameHelper.UploadHostBean?) {
        host?.let {
            if (!it.appName.isNullOrEmpty())
                adBrandName = it.appName
            if (!it.title.isNullOrEmpty())
                adTitle = it.title
            if (!it.packageName.isNullOrEmpty())
                appPackage = it.packageName
            if (!it.images.isNullOrEmpty())
                adImageUrl = it.images.toString()
            if (!it.videoUrl.isNullOrEmpty())
                adVideoUrl = it.videoUrl
            if (!it.url.isNullOrEmpty())
                actionUrl = it.url
            if (it.interactionType != 0)
                adInteractionType = it.interactionType
            if (!it.requestId.isNullOrEmpty()) {
                adId = it.requestId
            }
        }
    }
}