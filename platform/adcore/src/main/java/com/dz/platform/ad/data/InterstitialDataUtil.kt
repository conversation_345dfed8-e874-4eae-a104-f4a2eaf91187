package com.dz.platform.ad.data

import com.dz.business.base.data.bean.UserTacticsVo
import com.dz.platform.ad.vo.MallAdVo

object InterstitialDataUtil {

    var mineMallUserTacticsVo: UserTacticsVo? = null

    fun onMineMallAdConfigFetched(
        mallAdVo: MallAdVo?
    ) {
        InterstitialAdKV.mallAdId = mallAdVo?.getAdId() ?: ""
        InterstitialAdKV.mallAdBlockConfigId = mallAdVo?.blockConfigId ?: ""
        InterstitialAdKV.mallSuperscriptDocs = mallAdVo?.getSuperscriptDocs() ?: ""
        InterstitialAdKV.mallDocInterval = mallAdVo?.getDocInterval() ?: 30
        InterstitialAdKV.mallLoadTimeout = mallAdVo?.getLoadTimeout()?.toLong() ?: 10
        InterstitialAdKV.mallAdCanPreload = mallAdVo?.getCanPreload() == true
        InterstitialAdKV.mallName = mallAdVo?.getMallName() ?: "我的商城"
        mallAdVo?.userTacticsVo?.let { userTacticsVo ->
            mineMallUserTacticsVo = userTacticsVo
            InterstitialAdKV.mallAdTacticsId = userTacticsVo.tacticsId ?: 0
            InterstitialAdKV.mallAdTacticsName = userTacticsVo.tacticsName ?: ""
            InterstitialAdKV.mallAdSourceId = userTacticsVo.sourceId ?: 0
            InterstitialAdKV.mallAdSourceName = userTacticsVo.sourceName ?: ""
            InterstitialAdKV.mallAdShuntID = userTacticsVo.shuntID ?: 0
            InterstitialAdKV.mallAdShuntName = userTacticsVo.shuntName ?: ""
        }
    }

}