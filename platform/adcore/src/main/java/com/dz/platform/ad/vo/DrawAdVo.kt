package com.dz.platform.ad.vo

import com.dz.business.base.data.bean.BaseBean
import com.dz.business.base.data.bean.RemoveAdWayVo
import com.dz.foundation.base.utils.LogUtil
import com.dz.platform.ad.DrawForceViewTimeUtil
import com.dz.platform.ad.data.DrawAdKV
import com.dz.platform.ad.vo.basic.AdSharedInfo

data class DrawAdVo(
    private var adConfExt: DrawAdConfExt? = null,

    /**======= 以下为二级draw特有的配置 =======*/
    val rechargeSwitch: Int? = 0,                           // 广告是否展示充值入口  1 展示    0不展示
    val removeAdArray: MutableList<RemoveAdWayVo>? = null,  // 去除广告方式数组
    var horizontalAdId: String? = null,                     // 支持 横屏的剧，在横屏下沉浸式广告的广告id
    val autoJumpSwitch: Int? = null,                        // 广告播放完自动跳过广告位开关 沉浸式 1自动跳过 0不跳过
    var guideMallAdVo: MallAdVo? = null,                    // 商城广告配置  穿山甲插屏
    var drawTask: DrawTaskVo? = null
) : AdSharedInfo() {

    fun getAdConf(): DrawAdConfExt? = adConfExt
    fun updateAdConf(confExt: DrawAdConfExt) {
        adConfExt = confExt
    }

    fun getAdId() = adConfExt?.adId
    fun getDayMaxShowNum() = adConfExt?.maxShowNum ?: 0
    fun getSingleTime() = adConfExt?.singleTime ?: 0
    fun getDayMaxFailLoopNum() = adConfExt?.dayTime ?: 0
    fun getDetailAutoJumpAdNum() =
        if ((adConfExt?.contPlayNum ?: 0) > 0) adConfExt?.contPlayNum else null

    fun getRewardAdRemoveInfo(): RemoveAdWayVo? = if (removeAdArray.isNullOrEmpty()) {
        null
    } else {
        var info: RemoveAdWayVo? = null
        for (item in removeAdArray) {
            if (item.isAd() && item.isRewardAd()) {
                info = item
            }
        }
        info
    }

    fun getRewardPreLoadNum(): Int = getRewardAdRemoveInfo()?.getPreloadNum() ?: 1

    fun canPreLoadRewardAd(): Boolean = getRewardAdRemoveInfo()?.canPreLoad() ?: false

    fun getRewardRemoveAdId(): String? = getRewardAdRemoveInfo()?.adId()

    fun getApi2150Interval(): Long? = adConfExt?.slotRefreshInterval

    fun getDetailDrawDailyProtectionTime() =
        adConfExt?.dailyProtectDurations ?: 0L   // 出广告所需要的最小观看时间，秒

    fun getBeginnerGuideCfg(): GuildPopConf? = adConfExt?.beginnerGuideCfg
    fun getRewardGuideCfg(): GuildPopConf? = getRewardAdRemoveInfo()?.getRewardGuildCfg()

    fun canFeedbackAd(): Boolean = adConfExt?.adFeedbackSwitch == 1

    fun getFeedbackAdCfg(): AdFeedbackVo? = adConfExt?.adFeedbackCfg
    fun getAfGuideCfg(): AfGuideCfgVo? = adConfExt?.afGuideCfg
}

data class DrawAdConfExt(
    val adId: String? = "",
    val maxShowNum: Int? = 0,        // 当日最大展示次数
    var dayTime: Int? = 0,           // 单日请求错误最大轮循次数
    val singleTime: Int? = 0,        // 单次请求错误最大轮循次数
    val intervalChapter: IntervalChapter? = null,       // 间隔章节
    val intervalChapterEpd: IntervalChapterEpd? = null, // 间隔章节 高优先级
    val minWatchTimeForAd: Long? = 0, // 出广告所需要的最小观看时间，分钟

    /**======= 以下为二级draw特有的配置 =======*/
    val adCacheConfig: Int? = 0, // draw多缓存配置开关 0-走老逻辑(缓存一个，且不区分强普)；
    val mandatoryViewingDuration: MandatoryViewingDuration? = null,    // 二级播放页广告强制观看时长配置  单位：秒
    val slotRefreshInterval: Long? = 60, // 2150 接口请求间隔 单位s
    val contPlayNum: Int? = 0,     //二级draw，广告可连续自动跳过次数
    val dailyProtectDurations: Long? = 0L, //当日看剧保护时间,单位秒
    val beginnerGuideCfg: GuildPopConf? = null,             // draw新手引导气泡配置
//      val beginnerGuideCfg: GuildPopConf? = mockBeginnerGuidePop(), // todo comment out this line

    val adFeedbackSwitch: Int? = 0,//反馈功能开关：adFeedbackSwitch，int类型，1-开，0-关
    val adFeedbackCfg: AdFeedbackVo? = null,//广告反馈
    val afGuideCfg: AfGuideCfgVo? = null,//广告反馈新手引导气泡

    val behaviorCtrlCfg: BehaviorCtrlCfg? = null, // draw流 用户访问行为调控
    var coinDropConfig: CoinsDropVo? = null,   // 金币掉落配置

) : BaseBean() {

    fun getMinWatchTimeForAdSec() = (minWatchTimeForAd ?: 0L) * 60    // 出广告所需要的最小观看时间，秒
}

/**
 * 按照ecpm，观看时长来选择一个interval
 */
data class IntervalChapter(
    val startIndex: Int = 0,
    val decrement: Int = 0,
    val termination: Int = 0,
    val adIntervals: List<AdInterval>? = null       // 广告间隔：2，2，3 按照ecpm，观看时长来选择
) : BaseBean()

/**
 * 按照剧集分段来选择一个interval
 * 例子：
 * {"totalEpdStart":50,"totalEpdEnd":80,"epdStart":1,"epdEnd":20,"interval":"2","intervalTime":1800}
 * {"totalEpdStart":50,"totalEpdEnd":80,"epdStart":20,"epdEnd":70,"interval":"3","intervalTime":1800}
 * {"totalEpdStart":50,"totalEpdEnd":80,"lastEpd":20,"interval":"4","intervalTime":1800}
 * 一个75集的剧，前1-19集每2集出一次广告，20-54每3集出一次广告，最后55-75集每4集出一次广告
 */
data class IntervalChapterEpd(
    val adIntervals: List<AdIntervalEpd>? = null,       // 广告间隔：2，2，3 按照剧的长度来选择
) : BaseBean()

data class MandatoryViewingDuration(
    val adDurations: List<AdDurations>? = null
) : BaseBean()

data class BasicInterval(
    var interval: String? = null,         // 强制观看时长 "3" 或者 "3,3,2"
    var intervalTime: Long? = null,       // 间隔时长 单位：秒。当前的累计观看时长-上一次广告曝光时候的累计观看时长>=这个，下一集出广告
    var chapterDurations: String? = null  // 章节分段的强制观看时间 "2,2,3"
) {
    fun isAllSet(): Boolean {
        return interval?.isNotEmpty() == true && (intervalTime
            ?: 0) > 0 && chapterDurations?.isNotEmpty() == true
    }

    fun syncChapterValues() {
        DrawAdKV.detailDrawAdLastIntervalTimeSec = intervalTime ?: 0
        DrawForceViewTimeUtil.chapterForceWatchTime = chapterDurations
    }

    fun syncEcpmValues() {
        DrawAdKV.detailDrawAdLastIntervalTimeSec = intervalTime ?: 0
    }
}

data class AdInterval(
    private val playDurationStart: Int = 0,
    private val playDurationEnd: Int = 0, // 0 代表无限制,分钟
    private val ecpmStart: Int = 0,
    private val ecpmEnd: Int = 0,   // 0 代表无限制 单位：元
    val interval: String? = null,        // 强制观看时长 "3" 或者 "3,3,2"
    private val intervalTime: Long? = 0, // 间隔时长 单位：秒。当前的累计观看时长-上一次广告曝光时候的累计观看时长>=这个，下一集出广告

) : BaseBean() {

    val playDurationStartSec: Int
        get() = playDurationStart * 60 // 秒

    val playDurationEndSec: Int
        get() = playDurationEnd * 60 // 秒

    val ecpmStartCent: Long
        get() = ecpmStart * 100L // 分

    val ecpmEndCent: Long
        get() = ecpmEnd * 100L   // 分

    val intervalTimeSec: Long
        get() = intervalTime ?: 0
}

data class AdIntervalEpd(
    val totalEpdStart: Int = 0,          // 剧集范围，比如[1-50)集的剧集，或者50-100集的剧集
    private val totalEpdEnd: Int = 0,
    val epdStart: Int = 0,               // 第几集~第几集 比如第[1集~第50)集 或者 50集~80集
    private val epdEnd: Int = 0,
    val lastEpd: Int = 0,                 // 倒数第几集~最后一集。有交集的话，以lastEpd为准
    val interval: String? = null,         // 间隔章节 "3" 或者 "3,3,2"
    val intervalTime: Long? = 0,  // 间隔时长 单位：秒。当前的累计观看时长-上一次广告曝光时候的累计观看时长>=这个，下一集出广告
    val durations: String? = null // 强制观看时间 "2,2,3"
) : BaseBean() {

    fun getTotalEpdEnd(): Int {
        return if (totalEpdEnd == 0) Integer.MAX_VALUE else totalEpdEnd
    }

    fun getEpdEnd():Int {
        return if (epdEnd == 0) Integer.MAX_VALUE else epdEnd
    }
}

/**
 * 按照ecpm，观看时长来选择一个durations
 */
data class AdDurations(
    private val playDurationStart: Int = 0,
    private val playDurationEnd: Int = 0, // 0 代表无限制,分钟
    private val ecpmStart: Int = 0,
    private val ecpmEnd: Int = 0,   // 0 代表无限制 单位：元
    val durations: String? = null // "3" 或者 "3,3,2"
) : BaseBean() {
    val playDurationStartSec: Int
        get() = playDurationStart * 60 // 秒

    val playDurationEndSec: Int
        get() = playDurationEnd * 60 // 秒

    val ecpmStartCent: Long
        get() = ecpmStart * 100L // 分

    val ecpmEndCent: Long
        get() = ecpmEnd * 100L   // 分
}

data class GuildPopConf(
    private val freq: Int = 0,
    private val doc: String? = null
) : BaseBean() {
    fun getFreq(): Int = freq
    fun getDoc(): String? = doc
}

data class BehaviorCtrlCfg(
    val sessionExpireThreshold: Int = 0,    // 中断观剧多久记为新会话（秒）; 值范围: >0
    val lastSessionDuration: Int = 0,       // 上次会话观剧时长（秒）     ; 值范围: >0; (插广告必要条件)
    val sessionStartPlayDuration: Int = 0,  // 新会话广告起点-播放时长    ; 值范围: >=0; 0-表示未勾选，其他-满足对应时长;
    val sessionStartCompleteVideo: Int = 0, // 新会话广告起点-至少播放一个完整集; 值范围 : 0,1; 0-表示未勾选，1-表示勾选；
) : BaseBean() {

    fun recordToMMKV() {
        DrawAdKV.sessionExpireThreshold = sessionExpireThreshold
        DrawAdKV.lastSessionDuration = lastSessionDuration
        DrawAdKV.sessionStartPlayDuration = sessionStartPlayDuration
        DrawAdKV.sessionStartCompleteVideo = sessionStartCompleteVideo

//        DrawAdKV.sessionExpireThreshold = sessionExpireThreshold          // for test, need comment out
//        DrawAdKV.lastSessionDuration = lastSessionDuration                // for test, need comment out
//        DrawAdKV.sessionStartPlayDuration = 150                           // for test, need comment out
//        DrawAdKV.sessionStartCompleteVideo = sessionStartCompleteVideo    // for test, need comment out
        LogUtil.d(
            "userSsBehavior",
            "用户行为功能开启：sessionExpireThreshold:$sessionExpireThreshold lastSessionDuration:$lastSessionDuration sessionStartPlayDuration:$sessionStartPlayDuration sessionStartCompleteVideo:$sessionStartCompleteVideo"
        )
    }

    object ReSetter {
        fun resetValue() {
            DrawAdKV.sessionExpireThreshold = 0 // 功能关闭
            DrawAdKV.lastSessionDuration = 0
            DrawAdKV.sessionStartPlayDuration = 0
            DrawAdKV.sessionStartCompleteVideo = 0
            LogUtil.d("userSsBehavior", "用户行为功能关闭，重置相关值")
        }
    }
}

private fun mockBeginnerGuidePop(): GuildPopConf {
    return GuildPopConf(
        30, "平台主要为免费短剧，广告是为了丰富优质短剧内容，感恩您的支持～\n" +
                "倒计时结束后上滑可继续观看短剧"
    )
}

fun mockRewardGuidePop(): GuildPopConf {
    return GuildPopConf(
        120, "观看30秒视频可以享受20-60分钟无广告沉浸式看剧观看30秒视频可以享受20-60分钟无广告沉浸式看剧"
    )
}