package com.dz.platform.ad.cache

import android.app.Activity
import com.dianzhong.base.listener.ActivityLifecycleCallbackAdapter
import com.dianzhong.common.util.DzLog
import com.dz.foundation.base.utils.LogUtil
import com.dz.platform.ad.data.AdKV
import com.dz.platform.ad.sky.InterstitialAd
import com.dz.platform.ad.sky.RewardAd
import java.util.concurrent.ConcurrentHashMap

object AdCachePool {
    var crossPage: Boolean
        get() = AdKV.enableRewardAdReuseCrossPage
        set(_) {}

    const val TAG = "AdCachePool"

    // key           value
    // activity      soltId(key)  ->  ad(value)
    private val rewardAdsCatchMap: HashMap<Int, ConcurrentHashMap<String, MutableList<RewardAd>>?> = HashMap()

    // slotId(key)  ->  ad(value)
    private val rewardAdsCacheMap: ConcurrentHashMap<String, MutableList<RewardAd>?> =
        ConcurrentHashMap()

    //标记activity注册了ActivityLifecycleCallbacks˙
    private val activitySet = HashSet<Int>()

    //-------------------------------------
    fun putRewardAds(activity: Activity, slotId: String, ad: RewardAd) {
        if (crossPage) {
            val activityHash = activity.hashCode()
            LogUtil.d(TAG, "跨页面 putRewardAd 广告位$slotId 有新广告加入缓存池中 activityHash=$activityHash")

            val list = rewardAdsCacheMap[slotId] ?: mutableListOf()
            list.add(ad)
            list.sort()
            rewardAdsCacheMap[slotId] = list
        } else {
            val activityHash = activity.hashCode()
            LogUtil.d(TAG, "putRewardAd 广告位$slotId 有新广告加入缓存池中 activityHash=$activityHash")
            val adsMap = rewardAdsCatchMap[activityHash]
                ?: ConcurrentHashMap<String, MutableList<RewardAd>>()
            val list = adsMap[slotId] ?: mutableListOf()
            list.add(ad)
            list.sort()
            adsMap[slotId] = list
            // 新增：检查是否已注册过回调
            rewardAdsCatchMap[activityHash] = adsMap

            if (!activitySet.contains(activityHash)) {
                val callback = object : ActivityLifecycleCallbackAdapter() {
                    override fun onActivityDestroyed(destroyedActivity: Activity) {
                        if (destroyedActivity.hashCode() == activityHash) {
                            DzLog.d(TAG, "加载的activity销毁，移除激励缓存广告：$slotId activityHash=$activityHash")
                            // 移除该Activity关联的所有缓存
                            removeAllAds(activityHash)
                            // 注销回调
                            destroyedActivity.application.unregisterActivityLifecycleCallbacks(this)
                            activitySet.remove(activityHash)
                        }
                    }
                }
                activity.application.registerActivityLifecycleCallbacks(callback)
                activitySet.add(activityHash)
            }
        }
    }

    fun getRewardAdFromMap(
        slotId: String,
        activity: Activity,
        removeAd: Boolean? = false
    ): RewardAd? {
        if (crossPage) {
            LogUtil.d(TAG, "跨页面 激励-缓存池广告 广告位$slotId")
            rewardAdsCacheMap[slotId]?.removeAll { !it.isValid(activity) }
            rewardAdsCacheMap[slotId] ?: return null
            rewardAdsCacheMap[slotId]?.forEachIndexed { index, rewardAd ->
                LogUtil.d(
                    TAG,
                    "跨页面 激励-缓存池广告 $index  ${rewardAd.rewardSky?.getAgentId()} ${rewardAd.hashCode()} ${rewardAd.getEcpm()} ${rewardAd.expiresTime()}"
                )
            }
            val adCacheSize = rewardAdsCacheMap[slotId]?.size ?: 0
            return if (adCacheSize > 0) {
                //有缓存广告
                if (removeAd == true) {
                    val ad = rewardAdsCacheMap[slotId]?.removeAt(0)
                    ad?.let {
                        LogUtil.d(
                            TAG,
                            "跨页面 激励-取出广告 ${it.rewardSky?.getAgentId()} ${it.hashCode()} ${it.getEcpm()} ${it.expiresTime()} 移除"
                        )
                    }
                    ad
                } else {
                    val ad = rewardAdsCacheMap[slotId]?.firstOrNull()
                    ad?.let {
                        LogUtil.d(
                            TAG,
                            "跨页面 激励-取出广告  ${it.rewardSky?.getAgentId()} ${it.hashCode()} ${it.getEcpm()} ${it.expiresTime()}  不移除"
                        )
                    }
                    ad
                }
            } else {
                null
            }.also {
                it?.adIsFromCache = true
            }
        } else {
            LogUtil.d(TAG, "激励-缓存池广告 广告位$slotId")
            val activityHash = activity.hashCode()
            val adsMap = rewardAdsCatchMap[activityHash] ?: return null
            adsMap[slotId] ?: return null
            adsMap[slotId]?.removeAll { !it.isValid(activity) }
            adsMap[slotId]?.forEachIndexed { index, rewardAd ->
                LogUtil.d(
                    TAG,
                    "激励-缓存池广告 $index  ${rewardAd.rewardSky?.getAgentId()} ${rewardAd.hashCode()} ${rewardAd.getEcpm()} ${rewardAd.expiresTime()}"
                )
            }
            return if (getRewardAdCacheSize(activity, slotId) > 0) {
                //有缓存广告
                if (removeAd == true) {
                    val ad = adsMap[slotId]?.removeAt(0)
                    rewardAdsCatchMap[activityHash] = adsMap
                    ad?.let {
                        LogUtil.d(
                            TAG,
                            "激励-取出广告 ${it.rewardSky?.getAgentId()} ${it.hashCode()} ${it.getEcpm()} ${it.expiresTime()} 移除"
                        )
                    }
                    ad
                } else {
                    val ad = adsMap[slotId]?.firstOrNull()
                    ad?.let {
                        LogUtil.d(
                            TAG,
                            "激励-取出广告  ${it.rewardSky?.getAgentId()} ${it.hashCode()} ${it.getEcpm()} ${it.expiresTime()}  不移除"
                        )
                    }
                    ad
                }
            } else {
                null
            }.also {
                it?.adIsFromCache = true // 取出广告时，重置isFirstLoad状态
            }
        }
    }

    fun getRewardAdCacheSize(activity: Activity, slotId: String): Int {
        if (crossPage) {
            rewardAdsCacheMap[slotId]?.removeAll { !it.isValid(activity) }
            return rewardAdsCacheMap[slotId]?.size ?: 0
        } else {
            val adsMap = rewardAdsCatchMap[activity.hashCode()]
            return adsMap?.get(slotId)?.size ?: 0
        }
    }


    /**
     * ---------------------插屏广告------------------------------------------------------------------------------------
     * */

    private val interstitialAdsCatchMap: HashMap<Int, ConcurrentHashMap<String, MutableList<InterstitialAd>>?> = HashMap()

    // slotId(key)  ->  ad(value)
    private val interstitialAdsCacheMap: ConcurrentHashMap<String, MutableList<InterstitialAd>?> =
        ConcurrentHashMap()

    //-------------------------------------
    fun putInterstitialAds(activity: Activity, slotId: String, ad: InterstitialAd) {
        if (crossPage) {
            val activityHash = activity.hashCode()
            LogUtil.d(TAG, "跨页面 putInterstitialAds 广告位$slotId 插屏-有新广告加入缓存池中 activityHash=$activityHash")

            val list = interstitialAdsCacheMap[slotId] ?: mutableListOf()
            list.add(ad)
            list.sort()
            interstitialAdsCacheMap[slotId] = list
        } else {
            val activityHash = activity.hashCode()
            LogUtil.d(TAG, "putInterstitialAds 广告位$slotId 插屏-有新广告加入缓存池中 activityHash=$activityHash")
            val adsMap = interstitialAdsCatchMap[activityHash]
                ?: ConcurrentHashMap<String, MutableList<InterstitialAd>>()
            val list = adsMap[slotId] ?: mutableListOf()
            list.add(ad)
            list.sort()
            adsMap[slotId] = list
            // 新增：检查是否已注册过回调
            interstitialAdsCatchMap[activityHash] = adsMap

            if (!activitySet.contains(activityHash)) {
                val callback = object : ActivityLifecycleCallbackAdapter() {
                    override fun onActivityDestroyed(destroyedActivity: Activity) {
                        if (destroyedActivity.hashCode() == activityHash) {
                            DzLog.d(
                                TAG,
                                "加载的activity销毁，移除激励缓存广告：$slotId activityHash=$activityHash"
                            )
                            // 移除该Activity关联的所有缓存
                            removeAllAds(activityHash)
                            // 注销回调
                            destroyedActivity.application.unregisterActivityLifecycleCallbacks(this)
                            activitySet.remove(activityHash)
                        }
                    }
                }
                activity.application.registerActivityLifecycleCallbacks(callback)
                activitySet.add(activityHash)
            }
        }
    }

    fun getInterstitialAdFromMap(
        slotId: String,
        activity: Activity,
        removeAd: Boolean? = false
    ): InterstitialAd? {
        if (crossPage) {
            LogUtil.d(TAG, "跨页面 插屏-缓存池广告 广告位$slotId")
            interstitialAdsCacheMap[slotId]?.removeAll { !it.isValid(activity) }
            interstitialAdsCacheMap[slotId] ?: return null
            interstitialAdsCacheMap[slotId]?.forEachIndexed { index, interstitialAd ->
                LogUtil.d(
                    TAG,
                    "跨页面 插屏-缓存池广告 $index  ${interstitialAd.adSky?.getAgentId()} ${interstitialAd.hashCode()} ${interstitialAd.getEcpm()} ${interstitialAd.expiresTime()}"
                )
            }
            val adCacheSize = interstitialAdsCacheMap[slotId]?.size ?: 0
            return if (adCacheSize > 0) {
                //有缓存广告
                if (removeAd == true) {
                    val ad = interstitialAdsCacheMap[slotId]?.removeAt(0)
                    ad?.let {
                        LogUtil.d(
                            TAG,
                            "跨页面 插屏-取出广告 ${it.adSky?.getAgentId()} ${it.hashCode()} ${it.getEcpm()} ${it.expiresTime()} 移除"
                        )
                    }
                    ad
                } else {
                    val ad = interstitialAdsCacheMap[slotId]?.firstOrNull()
                    ad?.let {
                        LogUtil.d(
                            TAG,
                            "跨页面 插屏-取出广告  ${it.adSky?.getAgentId()} ${it.hashCode()} ${it.getEcpm()} ${it.expiresTime()}  不移除"
                        )
                    }
                    ad
                }
            } else {
                null
            }.also {
                it?.adIsFromCache = true
            }
        } else {
            LogUtil.d(TAG, "插屏-缓存池广告 广告位$slotId")
            val activityHash = activity.hashCode()
            val adsMap = interstitialAdsCatchMap[activityHash]
            if (adsMap.isNullOrEmpty()) {
                return null
            }
            adsMap[slotId]?.removeAll { !it.isValid(activity) }
            adsMap[slotId]?.forEachIndexed { index, interstitialAd ->
                LogUtil.d(
                    TAG,
                    "插屏-缓存池广告 $index  ${interstitialAd.adSky?.getAgentId()} ${interstitialAd.hashCode()} ${interstitialAd.getEcpm()} ${interstitialAd.expiresTime()}"
                )
            }
            return if (getInterstitialAdCacheSize(activity, slotId) > 0) {
                //有缓存广告
                if (removeAd == true) {
                    val ad = adsMap[slotId]?.removeAt(0)
                    interstitialAdsCatchMap[activityHash] = adsMap
                    ad?.let {
                        LogUtil.d(
                            TAG,
                            "插屏-取出广告 ${it.adSky?.getAgentId()} ${it.hashCode()} ${it.getEcpm()} ${it.expiresTime()} 移除"
                        )
                    }
                    ad
                } else {
                    val ad = adsMap[slotId]?.firstOrNull()
                    ad?.let {
                        LogUtil.d(
                            TAG,
                            "插屏-取出广告  ${it.adSky?.getAgentId()} ${it.hashCode()} ${it.getEcpm()} ${it.expiresTime()}  不移除"
                        )
                    }
                    ad
                }
            } else {
                null
            }.also {
                it?.adIsFromCache = true
            }
        }
    }

    fun getInterstitialAdCacheSize(activity: Activity, slotId: String): Int {
        if (crossPage) {
            interstitialAdsCacheMap[slotId]?.removeAll { !it.isValid(activity) }
            return interstitialAdsCacheMap[slotId]?.size ?: 0
        } else {
            val adsMap = interstitialAdsCatchMap[activity.hashCode()]
            return adsMap?.get(slotId)?.size ?: 0
        }
    }


    /**
     * 根据activity移除缓存的广告
     * */
    private fun removeAllAds(activityHash: Int) {
        LogUtil.d(TAG, "removeAllAds  有广告移除 activityHash=$activityHash")
        rewardAdsCatchMap.remove(activityHash)
        interstitialAdsCatchMap.remove(activityHash)
        LogUtil.d(TAG, "removeAllAds 后 激励视频 ${rewardAdsCatchMap[activityHash]}")
        LogUtil.d(TAG, "removeAllAds 后 插屏广告 ${interstitialAdsCatchMap[activityHash]}")
    }
}