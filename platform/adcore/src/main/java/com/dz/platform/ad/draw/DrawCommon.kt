package com.dz.platform.ad.draw

import com.dianzhong.base.data.bean.DrawSeries
import com.dianzhong.common.util.DzLog
import com.dz.foundation.base.utils.LogUtil
import com.dz.platform.ad.data.DrawAdKV
import com.dz.platform.ad.sky.FeedAd
import com.dz.platform.ad.sky.SwipeReqParam
import com.dz.platform.ad.sky.getCacheExpireLeftTime
import com.dz.platform.ad.sky.getDrawEndIndex
import com.dz.platform.ad.sky.getDrawStartIndex
import com.dz.platform.ad.sky.getSlotId
import com.dz.platform.ad.sky.getSubSlotId
import java.util.concurrent.ConcurrentLinkedQueue

object DrawCommon {

    fun getHomeCacheAd(
        adCacheMap: Map<String, ConcurrentLinkedQueue<FeedAd>>,
        cacheKey: String,
        tag: String
    ): FeedAd? {
        var result: FeedAd? = null
        val feedAdQueue = adCacheMap[cacheKey]
        if (!feedAdQueue.isNullOrEmpty()) {
            while (feedAdQueue.size > 0 && feedAdQueue.peek()?.isSdkInvokeOnShow == true) {
                val firstFeed = feedAdQueue.peek()
                LogUtil.i(tag, "获取广告缓存，feedAd =${firstFeed}  已被曝光，删除")
                feedAdQueue.remove(firstFeed)
            }
            if (feedAdQueue.size > 0) {
                result = feedAdQueue.peek()
            }
        }
        LogUtil.d(tag, "getCacheAd()，cacheKey=${cacheKey} result:${result}")
        return result
    }

    fun getMaxCacheFeedAd(
        reqParam: SwipeReqParam, cacheList: ConcurrentLinkedQueue<FeedAdCache>?, tag: String
    ): FeedAd? {
        val ret = selectMaxFeedAd(reqParam, cacheList, tag)
        DzLog.d(
            tag,
            "取最大广告 ret: $ret, reqParam: $reqParam adRequestSeq"
        )
        return ret
    }

    fun getMinPDReqEcpmYuan(
        reqParam: SwipeReqParam,
        cacheList: ConcurrentLinkedQueue<FeedAdCache>?,
        tag: String
    ): Double {
        val minPDReqEcpmYuan = selectMaxFeedAd(reqParam, cacheList, tag)?.getEcpm() ?: -1.0
        DzLog.d(
            tag,
            "  getMinPDReqEcpmYuan() minPDReqEcpmYuan: $minPDReqEcpmYuan, reqParam: $reqParam"
        )
        return minPDReqEcpmYuan
    }

    private fun selectMaxFeedAd(
        reqParam: SwipeReqParam,
        cacheList: ConcurrentLinkedQueue<FeedAdCache>?,
        tag: String
    ): FeedAd? {
        val ret = getCacheFeedAds(reqParam, cacheList, tag)?.maxWithOrNull(
            compareBy(
                { it.getEcpm() },                   // Ecpm优先级最高，越大越好
                { -it.getCacheExpireLeftTime() },   // 如果 Ecpm 相等，再按照 过期时间，越小越好
            )
        )
        return ret
    }

    fun getCacheFeedAds(
        reqParam: SwipeReqParam, cacheList: ConcurrentLinkedQueue<FeedAdCache>?, tag: String
    ): List<FeedAd>? {
        return getFeedAdCaches(reqParam, cacheList, tag)?.map { it.feedAd }
    }

    fun getFeedAdCaches(
        reqParam: SwipeReqParam,
        cacheList: ConcurrentLinkedQueue<FeedAdCache>?,
        tag: String
    ): List<FeedAdCache>? {
        cacheList ?: return null.also {
            LogUtil.d(tag, "getFeedAdCaches()，cacheList is null")
        }
        val rangeMatchCaches = rangeMatch(cacheList, reqParam)
        return rangeMatchCaches.filter { insertTypeMatch(reqParam, it) }
    }

    private fun rangeMatch(
        cacheList: ConcurrentLinkedQueue<FeedAdCache>,
        reqParam: SwipeReqParam
    ): List<FeedAdCache> {
        val rangeMatchCaches = cacheList.filter { cacheAd ->
            val startIndex = cacheAd.feedAd.getDrawStartIndex()
            val endIndex = cacheAd.feedAd.getDrawEndIndex()
            startIndex != 0 && DrawSeries.isInRange(reqParam.reqSeq, startIndex, endIndex)
        }
        return rangeMatchCaches
    }

    private fun insertTypeMatch(
        reqParam: SwipeReqParam,
        feedAdCache: FeedAdCache,
    ) =
        (reqParam.insertType == feedAdCache.insertType || feedAdCache.insertType == 1)
                || (DrawAdKV.detailDrawMultiCacheSwitch == 0).also {
            if (it) {
                DzLog.d("SkyLoader", "多缓存未开启，不区分强弱插")
            }
        }
}

data class FeedAdCache(
    var startIndex: Int = 0,    // 刷次区间：开始值
    var endIndex: Int = 0,      // 刷次区间：结束值
    var insertType: Int = 0,    // 插入类型：1=否，2=普，3=强
    var feedAd: FeedAd
) {
    override fun toString(): String {
        return "sIdx:$startIndex, eIdx:$endIndex, insertType:$insertType, subSlotId:${feedAd.getSubSlotId()}}"
    }
}