package com.dz.platform.ad

import com.dz.foundation.base.utils.LogUtil
import com.dz.platform.ad.data.AdKV
import com.dz.platform.ad.vo.BannerAdDurations

object DetailBannerIntervalTimeUtil {

    private const val TAG = "BannerIntervalTime"

    private var remoteBannerAdDurations: List<BannerAdDurations>? = null // 底通banner ecpm限制策略

    fun setIntervalData(durations: List<BannerAdDurations>?) {
        remoteBannerAdDurations = durations
    }

    //需要先执行 setIntervalData
    fun checkBannerAdCanShow(ecpm: Long): Boolean {
        LogUtil.d(
            TAG,
            "calAdEcpmLimit checkBannerAdCanShow ecpm=$ecpm"
        )
        if (remoteBannerAdDurations.isNullOrEmpty()) {
            return true
        }
        // 根据观看时长，ecpm计算间隔周期
        val ecpmLimit = calAdEcpmLimit()
        return ecpm >= ecpmLimit
    }

    // 根据ecpm,观看时长 来决定使用哪个Ecpm限制的配置
    private fun calAdEcpmLimit(): Long {
        var result = 0L
        val watchDuration = AdKV.todayWatchedDurationSec2
        // 看看watchDuration的值落在哪个区间。注意，如果playDurationEnd为0，则代表右侧无限制
        remoteBannerAdDurations?.forEach {
            if (watchDuration >= it.playDurationStart && (it.playDurationEnd == 0 || watchDuration < it.playDurationEnd)) {
                result = it.ecpm
                return@forEach
            }
        }
        LogUtil.d(
            TAG,
            "calAdEcpmLimit ecpmLimit:$result watchDuration:$watchDuration"
        )
        return result
    }

    // mock数据 test only
    private fun mockAdIntervals(): List<BannerAdDurations> {
        val adIntervals = mutableListOf<BannerAdDurations>()
        adIntervals.add(BannerAdDurations(0, 50, 40000))
        adIntervals.add(BannerAdDurations(50, 300, 30000))
        adIntervals.add(BannerAdDurations(300, 10000, 20000))
        return adIntervals
    }

}