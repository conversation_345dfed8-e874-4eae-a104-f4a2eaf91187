package com.dz.platform.ad.manager

import android.app.Activity
import com.dianzhong.base.data.bean.sky.RewardCloseParams
import com.dianzhong.core.manager.loader.RewardVideoLoader
import com.dz.foundation.base.utils.LogUtil
import com.dz.platform.ad.AdManager
import com.dz.platform.ad.cache.AdCachePool
import com.dz.platform.ad.callback.BaseAdLoadCallback
import com.dz.platform.ad.callback.RewardAdLoadCallback
import com.dz.platform.ad.callback.RewardAdShowCallback
import com.dz.platform.ad.sky.RewardAd
import com.dz.platform.ad.sky.SkyAd

/**
 * 1.福利页插屏混竞-激励
 * 2.免广，3.福利页纯激励
 * 有缓存能力
 */
object RewardAdManager {

    const val TAG = "RewardAdManager"

    private val map: HashMap<String, RewardVideoLoader> = hashMapOf()
    private val preloadMap: HashMap<String, RewardVideoLoader> = hashMapOf()

    fun cancelPreloadAd(adId: String) {
        if (preloadMap.containsKey(adId)) {
            val adLoader = preloadMap[adId]
            LogUtil.d(TAG, "cancelPreloadAd 取消广告加载，广告位：$adId")
            adLoader?.cancelAdsLoading()
            preloadMap.remove(adId)
        } else {
            LogUtil.d(TAG, "cancelPreloadAd 失败，广告位：$adId")
        }
    }

    /**
     * 预加载激励视频广告
     */
    fun preLoadRewardAd(
        activity: Activity,
        adId: String,
        physicalPosId: Int?,
        blockConfigId: String?,
        bookId: String?,
        chapterId:String?,
        preLoadNum: Int,
        requestId: String?,
        callback: BaseAdLoadCallback<RewardAd>?
    ) {
        LogUtil.d(TAG, "preLoadRewardAd 开始加载广告，广告位：$adId")
        val cacheAdNum = AdCachePool.getRewardAdCacheSize(activity, adId)
        if (cacheAdNum > preLoadNum) {
            LogUtil.d(TAG, "have catch ad,not need pre load")
            return
        }
        LogUtil.d(TAG, "catchAd 不足,cacheAdNum=$cacheAdNum preLoadNum=$preLoadNum")
        val preloader = AdManager.loadRewardAd(
            activity,
            adId,
            physicalPosId,
            blockConfigId,
            bookId,
            chapterId,
            null,
            requestId,
            object : BaseAdLoadCallback<RewardAd> {
                override fun onStartLoad() {
                    LogUtil.d(TAG, "preLoad-onStartLoad")
                    callback?.onStartLoad()
                }

                override fun onLoadError(code: Int, msg: String) {
                    LogUtil.d(TAG, "preLoad-onLoadError $code $msg")
                    callback?.onLoadError(code, msg)
                }

                override fun onMaterialStartLoad(sky: SkyAd?) {
                }

                override fun onMaterialStatusChanged(sky: SkyAd?) {
                }

                override fun onLoadSuccess(ad: RewardAd) {
                    LogUtil.d(TAG, "preLoad-onLoadSuccess")
                    AdCachePool.putRewardAds(activity, adId, ad)
                    callback?.onLoadSuccess(ad)
                }
            })
        preloader?.let {
            preloadMap.put(adId, it)
        }
    }

    fun needLoadMoreAds(activity: Activity, adId: String, number: Int): Boolean {
        val size = AdCachePool.getRewardAdCacheSize(activity, adId)
        LogUtil.d(TAG, "adId=$adId needPreLoadNum=$number rewardAdCacheSize=$size")
        return number - size > 0
    }

    fun hasCachedAd(activity: Activity, adId: String): Boolean {
        val size = AdCachePool.getRewardAdCacheSize(activity, adId)
        LogUtil.d(TAG, "adId=$adId hasCachedAd rewardAdCacheSize=$size")
        return size > 0
    }

    /**
     * 获取激励视频广告
     */
    fun loadRewardAd(
        activity: Activity,
        adId: String,
        physicalPosId: Int?,
        bookId: String?,
        chapterId:String?,
        blockConfigId: String?,
        timeout: Long?,
        requestId: String?,
        callback: RewardAdLoadCallback<RewardAd>
    ) {
        LogUtil.d(TAG, "loadRewardAd 开始加载广告，广告位：$adId")
        val ad = AdCachePool.getRewardAdFromMap(adId, activity, false)
        if (ad != null) {
            LogUtil.d(TAG, "have cache ad")
            callback.onLoadSuccess(ad, true)
            return
        }
        LogUtil.d(TAG, "no cache ad,need load")
        val adLoader =
            AdManager.loadRewardAd(
                activity,
                adId,
                physicalPosId,
                blockConfigId,
                bookId,
                chapterId,
                timeout,
                requestId,
                object : BaseAdLoadCallback<RewardAd> {
                    override fun onStartLoad() {
                        LogUtil.d(TAG, "load-onStartLoad")
                        callback.onStartLoad()
                    }

                    override fun onLoadSuccess(ad: RewardAd) {
                        LogUtil.d(TAG, "load-onLoadSuccess")
                        AdCachePool.putRewardAds(activity, adId, ad)
                        callback.onLoadSuccess(ad, false)
                        map.remove(adId)
                    }

                    override fun onLoadError(code: Int, msg: String) {
                        LogUtil.d(TAG, "load-onLoadError $code $msg")
                        callback.onLoadError(code, msg)
                        map.remove(adId)
                    }

                    override fun onMaterialStartLoad(sky: SkyAd?) {
                        callback.onMaterialStartLoad(sky)
                    }

                    override fun onMaterialStatusChanged(sky: SkyAd?) {
                        callback.onMaterialStatusChanged(sky)
                    }
                })
        adLoader?.let {
            map.put(adId, it)
        }
    }


    /**
     * 显示激励视频
     */
    fun showCachedRewardAd(
        activity: Activity,
        adId: String,
        callback: RewardAdShowCallback
    ): Boolean {
        LogUtil.d(TAG, "showRewardAd")
        val ad = AdCachePool.getRewardAdFromMap(adId, activity, true)
        if (ad != null && ad.isValid(activity)) {
            LogUtil.d(TAG, "have cache ad")
            AdManager.showRewardAd(ad, object : RewardAdShowCallback {
                override fun onVideoStart() {
                }

                override fun onVideoComplete() {
                    callback.onVideoComplete()
                }

                override fun onShow(ad: RewardAd) {
                    LogUtil.d(TAG, "onShow")
                    callback.onShow(ad)
                }

                override fun onShowError(ad: RewardAd, code: Int, msg: String) {
                    LogUtil.d(TAG, "onShowError $code $msg")
                    callback.onShowError(ad, code, msg)
                }

                override fun onClick(ad: RewardAd) {
                    LogUtil.d(TAG, "onClick")
                    callback.onClick(ad)
                }

                override fun onReward(ad: RewardAd) {
                    LogUtil.d(TAG, "onReward")
                    callback.onReward(ad)
                }

                override fun onClose(ad: RewardAd, rewardCloseParams: RewardCloseParams?) {
                    LogUtil.d(TAG, "onClose")
                    callback.onClose(ad, rewardCloseParams)
                }

            })
            return true
        } else {
            return false
        }
    }

    fun cancelAdsLoading(adId: String) {
        if (map.containsKey(adId)) {
            val adLoader = map[adId]
            adLoader?.cancelAdsLoading()
            map.remove(adId)
        }
    }
}