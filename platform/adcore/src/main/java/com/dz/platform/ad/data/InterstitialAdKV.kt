package com.dz.platform.ad.data

import com.dz.foundation.base.data.kv.KVData

object InterstitialAdKV : KVData {

    override fun getGroupName() = "com.dianzhong.ad.interstitial"

    /** 我的商城相关的本地存储 */
    var mallAdId by delegate("mallAdId", "") // 广告位ID
    var mallAdBlockConfigId by delegate("mallAdBlockConfigId", "") // 广告位ID
    var mallName by delegate("mallName", "我的商城") // 按钮文案
    var mallSuperscriptDocs by delegate("mallSuperscriptDocs", "")  // 按钮文案
    var mallDocInterval by delegate("mallDocInterval", 0) //  间隔时长，单位：分
    var mallLoadTimeout by delegate("mallLoadTimeout", 0L) // 超时时长，单位：秒
    var showMallAdTime by delegate("showMallAdTime", 0L) // 超时时长，单位：秒
    var mallAdCanPreload by delegate("mallAdCanPreload", false) // 是否开启预加载

    var mallAdTacticsId by delegate("mineMallAdTacticsId", 0)//用户分层策略ID
    var mallAdTacticsName by delegate("mineMallAdTacticsName", "")//用户分层策略名称
    var mallAdSourceId by delegate("mineMallAdSourceId", 0)//来源ID
    var mallAdSourceName by delegate("mineMallAdSourceName", "")//来源名称
    var mallAdShuntID by delegate("mineMallAdShuntID", 0)//分组ID
    var mallAdShuntName by delegate("mineMallAdShuntName", "")//分组名称

}