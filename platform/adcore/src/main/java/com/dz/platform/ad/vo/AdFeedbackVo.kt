package com.dz.platform.ad.vo

import com.dz.business.base.data.bean.BaseBean

data class AdFeedbackVo(
    val feedbacks: ArrayList<String>? = null,//广告反馈选项：feedbacks,List<String>类型（字符串数组）
    val explainDoc: String = "",//出广告文案：explainDoc,   String类型
    val countdownDoc: String = "",
    val finishedDoc: String = "",//反馈后文案：finishedDoc,  String类型
    val intervalTime: Int = 0,//间隔时长：intervalTime,  int类型（单位为秒）
) : BaseBean()

data class AfGuideCfgVo(
    val freq: Int = 0,
    val doc: String = "",
) : BaseBean()
