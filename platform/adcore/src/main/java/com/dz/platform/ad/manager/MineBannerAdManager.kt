package com.dz.platform.ad.manager

import android.app.Activity
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import com.dz.foundation.base.utils.LogUtil
import com.dz.foundation.base.utils.RandomUtils
import com.dz.foundation.base.utils.ScreenUtil
import com.dz.platform.ad.AdManager
import com.dz.platform.ad.R
import com.dz.platform.ad.callback.FeedAdCallback
import com.dz.platform.ad.data.BannerAdKV
import com.dz.platform.ad.sky.FeedAd
import com.dz.platform.ad.sky.SkyAd
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch

object MineBannerAdManager {

    const val TAG = "MineBannerAdManager"
    private const val adHeight = 80

    var bannerAd: FeedAd? = null

    private var mIsLoading: Boolean = false
    private var mWidthTemplate: Int = 0//广告宽度
    private var mHeightTemplate: Int = 0//广告高度

    var isShowing: Boolean = false//当前广告是否是在播放

    var onLoadSuccess: ((feedAd: FeedAd?, timeSpent: Long, requestId: String, preLoad: Boolean) -> Unit)? =
        null
    var onLoadError: (() -> Unit)? = null
    var onAdShow: ((feedAd: FeedAd?, time: Int, requestId: String) -> Unit)? = null
    var onAdClose: ((feedAd: FeedAd?, showTime: Long, requestId: String) -> Unit)? = null
    var onAdClick: ((feedAd: FeedAd?, showTime: Long, requestId: String) -> Unit)? = null

    fun loadBannerAd(
        userId: String,
        adId: String,
        preLoad: Boolean,
        activity: Activity?,
        originalDensity: Float
    ) {
        LogUtil.d(TAG, "开始请求广告 adId=$adId")
        if (adId.isEmpty()) {
            return
        }
        if (mIsLoading) {
            LogUtil.d(TAG, "不满足广告请求条件，广告请求结束 mIsLoading==$mIsLoading")
            return
        }
        GlobalScope.launch(Dispatchers.IO) {
            activity?.let {
                mWidthTemplate = ScreenUtil.getScreenWidth()
                mHeightTemplate = ScreenUtil.dip2px(it, adHeight)
                val requestTime = System.currentTimeMillis()
                val requestId: String =
                    userId + "_" + requestTime + "_" + RandomUtils.getRandumNum(999, 100)
                AdManager.loadFeedAd(
                    activity = it,
                    ScreenUtil.px2dipByDensity(it, originalDensity, mWidthTemplate),
                    adHeight,
                    mWidthTemplate,
                    mHeightTemplate,
                    adId = adId,
                    physicalPosId = 134,
                    blockConfigId = BannerAdKV.mineBannerAdBlockConfigId,
                    adDrawReqSeq = 0,
                    backgroundColor = ContextCompat.getColor(it, R.color.common_white),
                    isNightMode = false,
                    isDrawAd = false,
                    bookId = "",
                    chapterId = "",
                    videoMute = true,
                    callback = object : FeedAdCallback {
                        var loadedTime = 0L
                        var playComplete = false
                        var playTime = 0L
                        var showTime = 0L //开始播放时间
                        override fun onStartLoad(feedAd: FeedAd?) {

                        }

                        override fun onFeedSkyLoaded(feedAd: FeedAd?) {
                            bannerAd = feedAd
                            showTime = 0L
                            LogUtil.d(TAG, "广告onFeedSkyLoaded")
                            mIsLoading = false
                            feedAd?.run {
                                bannerAd = this
                                LogUtil.d(TAG, "广告成功返回，广告请求结束")
                                onLoadSuccess?.invoke(
                                    feedAd,
                                    loadedTime - requestTime,
                                    requestId,
                                    preLoad
                                )
                            }
                            loadedTime = System.currentTimeMillis()

                        }

                        override fun onFail(feedAd: FeedAd?, message: String?, code: String?) {
                            LogUtil.d(TAG, "广告请求失败 message=$message code=$code")
                            showTime = 0L
                            mIsLoading = false
                            onLoadError?.invoke()
                            resetData()
                        }

                        override fun onShow(feedAd: FeedAd?) {
                            LogUtil.d(TAG, "广告曝光")
                            isShowing = true
                            showTime = System.currentTimeMillis()
                            feedAd?.showTime = showTime
                            val time =
                                (feedAd?.mFeedSky?.strategyInfo?.imp_time ?: 30000) / 1000
                            onAdShow?.invoke(
                                feedAd,
                                (if (time > 0) {
                                    time
                                } else {
                                    30
                                }).toInt(), requestId
                            )
                        }

                        override fun onClick(feedAd: FeedAd?) {
                            LogUtil.d(TAG, "广告点击")
                            onAdClick?.invoke(feedAd, showTime, requestId)
                        }

                        override fun onClose(feedAd: FeedAd?) {
                            LogUtil.d(TAG, "广告关闭")
                            isShowing = false
                            onAdClose?.invoke(feedAd, showTime, requestId)
//                            resetData()
                        }

                        override fun onVideoStart(feedAd: FeedAd?) {
                            LogUtil.d(TAG, "广告onVideoStart")
                            playTime = System.currentTimeMillis()
                        }

                        override fun onVideoComplete(feedAd: FeedAd?) {
                            LogUtil.d(TAG, "广告onVideoComplete")
                            playComplete = true
                        }

                        override fun loadStart(sky: SkyAd?) {

                        }

                        override fun loadStatus(sky: SkyAd?) {

                        }
                    }
                )
            }
        }
    }

    private fun resetData() {
        bannerAd = null
        mIsLoading = false
    }

    fun getBannerAd() {
        bannerAd
    }

    fun onDestroy() {
        LogUtil.d(TAG, "个人中心页广告onDestroy")
        bannerAd?.mFeedLoader?.cancelAdsLoading()
        bannerAd?.destroy()
        mIsLoading = false
        bannerAd = null
        recycleAdResource()
    }

    private fun recycleAdResource() {
        onLoadError = null
        onAdClose = null
        onLoadSuccess = null
        onAdShow = null
        bannerAd = null
    }

}