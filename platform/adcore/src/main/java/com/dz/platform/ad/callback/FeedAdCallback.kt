package com.dz.platform.ad.callback

import com.dz.platform.ad.sky.FeedAd
import com.dz.platform.ad.sky.SkyAd

interface FeedAdCallback {

    fun onStartLoad(feedAd: FeedAd?)

    fun onFeedSkyLoaded(feedAd: FeedAd?)

    fun onFail(feedAd: FeedAd?, message: String?, code: String?)

    fun onShow(feedAd: FeedAd?)

    fun onClick(feedAd: FeedAd?)

    fun onClose(feedAd: FeedAd?)

    fun onVideoStart(feedAd: FeedAd?)

    fun onVideoComplete(feedAd: FeedAd?)

    fun loadStart(sky: SkyAd?)

    fun loadStatus(sky: SkyAd?)

    fun onReward(feedAd: FeedAd?){

    }
}