package com.dz.platform.ad.sky

import com.dianzhong.base.Sky.SplashSky
import com.dianzhong.core.manager.loader.SplashLoader

/**
 * Created by FeiHK
 * Date: on 2023/2/2.
 * Desc:
 */
class SplashAd : Ad() {
    var splashSky: SplashSky? = null
        set(value) {
            field = value
            setBasicAdValue(value)
            setAdValue()
            setHostInfo(value?.uploadHostBean)
        }
    var splashLoader: SplashLoader? = null

    var timeSpent: Long = 0//耗时
    var updateTime: Long = 0


    fun onDestroy(){
        splashLoader?.run {
            cancelAdsLoading()
        }
    }

    private fun setAdValue() {
        splashSky?.run {
            chnType = realAdSourceName
            if (isMaterialFromCache) {
                chnType += CACHE
            }
            adEdition = skyApi.sdkVersion
            codeId = slotId
            <EMAIL> = strategyInfo.slotId
//            <EMAIL> = getEcpm()
            adVideoDuration = videoTime
            adSource = strategyInfo.chn_type
            cLayered = strategyInfo.layer.toString()
            order = strategyInfo.currentIdIndex.toString()
            adLoaderOrder = "$adSource-$cLayered-$order"
        }
    }

    override fun getEcpm():Double{
        return splashSky?.strategyInfo?.ecpmYuanStr?.toDouble()?:0.0
    }
}