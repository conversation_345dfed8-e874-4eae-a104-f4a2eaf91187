package com.dz.platform.ad.vo

import com.dz.business.base.data.bean.BaseBean
import com.dz.business.base.data.bean.REWARD_CAN_PRELOAD
import com.dz.business.base.data.bean.REWARD_NOT_CAN_PRELOAD
import com.dz.foundation.base.utils.JsonUtil
import com.dz.platform.ad.vo.basic.AdSharedInfo


data class MallAdVo(
    val adConfExt: MallAdVoConfExt? = null,                // 广告配置透传字段
) : AdSharedInfo() {
    fun getAdId() = adConfExt?.adId
    fun getSuperscriptDocs() = JsonUtil.toJson(adConfExt?.superscriptDocs ?: "")
    fun getDocInterval() = adConfExt?.mallDocInterval ?: 30
    fun getLoadTimeout() = adConfExt?.mallLoadTimeout ?: 3
    fun getCanPreload() = adConfExt?.preLoadConfig == REWARD_CAN_PRELOAD
    fun getMallName() = if (!adConfExt?.mallName.isNullOrEmpty()) {
        adConfExt?.mallName
    } else {
        DEFAULT_MALL_NAME
    }

    companion object{
        const val DEFAULT_MALL_NAME = "我的商城"
    }


    fun canShowMallIcon(renderStyle: Int, adChn: String): Boolean {
        if (adConfExt?.malltpSwitch == 0) {
            return false
        }
        if (adConfExt?.malltpStps.isNullOrEmpty() || !(adConfExt?.malltpStps!!.contains(renderStyle))) {
            return false
        }
        if (adConfExt.malltpChns.isNullOrEmpty() || !(adConfExt.malltpChns!!.contains(adChn))) {
            return false
        }
        return true
    }

    fun canShowCsjMallIcon(): Boolean {
        return adConfExt?.mallCsjtpSwitch != 0
    }


}

data class MallAdVoConfExt(
    //个人页商城
    var adId: String? = null,                    // 广告位ID
    var superscriptDocs: List<String>? = null,   // 按钮文案
    var mallDocInterval: Int? = 30,               //  间隔时长，单位：分
    var mallLoadTimeout: Int? = 3,               // 超时时长，单位：秒
    val preLoadConfig: Int? = REWARD_NOT_CAN_PRELOAD, //0-关闭 1-开启
    var mallName: String? = MallAdVo.DEFAULT_MALL_NAME,

    // 二级Draw商城
    var malltpSwitch: Int? = 0,               //  商城组件样式开关：0-关，1-开
    var malltpDoc: String? = null,            //  文案
    var malltpStps: List<Int>? = null,        //  生效样式： "70001,70002,70003"
    var malltpChns: List<String>? = null,     //  生效广告源："SDK_CSJ,API_HUAWEI"
    var mallCsjtpSwitch: Int? = 0,            //   穿山甲闭环电商样式开关：0-关，1-开
    var mallCsjtpDoc: String? = null,         //   穿山甲闭环电商样式开关：0-关，1-开
    var mallCsjtpSubdoc1: String? = null,     //   副标题1文案
    var mallCsjtpSubdoc2: String? = null,     //   副标题2文案
    var mallCsjtpActionBtnDoc: String? = null,//   行动按钮文案
    var mallCsjtpShowTs: String? = null,      //   展示时间： 4

) : BaseBean()