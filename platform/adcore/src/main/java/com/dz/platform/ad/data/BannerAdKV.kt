package com.dz.platform.ad.data

import com.dz.foundation.base.data.kv.KVData

object BannerAdKV : KVData {

    override fun getGroupName() = "com.dianzhong.ad.banner"

    /** banner相关的本地存储 */
    var bannerHeight by delegate("bannerHeight", 60)                        // banner广告的高度，默认是60
    var bannerAdMinWatchTimeSec by delegate("bannerAdMinWatchTimeSec", 0L)  // banner出广告所需要的最小观看时间，秒
    var bannerAdDate by delegate("bannerAdDate", "")      // 沉浸式广告展示次数生效日期
    var bannerAdNum by delegate("bannerAdNum", 0)         // banner广告当天显示次数
    var bannerReLoadNum by delegate("bannerReLoadNum", 0) // banner广告当天报错重试的总次数

    var bannerFirstShowTime by delegate("bannerFirstShowTime", 0L) //  banner出广告所需要的最小观看时间，秒
    var bannerEcpmBelowLimitErrorTime by delegate("bannerEcpmBelowLimitErrorTime", 0L) // banner广告间隔时间


    /** mine-banner相关的本地存储 */
    var freeMineBannerAd by delegate("freeMineBannerAd", 0)       //是否免主页banner广告
    var mineBannerAdId by delegate("mineBannerAdId", "")          // banner广告位
    var mineBannerAdBlockConfigId by delegate("mineBannerAdBlockConfigId", "")
    var mineBannerHeight by delegate("mineBannerHeight", 80)      // banner广告的高度，默认是80
    var mineBannerAdDate by delegate("mineBannerAdDate", "")      // banner广告展示次数生效日期
    var mineBannerAdCloseAdIntervalNum by delegate("mineBannerAdCloseAdIntervalNum", 10) // banner关闭广告后间隔
    var mineBannerObtainAdFailInterval by delegate("mineBannerObtainAdFailInterval", 0) // 广告请求失败间隔
    var mineBannerAdFailRetry by delegate("mineBannerAdFailRetry", 0) // 广告失败重试次数
    var mineBannerReLoadNum by delegate("mineBannerReLoadNum", 0) // banner广告当天报错重试的总次数
    var mineBannerCanFeedbackAd by delegate("mineBannerCanFeedbackAd", false) // 是否可以展示广告反馈
    var mineBannerFeedbackCfg by delegate("mineBannerFeedbackCfg", "") // 广告反馈配置


    var mineBannerAdTacticsId by delegate("mineBannerAdTacticsId", 0)//用户分层策略ID
    var mineBannerAdTacticsName by delegate("mineBannerAdTacticsName", "")//用户分层策略名称
    var mineBannerAdSourceId by delegate("mineBannerAdSourceId", 0)//来源ID
    var mineBannerAdSourceName by delegate("mineBannerAdSourceName", "")//来源名称
    var mineBannerAdShuntID by delegate("mineBannerAdShuntID", 0)//分组ID
    var mineBannerAdShuntName by delegate("mineBannerAdShuntName", "")//分组名称
}