package com.dz.platform.ad.callback

import com.dz.platform.ad.sky.InterstitialAd
import com.dz.platform.ad.sky.SkyAd


interface InterstitialAdCallback {

    fun onStartLoad(ad: InterstitialAd?)

    fun onLoaded(ad: InterstitialAd?)

    fun onADFail(ad: InterstitialAd?, message: String?)

    fun onADClick(ad: InterstitialAd?)

    fun onADShow(ad: InterstitialAd?)

    fun onReward()

    fun onADClose(ad: InterstitialAd?)

    fun loadStart(sky: SkyAd?)

    fun loadStatus(sky: SkyAd?)
}