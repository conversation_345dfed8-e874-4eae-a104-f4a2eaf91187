package com.dz.platform.ad.callback

import com.dianzhong.base.data.bean.sky.RewardCloseParams
import com.dz.platform.ad.vo.AdConstant

/**
 *@Author: zhanggy
 *@Date: 2023-05-19
 *@Description:广告展示的各种回调
 *@Version:1.0
 */
interface BaseAdShowCallback<T> {

    fun onShow(ad: T)

    fun onShowError(ad: T, code: Int, msg: String)

    fun onClick(ad: T)

    fun onReward(ad: T)

    fun onClose(ad: T,rewardCloseParams: RewardCloseParams?)

    fun onGetReportParams(): Map<String, Any>? {
        return null
    }
}

fun BaseAdShowCallback<*>.onGetClickLoadTime(): Long {
    return onGetReportParams()?.get(AdConstant.WELFARE_USER_CLICK_LOAD_MILLIS) as? Long ?: 0L
}