package com.dz.platform.ad.sky

import android.text.TextUtils
import com.dianzhong.base.Sky.Sky
import com.dz.foundation.base.utils.LogUtil


/**
 * Created by FeiHK
 * Date: on 2022/8/12.
 * Desc:
 */
class SkyAd : Ad() {
    var sky: Sky<*, *>? = null
    private var mSkySource: String? = null
    private var mSkySDKVersion: String? = null
    private var mCodeId: String? = null
    private var mTotalAdNum //总策略数
            = 0
    private var mTotalAdDeck //总层数
            = 0
    val skySource: String?
        get() {
            if (sky != null && sky!!.skySource != null) {
                mSkySource = sky!!.realAdSourceName
            }
            if (sky!!.isMaterialFromCache) {
                mSkySource += CACHE
            }
            return mSkySource
        }
    val skySDKVersion: String?
        get() {
            if (sky != null && sky!!.skyApi != null) {
                mSkySDKVersion = sky!!.skyApi.sdkVersion
            }
            return mSkySDKVersion
        }
    val skyCodeId: String?
        get() {
            if (sky != null && sky!!.strategyInfo != null) {
                mCodeId = sky!!.strategyInfo.chn_slot_id
            }
            return mCodeId
        }

    fun setTotalAdNum(mTotalAdNum: Int) {
        this.mTotalAdNum = mTotalAdNum
    }

    fun setTotalAdDeck(mTotalAdDeck: Int) {
        this.mTotalAdDeck = mTotalAdDeck
    }

    /**
     * 并发层级
     *
     * @return '8-3-1 总id量-总层次-当前层
     */
    val cLayereds: String
        get() {
            val stringBuilder = StringBuilder()
            stringBuilder.append(mTotalAdNum)
            stringBuilder.append("-")
            stringBuilder.append(mTotalAdDeck)
            stringBuilder.append("-")
            if (sky != null && sky!!.strategyInfo != null) {
                stringBuilder.append(sky!!.strategyInfo.layer)
            }
            return stringBuilder.toString()
        }

    /**
     * 并发层级
     *
     * @return '8-3-1 当前层-并发量-次序
     */
    val orders: String
        get() {
            val stringBuilder = StringBuilder()
            if (sky != null) {
                val strategyInfo = sky!!.strategyInfo
                if (strategyInfo != null) {
                    stringBuilder.append(strategyInfo.layer)
                    stringBuilder.append("-")
                    stringBuilder.append(strategyInfo.currentLayerAdNum)
                    stringBuilder.append("-")
                    stringBuilder.append(strategyInfo.currentIdIndex)
                }
            }
            return stringBuilder.toString()
        }
    val stateCode: String
        get() {
            var code = "0"
            if (sky != null && sky!!.strategyInfo != null) {
                val msg = sky!!.strategyInfo.msg
                if (!TextUtils.isEmpty(msg)) {
                    code = "1"
                }
            }
            return code
        }
    val stateMsg: String
        get() {
            var msg = ""
            if (sky != null && sky!!.strategyInfo != null) {
                msg = sky!!.strategyInfo.msg
            }
            return msg
        }
    val timeSpent: Long
        get() {
            var timeSpent: Long = 0
            if (sky != null) {
                timeSpent = sky!!.endRequestTime - sky!!.startRequestTime
                LogUtil.d(
                    "TimeSpent_log",
                    "adPosition=" + sky!!.adPositionId + "  startRequestTime=" + sky!!.startRequestTime + "  endRequestTime=" + sky!!.endRequestTime
                )
            }
            return timeSpent
        }

    override fun getEcpm(): Double {
        return try {
            sky?.strategyInfo?.ecpmYuanStr?.toDouble() ?: 0.0
        } catch (e: Exception) {
            0.0
        }
    }
}