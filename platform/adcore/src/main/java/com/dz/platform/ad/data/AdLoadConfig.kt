package com.dz.platform.ad.data

import com.dianzhong.base.eventbus.AdType

data class AdLoadConfig(
    val adType: AdType,
    val adId: String,
    val needPreload: Boolean? = false,
    val preLoadNum: Int? = 0,
    val loadTimeOut: Long?,
    val bgTipText: String? = "",
    val isNeedInterstitialBg: Boolean? = false,
) {
    override fun toString(): String {
        return "AdLoadConfig={adType=$adType,adId=$adId,needPreload=$needPreload,preLoadNum=$preLoadNum,loadTimeOut=$loadTimeOut,bgTipText=$bgTipText,=$isNeedInterstitialBg}"
    }
}
