package com.dz.platform.ad.sky

import com.dianzhong.base.Sky.FeedSky
import com.dianzhong.wall.manager.listener.wall.WallAd

/**
 *@Author: zhanggy
 *@Date: 2023-05-18
 *@Description: 激励墙广告
 *@Version:1.0
 */
class RewardWallAd : Ad() {
    var adSky: WallAd? = null
        set(value) {
            field = value
            setAdValue()
        }

    var isShow = false

    /**
     * 是否参与了竞价
     */
    var hasBidding = false

    /**
     * 广告墙id
     */
    var wallId: String? = ""

    /**
     * 广告墙的广告位id
     * 是一个数组
     */
    var wallSlotId = ""

    private fun setAdValue() {
        adSky?.let { wallAd ->
            wallId = wallAd.wallId  // 广告墙id
            wallSlotId = wallAd.slotId  // 广告位id
//            codeId = wallAd.  // 代码位id
//            preEcpm = wallAd.ecpm  // 墙预估ECPM
        }
    }

    fun isValid(): Boolean {
        return adSky?.isValid == true
    }

    override fun getEcpm(): Double {
        return adSky?.ecpm ?: 0.0
    }
}

class RewardWallItem(private val feedSky: FeedSky?) {

    /**
     * 代码位id
     */
    fun getCodeId(): String {
        return feedSky?.strategyInfo?.chn_slot_id ?: ""
    }

    /**
     * 预估ecpm
     */
    fun getEcpm() : Double {
        return feedSky?.strategyInfo?.run{
            try {
                ecpmYuanStr.toDouble()
            } catch (e: Exception) {
                0.0
            }
        } ?: 0.0
    }

}