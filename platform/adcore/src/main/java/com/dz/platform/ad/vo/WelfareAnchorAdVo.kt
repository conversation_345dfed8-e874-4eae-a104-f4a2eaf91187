package com.dz.platform.ad.vo

import com.dz.business.base.data.bean.BaseBean
import com.dz.platform.ad.vo.basic.AdSharedInfo


data class WelfareAnchorAdVo(
    val adConfExt: WelfareAnchorAdVoConfExt? = null,
) : AdSharedInfo() {
    fun getAdConf(): WelfareAnchorAdVoConfExt? = adConfExt
    fun getAdId(): String = adConfExt?.adId ?: "" // 广告id

    fun getDelayDuration(): Int = adConfExt?.welfarePoint?.delayDuration ?: 1
    fun getShowDuration(): Int = adConfExt?.welfarePoint?.showDuration ?: 10
    fun getAdWidth(): Int = adConfExt?.welfarePoint?.width ?: 180
    fun getAdHeight(): Int = adConfExt?.welfarePoint?.height ?: 138
    fun getAdRetryNum(): Int = adConfExt?.singleTime ?: 0  // 一轮请求失败重试次数
    fun getDayRetryAdNum(): Int = adConfExt?.dayTime ?: 100 // 广告每日总请求次数
    fun getMinWatchTimeForAdSec(): Long =
        (adConfExt?.minWatchTimeForAd ?: 0) * 60  // 出广告所需要的最小观看时间，秒

    //v2
    fun getWelfareShowDuration() = adConfExt?.welfarePoint?.welfareShowDuration ?: 5 // 福利锚点广告展示时长，单位s
    fun getSelfrenderShowDuration() = adConfExt?.welfarePoint?.selfrenderShowDuration ?: 5 // 信息流自渲染广告展示时长，单位s
    fun getWelfareAdNum() = adConfExt?.welfarePoint?.adNum ?: 1 // 福利锚点广告个数
    fun getSelfrenderAdNum() = adConfExt?.welfarePoint?.selfrenderAdNum ?: 0 // 信息流自渲染广告个数
    fun getSelfrenderCloseArea() = adConfExt?.welfarePoint?.selfrenderCloseArea ?: 1 // 信息流关闭按钮热区

    fun hasOnlyCommonAds() = getWelfareAdNum() == 0 && getSelfrenderAdNum() != 0
    fun hasOnlyCsjAds() = getWelfareAdNum() != 0 && getSelfrenderAdNum() == 0
}

data class WelfareAnchorAdVoConfExt(
    val adId: String? = "",       //  广告id, 开屏
    val singleTime: Int?,         //  广告单次轮询次数
    val dayTime: Int?,            //  广告每日轮询次数
    val minWatchTimeForAd: Long?, //  出广告所需要的最小观看时间，分钟
    val intervalChapter: IntervalChapter? = null, // 间隔章节
    val welfarePoint: WelfarePoint? = null,
) : BaseBean()

data class WelfarePoint(
    val delayDuration: Int?, // 剧集播放几秒后广告可出现,单位s
    val showDuration: Int?,  // 广告展示时长,单位s
    val welfareShowDuration: Int?,  // 广告展示时间（福利锚点）广告展示时长,单位s
    val selfrenderShowDuration: Int?,  // 广告展示时间（信息流自渲染）广告展示时长,单位s
    val adNum: Int?,  // 广告个数（福利锚点）
    val selfrenderAdNum: Int?,  // 广告个数（信息流自渲染）
    val selfrenderCloseArea: Int?,  // 信息流关闭按钮热区selfrenderCloseArea：取值范围: 1、2、3，默认1  1 = 关闭按钮区 2 = 外扩剧集区域 3 = 外扩剧集区域+广告区域
    val width: Int?,         // 广告宽
    val height: Int?,        // 广告高
) : BaseBean()