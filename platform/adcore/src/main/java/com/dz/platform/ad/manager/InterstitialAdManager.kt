package com.dz.platform.ad.manager

import android.app.Activity
import com.dianzhong.base.data.bean.sky.RewardCloseParams
import com.dianzhong.core.manager.loader.InterstitialLoader
import com.dz.foundation.base.utils.LogUtil
import com.dz.platform.ad.AdManager
import com.dz.platform.ad.cache.AdCachePool
import com.dz.platform.ad.callback.BaseAdLoadCallback
import com.dz.platform.ad.callback.InterstitialAdLoadCallback
import com.dz.platform.ad.callback.InterstitialAdShowCallback
import com.dz.platform.ad.sky.InterstitialAd
import com.dz.platform.ad.sky.SkyAd

// 有缓存能力
object InterstitialAdManager {

    const val TAG = "InterstitialAdManager"

    private val map: HashMap<String, InterstitialLoader> = hashMapOf()
    private val preloadMap: HashMap<String, InterstitialLoader> = hashMapOf()

    fun cancelPreloadAd(adId: String) {
        if (preloadMap.containsKey(adId)) {
            val adLoader = preloadMap[adId]
            LogUtil.d(RewardAdManager.TAG, "cancelPreloadAd 取消广告加载，广告位：$adId")
            adLoader?.cancelAdsLoading()
            preloadMap.remove(adId)
        } else {
            LogUtil.d(RewardAdManager.TAG, "cancelPreloadAd 失败，广告位：$adId")
        }
    }

    /**
     * 预加载插屏广告
     */
    fun preLoadInterstitialAd(
        activity: Activity,
        adId: String,
        requestId: String?,
        bgTipText: String? = "",
        isNeedInterstitialBg: Boolean? = false,
        blockConfigId: String?,
        bookId: String?,
        chapterId: String?,
        preLoadNum: Int,
        callback: BaseAdLoadCallback<InterstitialAd>?
    ) {
        LogUtil.d(
            TAG,
            "preLoadInterstitialAd 开始加载广告，广告位：$adId bgTipText=$bgTipText isNeedInterstitialBg=$isNeedInterstitialBg"
        )
        val cacheAdNum = AdCachePool.getInterstitialAdCacheSize(activity, adId)
        if (cacheAdNum > preLoadNum) {
            LogUtil.d(TAG, "have catch ad,not need pre load")
            return
        }
        LogUtil.d(TAG, "catchAd 不足,cacheAdNum=$cacheAdNum preLoadNum=$preLoadNum")
        val preload = AdManager.loadInterstitialAd(
            activity = activity,
            adId = adId,
            requestId = requestId,
            blockConfigId = blockConfigId,
            bookId = bookId,
            chapterId = chapterId,
            bgTipText = bgTipText,
            isNeedInterstitialBg = isNeedInterstitialBg,
            timeout = null,
            loadCallback = object : BaseAdLoadCallback<InterstitialAd> {

                override fun onStartLoad() {
                    LogUtil.d(TAG, "preLoad-onStartLoad")
                    callback?.onStartLoad()
                }

                override fun onLoadError(code: Int, msg: String) {
                    LogUtil.d(TAG, "preLoad-onLoadError $code $msg")
                    callback?.onLoadError(code, msg)
                }

                override fun onMaterialStartLoad(sky: SkyAd?) {
                }

                override fun onMaterialStatusChanged(sky: SkyAd?) {
                }

                override fun onLoadSuccess(ad: InterstitialAd) {
                    LogUtil.d(TAG, "preLoad-onLoadSuccess")
                    AdCachePool.putInterstitialAds(activity, adId, ad)
                    callback?.onLoadSuccess(ad)
                }
            })
        preload?.let {
            preloadMap.put(adId, it)
        }
    }

    fun needLoadMoreAds(activity: Activity, adId: String, number: Int): Boolean {
        val size = AdCachePool.getInterstitialAdCacheSize(activity, adId)
        LogUtil.d(TAG, "adId=$adId needPreLoadNum=$number InterstitialAdCacheSize=$size")
        return number - size > 0
    }

    fun hasCachedAd(activity: Activity, adId: String): Boolean {
        val size = AdCachePool.getInterstitialAdCacheSize(activity, adId)
        LogUtil.d(TAG, "adId=$adId hasCachedAd rewardAdCacheSize=$size")
        return size > 0
    }

    /**
     * 获取插屏广告
     */
    fun loadInterstitialAd(
        activity: Activity,
        adId: String,
        requestId: String?,
        bgTipText: String? = "",
        isNeedInterstitialBg: Boolean? = false,
        bookId: String?,
        chapterId: String?,
        blockConfigId: String?,
        timeout: Long?,
        callback: InterstitialAdLoadCallback<InterstitialAd>
    ) {
        LogUtil.d(TAG, "loadInterstitialAd 开始加载广告，广告位：$adId  bgTipText=$bgTipText isNeedInterstitialBg=$isNeedInterstitialBg")
        val ad = AdCachePool.getInterstitialAdFromMap(adId, activity, false)
        if (ad != null) {
            LogUtil.d(TAG, "have cache ad")
            callback.onLoadSuccess(ad, true)
            return
        }
        LogUtil.d(TAG, "no cache ad,need load")
        val adLoader =
            AdManager.loadInterstitialAd(
                activity,
                adId,
                requestId = requestId,
                blockConfigId,
                bgTipText = bgTipText,
                isNeedInterstitialBg = isNeedInterstitialBg,
                bookId = bookId,
                chapterId = chapterId,
                timeout = timeout,
                loadCallback = object : BaseAdLoadCallback<InterstitialAd> {
                    override fun onStartLoad() {
                        LogUtil.d(TAG, "load-onStartLoad")
                        callback.onStartLoad()
                    }

                    override fun onLoadSuccess(ad: InterstitialAd) {
                        LogUtil.d(TAG, "load-onLoadSuccess")
                        AdCachePool.putInterstitialAds(activity, adId, ad)
                        callback.onLoadSuccess(ad, false)
                        map.remove(adId)
                    }

                    override fun onLoadError(code: Int, msg: String) {
                        LogUtil.d(TAG, "load-onLoadError $code $msg")
                        callback.onLoadError(code, msg)
                        map.remove(adId)
                    }

                    override fun onMaterialStartLoad(sky: SkyAd?) {
                        callback.onMaterialStartLoad(sky)
                    }

                    override fun onMaterialStatusChanged(sky: SkyAd?) {
                        callback.onMaterialStatusChanged(sky)
                    }
                })
        adLoader?.let {
            map.put(adId, it)
        }
    }


    /**
     * 显示插屏广告
     */
    fun showInterstitialAd(
        activity: Activity,
        adId: String,
        callback: InterstitialAdShowCallback
    ): Boolean {
        LogUtil.d(TAG, "showInterstitialAd")
        val ad = AdCachePool.getInterstitialAdFromMap(adId, activity, true)
        if (ad != null && ad.isValid(activity)) {
            LogUtil.d(TAG, "have cache ad")
            AdManager.showInterstitialAd(ad, object : InterstitialAdShowCallback {

                override fun onShow(ad: InterstitialAd) {
                    LogUtil.d(TAG, "onShow")
                    callback.onShow(ad)
                }

                override fun onShowError(ad: InterstitialAd, code: Int, msg: String) {
                    LogUtil.d(TAG, "onShowError $code $msg")
                    callback.onShowError(ad, code, msg)
                }

                override fun onClick(ad: InterstitialAd) {
                    LogUtil.d(TAG, "onClick")
                    callback.onClick(ad)
                }

                override fun onReward(ad: InterstitialAd) {
                    LogUtil.d(TAG, "onReward")
                    callback.onReward(ad)
                }

                override fun onClose(ad: InterstitialAd, rewardCloseParams: RewardCloseParams?) {
                    LogUtil.d(TAG, "onClose")
                    callback.onClose(ad, rewardCloseParams)
                }

            })
            return true
        } else {
            return false
        }
    }

    fun cancelAdsLoading(adId: String) {
        if (map.containsKey(adId)) {
            val adLoader = map[adId]
            adLoader?.cancelAdsLoading()
            map.remove(adId)
        }
    }
}