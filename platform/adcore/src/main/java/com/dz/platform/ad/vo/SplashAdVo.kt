package com.dz.platform.ad.vo

import com.dz.business.base.data.bean.BaseBean
import com.dz.platform.ad.vo.basic.AdSharedInfo

data class SplashAdVo(
    val userPushType: String = "", // 本地使用，冷起开屏拉活用户类型
    val adConfExt: SplashAdConfExt? = null,
) : AdSharedInfo() {
    fun getAdId():String = adConfExt?.adId ?: "" // 开屏广告id
    fun getAdIntervalSec():Int = adConfExt?.adIntervalNum ?: 180  // 退后台后，再次进入前台，会展示热启动开屏广告的时间
    fun getDetailAdIntervalSec():Int = adConfExt?.intervalTime ?: -1  // 同上，二级播放页场景。
    fun getMaxWaitAdTime():Int = adConfExt?.maxWaitAdTime ?: 3000 // 开屏后等多久没有广告的话就进入主页。后台目前下发的3秒
    fun getMinWatchTimeForAdSec():Long = (adConfExt?.minWatchTimeForAd ?: 0) * 60 // 出广告所需要的最小观看时间，秒
}

data class SplashAdConfExt(
    val adId: String? = "",          // 广告id, 开屏
    val adIntervalNum: Int?,         // 广告展示间隔, 开屏
    val intervalTime: Int? = null,   // 同上，二级播放页场景
    var maxWaitAdTime: Int?,   // 启动最大等待时长, 开屏
    val minWatchTimeForAd: Long?, // 出广告所需要的最小观看时间，分钟
) : BaseBean()

// dp拉活的广告对象
fun createDpSplashAdVo(adId: String, userPushType: String): SplashAdVo {
    return SplashAdVo(
        userPushType = userPushType,
        SplashAdConfExt(
            adId = adId,
            maxWaitAdTime = 3000,
            adIntervalNum = null,
            minWatchTimeForAd = 0
        )
    )
}

// 冷启动开屏拉活广告信息封装
data class ColdStartPushAdConfig(
    private val pushSlotId: String,   // 拉活广告id，空则不是拉活广告
    private val pushType: String,     // 拉活用户类型：deeplink 或者 push
) : BaseBean() {
    fun getPushSlotId(): String = pushSlotId
    fun isPushAd(): Boolean = pushSlotId.isNotEmpty()
    fun getPushType(): String = if (isPushAd()) pushType else ""
}