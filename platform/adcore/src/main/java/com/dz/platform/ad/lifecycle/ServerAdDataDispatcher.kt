package com.dz.platform.ad.lifecycle

import com.dz.platform.ad.data.BannerDataUtil
import com.dz.platform.ad.data.DrawDataUtil
import com.dz.platform.ad.data.InterstitialDataUtil
import com.dz.platform.ad.data.SplashDataUtil
import com.dz.platform.ad.data.WelfareAnchorAdDataUtil
import com.dz.platform.ad.vo.BannerAdVo
import com.dz.platform.ad.vo.DrawAdVo
import com.dz.platform.ad.vo.MallAdVo
import com.dz.platform.ad.vo.MineBannerAdVo
import com.dz.platform.ad.vo.SplashAdVo
import com.dz.platform.ad.vo.WelfareAnchorAdVo

object ServerAdDataDispatcher {

    // 1103接口返回（初始化接口,开屏广告）
    fun onSplashAdFetched(splashAdVo: SplashAdVo?, hotSplashAdVo: SplashAdVo?) {
        SplashDataUtil.onSplashAdFetched(splashAdVo, hotSplashAdVo)
    }

    // 1150接口返回（通用配置数据,首页draw）
    fun onHomeDrawAdConfigFetched(homeDrawAdVo: DrawAdVo) {
        DrawDataUtil.onHomeDrawAdConfigFetched(homeDrawAdVo)
    }

    // 2150接口返回（通用配置数据,首页draw）
    fun onHomeDrawAdConfigUpdate(homeDrawAdVo: DrawAdVo) {
        DrawDataUtil.onHomeDrawAdConfigFetched(homeDrawAdVo)
    }

    // 1150接口返回（通用配置数据,个人页Banner）
    fun onMineBannerAdConfigFetched(bannerAdVo: MineBannerAdVo?) {
        BannerDataUtil.onMineBannerAdConfigFetched(bannerAdVo)
    }

    // 1150接口返回（通用配置数据,个人页我的商城）
    fun onMineMallAdConfigFetched(mallAdVo: MallAdVo?) {
        InterstitialDataUtil.onMineMallAdConfigFetched(mallAdVo)
    }

    // 1131接口返回（二级页draw）每次进入二级页只会调用一次
    fun onDetailDrawAdConfigFetched(detailDrawAdVo: DrawAdVo) {
        DrawDataUtil.onDetailDrawAdConfigFetched(detailDrawAdVo)
    }

    // 1131接口返回（二级页福利锚点）每次进入二级页只会调用一次
    fun onDetailWelfareAnchorAdConfigFetched(welfareAnchorAdVo: WelfareAnchorAdVo) {
        WelfareAnchorAdDataUtil.onDetailWelfareAnchorAdConfigFetched(welfareAnchorAdVo)
    }

    // 2150接口返回（二级页draw）
    fun onDetailDrawAdConfigUpdate(detailDrawAdVo: DrawAdVo) {
        DrawDataUtil.onDetailDrawAdConfigFetched(detailDrawAdVo, false)
    }

    // 1131接口返回（底部banner）
    fun onBannerAdFetched(bannerAdVo: BannerAdVo?, onHeightChanged: ((Int) -> Unit)? = null) {
        BannerDataUtil.onBannerAdFetched(bannerAdVo, onHeightChanged)
    }
}