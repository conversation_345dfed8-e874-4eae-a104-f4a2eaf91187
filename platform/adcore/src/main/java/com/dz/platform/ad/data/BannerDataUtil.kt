package com.dz.platform.ad.data

import com.dz.foundation.base.utils.LogUtil
import com.dz.platform.ad.vo.BannerAdVo
import com.dz.platform.ad.vo.MineBannerAdVo

object BannerDataUtil {

    fun onBannerAdFetched(bannerAdVo: BannerAdVo?, onHeightChanged: ((Int) -> Unit)? = null) {
        val bannerHeight = 60 + (bannerAdVo?.getExtraBannerHeight() ?: 0)
        if (BannerAdKV.bannerHeight != bannerHeight) {
            BannerAdKV.bannerHeight = bannerHeight
            onHeightChanged?.invoke(bannerHeight)
        }
        bannerAdVo?.getMinWatchTimeForAdSec()?.let {
            BannerAdKV.bannerAdMinWatchTimeSec = it
        }
    }

    fun onMineBannerAdConfigFetched(bannerAdVo: MineBannerAdVo?) {
        BannerAdKV.mineBannerAdBlockConfigId = bannerAdVo?.blockConfigId ?: ""
        BannerAdKV.mineBannerAdId = bannerAdVo?.getAdId() ?: ""
        BannerAdKV.mineBannerAdCloseAdIntervalNum = bannerAdVo?.getCloseAdIntervalNum() ?: 0
        BannerAdKV.mineBannerObtainAdFailInterval = bannerAdVo?.getObtainAdFailInterval() ?: 0
        BannerAdKV.mineBannerAdFailRetry = bannerAdVo?.getAdFailRetry() ?: 0
        BannerAdKV.mineBannerCanFeedbackAd = bannerAdVo?.canFeedbackAd() ?: false
        bannerAdVo?.getFeedbackAdCfg()?.let {
            BannerAdKV.mineBannerFeedbackCfg = bannerAdVo.getFeedbackAdCfgStr()?:""
        }
        bannerAdVo?.userTacticsVo?.let { userTacticsVo ->
            BannerAdKV.mineBannerAdTacticsId = userTacticsVo.tacticsId ?: 0
            BannerAdKV.mineBannerAdTacticsName = userTacticsVo.tacticsName ?: ""
            BannerAdKV.mineBannerAdSourceId = userTacticsVo.sourceId ?: 0
            BannerAdKV.mineBannerAdSourceName = userTacticsVo.sourceName ?: ""
            BannerAdKV.mineBannerAdShuntID = userTacticsVo.shuntID ?: 0
            BannerAdKV.mineBannerAdShuntName = userTacticsVo.shuntName ?: ""
        }

    }

    /**
     * banner Ad 不满足最小观看时长
     */
    fun minWatchTimeNotMetForBannerAd(): Boolean {
        return (AdKV.historyWatchedDurationSec < BannerAdKV.bannerAdMinWatchTimeSec).also {
            if (it) LogUtil.i("detail_banner_ad_tag", "banner不满足最小观看时长 已观看:${AdKV.historyWatchedDurationSec} 门槛:${BannerAdKV.bannerAdMinWatchTimeSec}")
        }
//        return (AdKV.historyWatchedDurationSec < 10).also { // todo comment out test code
//            if (it) LogUtil.i("detail_banner_ad_tag", "banner不满足最小观看时长 已观看:${AdKV.historyWatchedDurationSec} 门槛:${BannerAdKV.bannerAdMinWatchTimeSec} ")
//        }
    }
}