package com.dz.platform.ad.data

import com.dz.foundation.base.utils.LogUtil
import com.dz.platform.ad.WelfareAnchorIntervalUtil
import com.dz.platform.ad.vo.WelfareAnchorAdVo

object WelfareAnchorAdDataUtil {

    var isDetailWelfareFromColdStart = false

    /**
     * @param reopen:是否是重新进入二级页
     * */
    fun onDetailWelfareAnchorAdConfigFetched(adVo: WelfareAnchorAdVo, reopen: Boolean? = true) {
        updateDetailDrawAdData(adVo)
        if (reopen == true) {
            // 每次进入二级页递减
            decreaseDrawStartIndex()
        }
    }

    private fun updateDetailDrawAdData(adVo: WelfareAnchorAdVo) {
        val adConfExt = adVo.getAdConf()
        adConfExt ?: return

        val intervalChapter = adConfExt.intervalChapter
        if (intervalChapter != null) {
            FLMDAdKV.detailFLMDAdDefaultStartIndex =
                intervalChapter.startIndex.coerceAtLeast(WelfareAnchorIntervalUtil.MIN_START_INDEX)
            FLMDAdKV.detailFLMDAdDecrement = intervalChapter.decrement
            FLMDAdKV.detailFLMDAdTermination = intervalChapter.termination
            WelfareAnchorIntervalUtil.setAdIntervals(intervalChapter.adIntervals)
        }
        FLMDAdKV.detailFLMDAdMinWatchTimeSec = adVo.getMinWatchTimeForAdSec()
    }

    private fun decreaseDrawStartIndex() {
        if (isDetailWelfareFromColdStart) {
            isDetailWelfareFromColdStart = false
            LogUtil.d(
                "detail_welfare_ad_tag",
                "decreaseStartIndex 冷起后重置 startIndex: ${FLMDAdKV.detailFLMDAdStartIndex}"
            )
            //福利锚点部分
            FLMDAdKV.detailFLMDAdStartIndex = FLMDAdKV.detailFLMDAdDefaultStartIndex
        } else {
            //福利锚点部分
            val oldWelfare = FLMDAdKV.detailFLMDAdStartIndex
            val decWelfare = FLMDAdKV.detailFLMDAdDecrement
            val terWelfare = FLMDAdKV.detailFLMDAdTermination
            FLMDAdKV.detailFLMDAdStartIndex = (oldWelfare - decWelfare).coerceAtLeast(terWelfare)
            LogUtil.d(
                "detail_welfare_ad_tag",
                "decreaseStartIndex old:$oldWelfare, dec:$decWelfare ter:$terWelfare newStartIndex:${FLMDAdKV.detailFLMDAdStartIndex}"
            )
        }
    }

    /**
     * 二级页福利锚点 Ad 不满足最小观看时长
     */
    fun minWatchTimeNotMetForWelfareAd(): Boolean {
        return (AdKV.historyWatchedDurationSec < FLMDAdKV.detailFLMDAdMinWatchTimeSec).also {
            LogUtil.d(
                "detail_welfare_ad_tag",
                "二级页福利锚点 观看时长 已观看:${AdKV.historyWatchedDurationSec} 门槛:${FLMDAdKV.detailFLMDAdMinWatchTimeSec}"
            )
        }
    }
}