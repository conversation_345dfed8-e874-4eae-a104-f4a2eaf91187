package com.dz.platform.ad.callback

import com.dz.platform.ad.sky.SkyAd
import com.dz.platform.ad.sky.SplashAd

/**
 * Created by FeiHK
 * Date: on 2023/2/2.
 * Desc:
 */
interface SplashAdCallback {

    fun onStartLoad(ad: SplashAd)

    fun onADLoad(ad: SplashAd)

    fun onADFail(ad: SplashAd, code: String?, message: String?)

    fun onADShow(ad: SplashAd)

    fun onADClick(ad: SplashAd)

    fun onADClose(ad: SplashAd)

    fun loadStart(sky: SkyAd)

    fun loadStatus(sky: SkyAd)

    fun onSeriesStartLoad()

    fun onSeriesEndLoad()
}