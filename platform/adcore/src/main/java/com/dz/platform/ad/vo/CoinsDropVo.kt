package com.dz.platform.ad.vo

import com.dz.business.base.data.bean.BaseBean

data class CoinsDropVo(
    val dropSwitch: Int = 0,     // 总开关 1-开，0-关（默认）
    val dropTrigTime: Int = 0,   // 曝光多久后开始动画；>=0，默认0s
    val dropWatchTime: Int = -1, // 观看xx秒下发奖励； >=1，默认不配置（为-1,代表关闭）
    val dropClkConfig: Int = 0,  // 点击下发奖励；1-开，0-关（默认）
    val dropToastContent: String? = null,      // 文案toast展示倒计时 dropToastContent，字符串类型，默认不配置（下发空字符串）；如果配置的字符串里有{time},{coin}，需要替换为观看时长和下发金币数
    val dropProgressContent: String? = null,   // 进度条文案 dropProgressContent，字符串类型，默认不配置（下发空字符串）；如果配置的字符串里有{time},{coin}，需要替换为观看时长和下发金币数
    val dropVoice: Int = 1,                    // 领金币声音 dropVoice，取值范围：1-开（默认），0-关;
    val dropIntervalConfigs: List<DropIntervalConfigVo>? = null // 每个对象包含播放时长开始、播放时长结束、ecpm开始、ecpm结束、下次首次展示、展示间隔
) : BaseBean() {

    fun disable(): Boolean {
        return dropSwitch == 0 || (dropWatchTime < 0 && dropClkConfig == 0)
    }

    fun onlyClickReward(): Boolean {
        return enableClickReward() && !enableWatchReward()
    }

    fun enableClickReward(): Boolean {
        return dropSwitch == 1 && dropClkConfig == 1
    }

    fun enableWatchReward(): Boolean {
        return dropSwitch == 1 && dropWatchTime >= 1
    }
}

data class DropIntervalConfigVo(
    val playDurationStart: Int = 0, // 播放时长开始，单位：秒
    val playDurationEnd: Int = 0,   // 播放时长结束，单位：秒
    val ecpmStart: Int = 0,         // ecpm开始，单位：分
    val ecpmEnd: Int = 0,           // ecpm结束，单位：分
    val dropFirstOrder: Int = 1,    // 下次首次展示，取值范围：>=-1，默认为1；配置成-1表示不展示金币掉落，由客户端改成Integer.MAX_VALUE
    val dropShowInterval: String? = "0" // 展示间隔，可配置多个，用,隔开
) : BaseBean()


// 金币掉落任务数据
data class DrawTaskVo(
    val count: Int = 0,           // 剩余次数，达到最大次数下发0
    val awardNum: Int? = null,    // 奖励金额，如：47
    val id: Int? = null,      // 开关id  573   （据说没有任务的概念）
    val type: Int? = null     // 开关类型：236  （据说没有任务的概念）
) : BaseBean() {
}

val drawTaskVoTest = DrawTaskVo(
    count = 10,
    awardNum = 47,
    id = 1001,
    type = 101
)

val drawDropVoTest = CoinsDropVo(
    dropSwitch = 1,
    dropTrigTime = 1,
    dropWatchTime = 10,
    dropClkConfig = 1,
    dropToastContent = "观看{time}秒，领取{coin}金币",
    dropProgressContent = "观看{time}秒，领取{coin}金币",
    dropVoice = 1,
    dropIntervalConfigs = listOf(
        DropIntervalConfigVo(
            playDurationStart = 0,
            playDurationEnd = 300,
            ecpmStart = 10,
            ecpmEnd = 20,
            dropFirstOrder = 1,
            dropShowInterval = "2,3,4"
        )
    )
)