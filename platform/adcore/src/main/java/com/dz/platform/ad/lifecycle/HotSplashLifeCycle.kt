package com.dz.platform.ad.lifecycle

import com.dz.foundation.base.utils.LogUtil
import com.dz.platform.ad.data.DrawDataUtil.isDetailFromColdStart
import com.dz.platform.ad.data.WelfareAnchorAdDataUtil.isDetailWelfareFromColdStart

class HotSplashLifeCycle {

    fun onWarmToCold() {
        LogUtil.i("interval_chapter_detail", "onWarmToCold")
        isDetailFromColdStart = true
        isDetailWelfareFromColdStart = true
    }
}