package com.dz.platform.ad.callback

import com.dz.platform.ad.sky.RewardAd
import com.dz.platform.ad.sky.SkyAd

interface RewardAdCallback {

    fun onStartLoad(rewardAd: RewardAd?)

    fun onAdLoadSuccess(rewardAd: RewardAd?)

    fun onFail(rewardAd: RewardAd, message: String)

    fun onClick(rewardAd: RewardAd?)

    fun onShow(rewardAd: RewardAd?)

    fun onClose(rewardAd: RewardAd?)

    fun onReward()
    fun onVideoStart()

    fun onVideoComplete()

    fun loadStart(sky: SkyAd?)

    fun loadStatus(sky: SkyAd?)

}