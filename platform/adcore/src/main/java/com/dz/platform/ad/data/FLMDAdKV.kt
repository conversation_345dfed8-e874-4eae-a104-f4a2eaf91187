package com.dz.platform.ad.data

import com.dz.foundation.base.data.kv.KVData

object FLMDAdKV : KVData {

    override fun getGroupName() = "com.dianzhong.ad.flmd"

    private const val DEFAULT_FLMD_START_INDEX = 15 // 二级页福利锚点默认的首出章节
    private const val DEFAULT_FLMD_INTERVAL = "3"   // 二级页福利锚点默认的间隔章节

    // 用户当天的上一次的福利锚点的ecpm
    var todayLastFLMDAdEcpmCent by todayDelegate("todayLastFLMDAdEcpmCent", 0) //用户当天的上一次的福利锚点的ecpm

    // 用户当天福利锚点广告id请求次数
    var todayFLMDAdRequestCount by todayDelegate("todayFLMDAdRequestCount", 0) //用户当天福利锚点广告id请求次数

    /** 福利锚点 START */
    /** 后台下发的二级draw相关的数据持久化 */
    var detailFLMDAdDefaultStartIndex by delegate("detailFLMDAdDefaultStartIndex", DEFAULT_FLMD_START_INDEX) // 二级页福利锚点默认的首出章节
    var detailFLMDAdTermination by delegate("detailFLMDAdTermination", DEFAULT_FLMD_START_INDEX) //  二级页福利锚点首出章节终止值
    var detailFLMDAdDecrement by delegate("detailFLMDAdDecrement", 0)    // 二级页福利锚点首出章节递减值，默认值是0
    var detailFLMDAdMinWatchTimeSec by delegate("detailFLMDAdMinWatchTime", 0L)    // 二级页福利锚点出广告所需要的最小观看时间，秒

    var detailFLMDAdStartIndex by delegate("detailFLMDAdStartIndex", DEFAULT_FLMD_START_INDEX)  // 二级页福利锚点首出章节, 每次进入二级页递减；递减到detailFLMDAdTermination时，不再递减
    var detailFLMDAdLastInterval by delegate("detailFLMDAdLastInterval", DEFAULT_FLMD_INTERVAL) //二级页福利锚点上次使用的间隔章节,"3"也可能为："2,2,3"
    var detailFLMDAdLastIntervalTimeSec by delegate("detailFLMDAdLastIntervalTimeSec", 0L) //二级页福利锚点上次使用的间隔章节, 所对应的间隔时长


    /** 福利锚点 END */

}


