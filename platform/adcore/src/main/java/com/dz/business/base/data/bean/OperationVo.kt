package com.dz.business.base.data.bean

import com.dz.platform.ad.vo.GuildPopConf
import com.dz.platform.ad.vo.basic.AdSharedInfo

/**
 * @Author: guyh
 * @Date: 2023/2/21 20:22
 * @Description:
 * @Version:1.0
 */
data class OperationVo(
    /**
     * 广告ID
     */
    val adId: String = "",
    /**
     * 最大展示次数
     */
    val maxShowNum: Int = 0,

    /**
     * 单日请求错误最大轮循次数
     */
    var dayTime: Int? = 0,
    /**
     * 单次请求错误最大轮循次数
     */
    val singleTime: Int? = 0,
    /**
     * 二级播放页广告强制观看时长 单位：秒
     */
    val viewTime: Int? = 0,
    /**
     * 二级播放页广告是否展示充值入口  1 展示    0不展示
     */
    val rechargeSwitch: Int? = 0,
    /**
     * 去除广告方式数组
     */
    val removeAdArray: MutableList<RemoveAdWayVo>? = null,

    /**
     * 支持 横屏的剧，在横屏下沉浸式广告的广告id
     */
    var horizontalAdId: String? = null,

    /**
     * 广告播放完自动跳过广告位开关
     * 沉浸式 1自动跳过 0不跳过
     */
    val autoJumpSwitch: Int? = null
) : AdSharedInfo() {

    fun isShowRechargeSwitch(): Boolean {
        return rechargeSwitch == 1
    }

    //是否自动跳过广告
    fun isAutoJumpAd(): Boolean {
        return autoJumpSwitch == 1
    }
}

data class RemoveAdWayVo(
    val btnName: String? = null,//按钮名称
    val btnRouteUrl: String? = null,//按钮路由地址
    val type: String? = null,//类型  广告/ad、充值/recharge
    val extendParam: ExtendParam? = null,//扩展参数
    val adConfExt: RemoveAdConfExt? = null,//扩展参数
    /**
     * 运营位ID
     */
    var operationId: Int? = null
) : BaseBean() {

    var adScene: String? = null // 广告场景
    var rewardScene: String = REWARD_SCENE_NORMAL // 1-普通场景  2-再看场景

    fun winTitleDoc() = extendParam?.winTitleDoc     // 看完激励视频提示文案
    fun hotWords() = extendParam?.hotWords           // 高亮显示部分
    fun maxLodTime() = extendParam?.maxLodTime       // loading时间,单位S
    fun incentiveAdId() = adConfExt?.adId // 激励视频广告位id
    fun exemptTime() = extendParam?.exemptTime       // 免广告时间 单位分钟
    fun canPreLoad(): Boolean = adConfExt?.preLoadConfig == REWARD_CAN_PRELOAD  // 是否开启预加载
    fun isRewardAd(): Boolean = adConfExt?.adType == AT_REWARD  // 是否是激励解锁
    fun adId(): String? = adConfExt?.adId  // 免广激励id
    fun blockConfigId(): String? = extendParam?.blockConfigId
    fun getRewardGuildCfg(): GuildPopConf? = adConfExt?.getRewardGuideCfg().takeIf { isAd() }
    fun getPreloadNum(): Int {
        return if (canPreLoad() && adConfExt?.preLoadNum == 0) {
            1
        } else {
            adConfExt?.preLoadNum ?: 1
        }
    }

    //是否是广告类型
    fun isAd(): Boolean {
        return type?.equals("ad") == true
    }

    //是否是充值类型
    fun isRecharge(): Boolean {
        return type?.equals("recharge") == true
    }
}

data class ExtendParam(
    val blockConfigId: String? = null, // 广告位配置ID。代表当前用户命中的是哪个配置，格式为：配置项ID-配置id ig."001-56,002-76"，多个用英文,连接
    val winTitleDoc: String? = null,
    val hotWords: String? = null,
    val maxLodTime: String? = null,
    val incentiveAdId: String? = null,
    val exemptTime: String? = null,
    val multiBtnConf: List<MultiBtnConf>? = null,
) : BaseBean()

/**
 * 去广告配置(免广配置) adType：或者vip, 或则激励视频
 */
data class RemoveAdConfExt(
    val adId: String? = null,
    val preLoadConfig: Int? = REWARD_NOT_CAN_PRELOAD, //0-关闭 1-开启
    val adType: Int? = 0, //广告类型
    val preLoadNum: Int? = 1, //缓存个数， 默认是1
    private val bubbleGuideCfg: GuildPopConf? = null,  // 外层type?.equals("ad") 即是激励免广类型才会有。免广引导气泡配置
) : BaseBean() {
    fun getRewardGuideCfg(): GuildPopConf? = bubbleGuideCfg
//    fun getRewardGuideCfg(): GuildPopConf = mockRewardGuidePop() // todo comment test code
}

data class MultiBtnConf(
    val btnName: String? = null,        // "免20分钟广告"
) : BaseBean()

const val REWARD_SCENE_NORMAL = "1"     // 普通场景
const val REWARD_SCENE_LOOK_AGAIN = "2" // 再看场景

//----------------preLoadConfig----------------------
const val REWARD_NOT_CAN_PRELOAD = 0 // 不可以预加载
const val REWARD_CAN_PRELOAD = 1     // 可以预加载
//---------------------------------------------------

//--------------------adType--------------------------
const val AT_NATIVE_FEEDS = 1;        /* 信息流广告位 */
const val AT_BANNER = 2;              /* 横幅广告位 */
const val AT_SPLASH_INTERSTITIAL = 3; /* 开屏广告位 */
const val AT_INTERSTITIAL = 4;        /* 插屏广告位 */
const val AT_REWARD = 5;              /* 激励视频广告位 */
const val AT_TASK_WALL = 6;           /* 任务墙广告 */
const val AT_DRAW = 7;                /* draw视频流 */
const val AT_PATCH = 8;               /* 贴片广告 */
const val AT_SYS_SPLASH_INTERSTITIAL = 20;    /* 系统开屏 */
//----------------------------------------------------