package com.dz.platform.login.wechat

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import com.dz.foundation.base.utils.LogUtil
import com.dz.platform.pay.paycore.PayCoreME
import com.dz.platform.pay.paycore.bean.WxPayOnRespBean
import com.tencent.mm.opensdk.constants.ConstantsAPI
import com.tencent.mm.opensdk.modelbase.BaseReq
import com.tencent.mm.opensdk.modelbase.BaseResp
import com.tencent.mm.opensdk.modelmsg.SendAuth
import com.tencent.mm.opensdk.modelmsg.ShowMessageFromWX
import com.tencent.mm.opensdk.openapi.IWXAPI
import com.tencent.mm.opensdk.openapi.IWXAPIEventHandler
import com.tencent.mm.opensdk.openapi.WXAPIFactory

/**
 * 微信分享回调基础类
 */
abstract class BaseWXEnTryActivity : AppCompatActivity(), IWXAPIEventHandler {

    companion object {
        const val TAG = "wechat"
        var WECHAT_APP_ID = ""
    }

    private lateinit var api: IWXAPI

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        api = WXAPIFactory.createWXAPI(applicationContext, WECHAT_APP_ID, true)
        api.registerApp(WECHAT_APP_ID)

        //注意：
        //第三方开发者如果使用透明界面来实现WXEntryActivity，需要判断handleIntent的返回值，
        // 如果返回值为false，则说明入参不合法未被SDK处理，应finish当前透明界面，
        // 避免外部通过传递非法参数的Intent导致停留在透明界面，引起用户的疑惑
        try {
            val flag = api.handleIntent(intent, this)
            if (!flag) {
                finish()
            }
        } catch (e: Exception) {
            e.printStackTrace()
            finish()
        }
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        setIntent(intent)

        //注意：
        //第三方开发者如果使用透明界面来实现WXEntryActivity，需要判断handleIntent的返回值，如果返回值为false，则说明入参不合法未被SDK处理，应finish当前透明界面，避免外部通过传递非法参数的Intent导致停留在透明界面，引起用户的疑惑
        try {
            val flag = api.handleIntent(getIntent(), this)
            if (!flag) {
                finish()
            }
        } catch (e: Exception) {
            e.printStackTrace()
            finish()
        }
    }

    override fun onReq(baseReq: BaseReq) {
        //获取开放标签传递的extinfo数据逻辑
        if (baseReq.type == ConstantsAPI.COMMAND_SHOWMESSAGE_FROM_WX && baseReq is ShowMessageFromWX.Req) {
            val extInfo = baseReq.message.messageExt
            LogUtil.d("message_from_wx", "Wx_extInfo:$extInfo")
            val intent = Intent()
            intent.action = Intent.ACTION_VIEW
            intent.data = Uri.parse(extInfo)
            intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_SINGLE_TOP
            startActivity(intent)
            finish()
        }
    }
    override fun onResp(baseResp: BaseResp) {
        LogUtil.d(
            WechatLoginHelper.TAG,
            "Wx_errorCode:" + baseResp.errCode + "___errorStr:" + baseResp.errStr
        )
        if (baseResp.type == ConstantsAPI.COMMAND_PAY_BY_WX) {
            wxPayResp(baseResp)
        }

        when (baseResp.errCode) {
            BaseResp.ErrCode.ERR_OK -> {
                // 登录/分享成功
                if (baseResp.type == ConstantsAPI.COMMAND_SENDAUTH) {  // 微信登录认证
                    WechatLoginHelper.loginListener?.get()
                        ?.onWechatCodeReceived(true, (baseResp as SendAuth.Resp).code, "获取微信code成功")
                } else if (baseResp.type == ConstantsAPI.COMMAND_SENDMESSAGE_TO_WX) {  // 向微信分享
//                    wxLoginEventMessage.setShareType(true);
                }
            }
            else -> {
                WechatLoginHelper.loginListener?.get()
                    ?.onWechatCodeReceived(false, baseResp.errCode.toString(), baseResp.errStr?:"登录失败")
            }
        }
        finish()
    }

    private fun wxPayResp(baseResp: BaseResp) {
        baseResp.run {
            LogUtil.i("Recharge", "----onResp微信SDK支付接收返回结果: $errCode errMsg $errStr")
            PayCoreME.get().onWxOnResp().post(
                WxPayOnRespBean(
                    baseResp.type == ConstantsAPI.COMMAND_PAY_BY_WX,
                    errCode,
                    errStr,
                    transaction,
                    openId
                )
            )
        }
    }
}