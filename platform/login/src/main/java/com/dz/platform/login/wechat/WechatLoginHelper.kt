package com.dz.platform.login.wechat

import com.dz.foundation.base.module.AppModule
import com.dz.foundation.base.utils.LogUtil
import com.tencent.mm.opensdk.modelmsg.SendAuth
import com.tencent.mm.opensdk.openapi.IWXAPI
import com.tencent.mm.opensdk.openapi.WXAPIFactory
import java.lang.ref.WeakReference

/**
 *@Author: zhanggy
 *@Date: 2022-10-21
 *@Description: 微信登录
 * 第一步：请求 CODE：
 * 第二步：通过 code 获取 access_token：在BaseWXEnTryActivity中会拿到微信回调的code
 * 第三步：通过access_token调用咱们的业务接口，获取用户信息
 *@Version:1.0
 */
object WechatLoginHelper {

    const val TAG = "login_wechat"
//登录成功后回调，为什么不采用总线? TODO@GQ
    var loginListener :WeakReference<LoginListener>? = null

    fun login(listener: LoginListener?) {
        try {
            loginListener = WeakReference(listener)
            val iwxapi = WXAPIFactory.createWXAPI(AppModule.getApplication(), BaseWXEnTryActivity.WECHAT_APP_ID)
            if (!iwxapi.isWXAppInstalled || !isWXAppSupportAPI(iwxapi)) {
                listener?.onWechatCodeReceived(false, "", "您暂未安装微信")
                return
            }
            iwxapi.registerApp(BaseWXEnTryActivity.WECHAT_APP_ID)
            val req = SendAuth.Req()
            req.scope = "snsapi_userinfo"
            req.state = "wechat_sdk_demo_test"
            val result = iwxapi.sendReq(req)
            LogUtil.d(TAG, "微信登录，发送code到微信，结果：$result")
        } catch (ex: Exception) {
            listener?.onWechatCodeReceived(false, "", "请求发起异常")
        }
    }

    //这个需要验证，微信升级后没有这个方法了，自己看的以前源码改的
    private fun isWXAppSupportAPI(api: IWXAPI): Boolean {
        return api.wxAppSupportAPI >= 620823552
    }

    interface LoginListener {
        fun onWechatCodeReceived(suc: Boolean, code: String, msg: String)
    }

}