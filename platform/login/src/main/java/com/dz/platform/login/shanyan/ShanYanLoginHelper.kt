package com.dz.platform.login.shanyan

import com.chuanglan.shanyan_sdk.OneKeyLoginManager
import com.chuanglan.shanyan_sdk.listener.ActionListener
import com.chuanglan.shanyan_sdk.listener.OneKeyLoginListener
import com.chuanglan.shanyan_sdk.listener.OpenLoginAuthListener
import com.chuanglan.shanyan_sdk.tool.ShanYanUIConfig
import com.dz.foundation.base.module.AppModule
import com.dz.foundation.base.utils.LogUtil

/**
 *@Author: zhanggy
 *@Date: 2022-10-21
 *@Description: 闪验一键登录
 * 大致流程：
 * 1. 执行初始化
 * 2. 有条件的话，在拉起授权页前2-3秒，执行预取号。这样可以缩短拉起授权页的时间。
 * 3. 拉起授权页。就是最终的一键登录页面。
 *@Version:1.0
 */
object ShanYanLoginHelper {

    const val TAG = "login_shanyan"

    var callBack: CallBack? = null

    fun init(appId: String) {
        // 闪验一键登录
        OneKeyLoginManager.getInstance().init(AppModule.getApplication(), appId) { code: Int, result: String ->
            LogUtil.d(TAG, "初始化结果，code:$code，结果:$result")
        }
    }

    fun prepare() {
        OneKeyLoginManager.getInstance().getPhoneInfo( ){ code: Int, result: String ->
            LogUtil.d(TAG, "预取号结果，code:$code，结果:$result")
        }
    }

    /**
     * 拉起授权页
     */
    fun open(config: ShanYanUIConfig?) {
        // 页面UI配置
        config?.let {
            OneKeyLoginManager.getInstance().setAuthThemeConfig(it, null)
        }
        // 拉起授权页
        OneKeyLoginManager.getInstance().openLoginAuth(false,
            { code, result ->
                //	code为1000：授权页成功拉起 其他：失败
                LogUtil.d(TAG, "拉起授权页结果，code:$code，结果:$result")
                callBack?.getOpenLoginAuthStatus(code, result)
            }) { code, result ->
                // code为1000：成功 其他：失败 (包含点击返回键 code==1011)   当外层code为1000时，result的返回为 {"token": ""}  置换手机号接口所需的token。每个token只能使用一次，一次有效。
                LogUtil.d(TAG, "一键登录状态，code:$code，结果:$result")
                callBack?.getOneKeyLoginStatus(code, result)
        }
    }

    //销毁授权页
    fun finishAuthActivity() {
        callBack = null
        OneKeyLoginManager.getInstance().removeAllListener()
        OneKeyLoginManager.getInstance().unregisterOnClickPrivacyListener()
        OneKeyLoginManager.getInstance().finishAuthActivity()
    }

    fun setLoadingVisibility(visibility: Boolean) {
        //获取token失败，可将loading隐藏，再次点击一键登录重新获取（最多获取4次，4次之后按钮默认会置灰不可点击）
        OneKeyLoginManager.getInstance().setLoadingVisibility(visibility)
    }

    //授权页点击事件监听
    fun setActionListener(actionListener: CallBack): ShanYanLoginHelper {
        callBack = actionListener
        OneKeyLoginManager.getInstance().setActionListener { type, code, message ->
            /**
             * @param type  type=1 ，隐私协议点击事件
             * type=2 ，checkbox点击事件
             * type=3 ，一键登录按钮点击事件
             * @param code  type=1 ，隐私协议点击事件，code分为0,1,2,3（协议页序号）
             * type=2 ，checkbox点击事件，code分为0,1（0为未选中，1为选中）
             * type=3 ，一键登录点击事件，code分为0,1（0为协议未勾选时，1为协议勾选时）
             * @param message   点击事件的详细信息
             */
            callBack?.ActionListner(type, code, message)
        }
        return this
    }

    //清空SDK监听回调，防止内存泄漏
    fun removeAllListener() {
        OneKeyLoginManager.getInstance().removeAllListener()
    }

    interface CallBack: ActionListener, OpenLoginAuthListener, OneKeyLoginListener {

    }
}