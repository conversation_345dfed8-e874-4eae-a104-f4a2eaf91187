package com.dz.platform.pay.aliwap

import android.app.Activity
import android.content.Intent
import com.dz.platform.pay.aliwap.ui.ALIWapPayActivity
import com.dz.platform.pay.base.PayCallback
import com.dz.platform.pay.base.PayWayType
import com.dz.platform.pay.base.data.ALIWapOrderInfo
import com.dz.platform.pay.base.data.PayOrderInfo
import com.dz.platform.pay.base.service.ALIWapPayService

/**
 * <AUTHOR>
 * @description:支付宝WAP支付
 * @date :2022/10/26 15:28
 */
class ALIWapPayMSImpl : ALIWapPayService {
    override fun doPay(context: Activity?, orderInfo: PayOrderInfo, callback: PayCallback?) {
        val mOrderInfo = orderInfo as ALIWapOrderInfo
        val intent = Intent(context, ALIWapPayActivity::class.java)
        intent.putExtra("link", mOrderInfo.dpAction)
        context!!.startActivity(intent)
    }

    override fun isAvailable(): Boolean {
        return true
    }

    override fun isWapPay(): Boolean {
        return true
    }

    override fun platform(): String = PayWayType.ALIPAY_WEB_PAY
}