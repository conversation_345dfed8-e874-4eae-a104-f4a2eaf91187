package com.dz.platform.pay.aliwap.ui

import android.app.Activity
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.net.Uri
import android.opengl.Visibility
import android.os.Build
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.webkit.*
import android.widget.ImageView
import android.widget.LinearLayout
import androidx.appcompat.app.AppCompatActivity
import com.dz.foundation.base.manager.task.Task
import com.dz.foundation.base.manager.task.TaskManager
import com.dz.foundation.base.utils.DeviceInfoUtil
import com.dz.foundation.base.utils.LogUtil
import com.dz.platform.pay.aliwap.R
import com.gyf.immersionbar.BarHide
import com.gyf.immersionbar.ImmersionBar

/**
 * <AUTHOR>
 * @description:
 * @date :2022/10/26 16:17
 */
class ALIWapPayActivity : AppCompatActivity() {

    private var webView: WebView? = null
    private var webViewParent: ViewGroup? = null
    private var mLlCloseTitle: LinearLayout? = null
    private var ivClose: ImageView? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        init()
    }

    private fun init() {
        setStatusBarColor()
        loadView()
        initView()
        initData()
    }

    private fun loadView() {
        setContentView(R.layout.aliwap_pay_activity)
    }

    private fun setStatusBarColor() {
        ImmersionBar.with(this)
            .transparentStatusBar()
            .transparentNavigationBar()
            .statusBarDarkFont(!DeviceInfoUtil.isDarkTheme(this))
            .init()
    }


    private fun initView() {
        webView = WebView(this)
        webViewParent = findViewById(R.id.webview_parent)
        mLlCloseTitle = findViewById(R.id.ll_close_title)
        ivClose = findViewById(R.id.iv_close)

    }


    private fun initData() {
        val context: Activity = this
        initViewData(context)
        val intent = intent
        val link = intent.getStringExtra("link")
        LogUtil.d("king_pay-aliwappay", "initData:link：$link")
        webView!!.loadDataWithBaseURL(null, createWebHtml(link), "text/html", "UTF-8", null)
    }

    private fun initViewData(context: Activity) {
        webView?.run {
            setHorizontalScrollbarOverlay(false)
            isHorizontalScrollBarEnabled = false
            isVerticalScrollBarEnabled = false
            settings.run {
                javaScriptEnabled = true
                setSupportZoom(false)
                cacheMode = WebSettings.LOAD_DEFAULT
                allowFileAccess = true
                useWideViewPort = true
                databaseEnabled = true
                domStorageEnabled = true
//                setAppCacheEnabled(true)
                mixedContentMode = WebSettings.MIXED_CONTENT_ALWAYS_ALLOW
            }
            isFocusable = true
            scrollBarStyle = WebView.SCROLLBARS_INSIDE_OVERLAY
            isVerticalScrollBarEnabled = false
            setDownloadListener(MyWebViewDownLoadListener(context))
            webChromeClient = object : WebChromeClient() {}
            webViewClient = object : WebViewClient() {

                override fun shouldOverrideUrlLoading(view: WebView?, url: String?): Boolean {
                    LogUtil.d(
                        "king_pay-aliwappay",
                        "AliWapPayActivity:shouldOverrideUrlLoading:url：$url"
                    )
                    if (url != null) {
                        if (url.startsWith("alipays:") || url.startsWith("alipay")) {
                            try {
                                val intent = Intent("android.intent.action.VIEW", Uri.parse(url))
                                val resolveInfo = packageManager.resolveActivity(
                                    intent,
                                    PackageManager.MATCH_DEFAULT_ONLY
                                )
                                if (resolveInfo != null) {
                                    startActivity(intent)
                                    hasStartAliPaySuccess = true
                                    val layoutParams = webView?.layoutParams
                                    layoutParams?.height = 0
                                    setLayoutParams(layoutParams)
                                    mLlCloseTitle?.visibility = View.GONE
                                } else {
                                    showWebView(url)
                                }
                            } catch (e: Exception) {
                            }
                            return true
                        }
                        if (!(url.startsWith("http") || url.startsWith("https"))) {
                            return true
                        }
                        view?.loadUrl(url)
                    }
                    return true
                }

                override fun onPageStarted(view: WebView?, url: String?, favicon: Bitmap?) {
                    LogUtil.d("king_pay-aliwappay", "AliWapPayActivity:onPageStarted:url：$url")
                    super.onPageStarted(view, url, favicon)
                }

                override fun onPageFinished(view: WebView?, url: String?) {
                    super.onPageFinished(view, url)
                    LogUtil.d("king_pay-aliwappay", "AliWapPayActivity:onPageFinished:url：$url")
                    url?.let {
                        if (!TextUtils.isEmpty(it) && it.contains("about:blank")) {
                            checkShowWebView(url)
                        }
                    }
                }


            }
        }

        ivClose?.setOnClickListener(View.OnClickListener { finish() })
        mLlCloseTitle?.setOnClickListener(View.OnClickListener { })
    }

    override fun onDestroy() {
        timeOutTask?.cancel()
        super.onDestroy()
    }

    var timeOutTask: Task? = null
    private fun checkShowWebView(url: String) {
        timeOutTask = TaskManager.delayTask(1800) {
            if (!hasStartAliPaySuccess) {
                showWebView(url)
            }
        }
    }

    private fun showWebView(url: String) {
        webView?.let { wv ->
            if (wv.parent == null) {
                //注意，必须设置为match_parent，这个是viewpager2强制要求的
                val layoutParams = ViewGroup.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT
                )
                webViewParent?.addView(wv, layoutParams)
            }
            webViewParent?.visibility = View.VISIBLE
            mLlCloseTitle?.visibility = View.VISIBLE
            wv.visibility = View.VISIBLE

            ImmersionBar.with(this)
                .statusBarColor(com.dz.platform.common.R.color.common_card_FFFFFFFF)
                .navigationBarColor(com.dz.platform.common.R.color.common_card_FFFFFFFF)
                .navigationBarDarkIcon(!DeviceInfoUtil.isDarkTheme(this))
                .statusBarDarkFont(!DeviceInfoUtil.isDarkTheme(this))
                .fitsSystemWindows(true)
                .hideBar(BarHide.FLAG_SHOW_BAR)
                .init()
        }

    }

    private class MyWebViewDownLoadListener internal constructor(private val context: Activity) :
        DownloadListener {
        override fun onDownloadStart(
            url: String,
            userAgent: String,
            contentDisposition: String,
            mimetype: String,
            contentLength: Long
        ) {
            val uri = Uri.parse(url)
            val intent = Intent(Intent.ACTION_VIEW, uri)
            context.startActivity(intent)
        }
    }

    /**
     * 创建html页面
     *
     * @param link
     * @return
     */
    private fun createWebHtml(link: String?): String {
        val stringBuffer = StringBuffer()
        stringBuffer.append("<!DOCTYPE HTML>")
            .append("<html>")
            .append("<body>")
            .append(link)
            .append("</body>")
            .append("</html>")
        return stringBuffer.toString()
    }

    private var hasStartAliPaySuccess = false

    override fun onResume() {
        super.onResume()
        if (hasOnPause && hasStartAliPaySuccess) {
            finish()
        }
    }

    private var hasOnPause = false

    override fun finish() {
        super.finish()
    }

    override fun onPause() {
        hasOnPause = true
        super.onPause()
    }
}