<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/webview_parent"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@android:color/transparent"
        android:orientation="horizontal"
        android:visibility="gone" />


    <LinearLayout
        android:id="@+id/ll_close_title"
        android:layout_width="match_parent"
        android:layout_height="@dimen/common_dp48"
        android:background="#FFFFFF"
        android:orientation="horizontal"
        android:visibility="gone">

        <ImageView
            android:id="@+id/iv_close"
            android:layout_width="@dimen/common_dp24"
            android:layout_height="@dimen/common_dp24"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="@dimen/common_dp10"
            android:src="@drawable/common_back_icon" />
    </LinearLayout>

</RelativeLayout>