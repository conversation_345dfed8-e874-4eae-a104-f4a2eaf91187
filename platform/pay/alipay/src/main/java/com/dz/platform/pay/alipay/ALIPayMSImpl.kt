package com.dz.platform.pay.alipay

import android.app.Activity
import android.os.Handler
import android.os.Looper
import android.os.Message
import com.alipay.sdk.app.PayTask
import com.dz.foundation.base.utils.LogUtil
import com.dz.platform.pay.alipay.bind.AlipayAuthorize
import com.dz.platform.pay.base.PayCallback
import com.dz.platform.pay.base.PayWayType
import com.dz.platform.pay.base.data.AliOrderInfo
import com.dz.platform.pay.base.data.PayOrderInfo
import com.dz.platform.pay.base.data.PayResult
import com.dz.platform.pay.base.service.ALIPayService

/**
 * <AUTHOR>
 * @description:支付宝SDK支付模块
 * 从“点众阅读”APP中迁移而来，添加了一些改动
 * 官方文档：https://opendocs.alipay.com/open/repo-0038v9?ref=api
 * @date :2022/10/26 15:20
 */
class ALIPayMSImpl : ALIPayService {

    var payCallback: PayCallback? = null

    /**
     * ALIPAY_MOBILE_PAY 支付完成
     */
    private val ALIPAY_MOBILE_PAY_FINISH = 100310

    override fun isWapPay(): Boolean {
        return false
    }

    override fun platform(): String = PayWayType.ALIPAY_MOBILE_PAY

    private val mHandler: Handler = object : Handler(Looper.getMainLooper()) {
        override fun handleMessage(msg: Message) {
            LogUtil.i("king_pay", "----doPay 支付宝支付进入SDK handleMessage")

            when (msg.what) {
                ALIPAY_MOBILE_PAY_FINISH -> {
                    val payResult = ALIPayResult(msg.obj as Map<String, String>?)
                    val resultStatus = payResult.resultStatus
                    LogUtil.i(
                        "king_pay",
                        "----doPay 支付宝支付进入SDK handleMessage resultStatus $resultStatus"
                    )
                    /*
                    9000 	订单支付成功
                    8000 	正在处理中
                    4000 	订单支付失败
                    6001 	用户中途取消
                    6002 	网络连接出错*/
                    // 相关返回码参加官方文档：https://opendocs.alipay.com/open/204/105302
                    when (resultStatus) {
                        "9000", "8000" -> {
                            notifyResult(PayResult(PayResult.RESULT_COMMIT, "支付成功"))
                        }
                        "6001" -> {
                            notifyResult(PayResult(PayResult.RESULT_CANCEL, "取消支付"))
                        }
                        else -> {
                            notifyResult(PayResult(PayResult.RESULT_PAY_FAIL, payResult.getResultMsg()))
                        }
                    }
                }
            }
        }
    }

    private fun notifyResult(payResult: PayResult) {
        payCallback?.onResult(payResult)
    }

    override fun isAvailable(): Boolean {
        return true
    }

    override fun doPay(context: Activity?, orderInfo: PayOrderInfo, callback: PayCallback?) {
        this.payCallback = callback
        context?.let { activity ->
            val mOrderInfo = orderInfo as AliOrderInfo
            val payRunnable = Runnable { // 构造PayTask 对象
                val alipay = PayTask(activity)
                // 调用支付接口，获取支付结果
                val result: Map<String, String> = alipay.payV2(mOrderInfo.orderInfo, true)
                val msg = Message()
                msg.what = ALIPAY_MOBILE_PAY_FINISH
                msg.obj = result
                LogUtil.i("king_pay", "----doPay 支付宝支付进入SDK mHandler.sendMessage(msg) $result")
                mHandler.sendMessage(msg)
            }
//        TODO runOnUiThread
            if (Looper.myLooper() == Looper.getMainLooper()) {
                val payThread = Thread(payRunnable)
                payThread.start()
            } else {
                payRunnable.run()
            }
        } ?: let {
            payCallback?.onResult(PayResult(PayResult.RESULT_PAY_FAIL, "支付失败"))
        }
    }

    override fun bindAlipay(activity: Activity , pid : String? , appId : String?): Map<String, String> {
        return AlipayAuthorize.bindAlipay(activity , pid = pid , appId = appId)
    }

}