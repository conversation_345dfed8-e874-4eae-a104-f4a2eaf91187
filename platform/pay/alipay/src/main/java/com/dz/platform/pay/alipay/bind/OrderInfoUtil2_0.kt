package com.dz.platform.pay.alipay.bind

import java.io.UnsupportedEncodingException
import java.net.URLEncoder
import java.nio.charset.StandardCharsets
import java.security.KeyFactory
import java.security.spec.PKCS8EncodedKeySpec
import android.util.Base64

object OrderInfoUtil2_0 {

    /**
     * 构造授权参数列表
     *
     * @param pid
     * @param appId
     * @param targetId

     * @return
     */
    fun buildAuthInfoMap(pid: String, appId: String, targetId: String, rsa2: Boolean): Map<String, String> {
        val keyValues = mutableMapOf<String, String>()

        // 商户签约拿到的app_id，如：****************
        keyValues["app_id"] = appId

        // 商户签约拿到的pid，如：****************
        keyValues["pid"] = pid

        // 服务接口名称， 固定值
        keyValues["apiname"] = "com.alipay.account.auth"

        // 服务接口名称， 固定值
        keyValues["methodname"] = "alipay.open.auth.sdk.code.get"

        // 商户类型标识， 固定值
        keyValues["app_name"] = "mc"

        // 业务类型， 固定值
        keyValues["biz_type"] = "openservice"

        // 产品码， 固定值
        keyValues["product_id"] = "APP_FAST_LOGIN"

        // 授权范围， 固定值
        keyValues["scope"] = "kuaijie"

        // 商户唯一标识，如：kkkkk091125
        keyValues["target_id"] = targetId

        // 授权类型， 固定值
        keyValues["auth_type"] = "AUTHACCOUNT"

        // 签名类型
        keyValues["sign_type"] = if (rsa2) "RSA2" else "RSA"

        return keyValues
    }

    /**
     * 拼接键值对
     *
     * @param key
     * @param value
     * @param isEncode
     * @return
     */

    fun buildOrderParam(map: Map<String, String>): String {
        val keys = map.keys.toList()
        val stringbuild = StringBuilder()

        for (i in 0 until keys.size - 1) {
            val key = keys[i]
            val value = map[key]?:""
            stringbuild.append(buildKeyValue(key, value, true))
            stringbuild.append("&")
        }

        val tailKey = keys[keys.size - 1]
        val tailValue = map[tailKey]?:""
        stringbuild.append(buildKeyValue(tailKey, tailValue, true))

        return stringbuild.toString()
    }



    object SignUtils {

        private const val ALGORITHM = "RSA"
        private const val SIGN_ALGORITHMS = "SHA1WithRSA"
        private const val SIGN_SHA256RSA_ALGORITHMS = "SHA256WithRSA"
        private const val DEFAULT_CHARSET = "UTF-8"

        private fun getAlgorithms(rsa2: Boolean): String {
            return if (rsa2) SIGN_SHA256RSA_ALGORITHMS else SIGN_ALGORITHMS
        }

        @JvmStatic
        fun sign(content: String, privateKey: String, rsa2: Boolean): String? {
            return try {
                // 1. 创建 PKCS8EncodedKeySpec 对象
                val priPKCS8 = PKCS8EncodedKeySpec(Base64.decode(privateKey, Base64.DEFAULT))
                val keyFactory = KeyFactory.getInstance(ALGORITHM)
                val priKey = keyFactory.generatePrivate(priPKCS8)

                // 2. 获取签名对象
                val signature = java.security.Signature.getInstance(getAlgorithms(rsa2))

                // 3. 初始化签名对象
                signature.initSign(priKey)
                signature.update(content.toByteArray(StandardCharsets.UTF_8))

                // 4. 执行签名操作
                val signed = signature.sign()

                // 5. 对签名结果进行 Base64 编码
                Base64.encodeToString(signed, Base64.DEFAULT)
            } catch (e: Exception) {
                e.printStackTrace()
                null
            }
        }
    }




    fun getSign(map: Map<String, String>, rsaKey: String, rsa2: Boolean): String {
        val keys = ArrayList(map.keys)
        // 排序
        keys.sort()

        val authInfo = StringBuilder()
        for (i in 0 until keys.size - 1) {
            val key = keys[i]
            val value = map[key]?:""
            authInfo.append(buildKeyValue(key, value, false))
            authInfo.append("&")
        }

        // 处理最后一个键值对
        val tailKey = keys[keys.size - 1]
        val tailValue = map[tailKey]?:""
        authInfo.append(buildKeyValue(tailKey, tailValue, false))

        val oriSign = SignUtils.sign(authInfo.toString(), rsaKey, rsa2)
        val encodedSign: String = try {
            URLEncoder.encode(oriSign, StandardCharsets.UTF_8.name())
        } catch (e: Exception) {
            e.printStackTrace()
            ""
        }

        return "sign=$encodedSign"
    }


    private fun buildKeyValue(key: String, value: String, isEncode: Boolean): String {
        val sb = StringBuilder()
        sb.append(key)
        sb.append("=")
        if (isEncode) {
            try {
                sb.append(URLEncoder.encode(value, "UTF-8"))
            } catch (e: UnsupportedEncodingException) {
                sb.append(value)
            }
        } else {
            sb.append(value)
        }
        return sb.toString()
    }
}
