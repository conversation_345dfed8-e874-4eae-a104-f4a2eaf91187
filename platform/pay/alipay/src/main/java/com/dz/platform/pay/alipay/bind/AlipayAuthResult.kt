package com.dz.platform.pay.alipay.bind

class AuthResult(rawResult: Map<String, String>?) {

    var resultStatus: String? = null
        private set
    var result: String? = null
        private set
    var memo: String? = null
        private set
    var resultCode: String? = null
        private set
    var authCode: String? = null
        private set
    var alipayOpenId: String? = null
        private set
    var resultSummary: String? = null
        private set


    init {
        rawResult?.let {
            for ((key, value) in it) {
                when (key) {
                    "resultStatus" -> resultStatus = value
                    "result" -> result = value
                    "memo" -> memo = value
                }
            }

            resultSummary = buildString {
                append("resultStatus:").append(resultStatus).append(", ")
                append("result:").append(result).append(", ")
                append("memo:").append(memo)
            }
        }?: run {
            // 如果 rawResult 是 null，可以在这里处理
            resultSummary = "No data available"
        }

        }


//    private fun String.removeBrackets(remove: Boolean): String {
//        if (remove) {
//            var str = this
//            if (str.isNotEmpty()) {
//                if (str.startsWith("\"")) {
//                    str = str.removePrefix("\"")
//                }
//                if (str.endsWith("\"")) {
//                    str = str.removeSuffix("\"")
//                }
//            }
//            return str
//        }
//        return this
//    }
//
//    private fun getValue(header: String, data: String): String {
//        return data.substring(header.length)
//    }



}
