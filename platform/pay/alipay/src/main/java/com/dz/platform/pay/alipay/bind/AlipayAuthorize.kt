package com.dz.platform.pay.alipay.bind

import android.app.Activity
import android.text.TextUtils
import com.alipay.sdk.app.AuthTask
import com.dz.foundation.base.utils.LogUtil
import com.dz.platform.pay.alipay.bind.OrderInfoUtil2_0.buildAuthInfoMap
import com.dz.platform.pay.alipay.bind.OrderInfoUtil2_0.buildOrderParam


object AlipayAuthorize {

    private val PID = "2088141075515306"
    private val APPID = "2021004161603521"
    private val TARGET_id = System.currentTimeMillis()
    private val TARGET_ID = TARGET_id.toString()

    private val SDK_AUTH_FLAG = 2


    fun bindAlipay(
        activity: Activity,
        pid: String? = null,
        appId: String? = null
    ): Map<String, String> {
        val RSA_PRIVATE = ""
        val RSA2_PRIVATE = ""
        val rsa2: Boolean = RSA2_PRIVATE.isNotEmpty()
        val realPid = pid ?: PID
        val realAppId = appId ?: APPID
        val authInfoMap = buildAuthInfoMap(realPid, realAppId, TARGET_ID, rsa2)
        val info = buildOrderParam(authInfoMap)

        val privateKey: String = if (rsa2) RSA2_PRIVATE else RSA_PRIVATE
        val sign: String = OrderInfoUtil2_0.getSign(authInfoMap, privateKey, rsa2)
        val authInfo = "$info&$sign"
        LogUtil.d("alipayresult", "传入的支付宝参数 realAppId = $realAppId   realPid = $realPid")
        if (TextUtils.isEmpty(realPid) || TextUtils.isEmpty(realAppId) || TextUtils.isEmpty(
                TARGET_ID
            )
        ) {
            LogUtil.d("alipayresult", "没有支付宝id")
            return emptyMap<String, String>()
        }

        val authTask = AuthTask(activity)
        val result = authTask.authV2(authInfo, true)
        result["pid"] = realPid
        result["appId"] = realAppId
        val aliresult = AuthResult(result).resultSummary
        LogUtil.d("支付宝返回", "$aliresult")
        return result
//        val executor: ExecutorService = Executors.newSingleThreadExecutor()
//        val authCallable = Callable<Map<String, String>> {
//            try {
//
//            } catch (e: Exception) {
//                LogUtil.e("alipay", "授权请求异常")
//                emptyMap<String, String>() // 返回一个空的 Map 以表示异常
//            }
//        }
//
//        val future: Future<Map<String, String>> = executor.submit(authCallable)
//        return try {
//            val result = future.get()
//            aliresult
//        } catch (e: InterruptedException) {
//            LogUtil.e("alipay", "线程被中断")
//            null
//        } catch (e: ExecutionException) {
//            LogUtil.e("alipay", "执行异常")
//            null
//        } finally {
//            executor.shutdown()
//        }
//    }


    }
}
