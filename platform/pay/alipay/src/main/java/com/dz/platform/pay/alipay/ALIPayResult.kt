package com.dz.platform.pay.alipay

import android.text.TextUtils
import org.json.JSONObject
import java.io.Serializable

/**
 * <AUTHOR>
 * @description:
 * @date :2022/10/25 17:12
 */
class ALIPayResult(var rawResult: Map<String, String>?) : Serializable {
    /**
     * 9000 	订单支付成功
     * 8000 	正在处理中
     * 4000 	订单支付失败
     * 6001 	用户中途取消
     * 6002 	网络连接出错
     */
    var resultStatus: String? = null

    /**
     * ֧支付宝返回此次支付结果及加签，建议对支付宝签名信息拿签约时支付宝提供的公钥做验签
     */
    var result: String? = null

    var memo: String? = null

    init {

        rawResult?.let {
            for (key in rawResult!!.keys) {
                if (TextUtils.equals(key, "resultStatus")) {
                    resultStatus = rawResult!![key]
                } else if (TextUtils.equals(key, "result")) {
                    result = rawResult!![key]
                } else if (TextUtils.equals(key, "memo")) {
                    memo = rawResult!![key]
                }
            }

        }

    }

    fun getResultMsg(): String {
        return when (resultStatus) {
            "9000", "8000" -> "订单支付成功"
            "6001" -> "取消支付"
            "6002" -> "网络连接失败,请检查您的网络"
            else -> "订单支付失败"
        }
    }
}