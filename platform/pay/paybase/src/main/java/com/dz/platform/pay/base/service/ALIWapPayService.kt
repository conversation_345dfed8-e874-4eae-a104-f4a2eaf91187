package com.dz.platform.pay.base.service

import com.dz.platform.common.service.get
import com.dz.platform.pay.base.data.ALIWapOrderInfo

/**
 * <AUTHOR>
 * @description:
 * @date :2022/10/25 15:10
 */
interface ALIWapPayService : PaySdkModuleService<ALIWapOrderInfo> {
    companion object {
        private val instance = ALIWapPayService::class.java.get()
        fun get(): ALIWapPayService? {
            return instance
        }
    }
}