package com.dz.platform.pay.base.data

import android.text.TextUtils
import com.dz.platform.pay.base.PayWayType
import java.io.Serializable

/**
 * <AUTHOR>
 * @description:下单返回的订单信息
 * @date :2022/10/24 18:39
 */
class OrderInfo : Serializable {

    var dd100: WxOrderInfo? = null
    var dd200: WxWapOrderInfo? = null
    var dd300: AliOrderInfo? = null
    var dd400: ALIWapOrderInfo? = null

    var orderNum: String? = null  // 订单号
    var tip: String? = null//微信或支付宝未安装提示文案，根据支付方式下发对应支付方式提示文案，固定找产品待定
//    var tip1: String? = null//弹窗文案第一条，如：更换金额或使用支付宝付款，请点击“取消支付”
//    var tip2: String? = null//第二个弹窗文案，如：资产存在延迟到账风险，请耐心等待，如一直未到账，请联系客服处理
    var time: Int = 5

//    /**
//     * 下单支付方式，
//     * dd100-WECHAT_MOBILE_PAY（微信SDK），
//     * dd200-WECHAT_WAP_PAY（现在支付），
//     * dd300-ALIPAY_MOBILE_PAY（支付宝SDK），
//     * dd400-ALIPAY_WEB_PAY（支付宝WEB支付），
//     * dd500-IOS_IAP_PAY（苹果内购）【命名规避苹果审核】
//     */
//    var descId: String? = null

    var msg: String? = null//下单提示语, 如：下单失败，请联系客服
//    var status: Int? = null//下单返回状态，0-失败，1-成功，失败则下发对应提示


    fun getPayOrderInfo(payWay: String): PayOrderInfo? {
        var payOrderInfo: PayOrderInfo? = null
        when (payWay) {
            PayWayType.ALIPAY_MOBILE_PAY -> payOrderInfo = dd300
            PayWayType.WECHAT_MOBILE_PAY -> payOrderInfo = dd100?.apply { tipText = tip }
            PayWayType.WECHAT_WAP_PAY -> payOrderInfo = dd200?.apply { tipText = tip }
            PayWayType.ALIPAY_WEB_PAY -> payOrderInfo = dd400
            else -> {}
        }
        payOrderInfo?.orderNum = orderNum
        return payOrderInfo
    }

    fun isAvailable(): Boolean {
        return orderNum?.isNotEmpty() == true && (dd100 != null || dd200 != null || dd300 != null || dd400 != null)
    }
}