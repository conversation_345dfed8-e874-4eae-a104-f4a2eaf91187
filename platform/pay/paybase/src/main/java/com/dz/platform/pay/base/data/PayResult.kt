package com.dz.platform.pay.base.data

import java.io.Serializable

/**
 * <AUTHOR>
 * @description: 支付结果 包括成功和失败
 * @date :2022/10/24 18:24
 */
open class PayResult(open var resultCode: Int = -1, open var message: String? = "") : Serializable {

    companion object {

        /**
         * 最终返回给调用者的结果
         */
        const val RESULT_UNKNOWN = 0  // 未知
        const val RESULT_PAY_SUCCESS = 1  // 支付成功，且在后台已经查到
        const val RESULT_PAY_FAIL = 2  // 支付失败
        const val RESULT_COMMIT = 3  // 支付成功，但未在后台查询到
        const val RESULT_CANCEL = 4  // 用户取消支付
        const val RESULT_CLIENT_NOT_INSTALL = 6  // 用户未安装客户端

    }

    /**
     * 下单成功的订单相关信息
     */
    var orderInfo: OrderInfo? = null

    /**
     * 查单响应的结果，包括余额等
     */
    var orderResult: OrderResult? = null

//    fun isSuccess() = resultCode == RESULT_OK

    fun getResultMsg(): String {
        return when (resultCode) {
            RESULT_UNKNOWN -> "未知错误"
            RESULT_PAY_SUCCESS -> "支付成功，且在后台已经查到"
            RESULT_PAY_FAIL -> "支付失败"
            RESULT_COMMIT -> "支付成功，但未在后台查询到支付信息"
            RESULT_CANCEL -> "用户取消支付"
            RESULT_CLIENT_NOT_INSTALL -> "您暂未安装微信"
            else -> "未知错误"
        }
    }

}