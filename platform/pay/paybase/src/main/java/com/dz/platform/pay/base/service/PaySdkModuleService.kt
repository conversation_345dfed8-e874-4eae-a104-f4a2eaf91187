package com.dz.platform.pay.base.service

import android.app.Activity
import com.dz.platform.common.service.IModuleService
import com.dz.platform.pay.base.PayCallback
import com.dz.platform.pay.base.data.PayOrderInfo

/**
 * <AUTHOR>
 * @description:
 * @date :2022/10/24 18:13
 */
interface PaySdkModuleService<T : PayOrderInfo> : IModuleService {

    fun doPay(context: Activity?, orderInfo: PayOrderInfo, callback: PayCallback?)

    fun isAvailable(): Boolean

    /**
     * 是否是WAP支付
     *
     * @return
     */
    fun isWapPay(): Boolean

    /**
     * 支付平台
     *
     * @return
     */
    fun platform(): String
}