package com.dz.platform.pay.base.service

import com.dz.platform.common.service.get
import com.dz.platform.pay.base.data.WxWapOrderInfo

/**
 * <AUTHOR>
 * @description:
 * @date :2022/10/25 15:15
 */
interface WxWapPayService : PaySdkModuleService<WxWapOrderInfo> {
    companion object {
        private val instance = WxWapPayService::class.java.get()
        fun get(): WxWapPayService? {
            return instance
        }
    }
}