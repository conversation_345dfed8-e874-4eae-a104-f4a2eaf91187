package com.dz.platform.pay.base.service

import android.app.Activity
import com.dz.platform.common.service.IModuleService
import com.dz.platform.common.service.get
import com.dz.platform.pay.base.data.AliOrderInfo

/**
 * <AUTHOR>
 * @description:
 * @date :2022/10/25 14:34
 */
interface ALIPayService : PaySdkModuleService<AliOrderInfo> {
    companion object {
        private val instance = ALIPayService::class.java.get()
        fun get(): ALIPayService? {
            return instance
        }
    }
    fun bindAlipay( activity: Activity , pid : String? = null , appId : String? = null):Map<String, String>
}