package com.dz.platform.pay.base.service

import com.dz.platform.common.service.get
import com.dz.platform.pay.base.data.WxOrderInfo

/**
 * <AUTHOR>
 * @description:
 * @date :2022/10/25 15:14
 */
interface WxPayService : PaySdkModuleService<WxOrderInfo> {
    companion object {
        private val instance = WxPayService::class.java.get()
        fun get(): WxPayService? {
            return instance
        }
    }
}