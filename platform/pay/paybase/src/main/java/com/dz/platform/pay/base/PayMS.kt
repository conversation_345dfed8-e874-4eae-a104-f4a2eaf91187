package com.dz.platform.pay.base

import com.dz.platform.common.service.IModuleService
import com.dz.platform.common.service.get
import com.dz.platform.pay.base.data.PayRequestInfo

/**
 * <AUTHOR>
 * @description:
 * @date :2022/10/25 16:21
 */
interface PayMS : IModuleService {
    companion object {
        private val instance = PayMS::class.java.get()
        fun get(): PayMS? {
            return instance
        }
    }

    /**
     * 开始支付
     *
     * @param payInfo 支付需要的信息
     */
    fun startPay(payInfo: PayRequestInfo?, payTaskHandler: PayTaskHandler?)

    fun getSupportPay(): Array<String>?

    fun isAvailable(): Bo<PERSON>an
}