package com.dz.platform.pay.base.util

import android.content.Context
import android.content.Intent
import android.content.pm.PackageInfo
import android.content.pm.PackageManager
import android.net.Uri

/**
 * <AUTHOR>
 * @description:
 * @date :2022/11/15 19:35
 */
class AppInstallUtils {

    companion object {


        /**
         * 检测是否安装支付宝
         *
         * @param context
         * @return
         */
        fun isAliPayInstalled(context: Context): <PERSON><PERSON><PERSON> {
            val uri = Uri.parse("alipays://platformapi/startApp")
            val intent = Intent(Intent.ACTION_VIEW, uri)
            val componentName = intent.resolveActivity(context.packageManager)
            return componentName != null
        }

        /**
         * 判断 用户是否安装微信客户端
         */
        fun isWxInstalled(context: Context?): <PERSON><PERSON><PERSON> {
            if (context == null) {
                return false
            }
            val pm = context.packageManager
            var packageInfo: PackageInfo? = null
            try {
                packageInfo = pm.getPackageInfo("com.tencent.mm", PackageManager.GET_SIGNATURES)
                if (packageInfo != null) {
                    return true
                }
            } catch (e: PackageManager.NameNotFoundException) {
                e.printStackTrace()
            }
            return false
        }


    }
}