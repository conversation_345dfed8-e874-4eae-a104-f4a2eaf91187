package com.dz.platform.pay.base.util

import com.dz.foundation.base.service.DzServiceManager
import com.dz.platform.pay.base.PayWayType
import com.dz.platform.pay.base.data.PayOrderInfo
import com.dz.platform.pay.base.service.*

/**
 * <AUTHOR>
 * @description:
 * @date :2022/10/25 15:16
 */
class PayUtil {
    companion object {

        fun getSdkPayService(payType: String?): PaySdkModuleService<out PayOrderInfo>? {

            var payService: PaySdkModuleService<out PayOrderInfo>? = null

            when (payType) {
                PayWayType.ALIPAY_MOBILE_PAY -> payService =
                    ALIPayService.get()
                PayWayType.WECHAT_MOBILE_PAY -> payService =
                    WxPayService.get()
                PayWayType.WECHAT_WAP_PAY -> payService =
                    WxWapPayService.get()
                PayWayType.ALIPAY_WEB_PAY -> payService =
                    ALIWapPayService.get()
                else -> {}
            }
            return payService
        }

        /**
         * 获取支持的 充值方式
         *
         * @return
         */
        fun getSupportPay(): Array<String>? {
            val list = ArrayList<String>()

            val aliPayService = DzServiceManager.getService(ALIPayService::class.java)
            aliPayService?.let {
                if (it.isAvailable()) {
                    list.add(PayWayType.ALIPAY_MOBILE_PAY)
                }

            }

            val wxPayService = DzServiceManager.getService(WxPayService::class.java)
            wxPayService?.let {
                if (it.isAvailable()) {
                    list.add(PayWayType.WECHAT_MOBILE_PAY)
                }
            }

            val aliWapPayService = DzServiceManager.getService(ALIWapPayService::class.java)
            aliWapPayService?.let {
                if (it.isAvailable()) {
                    list.add(PayWayType.ALIPAY_WEB_PAY)
                }
            }

            val wxWapPayService = DzServiceManager.getService(WxWapPayService::class.java)
            wxWapPayService?.let {
                if (it.isAvailable()) {
                    list.add(PayWayType.WECHAT_WAP_PAY)
                }
            }
            return list.toTypedArray()
        }
    }

}