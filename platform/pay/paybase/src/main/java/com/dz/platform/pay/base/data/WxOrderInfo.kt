package com.dz.platform.pay.base.data

import android.text.TextUtils

/**
 * <AUTHOR>
 * @description:
 * @date :2022/10/25 14:46
 */
class WxOrderInfo(

    val appId: String? = null,//微信开放平台审核通过的应用APPID
    val partnerId: String? = null,//微信支付分配的商户号
    val prepayId: String? = null,//微信返回的支付交易会话ID
    val nonceStr: String? = null,//随机字符串，不长于32位。推荐随机数生成算法
    val timeStamp: String? = null,//时间戳，请见接口规则-参数规定
    val packageValue: String? = "Sign=WXPay",//暂填写固定值Sign=WXPay
    val sign: String? = null,//签名，详见签名生成算法
    val extData: String? = null//订单参数对应的哪个马甲产品 例如:快看小说，快看阅读

) : PayOrderInfo() {
    var tipText: String? = null
    override fun isAvailable(): Boolean {
        return !TextUtils.isEmpty(appId) && !TextUtils.isEmpty(partnerId);
    }
}