<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#01000000">

    <WebView
        android:id="@+id/webview"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#01000000"
        android:visibility="gone" />

    <LinearLayout
        android:id="@+id/ll_close_title"
        android:layout_width="match_parent"
        android:layout_height="@dimen/common_dp48"
        android:background="#FFFFFF"
        android:orientation="horizontal"
        android:visibility="gone">

        <ImageView
            android:id="@+id/iv_close"
            android:layout_width="@dimen/common_dp24"
            android:layout_height="@dimen/common_dp24"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="@dimen/common_dp10"
            android:src="@drawable/common_back_icon" />

    </LinearLayout>

</RelativeLayout>