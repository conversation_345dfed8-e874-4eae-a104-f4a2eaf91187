package com.dz.platform.pay.wxwap.ui

import android.app.Activity
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import android.view.WindowManager
import android.webkit.*
import android.widget.ImageView
import android.widget.LinearLayout
import androidx.appcompat.app.AppCompatActivity
import com.dz.foundation.base.utils.DeviceInfoUtil
import com.dz.foundation.base.utils.LogUtil
import com.dz.platform.pay.wxwap.R
import com.gyf.immersionbar.BarHide
import com.gyf.immersionbar.ImmersionBar

/**
 * <AUTHOR>
 * @description:
 * @date :2022/10/26 16:17
 */
class WxWapPayActivity : AppCompatActivity() {

    private var webView: WebView? = null
    private var mLlCloseTitle: LinearLayout? = null
    private var ivClose: ImageView? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        init()
    }

    private fun init() {
        setStatusBarColor()
        loadView()
        initView()
        initData()
    }

    private fun loadView() {
        setContentView(R.layout.wxwap_pay_activity)

    }

    private fun setStatusBarColor() {
        ImmersionBar.with(this)
            .transparentStatusBar()
            .transparentNavigationBar()
            .statusBarDarkFont(!DeviceInfoUtil.isDarkTheme(this))
            .init()
    }


    private fun initView() {

        webView = findViewById(R.id.webview)
        mLlCloseTitle = findViewById(R.id.ll_close_title)
        ivClose = findViewById(R.id.iv_close)

    }


    private fun initData() {
        val context: Activity = this
        initViewData(context)
        val intent = intent
        val link = intent.getStringExtra("link")
        val referer = intent.getStringExtra("referer")
        if (link != null && referer != null) {
            val headers: MutableMap<String, String> = HashMap()
            headers["Referer"] = referer
            webView!!.loadUrl(link, headers)
        }
    }

    private fun initViewData(context: Activity) {
        webView?.run {
            setBackgroundColor(0)
            setHorizontalScrollbarOverlay(false)
            isHorizontalScrollBarEnabled = false
            isVerticalScrollBarEnabled = false
            settings.run {
                javaScriptEnabled = true
                setSupportZoom(false)
                cacheMode = WebSettings.LOAD_DEFAULT
                allowFileAccess = true
                useWideViewPort = true
                databaseEnabled = true
                domStorageEnabled = true
//                setAppCacheEnabled(true)
                mixedContentMode = WebSettings.MIXED_CONTENT_ALWAYS_ALLOW
            }
            isFocusable = true
            scrollBarStyle = WebView.SCROLLBARS_INSIDE_OVERLAY
            isVerticalScrollBarEnabled = false
            setDownloadListener(MyWebViewDownLoadListener(context))
            webChromeClient = object : WebChromeClient() {}
            webViewClient = object : WebViewClient() {

                override fun shouldOverrideUrlLoading(view: WebView?, url: String?): Boolean {
                    LogUtil.d("king_wxwappay", "WxWapPayActivity:shouldOverrideUrlLoading:url：$url")
                    if (url != null) {
                        if (url.startsWith("weixin:")) {
                            try {
                                val intent = Intent("android.intent.action.VIEW", Uri.parse(url))
                                val resolveInfo = packageManager.resolveActivity(
                                    intent,
                                    PackageManager.MATCH_DEFAULT_ONLY
                                )
                                if (resolveInfo != null) {
                                    startActivity(intent)
                                    hasStartAliPaySuccess = true
                                    val layoutParams = webView?.layoutParams
                                    layoutParams?.height = 0
                                    setLayoutParams(layoutParams)
                                    mLlCloseTitle?.visibility = View.GONE
                                    LogUtil.d(
                                        "king_wxwappay",
                                        "WxWapPayActivity:shouldOverrideUrlLoading:url：mLlCloseTitle?.GONE"
                                    )
                                }
                            } catch (e: Exception) {
                            }
                            return true
                        }
                        if (!(url.startsWith("http") || url.startsWith("https"))) {
                            return true
                        }
                        view?.loadUrl(url)
                    }
                    return true
                }

                override fun onPageStarted(view: WebView?, url: String?, favicon: Bitmap?) {
                    LogUtil.d("king_wxwappay", "AliWapPayActivity:onPageStarted:url：$url")
                    super.onPageStarted(view, url, favicon)
                }

                override fun onPageFinished(view: WebView?, url: String?) {
                    LogUtil.d("king_wxwappay", "AliWapPayActivity:onPageFinished:url：$url")
                    super.onPageFinished(view, url)
                    if (url != null) {
                        if (!TextUtils.isEmpty(url) && !url.contains("about:blank")) {
                            if (webView?.visibility != View.VISIBLE) {
                                LogUtil.d(
                                    "king_wxwappay",
                                    "AliWapPayActivity:onPageFinished:mLlCloseTitle?.setVisibility(View.VISIBLE)"
                                )
                                mLlCloseTitle?.visibility = View.GONE
                                webView?.visibility = View.VISIBLE
                            }
                        }
                    }
                }
            }
        }

        ivClose?.setOnClickListener(View.OnClickListener { finish() })
    }

    private class MyWebViewDownLoadListener internal constructor(private val context: Activity) :
        DownloadListener {
        override fun onDownloadStart(
            url: String,
            userAgent: String,
            contentDisposition: String,
            mimetype: String,
            contentLength: Long
        ) {
            val uri = Uri.parse(url)
            val intent = Intent(Intent.ACTION_VIEW, uri)
            context.startActivity(intent)
        }
    }

    /**
     * 创建html页面
     *
     * @param link
     * @return
     */
    private fun createWebHtml(link: String?): String {
        val stringBuffer = StringBuffer()
        stringBuffer.append("<!DOCTYPE HTML>")
            .append("<html>")
            .append("<body>")
            .append(link)
            .append("</body>")
            .append("</html>")
        return stringBuffer.toString()
    }

    private var hasStartAliPaySuccess = false

    override fun onResume() {
        super.onResume()
        if (hasOnPause && hasStartAliPaySuccess) {
            finish()
        }
    }

    private var hasOnPause = false

    override fun finish() {
        super.finish()
    }

    override fun onPause() {
        hasOnPause = true
        super.onPause()
    }
}