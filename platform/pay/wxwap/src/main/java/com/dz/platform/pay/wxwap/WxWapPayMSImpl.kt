package com.dz.platform.pay.wxwap

import android.app.Activity
import android.content.Intent
import android.net.Uri
import android.text.TextUtils
import com.dz.platform.pay.base.PayCallback
import com.dz.platform.pay.base.PayWayType
import com.dz.platform.pay.base.data.PayOrderInfo
import com.dz.platform.pay.base.data.PayResult
import com.dz.platform.pay.base.data.WxWapOrderInfo
import com.dz.platform.pay.base.service.WxWapPayService
import com.dz.platform.pay.base.util.AppInstallUtils
import com.dz.platform.pay.wxwap.ui.WxWapPayActivity

/**
 * <AUTHOR>
 * @description:微信WAP支付
 * @date :2022/10/26 15:55
 */
class WxWapPayMSImpl : WxWapPayService {

    override fun isWapPay(): Boolean {
        return true
    }

    override fun platform(): String = PayWayType.WECHAT_WAP_PAY

    override fun doPay(context: Activity?, orderInfo: PayOrderInfo, callback: PayCallback?) {
        val mOrderInfo = orderInfo as WxWapOrderInfo
        if (!AppInstallUtils.isWxInstalled(context)) {
            callback!!.onResult(PayResult(PayResult.RESULT_PAY_FAIL, mOrderInfo.tipText))
            return
        }
        if (TextUtils.isEmpty(mOrderInfo.referer)) {
            //现代支付
            val uri = Uri.parse(mOrderInfo.mweb_url)
            val intent = Intent(Intent.ACTION_VIEW, uri)
            context?.startActivity(intent)
        } else {
            //微信官方支付
            val intent = Intent(context, WxWapPayActivity::class.java)
            intent.putExtra("link", mOrderInfo.mweb_url)
            intent.putExtra("referer", mOrderInfo.referer)
            context!!.startActivity(intent)
        }

    }

    override fun isAvailable(): Boolean {
        return true
    }
}