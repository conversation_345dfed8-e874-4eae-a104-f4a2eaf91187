package com.dz.platform.pay.wxpay

import android.app.Activity
import com.dz.foundation.base.utils.LogUtil
import com.dz.platform.pay.base.PayCallback
import com.dz.platform.pay.base.PayWayType
import com.dz.platform.pay.base.data.PayOrderInfo
import com.dz.platform.pay.base.data.PayResult
import com.dz.platform.pay.base.data.WxOrderInfo
import com.dz.platform.pay.base.service.WxPayService
import com.tencent.mm.opensdk.constants.Build
import com.tencent.mm.opensdk.modelpay.PayReq
import com.tencent.mm.opensdk.openapi.WXAPIFactory

/**
 * <AUTHOR>
 * @description:
 * @date :2022/10/26 15:35
 */
class WxPayMSImpl : WxPayService {

    override fun isWapPay(): Boolean {
        return false
    }

    override fun platform(): String = PayWayType.WECHAT_MOBILE_PAY

    override fun doPay(context: Activity?, orderInfo: PayOrderInfo, callback: PayCallback?) {

        val mOrderInfo = orderInfo as WxOrderInfo
        LogUtil.i("king_pay", "----WxPayMSImpl doPay appId ${mOrderInfo.appId} ")
        val api = WXAPIFactory.createWXAPI(context, mOrderInfo.appId)
        // 将该app注册到微信
        api.registerApp(mOrderInfo.appId)

        if (!api.isWXAppInstalled) {
            callback!!.onResult(PayResult(PayResult.RESULT_CLIENT_NOT_INSTALL, mOrderInfo.tipText))
            return
        }
        //检查微信版本是否支持支付
        val isPaySupported = api.wxAppSupportAPI >= Build.PAY_SUPPORTED_SDK_INT
        if (!isPaySupported) {
            callback!!.onResult(PayResult(PayResult.RESULT_PAY_FAIL, "您的微信版本过低，不支持支付，请升级微信版本。"))
            return
        }
        val req = PayReq().apply {
            appId = mOrderInfo.appId
            partnerId = mOrderInfo.partnerId
            prepayId = mOrderInfo.prepayId
            nonceStr = mOrderInfo.nonceStr
            timeStamp = mOrderInfo.timeStamp
            packageValue = mOrderInfo.packageValue
            extData = mOrderInfo.extData
            sign = mOrderInfo.sign
        }
        api.sendReq(req)
    }

    override fun isAvailable(): Boolean {
        return true
    }
}