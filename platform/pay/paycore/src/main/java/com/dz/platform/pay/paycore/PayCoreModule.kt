package com.dz.platform.pay.paycore

import com.dz.foundation.base.module.LibModule
import com.dz.foundation.base.service.DzServiceManager
import com.dz.platform.pay.base.PayMS

/**
 * <AUTHOR>
 * @description: 支付模块
 * @date :2022/10/26 11:06
 */
class PayCoreModule : LibModule() {

    override fun onCreate() {
        DzServiceManager.registerService(PayMS::class.java, PayCoreMSImpl::class.java)
    }
}