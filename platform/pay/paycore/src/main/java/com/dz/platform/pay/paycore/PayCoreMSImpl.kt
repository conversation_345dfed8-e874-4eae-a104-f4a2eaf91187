package com.dz.platform.pay.paycore

import com.dz.platform.pay.base.PayMS
import com.dz.platform.pay.base.PayTaskHandler
import com.dz.platform.pay.base.data.PayRequestInfo
import com.dz.platform.pay.base.util.PayUtil
import com.dz.platform.pay.paycore.intent.PayCoreMR

/**
 * <AUTHOR>
 * @description:
 * @date :2022/10/26 15:12
 */
class PayCoreMSImpl : PayMS {

    override fun startPay(payInfo: PayRequestInfo?, payTaskCallback: PayTaskHandler?) {
        PayCoreMR.get().payCore().apply {
            payTaskHandler = payTaskCallback
        }.start()
    }

    override fun getSupportPay(): Array<String>? {
        return PayUtil.getSupportPay()
    }

    override fun isAvailable(): Boolean {
        return true
    }
}