package com.dz.platform.pay.paycore.bean

import java.io.Serializable

/**
 * <AUTHOR>
 * @description:微信支付返回值
 * @date :2022/12/3 16:18
 */
class WxPayOnRespBean(
    var isWxPay: Boolean? = false,
    var errCode: Int? = -1,  // 0成功，-1错误, -2用户取消
    var errStr: String? = null,
    var transaction: String? = null,
    var openId: String? = null
) : Serializable {

    fun getMsg(): String {
        return when (errCode) {
            0 -> "订单支付成功"
            -2 -> "取消支付"
            else -> {
                "订单支付异常"
            }
        }
    }
}