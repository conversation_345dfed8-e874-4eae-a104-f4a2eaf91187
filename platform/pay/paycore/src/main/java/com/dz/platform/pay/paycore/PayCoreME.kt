package com.dz.platform.pay.paycore

import com.dz.foundation.event.Event
import com.dz.foundation.event.IModuleEvent
import com.dz.foundation.event.get
import com.dz.platform.pay.base.data.PayResult
import com.dz.platform.pay.paycore.bean.WxPayOnRespBean

/**
 * <AUTHOR>
 * @description:
 * @date :2022/12/3 16:13
 */
interface PayCoreME : IModuleEvent {
    companion object {
        fun get(): PayCoreME {
            return PayCoreME::class.java.get()
        }
    }

    fun onWxOnResp(): Event<WxPayOnRespBean>

    /**
     * 第三方支付返回支付结果后的回调
     *
     * @return
     */
    fun onPayResult(): Event<PayResult>
}