package com.dz.platform.pay.paycore.intent

import com.dz.foundation.router.IModuleRouter
import com.dz.foundation.router.annotation.RouteAction
import com.dz.foundation.router.get

/**
 * <AUTHOR>
 * @description:
 * @date :2022/10/26 10:59
 */
interface PayCoreMR : IModuleRouter {
    companion object {
        const val PAYCORE = "paycore"
        private val instance = PayCoreMR::class.java.get()
        fun get(): PayCoreMR {
            return instance
        }
    }

    @RouteAction(PAYCORE)
    fun payCore(): PayCoreIntent
}