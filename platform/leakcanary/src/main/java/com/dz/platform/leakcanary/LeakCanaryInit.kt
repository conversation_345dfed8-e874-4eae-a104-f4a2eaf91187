package com.dz.platform.leakcanary

import android.content.Context
import com.dz.foundation.base.utils.CrashUtils
import com.dz.foundation.base.utils.LogUtil
import com.therouter.app.flowtask.lifecycle.FlowTask
import leakcanary.EventListener
import leakcanary.LeakCanary

/**
 *@Author: zhanggy
 *@Date: 2024-02-19
 *@Description:
 *@Version:1.0
 */

@FlowTask(taskName = "initLeakCanaryDebug", dependsOn = "onAgreeProtocol", async = true)
fun initLeakCanary(context: Context) {
    LogUtil.d("StartUp", "initLeakCanary start")
    val startTime = System.currentTimeMillis()
    val analysisUploadListener = EventListener { event ->
        try {
            when (event) {
                is EventListener.Event.DumpingHeap -> {
                }

                is EventListener.Event.HeapDump -> {
                }

                is EventListener.Event.HeapAnalysisDone.HeapAnalysisSucceeded -> {
                    event.heapAnalysis.applicationLeaks.forEach {
                        CrashUtils.reportError(
                            "内存泄露监控",
                            MLException(it.shortDescription + "\n" + event.heapAnalysis.toString())
                        )
                    }
                }

                else -> {

                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    LeakCanary.config = LeakCanary.config.run {
        copy(eventListeners = eventListeners + analysisUploadListener)
    }
    LogUtil.d(
        "speed",
        "initLeakCanary 耗时:${System.currentTimeMillis() - startTime}. Thread:${Thread.currentThread().name}"
    )
}