package com.dz.platform.abtest

import com.dz.foundation.base.module.AppModule
import com.dz.foundation.base.utils.LogUtil
import com.sensorsdata.abtest.SensorsABTest
import com.sensorsdata.abtest.SensorsABTestConfigOptions


/**
 *@Author: zhanggy
 *@Date: 2023-05-08
 *@Description:
 *@Version:1.0
 */
const val TAG = "ABTest"

object ABTestUtil {

    fun init() {
        try {
            LogUtil.d(TAG, "开始初始化")
            // A/B Testing SDK 初始化
            val abTestConfigOptions =
                SensorsABTestConfigOptions("https://abt.dzfread.cn/api/v2/abtest/online/results?project-key=274191EB741FAFF7FA69302E2E498E3A90028501")
            SensorsABTest.startWithConfigOptions(AppModule.getApplication(), abTestConfigOptions)
            aBTestListener?.onInitialized()
        } catch (e: Exception) {
            e.printStackTrace()
            LogUtil.e(TAG, "初始化失败, ${e.message}")
        }
    }

    var aBTestListener: ABTestListener? = null

    interface ABTestListener {
        fun onInitialized()
    }
}