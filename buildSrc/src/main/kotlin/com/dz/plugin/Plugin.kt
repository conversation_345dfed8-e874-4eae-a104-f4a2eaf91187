package com.dz.plugin



import com.dz.plugin.transform.BaseASMHookPlugin
import com.dz.plugin.transform.BaseHookTransform
import com.dz.plugin.transform.BaseScanTransform
import com.dz.plugin.transform.HookConfig

class Plugin : BaseASMHookPlugin() {
    override fun getHookConfig(): HookConfig {
        val hookConfig = HookConfig()
        hookConfig.hookMethodConfigClassName = "DzHookMethodUtil.class"
        hookConfig.hookMethodConfigClassPath = "com/dz/platform/hook/DzHookMethodUtil.class"
        return hookConfig
    }

    override fun getHookTransform(): BaseHookTransform {
        return  PrivacyHookTransform()
    }

    override fun getScanTransform(): BaseScanTransform {
        return  PrivacyScanTransform()
    }

    class PrivacyHookTransform: BaseHookTransform() {

    }
    class PrivacyScanTransform: BaseScanTransform(){

    }

}