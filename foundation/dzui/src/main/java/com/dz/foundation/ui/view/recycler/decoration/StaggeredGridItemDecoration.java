package com.dz.foundation.ui.view.recycler.decoration;

import android.graphics.Rect;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.StaggeredGridLayoutManager;

import com.dz.foundation.base.utils.LogUtil;

/**
 * @Author: zhanggy
 * @Date: 2025-04-11
 * @Description:瀑布流布局的间距
 * @Version:1.0
 */
public class StaggeredGridItemDecoration extends RecyclerView.ItemDecoration {

    private final int mVerticalSpacing;
    private final int mHorizontalSpacing;
    private final boolean mIncludeEdge;

    /**
     * 注意：当纵向滑动方向时，那么水平间距不生效。这是由于 StaggeredGridLayoutManager 的两边对齐，导致中间item
     * 会自动留出水平间距，所以水平间距不生效。
     * 当横向滑动方向时，那么垂直间距不生效。
     * 解决办法：固定每个Holder的宽度，然后通过设置RecyclerView的宽度，来变相设置Holder间的水平间距。
     *
     * @param includeEdge 是否在边缘应用间距
     * @param verSpacing  垂直方向的间距 (像素)
     * @param horSpacing  水平方向的间距 (像素)
     */
    public StaggeredGridItemDecoration(boolean includeEdge, int verSpacing, int horSpacing) {
        this.mIncludeEdge = includeEdge;
        this.mVerticalSpacing = verSpacing;
        this.mHorizontalSpacing = horSpacing;
    }

    @Override
    public void getItemOffsets(@NonNull Rect outRect, @NonNull View view, @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
        int position = parent.getChildAdapterPosition(view);
        if (position == RecyclerView.NO_POSITION) {
            return;
        }

        StaggeredGridLayoutManager layoutManager = (StaggeredGridLayoutManager) parent.getLayoutManager();
        if (layoutManager == null) {
            return;
        }

        StaggeredGridLayoutManager.LayoutParams layoutParams = (StaggeredGridLayoutManager.LayoutParams) view.getLayoutParams();
        int spanIndex = layoutParams.getSpanIndex();
        int spanCount = layoutManager.getSpanCount();
        int orientation = layoutManager.getOrientation();
        LogUtil.d("XXX", "getItemOffsets 调整以前 orientation=" + orientation + ",spanIndex=" + spanIndex + ",spanCount=" + spanCount);
        if (orientation == RecyclerView.VERTICAL) {
            // 垂直方向的 StaggeredGridLayoutManager
            if (mIncludeEdge) {
                outRect.left = mHorizontalSpacing - spanIndex * mHorizontalSpacing / spanCount;
                outRect.right = (spanIndex + 1) * mHorizontalSpacing / spanCount;
                if (position < spanCount) {
                    outRect.top = mVerticalSpacing;
                }
                outRect.bottom = mVerticalSpacing;
            } else {
                outRect.left = spanIndex * mHorizontalSpacing / spanCount;
                outRect.right = mHorizontalSpacing - (spanIndex + 1) * mHorizontalSpacing / spanCount;
                if (position >= spanCount) {
                    outRect.top = mVerticalSpacing;
                }
            }
        } else {
            // 水平方向的 StaggeredGridLayoutManager
            if (mIncludeEdge) {
                outRect.top = mVerticalSpacing - spanIndex * mVerticalSpacing / spanCount;
                outRect.bottom = (spanIndex + 1) * mVerticalSpacing / spanCount;
                if (position < spanCount) {
                    outRect.left = mHorizontalSpacing;
                }
                outRect.right = mHorizontalSpacing;
            } else {
                outRect.top = spanIndex * mVerticalSpacing / spanCount;
                outRect.bottom = mVerticalSpacing - (spanIndex + 1) * mVerticalSpacing / spanCount;
                if (position >= spanCount) {
                    outRect.left = mHorizontalSpacing;
                }
            }
        }

        LogUtil.d("XXX", "getItemOffsets 调整以后outRect=" + outRect.toShortString());
    }
}
