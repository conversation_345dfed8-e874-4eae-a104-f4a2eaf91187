package com.dz.foundation.ui.utils

import android.graphics.drawable.GradientDrawable
import android.graphics.drawable.StateListDrawable
import kotlin.math.ceil

/**
 * @Author: wanxin
 * @Date: 2023/7/8 14:15
 * @Description:
 * @Version: 1.0
 */
object ShapeDrawableUtil {

    fun createStateListDrawable(
        radius: Float = 0f,
        leftTopRadius: Float = 0f,
        rightTopRadius: Float = 0f,
        rightBottomRadius: Float = 0f,
        leftBottomRadius: Float = 0f,
        solidColor: Int = 0,
        gradientStartColor: Int = 0,
        gradientEndColor: Int = 0,
        gradientOrientation: Int = 0,
        stokeColor: Int = 0,
        stokeWidth: Float = 0f,
        statePressedAlpha: Float = 0f
    ): StateListDrawable {
        val stateListDrawable = StateListDrawableUtils.ShapeBuilder().build()
        val shape = createGradientDrawable(
            radius,
            leftTopRadius,
            rightTopRadius,
            rightBottomRadius,
            leftBottomRadius,
            solidColor,
            gradientStartColor,
            gradientEndColor,
            gradientOrientation,
            stokeColor,
            stokeWidth
        )
        if (statePressedAlpha != 0f) {
            val pressedShape = createGradientDrawable(
                radius,
                leftTopRadius,
                rightTopRadius,
                rightBottomRadius,
                leftBottomRadius,
                solidColor,
                gradientStartColor,
                gradientEndColor,
                gradientOrientation,
                stokeColor,
                stokeWidth
            )
            pressedShape.alpha = getAlpha(statePressedAlpha)
            stateListDrawable.addState(statePressed(), pressedShape)
            stateListDrawable.addState(stateEnable(), pressedShape)
        }
        stateListDrawable.addState(intArrayOf(), shape)
        return stateListDrawable
    }


    fun createGradientDrawable(
        radius: Float = 0f,
        leftTopRadius: Float = 0f,
        rightTopRadius: Float = 0f,
        rightBottomRadius: Float = 0f,
        leftBottomRadius: Float = 0f,
        solidColor: Int = 0,
        gradientStartColor: Int = 0,
        gradientEndColor: Int = 0,
        gradientOrientation: Int = 0,
        stokeColor: Int = 0,
        stokeWidth: Float = 0f
    ): GradientDrawable {
        val shape = DrawableUtils.ShapeBuilder().build()
        if (radius != 0f) {
            shape.cornerRadius = radius
        } else {
            val mRadii = floatArrayOf(
                leftTopRadius,
                leftTopRadius,
                rightTopRadius,
                rightTopRadius,
                rightBottomRadius,
                rightBottomRadius,
                leftBottomRadius,
                leftBottomRadius
            )
            shape.cornerRadii = mRadii
        }
        if (solidColor != 0) {
            shape.setColor(solidColor)
        }
        if (gradientStartColor != 0 && gradientEndColor != 0) {
            shape.orientation =
                if (gradientOrientation == 0) GradientDrawable.Orientation.LEFT_RIGHT else GradientDrawable.Orientation.TOP_BOTTOM
            shape.colors = intArrayOf(gradientStartColor, gradientEndColor)
        }
        if (stokeColor != 0) {
            shape.setStroke(ceil(stokeWidth.toDouble()).toInt(), stokeColor)
        }
        return shape
    }

    private fun getAlpha(statePressedAlpha: Float): Int {
        return (255 * statePressedAlpha).toInt()
    }

    private fun statePressed() = intArrayOf(android.R.attr.state_pressed)
    private fun stateEnable() = intArrayOf(-android.R.attr.state_enabled)
}