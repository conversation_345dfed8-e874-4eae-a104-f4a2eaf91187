package com.dz.foundation.ui.view.recycler;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Color;
import android.graphics.drawable.InsetDrawable;
import android.graphics.drawable.ShapeDrawable;
import android.graphics.drawable.shapes.RectShape;
import android.util.AttributeSet;
import android.view.MotionEvent;

import androidx.annotation.ColorInt;
import androidx.annotation.ColorRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.StaggeredGridLayoutManager;

import com.dz.foundation.base.manager.task.TaskManager;
import com.dz.foundation.ui.R;
import com.dz.foundation.ui.utils.DPUtils;
import com.dz.foundation.ui.view.recycler.decoration.DividerItemDecoration;
import com.dz.foundation.ui.view.recycler.decoration.GridSpacingItemDecoration;
import com.dz.foundation.ui.view.recycler.decoration.LinearSpacingItemDecoration;
import com.dz.foundation.ui.view.recycler.decoration.StaggeredGridItemDecoration;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;


public class DzRecyclerView extends RecyclerView implements DzRecyclerViewCellOperations {
    public static final int LINEAR_VERTICAL = 0;
    public static final int LINEAR_HORIZONTAL = 1;
    public static final int GRID = 2;
    public static final int STAGGERED_VERTICAL = 3;
    public static final int STAGGERED_HORIZONTAL = 4;

    private DzRecyclerViewAdapter mAdapter;
    private int layoutMode;
    private int gridSpanCount;
    private int spacing;
    private int verticalSpacing;
    private int horizontalSpacing;
    private boolean isSpacingIncludeEdge;
    private boolean showDivider;
    private boolean showLastDivider;
    private int dividerColor;
    private int dividerOrientation;
    private int dividerPaddingLeft;
    private int dividerPaddingRight;
    private int dividerPaddingTop;
    private int dividerPaddingBottom;
    private int dividerHeight;
    private boolean reverseLayout;
    //开启真实曝光
    private boolean openRealExpose;
    private RecyclerViewSetting mSetting;

    public DzRecyclerView(@NonNull Context context) {
        this(context, null);
    }

    public DzRecyclerView(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public DzRecyclerView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        init(attrs, defStyle);
    }

    private void init(@Nullable AttributeSet attrs, int defStyle) {
        initAttrs(attrs, defStyle);
        mAdapter = new DzRecyclerViewAdapter(getContext());
        mSetting = new RecyclerViewSetting(this);
        setHasFixedSize(true);
        setOverScrollMode(OVER_SCROLL_NEVER);
        setAdapter(mAdapter);
        initLayoutManager();
        setDecorations();
        setItemRealExpose();
    }

    public void setItemRealExpose() {
        DzRecyclerView currentRv = this;
        if (openRealExpose) {
            TaskManager.Companion.ioTask(continuation -> {
                new DzExposeRvItemUtil().setDzRecyclerItemExposeListener(currentRv);
                return null;
            });
        }
    }

    private void initAttrs(AttributeSet attrs, int defStyle) {
        TypedArray typedArray = getContext().getTheme().obtainStyledAttributes(attrs, R.styleable.DzRecyclerView, defStyle, 0);
        layoutMode = typedArray.getInt(R.styleable.DzRecyclerView_drv_layoutMode, 0);
        gridSpanCount = typedArray.getInt(R.styleable.DzRecyclerView_drv_gridSpanCount, 0);
        spacing = typedArray.getDimensionPixelSize(R.styleable.DzRecyclerView_drv_spacing, 0);
        verticalSpacing = typedArray.getDimensionPixelSize(R.styleable.DzRecyclerView_drv_verticalSpacing, 0);
        horizontalSpacing = typedArray.getDimensionPixelSize(R.styleable.DzRecyclerView_drv_horizontalSpacing, 0);
        isSpacingIncludeEdge = typedArray.getBoolean(R.styleable.DzRecyclerView_drv_isSpacingIncludeEdge, false);
        reverseLayout = typedArray.getBoolean(R.styleable.DzRecyclerView_drv_reverseLayout, false);
        showDivider = typedArray.getBoolean(R.styleable.DzRecyclerView_drv_showDivider, false);
        showLastDivider = typedArray.getBoolean(R.styleable.DzRecyclerView_drv_showLastDivider, false);
        dividerColor = typedArray.getColor(R.styleable.DzRecyclerView_drv_dividerColor, 0);
        dividerOrientation = typedArray.getInt(R.styleable.DzRecyclerView_drv_dividerOrientation, 2);
        dividerPaddingLeft = typedArray.getDimensionPixelSize(R.styleable.DzRecyclerView_drv_dividerPaddingLeft, 0);
        dividerHeight = typedArray.getDimensionPixelSize(R.styleable.DzRecyclerView_drv_dividerHeight, 0);
        dividerPaddingRight = typedArray.getDimensionPixelSize(R.styleable.DzRecyclerView_drv_dividerPaddingRight, 0);
        dividerPaddingTop = typedArray.getDimensionPixelSize(R.styleable.DzRecyclerView_drv_dividerPaddingTop, 0);
        dividerPaddingBottom = typedArray.getDimensionPixelSize(R.styleable.DzRecyclerView_drv_dividerPaddingBottom, 0);
        openRealExpose = typedArray.getBoolean(R.styleable.DzRecyclerView_drv_openRealExpose, false);
    }

    private void setDecorations() {
        if (showDivider) {
            if (dividerColor != 0) {
                showDividerInternal(dividerColor, dividerPaddingLeft, dividerPaddingTop, dividerPaddingRight, dividerPaddingBottom);
            } else {
                showDivider();
            }
        }

        if (spacing != 0) {
            setSpacingInternal(spacing, spacing, isSpacingIncludeEdge);
        } else if (verticalSpacing != 0 || horizontalSpacing != 0) {
            setSpacingInternal(verticalSpacing, horizontalSpacing, isSpacingIncludeEdge);
        }
    }

    /**
     * divider
     */
    private void showDividerInternal(@ColorInt int color,
                                     int paddingLeft, int paddingTop,
                                     int paddingRight, int paddingBottom) {
        if (layoutManager instanceof GridLayoutManager) {
            if (dividerOrientation == 0) {
                addDividerItemDecoration(color, DividerItemDecoration.HORIZONTAL,
                        paddingLeft, paddingTop, paddingRight, paddingBottom);
            } else if (dividerOrientation == 1) {
                addDividerItemDecoration(color, DividerItemDecoration.VERTICAL,
                        paddingLeft, paddingTop, paddingRight, paddingBottom);
            } else {
                addDividerItemDecoration(color, DividerItemDecoration.VERTICAL,
                        paddingLeft, paddingTop, paddingRight, paddingBottom);
                addDividerItemDecoration(color, DividerItemDecoration.HORIZONTAL,
                        paddingLeft, paddingTop, paddingRight, paddingBottom);
            }
        } else if (layoutManager instanceof LinearLayoutManager) {
            int orientation = ((LinearLayoutManager) layoutManager).getOrientation();
            addDividerItemDecoration(color, orientation,
                    paddingLeft, paddingTop, paddingRight, paddingBottom);
        }
    }

    private void addDividerItemDecoration(@ColorInt int color, int orientation,
                                          int paddingLeft, int paddingTop,
                                          int paddingRight, int paddingBottom) {
        DividerItemDecoration decor = new DividerItemDecoration(getContext(), orientation);
        if (color != 0) {
            ShapeDrawable shapeDrawable = new ShapeDrawable(new RectShape());
            shapeDrawable.setIntrinsicHeight(dividerHeight == 0 ? 1 : dividerHeight);
            shapeDrawable.setIntrinsicWidth(dividerHeight == 0 ? 1 : dividerHeight);
            shapeDrawable.getPaint().setColor(color);
            InsetDrawable insetDrawable = new InsetDrawable(shapeDrawable, paddingLeft, paddingTop, paddingRight, paddingBottom);
            decor.setDrawable(insetDrawable);
        }
        decor.setShowLastDivider(showLastDivider);
        addItemDecoration(decor);
    }


    public void showDivider() {

        showDividerInternal(Color.parseColor("#00000000"), dividerPaddingLeft, dividerPaddingTop, dividerPaddingRight, dividerPaddingBottom);

    }

    public void showDivider(@ColorRes int colorRes) {
        showDividerInternal(ContextCompat.getColor(getContext(), colorRes),
                dividerPaddingLeft, dividerPaddingTop, dividerPaddingRight, dividerPaddingBottom);
    }


    public void showDivider(@ColorRes int colorRes, int paddingLeftDp, int paddingTopDp, int paddingRightDp, int paddingBottomDp) {
        showDividerInternal(ContextCompat.getColor(getContext(), colorRes),
                DPUtils.dip2px(getContext(), paddingLeftDp), DPUtils.dip2px(getContext(), paddingTopDp),
                DPUtils.dip2px(getContext(), paddingRightDp), DPUtils.dip2px(getContext(), paddingBottomDp));
    }

    /**
     * spacing
     */
    public void setGridSpacingInternal(int verSpacing, int horSpacing, boolean includeEdge) {
        addItemDecoration(GridSpacingItemDecoration.newBuilder().verticalSpacing(verSpacing).horizontalSpacing(horSpacing).includeEdge(includeEdge).build());
    }

    public void setLinearSpacingInternal(int spacing, boolean includeEdge) {
        int orientation = ((LinearLayoutManager) layoutManager).getOrientation();
        addItemDecoration(LinearSpacingItemDecoration.newBuilder().spacing(spacing).orientation(orientation).includeEdge(includeEdge).build());
    }

    /**
     * 为瀑布流添加holder间距
     * @param verSpacing
     * @param horSpacing
     * @param includeEdge
     */
    public void setStaggeredGridSpacingInternal(int verSpacing, int horSpacing, boolean includeEdge) {
        addItemDecoration(new StaggeredGridItemDecoration(includeEdge, verSpacing, horSpacing));
    }

    private void setSpacingInternal(int verSpacing, int horSpacing, boolean includeEdge) {
        if (layoutManager instanceof GridLayoutManager) {
            setGridSpacingInternal(verSpacing, horSpacing, includeEdge);
        } else if (layoutManager instanceof LinearLayoutManager) {
            setLinearSpacingInternal(verSpacing, includeEdge);
        } else if (layoutManager instanceof StaggeredGridLayoutManager) {
            setStaggeredGridSpacingInternal(verSpacing, horSpacing, includeEdge);
        }
    }

    public RecyclerViewSetting getSetting() {
        return mSetting;
    }

    public void setLinearVerticalLayoutManager() {
        layoutMode = LINEAR_VERTICAL;
        useLinearVerticalMode();
    }

    public void setLinearHorizontalLayoutManager() {
        layoutMode = LINEAR_HORIZONTAL;
        useLinearHorizontalMode();
    }

    public void setGridLayoutManager(int spanCount) {
        layoutMode = GRID;
        gridSpanCount = spanCount;
        useGridMode(spanCount);

    }

    public void setStaggeredVerticalLayoutManager(int spanCount) {
        layoutMode = STAGGERED_VERTICAL;
        gridSpanCount = spanCount;
        useStaggeredVerticalMode(spanCount);
    }

    public void setStaggeredHorizontalLayoutManager(int spanCount) {
        layoutMode = STAGGERED_HORIZONTAL;
        gridSpanCount = spanCount;
        useStaggeredHorizontalMode(spanCount);
    }

    private void initLayoutManager() {
        if (layoutMode == GRID) {
            setGridLayoutManager(gridSpanCount);
        } else if (layoutMode == LINEAR_VERTICAL) {
            setLinearVerticalLayoutManager();
        } else if (layoutMode == LINEAR_HORIZONTAL) {
            setLinearHorizontalLayoutManager();
        } else if (layoutMode == STAGGERED_VERTICAL) {
            setStaggeredVerticalLayoutManager(gridSpanCount);
        } else if (layoutMode == STAGGERED_HORIZONTAL) {
            setStaggeredHorizontalLayoutManager(gridSpanCount);
        }
    }

    private RecyclerView.LayoutManager layoutManager;

    private class DzLinearLayoutManager extends LinearLayoutManager {

        public DzLinearLayoutManager(Context context) {
            super(context);
        }

        public DzLinearLayoutManager(Context context, int orientation, boolean reverseLayout) {
            super(context, orientation, reverseLayout);
        }

        public DzLinearLayoutManager(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
            super(context, attrs, defStyleAttr, defStyleRes);
        }

        @Override
        public void onLayoutChildren(RecyclerView.Recycler recycler, RecyclerView.State state) {
            try {
                super.onLayoutChildren(recycler, state);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private class DzGridLayoutManager extends GridLayoutManager {


        public DzGridLayoutManager(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
            super(context, attrs, defStyleAttr, defStyleRes);
        }

        public DzGridLayoutManager(Context context, int spanCount) {
            super(context, spanCount);
        }

        public DzGridLayoutManager(Context context, int spanCount, int orientation, boolean reverseLayout) {
            super(context, spanCount, orientation, reverseLayout);
        }

        @Override
        public void onLayoutChildren(RecyclerView.Recycler recycler, RecyclerView.State state) {
            try {
                super.onLayoutChildren(recycler, state);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private class DzStaggeredGridLayoutManager extends StaggeredGridLayoutManager {
        public DzStaggeredGridLayoutManager(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
            super(context, attrs, defStyleAttr, defStyleRes);
        }

        public DzStaggeredGridLayoutManager(int spanCount, int orientation) {
            super(spanCount, orientation);
        }

        @Override
        public void onLayoutChildren(RecyclerView.Recycler recycler, RecyclerView.State state) {
            try {
                super.onLayoutChildren(recycler, state);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        @Override
        public void onScrollStateChanged(int state) {
            try {
                super.onScrollStateChanged(state);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * layout modes
     */
    private void useLinearVerticalMode() {
        layoutManager = new DzLinearLayoutManager(getContext(), LinearLayoutManager.VERTICAL, reverseLayout);
        setLayoutManager(layoutManager);
    }


    private void useLinearHorizontalMode() {
        layoutManager = new DzLinearLayoutManager(getContext(), LinearLayoutManager.HORIZONTAL, reverseLayout);
        setLayoutManager(layoutManager);
    }

    private void useGridMode(int spanCount) {
        layoutManager = new DzGridLayoutManager(getContext(), spanCount);
        setLayoutManager(layoutManager);

        GridLayoutManager.SpanSizeLookup spanSizeLookup = new GridLayoutManager.SpanSizeLookup() {
            @Override
            public int getSpanSize(int position) {
                try {
                    int spanSize = mAdapter.getCell(position).getSpanSize();
                    return spanSize <= 0 ? 1 : spanSize;
                } catch (Exception e) {
                    return 1;
                }
            }
        };
        spanSizeLookup.setSpanIndexCacheEnabled(true);
        ((GridLayoutManager) layoutManager).setSpanSizeLookup(spanSizeLookup);

        if (getSetting().downRefreshCell != null) {
            getSetting().downRefreshCell.setSpanSize(spanCount);
        }

    }

    private void useStaggeredVerticalMode(int spanCount) {
        layoutManager = new DzStaggeredGridLayoutManager(spanCount, StaggeredGridLayoutManager.VERTICAL);
        setLayoutManager(layoutManager);
    }

    private void useStaggeredHorizontalMode(int spanCount) {
        layoutManager = new DzStaggeredGridLayoutManager(spanCount, StaggeredGridLayoutManager.HORIZONTAL);
        setLayoutManager(layoutManager);
    }

    @Override
    public void addCell(int atPosition, DzRecyclerViewCell cell) {
        mAdapter.addCell(atPosition, cell);
    }

    @Override
    public void addCell(DzRecyclerViewCell cell) {
        mAdapter.addCell(cell);
    }


    @Override
    public void addCells(List<? extends DzRecyclerViewCell> cells) {
        mAdapter.addCells(cells);
    }

    @Override
    public void insertedCells(int atPosition, List<? extends DzRecyclerViewCell> cells) {
        mAdapter.insertedCells(atPosition, cells);
    }

    @Override
    public void addOrUpdateCell(DzRecyclerViewCell cell) {
        mAdapter.addOrUpdateCell(cell);
    }

    @Override
    public void addOrUpdateCells(List<DzRecyclerViewCell> cells) {
        mAdapter.addOrUpdateCells(cells);
    }

    @Override
    public void removeAllCells() {
        //判断一下是否开启了下拉刷新
        if (mSetting.isDownRefreshEnabled && getAllCells().size() > 0 && getCell(0).equals(getSetting().downRefreshCell)) {
            if (getAllCells().size() <= 1) {
                return;
            }
            mAdapter.removeCells(1, getAllCells().size() - 1);
        } else {
            mAdapter.removeAllCells();
        }
    }

    public void removeCellsFormPosition(int fromPosition) {
        if (getAllCells().isEmpty()) {
            return;
        }
        if (fromPosition < 0 || fromPosition >= getAllCells().size() - 1) {
            return;
        }
        mAdapter.removeCells(fromPosition, getAllCells().size() - 1);
    }


    @Override
    public void updateCell(DzRecyclerViewCell cell, Object payloads) {
        mAdapter.updateCell(cell, payloads);
    }

    @Override
    public void updateCell(int atPosition, Object payloads) {
        mAdapter.updateCell(atPosition, payloads);
    }

    @Override
    public void updateCells(int fromPosition, int toPosition, List<Object> payloads) {
        mAdapter.updateCells(fromPosition, toPosition, payloads);
    }

    @Override
    public void removeCell(DzRecyclerViewCell cell) {
        mAdapter.removeCell(cell);
    }

    @Override
    public void removeCell(int atPosition) {
        mAdapter.removeCell(atPosition);
    }

    @Override
    public void removeCells(List<? extends DzRecyclerViewCell> cells) {
        mAdapter.removeCells(cells);
    }


    public DzRecyclerViewAdapter getAdapter() {
        return mAdapter;
    }


    public DzRecyclerViewCell getCell(int position) {
        return mAdapter.getCell(position);
    }

    public ArrayList<DzRecyclerViewCell> getAllCells() {
        return mAdapter.getAllCells();
    }


    public boolean isEmpty() {
        return getItemCount() <= 0;
    }


    public int getItemCount() {
        return mAdapter.getItemCount();
    }


    public void doDownRefresh() {
        mSetting.doDownRefresh();
    }

    /**
     * 刷新完成调用
     *
     * @param dataSize 返回的数据大小  如果本次请求未返回数据 传 null
     */
    public void setOnDownRefreshCompleted(int dataSize, boolean hasMore) {
        mSetting.setOnDownRefreshCompleted(dataSize, hasMore);
    }


    /**
     * 上拉加载完成调用此代码
     *
     * @param hasMore 是否还有更多
     */
    public void setOnUpLoadMoreCompleted(int dataSize, boolean hasMore) {
        mSetting.setOnUpLoadMoreCompleted(hasMore);
    }

    public int getGridSpanCount() {
        return gridSpanCount;
    }

    public void setGridSpanCount(int spanCount) {
        this.gridSpanCount = spanCount;
        if (layoutManager instanceof GridLayoutManager) {
            ((GridLayoutManager) layoutManager).setSpanCount(gridSpanCount);
        }
    }

    public int getFirstVisibleItemPosition() {
        try {
            if (layoutManager instanceof GridLayoutManager) {
                return ((GridLayoutManager) layoutManager).findFirstVisibleItemPosition();
            } else if (layoutManager instanceof LinearLayoutManager) {
                return ((LinearLayoutManager) layoutManager).findFirstVisibleItemPosition();
            } else if (layoutManager instanceof StaggeredGridLayoutManager) {
                return ((StaggeredGridLayoutManager) layoutManager).findFirstVisibleItemPositions(null)[0];
            } else {
                return -1;
            }
        } catch (Exception e) {
            return -1;
        }
    }

    public int getLastVisibleItemPosition() {
        try {
            if (layoutManager instanceof GridLayoutManager) {
                return ((GridLayoutManager) layoutManager).findLastVisibleItemPosition();
            } else if (layoutManager instanceof LinearLayoutManager) {
                return ((LinearLayoutManager) layoutManager).findLastVisibleItemPosition();
            } else if (layoutManager instanceof StaggeredGridLayoutManager) {
                return ((StaggeredGridLayoutManager) layoutManager).findLastVisibleItemPositions(null)[0];
            } else {
                return -1;
            }
        } catch (Exception e) {
            return -1;
        }
    }


    float mLastY = -1;

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        if (mLastY == -1) {
            mLastY = event.getRawY();
        }
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                mLastY = event.getRawY();
                break;
            case MotionEvent.ACTION_MOVE:
                final float deltaY = event.getRawY() - mLastY;
                mLastY = event.getRawY();
                if (mSetting.mRefreshHeader != null && mSetting.mRefreshHeader.isOnTop() && mSetting.isDownRefreshEnabled && mSetting.mRefreshHeader.getState() < mSetting.mRefreshHeader.STATE_REFRESHING) {
                    mSetting.mRefreshHeader.onMove(deltaY / 3);
                    if (mSetting.mRefreshHeader.getVisibleHeight() > 0 && mSetting.mRefreshHeader.getState() < mSetting.mRefreshHeader.STATE_REFRESHING) {
                        return false;
                    }
                }
                break;
            default:
                mLastY = -1; // reset
                if (mSetting.mRefreshHeader != null && mSetting.mRefreshHeader.isOnTop() && mSetting.isDownRefreshEnabled) {
                    if (mSetting.mRefreshHeader.releaseAction()) {
                        //回调刷新方法
                        if (mSetting.onDownRefreshListener != null) {
                            mSetting.onDownRefreshListener.onRefresh(new DownRefreshNotify(this));
                        }
                    }
                }
                break;

        }
        return super.onTouchEvent(event);
    }


    private List<String> noDividerCellTypes;

    public void setNoDividerForCellType(Class<? extends DzRecyclerViewItem>... classes) {
        if (noDividerCellTypes == null) {
            noDividerCellTypes = new ArrayList<>();
        }

        for (Class<?> aClass : classes) {
            noDividerCellTypes.add(aClass.getName());
        }
    }


    public List<String> getNoDividerCellTypes() {
        return noDividerCellTypes == null ? Collections.<String>emptyList() : noDividerCellTypes;
    }

    public DownRefreshNotify downRefreshNotify() {
        return new DownRefreshNotify(this);
    }

    public LoadMoreNotify loadMoreNotify() {
        return new LoadMoreNotify(this);
    }

    public void notifyDataSetChanged() {
        getAdapter().notifyDataSetChanged();
    }
}
