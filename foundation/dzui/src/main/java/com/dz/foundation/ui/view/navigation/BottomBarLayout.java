package com.dz.foundation.ui.view.navigation;

import android.content.Context;
import android.util.AttributeSet;
import android.util.SparseArray;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;

import androidx.fragment.app.Fragment;

import com.dz.foundation.base.utils.LogUtil;
import com.dz.foundation.ui.widget.DzFrameLayout;

import java.io.Serializable;
import java.util.ArrayList;


/**
 *
 */

public class BottomBarLayout extends DzFrameLayout implements View.OnClickListener, STabLayout {

    private OnTabTouchListener mOnTabTouchListener;

    public BottomBarLayout(Context context) {
        this(context, null);
    }

    public BottomBarLayout(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public BottomBarLayout(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView(context, attrs);
    }

    private ArrayList<TabItemBean> mTabItemBeans;
    private LinearLayout tabContainer;
    private int tabContainerId;

    private int checkedPosition = -1;
    private int default_checked_Position = 0;

    SparseArray<View> sparseArray = new SparseArray<>();

    private void initView(Context context, AttributeSet attrs) {

//        float elevation = DPUtils.dip2px(getContext(), 8);
//        ViewCompat.setElevation(this, elevation);
        setClipToPadding(false);
        LayoutParams layoutParams = new LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
        tabContainer = new LinearLayout(getContext());
        tabContainer.setLayoutParams(layoutParams);
        tabContainer.setOrientation(LinearLayout.HORIZONTAL);
        this.addView(tabContainer);

    }

    @Override
    public void onClick(View view) {
        if (mBlock) {
            return;
        }
        int position = (int) view.getTag();
        setSelect(position,true);
    }

    private boolean mBlock = false;

    public void blockClick(Boolean block) {
        mBlock = block;
    }

    public void addTabItems(ArrayList<TabItemBean> tabItemBeans) {
        mTabItemBeans = tabItemBeans;
        int size = tabItemBeans.size();
        NavigationTabView tabView;
        sparseArray.clear();
        tabContainer.removeAllViews();
        for (int i = 0; i < size; i++) {
            TabItemBean tabItemBean = tabItemBeans.get(i);
            tabView = new NavigationTabView(getContext());
            
            // 设置Tab样式模式
            if (tabItemBean.tabStyle != null) {
                tabView.setTabStyle(tabItemBean.tabStyle);
            }
            
            tabView.setTabIconRes(tabItemBean.icon_res_unselected, tabItemBean.icon_res_selected);
            tabView.setTabText(tabItemBean.tabText);
            tabView.setTabStateColorRes(tabItemBean.text_color_unselected, tabItemBean.text_color_selected);

            if (tabView instanceof NavigationAble) {
                if (checkedPosition < 0) {
                    checkedPosition = default_checked_Position;
                }
                if (i == checkedPosition) {
                    ((NavigationAble) tabView).select(tabItemBean);
                    onTabSelected(i, tabView,false);
                } else {
                    ((NavigationAble) tabView).unSelect(tabItemBean);
                }
//                ((NavigationAble) tabView).hideNewMessage();
            }
            tabView.setTag(i);
            sparseArray.put(i, tabView);
            tabView.setOnClickListener(BottomBarLayout.this);
            onTouch(tabView);
            LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(0, LayoutParams.MATCH_PARENT);
            layoutParams.weight = 1;
            if (tabItemBean.hide) {
                tabView.setVisibility(View.GONE);
            }
            tabContainer.addView(tabView, layoutParams);
        }
    }

    /**
     * @param index
     * @param isNormal 当前底部tab样式，默认黑色，false、白色；
     */
    public void setNewMessageStroke(int index, Boolean isNormal) {
        if (index >= sparseArray.size()) {
            return;
        }
        View tab = sparseArray.get(index);
        if (tab instanceof NavigationTabView) {
            ((NavigationTabView) tab).setNewMessageStroke(isNormal);
        }
    }

    public void setTabVisibility(int index, int visibility) {
        View tab = sparseArray.get(index);
        if (tab instanceof NavigationTabView) {
            tab.setVisibility(visibility);
        }
    }

    public void setTabText(int index, String title) {
        View tab = sparseArray.get(index);
        if (tab instanceof NavigationTabView) {
            NavigationTabView tabView = (NavigationTabView) tab;
            tabView.setTabText(title);
        }
    }

    /**
     * 显示小红点
     */
    public void showNewMessage(int position) {
        View view = sparseArray.get(position);
        if (view != null && view instanceof NavigationAble) {
            ((NavigationAble) view).showNewMessage();
        }
    }

    /**
     * 隐藏小红点
     */
    public void hideNewMessage(int position) {
        View view = sparseArray.get(position);
        if (view != null && view instanceof NavigationAble) {
            ((NavigationAble) view).hideNewMessage();
        }
    }

    /**
     * 显示带内容的小红点
     */
    public void showNewMessage(int position, String msg) {
        View view = sparseArray.get(position);
        if (view != null && view instanceof NavigationAble) {
            ((NavigationAble) view).showNewMessage(msg);
        }
    }

    /**
     * 设置小红点自动隐藏
     */
    public void setShowMessageAlways(int position, boolean show) {
        View view = sparseArray.get(position);
        if (view instanceof NavigationAble) {
            ((NavigationAble) view).setShowMessageAlways(show);
        }
    }

    private ArrayList<OnTabSelectedListener> tabSelectedListeners = new ArrayList<OnTabSelectedListener>();

    @Override
    public void addOnTabSelectedListener(
            OnTabSelectedListener onTabSelectedListener) {
        if (tabSelectedListeners.contains(onTabSelectedListener)) {
            return;
        }
        LogUtil.d("BOTTOM_BAR_TAG", "addOnTabSelectedListener");
        tabSelectedListeners.add(onTabSelectedListener);

    }

    @Override
    public void removeOnTabSelectedListener(
            OnTabSelectedListener onTabSelectedListener) {
        LogUtil.d("BOTTOM_BAR_TAG", "removeOnTabSelectedListener");
        tabSelectedListeners.remove(onTabSelectedListener);

    }


    public void setSelect(int position, boolean isClick) {
        LogUtil.d("BOTTOM_BAR_TAG", "setSelect  position==" + position + "\n  checkedPosition==" + checkedPosition);
        if (position == checkedPosition) {
            onTabReselected(position, isClick);
            return;
        }
        View view = sparseArray.get(position);
        if ((view != null) && (view instanceof NavigationAble) && (position != checkedPosition)) {
            View checkedView = sparseArray.get(checkedPosition);
            if ((checkedView != null) && (checkedView instanceof NavigationAble)) {
                ((NavigationAble) checkedView).unSelect(mTabItemBeans.get(position));
                onTabUnselected(checkedPosition);
            }
            ((NavigationAble) view).select(mTabItemBeans.get(position));
//            if (mListener != null) {
//                mListener.onTabSelect(view, position, checkedPosition);
//            }
            onTabSelected(position, view, isClick);
        }
        checkedPosition = position;
    }


    public void onTabSelected(int position, View view, boolean isClick) {
        this.setBackgroundResource(mTabItemBeans.get(position).tab_bg_color);
        LogUtil.d("BOTTOM_BAR_TAG", "onTabSelected,position==" + position);
        for (OnTabSelectedListener tabSelectedListener : tabSelectedListeners) {
            tabSelectedListener.onTabSelected(position, view, isClick);
        }
    }


    public void onTabUnselected(int position) {
        for (OnTabSelectedListener tabSelectedListener : tabSelectedListeners) {
            tabSelectedListener.onTabUnselected(position);
        }
    }

    private long clickTime = 0;

    public void onTabReselected(int position,boolean isClick) {
        if (position == checkedPosition) {
            if ((System.currentTimeMillis() - clickTime) > 500) {
                clickTime = System.currentTimeMillis();
            } else {
                for (OnTabSelectedListener tabSelectedListener : tabSelectedListeners) {
                    tabSelectedListener.onDoubleClick(position);
                }
            }
        }
        for (OnTabSelectedListener tabSelectedListener : tabSelectedListeners) {
            tabSelectedListener.onTabReselected(position, isClick);
        }
    }

    public void setOnTouchListener(OnTabTouchListener listener) {
        this.mOnTabTouchListener = listener;
    }

    public void onTouch(View view) {
        final Long[] actionDownTime = {0L};
        final Boolean[] seekBarTakeOver = {false};
        view.setOnTouchListener(new OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                int action = event.getAction();
                if (action == MotionEvent.ACTION_DOWN) {
                    actionDownTime[0] = System.currentTimeMillis();
                    seekBarTakeOver[0] = false;
                    if (mOnTabTouchListener != null) {
                        mOnTabTouchListener.onTabTouch(event);
                    }
                } else if (action == MotionEvent.ACTION_MOVE) {
                    Boolean result = false;
                    if (System.currentTimeMillis() - actionDownTime[0] > 300) {
                        seekBarTakeOver[0] = true;
                        if (mOnTabTouchListener != null) {
                            mOnTabTouchListener.onTabTouch(event);
                        }
                        result = true;
                    }
                    return result;
                } else {
                    if (seekBarTakeOver[0]) {
                        if (mOnTabTouchListener != null) {
                            mOnTabTouchListener.onTabTouch(event);
                        }
                        return true;
                    }
                }
                return false;
            }
        });
    }

    @Override
    public int getCurrentTabPosition() {
        return checkedPosition;
    }

    public static class TabItemBean implements Serializable {
        //tab 对应的ui
        public Fragment tab_fragment;

        //标题
        public String tabText;
        //选中图片资源
        public int icon_res_selected;
        //未选中图片资源
        public int icon_res_unselected;

        //选中字体颜色
        public int text_color_selected;

        //未选中字体颜色
        public int text_color_unselected;
        public boolean isSelected;
        public String tabName;

        //菜单栏背景颜色
        public int tab_bg_color;

        public boolean hide = false;

        public long tabId;

        // Tab样式模式：NavigationTabView.STYLE_ICON_TEXT 或 NavigationTabView.STYLE_TEXT_ONLY
        public Integer tabStyle = NavigationTabView.STYLE_ICON_TEXT;
    }

    /**
     * 获取指定位置的 TabItem 对应的 View
     *
     * @param position TabItem 的位置
     * @return 对应位置的 View
     */
    public View getTabViewAtPosition(int position) {
        return sparseArray.get(position);
    }

    /**
     * 设置指定位置Tab的样式模式
     * @param style NavigationTabView.STYLE_ICON_TEXT 或 NavigationTabView.STYLE_TEXT_ONLY
     */
    public void setTabStyle(int style,int welfareBottomBarStyle) {
        for (int i = 0; i < sparseArray.size(); i++) {
            View view = sparseArray.valueAt(i);
            if (view instanceof NavigationTabView) {
                NavigationTabView tabView = (NavigationTabView) view;
                if ("福利".equals(tabView.getTabText())) {
                    tabView.setTabStyle(welfareBottomBarStyle);
                } else {
                    tabView.setTabStyle(style);
                }
            }
        }
    }
}
