package com.dz.foundation.ui.view.recycler;

import android.content.Context;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;

import androidx.recyclerview.widget.RecyclerView;

import com.dz.foundation.ui.R;
import com.dz.foundation.ui.view.custom.ActionListener;
import com.dz.foundation.ui.view.custom.ActionListenerOwner;

import java.lang.reflect.Constructor;

public class DzRecyclerViewCell<VD> implements Updatable {

    private VD viewData;

    private long itemId = 0l;

    public DzRecyclerViewCell() {
        this.itemId = ItemIds.nextId();
    }


    public VD getViewData() {
        return viewData;
    }

    public DzRecyclerViewCell<VD> setViewData(VD viewData) {
        this.viewData = viewData;
        return this;
    }

    protected ActionListener viewListener;

    public DzRecyclerViewCell<VD> setActionListener(ActionListener listener) {
        this.viewListener = listener;
        return this;
    }

    private Class<? extends DzRecyclerViewItem> viewClass;

    public Class<? extends DzRecyclerViewItem> getViewClass() {
        return viewClass;
    }

    public DzRecyclerViewCell<VD> setViewClass(Class<? extends DzRecyclerViewItem> viewClass) {
        this.viewClass = viewClass;
        return this;
    }


    //当直接复用其它自定义View时，覆写此方法，通常直接覆写 getLayoutRes方法走默认实现即可
    protected View onCreateView(ViewGroup parent) {
        try {
            if (viewClass != null) {
                View view = createViewByViewClass(parent, viewClass, getViewData());

                if (view == null) {
                    Constructor constructor = viewClass.getConstructor(Context.class);
                    view = (View) constructor.newInstance(parent.getContext());
                }
                if (view instanceof ActionListenerOwner) {
                    ((ActionListenerOwner) view).setActionListener(viewListener);
                }
                return view;

            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    protected View createViewByViewClass(ViewGroup parent, Class viewClass, VD VD) {
        View view = null;
        try {
            Constructor constructor = viewClass.getConstructor(Context.class);
            view = (View) constructor.newInstance(parent.getContext());
        } catch (Exception e) {
            e.printStackTrace();
        }

        return view;
    }

    private DzRecyclerView recyclerView;

    public DzRecyclerView getRecyclerView() {
        return recyclerView;
    }

    protected DzRecyclerViewAdapter.AdapterCellHolder onCreateViewHolder(DzRecyclerView parent) {
        View itemView = onCreateView(parent);
        RecyclerView.LayoutParams recyclerViewItemLayoutParams = ((DzRecyclerViewItem) itemView).onCreateRecyclerViewItem(parent, itemView);
        itemView.setLayoutParams(recyclerViewItemLayoutParams);
        return new DzRecyclerViewAdapter.AdapterCellHolder(itemView, parent);
    }

    protected void onBindViewHolder(DzRecyclerViewAdapter.AdapterCellHolder holder, View itemView, int position, Context context, VD model) {
        if (recyclerView == null) {
            recyclerView = holder.recyclerView;
        }
//        Log.d("onBindViewHolder1:", this.getViewClass().getName());
//        Log.d("onBindViewHolder1:", " 23:" + recyclerView == null ? "true" : "false");
        itemView.setTag(R.id.dzui_rv_item_position,position);
        ((DzRecyclerViewItem) itemView).onBindRecyclerViewItem(model, position);
    }

    private int spanSize = 1;

    public int getSpanSize() {
        return spanSize;
    }

    public void setSpanSize(int spanSize) {
        this.spanSize = spanSize;

    }


    public long getItemId() {

        return this.itemId;
    }

    @Override
    public boolean areContentsTheSame(Object newItem) {
        return false;
    }

    @Override
    public Object getChangePayload(Object newItem) {
        return newItem;
    }


    /**
     * 刷新数据
     *
     * @param viewData
     */
    public void update(VD viewData) {
        if (recyclerView != null) {
            recyclerView.updateCell(this, viewData);
        }
    }

    public static class ItemIds {
        private static long currentMaxId = 0L;

        public static synchronized long nextId() {
            currentMaxId += 1;
            return currentMaxId;
        }

    }
}
