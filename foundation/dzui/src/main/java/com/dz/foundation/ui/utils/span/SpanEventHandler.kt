package com.dz.foundation.ui.utils.span

import android.content.Context
import android.text.SpannableString
import android.text.style.StyleSpan

/**
 * @Author: guyh
 * @Date: 2023/1/4 20:14
 * @Description:
 * @Version:1.0
 */
/**
 * @param context 上下文
 * @param clickableStr 高亮点击的文字
 * @param color 高亮的颜色
 * @param listener 点击监听  默认为null，表示不设置监听
 * @param underlineText 是否有下划线，true：默认，显示下划线；false：不显示下划线
 * @return 返回SpannableString
 */
fun String.setSpan(
    context: Context,
    clickableStr: String,
    listener: OnSpanClickListener? = null,
    color: Int? = 0,
    underlineText: Boolean? = true,
    absoluteSizeSpan: Int? = 0,
    boldSpan: StyleSpan? = null,
    lineHeightSpan: Int? = 0
): SpannableString {
    return SpannableUtil.setSpan(
        context,
        this,
        color,
        underlineText,
        clickableStr,
        listener,
        absoluteSizeSpan,
        boldSpan,
        lineHeightSpan,
    )
}

/**
 *
 * @param context 上下文
 * @param clickableStr 高亮点击的文字
 * @param listener 点击监听  默认为null，表示不设置监听
 * @param color 高亮的颜色 默认为0，表示不设置高亮颜色
 * @param underlineText 是否有下划线，true：默认，显示下划线；false：不显示下划线
 * @return 返回SpannableString
 */
fun SpannableString.setSpan(
    context: Context,
    clickableStr: String,
    listener: OnSpanClickListener? = null,
    color: Int? = 0,
    underlineText: Boolean? = true,
    absoluteSizeSpan: Int? = 0,
    boldSpan: StyleSpan? = null,
    lineHeightSpan: Int? = 0
): SpannableString {
    return SpannableUtil.setSpan(
        context,
        this,
        color,
        underlineText,
        clickableStr,
        listener,
        absoluteSizeSpan,
        boldSpan,
        lineHeightSpan,
    )
}