package com.dz.foundation.ui.view.recycler;

import android.content.Context;
import android.util.AttributeSet;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

import com.dz.foundation.ui.R;
import com.dz.foundation.ui.widget.DzFrameLayout;

public class EmptyView extends DzFrameLayout implements DzRecyclerViewItem {
    public EmptyView(@NonNull Context context) {
        this(context, null);
    }

    public EmptyView(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public EmptyView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs, defStyleAttr);
    }


    protected void init(Context context, AttributeSet attrs, int defStyleAtt) {
        initAttrs(attrs);
        loadView();
        initData();
        initView();
    }


    protected void initAttrs(AttributeSet attrs) {

    }

    protected void loadView() {
        inflate(this.getContext(), R.layout.dzui_empty_view, this);
    }

    protected void initData() {

    }

    protected void initView() {

    }

    @Override
    public RecyclerView.LayoutParams onCreateRecyclerViewItem(DzRecyclerView parent, View itemView) {
        RecyclerView.LayoutParams layoutParams = new RecyclerView.LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT);
        return layoutParams;
    }

    @Override
    public void onBindRecyclerViewItem(Object model, int position) {

    }
}
