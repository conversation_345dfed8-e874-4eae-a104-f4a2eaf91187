package com.dz.foundation.ui.utils.click

import android.view.View

import java.util.ArrayList

object GlobalListenerManager {


    var listeners: ArrayList<View.OnClickListener> = ArrayList()
    fun addListener(listener: View.OnClickListener) {
        if (listeners.contains(listener)) {
            return
        }
        listeners.add(listener)
    }

    fun doClick(view: View) {
        if (listeners.size == 0) {
            return
        }
        for (listener in listeners) {
            listener.onClick(view)
        }
    }


}