package com.dz.foundation.ui.widget

import android.content.Context
import android.util.AttributeSet
import androidx.core.widget.NestedScrollView

class DzNestedScrollView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : NestedScrollView(context, attrs, defStyleAttr), DzWidget {

    init {
        overScrollMode = OVER_SCROLL_NEVER
        initShapeBackground(context, attrs, defStyleAttr)
    }
}
