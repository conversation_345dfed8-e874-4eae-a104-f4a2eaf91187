package com.dz.foundation.ui.view.recycler;

import java.util.List;

public interface DzRecyclerViewCellOperations {
    void addCell(int atPosition, DzRecyclerViewCell cell);

    void addCell(DzRecyclerViewCell cell);

    void addCells(List<? extends DzRecyclerViewCell> cells);

    void insertedCells(int atPosition, List<? extends DzRecyclerViewCell> cells);

    void addOrUpdateCell(DzRecyclerViewCell cell);

    void addOrUpdateCells(List<DzRecyclerViewCell> cells);

    void removeCell(DzRecyclerViewCell cell);

    void removeCell(int atPosition);

    void removeCells(List<? extends DzRecyclerViewCell> cells);

    void removeAllCells();

    void updateCell(DzRecyclerViewCell cell, Object payloads);

    void updateCell(int atPosition, Object payloads);

    void updateCells(int fromPosition, int toPosition, List<Object> payloads);
}
