package com.dz.foundation.ui.widget

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import android.widget.FrameLayout


open class DzFrameLayout @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr), DzWidget {
    private var mOnItemTouchListener: OnItemTouchListener? = null

    init {
        this.initShapeBackground(context, attrs, defStyleAttr)
    }
    var onSizeChangedListener: OnSizeChangedListener? = null

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        onSizeChangedListener?.onSizeChanged(w, h, oldw, oldh)
    }

    interface OnSizeChangedListener {
        fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int)
    }

    override fun onInterceptTouchEvent(ev: MotionEvent): Boolean {
        return mOnItemTouchListener?.onInterceptTouchEvent(ev) ?: false
    }

    fun addOnItemTouchListener(listener: OnItemTouchListener) {
        mOnItemTouchListener = listener
    }
}

interface OnItemTouchListener {
    fun onInterceptTouchEvent(e: MotionEvent): Boolean
}