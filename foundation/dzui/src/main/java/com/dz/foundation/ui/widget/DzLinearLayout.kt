package com.dz.foundation.ui.widget

import android.content.Context
import android.util.AttributeSet

import android.widget.LinearLayout


open class DzLinearLayout @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : LinearLayout(context, attrs, defStyleAttr), DzWidget {

    init {
        this.initShapeBackground(context, attrs, defStyleAttr)
    }
}