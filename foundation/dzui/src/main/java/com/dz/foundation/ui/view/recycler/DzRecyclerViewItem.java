package com.dz.foundation.ui.view.recycler;

import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;

import androidx.recyclerview.widget.RecyclerView;

import com.dz.foundation.base.utils.LogUtil;
import com.dz.foundation.ui.R;

import java.util.HashSet;

/**
 * @Description: recyclerView  itemView  标识接口
 * @Author: shidz
 * @CreateDate: 2020/12/15 16:35
 */
public interface DzRecyclerViewItem<M> {

    /**
     * 做为recyclerView 被创建
     *
     * @param parent
     * @param itemView
     * @return
     */
    default RecyclerView.LayoutParams onCreateRecyclerViewItem(DzRecyclerView parent, View itemView) {
        return new RecyclerView.LayoutParams(FrameLayout.LayoutParams.MATCH_PARENT, FrameLayout.LayoutParams.WRAP_CONTENT);
    }

    /**
     * 绑定数据
     *
     * @param model
     * @param position
     */
    void onBindRecyclerViewItem(M model, int position);

    default int getRecyclerViewItemPosition() {
        if (this instanceof View) {
            View itemView = (View) this;
            Object tag = itemView.getTag(R.id.dzui_rv_item_position);
            if (tag != null && tag instanceof Integer) {
                LogUtil.d("DzRecyclerViewItem", "getRecyclerViewItemPosition ddsdf  position=" + tag);
                return ((Integer) tag);
            }
            if (itemView.getParent() instanceof RecyclerView) {
                RecyclerView recyclerView = (RecyclerView) itemView.getParent();
                return recyclerView.getChildAdapterPosition(itemView);
            }

        }
        return -1;
    }

    default DzRecyclerViewCell getRecyclerCell() {
        int recyclerViewItemPosition = getRecyclerViewItemPosition();
        if (recyclerViewItemPosition < 0) {
            return null;
        }
        DzRecyclerView recyclerView = getRecyclerView();
        if (recyclerView == null) {
            return null;
        }
        return recyclerView.getCell(recyclerViewItemPosition);
    }

    default DzRecyclerView getRecyclerView() {
        if (this instanceof View) {
            View itemView = (View) this;
            if (itemView.getParent() instanceof RecyclerView) {
                return (DzRecyclerView) itemView.getParent();
            }
        }
        return null;
    }

    default void onExpose(boolean isFirstExpose) {
    }


    //判断赋值曝光是否是首次
    default void decideExposeView() {
        if (this instanceof View) {
            View itemView = (View) this;
            long itemId = getRecyclerCell().getItemId();
            Object tagHashSet = itemView.getTag(R.id.dzui_rv_item_first_expose);
            boolean isFirstExpose;
            if (tagHashSet == null) {
                isFirstExpose = true;
                HashSet<Long> saveTag = new HashSet<>();
                saveTag.add(itemId);
                itemView.setTag(R.id.dzui_rv_item_first_expose, saveTag);
            } else {
                HashSet<Long> saveTag = (HashSet<Long>) tagHashSet;
                if (saveTag.contains(itemId)) {
                    isFirstExpose = false;
                } else {
                    isFirstExpose = true;
                    saveTag.add(itemId);
                    itemView.setTag(R.id.dzui_rv_item_first_expose, saveTag);
                }
            }
            onExpose(isFirstExpose);
        }

    }

    default void nestExpose(DzRecyclerView nestRv) {
        DzExposeRvItemUtil exposeUtil = new DzExposeRvItemUtil();
        exposeUtil.setDzRecyclerItemExposeListener(nestRv);
        exposeUtil.handleCurrentVisibleItems();
    }


    default DzRecyclerView getNestRecyclerView(View view) {
        if (null == view) {
            return null;
        }
        if (view instanceof ViewGroup) {
            ViewGroup itemView = (ViewGroup) view;
            for (int i = 0; i < itemView.getChildCount(); i++) {
                View child = itemView.getChildAt(i);
                if (child instanceof DzRecyclerView) {
                    return (DzRecyclerView) child;
                } else {
                    if (child instanceof ViewGroup) {
                        DzRecyclerView nestRv = getNestRecyclerView(child);
                        if (nestRv != null) {
                            return nestRv;
                        }
                    }
                }
            }
        }
        return null;
    }

}