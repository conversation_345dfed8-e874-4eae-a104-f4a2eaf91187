package com.dz.foundation.ui.widget

import android.annotation.SuppressLint
import android.content.Context
import android.content.res.ColorStateList
import android.graphics.drawable.GradientDrawable
import android.util.AttributeSet
import android.view.View
import android.widget.Button
import android.widget.TextView
import com.dz.foundation.ui.R
import com.dz.foundation.ui.utils.DrawableUtils
import com.dz.foundation.ui.utils.StateListDrawableUtils
import kotlin.math.ceil


/**
 * @Description: dzWidget 标识 接口，可以声明定义一些通用的东西 便于以后拓展
 * @Author: shidz
 * @CreateDate: 2020/11/30 17:31
 */
interface DzWidget {
    @SuppressLint("RestrictedApi")
    fun initShapeBackground(context: Context, attrs: AttributeSet?, defStyleAttr: Int) {
        val a = context.obtainStyledAttributes(attrs, R.styleable.DzFrameLayout, defStyleAttr, 0)
        if (a != null) {
            val shapeType = a.getInt(R.styleable.DzFrameLayout_shape, 0)
            val solidColor = a.getColor(R.styleable.DzFrameLayout_shape_solid_color, 0)
            val mStokeColor = a.getColor(R.styleable.DzFrameLayout_shape_stroke_color, 0)
            val radius =
                a.getDimensionPixelSize(R.styleable.DzFrameLayout_shape_radius, 0).toFloat()
            val leftTopRadius =
                a.getDimensionPixelSize(R.styleable.DzFrameLayout_shape_left_top_radius, 0)
                    .toFloat()
            val rightTopRadius =
                a.getDimensionPixelSize(R.styleable.DzFrameLayout_shape_right_top_radius, 0)
                    .toFloat()
            val leftBottomRadius =
                a.getDimensionPixelSize(R.styleable.DzFrameLayout_shape_left_bottom_radius, 0)
                    .toFloat()
            val rightBottomRadius =
                a.getDimensionPixelSize(R.styleable.DzFrameLayout_shape_right_bottom_radius, 0)
                    .toFloat()
            val mStokeWidth = a.getDimension(R.styleable.DzFrameLayout_shape_stroke_width, 0f)

            //渐变设置
            val gradientOrientation =
                a.getInt(R.styleable.DzFrameLayout_shape_solid_gradient_orientation, 0)
            val gradientStartColor =
                a.getInt(R.styleable.DzFrameLayout_shape_solid_gradient_start_color, 0)
            val gradientEndColor =
                a.getInt(R.styleable.DzFrameLayout_shape_solid_gradient_end_color, 0)

            //按下与正常的透明度
            val statePressedAlpha =
                a.getFloat(R.styleable.DzFrameLayout_shape_selector_state_pressed_alpha, 0f)
            val stateListDrawable = StateListDrawableUtils.ShapeBuilder().build()
            val shape = getShape(
                shapeType,
                radius,
                leftTopRadius,
                rightTopRadius,
                rightBottomRadius,
                leftBottomRadius,
                solidColor,
                gradientStartColor,
                gradientEndColor,
                gradientOrientation,
                mStokeColor,
                mStokeWidth
            )
            if (statePressedAlpha != 0f) {
                val pressedShape = getShape(
                    shapeType,
                    radius,
                    leftTopRadius,
                    rightTopRadius,
                    rightBottomRadius,
                    leftBottomRadius,
                    solidColor,
                    gradientStartColor,
                    gradientEndColor,
                    gradientOrientation,
                    mStokeColor,
                    mStokeWidth
                )
                pressedShape.alpha = getAlpha(statePressedAlpha)
                stateListDrawable.addState(statePressed(), pressedShape)
                if (this is TextView || this is Button) {
                    var mView = this as TextView
                    var textColors = mView.textColors
                    if (textColors != null && !textColors.isStateful) {
                        mView.setTextColor(
                            getColorStateList(
                                textColors,
                                getAlpha(statePressedAlpha)
                            )
                        )
                    }
                }
            }
            stateListDrawable.addState(intArrayOf(), shape)
            val isDefault =
                shapeType == 0 && solidColor == 0 && mStokeColor == 0 && radius == 0f && leftTopRadius == 0f && rightTopRadius == 0f && leftBottomRadius == 0f && rightBottomRadius == 0f && mStokeWidth == 0f && gradientOrientation == 0 && (gradientStartColor == 0 || gradientEndColor == 0)
            if (!isDefault) {
                (this as View).background = stateListDrawable
            }
        }
    }

    fun getAlpha(statePressedAlpha: Float): Int {
        return (255 * statePressedAlpha).toInt()
    }

    fun getColorStateList(
        textColors: ColorStateList,
        alpha: Int
    ): ColorStateList {
        var defaultColor = textColors.defaultColor
        var pressedColor = textColors.withAlpha(alpha).defaultColor
        val states = arrayOfNulls<IntArray>(2)
        val colors = IntArray(2)
        states[0] = statePressed()
        colors[0] = pressedColor
        states[1] = IntArray(0)
        colors[1] = defaultColor
        return ColorStateList(states, colors)
    }

    fun statePressed() = intArrayOf(android.R.attr.state_pressed)

    fun getShape(
        shapeType: Int,
        radius: Float,
        leftTopRadius: Float,
        rightTopRadius: Float,
        rightBottomRadius: Float,
        leftBottomRadius: Float,
        solidColor: Int,
        gradientStartColor: Int,
        gradientEndColor: Int,
        gradientOrientation: Int,
        mStokeColor: Int,
        mStokeWidth: Float
    ): GradientDrawable {
        val shape = DrawableUtils.ShapeBuilder().build()
        shape.shape = shapeType
        if (radius != 0f) {
            shape.cornerRadius = radius
        } else {
            val mRadii = floatArrayOf(
                leftTopRadius,
                leftTopRadius,
                rightTopRadius,
                rightTopRadius,
                rightBottomRadius,
                rightBottomRadius,
                leftBottomRadius,
                leftBottomRadius
            )
            shape.cornerRadii = mRadii
        }
        if (solidColor != 0) {
            shape.setColor(solidColor)
        }
        if (gradientStartColor != 0 && gradientEndColor != 0) {
            shape.orientation =
                if (gradientOrientation == 0) GradientDrawable.Orientation.LEFT_RIGHT else GradientDrawable.Orientation.TOP_BOTTOM
            shape.colors = intArrayOf(gradientStartColor, gradientEndColor)
        }
        if (mStokeColor != 0) {
            shape.setStroke(ceil(mStokeWidth.toDouble()).toInt(), mStokeColor)
        }
        return shape
    }

    fun setShapeBackground(
        solidColor: Int = 0,
        radius: Float = 0f,
        leftTopRadius: Float = 0f,
        rightTopRadius: Float = 0f,
        leftBottomRadius: Float = 0f,
        rightBottomRadius: Float = 0f,
        stokeWidth: Float = 0f,
        stokeColor: Int = 0,
        gradientOrientation: Int = 0,
        gradientStartColor: Int = 0,
        gradientEndColor: Int = 0
    ) {
        val shape = DrawableUtils.ShapeBuilder().build()
        if (radius != 0f) {
            shape.cornerRadius = radius
        } else {
            val mRadii = floatArrayOf(
                leftTopRadius,
                leftTopRadius,
                rightTopRadius,
                rightTopRadius,
                rightBottomRadius,
                rightBottomRadius,
                leftBottomRadius,
                leftBottomRadius
            )
            shape.cornerRadii = mRadii
        }
        if (solidColor != 0) {
            shape.setColor(solidColor)
        }
        if (gradientStartColor != 0 && gradientEndColor != 0) {
            shape.orientation =
                if (gradientOrientation == 0) GradientDrawable.Orientation.LEFT_RIGHT else GradientDrawable.Orientation.TOP_BOTTOM
            shape.colors = intArrayOf(gradientStartColor, gradientEndColor)
        }
        if (stokeColor != 0) {
            shape.setStroke(ceil(stokeWidth.toDouble()).toInt(), stokeColor)
        }
        val isDefault =
            solidColor == 0 && stokeColor == 0 && radius == 0f && leftTopRadius == 0f && rightTopRadius == 0f && leftBottomRadius == 0f && rightBottomRadius == 0f && stokeWidth == 0f && gradientOrientation == 0 && (gradientStartColor == 0 || gradientEndColor == 0)
        if (!isDefault) {
            (this as View).background = shape
        }
    }
}