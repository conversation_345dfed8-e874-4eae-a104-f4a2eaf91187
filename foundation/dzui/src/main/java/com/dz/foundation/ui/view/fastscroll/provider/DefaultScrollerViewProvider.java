package com.dz.foundation.ui.view.fastscroll.provider;

import android.view.ViewGroup;
import android.widget.ImageView;

import com.dz.foundation.ui.R;
import com.dz.foundation.ui.utils.DPUtils;

/**
 * @Author: wanxin
 * @Date: 2022/10/19 14:53
 * @Description:
 * @Version: 1.0
 */
public class DefaultScrollerViewProvider extends ScrollerViewProvider {

    protected ImageView handle;

    @Override
    public ImageView provideHandleView(ViewGroup container) {
        handle = new ImageView(getContext());

//        int verticalInset = getScroller().isVertical() ? 0 : getContext().getResources().getDimensionPixelSize(R.dimen.dzui_fastscroll_handle_inset);
//        int horizontalInset = !getScroller().isVertical() ? 0 : getContext().getResources().getDimensionPixelSize(R.dimen.dzui_fastscroll_handle_inset);
//        InsetDrawable handleBg = new InsetDrawable(ContextCompat.getDrawable(getContext(), R.drawable.dzui_fastscroll_handle), horizontalInset, verticalInset, horizontalInset, verticalInset);
//        handle.setBackground(handleBg);


//        int handleWidth = getContext().getResources().getDimensionPixelSize(getScroller().isVertical() ? R.dimen.dzui_fastscroll_handle_clickable_width : R.dimen.dzui_fastscroll_handle_height);
//        int handleHeight = getContext().getResources().getDimensionPixelSize(getScroller().isVertical() ? R.dimen.dzui_fastscroll_handle_height : R.dimen.dzui_fastscroll_handle_clickable_width);
//        ViewGroup.LayoutParams params = new ViewGroup.LayoutParams(handleWidth, handleHeight);
//        handle.setLayoutParams(params);
        ViewGroup.LayoutParams params = new ViewGroup.LayoutParams(DPUtils.dip2px(getContext(), 25), DPUtils.dip2px(getContext(), 45));
        handle.setLayoutParams(params);
        handle.setBackgroundResource(R.drawable.dzui_fastscroll_handle);

        return handle;
    }


}
