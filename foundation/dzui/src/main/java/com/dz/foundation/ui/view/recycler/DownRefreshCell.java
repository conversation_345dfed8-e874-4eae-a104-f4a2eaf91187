package com.dz.foundation.ui.view.recycler;

import android.view.View;
import android.view.ViewGroup;

/**
 * Created by stone on 2017/3/8.
 */

public class DownRefreshCell extends DzRecyclerViewCell implements RefreshHeader {

    private DownRefreshView refreshHeader;

    @Override
    protected View createViewByViewClass(ViewGroup parent, Class viewClass, Object VD) {

        return this.refreshHeader;
    }

    public void setRefreshHeaderView(DownRefreshView refreshHeader) {
        this.refreshHeader = refreshHeader;
    }

    @Override
    public void onMove(float delta) {

        refreshHeader.onMove(delta);
    }

    @Override
    public boolean releaseAction() {
        if (refreshHeader != null) {
            return refreshHeader.releaseAction();
        }
        return false;
    }

    @Override
    public void refreshComplete() {
        if (refreshHeader != null) {
            refreshHeader.refreshComplete();
        }
    }

    @Override
    public int getVisibleHeight() {
        if (refreshHeader != null) {
            return refreshHeader.getVisibleHeight();
        }
        return 0;
    }

    @Override
    public int getState() {
        if (refreshHeader != null) {
            return refreshHeader.getState();
        }
        return STATE_NORMAL;
    }

    @Override
    public boolean isOnTop() {
        if (refreshHeader != null) {
            refreshHeader.isOnTop();
        }
        return false;
    }

    public void setDownRefreshAlertMsg(String pullText, String releaseText, String refreshingText, String refreshDoneText) {
        if (refreshHeader != null) {
            refreshHeader.setDownRefreshAlertMsg(pullText, releaseText, refreshingText, refreshDoneText);
        }
    }

    public void setDownRefreshAlertMsg(String pullText, String releaseText, String refreshingText, String refreshDoneText, int arrowRes) {
        if (refreshHeader != null) {
            refreshHeader.setDownRefreshAlertMsg(pullText, releaseText, refreshingText, refreshDoneText);
//            refreshHeader.setArrowImageView(arrowRes);
        }
    }

    public DownRefreshView.onDownStateChangedListener getOnDownStateChangedListener() {
        if (refreshHeader != null) {
            return refreshHeader.getOnDownStateChangedListener();
        }
        return null;
    }

    public void setOnDownStateChangedListener(DownRefreshView.onDownStateChangedListener onDownStateChangedListener) {
        if (refreshHeader != null) {
            refreshHeader.setOnDownStateChangedListener(onDownStateChangedListener);
        }
    }
}
