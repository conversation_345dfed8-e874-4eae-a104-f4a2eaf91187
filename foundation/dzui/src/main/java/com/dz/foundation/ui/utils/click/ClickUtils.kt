package com.dz.foundation.ui.utils.click

import android.os.SystemClock

/**
 * @Author: guyh
 * @Date: 2023/12/8 18:13
 * @Description:
 * @Version:1.0
 */
object ClickUtils {
    /**
     * 当前view是否可以触发点击
     * @param id 当前view的id
     * @param intervalMills 当前点击距离上个view触发的时间 毫秒
     * @return
     */
    fun isCanClick(id: Int, intervalMills: Int = 600): Bo<PERSON>an {
        val currentTimeMillis = SystemClock.elapsedRealtime()
        val globalDistance =
            currentTimeMillis - OnClickListenerWrapper.lastGlobalClickTime
        if (globalDistance < intervalMills && OnClickListenerWrapper.lastClickViewId != id) {
            return false
        } else {
            OnClickListenerWrapper.lastClickViewId = id
            OnClickListenerWrapper.lastGlobalClickTime = currentTimeMillis
            return true
        }
    }
}