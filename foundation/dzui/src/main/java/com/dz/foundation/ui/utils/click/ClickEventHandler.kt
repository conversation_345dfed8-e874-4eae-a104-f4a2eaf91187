package com.dz.foundation.ui.utils.click

import android.view.View
import com.dz.foundation.ui.R

/**
 * 点击事件处理
 * Created by stone on 2017/11/4.
 */
class ClickEventHandler private constructor() {
    private var onClickListenerWrapper: OnClickListenerWrapper =
        OnClickListenerWrapper { onClick(it) }

    fun registerClickView(
        intervalMills: Long,
        view: View,
        block: OnClickAction
    ) {
        var clickIntervalMills = intervalMills

        if (clickIntervalMills < 0) {
            clickIntervalMills = SAME_VIEW_CLICK_INTERVAL_MILLS
        }

        view.setOnClickListener(onClickListenerWrapper)
        view.setTag(R.id.dzui_view_click_interval_tag, clickIntervalMills)
        view.setTag(R.id.dzui_view_click_block_tag, block)
    }

    fun addInterceptor(clickInterceptor: ClickInterceptor): ClickEventHandler {
        onClickListenerWrapper.addInterceptor(clickInterceptor)
        return this
    }

    private fun onClick(v: View) {
        getViewClickAction(v)?.let {
            it.onClick(v)
        }
    }

    private fun getViewClickAction(view: View): OnClickAction? {

        val tag = view.getTag(R.id.dzui_view_click_block_tag)
        if (tag != null && tag is OnClickAction) {
            return tag
        }
        return null
    }

    companion object {
        //view 上点击间隔tag
        //两个view 之间的 全局点击间隔时间毫秒
        const val GLOBAL_CLICK_INTERVAL_MILLS: Long = 200

        //同一个view 点击间隔时间毫秒
        const val SAME_VIEW_CLICK_INTERVAL_MILLS: Long = 600
        fun newInstance(): ClickEventHandler {
            return ClickEventHandler()
        }

        fun addGlobalListener(clickListener: View.OnClickListener) {
            GlobalListenerManager.addListener(clickListener)
        }

        internal fun getViewExtendParams(view: View): ExtendParams {

            val tag = view.getTag(R.id.dzui_view_extend_params_tag)
            return if (tag != null && tag is ExtendParams) {
                tag
            } else {
                val extendParams = ExtendParams()
                view.setTag(R.id.dzui_view_extend_params_tag, extendParams)
                extendParams
            }

        }

    }
}

fun interface OnClickAction {
    fun onClick(view: View)
}

fun <T : View> T.getViewExtendParams(): ExtendParams {
    return ClickEventHandler.getViewExtendParams(this)
}


