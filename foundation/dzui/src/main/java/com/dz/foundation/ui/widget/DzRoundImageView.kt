package com.dz.foundation.ui.widget

import android.content.Context
import android.graphics.Canvas
import android.graphics.Path
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatImageView
import com.dz.foundation.ui.R

/**
 * @Author: zhanggy
 * @Date: 2025/4/21 11:42
 * @Description: 圆角图片
 * @Version:1.0
 */
class DzRoundImageView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : AppCompatImageView(context, attrs, defStyleAttr) {
    private var radius = 0f
    private var leftTopRadius = 0f
    private var rightTopRadius = 0f
    private var leftBottomRadius = 0f
    private var rightBottomRadius = 0f
    private var mRoundPath = Path()

    init {
        mRoundPath = Path()
        val a =
            context.obtainStyledAttributes(attrs, R.styleable.DzRoundImageView, defStyleAttr, 0)
        if (a != null) {
            radius =
                a.getDimensionPixelSize(R.styleable.DzRoundImageView_round_radius, 0)
                    .toFloat()
            leftTopRadius =
                a.getDimensionPixelSize(
                    R.styleable.DzRoundImageView_round_left_top_radius,
                    0
                )
                    .toFloat()
            rightTopRadius =
                a.getDimensionPixelSize(
                    R.styleable.DzRoundImageView_round_right_top_radius,
                    0
                )
                    .toFloat()
            leftBottomRadius =
                a.getDimensionPixelSize(
                    R.styleable.DzRoundImageView_round_left_bottom_radius,
                    0
                )
                    .toFloat()
            rightBottomRadius =
                a.getDimensionPixelSize(
                    R.styleable.DzRoundImageView_round_right_bottom_radius,
                    0
                )
                    .toFloat()
            a.recycle()
        }
    }

    override fun onDraw(canvas: Canvas) {
        checkRadius()
        mRoundPath.reset()
        //绘制左上圆角
        if (leftTopRadius != 0F) {
            mRoundPath.moveTo(0F, leftTopRadius)
            mRoundPath.quadTo(0F, 0F, leftTopRadius, 0F)
        } else {
            //如果未设置圆角，不作处理，下同
            mRoundPath.moveTo(0F, 0F)
        }
        //绘制右上圆角
        if (rightTopRadius != 0f) {
            mRoundPath.lineTo(width - rightTopRadius, 0F)
            mRoundPath.quadTo(width.toFloat(), 0F, width.toFloat(), rightTopRadius)
        } else {
            mRoundPath.lineTo(width.toFloat(), 0F)
        }
        //绘制右下圆角
        if (rightBottomRadius != 0f) {
            mRoundPath.lineTo(width.toFloat(), height - rightBottomRadius)
            mRoundPath.quadTo(
                width.toFloat(), height.toFloat(), width - rightBottomRadius,
                height.toFloat()
            )
        } else {
            mRoundPath.lineTo(width.toFloat(), height.toFloat())
        }
        //绘制左下圆角
        if (leftBottomRadius != 0f) {
            mRoundPath.lineTo(leftBottomRadius, height.toFloat())
            mRoundPath.quadTo(0F, height.toFloat(), 0F, height - leftBottomRadius)
        } else {
            mRoundPath.lineTo(0F, height.toFloat())
        }
        mRoundPath.close()
        canvas.clipPath(mRoundPath)
        super.onDraw(canvas)
    }

    private fun checkRadius() {
        //如果未分别设置四个原画半径，都使用统一圆角半径
        if (leftTopRadius == 0f) {
            leftTopRadius = radius
        }
        if (rightTopRadius == 0f) {
            rightTopRadius = radius
        }
        if (leftBottomRadius == 0f) {
            leftBottomRadius = radius
        }
        if (rightBottomRadius == 0f) {
            rightBottomRadius = radius
        }
        //获取控件较短的一条边
        val minSize = (Math.min(width, height) / 2)
        //如果用户任意设置一个很大的半径，将radius修正为较短边的一半
        //显示效果就是一个圆或者两端呈圆形的矩形
        if (leftTopRadius > minSize) {
            leftTopRadius = minSize.toFloat()
        }
        if (leftBottomRadius > minSize) {
            leftBottomRadius = minSize.toFloat()
        }
        if (rightTopRadius > minSize) {
            rightTopRadius = minSize.toFloat()
        }
        if (rightBottomRadius > minSize) {
            rightBottomRadius = minSize.toFloat()
        }
    }
}