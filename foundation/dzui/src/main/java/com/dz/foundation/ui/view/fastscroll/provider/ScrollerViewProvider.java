package com.dz.foundation.ui.view.fastscroll.provider;

import android.content.Context;
import android.view.ViewGroup;
import android.widget.ImageView;

import com.dz.foundation.ui.view.fastscroll.FastScrollerBar;


/**
 * @Author: wanxin
 * @Date: 2022/10/19 17:08
 * @Description:
 * @Version: 1.0
 */
public abstract class ScrollerViewProvider {

    private FastScrollerBar scroller;

    public void setFastScroller(FastScrollerBar scroller) {
        this.scroller = scroller;
    }

    protected Context getContext() {
        return scroller.getContext();
    }

    protected FastScrollerBar getScroller() {
        return scroller;
    }

    public abstract ImageView provideHandleView(ViewGroup container);


}
