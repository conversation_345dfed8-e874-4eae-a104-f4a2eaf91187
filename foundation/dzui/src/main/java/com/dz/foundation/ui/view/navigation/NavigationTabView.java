package com.dz.foundation.ui.view.navigation;

import android.content.Context;
import android.content.res.ColorStateList;
import android.content.res.TypedArray;
import android.graphics.Typeface;
import android.graphics.drawable.StateListDrawable;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;

import com.airbnb.lottie.LottieAnimationView;
import com.dz.foundation.base.utils.LogUtil;
import com.dz.foundation.ui.R;
import com.dz.foundation.ui.utils.DPUtils;
import com.dz.foundation.ui.utils.SelectorUtil;
import com.dz.foundation.ui.widget.DzLinearLayout;


/**
 * Created by wxliao on 2016/7/18.
 */
public class NavigationTabView extends DzLinearLayout implements NavigationAble {

    // 样式模式常量
    public static final int STYLE_ICON_TEXT = 0;
    public static final int STYLE_TEXT_ONLY = 1;

    public static final int STYLE_IMAGE_ONLY = 2;

    public static final int STYLE_LOTTIE_ONLY = 3;

    private int tabIconRes;
    private String tabText;
    private int tabStateColorRes;
    private int tabStyle = STYLE_ICON_TEXT; // 默认为图标+文字模式

    // 图标+文字模式的控件
    private ConstraintLayout normalTab;
    private ImageView imageView;
    private TextView textView;
    private TextView textView_dot;
    private TextView textView_dot_1;

    // 纯文字模式的控件
    private ConstraintLayout textOnlyTab;
    private TextView textView_only;
    private TextView textView_dot_only;
    private TextView textView_dot_1_only;
    private ImageView imageOnlyTab;
    private LottieAnimationView lottieOnlyTab;
    private static final int ANIMATION_DURATION = 200;

    private boolean showMessageAlways = true; //小红点是否一直显示，选中也不隐藏
    
    // 标记是否已经开始过 Lottie 动画
    private boolean hasStartedLottieAnimation = false;

    public NavigationTabView(Context context) {
        this(context, null);
    }

    public NavigationTabView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public NavigationTabView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView(context, attrs);
    }

    private void initView(Context context, AttributeSet attrs) {
        setOrientation(LinearLayout.VERTICAL);

        if (attrs != null) {
            TypedArray a = context.getTheme().obtainStyledAttributes(attrs, R.styleable.NavigationTabView, 0, 0);
            tabIconRes = a.getResourceId(R.styleable.NavigationTabView_tab_icon, 0);
            tabText = a.getString(R.styleable.NavigationTabView_tab_text);
            tabStateColorRes = a.getResourceId(R.styleable.NavigationTabView_tab_stateColor, 0);
            a.recycle();
        }

        LayoutInflater.from(context).inflate(R.layout.dzui_navigation_tab, this);

        // 初始化图标+文字模式的控件
        normalTab = findViewById(R.id.normal_tab);
        imageView = findViewById(R.id.imageView);
        textView = findViewById(R.id.textView);
        textView_dot = findViewById(R.id.textView_dot);
        textView_dot_1 = findViewById(R.id.textView_dot_1);

        // 初始化纯文字模式的控件
        textOnlyTab = findViewById(R.id.text_only_tab);
        textView_only = findViewById(R.id.textView_only);
        textView_dot_only = findViewById(R.id.textView_dot_only);
        textView_dot_1_only = findViewById(R.id.textView_dot_1_only);

        // 初始化纯图片模式的控件
        imageOnlyTab = findViewById(R.id.iv_welfare_bottom);

        // 初始化纯动效模式的控件
        lottieOnlyTab = findViewById(R.id.lv_welfare_bottom);

        // 根据样式模式显示对应的布局
        updateLayoutVisibility();
    }

    /**
     * 根据样式模式更新布局可见性
     */
    private void updateLayoutVisibility() {
        if (tabStyle == STYLE_TEXT_ONLY) {
            normalTab.setVisibility(View.GONE);
            textOnlyTab.setVisibility(View.VISIBLE);
            imageOnlyTab.setVisibility(View.GONE);
            lottieOnlyTab.setVisibility(View.GONE);
        } else if (tabStyle == STYLE_IMAGE_ONLY) {
            normalTab.setVisibility(View.GONE);
            textOnlyTab.setVisibility(View.GONE);
            imageOnlyTab.setVisibility(View.VISIBLE);
            lottieOnlyTab.setVisibility(View.GONE);
        } else if (tabStyle == STYLE_LOTTIE_ONLY) {
            normalTab.setVisibility(View.GONE);
            textOnlyTab.setVisibility(View.GONE);
            imageOnlyTab.setVisibility(View.GONE);
            lottieOnlyTab.setVisibility(View.VISIBLE);
            LogUtil.d("打印","tab:updateLayoutVisibility hasStartedLottieAnimation="+hasStartedLottieAnimation);
            if (!hasStartedLottieAnimation) {
                hasStartedLottieAnimation = true;
                lottieOnlyTab.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        if (lottieOnlyTab != null && tabStyle == STYLE_LOTTIE_ONLY) {
                            lottieOnlyTab.playAnimation();
                            delayShowLottie(delayTime);
                        }
                    }
                }, 5000);
            }
        } else {
            normalTab.setVisibility(View.VISIBLE);
            textOnlyTab.setVisibility(View.GONE);
            imageOnlyTab.setVisibility(View.GONE);
            lottieOnlyTab.setVisibility(View.GONE);
        }
    }

    // 延迟显示Lottie动画
    //TODO 测试时期改为1分钟一次 线上恢复八小时一次
//    long delayTime = 1000*60*60*8;
    long delayTime = 1000*60;
    private void delayShowLottie(long time) {
        LogUtil.d("打印", "tab:delayShowLottie delayShowLottie=" + time);
        lottieOnlyTab.postDelayed(new Runnable() {
            @Override
            public void run() {
                LogUtil.d("打印", "tab:delayShowLottie delayShowLottie=" + time);
                if (lottieOnlyTab != null && tabStyle == STYLE_LOTTIE_ONLY) {
                    lottieOnlyTab.playAnimation();
                    delayShowLottie(delayTime);
                }
            }
        }, time);
    }


    @Override
    public void select(BottomBarLayout.TabItemBean tabItemBean) {
        setSelected(true);
        //图标+文字模式
        textView.setSelected(true);
        textView.setTextColor(ContextCompat.getColor(getContext(), tabSelectedColor));
        textView.setTypeface(null, Typeface.BOLD);

        //纯文字模式
        textView_only.setSelected(true);
        textView_only.setTextColor(ContextCompat.getColor(getContext(), tabSelectedColor));
        textView_only.setTypeface(null, Typeface.BOLD);

        //设置图标
        if (!showMessageAlways) {
            hideNewMessage();
        }
    }

    @Override
    public void unSelect(BottomBarLayout.TabItemBean tabItemBean) {
        setSelected(false);
        //文字+图标模式
        textView.setTextColor(ContextCompat.getColor(getContext(), tabUnSelectColor));
        textView.setTypeface(null, Typeface.NORMAL);

        //纯文字模式
        textView_only.setTextColor(ContextCompat.getColor(getContext(), tabUnSelectColor));
        textView_only.setTypeface(null, Typeface.BOLD);
    }

    @Override
    public void showNewMessage() {
        showNewMessage("");
    }

    @Override
    public void showNewMessage(String msg) {
        if (!isSelected() || showMessageAlways) {
            if (!TextUtils.isEmpty(msg)) {
                //文字+图标
                textView_dot_1.setVisibility(View.VISIBLE);
                textView_dot_1.setText(msg);
                textView_dot_1.animate().scaleX(1).scaleY(1).setDuration(ANIMATION_DURATION).start();

                //纯文字
                textView_dot_1_only.setVisibility(View.VISIBLE);
                textView_dot_1_only.setText(msg);
                textView_dot_1_only.animate().scaleX(1).scaleY(1).setDuration(ANIMATION_DURATION).start();
            } else {
                //文字+图标
                textView_dot.setVisibility(View.VISIBLE);
                textView_dot.animate().scaleX(1).scaleY(1).setDuration(ANIMATION_DURATION).start();

                //纯文字
                textView_dot_only.setVisibility(View.VISIBLE);
                textView_dot_only.animate().scaleX(1).scaleY(1).setDuration(ANIMATION_DURATION).start();
            }
        }
    }

    private void setTextViewDot(String msg) {
        ViewGroup.LayoutParams lp = textView_dot.getLayoutParams();
        int size;
        if (!TextUtils.isEmpty(msg)) {
            textView_dot.setText(msg);
            textView_dot_only.setText(msg);
            size = DPUtils.dip2px(getContext(), 15);
        } else {
            textView_dot.setText("");
            textView_dot_only.setText("");
            size = DPUtils.dip2px(getContext(), 9);
        }
        lp.height = size;
        lp.width = size;
        textView_dot.setLayoutParams(lp);
        textView_dot_only.setLayoutParams(lp);
    }

    @Override
    public void setShowMessageAlways(boolean show) {
        showMessageAlways = show;
    }

    @Override
    public void setNewMessageStroke(Boolean isNormal) {
//        setTextBg(isNormal, textView_dot_1);
        setTextBg(isNormal, textView_dot);
        setTextBg(isNormal, textView_dot_only);
    }

    private void setTextBg(Boolean isNormal, TextView textView) {
        if (textView.getVisibility() == View.VISIBLE) {
            if (isNormal) {
                textView.setBackgroundResource(R.drawable.dzui_shape_dot_black_ring);
            } else {
                textView.setBackgroundResource(R.drawable.dzui_shape_dot_white_ring);
            }
        }
    }

    @Override
    public void hideNewMessage() {
        //文字+图标模式
        if (textView_dot_1.getVisibility() == View.VISIBLE) {
            textView_dot_1.animate().scaleX(0).scaleY(0).setDuration(ANIMATION_DURATION).start();
        }
        if (textView_dot.getVisibility() == View.VISIBLE) {
            textView_dot.animate().scaleX(0).scaleY(0).setDuration(ANIMATION_DURATION).start();
        }

        //纯文字模式
        if (textView_dot_1_only.getVisibility() == View.VISIBLE) {
            textView_dot_1_only.animate().scaleX(0).scaleY(0).setDuration(ANIMATION_DURATION).start();
        }
        if (textView_dot_only.getVisibility() == View.VISIBLE) {
            textView_dot_only.animate().scaleX(0).scaleY(0).setDuration(ANIMATION_DURATION).start();
        }
    }


    int tabUnSelectIconRes;
    int tabSelectedIconRes;

    public void setTabIconRes(int tabUnSelectIconRes, int tabSelectedIconRes) {
        this.tabUnSelectIconRes = tabUnSelectIconRes;
        this.tabSelectedIconRes = tabSelectedIconRes;
        StateListDrawable drawableSelector = SelectorUtil.createDrawableSelector(getContext(), tabUnSelectIconRes, tabSelectedIconRes);
        if (drawableSelector != null) {
            if (drawableSelector instanceof StateListDrawable) {
                imageView.setImageDrawable(drawableSelector);
            }
        }
    }

    public void setTabText(String tabText) {
        this.tabText = tabText;
        textView.setText(tabText);
        textView_only.setText(tabText);
    }

    int tabUnSelectColor;
    int tabSelectedColor;

    public void setTabStateColorRes(int tabUnSelectColor, int tabSelectedColor) {
        this.tabUnSelectColor = tabUnSelectColor;
        this.tabSelectedColor = tabSelectedColor;
        ColorStateList colorSelector = SelectorUtil.createColorSelector(tabUnSelectColor, tabSelectedColor, tabSelectedColor, tabUnSelectColor);
        if (colorSelector != null) {
            textView.setTextColor(colorSelector);
            textView_only.setTextColor(colorSelector);
        }
    }

    /**
     * 设置Tab样式模式
     *
     * @param style STYLE_ICON_TEXT 或 STYLE_TEXT_ONLY
     */
    public void setTabStyle(int style) {
        if (this.tabStyle != style) {
            this.tabStyle = style;
            updateLayoutVisibility();
        }
    }

    /**
     * 获取当前Tab样式模式
     *
     * @return STYLE_ICON_TEXT 或 STYLE_TEXT_ONLY
     */
    public int getTabStyle() {
        return tabStyle;
    }

    /**
     * 获取Tab文本
     *
     * @return tabText
     */
    public String getTabText() {
        return tabText;
    }
}
