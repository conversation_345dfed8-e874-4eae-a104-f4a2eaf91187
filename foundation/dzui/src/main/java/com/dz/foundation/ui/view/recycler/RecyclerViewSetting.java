package com.dz.foundation.ui.view.recycler;

import android.util.Log;

import androidx.recyclerview.widget.RecyclerView;

public class RecyclerViewSetting {
    private DzRecyclerView recyclerView;
    private LoadMoreCell loadMoreViewCell;
    public DownRefreshCell downRefreshCell;
    public EmptyStateCell emptyStateCell;

    protected DownRefreshView mRefreshHeader;
    private int autoLoadMoreThreshold;
    protected OnDownRefreshListener onDownRefreshListener;
    private OnUpLoadMoreListener onUpLoadMoreListener;
    private boolean isLoadingMore;//是否正在加载数据
    private boolean isLoadMoreCellShown;
    public boolean isDownRefreshEnabled;//是否开启下拉刷新
    public boolean isLoadMoreEnabled;   //是否开启上拉加载
    private boolean isLoadAllData;  //是否加载完所有数据
    private boolean isEmptyData;  //是否无数据

    public RecyclerViewSetting(DzRecyclerView recyclerView) {
        this.recyclerView = recyclerView;
        emptyStateCell = new EmptyStateCell();
        emptyStateCell.setViewClass(EmptyView.class);
        emptyStateCell.setViewData("暂无数据");
        emptyStateCell.setSpanSize(recyclerView.getGridSpanCount());
        setScrollListener();
    }

    private boolean isScrollUp;//是否正在向上滑动
    private boolean isEmptyViewShown;

    private void setScrollListener() {
        recyclerView.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrolled(RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);
                isScrollUp = dy < 0;
                //滑动时在所有条目未显示出来的之前，把加载更多的cell添加recyclerView
                if (isLoadMoreEnabled && recyclerView.canScrollVertically(-1)) {
                    if (containsEmptyCell() && isEmptyData) {
                        return;
                    }
                    showLoadMoreView();
                }
            }

            @Override
            public void onScrollStateChanged(RecyclerView recyclerView, int newState) {
                super.onScrollStateChanged(recyclerView, newState);

                if (newState == RecyclerView.SCROLL_STATE_IDLE && isLoadMoreEnabled && !isLoadingMore) {
                    //停止滚动的时候触发加载更多的操作
                    checkLoadMoreThreshold();
                }
            }
        });

    }

    private RecyclerViewSetting setDownRefreshEnabled(boolean downRefreshEnabled) {
        this.isDownRefreshEnabled = downRefreshEnabled;
        if (downRefreshCell == null) {
            setDownRefreshView();
        }
        return this;
    }

    public RecyclerViewSetting setOnLoadMoreEnabled(boolean loadMoreEnabled) {
        this.isLoadMoreEnabled = loadMoreEnabled;
        if (loadMoreViewCell == null) {
            setLoadMoreView();
        }
        return this;
    }


    public RecyclerViewSetting setOnUpLoadMoreListener(OnUpLoadMoreListener onUpLoadMoreListener) {
        this.onUpLoadMoreListener = onUpLoadMoreListener;
        setOnLoadMoreEnabled(true);
        return this;
    }

    public RecyclerViewSetting removeLoadMoreListener() {
        this.onUpLoadMoreListener = null;
        setOnLoadMoreEnabled(false);
        return this;
    }

    public RecyclerViewSetting setOnDownRefreshListener(OnDownRefreshListener onDownRefreshListener) {
        this.onDownRefreshListener = onDownRefreshListener;
        setDownRefreshEnabled(true);
        return this;
    }

    /**
     * 刷新完成调用
     *
     * @param dataSize 返回的数据大小  如果本次请求未返回数据 传 null
     */
    protected void setOnDownRefreshCompleted(int dataSize, boolean hasMore) {
        isLoadMoreCellShown = false;
        this.isEmptyData = false;
        if (dataSize == 0) {
            this.isEmptyData = true;
            this.isLoadAllData = true;
        } else {
            this.isLoadAllData = !hasMore;
        }

        if (isEmptyData) {
            showEmptyView();
        } else if (isLoadAllData) {
            showLoadMoreView();
        }
        mRefreshHeader.refreshComplete();

    }


    /**
     * 上拉加载完成调用此代码
     *
     * @param hasMore 是否还有更多
     */
    public void setOnUpLoadMoreCompleted(boolean hasMore) {
        this.isLoadAllData = !hasMore;
        isLoadingMore = false;
        hideLoadMoreView();

    }

    private int getGridSpanCount() {
        return recyclerView.getGridSpanCount();
    }


    private void checkLoadMoreThreshold() {
        if (isEmptyViewShown || isLoadingMore) {
            return;
        }
        //上拉
        if (isLoadMoreEnabled && !isScrollUp && recyclerView.canScrollVertically(-1)) {
            int lastVisibleItemPosition = recyclerView.getLastVisibleItemPosition();
            int bottomHiddenItemCount = (recyclerView.getItemCount() - 1) - lastVisibleItemPosition;
            Log.d("DzRecyclerView", "checkLoadMoreThreshold: bottomHiddenItemCount" + bottomHiddenItemCount);
            if (bottomHiddenItemCount >= 1) {
                return;
            }
            if (onUpLoadMoreListener != null && !isLoadAllData) {
                DzRecyclerViewCell cell = recyclerView.getCell(lastVisibleItemPosition);
                if (cell != null && cell.equals(loadMoreViewCell)) {
                    Log.d("DzRecyclerView", "handleLoadMore");
                    handleLoadMore();
                }
            }
        }
    }

    private void handleLoadMore() {
        isLoadingMore = true;
        recyclerView.smoothScrollToPosition(recyclerView.getItemCount() - 1);
        onUpLoadMoreListener.onLoadMore(new LoadMoreNotify(recyclerView));
    }


    /**
     * load more
     */


    private void setLoadMoreView() {
        this.loadMoreViewCell = new LoadMoreCell();
        this.loadMoreViewCell.setViewClass(LoadMoreView.class);
        LoadMoreModel loadMoreModel = new LoadMoreModel();
        loadMoreModel.setLoadState(LoadMoreModel.hidden);
        loadMoreModel.setLoadAllAlert("暂无更多");
        loadMoreViewCell.setViewData(loadMoreModel);
        loadMoreViewCell.setSpanSize(getGridSpanCount());
    }

    private void setDownRefreshView() {
        this.downRefreshCell = new DownRefreshCell();
        mRefreshHeader = new DownRefreshView(recyclerView.getContext());
        this.downRefreshCell.setViewClass(DownRefreshView.class);
        this.downRefreshCell.setRefreshHeaderView(mRefreshHeader);
        downRefreshCell.setSpanSize(getGridSpanCount());
        recyclerView.addCell(downRefreshCell);
    }


    private void showLoadMoreView() {
        if (loadMoreViewCell == null || isLoadMoreCellShown) {
            //判断是否已经显示了底部
            return;
        }
        Log.d("DzRecyclerView", "showLoadMoreView");
        if (containsLoadMoreCell()) {
            recyclerView.post(new Runnable() {
                @Override
                public void run() {
                    recyclerView.removeCell(loadMoreViewCell);
                }
            });

        }
        isLoadMoreCellShown = true;
        recyclerView.post(new Runnable() {
            @Override
            public void run() {
                int state = LoadMoreModel.loading;
                if (isLoadAllData) {
                    state = LoadMoreModel.loadAll;
                }
                loadMoreViewCell.setLoadState(state);
                recyclerView.addCell(loadMoreViewCell);

            }
        });

    }

    private boolean containsLoadMoreCell() {
        if (loadMoreViewCell != null && recyclerView != null
                && recyclerView.getAllCells().contains(loadMoreViewCell)) {
            return true;
        }
        return false;
    }

    private boolean containsEmptyCell() {
        if (emptyStateCell != null && recyclerView != null
                && recyclerView.getAllCells().contains(emptyStateCell)) {
            return true;
        }
        return false;
    }

    private void hideLoadMoreView() {

        if (loadMoreViewCell != null && containsLoadMoreCell()) {
            loadMoreViewCell.getViewData().setLoadState(LoadMoreModel.hidden);
            recyclerView.post(new Runnable() {
                @Override
                public void run() {
                    recyclerView.removeCell(loadMoreViewCell);
                }
            });

        }
        isLoadMoreCellShown = false;

    }

    public void setAutoLoadMoreThreshold(int hiddenCellCount) {
        if (hiddenCellCount < 0) {
            throw new IllegalArgumentException("hiddenCellCount must >= 0");
        }
        this.autoLoadMoreThreshold = hiddenCellCount;
    }

    public int getAutoLoadMoreThreshold() {
        return autoLoadMoreThreshold;
    }

    public void setOnDownStateChangedListener(DownRefreshView.onDownStateChangedListener onDownStateChangedListener) {
        mRefreshHeader.setOnDownStateChangedListener(onDownStateChangedListener);
    }

    public void showEmptyView() {
        if (!recyclerView.getAllCells().contains(emptyStateCell)) {
            recyclerView.addCell(emptyStateCell);
        }
    }

    /**
     * 执行下拉刷新动作
     */
    protected void doDownRefresh() {
        if (mRefreshHeader != null && isDownRefreshEnabled && onDownRefreshListener != null) {
            mRefreshHeader.setState(DownRefreshView.STATE_REFRESHING);
            recyclerView.postDelayed(new Runnable() {
                @Override
                public void run() {
                    onDownRefreshListener.onRefresh(new DownRefreshNotify(recyclerView));
                }
            }, 100);

        }
    }
}
