package com.dz.foundation.ui.view.recycler

import android.graphics.Rect
import android.view.View
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.StaggeredGridLayoutManager
import com.dz.foundation.base.utils.LogUtil

/**
 * @Author: wanxin
 * @Date: 2022/12/29 10:40
 * @Description:
 * @Version: 1.0
 */
class DzExposeRvItemUtil {
    private lateinit var mItemOnExposeListener: OnItemExposeListener
    private lateinit var mRecyclerView: RecyclerView
    private var limitNumV = 0.6f
    private var limitNumH = 0.35f
    private var firstLoad = true

    fun setDzRecyclerItemExposeListener(
        recyclerView: RecyclerView
    ) {
        initItemOnExposeListener()
        mRecyclerView = recyclerView
        if (mRecyclerView.visibility != View.VISIBLE) {
            return
        }
        //检测recyclerView的滚动事件
        mRecyclerView.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                //SCROLL_STATE_IDLE:停止滚动
                //SCROLL_STATE_DRAGGING: 用户慢慢拖动
                //SCROLL_STATE_SETTLING：惯性滚动
//                if (newState == RecyclerView.SCROLL_STATE_IDLE || newState == RecyclerView.SCROLL_STATE_DRAGGING || newState == RecyclerView.SCROLL_STATE_SETTLING) {
                if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                    LogUtil.d("rv曝光", "停止滑动开始判断是否曝光view")
                    handleCurrentVisibleItems()
                }
            }

            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                //包括刚进入列表时统计当前屏幕可见views
                if (firstLoad) {
                    LogUtil.d("rv曝光", "首次加载曝光view")
                    handleCurrentVisibleItems()
                    firstLoad = false
                }
            }
        })
    }

    fun directExposeRecyclerItem(
        recyclerView: RecyclerView,
    ) {
        recyclerView.postDelayed({
            initItemOnExposeListener()
            mRecyclerView = recyclerView
            handleCurrentVisibleItems()
        }, 300)
    }

    private fun initItemOnExposeListener() {
        if (::mItemOnExposeListener.isInitialized) {
            return
        }
        mItemOnExposeListener = object : OnItemExposeListener {
            override fun onItemViewVisible(
                viewItem: DzRecyclerViewItem<*>,
                moreLimitVisible: Boolean,
                position: Int
            ) {
                LogUtil.d(
                    "rv曝光",
                    "position = $position 可见范围满足曝光条件 = $moreLimitVisible  ${viewItem::class.simpleName}"
                )
                val cell = viewItem as View
                val findNestRv = viewItem.getNestRecyclerView(cell)
                if (findNestRv != null) {
                    LogUtil.d("rv曝光", "存在嵌套rv")
                    viewItem.nestExpose(findNestRv)
                    if (moreLimitVisible) {
                        viewItem.decideExposeView()
                    }
                } else {
                    if (moreLimitVisible) {
                        viewItem.decideExposeView()
                    }
                }
            }
        }
    }


    /**
     * 处理 当前屏幕上mRecyclerView可见的item view
     */
    fun handleCurrentVisibleItems() {
        //View.getGlobalVisibleRect(new Rect())，true表示view视觉可见，无论可见多少。
        if (!::mRecyclerView.isInitialized
            || mRecyclerView.visibility != View.VISIBLE
            || !mRecyclerView.isShown
            || !mRecyclerView.getGlobalVisibleRect(Rect())
        ) {
            return
        }
        try {
            var range = IntArray(2)
            var orientation = -1
            val manager = mRecyclerView.layoutManager
            when (manager) {
                is LinearLayoutManager -> {
                    range = findRangeLinear(manager)
                    orientation = manager.orientation
                }
                is GridLayoutManager -> {
                    range = findRangeGrid(manager)
                    orientation = manager.orientation
                }
                is StaggeredGridLayoutManager -> {
                    range = findRangeStaggeredGrid(manager)
                    orientation = manager.orientation
                }
            }
            if (range.size < 2) {
                return
            }
            //所有可见的view
            for (position in range[0]..range[1]) {
                val view = manager!!.findViewByPosition(position) ?: return
                setCallbackForLogicVisibleView(view, position, orientation)
//                if (directExpose) {
//                    setCallbackForLogicVisibleView(view, position, orientation)
//                } else {
//                    if (manager is GridLayoutManager) {
//                        setCallbackForLogicVisibleView(view, position, orientation)
//                    } else {
//                        if (position == range[0] || position == range[1]) {
//                            setCallbackForLogicVisibleView(view, position, orientation)
//                        } else {
//                            if (view is DzRecyclerViewItem<*>) {
//                                mItemOnExposeListener.onItemViewVisible(view, true, position)
//                            }
//                        }
//                    }
//                }

            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 为逻辑上可见的view设置 可见性回调
     * 说明：逻辑上可见--可见且可见高度（宽度）>view高度（宽度）的75%
     * @param view 可见item的view
     * @param position 可见item的position
     * @param orientation recyclerView的方向
     */
    private fun setCallbackForLogicVisibleView(view: View, position: Int, orientation: Int) {
        val rect = Rect()
        val cover = view.getGlobalVisibleRect(rect)
        if (view.visibility != View.VISIBLE || !view.isShown || !cover) {
            return
        }

        if (view is DzRecyclerViewItem<*>) {

            //item逻辑上可见：可见且可见高度（宽度）>view高度（宽度）50%才行
//            val visibleHeightEnough =
//                orientation == OrientationHelper.VERTICAL && rect.height() >= view.measuredHeight * limitNum
//            val visibleWidthEnough =
//                orientation == OrientationHelper.HORIZONTAL && rect.width() >= view.measuredWidth * limitNum
//            val isItemViewVisibleInLogic =visibleHeightEnough || visibleWidthEnough

            //之前打点 选择的方案 不建议修改 如果修改为面积计算，可能会导致曝光率降低很多。导致点击没变，曝光率降低。
            val visibleHeightEnough = rect.height() >= view.measuredHeight * limitNumV
            val visibleWidthEnough = rect.width() >= view.measuredWidth * limitNumH
            val isItemViewVisibleInLogic = visibleHeightEnough && visibleWidthEnough

            if (isItemViewVisibleInLogic) {
                mItemOnExposeListener.onItemViewVisible(view, true, position)
            } else {
                mItemOnExposeListener.onItemViewVisible(view, false, position)
            }
        }

    }

    private fun findRangeLinear(manager: LinearLayoutManager): IntArray {
        val range = IntArray(2)
        range[0] = manager.findFirstVisibleItemPosition()
        range[1] = manager.findLastVisibleItemPosition()
        return range
    }

    private fun findRangeGrid(manager: GridLayoutManager): IntArray {
        val range = IntArray(2)
        range[0] = manager.findFirstVisibleItemPosition()
        range[1] = manager.findLastVisibleItemPosition()
        return range
    }

    private fun findRangeStaggeredGrid(manager: StaggeredGridLayoutManager): IntArray {
        val startPos = IntArray(manager.spanCount)
        val endPos = IntArray(manager.spanCount)
        manager.findFirstVisibleItemPositions(startPos)
        manager.findLastVisibleItemPositions(endPos)
        return findRange(startPos, endPos)
    }

    private fun findRange(startPos: IntArray, endPos: IntArray): IntArray {
        var start = startPos[0]
        var end = endPos[0]
        for (i in 1 until startPos.size) {
            if (start > startPos[i]) {
                start = startPos[i]
            }
        }
        for (i in 1 until endPos.size) {
            if (end < endPos[i]) {
                end = endPos[i]
            }
        }
        return intArrayOf(start, end)
    }

    interface OnItemExposeListener {
        /**
         * item 可见性回调
         * 回调此方法时 视觉上一定是可见的（无论可见多少）
         * @param viewItem  item
         * @param moreLimitVisible  true，逻辑上可见，即宽/高 > 设置的临界
         * @param position item在列表中的位置
         */
        fun onItemViewVisible(
            viewItem: DzRecyclerViewItem<*>,
            moreLimitVisible: Boolean,
            position: Int
        )
    }
}