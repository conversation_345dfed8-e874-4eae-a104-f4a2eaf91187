package com.dz.foundation.ui.utils.click

import android.os.SystemClock
import android.view.View

/**
 *@Author: zhanggy
 *@Date: 2024-10-21
 *@Description: View点击防抖处理
 *@Version:1.0
 */
// 设置两次点击事件的最小间隔时间（例如：500毫秒）
private const val MIN_CLICK_INTERVAL: Long = 500

// 使用View的Tag属性来存储上一次点击的时间
private var View.lastClickTime: Long
    get() = this.getTag(123456789) as? Long ?: 0L
    set(value) {
        this.setTag(123456789, value)
    }

// 扩展函数，用于设置防连点的点击监听器
fun View.setOnAntiFrequentClickListener(listener: View.OnClickListener?) {
    this.setOnClickListener {
        val currentClickTime = SystemClock.elapsedRealtime()
        if (currentClickTime - lastClickTime >= MIN_CLICK_INTERVAL) {
            lastClickTime = currentClickTime
            listener?.onClick(it)
        }
    }
}