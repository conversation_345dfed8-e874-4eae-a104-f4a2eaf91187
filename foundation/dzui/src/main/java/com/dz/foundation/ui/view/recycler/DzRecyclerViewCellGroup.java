package com.dz.foundation.ui.view.recycler;

import com.dz.foundation.ui.view.custom.ActionListener;

import java.util.ArrayList;
import java.util.List;

public abstract class DzRecyclerViewCellGroup<VD> extends DzRecyclerViewCell<VD> {

    private ArrayList<DzRecyclerViewCell> childCells;

    public ArrayList<DzRecyclerViewCell> getChildCells() {
        return childCells;
    }


    private ActionListener groupListener;

    public void setGroupListener(ActionListener groupListener) {
        this.groupListener = groupListener;
    }

    public ActionListener getGroupListener() {
        return this.groupListener;
    }

    public DzRecyclerViewCellGroup() {

    }


    @Override
    public DzRecyclerViewCell<VD> setViewData(VD viewData) {
        if (childCells == null) {
            childCells = onCreateChild(viewData);
        }
        setChildData(viewData);
        return super.setViewData(viewData);
    }

    protected abstract List<Object> getChildData(VD viewData);

    private void setChildData(VD viewData) {
        if (childCells == null) {
            return;
        }
        List<Object> childData = getChildData(viewData);
        if (childData == null) {
            return;
        }

        int size = childCells.size();
        for (int i = 0; i < size; i++) {
            Object obj = childData.get(i);
            if (obj != null) {
                childCells.get(i).setViewData(obj);
            }
        }

    }

    protected abstract ArrayList<DzRecyclerViewCell> onCreateChild(VD viewData);

    @Override
    public void update(VD groupData) {
        super.update(groupData);
        if (childCells == null) {
            return;
        }
        List<Object> childData = getChildData(groupData);
        if (childData == null) {
            return;
        }
        int size = childCells.size();
        for (int i = 0; i < size; i++) {
            if (childData.size() <= i) {
                continue;
            }
            Object obj = childData.get(i);
            if (obj == null) {
                continue;
            }
            childCells.get(i).update(obj);
        }
    }

}
