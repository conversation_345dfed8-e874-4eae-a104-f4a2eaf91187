package com.dz.foundation.ui.view;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.RectF;
import android.util.AttributeSet;
import android.view.View;

import androidx.annotation.ColorInt;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.dz.foundation.base.utils.ScreenUtil;
import com.dz.foundation.ui.R;

/**
 * 原型进度条
 */
public class CircleProgress extends View {

    private static final String TAG = "CircleProgress";

    private static final String DEFAULT_PROGRESS_COLOR = "#3F51B5";
    private static final int DEFAULT_STROKE_WIDTH_DP = 4;
    private static final String DEFAULT_PROGRESS_BACKGROUND_COLOR = "#e0e0e0";
    /**
     * 默认开始角度
     */
    private static final int DEFAULT_PROGRESS_START_ANGLE = 270;

    private Paint progressPaint;
    private Paint progressBackgroundPaint;
    private RectF circleBounds;

    private float radius;

    private int startAngle = DEFAULT_PROGRESS_START_ANGLE;
    private float sweepAngle = 0.0f;
    private float progress;

    public CircleProgress(Context context) {
        super(context);
        init(context, null);
    }

    public CircleProgress(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init(context, attrs);
    }

    public CircleProgress(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs);
    }

    public CircleProgress(Context context, @Nullable AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        init(context, attrs);
    }

    private void init(@NonNull Context context, @Nullable AttributeSet attrs) {

        int progressColor = Color.parseColor(DEFAULT_PROGRESS_COLOR);
        int progressBackgroundColor = Color.parseColor(DEFAULT_PROGRESS_BACKGROUND_COLOR);
        int progressStrokeWidth = ScreenUtil.Companion.dip2px(context, DEFAULT_STROKE_WIDTH_DP);

        Paint.Cap progressStrokeCap = Paint.Cap.ROUND;
        if (attrs != null) {
            TypedArray a = context.getTheme().obtainStyledAttributes(attrs, R.styleable.CircleProgress, 0, 0);
            try {
                progressColor = a.getColor(R.styleable.CircleProgress_circle_color, 60000000);
                progress = a.getFloat(R.styleable.CircleProgress_circle_percent, 0.0F);
                progressStrokeWidth = a.getDimensionPixelSize(R.styleable.CircleProgress_circle_stroke_width, progressStrokeWidth);
                progressBackgroundColor = a.getColor(R.styleable.CircleProgress_circle_background, 80000000);
            } finally {
                a.recycle();
            }
        }

        progressPaint = new Paint();
        progressPaint.setStrokeCap(progressStrokeCap);
        progressPaint.setStrokeWidth(progressStrokeWidth);
        progressPaint.setStyle(Paint.Style.STROKE);
        progressPaint.setColor(progressColor);
        progressPaint.setAntiAlias(true);

        Paint.Style progressBackgroundStyle = Paint.Style.FILL_AND_STROKE;
        progressBackgroundPaint = new Paint();
        progressBackgroundPaint.setStyle(progressBackgroundStyle);
        progressBackgroundPaint.setColor(progressBackgroundColor);
        progressBackgroundPaint.setAntiAlias(true);

        circleBounds = new RectF();
        setProgress(progress);
    }

    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        calculateBounds(w, h);
    }

    private void calculateBounds(int w, int h) {
        radius = w / 2f;

        float progressWidth = progressPaint.getStrokeWidth();
        float progressBackgroundWidth = progressBackgroundPaint.getStrokeWidth();
        float strokeSizeOffset = Math.max(progressWidth, progressBackgroundWidth);
        float halfOffset = strokeSizeOffset / 2f;

        circleBounds.left = halfOffset;
        circleBounds.top = halfOffset;
        circleBounds.right = w - halfOffset;
        circleBounds.bottom = h - halfOffset;

        radius = circleBounds.width() / 2f;
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        drawProgressBackground(canvas);
        drawProgress(canvas);
    }

    private void drawProgressBackground(Canvas canvas) {
        canvas.drawArc(circleBounds, 0, 360, false, progressBackgroundPaint);
    }

    private void drawProgress(Canvas canvas) {
//        LogUtil.d(TAG, "drawProgress drawProgress:" + startAngle + " sweepAngle:" + sweepAngle);
        canvas.drawArc(circleBounds, startAngle, sweepAngle, false, progressPaint);
    }

    private void invalidateEverything() {
        calculateBounds(getWidth(), getHeight());
        requestLayout();
        invalidate();
    }

    private void refreshTheLayout() {
        invalidate();
        requestLayout();
    }

    public void setProgress(float progress) {
        this.progress = progress;
        sweepAngle = progress * 360;
//        LogUtil.d(TAG, "setProgress:" + progress + " sweepAngle:" + sweepAngle);
        invalidate();
    }

    public void setProgressColor(@ColorInt int color) {
        progressPaint.setColor(color);
        invalidate();
    }
}