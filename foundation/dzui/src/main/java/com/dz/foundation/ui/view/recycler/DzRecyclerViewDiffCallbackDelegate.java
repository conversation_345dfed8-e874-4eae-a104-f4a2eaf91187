package com.dz.foundation.ui.view.recycler;

import androidx.recyclerview.widget.DiffUtil;

import java.util.ArrayList;
import java.util.List;


class DzRecyclerViewDiffCallbackDelegate extends DiffUtil.Callback {

    private List<DzRecyclerViewCell> newList = new ArrayList<>();
    private List<DzRecyclerViewCell> oldList = new ArrayList<>();

    public DzRecyclerViewDiffCallbackDelegate(DzRecyclerViewAdapter adapter, List<DzRecyclerViewCell> newCells) {
        this.oldList.addAll(adapter.getAllCells());
        this.newList.addAll(oldList);
        insertOrUpdateNewList(newCells);
        adapter.setCells(newList);
    }

    private void insertOrUpdateNewList(List<? extends DzRecyclerViewCell> newCells) {
        for (DzRecyclerViewCell newCell : newCells) {
            int index = indexOf(newList, newCell);
            if (index != -1) {
                newList.set(index, newCell);
            } else {
                newList.add(newCell);
            }
        }
    }

    private int indexOf(List<? extends DzRecyclerViewCell> cells, DzRecyclerViewCell cell) {
        for (DzRecyclerViewCell c : cells) {
            if (c.getItemId() == cell.getItemId()) {
                return cells.indexOf(c);
            }
        }
        return -1;
    }

    @Override
    public int getOldListSize() {
        return oldList.size();
    }

    @Override
    public int getNewListSize() {
        return newList.size();
    }


    @Override
    public boolean areItemsTheSame(int oldItemPosition, int newItemPosition) {
        return oldList.get(oldItemPosition).getItemId() == newList.get(newItemPosition).getItemId();
    }

    @Override
    public boolean areContentsTheSame(int oldItemPosition, int newItemPosition) {
        return ((Updatable) oldList.get(oldItemPosition)).areContentsTheSame(newList.get(newItemPosition).getViewData());
    }

    @Override
    public Object getChangePayload(int oldItemPosition, int newItemPosition) {
        return ((Updatable) oldList.get(oldItemPosition)).getChangePayload(newList.get(newItemPosition).getViewData());
    }

}
