package com.dz.foundation.ui.widget

import android.content.Context
import android.util.AttributeSet
import androidx.viewpager.widget.ViewPager

open class DzViewPager @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ViewPager(context, attrs), DzWidget {

    init {
        overScrollMode = OVER_SCROLL_NEVER
        this.initShapeBackground(context, attrs, defStyleAttr)
    }
}