package com.dz.foundation.ui.view;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.text.TextPaint;
import android.util.AttributeSet;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.dz.foundation.ui.R;
import com.dz.foundation.ui.widget.DzTextView;


/**
 * @author: shidz
 * @Date： 2021/11/13$-19:25$
 * @Des: 有描边功能 的textView
 */
public class StrokeTextView extends DzTextView {
    private TextView borderText = null;///用于描边的TextView
    private int mStrokeColor;

    private float mStrokeWidth;
    private TextPaint tp1;

    public StrokeTextView(@NonNull Context context) {
        this(context, null);
    }

    public StrokeTextView(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public StrokeTextView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        //获取自定义的XML属性名称
        borderText = new TextView(context, attrs, defStyleAttr);
        TypedArray a = context.obtainStyledAttributes(attrs, R.styleable.StrokeTextView);

        //获取对应的属性值
        this.mStrokeColor = a.getColor(R.styleable.StrokeTextView_strokeColor, 0xffffff);
        this.mStrokeWidth = a.getDimension(R.styleable.StrokeTextView_strokeWidth, 0);
        a.recycle();

        doInit();
    }


    public void doInit() {
        tp1 = borderText.getPaint();
        tp1.setStrokeWidth(mStrokeWidth);//设置描边宽度
        tp1.setStyle(Paint.Style.FILL_AND_STROKE);//对文字只描边
        tp1.setAntiAlias(true);
        tp1.setStrokeCap(Paint.Cap.ROUND);
        tp1.setStrokeJoin(Paint.Join.ROUND);
        borderText.setTextColor(mStrokeColor);  //设置描边颜色
        borderText.setGravity(getGravity());
    }

    public void setStrokeColor(int mStrokeColor) {
        this.mStrokeColor = mStrokeColor;
        borderText.setTextColor(mStrokeColor);
        postInvalidate();
    }

    public void setStrokeWidth(float mStrokeWidth) {
        this.mStrokeWidth = mStrokeWidth;
        tp1.setStrokeWidth(mStrokeWidth);//设置描边宽度
        postInvalidate();
    }

    @Override
    public void setLayoutParams(ViewGroup.LayoutParams params) {
        super.setLayoutParams(params);
        borderText.setLayoutParams(params);
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        CharSequence tt = borderText.getText();

        //两个TextView上的文字必须一致
        if (tt == null || !tt.equals(this.getText())) {
            borderText.setText(getText());
            this.postInvalidate();
        }
        //需要重新设置一下textview的宽度（宽度为文字宽度加上描边大小）不然显示会有
        float textMeasureWith = tp1.measureText(borderText.getText().toString());
        int width = (int) (textMeasureWith + getPaddingLeft() + getPaddingRight() + mStrokeWidth);
        widthMeasureSpec = MeasureSpec.makeMeasureSpec(width, MeasureSpec.EXACTLY);
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        borderText.measure(widthMeasureSpec, heightMeasureSpec);
    }

    @Override
    protected void onLayout(boolean changed, int left, int top, int right, int bottom) {
        super.onLayout(changed, left, top, right, bottom);
        borderText.layout(left, top, right, bottom);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        borderText.draw(canvas);
        super.onDraw(canvas);
    }

}
