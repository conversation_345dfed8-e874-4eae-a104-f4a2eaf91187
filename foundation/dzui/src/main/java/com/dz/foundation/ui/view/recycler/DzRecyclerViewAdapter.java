package com.dz.foundation.ui.view.recycler;

import android.content.Context;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.DiffUtil;
import androidx.recyclerview.widget.RecyclerView;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

/**
 * @Auth shidz
 * @Date 2019/12/31 10:05
 * @Des 通用的RecyclerView cell adapter
 */
public class DzRecyclerViewAdapter extends RecyclerView.Adapter<DzRecyclerViewAdapter.AdapterCellHolder> implements DzRecyclerViewCellOperations {


    private Context mContext;


    private static int typeNum = 0;
    private final static HashMap<Class, Integer> cellTypeMap = new HashMap<>();
    private HashMap<Integer, ArrayList<DzRecyclerViewCell>> typeCellsMap = new HashMap<>();

    public int getCellType(DzRecyclerViewCell adapterCell) {

        if (adapterCell != null) {
            if (!cellTypeMap.containsKey(adapterCell.getViewClass())) {
                typeNum += 1;
                cellTypeMap.put(adapterCell.getViewClass(), typeNum);
            }
            Integer integer = cellTypeMap.get(adapterCell.getViewClass());
            return integer;
        }

        return -1;
    }

    private DzRecyclerViewCell getTypeCell(int type) {
        ArrayList<DzRecyclerViewCell> adapterCells = typeCellsMap.get(type);
        if (adapterCells != null && adapterCells.size() > 0) {
            return adapterCells.get(0);
        }
        return null;
    }

    private ArrayList<DzRecyclerViewCell> cellList = new ArrayList<>();
    private ArrayList<DzRecyclerViewCell> contentCellList = new ArrayList<>();
    private ArrayList<DzRecyclerViewCell> headerCellList = new ArrayList<>();
    private ArrayList<DzRecyclerViewCell> footCellList = new ArrayList<>();


    public void addHeaderCell(DzRecyclerViewCell recyclerViewCell) {
        this.headerCellList.add(recyclerViewCell);
        addCell(headerCellList.size() - 1, recyclerViewCell);
    }

    public void removeHeaderCell(DzRecyclerViewCell recyclerViewCell) {
        headerCellList.remove(recyclerViewCell);
        removeCell(recyclerViewCell);
    }


    public void addFootCell(DzRecyclerViewCell recyclerViewCell) {
        footCellList.add(recyclerViewCell);
        int atPosition = headerCellList.size() + contentCellList.size() + footCellList.size() - 1;
        addCell(atPosition, recyclerViewCell);
    }

    public void removeFootCell(DzRecyclerViewCell recyclerViewCell) {
        footCellList.remove(recyclerViewCell);
        removeCell(recyclerViewCell);
    }

    public DzRecyclerViewAdapter(Context mContext) {
        this.mContext = mContext;
        setHasStableIds(true);
    }


    @Override
    public AdapterCellHolder onCreateViewHolder(ViewGroup parent, int viewType) {

        DzRecyclerViewCell cell = getTypeCell(viewType);
    //    Log.d("DzRecyclerView", "onCreateViewHolder viewType:" + getCellType(cell) + " class: " + cell.getViewClass().getName() + " viewTypeSize:" + cellTypeMap.size());
        final AdapterCellHolder viewHolder = cell.onCreateViewHolder((DzRecyclerView) parent);
        return viewHolder;
    }

    @Override
    public void onBindViewHolder(AdapterCellHolder holder, int position) {
        DzRecyclerViewCell adapterCell = cellList.get(position);
        //    Log.d("DzRecyclerView", "onBindViewHolder position:" + position + " viewData:" + adapterCell.getViewData() + " cellSize:" + getAllCells().size());
        if (adapterCell != null) {
            adapterCell.onBindViewHolder(holder, holder.itemView, position, holder.itemView.getContext(), adapterCell.getViewData());
        }
    }

    @Override
    public void onBindViewHolder(@NonNull AdapterCellHolder holder, int position, @NonNull List<Object> payloads) {

        //  Log.d("DzRecyclerView", "onBindViewHolder position:" + position + " payloads:" + payloads);
        if (payloads != null && payloads.size() > 0) {
            DzRecyclerViewCell cell = cellList.get(position);
            if (cell != null) {
                cell.setViewData(payloads.get(0));
                cell.onBindViewHolder(holder, holder.itemView, position, holder.itemView.getContext(), cell.getViewData());
            }
        } else {
            this.onBindViewHolder(holder, position);
        }

    }

    @Override
    public int getItemCount() {
        return cellList.size();
    }


    @Override
    public long getItemId(int position) {
        return cellList.get(position).getItemId();
    }

    @Override
    public int getItemViewType(int position) {
        int type = -1;
        if (cellList != null && cellList.size() > position) {
            DzRecyclerViewCell adapterCell = cellList.get(position);
            type = getCellType(adapterCell);
        }
        return type;
    }

    private void addCellType(DzRecyclerViewCell cell) {
        if (!isCellTypeAdded(cell)) {
            ArrayList<DzRecyclerViewCell> cells = new ArrayList<>();
            cells.add(cell);
            int cellType = getCellType(cell);
            typeCellsMap.put(cellType, cells);
        } else {
            int cellType = getCellType(cell);
            typeCellsMap.get(cellType).add(cell);
        }
    }


    private boolean isCellTypeAdded(DzRecyclerViewCell cell) {
        return cellTypeMap.containsKey(cell.getViewClass()) && typeCellsMap.get(cellTypeMap.get(cell.getViewClass())) != null;
    }

    @Override
    public void addCell(DzRecyclerViewCell cell) {
        addCell(cellList.size(), cell);
    }

    @Override
    public void addCell(int atPosition, DzRecyclerViewCell cell) {
        cellList.add(atPosition, cell);
        addCellType(cell);
        notifyItemInserted(atPosition);
    }


    public void addContentCell(DzRecyclerViewCell cell) {
        addContentCell(contentCellList.size(), cell);
    }

    public void addContentCell(int atPosition, DzRecyclerViewCell cell) {
        contentCellList.add(atPosition, cell);
        int realPosition = headerCellList.size() + atPosition;
        addCell(realPosition, cell);
    }

    public void addContentCells(List<? extends DzRecyclerViewCell> cells) {
        if (cells.isEmpty()) {
            return;
        }
        int initialSize = this.headerCellList.size() + this.contentCellList.size();
        for (DzRecyclerViewCell cell : cells) {
            addCellType(cell);
        }
        this.contentCellList.addAll(cells);
        this.cellList.addAll(cells);
        notifyItemRangeInserted(initialSize, cells.size());
    }

    @Override
    public void addCells(List<? extends DzRecyclerViewCell> cells) {
        if (cells.isEmpty()) {
            return;
        }

        int initialSize = this.cellList.size();
        for (DzRecyclerViewCell cell : cells) {
            this.cellList.add(cell);
            addCellType(cell);
        }

        notifyItemRangeInserted(initialSize, cells.size());
    }

    @Override
    public void insertedCells(int atPosition, List<? extends DzRecyclerViewCell> cells) {
        if (cells.isEmpty()) {
            return;
        }
        int index = atPosition;
        for (DzRecyclerViewCell cell : cells) {
            this.cellList.add(index, cell);
            addCellType(cell);
            index++;
        }

        notifyItemRangeInserted(atPosition, cells.size());

    }

    public ArrayList<DzRecyclerViewCell> getAllCells() {
        return cellList;
    }


    @Override
    public void addOrUpdateCell(DzRecyclerViewCell cell) {
        addOrUpdateCells(Collections.singletonList(cell));
    }

    @Override
    public void addOrUpdateCells(List<DzRecyclerViewCell> cells) {
        DzRecyclerViewDiffCallbackDelegate callbackDelegate = new DzRecyclerViewDiffCallbackDelegate(this, cells);
        DiffUtil.DiffResult diffResult = DiffUtil.calculateDiff(callbackDelegate);
        diffResult.dispatchUpdatesTo(this);
    }

    @Override
    public void removeCell(DzRecyclerViewCell cell) {
        if (cell == null) {
            return;
        }
        int position = cellList.indexOf(cell);
        //      Log.d("DzRecyclerView", "removeCell :" + cell.getViewClass().getName() + " position:" + position);
        cellList.remove(cell);
        checkCellRemove(cell);
        notifyItemRemoved(position);
    }

    @Override
    public void removeCell(int atPosition) {
        if (atPosition < 0 || atPosition >= cellList.size()) {
            return;
        }
        removeCell(cellList.get(atPosition));
    }


    public void removeCells(int fromPosition, int toPosition) {
        if (cellList != null) {
            ArrayList<DzRecyclerViewCell> temList = new ArrayList<>();
            for (int i = fromPosition; i <= toPosition; i++) {
                DzRecyclerViewCell cell = cellList.get(i);
                checkCellRemove(cell);
                temList.add(cell);
            }
            cellList.removeAll(temList);
            temList.clear();
            notifyItemRangeRemoved(fromPosition, toPosition - fromPosition + 1);
        }
    }

    @Override
    public void removeCells(List<? extends DzRecyclerViewCell> cells) {
        if (cells != null) {
            for (int i = 0; i < cells.size(); i++) {
                DzRecyclerViewCell cell = cells.get(i);
                checkCellRemove(cell);
            }
            cellList.removeAll(cells);
            notifyDataSetChanged();
        }
    }

    @Override
    public void removeAllCells() {
        if (cellList != null) {
            cellList.clear();
        }

        contentCellList.clear();
        headerCellList.clear();
        footCellList.clear();

        if (typeCellsMap != null) {
            typeCellsMap.clear();
        }
        notifyDataSetChanged();
    }

    public void removeAllFoot() {
        for (int i = 0; i < footCellList.size(); i++) {
            removeCell(footCellList.get(i));
        }
        footCellList.clear();
        notifyDataSetChanged();
    }

    @Override
    public void updateCell(DzRecyclerViewCell cell, Object payloads) {
        int cellPosition = getCellPosition(cell);
        updateCell(cellPosition, payloads);
    }

    @Override
    public void updateCell(int atPosition, Object newData) {
        if (cellList == null) {
            return;
        }

        if (atPosition < 0 || atPosition >= cellList.size()) {
            return;
        }

        DzRecyclerViewCell dzRecyclerViewCell = cellList.get(atPosition);
        if (dzRecyclerViewCell != null) {
            dzRecyclerViewCell.setViewData(newData);
        }
        notifyItemChanged(atPosition, newData);
    }

    @Override
    public void updateCells(int fromPosition, int toPosition, List<Object> payloads) {

        notifyItemRangeChanged(fromPosition, toPosition - fromPosition + 1, payloads);
    }

    /**
     * 删除cell后检测 该类型的cell 是否为空
     *
     * @param cell
     */
    private void checkCellRemove(DzRecyclerViewCell cell) {
        int cellType = getCellType(cell);
        ArrayList<DzRecyclerViewCell> cells = typeCellsMap.get(cellType);
        if (cells != null) {
            if (cells.contains(cell)) {
                cells.remove(cell);
            }
            if (cells.size() <= 0) {
                typeCellsMap.remove(cellType);
            }
        }


    }

    public void setCells(List<DzRecyclerViewCell> newList) {
        this.cellList.clear();
        this.cellList.addAll(newList);
    }

    public boolean containCell(DzRecyclerViewCell cell) {
        return cellList.contains(cell);
    }

    public static class AdapterCellHolder extends RecyclerView.ViewHolder {

        protected DzRecyclerView recyclerView;

        public AdapterCellHolder(View itemView, DzRecyclerView recyclerView) {
            super(itemView);
            this.recyclerView = recyclerView;
        }

    }

    public DzRecyclerViewCell getCell(int position) {
        if (cellList != null && position < cellList.size()) {
            return cellList.get(position);
        }
        return null;
    }

    public int getCellPosition(DzRecyclerViewCell cell) {
        int index = cellList == null ? -1 : cellList.indexOf(cell);
        return index;
    }
}
