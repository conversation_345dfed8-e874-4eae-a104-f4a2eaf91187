package com.dz.foundation.ui.view.fastscroll.provider;

import android.view.View;

/**
 * @Author: wanxin
 * @Date: 2022/10/19 15:03
 * @Description:
 * @Version: 1.0
 */
public class ScrollBarUtils {

    public static float getViewRawY(View view) {
        int[] location = new int[2];
        location[1] = (int) view.getY();
        ((View) view.getParent()).getLocationInWindow(location);
        return location[1];
    }

    public static float getViewRawX(View view) {
        int[] location = new int[2];
        location[0] = (int) view.getX();
        ((View) view.getParent()).getLocationInWindow(location);
        return location[0];
    }

    public static float getValueInRange(float min, float max, float value) {
        float minimum = Math.max(min, value);
        return Math.min(minimum, max);
    }

}
