package com.dz.foundation.ui.view.navigation;

/**
 * Created by wxliao on 17/3/28.
 */

public interface NavigationAble {
    void select(BottomBarLayout.TabItemBean tabItemBean);

    void unSelect(BottomBarLayout.TabItemBean tabItemBean);

    void showNewMessage();

    void showNewMessage(String msg);

    void hideNewMessage();

    void setShowMessageAlways(boolean auto);

    /**
     * @param isNormal 红点背景样式，默认true，外边框为白色；false，外边框为黑色
     */
    void setNewMessageStroke(Boolean isNormal);
}
