package com.dz.foundation.ui.view.recycler.decoration

import android.graphics.Rect
import android.view.View
import androidx.recyclerview.widget.RecyclerView

/**
 *@Author: zhanggy
 *@Date: 2025-04-03
 *@Description:为水平RV前后添加额外的空白区域
 *@Version:1.0
 */
class HorizontalSpaceItemDecoration(private val startSpace: Int, private val endSpace: Int) : RecyclerView.ItemDecoration() {

    override fun getItemOffsets(outRect: Rect, view: View, parent: RecyclerView, state: RecyclerView.State) {
        if (parent.getChildAdapterPosition(view) == 0) {
            outRect.left = startSpace
        }
        if (parent.getChildAdapterPosition(view) == state.itemCount - 1) {
            outRect.right = endSpace
        }
    }
}