package com.dz.foundation.ui.utils.click

import android.os.SystemClock
import android.view.View
import com.dz.foundation.base.utils.LogUtil
import com.dz.foundation.ui.R

import java.util.ArrayList

class OnClickListenerWrapper(private val onClickListener: View.OnClickListener) :
    View.OnClickListener {
    private var viewLastClickTime = 0L
    private val interceptors: ArrayList<ClickInterceptor> = ArrayList()
    fun addInterceptor(clickInterceptor: ClickInterceptor) {
        if (interceptors.contains(clickInterceptor)) {
            return
        }
        interceptors.add(clickInterceptor)
    }

    override fun onClick(v: View) {

        val currentTimeMillis = SystemClock.elapsedRealtime()
        val globalDistance = currentTimeMillis - lastGlobalClickTime
        val viewDistance = currentTimeMillis - viewLastClickTime
        val viewIntervalMills = getViewIntervalMills(v)
        LogUtil.d(
            "ClickEvent",
            "globalIntervalMills:" + ClickEventHandler.GLOBAL_CLICK_INTERVAL_MILLS + " globalDistance:" + globalDistance + " viewIntervalMills:" + viewIntervalMills + " viewDistance:" + viewDistance
        )
        if (globalDistance < ClickEventHandler.GLOBAL_CLICK_INTERVAL_MILLS || v.id == lastClickViewId && viewDistance < viewIntervalMills) {
            return
        } else {


            val intercept = doIntercept(v)
            if (!intercept) {
                onClickListener.onClick(v)
            }
            GlobalListenerManager.doClick(v)
        }
        viewLastClickTime = currentTimeMillis
        lastGlobalClickTime = currentTimeMillis
        lastClickViewId = v.id
    }

    private fun getViewIntervalMills(v: View): Long {
        var intervalMills = ClickEventHandler.SAME_VIEW_CLICK_INTERVAL_MILLS
        val viewIntervalMills = v.getTag(R.id.dzui_view_click_interval_tag)
        if (viewIntervalMills != null && viewIntervalMills is Long) {
            if (viewIntervalMills > 0) {
                intervalMills = viewIntervalMills
            }
        }
        return intervalMills
    }

    private fun doIntercept(view: View): Boolean {
        if (interceptors.size == 0) {
            return false
        }
        for (interceptor in interceptors) {
            if (interceptor.onIntercept(view)) {
                return true
            }
        }
        return false
    }

    companion object {
        var lastClickViewId = 0
        var lastGlobalClickTime = 0L
    }
}