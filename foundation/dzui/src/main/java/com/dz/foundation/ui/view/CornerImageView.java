package com.dz.foundation.ui.view;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffXfermode;
import android.graphics.RectF;
import android.util.AttributeSet;

import com.dz.foundation.ui.R;
import com.dz.foundation.ui.widget.DzImageView;


/**
 * <AUTHOR>
 */

public class CornerImageView extends DzImageView {

    protected static final int DEFAULT_RADIUS_SIZE = 10;

    private final RectF roundRect = new RectF();
    private float rectAdius = 8f;
    private final Paint maskPaint = new Paint();
    private final Paint zonePaint = new Paint();

    public CornerImageView(Context context) {
        this(context, null);
    }

    public CornerImageView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public CornerImageView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        if (attrs != null) {
            TypedArray array = getContext().obtainStyledAttributes(attrs, R.styleable.CornerImageView, 0, 0);
            rectAdius = array.getDimensionPixelSize(R.styleable.CornerImageView_corner_radius, 0);
            array.recycle();
        }
        init();
    }

    private void init() {
        initCorner();
    }


    private void initCorner() {
        maskPaint.setAntiAlias(true);
        maskPaint.setXfermode(new PorterDuffXfermode(PorterDuff.Mode.SRC_IN));
        zonePaint.setAntiAlias(true);
        zonePaint.setColor(Color.WHITE);
//        float density = getResources().getDisplayMetrics().density;
//        rectAdius = rectAdius * density;
    }

    @Override
    protected void onLayout(boolean changed, int left, int top, int right, int bottom) {
        super.onLayout(changed, left, top, right, bottom);
        int w = getWidth();
        int h = getHeight();
        roundRect.set(0, 0, w, h);
    }

    @Override
    public void draw(Canvas canvas) {
        canvas.saveLayer(roundRect, zonePaint, Canvas.ALL_SAVE_FLAG);
        canvas.drawRoundRect(roundRect, rectAdius, rectAdius, zonePaint);
        canvas.saveLayer(roundRect, maskPaint, Canvas.ALL_SAVE_FLAG);
        super.draw(canvas);
        canvas.restore();
    }

    public void setDrawableRadius(float bookRadius) {
        float density = getResources().getDisplayMetrics().density;
        rectAdius = bookRadius * density;
        invalidate();
    }

}
