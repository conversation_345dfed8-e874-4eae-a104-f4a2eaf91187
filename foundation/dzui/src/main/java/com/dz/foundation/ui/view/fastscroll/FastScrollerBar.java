package com.dz.foundation.ui.view.fastscroll;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;

import androidx.core.graphics.drawable.DrawableCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.dz.foundation.ui.R;
import com.dz.foundation.ui.view.fastscroll.provider.DefaultScrollerViewProvider;
import com.dz.foundation.ui.view.fastscroll.provider.RecyclerScrollListener;
import com.dz.foundation.ui.view.fastscroll.provider.ScrollBarUtils;
import com.dz.foundation.ui.view.fastscroll.provider.ScrollerViewProvider;

/**
 * @Author: wanxin
 * @Date: 2022/10/19 14:54
 * @Description:
 * @Version: 1.0
 */
public class FastScrollerBar extends LinearLayout {

    private static final int STYLE_NONE = -1;
    private final RecyclerScrollListener scrollListener = new RecyclerScrollListener(this);
    private RecyclerView recyclerView;
    private OnScrollStopListener onScrollStopListener;

    private ImageView handleBar;
    private int handleColor = STYLE_NONE;
    private int scrollerOrientation;

    private boolean manuallyChangingPosition;

    private ScrollerViewProvider viewProvider;

    public FastScrollerBar(Context context) {
        this(context, null);
    }

    public FastScrollerBar(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public FastScrollerBar(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        setAlpha(0f);
        setClipChildren(false);
//        TypedArray style = context.obtainStyledAttributes(attrs, R.styleable.FastScroller);
//        try {
//            handleColor = style.getColor(R.styleable.FastScroller_fastscroll_handleColor, STYLE_NONE);
//        } finally {
//            style.recycle();
//        }
        setViewProvider(new DefaultScrollerViewProvider());
    }


    public void setViewProvider(ScrollerViewProvider viewProvider) {
        removeAllViews();
        this.viewProvider = viewProvider;
        viewProvider.setFastScroller(this);
        handleBar = viewProvider.provideHandleView(this);
        addView(handleBar);
    }

    public void setBarIsNightStyle(boolean isNightStyle) {
        if (isNightStyle) {
            handleBar.setBackgroundResource(R.drawable.dzui_fastscroll_handle_night);
        } else {
            handleBar.setBackgroundResource(R.drawable.dzui_fastscroll_handle);
        }
    }


    public void setRecyclerView(RecyclerView recyclerView) {
        this.recyclerView = recyclerView;
        recyclerView.addOnScrollListener(scrollListener);
    }

    //设置停止滚动回调
    public void setStopScrollListener(OnScrollStopListener listener) {
        this.onScrollStopListener = listener;
        scrollListener.setOnScrollStopListener(listener);
    }


    @Override
    public void setOrientation(int orientation) {
        scrollerOrientation = orientation;
        //switching orientation, because orientation in linear layout
        //is something different than orientation of fast scroller
        super.setOrientation(orientation == HORIZONTAL ? VERTICAL : HORIZONTAL);
    }


    public void setHandleColor(int color) {
        handleColor = color;
        invalidate();
    }


    public void addScrollerListener(RecyclerScrollListener.ScrollerListener listener) {
        scrollListener.addScrollerListener(listener);
    }

    @Override
    protected void onLayout(boolean changed, int l, int t, int r, int b) {
        super.onLayout(changed, l, t, r, b);

        initHandleMovement();
        applyStyling(); // this doesn't belong here, even if it works

        if (!isInEditMode()) {
            //sometimes recycler starts with a defined scroll (e.g. when coming from saved state)
            scrollListener.updateHandlePosition(recyclerView);
        }

    }

    private void applyStyling() {
        if (handleColor != STYLE_NONE) setBackgroundTint(handleBar, handleColor);
    }

    private void setBackgroundTint(View view, int color) {
        final Drawable background = DrawableCompat.wrap(view.getBackground());
        DrawableCompat.setTint(background.mutate(), color);
        view.setBackground(background);
    }

    @SuppressLint("ClickableViewAccessibility")
    private void initHandleMovement() {
        handleBar.setOnTouchListener(new OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                requestDisallowInterceptTouchEvent(true);
                if (event.getAction() == MotionEvent.ACTION_DOWN || event.getAction() == MotionEvent.ACTION_MOVE) {
                    manuallyChangingPosition = true;
                    if (event.getAction() == MotionEvent.ACTION_MOVE) {
                        float relativePos = getRelativeTouchPosition(event);
                        setScrollerPosition(relativePos);
                        setRecyclerViewPosition(relativePos);
                    }
                    clearAnimation();
                    animate().cancel();
                    setAlpha(1f);
                    return true;
                } else if (event.getAction() == MotionEvent.ACTION_UP) {
                    //抬起停止滚动
                    manuallyChangingPosition = false;
                    animate().cancel();
                    animate().alpha(0f).setDuration(3000).start();
                    if (onScrollStopListener != null) {
                        onScrollStopListener.onScrollStop();
                    }
                    return true;
                }
                return false;
            }
        });
    }

    private float getRelativeTouchPosition(MotionEvent event) {
        if (isVertical()) {
            float yInParent = event.getRawY() - ScrollBarUtils.getViewRawY(handleBar) - handleBar.getHeight() / 2f;
            return yInParent / (getHeight() - handleBar.getHeight());
        } else {
            float xInParent = event.getRawX() - ScrollBarUtils.getViewRawX(handleBar);
            return xInParent / (getWidth() - handleBar.getWidth());
        }
    }


    private boolean isRecyclerViewNotScrollable() {
        if (isVertical()) {
            return recyclerView.getChildAt(0).getHeight() * recyclerView.getAdapter().getItemCount() <= recyclerView.getHeight();
        } else {
            return recyclerView.getChildAt(0).getWidth() * recyclerView.getAdapter().getItemCount() <= recyclerView.getWidth();
        }
    }

    private void setRecyclerViewPosition(float relativePos) {
        if (recyclerView == null) return;
        int itemCount = recyclerView.getAdapter().getItemCount();
        int targetPos = (int) ScrollBarUtils.getValueInRange(0, itemCount - 1, (int) (relativePos * (float) itemCount));
        recyclerView.scrollToPosition(targetPos);
    }

    public void setScrollerPosition(float relativePos) {
        if (isVertical()) {
            handleBar.setY(ScrollBarUtils.getValueInRange(
                    0,
                    getHeight() - handleBar.getHeight(),
                    relativePos * (getHeight() - handleBar.getHeight()))
            );
        } else {
            handleBar.setX(ScrollBarUtils.getValueInRange(
                    0,
                    getWidth() - handleBar.getWidth(),
                    relativePos * (getWidth() - handleBar.getWidth()))
            );
        }
    }

    public boolean isVertical() {
        return scrollerOrientation == VERTICAL;
    }

    public boolean shouldUpdateHandlePosition() {
        return handleBar != null && !manuallyChangingPosition && recyclerView.getChildCount() > 0;
    }

    public ScrollerViewProvider getViewProvider() {
        return viewProvider;
    }

    public interface OnScrollStopListener {
        void onScrollStop();
    }
}
