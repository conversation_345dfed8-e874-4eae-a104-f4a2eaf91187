package com.dz.foundation.ui.utils

import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.widget.ViewPager2
import com.dz.foundation.base.utils.LogUtil
import java.lang.reflect.Field


/**
 * 降级ViewPager2灵敏度
 */
fun ViewPager2.desensitization() {
    //动态设置ViewPager2 灵敏度
    //先设置成不降低灵敏度
//    try {
//        val recyclerViewField: Field = ViewPager2::class.java.getDeclaredField("mRecyclerView")
//        recyclerViewField.isAccessible = true
//        val recyclerView = recyclerViewField.get(this) as RecyclerView
//        val touchSlopField: Field = RecyclerView::class.java.getDeclaredField("mTouchSlop")
//        touchSlopField.isAccessible = true
//        val touchSlop = touchSlopField.get(recyclerView) as Int
//        LogUtil.d("desensitization","touchSlop改前 "+touchSlop)
//        LogUtil.d("desensitization","touchSlop改后 "+(touchSlop * 1.5).toInt())
//        touchSlopField.set(recyclerView, (touchSlop * 0.5).toInt())
//    } catch (ignore: Exception) {
//    }
}
