package com.dz.foundation.ui.autosize

import android.app.Activity
import android.content.pm.ActivityInfo
import android.util.DisplayMetrics
import com.dz.foundation.base.utils.LogUtil
import me.jessyan.autosize.AutoAdaptStrategy
import me.jessyan.autosize.AutoSizeConfig
import me.jessyan.autosize.DefaultAutoAdaptStrategy

/**
 *@Author: zhanggy
 *@Date: 2022-12-08
 *@Description: 使用自定义的适配策略来执行AutoSize适配
 *@Version:1.0
 */
class DzAdapterStrategy : AutoAdaptStrategy {

    private val autoStrategy = DefaultAutoAdaptStrategy()

    companion object {
        /**
         * 有些Activity不需要进行适配
         * 将这些Activity的名字添加到这里就可以了
         */
        val dontAdaptActs = listOf(
            "HotSplashActivity", "SplashActivity", "Stub_Activity", "Stub_SingleTask_Activity",
            "FlutterContainerActivity",
            "Stub_SingleTask_Activity_T",
            "Stub_Standard_Activity",
            "Stub_Standard_Activity_T",
            "Stub_Standard_Landscape_Activity",
            "Stub_Standard_Portrait_Activity", "AppPrivacyPolicyActivity",
            "AppDetailInfoActivity",
            "TTDelegateActivity",
            "JumpKllkActivity",
            "DownloadTaskDeleteActivity",
            "JumpUnknownSourceActivity"
        )
    }


    override fun applyAdapt(target: Any?, activity: Activity?) {
        if (activity == null) {
            return
        }
        dontAdaptActs.forEach {
            if (activity.javaClass.name.contains(it)) {
                return
            }
        }
        try {

            val dm: DisplayMetrics = activity.resources.displayMetrics
            val ratio = dm.heightPixels.toFloat() / dm.widthPixels.toFloat()
            val threshold = 4.0f / 3.0f
            if (activity.requestedOrientation== ActivityInfo.SCREEN_ORIENTATION_PORTRAIT&& ratio < threshold) {  // 根据高度与宽度的比例来决定是否启用AutoSize
                AutoSizeConfig.getInstance().stop(activity)  // AutoSize全部停用
                LogUtil.d(TAG, "没有进行适配：$activity")
            } else {
                autoStrategy.applyAdapt(target, activity)
                LogUtil.d(TAG, "已进行适配：$activity")
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

}

private const val TAG = "AutoSize"