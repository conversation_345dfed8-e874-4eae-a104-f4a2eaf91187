package com.dz.foundation.ui.view.fastscroll.provider;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.dz.foundation.ui.view.fastscroll.FastScrollerBar;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: wanxin
 * @Date: 2022/10/19 14:56
 * @Description:
 * @Version: 1.0
 */
public class RecyclerScrollListener extends RecyclerView.OnScrollListener {

    private final FastScrollerBar scrollerBar;
    private FastScrollerBar.OnScrollStopListener onScrollStopListener;

    List<ScrollerListener> listeners = new ArrayList<>();
    int oldScrollState = RecyclerView.SCROLL_STATE_IDLE;

    public RecyclerScrollListener(FastScrollerBar scroller) {
        this.scrollerBar = scroller;
    }

    public void addScrollerListener(ScrollerListener listener) {
        listeners.add(listener);
    }

    public void setOnScrollStopListener(FastScrollerBar.OnScrollStopListener listener) {
        onScrollStopListener = listener;
    }

    @Override
    public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newScrollState) {
        super.onScrollStateChanged(recyclerView, newScrollState);
        if (newScrollState == RecyclerView.SCROLL_STATE_IDLE && oldScrollState != RecyclerView.SCROLL_STATE_IDLE) {
            //停止滑动
            if (onScrollStopListener != null) {
                onScrollStopListener.onScrollStop();
            }
            scrollerBar.animate().cancel();
            scrollerBar.animate().alpha(0f).setDuration(3000).start();
        } else if (newScrollState != RecyclerView.SCROLL_STATE_IDLE && oldScrollState == RecyclerView.SCROLL_STATE_IDLE) {
            //开始滑动
            scrollerBar.animate().cancel();
            scrollerBar.animate().alpha(1f).setDuration(300).start();
        }
        oldScrollState = newScrollState;
    }

    @Override
    public void onScrolled(RecyclerView rv, int dx, int dy) {
        if (scrollerBar.shouldUpdateHandlePosition()) {
            if (oldScrollState == RecyclerView.SCROLL_STATE_IDLE) {
                if (onScrollStopListener != null) {
                    onScrollStopListener.onScrollStop();
                }
            }
            updateHandlePosition(rv);
        }
    }

    public void updateHandlePosition(RecyclerView rv) {
        float relativePos;
        if (scrollerBar.isVertical()) {
            int offset = rv.computeVerticalScrollOffset();
            int extent = rv.computeVerticalScrollExtent();
            int range = rv.computeVerticalScrollRange();
            relativePos = offset / (float) (range - extent);
        } else {
            int offset = rv.computeHorizontalScrollOffset();
            int extent = rv.computeHorizontalScrollExtent();
            int range = rv.computeHorizontalScrollRange();
            relativePos = offset / (float) (range - extent);
        }
        scrollerBar.setScrollerPosition(relativePos);
        notifyListeners(relativePos);
    }

    public void notifyListeners(float relativePos) {
        for (ScrollerListener listener : listeners) listener.onScroll(relativePos);
    }

    public interface ScrollerListener {
        void onScroll(float relativePos);
    }

}
