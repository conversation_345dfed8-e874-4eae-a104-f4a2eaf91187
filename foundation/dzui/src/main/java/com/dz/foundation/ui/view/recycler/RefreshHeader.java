package com.dz.foundation.ui.view.recycler;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 15/11/22.
 */
public interface RefreshHeader {

    int STATE_NORMAL = 0;//完全不显示状态
    int STATE_DOWN = 1;//开始下拉状态，未达到松开刷新
    int STATE_RELEASE_TO_REFRESH = 2;//达到松开刷新状态
    int STATE_REFRESHING = 3;//正在刷新状态
    int STATE_DONE = 4;//刷新完成状态

    void onMove(float delta);

    boolean releaseAction();

    void refreshComplete();

    int getVisibleHeight();

    int getState();

    boolean isOnTop();

}