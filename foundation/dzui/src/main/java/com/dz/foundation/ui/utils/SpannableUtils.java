package com.dz.foundation.ui.utils;

import android.text.Spannable;
import android.text.SpannableString;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.text.style.AbsoluteSizeSpan;
import android.text.style.ForegroundColorSpan;
import android.text.style.StrikethroughSpan;
import android.text.style.StyleSpan;
import android.text.style.UnderlineSpan;

/**
 * 创建一段复杂格式的文本
 *
 * <AUTHOR> 15/9/9.
 */
public class SpannableUtils extends SpannableStringBuilder {

    /**
     * 设置带有下划线的文本
     *
     * @param mainText 文本
     * @return 自身
     */
    public static CharSequence setUnderLine(String mainText, String secondText) {
        if (TextUtils.isEmpty(mainText) || TextUtils.isEmpty(secondText) || !mainText.contains(secondText)) {
            return mainText;
        }
        int start = mainText.indexOf(secondText);
        int end = start + secondText.length();
        SpannableString spanString = new SpannableString(mainText);
        spanString.setSpan(new UnderlineSpan(), start, end, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        return spanString;
    }

    /**
     * 设置带有删除线的文本
     *
     * @param mainText 文本
     * @return 自身
     */
    public static CharSequence setStrike(String mainText, String secondText) {
        if (TextUtils.isEmpty(mainText) || TextUtils.isEmpty(secondText) || !mainText.contains(secondText)) {
            return mainText;
        }
        int start = mainText.indexOf(secondText);
        int end = start + secondText.length();
        SpannableString spanString = new SpannableString(mainText);
        spanString.setSpan(new StrikethroughSpan(), start, end, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        return spanString;
    }

    /**
     * 设置带有色值的文本
     *
     * @param mainText 文本
     * @return 自身
     */
    public static CharSequence setBold(String mainText, String secondText) {
        return setSpannable(mainText, secondText, 0, 0, false);
    }

    /**
     * 设置带有色值的文本
     *
     * @param mainText 文本
     * @return 自身
     */
    public static CharSequence setColor(String mainText, String secondText, int color) {
        return setSpannable(mainText, secondText, 0, color, false);

    }

    /**
     * 设置 字体大小
     *
     * @param mainText 文本
     * @param size     字体大小 像素
     * @return 自身
     */
    public static CharSequence setFontSize(String mainText, String secondText, int size) {
        return setSpannable(mainText, secondText, size, 0, false);
    }

    /**
     * 设置 字体大小 且加粗
     *
     * @param mainText 文本
     * @param size     字体大小 像素
     * @return 自身
     */
    public static CharSequence setBoldFontSize(String mainText, String secondText, int size) {
        return setSpannable(mainText, secondText, size, 0, true);
    }


    public static CharSequence setSpannable(String mainText, String secondText, int size, int color, boolean bold) {
        if (TextUtils.isEmpty(mainText) || TextUtils.isEmpty(secondText) || !mainText.contains(secondText)) {
            return mainText;
        }
        int start = mainText.indexOf(secondText);
        int end = start + secondText.length();

        SpannableString spanString = new SpannableString(mainText);

        if (bold) {
            StyleSpan span = new StyleSpan(android.graphics.Typeface.BOLD);
            spanString.setSpan(span, start, end, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        }

        if (size != 0) {
            AbsoluteSizeSpan span = new AbsoluteSizeSpan(size, true);
            spanString.setSpan(span, start, end, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        }

        if (color != 0) {
            ForegroundColorSpan span = new ForegroundColorSpan(color);
            spanString.setSpan(span, start, end, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        }

        return spanString;
    }

}