package com.dz.foundation.ui.utils

import android.view.ViewTreeObserver
import androidx.core.view.isNotEmpty
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.StaggeredGridLayoutManager

/**
 * 监控RecyclerView 展示完毕后回调。
 */
fun RecyclerView.doOnFirstChildVisibleOnce(callback: () -> Unit) {
    if (isNotEmpty()) {
        callback()
        return
    }

    val layoutListener = object : ViewTreeObserver.OnGlobalLayoutListener {
        override fun onGlobalLayout() {
            if (isNotEmpty()) {
                viewTreeObserver.removeOnGlobalLayoutListener(this)
                callback()
            }
        }
    }

    viewTreeObserver.addOnGlobalLayoutListener(layoutListener)
}


/**
 * 获取第一个可见的item位置
 */
fun RecyclerView.getFirstVisibleItemPosition(): Int {
    return try {
        when (val manager = layoutManager) {
            is LinearLayoutManager -> manager.findFirstVisibleItemPosition()
            is StaggeredGridLayoutManager -> {
                val positions = manager.findFirstVisibleItemPositions(null)
                positions.minOrNull() ?: -1
            }

            else -> -1
        }
    } catch (e: Exception) {
        -1
    }
}

/**
 * 获取最后一个可见的item位置
 */
fun RecyclerView.getLastVisibleItemPosition(): Int {
    return try {
        when (val manager = layoutManager) {
            is LinearLayoutManager -> manager.findLastVisibleItemPosition()
            is StaggeredGridLayoutManager -> {
                val positions = manager.findLastVisibleItemPositions(null)
                positions.maxOrNull() ?: -1
            }

            else -> -1
        }
    } catch (e: Exception) {
        -1
    }
}