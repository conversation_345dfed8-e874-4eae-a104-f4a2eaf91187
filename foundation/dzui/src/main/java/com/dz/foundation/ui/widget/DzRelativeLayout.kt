package com.dz.foundation.ui.widget

import android.content.Context
import android.util.AttributeSet

import android.widget.RelativeLayout


class DzRelativeLayout @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : RelativeLayout(context, attrs, defStyleAttr), DzWidget {

    init {
        initShapeBackground(context, attrs, defStyleAttr)
    }
}