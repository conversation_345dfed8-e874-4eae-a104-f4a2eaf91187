package com.dz.foundation.ui.utils;

import android.text.Spannable;
import android.text.SpannableString;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.text.style.AbsoluteSizeSpan;
import android.text.style.ForegroundColorSpan;
import android.text.style.StrikethroughSpan;
import android.text.style.StyleSpan;

/**
 * 创建一段复杂格式的文本
 *
 * <AUTHOR> 15/9/9.
 */
public class DzSpanBuilder extends SpannableStringBuilder {

    public DzSpanBuilder() {
        super();
    }

    public DzSpanBuilder(CharSequence text) {
        super(text);
    }

    public DzSpanBuilder(CharSequence text, int start, int end) {
        super(text, start, end);
    }

    /**
     * 追加带有删除线的文本
     *
     * @param text 文本
     * @return 自身
     */
    public DzSpanBuilder appendStrike(CharSequence text) {
        if (!TextUtils.isEmpty(text)) {
            StrikethroughSpan span = new StrikethroughSpan();
            SpannableString spanString = new SpannableString(text);
            spanString.setSpan(span, 0, text.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            append(spanString);
        }
        return this;
    }

    /**
     * 追加 加粗的文本
     *
     * @param text 文本
     * @return 自身
     */
    public DzSpanBuilder appendBold(CharSequence text) {
        if (!TextUtils.isEmpty(text)) {
            final StyleSpan span = new StyleSpan(android.graphics.Typeface.BOLD);
            SpannableString spanString = new SpannableString(text);
            spanString.setSpan(span, 0, text.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            append(spanString);
        }
        return this;
    }

    /**
     * 追加 带颜色的文本
     *
     * @param text  文本
     * @param color 颜色值
     * @return 自身
     */
    public DzSpanBuilder appendColor(CharSequence text, int color) {
        if (!TextUtils.isEmpty(text)) {
            ForegroundColorSpan span = new ForegroundColorSpan(color);
            SpannableString spanString = new SpannableString(text);
            spanString.setSpan(span, 0, text.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            append(spanString);
        }
        return this;
    }


    /**
     * 追加 字体大小
     *
     * @param text 文本
     * @param size 字体大小 像素
     * @return 自身
     */
    public DzSpanBuilder appendFontSize(CharSequence text, int size) {
        if (!TextUtils.isEmpty(text)) {
            AbsoluteSizeSpan span = new AbsoluteSizeSpan(size, true);
            SpannableString spanString = new SpannableString(text);
            spanString.setSpan(span, 0, text.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            append(spanString);
        }
        return this;
    }


    /**
     * 追加 字体大小 且加粗
     *
     * @param text 文本
     * @param size 字体大小 像素
     * @return 自身
     */
    public DzSpanBuilder appendBoldFontSize(CharSequence text, int size) {
        if (!TextUtils.isEmpty(text)) {
            SpannableString spanString = new SpannableString(text);

            StyleSpan span = new StyleSpan(android.graphics.Typeface.BOLD);
            spanString.setSpan(span, 0, text.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            AbsoluteSizeSpan span2 = new AbsoluteSizeSpan(size, true);
            spanString.setSpan(span2, 0, text.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);

            append(spanString);
        }
        return this;
    }


    /**
     * 追加 字体大小且自定义颜色
     *
     * @param text 文本
     * @param size 字体大小 像素
     * @return 自身
     */
    public DzSpanBuilder appendColorFontSize(CharSequence text, int color, int size) {
        if (!TextUtils.isEmpty(text)) {
            SpannableString spanString = new SpannableString(text);

            AbsoluteSizeSpan span = new AbsoluteSizeSpan(size, true);
            spanString.setSpan(span, 0, text.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            ForegroundColorSpan span2 = new ForegroundColorSpan(color);
            spanString.setSpan(span2, 0, text.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);

            append(spanString);
        }
        return this;
    }

    /**
     * 追加 字体大小 加粗 且自定义颜色
     *
     * @param text 文本
     * @param size 字体大小 像素
     * @return 自身
     */
    public DzSpanBuilder appendColorBoldFontSize(CharSequence text, int color, int size) {
        if (!TextUtils.isEmpty(text)) {
            SpannableString spanString = new SpannableString(text);

            StyleSpan span = new StyleSpan(android.graphics.Typeface.BOLD);
            spanString.setSpan(span, 0, text.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            AbsoluteSizeSpan span2 = new AbsoluteSizeSpan(size, true);
            spanString.setSpan(span2, 0, text.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            ForegroundColorSpan span3 = new ForegroundColorSpan(color);
            spanString.setSpan(span3, 0, text.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);

            append(spanString);
        }
        return this;
    }


    @Override
    public DzSpanBuilder append(CharSequence text) {
        super.append(text);
        return this;
    }
}