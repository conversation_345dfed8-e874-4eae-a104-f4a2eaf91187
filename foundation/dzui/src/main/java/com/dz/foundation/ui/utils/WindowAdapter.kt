package com.dz.foundation.ui.utils

import android.app.Activity
import android.app.Application
import android.content.pm.ActivityInfo
import android.content.res.Configuration
import android.os.Bundle
import androidx.window.layout.WindowMetricsCalculator
import com.dz.foundation.base.utils.dp
import com.dz.foundation.base.utils.dp2px

/**
 *作者: shidz
 *创建时间 :  2023/9/19 12:59
 *功能描述: 窗体适配器，适配手机，折叠屏，平板
 *
 */
object WindowAdapter {


    enum class WindowSizeType { COMPACT, MEDIUM, EXPANDED }


    class WindowSizeInfo {
        var sizeType: WindowSizeType = WindowSizeType.COMPACT
        var pageSpace = 0f
        var dialogSpace = 0f
        var columnWidth = 0f//栅格宽度
        val defaultSpace = 12.dp.toFloat() //默认栅格间距
    }

    private var windowSizeType = WindowSizeType.COMPACT
    private var windowSizeWidth = 0f
    private var windowSizeHeight = 0f

    var orientation:Int = Configuration.ORIENTATION_UNDEFINED


    private val windowSizeInfo: WindowSizeInfo = WindowSizeInfo()
    fun getWindowSizeInfo(): WindowSizeInfo {
        return windowSizeInfo
    }

    fun getWindowWidth(): Float = windowSizeWidth.dp2px

    fun getWindowHeight(): Float = windowSizeHeight.dp2px

    fun getWindowSizeType(): WindowSizeType =  windowSizeInfo.sizeType
    fun setWindowSizeType(windowSizeType:WindowSizeType){
        this.windowSizeInfo.sizeType = windowSizeType
        this.windowSizeType = windowSizeType
    }

    fun getWindowSpace(): Float = windowSizeInfo.pageSpace

    private val activityLifecycleCallbacks = object : Application.ActivityLifecycleCallbacks {

        override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {
            resetWindowInfo(activity)
        }

        override fun onActivityStarted(activity: Activity) {

        }

        override fun onActivityResumed(activity: Activity) {

        }

        override fun onActivityPaused(activity: Activity) {

        }

        override fun onActivityStopped(activity: Activity) {

        }

        override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {

        }

        override fun onActivityDestroyed(activity: Activity) {

        }
    }

    private fun resetWindowInfo(activity: Activity) {
        windowSizeType = computeWindowSizeType(activity)

        windowSizeInfo.sizeType = windowSizeType

        computeWindowSpace()

//        if (Build.VERSION.SDK_INT != Build.VERSION_CODES.O ) {
//            // 横竖屏限制
//            activity.requestedOrientation =
//                if (isPortraitPhoneWindow())
//                    ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
//                else
//                    ActivityInfo.SCREEN_ORIENTATION_UNSPECIFIED
//        }

    }

    fun init(application: Application) {
        application.unregisterActivityLifecycleCallbacks(activityLifecycleCallbacks)
        application.registerActivityLifecycleCallbacks(activityLifecycleCallbacks)
    }


    /**
     * 是否是普通直屏手机窗口
     */
    private fun isPortraitPhoneWindow(): Boolean {
        var heightSizeType = when {
            windowSizeHeight < 600f -> WindowSizeType.COMPACT
            windowSizeHeight < 840f -> WindowSizeType.MEDIUM
            else -> WindowSizeType.EXPANDED
        }
        if (windowSizeType == WindowSizeType.COMPACT || heightSizeType == WindowSizeType.COMPACT) {
            return true
        }
        return false
    }

    /**
     * 计算屏幕显示宽度
     * 单位dp
     */
    private fun computeWindowSizeWidth(activity: Activity): Float {
        val metrics = WindowMetricsCalculator.getOrCreate()
            .computeCurrentWindowMetrics(activity)
            return metrics.bounds.width() /
                    activity.resources.displayMetrics.density
    }

    /**
     * 计算屏幕显示高度
     * 单位dp
     */
    private fun computeWindowSizeHeight(activity: Activity): Float {
        val metrics = WindowMetricsCalculator.getOrCreate()
            .computeCurrentWindowMetrics(activity)
            return metrics.bounds.height() /
                    activity.resources.displayMetrics.density
    }

    /**
     * 计算当前屏幕处于哪种尺寸
     */
    fun computeWindowSizeType(activity: Activity): WindowSizeType {
        windowSizeWidth = computeWindowSizeWidth(activity)
        windowSizeHeight = computeWindowSizeHeight(activity)
        return when {
            windowSizeWidth < 600f -> WindowSizeType.COMPACT
            windowSizeWidth < 840f -> WindowSizeType.MEDIUM
            else -> WindowSizeType.EXPANDED
        }
    }

    /**
     * 旋转屏幕，页面不重新创建时，重新计算间距
     */
    fun onConfigChanged(activity: Activity, orientation : Configuration){
        this.orientation = orientation.orientation
        resetWindowInfo(activity)
    }

    /**
     * 计算预留间距
     */
    private fun computeWindowSpace() {
         when (windowSizeType) {
            WindowSizeType.COMPACT -> {
                windowSizeInfo.columnWidth = ((windowSizeWidth.dp2px - windowSizeInfo.defaultSpace * 5) / 4)//每一个栅格的宽度
                windowSizeInfo.pageSpace = 0.dp2px
                windowSizeInfo.dialogSpace =0.dp2px

            }
            WindowSizeType.MEDIUM ->{

                windowSizeInfo.columnWidth = ((windowSizeWidth.dp2px - windowSizeInfo.defaultSpace * 9) / 8)//每一个栅格的宽度
                windowSizeInfo.pageSpace   =   0.dp.toFloat()//每侧边距 = 两个栅格 + 三个栅格间距 + 基础适配间距
                windowSizeInfo.dialogSpace =   windowSizeInfo.columnWidth * 1.5f  +   windowSizeInfo.defaultSpace * 2
            }

            else -> {
                windowSizeInfo.columnWidth = ((windowSizeWidth.dp2px - windowSizeInfo.defaultSpace * 15) / 12)//每一个栅格的宽度
                 windowSizeInfo.pageSpace   =   windowSizeInfo.columnWidth * 2     +   windowSizeInfo.defaultSpace * 3 - windowSizeInfo.defaultSpace//每侧边距 = 两个栅格 + 三个栅格间距 + 基础适配间距
                 windowSizeInfo.dialogSpace =   windowSizeInfo.columnWidth * 3.5f  +   windowSizeInfo.defaultSpace * 4
            }

        }
    }

    fun getWindowButtonWidth(): Int{
        return when (windowSizeType) {
            WindowSizeType.MEDIUM ->{
                (windowSizeInfo.columnWidth*6).toInt()+windowSizeInfo.defaultSpace.toInt() * 5
            }
            else -> {
                (windowSizeInfo.columnWidth*8).toInt()+windowSizeInfo.defaultSpace.toInt() * 7
            }
        }
    }
}