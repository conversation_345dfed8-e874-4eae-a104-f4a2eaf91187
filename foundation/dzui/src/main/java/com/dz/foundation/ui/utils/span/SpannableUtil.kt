package com.dz.foundation.ui.utils.span

import android.content.Context
import android.text.SpannableString
import android.text.Spanned
import android.text.TextPaint
import android.text.style.AbsoluteSizeSpan
import android.text.style.ClickableSpan
import android.text.style.ForegroundColorSpan
import android.text.style.LineHeightSpan
import android.text.style.StyleSpan
import android.view.View
import androidx.core.content.ContextCompat
import com.dz.foundation.base.utils.dp

/**
 * @Author: guyh
 * @Date: 2023/1/4 19:12
 * @Description:
 * @Version:1.0
 */
object SpannableUtil {
    const val diffTime = 500
    var lastClickTime = 0L

    fun setSpan(
        context: Context,
        spannableString: SpannableString,
        color: Int? = 0,
        underlineText: Boolean? = true,
        clickableStr: String,
        listener: OnSpanClickListener? = null,
        absoluteSizeSpan: Int? = 0,
        boldSpan: StyleSpan? = null,
        lineHeightSpan: Int? = null
    ): SpannableString {
        val startIndex = spannableString.toString().indexOf(clickableStr)
        if (startIndex == -1) {
            return spannableString
        }
        val endIndex = startIndex + clickableStr.length
        if (listener != null) {
            spannableString.setSpan(
                TextClickableSpan(clickableStr, underlineText == true, listener),
                startIndex,
                endIndex,
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )
        }
        color?.run {
            if (color != 0) {
                spannableString.setSpan(
                    ForegroundColorSpan(ContextCompat.getColor(context, color)),
                    startIndex,
                    endIndex,
                    Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                )
            }
        }
        absoluteSizeSpan?.run {
            if (absoluteSizeSpan != 0) {
                spannableString.setSpan(
                    AbsoluteSizeSpan(absoluteSizeSpan, true),
                    startIndex,
                    endIndex,
                    Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                )
            }
        }

        boldSpan?.run {
            spannableString.setSpan(
                boldSpan,
                startIndex,
                endIndex,
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )
        }

        return spannableString
    }

    fun setSpan(
        context: Context,
        content: String,
        color: Int? = 0,
        underlineText: Boolean? = true,
        clickableStr: String,
        listener: OnSpanClickListener? = null,
        absoluteSizeSpan: Int? = 0,
        boldSpan: StyleSpan? = null,
        lineHeightSpan: Int? =null,
    ): SpannableString {
        val spannableString = SpannableString(content)
        val startIndex = content.indexOf(clickableStr)
        if (startIndex == -1) {
            return spannableString
        }
        val endIndex = startIndex + clickableStr.length
        if (listener != null) {
            spannableString.setSpan(
                TextClickableSpan(clickableStr, underlineText == true, listener),
                startIndex,
                endIndex,
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )
        }
        color?.run {
            if (color != 0) {
                spannableString.setSpan(
                    ForegroundColorSpan(ContextCompat.getColor(context, color)),
                    startIndex,
                    endIndex,
                    Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                )
            }
        }

        absoluteSizeSpan?.run {
            if (absoluteSizeSpan != 0) {
                spannableString.setSpan(
                    AbsoluteSizeSpan(absoluteSizeSpan, true),
                    startIndex,
                    endIndex,
                    Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                )
            }
        }

        boldSpan?.run {
            spannableString.setSpan(
                boldSpan,
                startIndex,
                endIndex,
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )
        }

        return spannableString
    }

    class TextClickableSpan(
        private val content: String,
        private val underlineText: Boolean,
        private val listener: OnSpanClickListener
    ) :
        ClickableSpan() {
        override fun updateDrawState(ds: TextPaint) { //设置显示样式
            ds.isUnderlineText = underlineText //要默认下划线
        }

        override fun onClick(widget: View) { //点击事件的响应方法
            var currentTime = System.currentTimeMillis()
            var diffValue = currentTime - lastClickTime
            if (diffValue > diffTime) {
                lastClickTime = currentTime
                listener.onClick(widget, content)
            }
        }
    }
}