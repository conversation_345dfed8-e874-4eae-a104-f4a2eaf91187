package com.dz.foundation.ui.view.recycler;

import java.io.Serializable;

/**
 * Created by stone on 2017/3/13.
 */

public class LoadMoreModel implements Serializable {
    public static final int hidden = -1;//隐藏
    public static final int loading = 0;//加载中
    public static final int loadAll = 1;//已加载所有数据

    private int loadState;
    private String loadAllAlert;

    public int getLoadState() {
        return loadState;
    }

    public void setLoadState(int loadState) {
        this.loadState = loadState;
    }

    public String getLoadAllAlert() {
        return loadAllAlert;
    }

    public void setLoadAllAlert(String loadAllAlert) {
        this.loadAllAlert = loadAllAlert;
    }
}
