package com.dz.foundation.ui.widget


import android.annotation.SuppressLint
import android.app.Activity
import android.content.ContextWrapper
import android.view.MotionEvent
import android.view.View


fun View.getContainerActivity(): Activity? {
    var context = this.context
    while (context is ContextWrapper) {
        if (context is Activity) {
            return context
        }
        context = context.baseContext
    }
    return null
}

