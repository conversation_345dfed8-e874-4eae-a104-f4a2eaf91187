package com.dz.foundation.ui.view.recycler;

import android.content.Context;
import android.util.AttributeSet;
import android.view.View;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

import com.dz.foundation.ui.R;
import com.dz.foundation.ui.utils.DPUtils;
import com.dz.foundation.ui.widget.DzFrameLayout;


public class LoadMoreView extends DzFrameLayout implements DzRecyclerViewItem<LoadMoreModel> {
    private ProgressBar progressBar;
    private TextView tv_alert;

    public LoadMoreView(@NonNull Context context) {
        this(context, null);
    }

    public LoadMoreView(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public LoadMoreView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs, defStyleAttr);
    }


    protected void init(Context context, AttributeSet attrs, int defStyleAtt) {
        initAttrs(attrs);
        loadView();
        initData();
        initView();
    }


    protected void initAttrs(AttributeSet attrs) {

    }

    protected void loadView() {
        inflate(this.getContext(), R.layout.dzui_load_more_view, this);
    }

    protected void initData() {

    }

    protected void initView() {
        progressBar = findViewById(R.id.progress_bar);
        tv_alert = findViewById(R.id.tv_alert);
    }

    private void bindData(LoadMoreModel model) {

        switch (model.getLoadState()) {
            case LoadMoreModel.hidden:
                progressBar.setVisibility(View.GONE);
                tv_alert.setVisibility(View.GONE);
                break;
            case LoadMoreModel.loading:
                progressBar.setVisibility(View.VISIBLE);
                tv_alert.setVisibility(View.GONE);
                break;
            case LoadMoreModel.loadAll:
                progressBar.setVisibility(View.GONE);
                tv_alert.setVisibility(View.VISIBLE);
                tv_alert.setText(model.getLoadAllAlert());
                break;

        }
    }

    @Override
    public RecyclerView.LayoutParams onCreateRecyclerViewItem(DzRecyclerView parent, View itemView) {
        int height = DPUtils.dip2px(parent.getContext(), 40);
        RecyclerView.LayoutParams layoutParams = new RecyclerView.LayoutParams(LayoutParams.MATCH_PARENT, height);
        return layoutParams;
    }

    @Override
    public void onBindRecyclerViewItem(LoadMoreModel model, int position) {
        bindData(model);
    }
}
