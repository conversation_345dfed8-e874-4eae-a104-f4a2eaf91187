<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="59dp"
    android:gravity="center"
    android:orientation="vertical">

    <!-- 图标+文字模式的布局 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/normal_tab"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/imageView"
            android:layout_width="22dp"
            android:layout_height="22dp"
            android:scaleType="fitCenter"
            app:layout_constraintBottom_toTopOf="@+id/textView"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed" />

        <TextView
            android:id="@+id/textView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:layout_marginBottom="3dp"
            android:gravity="center"
            android:includeFontPadding="false"
            android:textSize="10dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/imageView"
            tools:ignore="SpUsage"
            tools:text="剧场" />


        <TextView
            android:id="@+id/textView_dot"
            android:layout_width="10dp"
            android:layout_height="10dp"
            android:layout_marginStart="17dp"
            android:layout_marginBottom="17dp"
            android:background="@drawable/dzui_shape_dot_white_ring"
            android:gravity="center"
            android:scaleType="centerInside"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@+id/imageView"
            app:layout_constraintStart_toStartOf="@+id/imageView" />

        <com.dz.foundation.ui.widget.DzTextView
            android:id="@+id/textView_dot_1"
            includeFontPadding="false"
            android:layout_width="wrap_content"
            android:layout_height="14dp"
            android:layout_marginStart="14.5dp"
            android:layout_marginBottom="13dp"
            android:background="@drawable/dzui_shape_dot1"
            android:gravity="center"
            android:includeFontPadding="false"
            android:paddingStart="3dp"
            android:paddingEnd="3dp"
            android:textColor="#FFFFFFFF"
            android:textSize="9dp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@+id/imageView"
            app:layout_constraintStart_toStartOf="@+id/imageView"
            tools:text="文案" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- 纯文字模式的布局 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/text_only_tab"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/textView_only"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="16dp"
            android:lineHeight="22dp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="剧场" />

        <TextView
            android:id="@+id/textView_dot_only"
            android:layout_width="10dp"
            android:layout_height="10dp"
            android:layout_marginStart="-5dp"
            android:layout_marginBottom="-6dp"
            android:visibility="gone"
            android:background="@drawable/dzui_shape_dot_white_ring"
            app:layout_constraintStart_toEndOf="@+id/textView_only"
            app:layout_constraintBottom_toTopOf="@+id/textView_only" />

        <com.dz.foundation.ui.widget.DzTextView
            android:id="@+id/textView_dot_1_only"
            includeFontPadding="false"
            android:layout_width="wrap_content"
            android:layout_height="14dp"
            android:background="@drawable/dzui_shape_dot1"
            android:gravity="center"
            android:includeFontPadding="false"
            android:paddingStart="3dp"
            android:paddingEnd="3dp"
            android:visibility="gone"
            android:layout_marginStart="-5dp"
            android:layout_marginBottom="-5dp"
            android:textColor="#FFFFFFFF"
            android:textSize="9dp"
            app:layout_constraintStart_toEndOf="@+id/textView_only"
            app:layout_constraintBottom_toTopOf="@+id/textView_only"
            tools:text="文案"  />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- 纯图片 -->
    <ImageView
        android:id="@+id/iv_welfare_bottom"
        android:layout_width="55dp"
        android:layout_height="55dp"
        android:layout_marginBottom="5dp"
        android:src="@drawable/dzui_ic_welfare_bottom"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <!-- 纯动画 -->
    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/lv_welfare_bottom"
        android:layout_width="55dp"
        android:layout_height="55dp"
        android:layout_marginBottom="5dp"
        android:layout_gravity="center"
        app:lottie_autoPlay="false"
        app:lottie_fallbackRes="@drawable/dzui_iv_refresh_header_done"
        app:lottie_fileName="welfare_bottom_tab.json"
        app:lottie_loop="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>