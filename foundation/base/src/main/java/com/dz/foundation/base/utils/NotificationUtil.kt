package com.dz.foundation.base.utils

import android.annotation.SuppressLint
import android.app.AppOpsManager
import android.app.NotificationManager
import android.content.Context
import android.content.Intent
import android.os.Build
import android.provider.Settings
import com.dz.foundation.base.data.notification.ChannelInfo

/**
 * @Author: guyh
 * @Date: 2022/10/24 10:25
 * @Description:通知权限获取工具类
 * @Version:1.0
 */
object NotificationUtil {
    private const val CHECK_OP_NO_THROW = "checkOpNoThrow"
    private const val OP_POST_NOTIFICATION = "OP_POST_NOTIFICATION"

    //调用该方法获取是否开启通知权限
    fun isNotifyEnabled(context: Context): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            isEnableV26(context)
        } else {
            isEnabledV19(context)
        }
    }

    /**
     * 8.0以下判断
     *
     * @param context api19  4.4及以上判断
     * @return
     */
    private fun isEnabledV19(context: Context): Boolean {
        val mAppOps = context.getSystemService(Context.APP_OPS_SERVICE) as AppOpsManager
        val appInfo = context.applicationInfo
        val pkg = context.applicationContext.packageName
        val uid = appInfo.uid
        try {
            val appOpsClass = Class.forName(AppOpsManager::class.java.name)
            val checkOpNoThrowMethod = appOpsClass.getMethod(
                CHECK_OP_NO_THROW,
                Integer.TYPE, Integer.TYPE, String::class.java
            )
            val opPostNotificationValue = appOpsClass.getDeclaredField(OP_POST_NOTIFICATION)
            val value = opPostNotificationValue[Int::class.java] as Int
            return checkOpNoThrowMethod.invoke(mAppOps, value, uid, pkg) as Int ==
                    AppOpsManager.MODE_ALLOWED
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return false
    }

    /**
     * 8.0及以上通知权限判断
     *
     * @param context
     * @return
     */
    @SuppressLint("NewApi")
    private fun isEnableV26(context: Context): Boolean {
        var result = false
        return try {
            val notificationManager =
                context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            result = notificationManager.areNotificationsEnabled()
            LogUtil.d("NotificationUtil", "isEnableV26 = $result")
            result
        } catch (e: Exception) {
            true
        }
    }

    fun openPushSetting(context: Context) {
        val intent = Intent()
        val packageName: String = context.packageName
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                intent.action = Settings.ACTION_APP_NOTIFICATION_SETTINGS
                intent.putExtra(Settings.EXTRA_APP_PACKAGE, packageName)
            } else
                intent.action = "android.settings.APP_NOTIFICATION_SETTINGS"
            intent.putExtra("app_package", packageName)
            intent.putExtra("app_uid", context.applicationInfo.uid)
            context.startActivity(intent)
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }
    }

    fun getAllChannel(context: Context): List<ChannelInfo> {
        LogUtil.d("PUSH", "获取Notification Channel")
        val notificationManager =
            context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            runCatching {
                notificationManager.notificationChannels.map { channel ->
                    ChannelInfo(
                        channel.name?.toString() ?: "",
                        channel.id ?: "",
                        channel.description ?: "",
                        if (channel.importance != NotificationManager.IMPORTANCE_NONE) 1 else 0
                    ).also {
                        LogUtil.d("PUSH", "ChannelInfo: $it")
                    }
                }
            }.getOrElse { exception ->
                LogUtil.d("PUSH", "获取channel失败：${exception.message}")
                emptyList()
            }
        } else {
            LogUtil.d("PUSH", "8.0以下设备，无channel")
            emptyList()
        }
    }
}