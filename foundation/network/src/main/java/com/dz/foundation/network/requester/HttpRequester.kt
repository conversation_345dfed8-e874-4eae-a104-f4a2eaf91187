package com.dz.foundation.network.requester

abstract class HttpRequester {

    var callback: HttpCallback? = null
    var sync: Boolean = false
    protected fun onFail(e: Exception) {
        callback?.onFail(e)
    }

    protected fun onSuccess(response: String?) {
        callback?.onSuccess(response)
    }

    abstract fun doGet(url: String, header: MutableMap<String, String>, tag: String)
    abstract fun doPost(url: String, header: MutableMap<String, String>, body: String, tag: String)
    abstract fun doUpload(
        url: String,
        header: MutableMap<String, String>,
        pathList: ArrayList<String>,
        tag: String
    )

    abstract fun cancel()
}