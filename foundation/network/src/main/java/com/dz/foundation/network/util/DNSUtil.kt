package com.dz.foundation.network.util

import com.dz.foundation.network.requester.HttpCallback
import com.dz.foundation.network.requester.okhttp.OkHttpRequester
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch

/**
 *@Author: zhanggy
 *@Date: 2023-10-10
 *@Description:
 *@Version:1.0
 */
object DNSUtil {

    var DNS_TEST_URL = ""

    fun checkDnsHijack(checkCallback: HttpCallback) {
        if (DNS_TEST_URL.isEmpty()) {
            checkCallback.onFail(Throwable("DNS测试地址为空"))
            return
        }
        MainScope().launch(Dispatchers.IO) {
            OkHttpRequester().apply {
                callback = checkCallback
            }
                .doGet(DNS_TEST_URL, mutableMapOf(), "dns_test")
        }
    }
}