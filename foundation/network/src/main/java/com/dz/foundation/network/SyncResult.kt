package com.dz.foundation.network

import com.dz.foundation.network.requester.RequestException

/**
 *@Author: shidz
 *@Date: 2022/10/20 2:21
 *@Description:同步返回结果
 *@Version:1.0
 */
class SyncResult<T> {
    private var resultData: T? = null
    private var exception: RequestException? = null
    fun setData(resultData: T?) {
        this.resultData = resultData
    }

    fun getData(): T? {
        return resultData
    }

    fun setHttpException(e: RequestException?) {
        exception = e
    }

    fun getHttpException(): RequestException? {
        return exception
    }

    fun success(): Boolean {
        return resultData != null
    }
}