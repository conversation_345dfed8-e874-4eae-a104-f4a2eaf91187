package com.dz.foundation.network.observer

import com.dz.foundation.network.requester.RequestException

class RequestObserver<T> {
    private var onStartBlock: (() -> Unit)? = null
    private var onResponseBlock: ((mode: T) -> Unit)? = null
    private var onEndBlock: (() -> Unit)? = null
    private var onErrorBlock: ((e: RequestException) -> Unit)? = null


    private var hasStart = false
    private var response: T? = null
    private var hasResponse = false

    private var e: RequestException? = null
    private var hasError = false

    private var hasEnd = false

    fun onStart(block: () -> Unit) {
        onStartBlock = block
    }

    fun onResponse(block: (mode: T) -> Unit) {
        onResponseBlock = block
    }

    fun onError(block: (e: RequestException) -> Unit) {
        onErrorBlock = block
    }

    fun onEnd(block: () -> Unit) {
        onEndBlock = block
    }


    fun doStart() {
        checkToStart()
    }

    fun doResponse(model: T) {
        checkToResponse(model)
    }

    fun doError(e: RequestException) {
        checkToError(e)
    }

    fun doEnd() {
        checkToEnd()
    }


    private fun checkToStart() {
        onStartBlock?.let {
            it()
        }
    }

    private fun checkToResponse(model: T) {
        onResponseBlock?.let {
            it(model)
        }
    }

    private fun checkToError(e: RequestException) {
        onErrorBlock?.let {
            it(e)
        }
    }

    private fun checkToEnd() {
        onEndBlock?.let {
            it()
        }
    }

}