package com.dz.foundation.network.requester

import android.net.ParseException
import android.text.TextUtils
import com.dz.foundation.network.DataRequest
import org.json.JSONException
import java.io.IOException
import java.net.ConnectException
import java.net.SocketTimeoutException
import java.net.UnknownHostException

/**
 * http 请求响应异常
 * Created by stone on 2019/8/13 17:34.
 */
class RequestException(
    e: Throwable?, //本次异常对应的请求
    val dataRequest: DataRequest<*>
) : Exception(e) {
    var errorCode
            : Int? = null

    val isHttpCodeException: Boolean
        get() = cause is HttpCodeException//域名解析失败//"网络连接超时";
    val isResponseCodeException: Boolean
        get() = cause is ResponseCodeException//约定的通用数据模型中的响应码 0为成功其它为异常

    //"连接失败,请检查网路设置";
    val isNetWorkError: Boolean
        get() {
            val e = cause!!
            return if (e is ConnectException) {
                //"连接失败,请检查网路设置";
                true
            } else if (e is SocketTimeoutException) {
                //"网络连接超时";
                true
            } else {
                //域名解析失败
                e is UnknownHostException
            }
        }//其它错误//  errorMsg = e.getMessage();//连接超时//均视为网络错误//均视为解析错误

    //  ApiException ex;
    override val message: String
        get() {
            var errorMsg: String? = null
            val e = cause!!
            //  ApiException ex;
            if (e is JSONException
                || e is ParseException
            ) {
                errorMsg = e.message + "网络异常，请稍后重试" //均视为解析错误
            } else if (e is ConnectException) {
                errorMsg = "网络异常，请稍后重试" //均视为网络错误
            } else if (e is SocketTimeoutException) {
                errorMsg = "网络异常，请稍后重试" //连接超时
            } else if (e is UnknownHostException) {
                errorMsg = "网络异常，请稍后重试"
            } else if (e is IOException) {
                errorMsg = "网络异常，请稍后重试"
            } else if (e is HttpCodeException) {

                //  errorMsg = e.getMessage();
                errorMsg = "网络异常，请稍后重试"
            } else {
                //其它错误
                errorMsg = e.message
                if (TextUtils.isEmpty(errorMsg)) {
                    errorMsg = "网络异常，请稍后重试"
                }
            }
            return errorMsg!!
        }

    companion object {
        //对应HTTP的状态码
        private const val UNAUTHORIZED = 401
        private const val FORBIDDEN = 403
        private const val NOT_FOUND = 404
        private const val REQUEST_TIMEOUT = 408
        private const val INTERNAL_SERVER_ERROR = 500
        private const val BAD_GATEWAY = 502
        private const val SERVICE_UNAVAILABLE = 503
        private const val GATEWAY_TIMEOUT = 504
    }

    init {
        if (e is HttpCodeException) {
            errorCode = e.code
        } else if (e is ResponseCodeException) {
            errorCode = e.code
        }
    }
}