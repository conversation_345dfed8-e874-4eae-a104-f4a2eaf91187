package com.dz.foundation.network

import android.text.TextUtils
import com.dz.foundation.base.utils.LogUtil
import com.dz.foundation.network.annotation.GET
import com.dz.foundation.network.annotation.POST
import com.dz.foundation.network.annotation.UPLOAD
import com.dz.foundation.network.annotation.URL
import com.dz.foundation.network.requester.HttpInterceptor
import com.dz.foundation.network.requester.RequestException
import java.lang.reflect.*
import java.util.concurrent.CopyOnWriteArraySet


object DzNetWorkManager {

    fun <T : NetworkApi> getApi(api: Class<T>): T {
        var invocationHandler = InvocationHandler { _, method, _ ->
            var url = ""
            val annotationUrl: URL? = findUrlAnnotation(method)
            annotationUrl?.let {
                url = it.value
            }

            var dataRequest: DataRequest<*>?
            val returnType = method.returnType

            dataRequest = returnType.newInstance() as DataRequest<*>

            var annotationUpload: UPLOAD? = findUploadAnnotation(method)
            annotationUpload?.let {
                dataRequest.setMethod(METHOD_UPLOAD)
                url = it.value
            }
            dataRequest.setUrl(url)
            var annotationGet: GET? = findGetAnnotation(method)
            annotationGet?.let {
                dataRequest.setMethod(METHOD_GET)
                dataRequest.setPath(it.value)
            }

            var annotationPost: POST? = findPostAnnotation(method)
            annotationPost?.let {
                dataRequest.setMethod(METHOD_POST)
                dataRequest.setPath(it.value)
            }

            val gReturnType: Type = method.genericReturnType
            if (gReturnType is ParameterizedType) {
                if (gReturnType.actualTypeArguments.isNotEmpty()) {
                    val responseType = gReturnType.actualTypeArguments[0]
                    dataRequest.setResponseType(responseType)
                }
            }

            dataRequest
        }

        val proxy = Proxy.newProxyInstance(
            DzNetWorkManager::class.java.classLoader,
            arrayOf(api),
            invocationHandler
        )
        return proxy as T
    }

    private fun findUrlAnnotation(method: Method): URL? {
        var annotation: URL? = null
        try {
            annotation = method.getAnnotation(URL::class.java)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return annotation
    }

    private fun findGetAnnotation(method: Method): GET? {
        var annotation: GET? = null
        try {
            annotation = method.getAnnotation(GET::class.java)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return annotation
    }

    private fun findPostAnnotation(method: Method): POST? {
        var annotation: POST? = null
        try {
            annotation = method.getAnnotation(POST::class.java)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return annotation
    }

    private fun findUploadAnnotation(method: Method): UPLOAD? {
        var annotation: UPLOAD? = null
        try {
            annotation = method.getAnnotation(UPLOAD::class.java)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return annotation
    }

    private val requestSet = CopyOnWriteArraySet<DataRequest<*>>()

    fun addRequest(request: DataRequest<*>) {
        requestSet.add(request)
        LogUtil.d("DzNetWorkManager", "addRequest:$request  size=${requestSet.size}")
    }

    fun removeRequest(request: DataRequest<*>) {
        requestSet.remove(request)
        LogUtil.d("DzNetWorkManager", "removeRequest:$request  size=${requestSet.size}")
    }

    fun cancel(tag: String) {
        requestSet.filter { TextUtils.equals(it.mTag, tag) }.forEach {
            it.cancel()
        }
    }

    fun cancelAll() {
        requestSet.forEach {
            it.cancel()
        }
        requestSet.clear()
    }

    private val interceptors = mutableListOf<HttpInterceptor>()
    fun addInterceptor(httpInterceptor: HttpInterceptor) {
        if (interceptors.contains(httpInterceptor)) {
            return
        }
        interceptors.add(httpInterceptor)
    }


    fun onInterceptResponse(request: DataRequest<*>, response: Any?): Boolean {
        if (!hasInterceptor()) {
            return false
        }
        val interceptCallback: HttpInterceptor.InterceptCallback =
            object : HttpInterceptor.InterceptCallback {
                override fun onContinue(request: DataRequest<*>) {
                    //重新请求
                    request.doRequest()
                    LogUtil.d(
                        "HttpInterceptor",
                        this.javaClass.simpleName + "onResponseSuccess:intercept onContinue"
                    )
                }

                override fun onInterrupt(request: DataRequest<*>, e: Throwable) {
                    request.onResponseError(e)
                    LogUtil.d(
                        "HttpInterceptor",
                        this.javaClass.simpleName + "onResponseSuccess:intercept onInterrupt"
                    )
                }

            }
        val size = interceptors.size
        for (i in 0 until size) {
            val httpInterceptor: HttpInterceptor = interceptors[i]
            val flag: Boolean = httpInterceptor.onResponse(request, response, interceptCallback)
            if (flag) {
                return true
            }
        }
        return false
    }

    fun onInterceptFail(e: RequestException) {
        if (!hasInterceptor()) {
            return
        }
//        if (e.isResponseCodeException || e.isHttpCodeException || e.isNetWorkError) {
        for (i in 0 until interceptors.size) {
            val httpInterceptor: HttpInterceptor = interceptors[i]
            httpInterceptor.onInterceptFail(e)
        }
//        }
    }

    private fun hasInterceptor(): Boolean {

        return interceptors.size > 0
    }
}