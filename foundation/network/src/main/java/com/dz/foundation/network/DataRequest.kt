package com.dz.foundation.network

import com.dz.foundation.NetworkEngineMC
import com.dz.foundation.base.meta.MetaDataOwner
import com.dz.foundation.base.service.IBaseService
import com.dz.foundation.base.utils.LogUtil
import com.dz.foundation.network.requester.HttpCallback
import com.dz.foundation.network.requester.HttpCodeException
import com.dz.foundation.network.requester.HttpRequester
import com.dz.foundation.network.requester.RequestException
import com.dz.foundation.network.requester.okhttp.OkHttpClientFactory
import com.dz.foundation.network.requester.okhttp.OkHttpRequester
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.Request
import okhttp3.RequestBody
import okhttp3.RequestBody.Companion.toRequestBody
import java.io.IOException
import java.lang.reflect.Type
import java.net.URLEncoder
import java.util.UUID

/**
 *@Author: shidz
 *@Date: 2022/9/13 17:10
 *@Description: 数据请求封装
 *@Version:1.0
 */
const val METHOD_GET: Int = 0
const val METHOD_POST: Int = 1
const val METHOD_UPLOAD: Int = 2

abstract class DataRequest<T> : MetaDataOwner {
    private var mUrl: String? = null
    fun setUrl(url: String): DataRequest<T> {
        this.mUrl = url
        return this
    }

    private var requestMethod: Int = METHOD_POST
    fun setMethod(method: Int): DataRequest<T> {
        this.requestMethod = method
        return this
    }

    /**
     * 接口号
     */
    private var mPath: String? = null
    fun setPath(value: String?): DataRequest<T> {
        this.mPath = value
        return this
    }

    protected fun getPath(): String? {
        return mPath
    }

    private var mResponseType: Type? = null
    internal fun setResponseType(responseType: Type) {
        mResponseType = responseType
    }

    protected fun getResponseType(): Type? {
        return mResponseType
    }

    abstract fun getUrl(): String
    private fun getRequestUrl(): String {

        return if (!mUrl.isNullOrBlank()) mUrl!! else getUrl()
    }


    private fun buildUrl(url: String): String {
        var result = url
        try {
            val stringBuilder = StringBuilder()
            for (key in params.keys) {
                stringBuilder.append(
                    "$key=" + URLEncoder.encode(
                        params[key].toString(),
                        "UTF-8"
                    ) + "&"
                )
            }
            if (!result.contains("?")) {
                result += "?"
            }
            result += stringBuilder.toString()
        } catch (e: Exception) {
        }
        return result
    }

    override val params: MutableMap<String, Any?> = mutableMapOf()

    internal var mTag: String = ""

    private val httpRequester: HttpRequester = OkHttpRequester()

    protected open fun onResponseSuccess(model: T) {
//        todo @gavin 网络会且主线程造成耗时
        LogUtil.d("渲染耗时","onResponseSuccess  onEnd :onResponseSuccess 前 真实时间 时间：${System.currentTimeMillis()}")
        mainScope.launch(Dispatchers.Main) {
            onResponseAction?.let { block -> block(model) }
            LogUtil.d("渲染耗时","onResponseSuccess  onEnd :onResponseSuccess 后 真实时间 时间：${System.currentTimeMillis()}")
            doOnEnd()
        }
    }

    open fun onResponseError(e: Throwable) {
        mainScope.launch(Dispatchers.Main) {
            onErrorAction?.let { block -> block(RequestException(e, this@DataRequest)) }
            doOnEnd()
        }

    }

    private var mainScope = MainScope()
    private fun doOnStart() {
        mainScope.launch(Dispatchers.Main) {
            onStartAction?.let { block ->
                block()
            }
            DzNetWorkManager.addRequest(this@DataRequest)
        }

    }

    private var hasEnd = false
    private fun doOnEnd() {
        if (hasEnd) {
            return
        }
        hasEnd = true
        LogUtil.d("渲染耗时","onResponseSuccess  doOnEnd :onResponseSuccess 前 真实时间 时间：${System.currentTimeMillis()}")
        mainScope.launch(Dispatchers.Main) {
            onEndAction?.let { block -> block() }
            LogUtil.d("渲染耗时","onResponseSuccess  doOnEnd :onResponseSuccess 后 真实时间 时间：${System.currentTimeMillis()}")
            recycle()
        }

    }

    private var inEnd = false
    private fun doEnd() {
        if (inEnd) {
            return
        }
        inEnd = true
        onEndAction?.let { block -> block() }
        recycle()
    }

    /**
     * @deprecated see [doSyncRequest2]
     */
    open fun doRequest() {
        val callback = object : HttpCallback {
            override fun onSuccess(response: String?) {
                if (response.isNullOrBlank()) {
                    onFail(Exception("响应数据为空"))
                    return
                }
                try {

                    val model = parseResponse(response)
                    //是否有拦截
                    if (DzNetWorkManager.onInterceptResponse(this@DataRequest, model)) {
                        return
                    }
                    onResponseSuccess(model)
                } catch (e: Exception) {
                    onFail(e)
                }

            }

            override fun onFail(e: Throwable) {
                LogUtil.printStackTrace(e)
                DzNetWorkManager.onInterceptFail(RequestException(e, this@DataRequest))
                onResponseError(e)
            }
        }
        doNetRequest(callback)
    }

    /**
     * 异步请求
     *
     * @param callback
     */
    private fun doNetRequest(callback: HttpCallback) {
        httpRequester.cancel()
        mainScope.cancel()
        mainScope = MainScope()
        doOnStart()
        mainScope.launch(Dispatchers.IO) {
            runCatching {
                runOnIOTask?.invoke(this@DataRequest)
                val requestUrl = getRequestUrl()
                val header = getHeader()
                //唯一ID,用于网络请求统计
                header["X-Request-ID"] = UUID.randomUUID().toString()
                val bodyContent = buildPostContent()
                httpRequester.callback = callback
                httpRequester.sync = false
                if (requestMethod == METHOD_GET) {
                    httpRequester.doGet(buildUrl(requestUrl), header, mTag)
                } else if (requestMethod == METHOD_UPLOAD) {
                    httpRequester.doUpload(requestUrl, header, buildUploadContent(), mTag)
                } else {
                    httpRequester.doPost(requestUrl, header, bodyContent, mTag)
                }
            }.onFailure {
                callback.onFail(it)
            }
        }


    }

    /**
     * @deprecated see [doSyncRequest2]
     * 同步请求
     *
     * @return
     */
    fun doSyncRequest(): SyncResult<T?> {
        var result: SyncResult<T?> = SyncResult()
        val callback = object : HttpCallback {
            override fun onSuccess(response: String?) {
                if (response.isNullOrBlank()) {
                    onFail(Exception("响应数据为空"))
                    return
                }
                try {

                    val model = parseResponse(response)
                    //是否有拦截
                    if (DzNetWorkManager.onInterceptResponse(this@DataRequest, model)) {
                        return
                    }
                    result.setData(model)

                    // 记录接口请求成功
                    IBaseService
                        .get()
                        ?.recordNetworkRequest(getPath(),getUrl(), true)

                } catch (e: Throwable) {
                    onFail(e)
                }
            }

            override fun onFail(e: Throwable) {
                //  throw AppHttpException(e, this@DataRequest)
                DzNetWorkManager.onInterceptFail(RequestException(e, this@DataRequest))
                result.setHttpException(RequestException(e, this@DataRequest))

                // 记录接口请求成功
                IBaseService
                    .get()
                    ?.recordNetworkRequest(getPath(),getUrl(), false)
            }
        }
        runCatching {
            httpRequester.cancel()
            doOnStart()
    //        runOnIOTask?.invoke(this@DataRequest)
            val requestUrl = getRequestUrl()
            val header = getHeader()
            //唯一ID,用于网络请求统计
            header["X-Request-ID"] = UUID.randomUUID().toString()
            val bodyContent = buildPostContent()
            httpRequester.callback = callback
            httpRequester.sync = true
            if (requestMethod == METHOD_GET) {
                httpRequester.doGet(buildUrl(requestUrl), header, mTag)
            } else if (requestMethod == METHOD_UPLOAD) {
                httpRequester.doUpload(requestUrl, header, buildUploadContent(), mTag)
            } else {
                httpRequester.doPost(requestUrl, header, bodyContent, mTag)
            }
        }.onFailure {
            callback.onFail(it)
        }
        return result
    }


    /**
     * 快速请求 不切换线程
     *
     * @return
     */
    fun doSyncRequest2(): SyncResult<T?> {
        val result: SyncResult<T?> = SyncResult()
        val callback = object : HttpCallback {
            override fun onSuccess(response: String?) {
                if (response.isNullOrBlank()) {
                    onFail(Exception("响应数据为空"))
                    return
                }
                try {
                    val model = parseResponse(response)
                    //是否有拦截
                    if (DzNetWorkManager.onInterceptResponse(this@DataRequest, model)) {
                        return
                    }
                    //接口请求成功统计
                    IBaseService
                        .get()
                        ?.recordNetworkRequest(getPath(),getUrl(), true)

                    result.setData(model)
                    onResponseAction?.let { block -> block(model) }
                    doEnd()
                } catch (e: Throwable) {
                    onFail(e)
                }
            }

            override fun onFail(e: Throwable) {
                //  throw AppHttpException(e, this@DataRequest)
                //接口请求失败统计
                IBaseService
                    .get()
                    ?.recordNetworkRequest(getPath(),getUrl(), false)

                DzNetWorkManager.onInterceptFail(RequestException(e, this@DataRequest))
                result.setHttpException(RequestException(e, this@DataRequest))
                onErrorAction?.let { block -> block(RequestException(e, this@DataRequest)) }
                doEnd()
            }
        }
//        TODO 为什么要 cancel？
        httpRequester.cancel()
        try {
            onStartAction?.let { block -> block() }
            DzNetWorkManager.addRequest(this@DataRequest)
            val requestUrl = getRequestUrl()
            val header = getHeader()
            //唯一ID,用于网络请求统计
            header["X-Request-ID"] = UUID.randomUUID().toString()
            val bodyContent = buildPostContent()
            val requestBody: RequestBody =
                bodyContent.toRequestBody("application/json; charset=utf-8".toMediaType())
            //OkHttp直接请求 不套用其他框架
            val builder = Request.Builder()
                .url(requestUrl)
                .post(requestBody)
            //暂时保留旧的header逻辑。不提出去
            addHeaders(builder, header)
            val request: Request = builder.build()
            NetworkEngineMC.requestUrl=request.url.toString()
            NetworkEngineMC.requestMethod=request.method
            OkHttpClientFactory.obtainOkHttpClient().newCall(request).execute().use { response ->
                if (response.isSuccessful) {
                    val responseStr = response.body?.string()
                    callback.onSuccess(responseStr)
                } else {
                    val code = response.code
                    val message = response.message
                    callback.onFail(HttpCodeException(code, message))
                }
            }
        } catch (e: Throwable) {
            callback.onFail(IOException(e))
        }

        return result
    }

    private fun addHeaders(builder: Request.Builder, header: MutableMap<String, String>) {
        header.let {
            header.forEach { entry ->
                if (entry.value.isNotEmpty()) {
                    builder.addHeader(entry.key, entry.value)
                }
            }
        }
    }

    protected abstract fun getHeader(): MutableMap<String, String>
    protected abstract fun buildPostContent(): String
    protected abstract fun buildUploadContent(): ArrayList<String>
    protected abstract fun parseResponse(response: String): T
    private fun recycle() {
        DzNetWorkManager.removeRequest(this)
        mainScope.cancel()
        LogUtil.d("渲染耗时","onResponseSuccess  recycle :onResponseSuccess 后 真实时间 时间：${System.currentTimeMillis()}")
    }

    fun cancel() {
        if (hasEnd) {
            return
        }
        httpRequester.cancel()
        doOnEnd()
    }


    internal var onStartAction: (() -> Unit)? = null
    internal var onResponseAction: ((response: T) -> Unit)? = null
    internal var onEndAction: (() -> Unit)? = null
    internal var onErrorAction: ((e: RequestException) -> Unit)? = null
    internal var runOnIOTask: (suspend (dataRequest: DataRequest<*>) -> Unit)? = null
}

fun <T : DataRequest<*>> T.setTag(tag: String): T {
    this.mTag = tag
    return this
}

fun <T : DataRequest<*>> T.onStart(block: () -> Unit): T {
    this.onStartAction = block
    return this
}

fun <T : DataRequest<*>> T.onEnd(block: () -> Unit): T {
    this.onEndAction = block
    return this
}

fun <M, T : DataRequest<M>> T.onResponse(block: (response: M) -> Unit): T {
    onResponseAction = block
    return this
}

fun <T : DataRequest<*>> T.onError(block: (e: RequestException) -> Unit): T {
    this.onErrorAction = block
    return this
}

fun <T : DataRequest<*>> T.setParamOnIO(block: suspend (dataRequest: DataRequest<*>) -> Unit): T {
    this.runOnIOTask = block
    return this
}


