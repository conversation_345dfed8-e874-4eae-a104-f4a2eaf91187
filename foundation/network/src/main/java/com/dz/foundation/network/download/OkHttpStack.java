package com.dz.foundation.network.download;

import com.dz.foundation.base.module.AppModule;
import com.dz.foundation.network.interceptor.RetryInterceptor;

import java.io.IOException;
import java.io.InputStream;
import java.util.concurrent.TimeUnit;

import okhttp3.Call;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.ResponseBody;


/**
 * Created by dance on 2017/3/26.
 */

public class OkHttpStack implements HttpStack {
    private OkHttpClient client;
    private InputStream is;
    private ResponseBody body;
    private long contentLength;

    public OkHttpStack(){
        client = AppModule.INSTANCE.getHttpClient();
    }

    /**
     *
     * @param retryCount 失败重试次数
     */
    public OkHttpStack(int retryCount) {
        client = AppModule.INSTANCE.getHttpClient().newBuilder()
                .connectTimeout(10L, TimeUnit.SECONDS)
                .writeTimeout(10L, TimeUnit.SECONDS)
                .readTimeout(10L, TimeUnit.SECONDS)
                .addInterceptor(new RetryInterceptor(retryCount))
                .build();
    }

    @Override
    public long getContentLength(){
        return contentLength;
    }
    @Override
    public InputStream download(String downloadUrl) {
        Request request = new Request.Builder()
                .get()
                .url(downloadUrl)
                .build();

        Call call = client.newCall(request);
        try {
            Response response = call.execute();
            if(response.isSuccessful()){
                body = response.body();
                contentLength =body.contentLength();
                is = body.byteStream();

            }
        } catch (Throwable e) {
            e.printStackTrace();
        }
        return is;
    }

    @Override
    public void close() {
        if(is!=null){
            try {
                body.close();
                is.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
}
