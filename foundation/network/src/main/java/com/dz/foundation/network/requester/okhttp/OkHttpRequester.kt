package com.dz.foundation.network.requester.okhttp


import com.dz.foundation.NetworkEngineMC
import com.dz.foundation.base.utils.LogUtil
import com.dz.foundation.network.requester.HttpCodeException
import com.dz.foundation.network.requester.HttpRequester
import okhttp3.Call
import okhttp3.Callback
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.MultipartBody
import okhttp3.Request
import okhttp3.RequestBody
import okhttp3.RequestBody.Companion.asRequestBody
import okhttp3.RequestBody.Companion.toRequestBody
import okhttp3.Response
import java.io.File
import java.io.IOException
import java.util.UUID


class OkHttpRequester : HttpRequester() {
    private var currentCall: Call? = null
    private val okCallback = object : Callback {
        override fun onFailure(call: Call, e: IOException) {
            onFail(e)
        }

        override fun onResponse(call: Call, response: Response) {
            if (response.isSuccessful) {
                try {
                    val responseStr = response.body?.string()
                    onSuccess(responseStr)
                } catch (e: Exception) {
                    onFail(e)
                }

            } else {
                val code = response.code
                val message = response.message
                onFail(HttpCodeException(code, message))
            }
        }
    }


    override fun doGet(url: String, header: MutableMap<String, String>, tag: String) {
        val builder = Request.Builder()
            .url(url)
            .tag(tag)
        addHeaders(builder, header)
        val request: Request = builder.build()
        doRequest(request)
    }

    override fun doPost(
        url: String,
        header: MutableMap<String, String>,
        body: String,
        tag: String
    ) {
        LogUtil.d("DzDataRequest", "tag:$tag")
        val requestBody: RequestBody = createRequestBody(body)
        val builder = Request.Builder()
            .url(url)
            .post(requestBody)
            .tag(tag)
        addHeaders(builder, header)
        val request: Request = builder.build()
        doRequest(request)

    }

    override fun doUpload(
        url: String,
        header: MutableMap<String, String>,
        pathList: ArrayList<String>,
        tag: String
    ) {
        var requestBody = MultipartBody.Builder()
            .setType(MultipartBody.ALTERNATIVE)
        for (path in pathList) {
            var file = File(path)
            val image: RequestBody = createRequestBody(file)
            requestBody.addFormDataPart(file.name, path, image)
        }
        val builder = Request.Builder()
            .url(url)
            .post(requestBody.build())
            .tag(tag)
        addHeaders(builder, header)
        val request: Request = builder.build()
        doRequest(request)
    }

    private fun doRequest(request: Request) {
        NetworkEngineMC.requestUrl=request.url.toString()
        NetworkEngineMC.requestMethod=request.method
        currentCall = OkHttpClientFactory.obtainOkHttpClient().newCall(request)
        currentCall?.let { call ->

            if (sync) {
                try {
                    val response = call.execute()
                    okCallback.onResponse(call, response)
                } catch (e: IOException) {
                    okCallback.onFailure(call, e)
                } catch (e: Throwable) {
                    okCallback.onFailure(call, IOException(e))
                }


            } else {
                try {
                    call.enqueue(okCallback)
                } catch (e: IOException) {
                    okCallback.onFailure(call, e)
                } catch (e: Throwable) {
                    okCallback.onFailure(call, IOException(e))
                }
            }
        }
    }

    private fun addHeaders(builder: Request.Builder, header: MutableMap<String, String>) {
        header.let {
            header.forEach { entry ->
                if (!entry.value.isNullOrEmpty()) {
                    builder.addHeader(entry.key, entry.value)
                }
            }
            //唯一ID,用于网络请求统计
            builder.addHeader("X-Request-ID", UUID.randomUUID().toString())
        }
    }

    private val jsonType = "application/json; charset=utf-8".toMediaType()
    private fun createRequestBody(bodyStr: String): RequestBody {
        return bodyStr.toRequestBody(jsonType)
    }

    private val imageType = "image/*".toMediaType()
    private fun createRequestBody(file: File): RequestBody {
        return file.asRequestBody(imageType)
    }

    override fun cancel() {
        currentCall?.cancel()
    }
}