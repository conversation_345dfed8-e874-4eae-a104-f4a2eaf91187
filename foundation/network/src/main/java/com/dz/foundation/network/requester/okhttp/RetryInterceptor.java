package com.dz.foundation.network.requester.okhttp;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.dz.support.internal.Clog;

import java.io.IOException;

import okhttp3.HttpUrl;
import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.Response;

/**
 * Des:
 * <p>
 * Time:2024/4/11
 * Author:Gavin
 */

public class RetryInterceptor implements Interceptor {
    //    累计报错次数
    public static int timeoutErrorCount = 0;
    //    累计无网报错次数
    public static int noNetErrorCount = 0;
    private final int maxRetry; // 最大重试次数
    private final long retryInterval; // 重试的间隔时间
    private final String primaryDomain = "freevideo.zqqds.cn";
    private final String alternateDomain = "freevideo.dzkjk.cn";
    public static String lastSucDomain = "freevideo.zqqds.cn";

    public @Nullable RetrySuccessCallback retrySuccessCallback;

    public RetryInterceptor(int maxRetry, long retryInterval) {
        this.maxRetry = maxRetry;
        this.retryInterval = retryInterval;
    }

    @NonNull
    @Override
    public Response intercept(Chain chain) throws IOException {
        Request request = chain.request();
        Response response = null;
        IOException exception = null;
        long requestTime = 0L;
        for (int i = 0; i < maxRetry; i++) {
            try {
                Clog.d("OKHTTPClient", "发起请求次数：" + i + ", " + request.url().url().getPath());
                requestTime = System.currentTimeMillis();
                response = chain.proceed(request);

                if (response.isSuccessful()) {
                    lastSucDomain = request.url().host();
                    if (i > 0) {
                        if (retrySuccessCallback != null) {
                            // 重试成功回调
                            retrySuccessCallback.onRetrySuccess(i, request.url().url().toString());
                        }
                        Clog.d("OKHTTPClient", "第" + i + "次重试成功" + request.url().url().getPath());
                    }
                    return response; // 如果请求成功，则直接返回结果
                }
            } catch (IOException e) {
                exception = e;
            } catch (Throwable e) {
                timeoutErrorCount++;
                throw new IOException(e);
            }
            // 请求被取消不进行重试
            if (exception != null && exception.getMessage() != null && exception.getMessage().contains("Canceled")) {
                Clog.d("OKHTTPClient", "请求被取消不进行重试");
                throw exception;
            }
            // 无网络连接不进行重试
            if (exception != null && exception.getMessage() != null && exception.getMessage().contains("No address associated with hostname")) {
                Clog.d("OKHTTPClient", "无网络连接不进行重试");
                noNetErrorCount++;
                throw exception;
            }


            if (response != null) {
                // 重新请求前需要关闭之前的响应
                try {
                    response.close(); // Close the previous response
                } catch (Throwable ignored) {

                }
            }

            if (exception != null && (!request.url().host().equals(primaryDomain) && !request.url().host().equals(alternateDomain))) {
                // 如果不是主域名报错，直接抛出异常
                throw exception;
            }

            request = createRequest(request);

            //部分报错需要立即发起重试，这里通过判断耗时来解决
            if ((System.currentTimeMillis() - requestTime) > 1000) {
                continue;
            }
            // 非连接问题，如无网，等待一段时间再重试 retryInterval ms 后发起重试
            try {
                // 切域名重试
                Thread.sleep(retryInterval);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                timeoutErrorCount++;
                throw new IOException(e);
            } catch (Throwable e) {
                timeoutErrorCount++;
                throw new IOException(e);
            }
        }

        // 如果重试了maxRetry次都没有成功，则抛出最后一次的异常
        if (exception != null) {
            timeoutErrorCount++;
            throw exception;
        } else {
            // 这里理论上不会执行到，因为前面已经抛出异常了
            throw new IOException("Unknown error after retrying " + maxRetry + " times");
        }

    }

    @NonNull
    private Request createRequest(Request request) {
        HttpUrl alternateUrl = request.url().newBuilder().host(alternateDomain.equals(request.url().host()) ? primaryDomain : alternateDomain)

                .build();
        request = request.newBuilder().url(alternateUrl).build();
        return request;
    }

    public interface RetrySuccessCallback {
        /**
         * 重试成功回调
         *
         * @param count 重试次数
         * @param url   请求的url
         */
        void onRetrySuccess(int count, String url);


    }

}


