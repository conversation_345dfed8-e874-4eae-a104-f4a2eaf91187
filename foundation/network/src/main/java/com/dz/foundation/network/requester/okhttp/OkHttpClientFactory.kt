package com.dz.foundation.network.requester.okhttp

import com.dz.foundation.NetworkEngineMC.Companion.CONNECT_TIMEOUT_SECONDS
import com.dz.foundation.NetworkEngineMC.Companion.READ_TIMEOUT_SECONDS
import com.dz.foundation.NetworkEngineMC.Companion.WRITE_TIMEOUT_SECONDS
import com.dz.foundation.base.utils.LogUtil
import com.dz.foundation.networkEngine.cache.DNSCacheRepository
import com.dz.foundation.networkEngine.dns.DnsResolver
import com.dz.foundation.networkEngine.monitor.NetworkEventListener
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import java.security.KeyManagementException
import java.security.NoSuchAlgorithmException
import java.util.concurrent.TimeUnit
import javax.net.ssl.SSLContext


/**
 * 业务接口使用的OkHttpClient
 * 业务接口指的是：freevideo.zqqds.cn 或 freevideo.dzkjk.cn 域名下的接口
 * 因 okHttpClient 是全局单例模式实现，且拦截器中当接口请求成功会缓存其域名，下次请求会直接使用缓存域名发起请求!! 因此此类只限用在业务接口中，其他域名的接口请勿使用，否则会导致请求失败
 * 因 okHttpClient 是全局单例模式实现，且拦截器中当接口请求成功会缓存其域名，下次请求会直接使用缓存域名发起请求!! 因此此类只限用在业务接口中，其他域名的接口请勿使用，否则会导致请求失败
 * 因 okHttpClient 是全局单例模式实现，且拦截器中当接口请求成功会缓存其域名，下次请求会直接使用缓存域名发起请求!! 因此此类只限用在业务接口中，其他域名的接口请勿使用，否则会导致请求失败
 */
object OkHttpClientFactory {
    val retryInterceptor = RetryInterceptor(3, 3000)

    private val okHttpClient: OkHttpClient by lazy {
        val builder = OkHttpClient.Builder()
        builder.connectTimeout(CONNECT_TIMEOUT_SECONDS, TimeUnit.SECONDS)
            .writeTimeout(WRITE_TIMEOUT_SECONDS, TimeUnit.SECONDS)
            .readTimeout(READ_TIMEOUT_SECONDS, TimeUnit.SECONDS)
            .retryOnConnectionFailure(true)
            .addInterceptor(retryInterceptor)
            .addInterceptor(logInterceptor())
            .eventListener(NetworkEventListener())
        if (DNSCacheRepository.canUseHttpDNS()) {
            builder.dns(DnsResolver.instance)
        }
        builder.build()
    }


    /**
     * 获取全局的 okHttpClient
     */
    fun obtainOkHttpClient(): OkHttpClient {
        return okHttpClient
    }

    private fun logInterceptor(): HttpLoggingInterceptor {
        val httpLoggingInterceptor = HttpLoggingInterceptor()
        if (LogUtil.isDebugMode()) {
            httpLoggingInterceptor.level = HttpLoggingInterceptor.Level.BODY
        } else {
            httpLoggingInterceptor.level = HttpLoggingInterceptor.Level.NONE
        }
        return httpLoggingInterceptor
    }

    /**
     * 设置支持https
     *
     * @return https的设置
     */
    private fun createSSLSocketFactory(): Tls12SocketFactory {
        var sslContext: SSLContext? = null
        try {
            sslContext = SSLContext.getInstance("TLS")
            try {
                sslContext!!.init(null, null, null)
            } catch (e: KeyManagementException) {
                e.printStackTrace()
            }

        } catch (e: NoSuchAlgorithmException) {
            e.printStackTrace()
        }
        return Tls12SocketFactory(sslContext!!.socketFactory)
    }
}