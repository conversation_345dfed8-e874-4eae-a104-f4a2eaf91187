package com.dz.foundation.network.requester

import com.dz.foundation.network.DataRequest

/**
 *@Author: shidz
 *@Date: 2022/11/25 18:13
 *@Description: http请求拦截
 *@Version:1.0
 */
interface HttpInterceptor {
    fun onResponse(
        dataRequest: DataRequest<*>,
        model: Any?,
        interceptCallback: InterceptCallback?,
    ): Boolean

    interface InterceptCallback {
        fun onContinue(request: DataRequest<*>)


        fun onInterrupt(request: DataRequest<*>, e: Throwable)
    }

    fun onInterceptFail(e: RequestException) {

    }
}