package com.dz.foundation.event;

import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.lifecycle.LifecycleOwner;

import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;
import java.lang.reflect.Proxy;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.Set;

/**
 * event 事件管理类
 */
public class DzEventManager {
    private static HashMap<Class<? extends IModuleEvent>, HashMap<String, EventLiveData>> groupEventMap = new HashMap<>();

    /**
     * @param
     * @return
     */
    public static <T extends IModuleEvent> T of(Class<T> eventClass) {
        ApiProxyInvocationHandler invocationHandler = new ApiProxyInvocationHandler(eventClass);
        T proxy = (T) Proxy.newProxyInstance(DzEventManager.class.getClassLoader(),
                new Class[]{eventClass}, invocationHandler);
        return proxy;
    }

    public static void removeObservers(@NonNull final LifecycleOwner owner) {
        try {
            if (groupEventMap == null || groupEventMap.size() == 0) {
                return;
            }

            Set<Map.Entry<Class<? extends IModuleEvent>, HashMap<String, EventLiveData>>> entries = groupEventMap.entrySet();

            Iterator<Map.Entry<Class<? extends IModuleEvent>, HashMap<String, EventLiveData>>> groupEntryIt = entries.iterator();

            while (groupEntryIt.hasNext()) {
                Map.Entry<Class<? extends IModuleEvent>, HashMap<String, EventLiveData>> groupEntry = groupEntryIt.next();
                HashMap<String, EventLiveData> liveDataMap = groupEntry.getValue();
                if (liveDataMap == null || liveDataMap.size() == 0) {
                    continue;
                }
                Set<Map.Entry<String, EventLiveData>> liveDataEntries = liveDataMap.entrySet();

                Iterator<Map.Entry<String, EventLiveData>> liveDataIterator = liveDataEntries.iterator();

                while (liveDataIterator.hasNext()) {
                    Map.Entry<String, EventLiveData> liveDataEntry = liveDataIterator.next();
                    EventLiveData liveData = liveDataEntry.getValue();
                    liveData.removeObservers(owner);
                    if (!liveData.hasObservers() && !liveData.isSticky()) {
                        liveDataIterator.remove();
                        if (liveDataMap.size() == 0) {
                            groupEntryIt.remove();
                        }
                    }
                }

            }

        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    public static void removeObservers(@NonNull String lifecycleTag) {
        try {
            if (TextUtils.isEmpty(lifecycleTag)) {
                return;
            }
            if (groupEventMap == null || groupEventMap.size() == 0) {
                return;
            }

            Set<Map.Entry<Class<? extends IModuleEvent>, HashMap<String, EventLiveData>>> entries = groupEventMap.entrySet();

            Iterator<Map.Entry<Class<? extends IModuleEvent>, HashMap<String, EventLiveData>>> groupEntryIt = entries.iterator();

            while (groupEntryIt.hasNext()) {
                Map.Entry<Class<? extends IModuleEvent>, HashMap<String, EventLiveData>> groupEntry = groupEntryIt.next();
                HashMap<String, EventLiveData> liveDataMap = groupEntry.getValue();
                if (liveDataMap == null || liveDataMap.size() == 0) {
                    continue;
                }
                Set<Map.Entry<String, EventLiveData>> liveDataEntries = liveDataMap.entrySet();

                Iterator<Map.Entry<String, EventLiveData>> liveDataIterator = liveDataEntries.iterator();

                while (liveDataIterator.hasNext()) {
                    Map.Entry<String, EventLiveData> liveDataEntry = liveDataIterator.next();
                    EventLiveData liveData = liveDataEntry.getValue();
                    liveData.removeObserver(lifecycleTag);
                    if (!liveData.hasObservers() && !liveData.isSticky()) {
                        liveDataIterator.remove();
                        if (liveDataMap.size() == 0) {
                            groupEntryIt.remove();
                        }
                    }
                }

            }

        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    private static class ApiProxyInvocationHandler implements InvocationHandler {
        private Class<? extends IModuleEvent> eventClass;

        public ApiProxyInvocationHandler(Class<? extends IModuleEvent> eventClass) {
            this.eventClass = eventClass;
        }

        @Override
        public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
            String methodName = method.getName();
            HashMap<String, EventLiveData> eventMap = groupEventMap.get(eventClass);
            if (eventMap == null) {
                eventMap = new HashMap<>();
                groupEventMap.put(eventClass, eventMap);
            }
            EventLiveData event = eventMap.get(methodName);
            if (event == null) {
                event = new EventLiveData();
                eventMap.put(methodName, event);
            }
            return event;
        }
    }
}
