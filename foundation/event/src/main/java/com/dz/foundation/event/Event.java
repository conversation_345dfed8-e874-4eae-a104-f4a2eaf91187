package com.dz.foundation.event;

import androidx.annotation.NonNull;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.Observer;

public interface Event<T> {
    void observe(@NonNull LifecycleOwner owner, @NonNull Observer<? super T> observer);

    void observeSticky(@NonNull LifecycleOwner owner, @NonNull Observer<T> observer);

    void observeForever(@NonNull Observer<? super T> observer);

    void observeForeverSticky(@NonNull Observer<T> observer);

    void observe(@NonNull LifecycleOwner owner, @NonNull String lifecycleTag, @NonNull Observer<? super T> observer);

    void observeSticky(@NonNull LifecycleOwner owner, @NonNull String lifecycleTag, @NonNull Observer<T> observer);

    void observeForever(@NonNull String lifecycleTag, @NonNull Observer<? super T> observer);

    void observeForeverSticky(@NonNull String lifecycleTag, @NonNull Observer<T> observer);

    void post(T value);

    void postDelay(T value, long delay);

    void postSticky(T value);
}
