package com.dz.foundation.event;

import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.arch.core.internal.SafeIterableMap;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.Observer;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Map;


public class EventLiveData<T> extends MutableLiveData<T> implements Event<T> {
    private static final Handler mainHandler = new Handler(Looper.getMainLooper());

    @Override
    public void observe(@NonNull LifecycleOwner owner, @NonNull Observer<? super T> observer) {

        super.observe(owner, new NotStickyObserverWrapper(observer));
    }

    @Override
    public void observeSticky(@NonNull LifecycleOwner owner, @NonNull Observer<T> observer) {
        super.observe(owner, new StickyObserverWrapper<T>(observer));
    }

    public void observeForever(@NonNull Observer<? super T> observer) {
        super.observeForever(new NotStickyObserverWrapper(observer));
    }

    @Override
    public void observeForeverSticky(@NonNull Observer<T> observer) {
        super.observeForever(new StickyObserverWrapper<T>(observer));
    }

    @Override
    public void observe(@NonNull LifecycleOwner owner, @NonNull String lifecycleTag, @NonNull Observer<? super T> observer) {

        super.observe(owner, new NotStickyObserverWrapper(observer, lifecycleTag));
    }

    @Override
    public void observeSticky(@NonNull LifecycleOwner owner, @NonNull String lifecycleTag, @NonNull Observer<T> observer) {
        super.observe(owner, new StickyObserverWrapper(observer, lifecycleTag));
    }

    @Override
    public void observeForever(@NonNull String lifecycleTag, @NonNull Observer<? super T> observer) {
        super.observeForever(new NotStickyObserverWrapper(observer, lifecycleTag));
    }

    @Override
    public void observeForeverSticky(@NonNull String lifecycleTag, @NonNull Observer<T> observer) {
        super.observeForever(new StickyObserverWrapper(observer, lifecycleTag));
    }

    @Override
    public void post(T value) {
        postValue(value);
    }

    private boolean stickyFlag = false;
    public boolean isSticky(){
        return stickyFlag;
    }

    public void postSticky(T value){
        stickyFlag = true;
        post(value);
    }

    @Override
    public void postDelay(T value, long delay) {
        mainHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                setValue(value);
            }
        }, delay);
    }


    @Override
    public void removeObserver(@NonNull Observer<? super T> observer) {
        super.removeObserver(observer);
    }

    private Field fieldVersion;

    public int getLiveDataVersion() {
        try {
            if (fieldVersion == null) {
                fieldVersion = LiveData.class.getDeclaredField("mVersion");
                fieldVersion.setAccessible(true);
            }
            return (int) fieldVersion.get(this);
        } catch (Exception e) {

        }
        return -1;
    }

    public void removeObserver(String lifecycleTag) {

        Field mObserversField = null;
        try {
            mObserversField = LiveData.class.getDeclaredField("mObservers");
            mObserversField.setAccessible(true);
            SafeIterableMap<Observer<? super T>, Object> mObservers = ((SafeIterableMap) mObserversField.get(this));
            for (Map.Entry entry : mObservers) {
                Object key = entry.getKey();
                if (key instanceof ObserverWrapper) {
                    String observerTag = ((ObserverWrapper<?>) key).getTag();
                    if (TextUtils.equals(observerTag, lifecycleTag)) {
                        removeObserver((Observer) key);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    public boolean hasObserverByTag(String lifecycleTag) {

        Field mObserversField = null;
        try {
            mObserversField = LiveData.class.getDeclaredField("mObservers");
            mObserversField.setAccessible(true);
            SafeIterableMap<Observer<? super T>, Object> mObservers = ((SafeIterableMap) mObserversField.get(this));
            for (Map.Entry entry : mObservers) {
                Object key = entry.getKey();
                if (key instanceof ObserverWrapper) {
                    String observerTag = ((ObserverWrapper<?>) key).getTag();
                    if (TextUtils.equals(observerTag, lifecycleTag)) {
                        return true;

                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    private abstract class ObserverWrapper<T> implements Observer<T> {
        @NonNull
        protected Observer<T> observer;
        protected String tag = "";

        public ObserverWrapper(@NonNull Observer<T> observer, String tag) {
            this.observer = observer;
            this.tag = tag;
        }

        public String getTag() {
            return tag;
        }

    }


    private class NotStickyObserverWrapper<T> extends ObserverWrapper<T> {
        //开始观察 liveData  liveData 当时的mVersion
        private int startVersion;


        public NotStickyObserverWrapper(@NonNull Observer<T> observer) {
            this(observer, "");
        }

        public NotStickyObserverWrapper(@NonNull Observer<T> observer, String tag) {
            super(observer, tag);
            this.startVersion = EventLiveData.this.getLiveDataVersion();
        }

        @Override
        public void onChanged(T t) {
            int liveDataVersion = EventLiveData.this.getLiveDataVersion();
            if (liveDataVersion <= startVersion) {
                return;
            }
            observer.onChanged(t);
        }
    }


    private class StickyObserverWrapper<T> extends ObserverWrapper<T> {
        public StickyObserverWrapper(@NonNull Observer<T> observer) {
            this(observer, "");
        }

        public StickyObserverWrapper(@NonNull Observer<T> observer, String tag) {
            super(observer, tag);
        }

        @Override
        public void onChanged(T t) {
            observer.onChanged(t);
        }
    }
}
