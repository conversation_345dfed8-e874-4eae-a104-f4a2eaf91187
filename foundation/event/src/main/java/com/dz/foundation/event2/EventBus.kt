package com.dz.foundation.event2

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch
import kotlin.reflect.KClass

/**
 * 全局事件总线，使用 SharedFlow 实现。
 */
object EventBus {

    const val TAG = "DzEventBus"

    private val scope = CoroutineScope(SupervisorJob() + Dispatchers.Default)

    /**
     * 主事件流。
     * - replay = 0: 不保留粘性事件，新订阅者不会收到旧事件。
     * - extraBufferCapacity = 64: 允许一定量的事件缓冲，减少发布者暂停的频率。
     * - onBufferOverflow = BufferOverflow.DROP_OLDEST: 如果缓冲区满了，丢弃最旧的事件。
     * 对于高频事件，这是一种常见的策略，确保消费者接收到最新事件。
     */
    private val _events = MutableSharedFlow<Any>(
        replay = 0,
        extraBufferCapacity = 64,
        onBufferOverflow = kotlinx.coroutines.channels.BufferOverflow.DROP_OLDEST  // 当缓冲区溢出时，如何处理新事件。
    )

    val events: SharedFlow<Any> = _events.asSharedFlow()


    // 改进：使用类型安全的 Map 来存储粘性事件
    // 每个 KClass 对应一个 MutableSharedFlow 来存储其粘性事件的最新值。
    // 这样，getStickyEvent 可以从这个 SharedFlow 中获取 replay 的值。
    // 或者，为了简单和效率，可以保留原始的 Map<KClass<*>, Any>，但使用泛型函数进行类型安全访问。
    private val stickyEvents = mutableMapOf<KClass<*>, Any>()

    /**
     * 发布一个普通事件。
     * 事件将在后台协程中发射。
     */
    fun post(event: Any) {
        scope.launch {
            try {
                _events.emit(event)
            } catch (e: Exception) {
                // 处理发射失败的情况，例如日志记录
                e.printStackTrace()
            }
        }
    }

    /**
     * 发布一个粘性事件。
     * 粘性事件会被存储，并且会通过普通事件流发布。
     */
    fun postSticky(event: Any) {
        stickyEvents[event::class] = event
        post(event) // 粘性事件也通过普通流发布，以便所有当前订阅者能立即收到
    }

    /**
     * 获取指定类型的最新粘性事件。
     * @param eventClass 事件的 KClass 类型。
     * @return 对应类型的最新粘性事件实例，如果不存在则返回 null。
     */
    @Suppress("UNCHECKED_CAST")
    fun <T : Any> getStickyEvent(eventClass: KClass<T>): T? {
        return stickyEvents[eventClass] as? T
    }

    /**
     * 移除指定类型的粘性事件。
     */
    fun <T : Any> removeStickyEvent(eventClass: KClass<T>) {
        stickyEvents.remove(eventClass)
    }

    /**
     * 清除所有粘性事件。
     */
    fun clearStickyEvents() {
        stickyEvents.clear()
    }

    /**
     * 取消 EventBus 的所有活跃协程。
     * 在应用程序关闭或不需要 EventBus 时调用，以释放资源。
     */
    fun release() {
        scope.cancel() // 取消 EventBus 作用域内的所有协程
    }
}