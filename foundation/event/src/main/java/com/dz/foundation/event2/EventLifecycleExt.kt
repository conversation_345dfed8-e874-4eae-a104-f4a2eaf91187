package com.dz.foundation.event2

import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch

/**
 * LifecycleOwner 扩展函数，方便在 Activity/Fragment 中使用
 */
inline fun <reified T : Any> LifecycleOwner.observeEvent(
    lifecycleState: Lifecycle.State = Lifecycle.State.STARTED,
    noinline onEvent: (T) -> Unit
) {
    lifecycleScope.launch {
        repeatOnLifecycle(lifecycleState) {
            EventBus.events
                .filter { it is T }
                .onEach { onEvent(it as T) }
                .launchIn(this)
        }
    }
}

/**
 * 在协程作用域中观察事件
 */
inline fun <reified T : Any> CoroutineScope.observeEvent(
    noinline onEvent: (T) -> Unit
) {
    EventBus.events
        .filter { it is T }
        .onEach { onEvent(it as T) }
        .launchIn(this)
}

/**
 * 获取粘性事件的扩展函数
 */
inline fun <reified T : Any> getStickyEvent(): T? {
    return EventBus.getStickyEvent(T::class)
}

/**
 * 移除粘性事件
 */
inline fun <reified T : Any> removeStickyEvent() {
    EventBus.removeStickyEvent(T::class)
}