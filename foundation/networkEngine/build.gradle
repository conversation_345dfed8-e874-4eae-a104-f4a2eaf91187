apply from: rootProject.ext.common_android_module
apply plugin: "kotlin-kapt"

dependencies {
    api fileTree(dir: 'libs', include: ['*.jar'])
    api rootProject.ext.getDep("base")
    api("com.squareup.okhttp3:okhttp:4.10.0")
    api("com.squareup.okhttp3:logging-interceptor:4.10.0")
    api 'com.aliyun.ams:alicloud-android-httpdns:2.4.2'
    api 'com.blankj:utilcodex:1.30.1'

    // 查询域名解析结果
//    implementation 'com.qiniu:happy-dns:2.0.1'

    kapt "androidx.room:room-compiler:2.4.3"
}
