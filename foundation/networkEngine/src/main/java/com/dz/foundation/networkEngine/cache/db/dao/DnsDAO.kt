package com.dz.foundation.networkEngine.cache.db.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.dz.foundation.NetworkEngineMC
import com.dz.foundation.networkEngine.cache.db.entity.DnsRecord


@Dao
interface DnsDAO {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insert(dns: DnsRecord)

    @Query("SELECT * FROM dns_record where host=:host order by queryTime DESC")
    fun getIpByHost(host: String): List<DnsRecord>

    @Query("UPDATE dns_record SET ips = :ips , queryTime = :queryTime,ttl = :ttl where host=:host and resolveType= :resolveType")
    fun update(host: String, ips: String, ttl: Long, resolveType: Int, queryTime: Long? = System.currentTimeMillis())


    @Query("DELETE FROM dns_record where host=:host and resolveType=:resolveType ")
    fun deleteByHostAndResolveType(host: String, resolveType: Int)


    @Query("Select*  FROM dns_record where host=:host and resolveType=${NetworkEngineMC.DNS_RESOLVE_TYPE_HTTP} order by queryTime limit 1 ")
    fun qetHttpDnsRecord(host: String): DnsRecord?


    @Query("Select*  FROM dns_record where host=:host order by resolveType DESC")
    fun getAllRecordByHost(host: String): List<DnsRecord>

    @Query("Select*  FROM dns_record where host=:host and resolveType=${NetworkEngineMC.DNS_RESOLVE_TYPE_LOCAL} order by queryTime DESC")
    fun getLocalDnsRecord(host: String): DnsRecord?
}