package com.dz.foundation.networkEngine.threadpool

import com.blankj.utilcode.util.ThreadUtils
import java.util.concurrent.Callable
import java.util.concurrent.ExecutorService
import java.util.concurrent.Future
import java.util.concurrent.TimeUnit

/**
 * 线程池管理类
 * @property KEEP_ALIVE_TIME Long
 * @property executor ExecutorService
 * @constructor NDS解析
 */
class ThreadPoolManager() {
    /**
     * 线程池维护线程所允许的空闲时间
     */
    private val KEEP_ALIVE_TIME: Long = 60L

    // 创建一个固定大小的线程池
//    private val executor: ExecutorService = ThreadPoolExecutor(
//        corePoolSize, // 核心线程数
//        corePoolSize, // 最大线程数（这里与核心线程数相同）
//        KEEP_ALIVE_TIME, // 空闲线程存活时间
//        TimeUnit.SECONDS, // 时间单位
//        LinkedBlockingQueue() // 任务队列
//    )

    private val executor: ExecutorService = ThreadUtils.getCachedPool()

    /**
     * 提交一个 Runnable 任务到线程池
     */
    fun submitTask(task: Runnable) {
        executor.execute(task)
    }

    /**
     * 提交一个 Callable 任务到线程池，并返回 Future 对象
     */
    fun <T> submitTask(task: Callable<T>): Future<T> {
        return executor.submit(task)
    }

    /**
     * 关闭线程池
     */
    fun shutdown() {
        executor.shutdown()
        try {
            if (!executor.awaitTermination(60, TimeUnit.SECONDS)) {
                executor.shutdownNow()
            }
        } catch (e: InterruptedException) {
            executor.shutdownNow()
            Thread.currentThread().interrupt()
        }
    }
}
