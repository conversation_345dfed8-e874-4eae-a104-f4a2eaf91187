package com.dz.foundation.networkEngine.data

import java.io.Serializable

/**
 * 网络请求跟踪bean
 */
class NetworkTraceBean : Serializable {
    var id: String? = null
    var url: String? = null
    var time: Long = 0
    var networkEventsMap= mutableMapOf<String, Long>()

    companion object {
        var CALL_START: String = "callStart"
        var CALL_END: String = "callEnd"
        var DNS_START: String = "dnsStart"
        var DNS_END: String = "dnsEnd"
        var CONNECT_START: String = "connectStart"
        var SECURE_CONNECT_START: String = "secureConnectStart"
        var SECURE_CONNECT_END: String = "secureConnectEnd"
        var CONNECT_END: String = "connectEnd"
        var REQUEST_BODY_START: String = "requestBodyStart"
        var REQUEST_BODY_END: String = "requestBodyEnd"
        var REQUEST_HEADERS_START: String = "requestHeadersStart"
        var REQUEST_HEADERS_END: String = "requestHeadersEnd"
        var RESPONSE_HEADERS_START: String = "responseHeadersStart"
        var RESPONSE_HEADERS_END: String = "responseHeadersEnd"
        var RESPONSE_BODY_START: String = "responseBodyStart"
        var RESPONSE_BODY_END: String = "responseBodyEnd"

    }
}
