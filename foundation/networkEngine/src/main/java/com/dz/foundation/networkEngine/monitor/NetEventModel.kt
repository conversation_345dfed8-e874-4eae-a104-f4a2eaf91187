package com.dz.foundation.networkEngine.monitor

data class NetEventModel(
        val fetchDuration: Long,//请求发出到拿到数据，不包括本地排队时间
        val dnsDuration: Long,//dns解析时间
        val connectDuration: Long,// 创建socket通道时间
        val sslDuration: Long,// ssl握手时间，connect_duration包含secure_duration
        val writeBytesDuration: Long,// writeBytes的时间
        val readBytesDuration: Long,// readBytes的时间
        val requestDuration: Long, // 相当于responseStartDate - requestEndDate
)