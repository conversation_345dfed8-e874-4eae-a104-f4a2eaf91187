package com.dz.foundation.networkEngine.dns

import com.alibaba.sdk.android.httpdns.HTTPDNSResult
import com.alibaba.sdk.android.httpdns.HttpDns
import com.alibaba.sdk.android.httpdns.HttpDnsService
import com.alibaba.sdk.android.httpdns.InitConfig
import com.alibaba.sdk.android.httpdns.RequestIpType
import com.alibaba.sdk.android.httpdns.log.HttpDnsLog
import com.dz.foundation.NetworkEngineMC
import com.dz.foundation.base.data.kv.GlobalKV
import com.dz.foundation.base.module.AppModule
import com.dz.foundation.base.utils.LogUtil
import com.dz.foundation.networkEngine.cache.DNSCacheRepository.getCacheIpWhileOverDate
import com.dz.foundation.networkEngine.cache.DNSCacheRepository.getValidHttpDnsIpList
import com.dz.foundation.networkEngine.cache.DNSCacheRepository.updateDatabase
import com.dz.foundation.networkEngine.threadpool.ThreadPoolManager
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withTimeoutOrNull
import okhttp3.Dns
import java.net.InetAddress
import java.net.UnknownHostException

/**
 * DNS解析
 */
class DnsResolver private constructor() : Dns {
    private val mHttpDns: HttpDnsService = HttpDns.getService(AppModule.getApplication(), NetworkEngineMC.HTTP_DNS_ACCOUNT_ID)
    private val mThreadPoolManager = ThreadPoolManager()
    private val mLocalDnsTimeout = 2 * 1000L

    init {
        val config = InitConfig.Builder().setEnableHttps(false).setTimeoutMillis(5 * 1000).setEnableCacheIp(true).setEnableExpiredIp(true).build()
        HttpDns.init(NetworkEngineMC.HTTP_DNS_ACCOUNT_ID, config)
        HttpDnsLog.enable(true)
    }

    companion object {
        val instance by lazy(LazyThreadSafetyMode.SYNCHRONIZED) { DnsResolver() }
    }

    @Throws(UnknownHostException::class)
    override fun lookup(hostname: String): List<InetAddress> = runBlocking {
        try {
            //开启了httpDNS，优先使用localDNS
            val timeout = GlobalKV.dnsResolveTimeout * 1000
            withTimeoutOrNull(timeout) {
                try {
                    val list = Dns.SYSTEM.lookup(hostname)
                    LogUtil.d("DzClientDns", "localDNS解析成功:$list")
                    updateDatabase(hostname, list, mLocalDnsTimeout, NetworkEngineMC.DNS_RESOLVE_TYPE_LOCAL)
                    list
                } catch (e: Exception) {
                    LogUtil.d("DzClientDns", "localDNS 解析异常:${e.message}")
                    null
                }
            } ?: lookupUsingHttpDns(hostname)

        } catch (e: Exception) {
            e.printStackTrace()
            val list = getCacheIpWhileOverDate(hostname)
            LogUtil.d("DzClientDns", "httpDNS和localDNS都解析失败，使用本地缓存:$list")
            list
        }
    }



    /**
     * httpDNS解析
     * @param host String 域名
     * @return List<InetAddress> ip列表
     */
    private fun lookupUsingHttpDns(host: String): List<InetAddress> {
        val inetAddresses: MutableList<InetAddress> = ArrayList()
        val ipList = getValidHttpDnsIpList(host)
        LogUtil.d("DzClientDns", "读取httpDNS缓存:$ipList")
        if (ipList.isEmpty()) {
            //没有缓存或过期，重新请求
            val httpdnsResult: HTTPDNSResult = mHttpDns.getHttpDnsResultForHostSync(host, RequestIpType.auto)
            LogUtil.d("DzClientDns", "httpdnsResult重新解析结果:$httpdnsResult")

            //未解析到ip，抛出异常，走缓存逻辑
            if (httpdnsResult.ips.isEmpty() && httpdnsResult.ipv6s.isEmpty()) {
                throw UnknownHostException("httpDNS解析失败")
            }

            httpdnsResult.ips?.plus(httpdnsResult.ipv6s.orEmpty())
                ?.filter { item -> item.isNotBlank() }
                ?.mapTo(inetAddresses) { InetAddress.getByName(it) }
            updateDatabase(
                host,
                inetAddresses,
                GlobalKV.httpDNSCacheExpires * 1000,
                NetworkEngineMC.DNS_RESOLVE_TYPE_HTTP
            )
        } else {
            //没过期，直接用
            ipList.mapTo(inetAddresses) { InetAddress.getByName(it) }
            LogUtil.d("DzClientDns", "httpdnsResult没过期，直接使用:$inetAddresses")
        }
        return inetAddresses
    }
}

