package com.dz.foundation.networkEngine.util

import com.dz.foundation.networkEngine.data.NetworkTraceBean

/**
 * 网络请求统计工具类
 */
object NetworkStatisticUtils {

    private var mTraceModelMap = mutableMapOf<String, NetworkTraceBean>()


    /**
     * 根据id获取网络请求的统计模型
     * @param id String? 请求id
     * @return NetworkTraceBean? 网络请求统计模型
     */
    fun getNetworkTraceModel(id: String?): NetworkTraceBean? {
        if (id.isNullOrBlank()) {
            return null
        }
        if (mTraceModelMap.containsKey(id)) {
            return mTraceModelMap[id]
        }
        val traceModel = NetworkTraceBean()
        traceModel.id = id
        traceModel.time = System.currentTimeMillis()
        mTraceModelMap[id] = traceModel
        return traceModel
    }

    /**
     * 计算时间差
     * @param eventsTimeMap Map<String?, Long?>
     * @param startName String?
     * @param endName String?
     * @return Long
     */
    fun getEventCostTime(eventsTimeMap: Map<String, Long>, startName: String?, endName: String?): Long {
        if (!eventsTimeMap.containsKey(startName) || !eventsTimeMap.containsKey(endName)) {
            return 0
        }
        val endTime = eventsTimeMap[endName]
        val start = eventsTimeMap[startName]
        val result = endTime!! - start!!
        return result
    }

}