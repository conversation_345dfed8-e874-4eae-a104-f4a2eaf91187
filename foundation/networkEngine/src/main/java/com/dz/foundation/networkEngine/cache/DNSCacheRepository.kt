package com.dz.foundation.networkEngine.cache

import com.dz.foundation.NetworkEngineMC
import com.dz.foundation.base.data.kv.GlobalKV
import com.dz.foundation.base.utils.LogUtil
import com.dz.foundation.networkEngine.cache.db.NetworkDatabase
import com.dz.foundation.networkEngine.cache.db.entity.DnsRecord
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.net.InetAddress

/**
 * DNS缓存Repository
 */
object DNSCacheRepository {

    private val dnsRecordDao = NetworkDatabase
        .get()
        .getDnsDao()


    /**
     * 获取有效期内httpDNS的ip解析缓存
     * @param host String
     * @return DnsRecord?
     */
    fun getValidHttpDnsIpList(host: String): List<String> {
        val record = dnsRecordDao.qetHttpDnsRecord(host)
        if (record != null && record.ttl > System.currentTimeMillis() - record.queryTime) {
            return record.ips
                ?.split(",")
                ?.filter { it.isNotBlank() }
                .orEmpty()
        }
        return emptyList()
    }


    fun getAllHttpDnsCache(host: String): DnsRecord? {
        return dnsRecordDao.qetHttpDnsRecord(host)
    }

    fun canUseHttpDNS(host: String? = null): Boolean {
        // 如果不启用HttpDNS，则直接返回false
        if (!GlobalKV.enableHttpDNS) return false

        // 如果host为空或空白，则允许使用HttpDNS
        if (host.isNullOrBlank()) return true

        //域名黑名单检测
        val blackList = GlobalKV.hostBlackList.split(",")
        return !blackList.contains(host)
    }

    fun canCheckHostHijacked(host: String): Boolean {
        val blackList = GlobalKV.hostBlackList.split(",")
        return GlobalKV.enableHostHijackedCheck && !blackList.contains(host)
    }



    /**
     * 获取缓存的ip，根据是否启用httpDNS取对应的缓存
     * @param host String
     * @return List<String>
     */
    fun getCacheIpWhileOverDate(host: String): List<InetAddress> {
        if (canUseHttpDNS(host)) {
            return getAllDnsCacheIp(host)
        }
        return getLocalDnsCacheIp(host)
    }


    /**
     * 获取所有的DNS解析缓存
     * @param host String
     * @return List<String>
     */
    private fun getAllDnsCacheIp(host: String): List<InetAddress> {
        return dnsRecordDao
            .getAllRecordByHost(host)
            .flatMap {
                it.ips
                    ?.trim()
                    ?.split(",")
                    ?.filter { item -> item.isNotBlank() } ?: emptyList()
            }
            .distinct()
            .mapNotNull { runCatching { InetAddress.getByName(it) }.getOrNull() }
    }

    /**
     * 获取localDNS解析缓存
     * @param host String
     * @return List<String>
     */
    private fun getLocalDnsCacheIp(host: String): List<InetAddress> {
        println("DzClientDns：getLocalDnsCacheIp!!!!!!")
        val record = dnsRecordDao.getLocalDnsRecord(host)?.ips
            ?.trim()
            ?.split(",")
            ?.filter { item -> item.isNotBlank() } ?: emptyList()
        return record
            .distinct()
            .mapNotNull { runCatching { InetAddress.getByName(it) }.getOrNull() }
    }


    /**
     * 更新数据库
     * @param host String 域名
     * @param list List<InetAddress> 解析记录
     * @param ttl Integer 过期时间
     * @param type DNS类型 :see [NetworkEngineMC.DNS_RESOLVE_TYPE_LOCAL] or [NetworkEngineMC.DNS_RESOLVE_TYPE_HTTP]
     */
    fun updateDatabase(host: String, list: List<InetAddress>, ttl: Long, type: Int) {
        CoroutineScope(Dispatchers.IO).launch {
            runCatching {
                val ipStr = list.joinToString(separator = ",") { it.hostAddress?.toString() ?: "" }
                if (ipStr.isEmpty()) {
                    LogUtil.d("DzClientDns", "IP列表为空，不更新数据库")
                    return@launch
                }
                val dns = DnsRecord(host = host, ips = ipStr, resolveType = type, ttl = ttl, queryTime = System.currentTimeMillis())
                val oldDns = dnsRecordDao
                    .getIpByHost(dns.host)
                    .firstOrNull { it.resolveType == dns.resolveType }

                if (oldDns != null) {
                    dnsRecordDao.update(dns.host, dns.ips ?: "", dns.ttl, dns.resolveType)
                    LogUtil.d("DzClientDns", "更新数据库记录：$dns")
                } else {
                    dnsRecordDao.insert(dns)
                    LogUtil.d("DzClientDns", "插入新数据库记录：$dns")
                }
            }.onFailure { e ->
                e.printStackTrace()
                LogUtil.e("DzClientDns", "数据库更新失败: ${e.message}")
            }
        }
    }


}