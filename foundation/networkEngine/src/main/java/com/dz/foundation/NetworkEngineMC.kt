package com.dz.foundation

class NetworkEngineMC {
    companion object {
        const val HTTP_DNS_ACCOUNT_ID = "101958"
        const val DNS_RESOLVE_TYPE_LOCAL = 1  // localDNS解析
        const val DNS_RESOLVE_TYPE_HTTP = 2   // httpDNS解析

        const val PUBLIC_DNS_SERVER_BAIDU = "180.76.76.76"
        const val PUBLIC_DNS_SERVER_ALIBABA = "223.5.5.5"
        const val PUBLIC_DNS_SERVER_TENCENT = "119.29.29.29"

        const val ERR_TYPE_DNS_RESOLVE="DNS解析失败"
        /**
         * 请求的url
         */
        var requestUrl: String = ""

        /**
         * 请求方式
         */
        var requestMethod: String = ""

        /**
         * 连接超时时间
         */
        const val CONNECT_TIMEOUT_SECONDS = 10L

        /**
         * read超时时间
         */
        const val READ_TIMEOUT_SECONDS = 10L

        /**
         * write超时时间
         */
        const val WRITE_TIMEOUT_SECONDS = 10L
    }
}