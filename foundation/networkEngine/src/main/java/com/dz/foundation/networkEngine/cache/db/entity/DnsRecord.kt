package com.dz.foundation.networkEngine.cache.db.entity

import androidx.annotation.Keep
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.dz.foundation.NetworkEngineMC.Companion.DNS_RESOLVE_TYPE_LOCAL

/**
 * DNS解析记录表
 */
@Keep
@Entity(tableName = "dns_record")
data class DnsRecord(
        var host: String,      //域名
        var ips: String? = "",       //ip地址，多个ip拼成字符串
        val resolveType: Int = DNS_RESOLVE_TYPE_LOCAL,  //解析方式
        val ttl: Long = 0,   //过期时间
        val queryTime: Long = 0,    //查询时间
        val extra1: String? = "",   //扩展字段
        val extra2: String? = "",   //扩展字段
        val extra3: String? = ""    //扩展字段
) {
    @PrimaryKey(autoGenerate = true)
    var id: Long = 0 //主键
}
