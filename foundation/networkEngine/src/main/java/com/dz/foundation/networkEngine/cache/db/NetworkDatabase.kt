package com.dz.foundation.networkEngine.cache.db

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import com.dz.foundation.base.module.AppModule
import com.dz.foundation.networkEngine.cache.db.dao.DnsDAO
import com.dz.foundation.networkEngine.cache.db.entity.DnsRecord

/**
 * 网络库db
 */
@Database(entities = [DnsRecord::class], version = 1)
abstract class NetworkDatabase : RoomDatabase() {
    companion object {
        private val instance by lazy {
            Room
                .databaseBuilder(
                    AppModule.getApplication(),
                    NetworkDatabase::class.java,
                    "network_engine.db"
                )
                .fallbackToDestructiveMigration()   //如果匹配不上迁移策略，则重建数据库
                .build()
        }

        fun get(): NetworkDatabase {
            return instance
        }
    }

    abstract fun getDnsDao(): DnsDAO
}