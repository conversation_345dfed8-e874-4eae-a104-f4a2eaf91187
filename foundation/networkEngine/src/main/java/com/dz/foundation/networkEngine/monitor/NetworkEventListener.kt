package com.dz.foundation.networkEngine.monitor

import android.os.SystemClock
import com.dz.foundation.NetworkEngineMC.Companion.CONNECT_TIMEOUT_SECONDS
import com.dz.foundation.NetworkEngineMC.Companion.READ_TIMEOUT_SECONDS
import com.dz.foundation.NetworkEngineMC.Companion.WRITE_TIMEOUT_SECONDS
import com.dz.foundation.base.data.kv.GlobalKV
import com.dz.foundation.base.utils.LogUtil
import com.dz.foundation.networkEngine.data.NetworkTraceBean
import com.dz.foundation.networkEngine.util.NetworkStatisticUtils.getEventCostTime
import com.dz.foundation.networkEngine.util.NetworkStatisticUtils.getNetworkTraceModel
import okhttp3.Call
import okhttp3.Connection
import okhttp3.EventListener
import okhttp3.Handshake
import okhttp3.Protocol
import okhttp3.Request
import okhttp3.Response
import java.io.IOException
import java.net.InetAddress
import java.net.InetSocketAddress
import java.net.Proxy


/**
 * 网络请求关键事件监听
 */
class NetworkEventListener : EventListener() {

    private val TAG = "NetworkEventListener"

    override fun cacheConditionalHit(call: Call, cachedResponse: Response) {
        super.cacheConditionalHit(call, cachedResponse)
        LogUtil.d(TAG, "cacheConditionalHit-->")
    }

    override fun cacheHit(call: Call, response: Response) {
        super.cacheHit(call, response)
        LogUtil.d(TAG, "cacheHit-->")
    }

    override fun cacheMiss(call: Call) {
        super.cacheMiss(call)
        LogUtil.d(TAG, "cacheMiss-->")
    }

    override fun callEnd(call: Call) {
        super.callEnd(call)
        LogUtil.d(TAG, "callEnd:")
        saveEvent(call, NetworkTraceBean.CALL_END)

        GlobalKV.requestTotalCount += 1
        updateNetworkStatisticsData(call)
    }

    override fun callFailed(call: Call, ioe: IOException) {
        super.callFailed(call, ioe)
        LogUtil.d(TAG, "callFailed-->")
        saveEvent(call, NetworkTraceBean.CALL_END)

        GlobalKV.requestTotalCount += 1
        GlobalKV.requestFailCount += 1
        updateNetworkStatisticsData(call)
    }

    // 按照请求顺序回调
    override fun callStart(call: Call) {
        super.callStart(call)
        LogUtil.d(TAG, "请求开始-------------》》》》》》:${call.request().headers["X-Request-ID"]}--${call.request().url}")
        LogUtil.d(TAG, "callStart:")
        saveEvent(call, NetworkTraceBean.CALL_START)
    }


    override fun connectEnd(call: Call, inetSocketAddress: InetSocketAddress, proxy: Proxy, protocol: Protocol?) {
        super.connectEnd(call, inetSocketAddress, proxy, protocol)
        LogUtil.d(TAG, "connectEnd:")
        saveEvent(call, NetworkTraceBean.CONNECT_END)
    }

    override fun connectFailed(call: Call, inetSocketAddress: InetSocketAddress, proxy: Proxy, protocol: Protocol?, ioe: IOException) {
        super.connectFailed(call, inetSocketAddress, proxy, protocol, ioe)
        LogUtil.d(TAG, "connectFailed:")
        saveEvent(call, NetworkTraceBean.CONNECT_END)
    }

    override fun connectStart(call: Call, inetSocketAddress: InetSocketAddress, proxy: Proxy) {
        super.connectStart(call, inetSocketAddress, proxy)
        LogUtil.d(TAG, "connectStart:")
        saveEvent(call, NetworkTraceBean.CONNECT_START)
    }

    override fun connectionAcquired(call: Call, connection: Connection) {
        super.connectionAcquired(call, connection)
        LogUtil.d(TAG, "connectionAcquired!!!")
    }

    override fun connectionReleased(call: Call, connection: Connection) {
        super.connectionReleased(call, connection)
        LogUtil.d(TAG, "connectionReleased!!!")
    }


    /**
     * 域名解析开始
     * @param call Call
     * @param domainName String
     */
    override fun dnsStart(call: Call, domainName: String) {
        super.dnsStart(call, domainName)
        LogUtil.d(TAG, "dnsStart:")
        saveEvent(call, NetworkTraceBean.DNS_START)
    }

    /**
     * 域名解析结束
     * @param call Call
     * @param domainName String
     * @param inetAddressList List<InetAddress>
     */
    override fun dnsEnd(call: Call, domainName: String, inetAddressList: List<InetAddress>) {
        super.dnsEnd(call, domainName, inetAddressList)
        LogUtil.d(TAG, "dnsEnd:")
        saveEvent(call, NetworkTraceBean.DNS_END)
    }


    override fun requestBodyEnd(call: Call, byteCount: Long) {
        super.requestBodyEnd(call, byteCount)
        LogUtil.d(TAG, "requestBodyEnd:")
        saveEvent(call, NetworkTraceBean.REQUEST_BODY_END)
    }

    override fun requestBodyStart(call: Call) {
        super.requestBodyStart(call)
        LogUtil.d(TAG, "requestBodyStart:")
        saveEvent(call, NetworkTraceBean.REQUEST_BODY_START)
    }

    override fun requestFailed(call: Call, ioe: IOException) {
        super.requestFailed(call, ioe)
        LogUtil.d(TAG, "requestFailed:")
        saveEvent(call, NetworkTraceBean.REQUEST_BODY_END)
    }

    override fun requestHeadersEnd(call: Call, request: Request) {
        super.requestHeadersEnd(call, request)
        LogUtil.d(TAG, "requestHeadersEnd:")
        saveEvent(call, NetworkTraceBean.REQUEST_HEADERS_END)
    }

    override fun requestHeadersStart(call: Call) {
        super.requestHeadersStart(call)
        LogUtil.d(TAG, "requestHeadersStart:")
        saveEvent(call, NetworkTraceBean.REQUEST_HEADERS_START)
    }

    override fun responseBodyEnd(call: Call, byteCount: Long) {
        super.responseBodyEnd(call, byteCount)
        LogUtil.d(TAG, "responseBodyEnd:")
        saveEvent(call, NetworkTraceBean.RESPONSE_BODY_END)

    }

    override fun responseBodyStart(call: Call) {
        super.responseBodyStart(call)
        LogUtil.d(TAG, "responseBodyStart:")
        saveEvent(call, NetworkTraceBean.RESPONSE_BODY_START)
    }

    override fun responseFailed(call: Call, ioe: IOException) {
        super.responseFailed(call, ioe)
        LogUtil.d(TAG, "responseFailed:")
        saveEvent(call, NetworkTraceBean.RESPONSE_BODY_END)
    }

    override fun responseHeadersEnd(call: Call, response: Response) {
        super.responseHeadersEnd(call, response)
        LogUtil.d(TAG, "responseHeadersEnd:")
        saveEvent(call, NetworkTraceBean.RESPONSE_HEADERS_END)
    }

    override fun responseHeadersStart(call: Call) {
        super.responseHeadersStart(call)
        LogUtil.d(TAG, "responseHeadersStart:")
        saveEvent(call, NetworkTraceBean.RESPONSE_HEADERS_START)

    }

    override fun satisfactionFailure(call: Call, response: Response) {
        super.satisfactionFailure(call, response)
        LogUtil.d(TAG, "satisfactionFailure!!!!")
    }

    override fun secureConnectEnd(call: Call, handshake: Handshake?) {
        super.secureConnectEnd(call, handshake)
        LogUtil.d(TAG, "secureConnectEnd:")
        saveEvent(call, NetworkTraceBean.SECURE_CONNECT_END)
    }

    override fun secureConnectStart(call: Call) {
        super.secureConnectStart(call)
        LogUtil.d(TAG, "secureConnectStart:")
        saveEvent(call, NetworkTraceBean.SECURE_CONNECT_START)
    }

    private fun saveEvent(call: Call, eventName: String) {
        kotlin.runCatching {
            val requestId = call.request().headers["X-Request-ID"]
            LogUtil.d(TAG, "saveEvent----------->$requestId")
            getNetworkTraceModel(requestId)?.apply {
                networkEventsMap[eventName] = SystemClock.elapsedRealtime()
            }
        }.onFailure {
            it.printStackTrace()
            LogUtil.d(TAG, "saveEvent Fail:${it.message}")
        }
    }

    /**
     * 更新网络请求统计数据
     * @param call Call
     */
    private fun updateNetworkStatisticsData(call: Call) {
        kotlin.runCatching {
            val requestId = call.request().headers["X-Request-ID"]
            getNetworkTraceModel(requestId)?.apply {
                val eventsTimeMap = this.networkEventsMap
                val totalCost = getEventCostTime(eventsTimeMap, NetworkTraceBean.CALL_START, NetworkTraceBean.CALL_END)
                val dnsCost = getEventCostTime(eventsTimeMap, NetworkTraceBean.DNS_START, NetworkTraceBean.DNS_END)
                val securityConnectCost = getEventCostTime(eventsTimeMap, NetworkTraceBean.SECURE_CONNECT_START, NetworkTraceBean.SECURE_CONNECT_END)
                val connectCost = getEventCostTime(eventsTimeMap, NetworkTraceBean.CONNECT_START, NetworkTraceBean.CONNECT_END)
                val requestHeaderCost = getEventCostTime(eventsTimeMap, NetworkTraceBean.REQUEST_HEADERS_START, NetworkTraceBean.REQUEST_HEADERS_END)
                val requestBodyCost = getEventCostTime(eventsTimeMap, NetworkTraceBean.REQUEST_BODY_START, NetworkTraceBean.REQUEST_BODY_END)
                val responseHeaderCost = getEventCostTime(eventsTimeMap, NetworkTraceBean.RESPONSE_HEADERS_START, NetworkTraceBean.RESPONSE_HEADERS_END)
                val responseBodyCost = getEventCostTime(eventsTimeMap, NetworkTraceBean.RESPONSE_BODY_START, NetworkTraceBean.RESPONSE_BODY_END)
                LogUtil.d(TAG, "计算各阶段耗时:totalCost:$totalCost,dnsCost:$dnsCost,securityConnectCost:$securityConnectCost,connectCost:$connectCost,requestHeaderCost:$requestHeaderCost,requestBodyCost:$requestBodyCost,responseHeaderCost:$responseHeaderCost,responseBodyCost:$responseBodyCost")
                //异常值过滤
                if (totalCost <= 0 || dnsCost < 0 || securityConnectCost < 0 || connectCost < 0 || requestHeaderCost < 0 || requestBodyCost < 0 || responseHeaderCost < 0 || responseBodyCost < 0) {
                    return
                }
                //极大值过滤
                val maxTimeoutTime = CONNECT_TIMEOUT_SECONDS + WRITE_TIMEOUT_SECONDS + READ_TIMEOUT_SECONDS
                if (connectCost > maxTimeoutTime) {
                    return
                }
                if (dnsCost > 0) {
                    GlobalKV.dnsCost = (GlobalKV.dnsCost + dnsCost) / 2
                }
                if (connectCost > 0) {
                    GlobalKV.connectCost = (GlobalKV.connectCost + connectCost) / 2
                }
                if (securityConnectCost > 0) {
                    GlobalKV.secureConnectCost = (GlobalKV.secureConnectCost + securityConnectCost) / 2
                }
                if (requestHeaderCost > 0) {
                    GlobalKV.requestHeaderCost = (GlobalKV.requestHeaderCost + requestHeaderCost) / 2
                }
                if (requestBodyCost > 0) {
                    GlobalKV.requestBodyCost = (GlobalKV.requestBodyCost + requestBodyCost) / 2
                }
                if (responseHeaderCost > 0) {
                    GlobalKV.responseHeaderCost = (GlobalKV.responseHeaderCost + responseHeaderCost) / 2
                }
                if (responseBodyCost > 0) {
                    GlobalKV.responseBodyCost = (GlobalKV.responseBodyCost + responseBodyCost) / 2
                }
                GlobalKV.totalCost = (GlobalKV.totalCost + totalCost) / 2
            }
        }.onFailure {
            it.printStackTrace()
            LogUtil.d(TAG, "updateNetworkStatisticsData Fail:${it.message}")
        }

    }
}
