package com.dz.foundation.imageloader

import android.graphics.Bitmap
import android.graphics.drawable.Drawable
import android.widget.ImageView
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.load.resource.bitmap.BitmapTransformation
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.request.RequestListener
import com.dz.foundation.imageloader.GlideUtils.UNSET
import java.util.concurrent.atomic.AtomicInteger

/**
 * 直接加载
 * imageView.load("https://xxxx")
 * 圆形
 * imageView.loadCircleImage("https://xxxx")
 * 圆角
 * imageView.loadRoundImage("https://xxxx", 20)
 * 圆形带边框
 * imageView.loadCircleImageWithBorder("https://xxxx", 10f, Color.parseColor("#FF0000"))
 * 圆角矩形带边框
 * imageView.loadRoundImageWithBorder("https://xxxx", 10f, Color.parseColor("#FF0000"),10f)
 * imageView.loadRoundImg(图片地址（Object类型）,)
 */

fun ImageView.load(
    url: String?,
    placeholder: Int = 0,
    error: Int = 0,
    listener: RequestListener<Drawable>? = null,
    width: Int = UNSET,//UNSET
    height: Int = UNSET//UNSET
) {
    GlideUtils.loadImage(
        context, url, this, placeholder, error, listener,
        width = width,
        height = height,
    )
}


fun ImageView.load(
    url: Any?,
    placeholder: Int = 0,
    error: Int = 0,
    listener: RequestListener<Drawable>? = null,
    width: Int = UNSET,//UNSET
    height: Int = UNSET//UNSET

) {
    GlideUtils.loadImage(
        context, url, this, placeholder, error, listener,
        width = width,
        height = height,
    )
}

fun ImageView.loadCircleImage(
    url: String?,
    placeholder: Int = 0,
    error: Int = 0,
    width: Int = UNSET,//UNSET
    height: Int = UNSET//UNSET
) {
    GlideUtils.loadCircleImage(
        context, url, this, placeholder, error,
        width = width,
        height = height,
    )
}

fun ImageView.loadNewCircleImage(
    url: String?,
    placeholder: Int = 0,
    error: Int = 0,
    listener: RequestListener<Drawable>? = null,
    width: Int = UNSET,//UNSET
    height: Int = UNSET//UNSET
) {
    GlideUtils.loadNewCircleImage(
        context, url, this, placeholder, error,
        width = width,
        height = height,
        listener = listener,
    )
}

//fun ImageView.loadRoundImg(
//    url: String?,
//    roundingRadius: Int,
//    placeholder: Int = 0,
//    error: Int = 0
//) {
//    GlideUtils.loadRoundImage(context, url, roundingRadius, this, placeholder, error)
//}

fun ImageView.loadRoundImg(
    img: Any?,
    radius: Int = 0,
    placeholder: Int = 0,
    error: Int = 0,
    listener: RequestListener<Drawable>? = null,
    width: Int = UNSET,//UNSET
    height: Int = UNSET,//UNSET
    transform: BitmapTransformation? = CenterCrop(),
) {
    GlideUtils.loadRoundImg(
        context = context,
        img = img,
        placeholder = placeholder,
        radius = radius,
        error = error,
        this,
        listener = RetryRequestListener(listener, 3),
        width = width,
        height = height,
        transform = transform,
    )
}

fun ImageView.loadRoundedCornersImage(
    img: Any?,
    placeholder: Int = 0,
    radius: Float,
    error: Int = 0,
    cornerType: RoundedCornersTransform.CornerType = RoundedCornersTransform.CornerType.ALL,
    width: Int = UNSET,//UNSET
    height: Int = UNSET//UNSET
) {
    GlideUtils.loadRoundedCornersImage(
        context = context,
        img = img,
        placeholder = placeholder,
        error = error,
        radius = radius,
        cornerType,
        imageView = this,
        width = width,
        height = height,

        )
}

// 2.6.0 重试加载图片
fun ImageView.loadRoundImgRetry(
    img: Any?,
    radius: Int,
    placeholder: Int = 0,
    error: Int = 0,
    listener: RequestListener<Drawable>? = null,
    width: Int = UNSET,//UNSET
    height: Int = UNSET,//UNSET
    transform: BitmapTransformation? = CenterCrop(),
    retry: Int = 3
) {
    GlideUtils.loadRoundImg(
        context = context,
        img = img,
        placeholder = placeholder,
        radius = radius,
        error = error,
        this,
        listener = RetryRequestListener(listener, retry),
        width = width,
        height = height,
        transform = transform,
    )
}

private class RetryRequestListener(
    private val wrapped: RequestListener<Drawable>?,
    private val maxRetries: Int
) : RequestListener<Drawable> {
    private var retryCount = AtomicInteger(0)

    override fun onLoadFailed(
        e: GlideException?,
        model: Any?,
        target: com.bumptech.glide.request.target.Target<Drawable>?, // 使用完整包名
        isFirstResource: Boolean
    ): Boolean {
        return if (retryCount.get() < maxRetries) {
            retryCount.incrementAndGet()
            false
        } else {
            wrapped?.onLoadFailed(e, model, target, isFirstResource) ?: false
        }
    }

    override fun onResourceReady(
        resource: Drawable,
        model: Any?,
        target: com.bumptech.glide.request.target.Target<Drawable>?, // 使用完整包名
        dataSource: DataSource?,
        isFirstResource: Boolean
    ): Boolean {
        return wrapped?.onResourceReady(resource, model, target, dataSource, isFirstResource)
            ?: false
    }
}

// 2.7.0 bitmap 圆角
fun ImageView.loadRoundBitmap(
    img: Bitmap?,
    radius: Float,
    cornerType: RoundedCornersTransform.CornerType = RoundedCornersTransform.CornerType.ALL,
) {
    GlideUtils.loadRoundedBitmap(
        context = context,
        bitmap = img,
        radius = radius,
        cornerType = cornerType,
        imageView = this,
    )
}
