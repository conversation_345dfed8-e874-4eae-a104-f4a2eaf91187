package com.dz.foundation.imageloader

import android.content.Context
import com.bumptech.glide.Glide
import com.bumptech.glide.GlideBuilder
import com.bumptech.glide.Registry
import com.bumptech.glide.annotation.GlideModule
import com.bumptech.glide.load.engine.executor.GlideExecutor
import com.bumptech.glide.module.AppGlideModule


@GlideModule
class CustomGlideModule : AppGlideModule() {

    override fun registerComponents(context: Context, glide: Glide, registry: Registry) {
        super.registerComponents(context, glide, registry)
    }

    override fun isManifestParsingEnabled(): Boolean {
        return false
    }

    override fun applyOptions(context: Context, builder: GlideBuilder) {
        super.applyOptions(context, builder)
        // 配置线程池大小
        val mSourceExecutor = GlideExecutor.newSourceBuilder()
            .setThreadCount(2)
            .setThreadTimeoutMillis(1000) //线程读写超时时间。
            .setName("fly-SourceExecutor")
            .build()
        val mDiskCacheBuilder = GlideExecutor.newDiskCacheBuilder()
            .setThreadCount(2)
            .setThreadTimeoutMillis(1000) //线程读写超时时间。
            .setName("fly-DiskCacheBuilder")
            .build()
        // 设置线程池
        builder.setDiskCacheExecutor(mDiskCacheBuilder)
        builder.setSourceExecutor(mSourceExecutor)
    }
}