package com.dz.foundation.imageloader

import android.app.Activity
import android.content.Context
import android.graphics.Bitmap
import android.graphics.drawable.Drawable
import android.text.TextUtils
import android.widget.ImageView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.load.resource.bitmap.BitmapTransformation
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.RequestOptions
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.target.Target
import com.dz.support.ImgResize
import java.io.File

/**
 * glide工具类
 * GlideUtils.loadImage(this,"https://xxxx",imageView)
 * GlideUtils...
 */
object GlideUtils {
    const val UNSET = -1

    /**
     * 使用原声缓存图片，并获取地址
     */
    fun getImageFilePath(
        context: Context?,
        url: String?,
    ): String? {
        if (context == null || TextUtils.isEmpty(url)) return null
        if (context is Activity && context.isDestroyed) {
            return null
        }
        val options = RequestOptions()
            .diskCacheStrategy(DiskCacheStrategy.ALL)
        val file: File = Glide.with(context).load(url).apply(options)
            .downloadOnly(Target.SIZE_ORIGINAL, Target.SIZE_ORIGINAL).get()
        return file.absolutePath
    }

    /**
     * 正常加载图片
     */
    fun loadImage(
        context: Context,
        url: String?,
        imageView: ImageView,
        placeholder: Int = 0,
        error: Int = 0,
        listener: RequestListener<Drawable>? = null,
        width: Int = UNSET,//UNSET
        height: Int = UNSET//UNSET
    ) {
        if (context is Activity && context.isDestroyed) {
            return
        }
        if (TextUtils.isEmpty(url)) return
        val options = RequestOptions().placeholder(placeholder) //占位图

            .error(error) //错误图
            .diskCacheStrategy(DiskCacheStrategy.ALL)
        if (width == UNSET) {
            Glide.with(context).load(url).apply(options).addListener(listener).into(imageView)
        } else {
            Glide.with(context).load(ImgResize.getResizeUrl(url.toString(), width))
                .override(ImgResize.memoryCacheWidth(width), ImgResize.memoryCacheWidth(height))
                .apply(options).addListener(listener).into(imageView)
        }

    }

    /**
     * 正常加载图片
     */
    fun loadImage(
        context: Context,
        url: Any?,
        imageView: ImageView,
        placeholder: Int = 0,
        error: Int = 0,
        listener: RequestListener<Drawable>? = null,
        width: Int = UNSET,//UNSET
        height: Int = UNSET//UNSET
    ) {
        if (context is Activity && context.isDestroyed) {
            return
        }
        val options = RequestOptions().placeholder(placeholder) //占位图
            .error(error) //错误图
            .diskCacheStrategy(DiskCacheStrategy.ALL)
        Glide.with(context)
            .load(if (width == UNSET) url else ImgResize.getResizeUrl(url.toString(), width))
            .override(ImgResize.memoryCacheWidth(width), ImgResize.memoryCacheWidth(height))
            .apply(options).addListener(listener).into(imageView)

    }

    /**
     * 裁剪为圆形加载图片
     */
    fun loadCircleImage(
        context: Context,
        url: String?,
        imageView: ImageView,
        placeholder: Int = 0,
        error: Int = 0,
        width: Int = UNSET,//UNSET
        height: Int = UNSET//UNSET
    ) {
        if (context is Activity && context.isDestroyed) {
            return
        }
        if (TextUtils.isEmpty(url)) return
        val options = RequestOptions().circleCrop().placeholder(placeholder).error(error)
            .diskCacheStrategy(DiskCacheStrategy.ALL)
        Glide.with(context)
            .load(if (width == UNSET) url else ImgResize.getResizeUrl(url.toString(), width))
            .override(ImgResize.memoryCacheWidth(width), ImgResize.memoryCacheWidth(height))
            .apply(options).into(imageView)
    }

    /**
     * 裁剪为圆形加载图片
     */
    fun loadNewCircleImage(
        context: Context,
        url: String?,
        imageView: ImageView,
        placeholder: Int = 0,
        error: Int = 0,
        listener: RequestListener<Drawable>? = null,
        width: Int = UNSET,//UNSET
        height: Int = UNSET,//UNSET
    ) {
        if (context is Activity && context.isDestroyed) {
            return
        }
        if (TextUtils.isEmpty(url)) return
        val options = RequestOptions().circleCrop().placeholder(placeholder).error(error)
            .diskCacheStrategy(DiskCacheStrategy.ALL)
        if (width == UNSET || height == UNSET) {
            Glide.with(context)
                .load(if (width == UNSET) url else ImgResize.getResizeUrl(url.toString(), width))
                .apply(options)
                .addListener(listener)
                .into(imageView)
        } else {
            Glide.with(context)
                .load(ImgResize.getResizeUrl(url.toString(), width))
                .override(ImgResize.memoryCacheWidth(width), ImgResize.memoryCacheWidth(height))
                .apply(options)
                .addListener(listener)
                .into(imageView)
        }
    }

    /**
     * 裁剪圆形带边框加载图片
     * borderWidth 边框宽度 单位像素
     * borderColor 边框颜色
     */
    fun loadCircleImageWithBorder(
        context: Context,
        url: String?,
        borderWidth: Int,
        borderColor: Int,
        imageView: ImageView,
        placeholder: Int = 0,
        error: Int = 0,
        width: Int = UNSET,//UNSET
        height: Int = UNSET//UNSET
    ) {
        if (context is Activity && context.isDestroyed) {
            return
        }
        if (TextUtils.isEmpty(url)) return
        val options = RequestOptions().placeholder(placeholder)
            .transform(TransformCircleWithBorder(borderWidth, borderColor)).error(error)
            .diskCacheStrategy(DiskCacheStrategy.ALL)
        Glide.with(context)
            .load(if (width == UNSET) url else ImgResize.getResizeUrl(url.toString(), width))
            .override(ImgResize.memoryCacheWidth(width), ImgResize.memoryCacheWidth(height))
            .apply(options).into(imageView)
    }


    /**
     * 预先加载图片
     */
    fun preloadImage(context: Context, url: String?) {
        if (TextUtils.isEmpty(url)) return
        Glide.with(context).load(url).preload()

    }

    /**
     * 预先加载图片并监听处理
     */
    fun preloadImageListen(
        context: Context,
        url: String?,
        firstLoad: Boolean? = true,
        onBack: (Boolean) -> Unit
    ) {
        if (TextUtils.isEmpty(url)) return
        Glide.with(context)
            .load(url)
            .addListener(object : RequestListener<Drawable> {
                override fun onLoadFailed(
                    e: GlideException?,
                    model: Any?,
                    target: Target<Drawable>?,
                    isFirstResource: Boolean
                ): Boolean {
                    if (firstLoad == true) {
                        preloadImageListen(context, url, false, onBack)
                    } else {
                        onBack.invoke(false)
                    }
                    return false
                }

                override fun onResourceReady(
                    resource: Drawable?,
                    model: Any?,
                    target: Target<Drawable>?,
                    dataSource: DataSource?,
                    isFirstResource: Boolean
                ): Boolean {
                    onBack.invoke(true)
                    return false
                }
            }).preload()
    }

    fun preloadImage(
        context: Context,
        url: String?,
        radius: Int = 0,
        width: Int = Target.SIZE_ORIGINAL,
        height: Int = Target.SIZE_ORIGINAL,
        onBack: (String?, Boolean) -> Unit
    ) {
        if (TextUtils.isEmpty(url)) return
        Glide.with(context)
            .load(url)
            .transform(CenterCrop(), RoundedCorners(radius))
            .addListener(object : RequestListener<Drawable> {
                override fun onLoadFailed(
                    e: GlideException?,
                    model: Any?,
                    target: Target<Drawable>?,
                    isFirstResource: Boolean
                ): Boolean {
                    onBack.invoke(url, false)
                    return false
                }

                override fun onResourceReady(
                    resource: Drawable?,
                    model: Any?,
                    target: Target<Drawable>?,
                    dataSource: DataSource?,
                    isFirstResource: Boolean
                ): Boolean {
                    onBack.invoke(url, true)
                    return false
                }
            })
            .preload(width, height)
    }

    fun downloadImage(context: Context, url: String, target: CustomTarget<Bitmap>) {
        Glide.with(context).asBitmap().load(url).into(target)
    }

    fun downloadImage(
        context: Context, url: String, target: CustomTarget<Bitmap>, width: Int, height: Int
    ) {
        Glide.with(context).asBitmap().load(url).fitCenter()
            .override(ImgResize.memoryCacheWidth(width), ImgResize.memoryCacheWidth(height))
            .into(target)
    }

    fun loadRoundImg(
        context: Context,
        img: Any?,
        placeholder: Int = 0,
        radius: Int = 0,
        error: Int = 0,
        imageView: ImageView,
        listener: RequestListener<Drawable>? = null,
        width: Int = UNSET,//UNSET
        height: Int = UNSET,//UNSET
        transform: BitmapTransformation? = CenterCrop(),
    ) {
        if (context is Activity && context.isDestroyed) {
            return
        }
        val glideRequest = Glide.with(context)
            .load(if (width == UNSET) img else ImgResize.getResizeUrl(img.toString(), width))
            .placeholder(placeholder)
            .override(ImgResize.memoryCacheWidth(width), ImgResize.memoryCacheWidth(height))
            .error(error)
            .addListener(listener)
        // 只有当 radius 大于 0 时才应用圆角转换
        if (radius > 0) {
            glideRequest.transform(transform, RoundedCorners(radius))
        } else if (transform != null) {
            // 如果没有圆角但有其他转换，则只应用该转换
            glideRequest.transform(transform)
        }
        glideRequest.into(imageView)
    }

    fun loadRoundedCornersImage(
        context: Context,
        img: Any?,
        placeholder: Int = 0,
        error: Int = 0,
        radius: Float,
        cornerType: RoundedCornersTransform.CornerType,
        imageView: ImageView,
        width: Int = UNSET,//UNSET
        height: Int = UNSET//UNSET
    ) {
        if (context is Activity && context.isDestroyed) {
            return
        }

        Glide.with(context)
            .load(if (width == UNSET) img else ImgResize.getResizeUrl(img.toString(), width))
            .override(ImgResize.memoryCacheWidth(width), ImgResize.memoryCacheWidth(height))
            .apply(
                RequestOptions().dontTransform().placeholder(placeholder).transform(
                    RoundedCornersTransform(
                        radius, cornerType
                    )
                ).error(error).diskCacheStrategy(DiskCacheStrategy.ALL)
            ).into(imageView)
    }

    /**
     * 加载圆角图片
     * @param context 上下文
     * @param bitmap 图片Bitmap
     * @param imageView 显示的ImageView
     * @param radius 圆角半径
     * @param cornerType 圆角类型
     */
    fun loadRoundedBitmap(
        context: Context,
        bitmap: Bitmap?,
        imageView: ImageView,
        radius: Float,
        cornerType: RoundedCornersTransform.CornerType
    ) {
        if (context is Activity && context.isDestroyed || bitmap == null) return

        Glide.with(context)
            .load(bitmap)
            .apply(
                RequestOptions().dontTransform()
                    .transform(RoundedCornersTransform(radius, cornerType))
                    .placeholder(imageView.drawable).dontAnimate().error(imageView.drawable)
                    .diskCacheStrategy(DiskCacheStrategy.ALL)
            ).into(imageView)
    }
}
