package com.dz.foundation.imageloader

import android.graphics.*
import com.bumptech.glide.load.engine.bitmap_recycle.BitmapPool
import com.bumptech.glide.load.resource.bitmap.BitmapTransformation
import java.security.MessageDigest

/**
 * 加载圆角矩形图片带边框
 */
class TransformRectWithBorder(
    private val mBorderWidth: Int,
    private val borderColor: Int,
    private val roundingRadius: Int = 0
) :
    BitmapTransformation() {

    private var mBorderPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        isDither = true
        color = borderColor
        style = Paint.Style.STROKE
        strokeWidth = mBorderWidth.toFloat()
    }


    override fun transform(
        pool: BitmapPool,
        toTransform: Bitmap,
        outWidth: Int,
        outHeight: Int
    ): Bitmap {
        return roundCrop(pool, toTransform)
    }

    private fun roundCrop(pool: BitmapPool, source: Bitmap): Bitmap {
        val result = pool.get(source.width, source.height, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(result)
        val paint = Paint()
        paint.shader = BitmapShader(
            source,
            Shader.TileMode.CLAMP,
            Shader.TileMode.CLAMP
        );
        paint.isAntiAlias = true;
        val rectF = RectF(
            mBorderWidth.toFloat(),
            mBorderWidth.toFloat(),
            source.width.toFloat() - mBorderWidth,
            source.height.toFloat() - mBorderWidth
        )
        canvas.drawRoundRect(rectF, roundingRadius.toFloat(), roundingRadius.toFloat(), paint);
        canvas.drawRoundRect(
            rectF,
            roundingRadius.toFloat(),
            roundingRadius.toFloat(),
            mBorderPaint
        )
        return result
    }

    override fun updateDiskCacheKey(messageDigest: MessageDigest) {}
}